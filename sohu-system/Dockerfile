FROM openjdk:11

MAINTAINER sohu

RUN mkdir -p /sohu/system/logs \
    /sohu/system/temp \
    /sohu/skywalking/agent

WORKDIR /sohu/system

ENV SERVER_PORT=9222

EXPOSE ${SERVER_PORT}

ADD sohu-system.jar ./app.jar

ENTRYPOINT ["java", \
            "-Djava.security.egd=file:/dev/./urandom", \
            "-Dserver.port=${SERVER_PORT}", \
#            "-Dskywalking.agent.service_name=sohu-system", \
#            "-javaagent:/sohu/skywalking/agent/skywalking-agent.jar", \
            "-jar", "app.jar"]
