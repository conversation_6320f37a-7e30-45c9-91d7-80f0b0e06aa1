<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.system.mapper.SysUserMapper">
    <resultMap type="com.sohu.system.api.domain.SysUser" id="SysUserResult">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="userType" column="user_type"/>
        <result property="email" column="email"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="birthday" column="birthday"/>
        <result property="payPassword" column="pay_password"/>
        <result property="withdrawReason" column="withdraw_reason"/>
        <result property="withdrawPic" column="withdraw_pic"/>
        <result property="withdrawPic" column="withdraw_audit"/>
        <result property="inviteCode" column="invite_code"/>
        <result property="banType" column="ban_type"/>
        <result property="banStatus" column="ban_status"/>
        <association property="dept" column="dept_id" javaType="com.sohu.system.api.domain.SysDept" resultMap="deptResult"/>
        <collection property="roles" javaType="java.util.List" resultMap="RoleResult"/>
    </resultMap>

    <resultMap id="deptResult" type="com.sohu.system.api.domain.SysDept">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="ancestors" column="ancestors"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="status" column="dept_status"/>
    </resultMap>

    <resultMap id="RoleResult" type="com.sohu.system.api.domain.SysRole">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="status" column="role_status"/>
    </resultMap>

    <sql id="selectUserVo">
        select u.user_id,
               u.dept_id,
               u.user_name,
               u.nick_name,
               u.user_type,
               u.email,
               u.avatar,
               u.phone_number,
               u.password,
               u.sex,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.remark,
               u.invite_code,
               u.birthday,
               u.pay_password,
               d.dept_id,
               d.parent_id,
               d.ancestors,
               d.dept_name,
               d.order_num,
               d.leader,
               d.status as dept_status,
               r.role_id,
               r.role_name,
               r.role_key,
               r.role_sort,
               r.data_scope,
               r.status as role_status
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
                 left join sys_user_role sur on u.user_id = sur.user_id
                 left join sys_role r on r.role_id = sur.role_id
    </sql>

    <select id="selectPageUserList" resultMap="SysUserResult">
        select u.user_id,
               u.dept_id,
               u.nick_name,
               u.user_name,
               u.email,
               u.avatar,
               u.phone_number,
               u.sex,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.remark,
               u.invite_code,
               d.dept_name,
               d.leader
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
            ${ew.getCustomSqlSegment}
    </select>

    <select id="selectUserList" resultMap="SysUserResult">
        select u.user_id,
               u.dept_id,
               u.nick_name,
               u.user_name,
               u.email,
               u.avatar,
               u.phone_number,
               u.sex,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.remark,
               u.invite_code,
               d.dept_name,
               d.leader
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
            ${ew.getCustomSqlSegment}
    </select>

    <select id="selectAllocatedList" resultMap="SysUserResult">
        select distinct u.user_id,
                        u.dept_id,
                        u.user_name,
                        u.nick_name,
                        u.email,
                        u.phone_number,
                        u.status,
                        u.create_time
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
                 left join sys_user_role sur on u.user_id = sur.user_id
                 left join sys_role r on r.role_id = sur.role_id
            ${ew.getCustomSqlSegment}
    </select>

    <select id="selectUnallocatedList" resultMap="SysUserResult">
        select distinct u.user_id,
                        u.dept_id,
                        u.user_name,
                        u.nick_name,
                        u.email,
                        u.phone_number,
                        u.status,
                        u.create_time
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
                 left join sys_user_role sur on u.user_id = sur.user_id
                 left join sys_role r on r.role_id = sur.role_id
            ${ew.getCustomSqlSegment}
    </select>

    <select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and u.user_name = #{userName}
    </select>

    <select id="selectUserByPhonenumber" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and u.phone_number = #{phonenumber}
    </select>

    <select id="selectUserByEmail" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and u.email = #{email}
    </select>

    <select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and u.user_id = #{userId}
    </select>


    <update id="updateInviteCode">
        UPDATE sys_user
        SET invite_code = #{inviteCode}
        WHERE user_id = #{userId}
    </update>

    <select id="page" resultType="com.sohu.system.api.domain.UserVo">
        SELECT su.user_id AS id,su.nick_name,su.user_name,su.avatar,COUNT(1) AS focusUserFans
        FROM sys_user su
        LEFT JOIN sohu_user_follow uf on su.user_id = uf.focus_user_id
        <where>
            su.del_flag = 0
            <if test="notInUserIds != null and notInUserIds.size() > 0">
                AND su.user_id NOT IN
                <foreach collection="notInUserIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="keyword != null and keyword != ''">
                AND (su.nick_name like concat('%', #{keyword}, '%') or su.user_name like concat('%', #{keyword}, '%'))
            </if>
        </where>
        GROUP BY su.user_id
        ORDER BY COUNT(1) DESC ,su.nick_name DESC
    </select>

    <select id="pageFriend" resultType="com.sohu.system.api.domain.UserVo">
        SELECT su.user_id AS id,su.nick_name,su.user_name,su.avatar,COUNT(1) AS focusUserFans
        FROM sys_user su
        LEFT JOIN sohu_user_follow uf ON su.user_id = uf.focus_user_id
        LEFT JOIN sohu_friends sf ON su.user_id = sf.friend_id AND sf.user_id = #{loginId} AND sf.apply_state ='pass'
        <where>
            su.del_flag = 0
            <if test="notInUserIds != null and notInUserIds.size() > 0">
                AND su.user_id NOT IN
                <foreach collection="notInUserIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="keyword != null and keyword != ''">
                AND (su.nick_name like concat('%', #{keyword}, '%') or su.user_name like concat('%', #{keyword}, '%') or su.phone_number like concat('%', #{keyword}, '%'))
            </if>
        </where>
        GROUP BY su.user_id
        ORDER BY COUNT(1) DESC ,su.nick_name DESC
    </select>


    <select id="selectAllUserById" resultType="com.sohu.system.api.domain.SysUser">
        select *
        FROM sys_user
        WHERE user_id = #{userId}
    </select>

    <update id="userApplyWithdraw">
        UPDATE sys_user SET status =2 ,withdraw_reason = #{reason} ,withdraw_pic =#{pic} WHERE id =#{userId}
    </update>

    <select id="queryByUserName" resultType="java.lang.Long">
        SELECT user_id
        FROM sys_user
        WHERE nick_name LIKE concat('%', #{nickname}, '%')
    </select>
    <select id="queryByLikePhonenumber" resultType="java.lang.Long">
        SELECT user_id
        FROM sys_user
        WHERE phone_number LIKE concat('%', #{phoneNumber}, '%')
    </select>

    <select id="selectAllocatedListOfPlatform" resultMap="SysUserResult">
        SELECT DISTINCT u.user_id,
                        u.dept_id,
                        u.user_name,
                        u.nick_name,
                        u.email,
                        u.phone_number,
                        u.status,
                        u.create_time
        FROM sys_user u
                 INNER JOIN sys_platform_user pu ON u.user_id = pu.user_id
        <where>
            <if test="bo.roleId != null">
                and pu.platform_role_id = #{bo.roleId}
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                AND u.user_name like concat('%',#{bo.userName},'%')
            </if>
            <if test="bo.nickName != null and bo.nickName != ''">
                AND u.nick_name like concat('%',#{bo.nickName},'%')
            </if>
            <if test="bo.phoneNumber != null and bo.phoneNumber != ''">
                AND u.phone_number like concat('%',#{bo.phoneNumber},'%')
            </if>
        </where>
    </select>

    <select id="selectUnallocatedListOfPlatform" resultMap="SysUserResult">
        SELECT DISTINCT u.user_id,
        u.dept_id,
        u.user_name,
        u.nick_name,
        u.email,
        u.phone_number,
        u.status,
        u.create_time
        FROM sys_user u
        LEFT JOIN sys_platform_user pu ON u.user_id = pu.user_id AND pu.platform_role_id= #{bo.roleId}
        WHERE pu.platform_role_id IS NULL
        <if test="bo.userName != null and bo.userName != ''">
            AND u.user_name like concat('%',#{bo.userName},'%')
        </if>
        <if test="bo.nickName != null and bo.nickName != ''">
            AND u.nick_name like concat('%',#{bo.nickName},'%')
        </if>
        <if test="bo.phoneNumber != null and bo.phoneNumber != ''">
            AND u.phone_number like concat('%',#{bo.phoneNumber},'%')
        </if>
    </select>

    <select id="getUserNum" resultType="java.lang.Long">
        SELECT count(1) FROM `sys_user`
        ${ew.getCustomSqlSegment}
    </select>

    <select id="getTenantRegisterNum" resultType="com.sohu.system.api.vo.TenantRegisterVo">
        SELECT tenant_id,count(1) as registerNum FROM `sys_user` WHERE tenant_id in
        <foreach collection="tenantIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach> group by tenant_id
    </select>
</mapper>
