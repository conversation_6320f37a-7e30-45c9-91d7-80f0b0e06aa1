<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.system.mapper.SysPlatformUserMapper">

    <select id="selectRolePermissionByUserId" resultType="java.lang.String">
        SELECT
            spr.role_key
        FROM
            `sys_platform_user` spu
                JOIN sys_platform_role spr ON spu.platform_role_id = spr.id
        WHERE
            spu.user_id = #{userId}
          AND spr.STATUS = 'ENABLE' and spr.del_flag = 0
    </select>

</mapper>
