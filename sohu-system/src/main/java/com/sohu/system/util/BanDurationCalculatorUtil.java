package com.sohu.system.util;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
public class BanDurationCalculatorUtil {
    // 使用一个 Map 来存储时长描述到计算逻辑的映射
    // Key: duration_description (如 "0.5h", "1天")
    // Value: 一个函数，接收 banDateTime，返回 expectedEndDateTime
    private static final Map<String, Function<LocalDateTime, LocalDateTime>> DURATION_CALCULATORS = new HashMap<>();

    static {
        DURATION_CALCULATORS.put("0.5h", banTime -> banTime.plusMinutes(30));
        DURATION_CALCULATORS.put("1h", banTime -> banTime.plusHours(1));
        DURATION_CALCULATORS.put("3h", banTime -> banTime.plusHours(3));
        DURATION_CALCULATORS.put("5h", banTime -> banTime.plusHours(5));
        DURATION_CALCULATORS.put("7h", banTime -> banTime.plusHours(7));
        DURATION_CALCULATORS.put("12h", banTime -> banTime.plusHours(12));
        DURATION_CALCULATORS.put("1天", banTime -> banTime.plusDays(1));
        DURATION_CALCULATORS.put("3天", banTime -> banTime.plusDays(3));
        DURATION_CALCULATORS.put("5天", banTime -> banTime.plusDays(5));
        DURATION_CALCULATORS.put("7天", banTime -> banTime.plusDays(7));
        DURATION_CALCULATORS.put("15天", banTime -> banTime.plusDays(15));
        DURATION_CALCULATORS.put("30天", banTime -> banTime.plusDays(30));
        DURATION_CALCULATORS.put("永久", banTime -> null);
    }

    /**
     * 根据封禁开始时间和时长描述计算预计结束时间
     * @param banDateTime 封禁开始时间
     * @param durationDescription 时长描述 (如 "1h", "3天", "永久")
     * @return 预计结束时间，如果为永久则返回 null，如果时长描述无效则抛出异常或返回特定值
     */
    public static LocalDateTime calculateExpectedEndTime(LocalDateTime banDateTime, String durationDescription) {
        if (banDateTime == null || durationDescription == null || durationDescription.trim().isEmpty()) {
            throw new IllegalArgumentException("封禁开始时间或时长描述不能为空");
        }

        Function<LocalDateTime, LocalDateTime> calculator = DURATION_CALCULATORS.get(durationDescription);
        if (calculator == null) {
            // 可以选择抛出异常，或者记录日志并返回一个默认行为（例如视为永久或短期）
            // 这里为了清晰，选择抛出异常
            throw new IllegalArgumentException("不支持的封禁时长描述: " + durationDescription);
        }
        return calculator.apply(banDateTime);
}
}
