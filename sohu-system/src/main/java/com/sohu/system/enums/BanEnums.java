package com.sohu.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 封禁相关枚举
 *
 * <AUTHOR>
 */
public class BanEnums {

    @Getter
    @AllArgsConstructor
    public enum BanTypeEnum {
        IP("ip","IP地址"),
        DEVICE("device","设备"),
        NORMAL("normal","正常-未封禁"),
        ACCOUNT("account","账号");

        private final String type;
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum BanStatusEnum {
        ACTIVE("active","封禁中"),
        LIFTED("lifted","已解封");
//        EXPIRED("expired","已解封-到期");

        private final String type;
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum UserOperationTypeEnum {
        RESET_AVATAR("RESET_AVATAR","重置头像"),
        RESET_NICKNAME("RESET_NICKNAME","重置昵称"),
        RESET_REMARK("RESET_REMARK","重置签名"),
        BAN_ACCOUNT("BAN_ACCOUNT","封禁账号"),
        UNBAN_ACCOUNT("UNBAN_ACCOUNT","解封账号");

        private final String type;
        private final String desc;
    }

    public static BanTypeEnum fromType(String type) {
        for (BanTypeEnum e : BanTypeEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return null;
    }

}
