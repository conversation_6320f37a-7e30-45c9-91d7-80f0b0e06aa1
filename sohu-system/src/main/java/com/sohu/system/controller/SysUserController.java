package com.sohu.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.utils.*;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.core.ExcelResult;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.enums.LabelEnum;
import com.sohu.middle.api.service.RemoteMiddleCommonLabelService;
import com.sohu.middle.api.service.RemoteMiddleSiteService;
import com.sohu.middle.api.vo.SohuSiteVo;
import com.sohu.middle.api.vo.SohuUserLabelRelationVo;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.system.api.RemoteUserService;
import com.sohu.system.api.bo.SohuDisableUserBo;
import com.sohu.system.api.bo.SohuWarnBo;
import com.sohu.system.api.domain.SysDept;
import com.sohu.system.api.domain.SysRole;
import com.sohu.system.api.domain.SysUser;
import com.sohu.system.api.vo.*;
import com.sohu.system.listener.SysUserImportListener;
import com.sohu.system.service.*;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR> Li
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/user")
public class SysUserController extends BaseController {

    private final ISysUserService userService;
    private final ISysRoleService roleService;
    private final ISysPostService postService;
    private final ISysPermissionService permissionService;
    private final ISysDeptService deptService;
    private final ISysPlatformRoleService platformRoleService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteMiddleSiteService remoteMiddleSiteService;
    @DubboReference
    private RemoteAccountService remoteAccountService;
    @DubboReference
    private RemoteMiddleCommonLabelService remoteMiddleCommonLabelService;

    /**
     * 获取用户列表
     */
    @SaCheckPermission("system:user:list")
    @GetMapping("/list")
    public TableDataInfo<SysUser> list(SysUser user, PageQuery pageQuery) {
        return userService.selectPageUserList(user, pageQuery);
    }

    /**
     * 导出用户列表
     */
    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @SaCheckPermission("system:user:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        List<SysUserExportVo> listVo = BeanUtil.copyToList(list, SysUserExportVo.class);
        for (int i = 0; i < list.size(); i++) {
            SysDept dept = list.get(i).getDept();
            SysUserExportVo vo = listVo.get(i);
            if (ObjectUtil.isNotEmpty(dept)) {
                vo.setDeptName(dept.getDeptName());
                vo.setLeader(dept.getLeader());
            }
        }
        ExcelUtil.exportExcel(listVo, "用户数据", SysUserExportVo.class, response);
    }

    /**
     * 导出风控用户列表
     */
    @Log(title = "风控用户管理", businessType = BusinessType.EXPORT)
    @SaCheckPermission("system:user:export")
    @PostMapping("/ban/export")
    public void banExport(HttpServletResponse response, SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        List<SysBanUserExportVo> listVo = BeanUtil.copyToList(list, SysBanUserExportVo.class);
        ExcelUtil.exportExcel(listVo, "风控用户数据", SysBanUserExportVo.class, response);
    }

    /**
     * 导入用户列表
     *
     * @param file          导入文件
     * @param updateSupport 更新已有数据
     */
    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @SaCheckPermission("system:user:import")
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Boolean> importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelResult<SysUserImportVo> result = ExcelUtil.importExcel(file.getInputStream(), SysUserImportVo.class, new SysUserImportListener(updateSupport));
        return R.ok(result.getAnalysis());
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil.exportExcel(new ArrayList<>(), "用户数据", SysUserImportVo.class, response);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public R<Map<String, Object>> getInfo() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Objects.requireNonNull(loginUser, MessageUtils.message("WRONG_PARAMS"));
        SysUser user = userService.selectUserById(loginUser.getUserId());
        SohuSiteVo siteVo = remoteMiddleSiteService.queryByStationmasterId(loginUser.getUserId());
        if (Objects.nonNull(siteVo)) {
            user.setSiteId(siteVo.getId());
            user.setSiteCate(siteVo.getSiteCate());
            user.setPlatformIndustryId(siteVo.getPlatformIndustryId());
        }
        if (CollUtil.isEmpty(loginUser.getRoles())) {
            loginUser = remoteUserService.buildLoginUser(user);
        }
        // 获取账户信息
        SohuAccountVo sohuAccountModel = remoteAccountService.queryByUserIdOfPass(loginUser.getUserId());
        Map<String, Object> ajax = new HashMap<>();
        ajax.put("user", user);
        ajax.put("roles", loginUser.getRolePermission());
        if (CollUtil.isEmpty(user.getPlatformRoles())) {
            ajax.put("platformRoles", new ArrayList<>());
        } else {
            ajax.put("platformRoles", user.getPlatformRoles().stream().map(p -> p.getRoleKey()).collect(Collectors.toSet()));
        }
        ajax.put("permissions", loginUser.getMenuPermission());
        ajax.put("hasPassword", StringUtils.isNotBlank(user.getPassword()) ? Boolean.TRUE : Boolean.FALSE);
        ajax.put("hasPayPassword", StringUtils.isNotBlank(loginUser.getPayPassword()) ? Boolean.TRUE : Boolean.FALSE);
        ajax.put("accountType", Objects.nonNull(sohuAccountModel) ? sohuAccountModel.getAccountType() : Boolean.FALSE);
        ajax.put("accountBank", Objects.nonNull(remoteAccountService.queryAccountBankByUserId(loginUser.getUserId())) ? Boolean.TRUE : Boolean.FALSE);
        List<SohuSiteVo> siteVos = remoteMiddleSiteService.listByStationmasterId(loginUser.getUserId(), 0);
        ajax.put("stationmasterSiteList", siteVos);
        return R.ok(ajax);
    }

    /**
     * 根据用户编号获取详细信息
     *
     * @param userId 用户ID
     */
    //@SaCheckPermission("system:user:query")
    @GetMapping(value = {"/", "/{userId}"})
    public R<Map<String, Object>> getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        userService.checkUserDataScope(userId);
        Map<String, Object> ajax = new HashMap<>();
        List<SysRole> roles = roleService.selectRoleAll();
        List<SohuUserLabelRelationVo> userLabelRelationVoList = remoteMiddleCommonLabelService.queryLabelsByUserId(userId);
        ajax.put("roles", LoginHelper.isAdmin(userId) ? roles : StreamUtils.filter(roles, r -> !r.isAdmin()));
        ajax.put("posts", postService.selectPostAll());
        ajax.put("platformRoles", platformRoleService.queryListOfEnable());
        if (ObjectUtil.isNotNull(userId)) {
            SysUser sysUser = userService.selectUserById(userId);
            sysUser.setCommonLabelIds(getLabelIdsByType(userLabelRelationVoList, LabelEnum.COMMON.getCode()));
            sysUser.setIndustryLabelIds(getLabelIdsByType(userLabelRelationVoList, LabelEnum.INDUSTRY.getCode()));
            sysUser.setContentTagIds(getLabelIdsByType(userLabelRelationVoList, LabelEnum.CONTENT.getCode()));
            ajax.put("user", sysUser);
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", StreamUtils.toList(sysUser.getRoles(), SysRole::getRoleId));
            ajax.put("platformRoleIds", StreamUtils.toList(sysUser.getPlatformRoles(), SysPlatformRoleVo::getId));
            ajax.put("labelList", userLabelRelationVoList);
        }
        return R.ok(ajax);
    }

    /**
     * 根据标签标识过滤标签数据
     */
    private List<Long> getLabelIdsByType(List<SohuUserLabelRelationVo> userLabelRelationVoList, String labelCode) {
        return userLabelRelationVoList.stream()
                .filter(vo -> vo.getLabelType().equals(labelCode))
                .map(SohuUserLabelRelationVo::getLabelId)
                .collect(Collectors.toList());
    }

    /**
     * 根据用户编号获取用户详细信息
     *
     * @param userId 用户ID
     */
    @Operation(summary = "根据用户编号获取用户详细信息", description = "负责人：张良峰，根据用户编号获取用户详细信息")
    @SaCheckPermission(value = "system:user:edit", orRole = {"admin"})
    @GetMapping("/getInfo/{userId}")
    public R<SysUserInfoVo> getInfoByUserId(@PathVariable(value = "userId", required = true) Long userId) {
        return R.ok(userService.getInfoByUserId(userId));
    }

    /**
     * 新增用户
     */
    @SaCheckPermission("system:user:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody SysUser user) {
        if (!userService.checkUserNameUnique(user)) {
            return R.fail("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhoneNumber())
                && !userService.checkPhoneUnique(user)) {
            return R.fail("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && !userService.checkEmailUnique(user)) {
            return R.fail("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setPassword(BCrypt.hashpw(user.getPassword()));
        int i = userService.insertUser(user);
//        if (i > 0) {
//            sohuAirecUserService.insertBySysUser(user);
//        }
        return toAjax(i);
    }

    /**
     * 管理员修改用户信息
     */
    @Operation(summary = "管理员修改用户信息", description = "负责人：张良峰，管理员修改用户信息")
    @SaCheckPermission(value = "system:user:edit", orRole = {"admin"})
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody SysUser user) {
        // 通过注解校验操作数据权限以及角色判断
//        userService.checkUserAllowed(user);
//        userService.checkUserDataScope(user.getUserId());
        if (!userService.checkUserNameUnique(user)) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhoneNumber())
                && !userService.checkPhoneUnique(user)) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && !userService.checkEmailUnique(user)) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     *
     * @param userIds 用户ID串
     */
    @SaCheckPermission("system:user:remove")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public R<Boolean> remove(@PathVariable Long[] userIds) {
        if (ArrayUtils.contains(userIds, LoginHelper.getUserId())) {
            return R.fail("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码-管理员使用
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public R<Boolean> resetPwd(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setPassword(BCrypt.hashpw(user.getPassword()));
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Boolean> changeStatus(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     *
     * @param userId 用户ID
     */
    @SaCheckPermission("system:user:query")
    @GetMapping("/authRole/{userId}")
    public R<Map<String, Object>> authRole(@PathVariable("userId") Long userId) {
        Map<String, Object> ajax = new HashMap<>();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", LoginHelper.isAdmin(userId) ? roles : StreamUtils.filter(roles, r -> !r.isAdmin()));
        return R.ok(ajax);
    }

    /**
     * 用户授权角色
     *
     * @param userRoleVo
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public R<Boolean> insertAuthRole(@RequestBody SysUserRoleVo userRoleVo) {
        log.info("userRoleVo:{}", JsonUtils.toJsonString(userRoleVo));
        userService.checkUserDataScope(userRoleVo.getUserId());
        userService.insertUserAuth(userRoleVo.getUserId(), userRoleVo.getRoleIds());
        return R.ok(Boolean.TRUE);
    }

    /**
     * 获取部门树列表
     */
    @SaCheckPermission("system:user:list")
    @GetMapping("/deptTree")
    public R<List<Tree<Long>>> deptTree(SysDept dept) {
        return R.ok(deptService.selectDeptTreeList(dept));
    }

    /**
     * 用户警告消息推送
     */
    @Log(title = "用户警告消息推送", businessType = BusinessType.OTHER)
    @PostMapping("/pushWarningMsg")
    public R<Void> pushWarningMsg(@RequestBody SohuWarnBo bo) {
        // TODO 判断用户是否有风控角色,使用@SaCheckRole
        userService.pushWarningMsg(bo);
        return R.ok();
    }

    /**
     * 用户封禁系列操作
     *
     * @param bo 用户信息
     * @return 结果
     */
    @Log(title = "用户封禁", businessType = BusinessType.OTHER)
    @PostMapping("/disable")
    public R<Boolean> disable(@RequestBody SohuDisableUserBo bo) {
        // TODO 判断用户是否有风控角色,使用@SaCheckRole
        return R.ok(userService.disable(bo));
    }

}
