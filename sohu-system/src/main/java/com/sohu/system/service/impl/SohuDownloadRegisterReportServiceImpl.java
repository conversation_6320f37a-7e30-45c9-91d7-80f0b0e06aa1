package com.sohu.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.system.api.bo.SohuDownloadRegisterReportBo;
import com.sohu.system.api.domain.SysUser;
import com.sohu.system.api.vo.SohuDownloadRegisterReportVo;
import com.sohu.system.domain.SohuDownloadRegisterReport;
import com.sohu.system.mapper.SohuDownloadRegisterReportMapper;
import com.sohu.system.service.ISohuDownloadRegisterReportService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 下载注册统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@RequiredArgsConstructor
@Service
public class SohuDownloadRegisterReportServiceImpl implements ISohuDownloadRegisterReportService {

    private final SohuDownloadRegisterReportMapper baseMapper;

    /**
     * 查询下载注册统计
     */
    @Override
    public SohuDownloadRegisterReportVo queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询下载注册统计列表
     */
    @Override
    public TableDataInfo<SohuDownloadRegisterReportVo> queryPageList(SohuDownloadRegisterReportBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuDownloadRegisterReport> lqw = buildQueryWrapper(bo);
        Page<SohuDownloadRegisterReportVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询下载注册统计列表
     */
    @Override
    public List<SohuDownloadRegisterReportVo> queryList(SohuDownloadRegisterReportBo bo) {
        LambdaQueryWrapper<SohuDownloadRegisterReport> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuDownloadRegisterReport> buildQueryWrapper(SohuDownloadRegisterReportBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuDownloadRegisterReport> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuDownloadRegisterReport::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceCode()), SohuDownloadRegisterReport::getDeviceCode, bo.getDeviceCode());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceBrand()), SohuDownloadRegisterReport::getDeviceBrand, bo.getDeviceBrand());
        lqw.eq(StringUtils.isNotBlank(bo.getDevicePlatform()), SohuDownloadRegisterReport::getDevicePlatform, bo.getDevicePlatform());
        lqw.eq(StringUtils.isNotBlank(bo.getAppMarket()), SohuDownloadRegisterReport::getAppMarket, bo.getAppMarket());
        lqw.eq(StringUtils.isNotBlank(bo.getDownloadChannel()), SohuDownloadRegisterReport::getDownloadChannel, bo.getDownloadChannel());
        lqw.eq(bo.getDownloadTime() != null, SohuDownloadRegisterReport::getDownloadTime, bo.getDownloadTime());
        lqw.eq(StringUtils.isNotBlank(bo.getRegisterChannel()), SohuDownloadRegisterReport::getRegisterChannel, bo.getRegisterChannel());
        lqw.eq(bo.getRegisterTime() != null, SohuDownloadRegisterReport::getRegisterTime, bo.getRegisterTime());
        lqw.eq(StringUtils.isNotBlank(bo.getRegisterAccount()), SohuDownloadRegisterReport::getRegisterAccount, bo.getRegisterAccount());
        lqw.eq(StringUtils.isNotBlank(bo.getRegisterAddress()), SohuDownloadRegisterReport::getRegisterAddress, bo.getRegisterAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getRegisterIp()), SohuDownloadRegisterReport::getRegisterIp, bo.getRegisterIp());
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            lqw.ge(SohuDownloadRegisterReport::getCreateTime, DateUtils.beginOfTime(bo.getStartTime()));
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            lqw.le(SohuDownloadRegisterReport::getCreateTime, DateUtils.endOfTime(bo.getEndTime()));
        }
        return lqw;
    }

    /**
     * 新增下载注册统计
     */
    @Override
    public Boolean insertByBo(SohuDownloadRegisterReportBo bo) {
        SohuDownloadRegisterReport add = BeanUtil.toBean(bo, SohuDownloadRegisterReport.class);
        if (baseMapper.exists(Wrappers.lambdaQuery(SohuDownloadRegisterReport.class).eq(SohuDownloadRegisterReport::getDeviceCode, add.getDeviceCode()))) {
            return true;
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改下载注册统计
     */
    @Override
    public Boolean updateByBo(SohuDownloadRegisterReportBo bo) {
        SohuDownloadRegisterReport update = BeanUtil.toBean(bo, SohuDownloadRegisterReport.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuDownloadRegisterReport entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除下载注册统计
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean canRegister(String deviceCode) {
        return baseMapper.selectCount(Wrappers.lambdaQuery(SohuDownloadRegisterReport.class).eq(SohuDownloadRegisterReport::getDeviceCode, deviceCode)) < 2;
    }

    @Override
    public void saveRegisterUserInfo(SohuDownloadRegisterReportBo bo) {
        // 查询该设备号下的所有记录,排除设备号为unknown的记录
        List<SohuDownloadRegisterReport> deviceRecords = baseMapper.selectList(Wrappers.lambdaQuery(SohuDownloadRegisterReport.class)
                .eq(SohuDownloadRegisterReport::getDeviceCode, bo.getDeviceCode())
                .ne(SohuDownloadRegisterReport::getDeviceCode, "unknown"));

        // 如果是下载操作(注册账号为空)
        if (StringUtils.isBlank(bo.getRegisterAccount())) {
            if (deviceRecords.isEmpty()) {
                // 没有记录则新增下载记录
                this.insertByBo(bo);
            }
            return;
        }

        // 查找未绑定注册账号的下载记录
        SohuDownloadRegisterReport emptyRecord = deviceRecords.stream()
                .filter(record -> StringUtils.isBlank(record.getRegisterAccount()))
                .findFirst()
                .orElse(null);

        // 查找是否存在相同手机号的记录
        SohuDownloadRegisterReport existRecord = deviceRecords.stream()
                .filter(record -> record.getRegisterAccount() != null
                        && record.getRegisterAccount().equals(bo.getRegisterAccount()))
                .findFirst()
                .orElse(null);

        if (existRecord != null) {
            // 更新已存在的注册记录
            existRecord.setRegisterTime(bo.getRegisterTime());
            existRecord.setRegisterAddress(bo.getRegisterAddress());
            existRecord.setRegisterIp(bo.getRegisterIp());
            existRecord.setUserId(bo.getUserId());
            existRecord.setRegisterChannel(bo.getRegisterChannel());
            baseMapper.updateById(existRecord);
            return;
        }

        // 获取已注册账号的记录数
        long registeredCount = deviceRecords.stream()
                .filter(record -> StringUtils.isNotBlank(record.getRegisterAccount()))
                .count();

        if (registeredCount >= 2) {
            throw new RuntimeException("该设备已绑定两个账号,无法继续绑定");
        }

        if (emptyRecord != null) {
            // 将下载记录更新为注册记录
            emptyRecord.setRegisterTime(bo.getRegisterTime());
            emptyRecord.setRegisterAccount(bo.getRegisterAccount());
            emptyRecord.setRegisterAddress(bo.getRegisterAddress());
            emptyRecord.setRegisterIp(bo.getRegisterIp());
            emptyRecord.setUserId(bo.getUserId());
            emptyRecord.setRegisterChannel(bo.getRegisterChannel());
            baseMapper.updateById(emptyRecord);
        } else {
            // 插入新的注册记录
            this.insertByBo(bo);
        }
    }

    @Override
    public Boolean checkData(String deviceCode) {
        return baseMapper.selectCount(Wrappers.lambdaQuery(SohuDownloadRegisterReport.class).eq(SohuDownloadRegisterReport::getDeviceCode, deviceCode)) > 0;
    }

    @Override
    public Long getDownloadNumByCreateTime(Date startTime, Date endTime) {
        LambdaQueryWrapper<SohuDownloadRegisterReport> lqw = new LambdaQueryWrapper<>();
        lqw.between(SohuDownloadRegisterReport::getDownloadTime, startTime, endTime);
        return this.baseMapper.selectCount(lqw);
    }

    @Override
    public Long getRegisterNumByCreateTime(Date startTime, Date endTime) {
        LambdaQueryWrapper<SohuDownloadRegisterReport> lqw = new LambdaQueryWrapper<>();
        lqw.between(SohuDownloadRegisterReport::getRegisterTime, startTime, endTime);
        return this.baseMapper.selectCount(lqw);
    }

    public Boolean checkRegister(String registerAccount) {
        return baseMapper.selectCount(Wrappers.lambdaQuery(SohuDownloadRegisterReport.class).eq(SohuDownloadRegisterReport::getRegisterAccount, registerAccount)) > 0;
    }
}
