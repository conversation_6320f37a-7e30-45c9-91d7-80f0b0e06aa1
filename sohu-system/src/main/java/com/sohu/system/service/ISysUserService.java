package com.sohu.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.system.api.bo.SohuDisableUserBo;
import com.sohu.system.api.bo.SohuWarnBo;
import com.sohu.system.api.bo.SysUserQueryBo;
import com.sohu.system.api.domain.SysUser;
import com.sohu.system.api.domain.UserVo;
import com.sohu.system.api.vo.SohuUserOrderInfoVo;
import com.sohu.system.api.vo.SysUserInfoVo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户 业务层
 *
 * <AUTHOR>
 */
public interface ISysUserService {
    TableDataInfo<SysUser> selectPageUserList(SysUser user, PageQuery pageQuery);

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    List<SysUser> selectUserList(SysUser user);

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    TableDataInfo<SysUser> selectAllocatedList(SysUser user, PageQuery pageQuery);

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    TableDataInfo<SysUser> selectUnallocatedList(SysUser user, PageQuery pageQuery);

    /**
     * 根据条件分页查询已分配用户平台角色列表
     *
     * @return 用户信息集合信息
     */
    TableDataInfo<SysUser> selectAllocatedListOfPlatform(SysUserQueryBo bo, PageQuery pageQuery);

    /**
     * 根据条件分页查询未分配用户平台角色列表
     *
     * @return 用户信息集合信息
     */
    TableDataInfo<SysUser> selectUnallocatedListOfPlatform(SysUserQueryBo bo, PageQuery pageQuery);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    SysUser selectUserByUserName(String userName);

    /**
     * 通过手机号查询用户
     *
     * @param phonenumber 手机号
     * @return 用户对象信息
     */
    SysUser selectUserByPhonenumber(String phonenumber);


    /**
     * 通过邮箱
     *
     * @param email 邮箱
     * @return 用户对象信息
     */
    SysUser selectUserByEmail(String email);


    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    SysUser selectUserById(Long userId);


    /**
     * 通过用户ID查询用户(已删除也查出来)
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    SysUser selectAllUserById(Long userId);

    /**
     * 根据用户ID查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    String selectUserRoleGroup(String userName);

    /**
     * 根据用户ID查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    String selectUserPostGroup(String userName);

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean checkUserNameUnique(SysUser user);

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean checkPhoneUnique(SysUser user);

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean checkEmailUnique(SysUser user);

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    void checkUserAllowed(SysUser user);

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    void checkUserDataScope(Long userId);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int insertUser(SysUser user);

    /**
     * 更新用户年龄信息
     *
     * @param user 用户信息
     * @return 结果
     */
    void saveUserAge(SysUser user);

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    Long registerUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUser(SysUser user);

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    void insertUserAuth(Long userId, String roleIds);

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUserStatus(SysUser user);

    /**
     * 修改用户状态
     *
     * @param userId
     * @param status 帐号状态（0正常 1停用）
     */
    void updateUserStatus(Long userId, String status);

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUserProfile(SysUser user);

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    boolean updateUserAvatar(String userName, String avatar);

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    int resetPwd(SysUser user);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    int resetUserPwd(String userName, String password);

    /**
     * 手机号重置用户支付密码
     *
     * @param phoneNumber 手机号
     * @param payPassword 支付密码
     * @return 结果
     */
    int resetUserPayPasswordByPhone(String phoneNumber, String payPassword);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    int deleteUserByIds(Long[] userIds);

    /**
     * 通过用户ID查询用户账户
     *
     * @param userId 用户ID
     * @return 用户账户
     */
    String selectUserNameById(Long userId);

    /**
     * 查询用户列表
     */
    List<SysUser> selectUserList(Collection<Long> ids);

    Page<UserVo> page(Collection<Long> notInUserIds, String keyword, Page<UserVo> page);

    /**
     * 根据站点id筛选用户
     *
     * @param cityIds
     * @return
     */
    List<Long> getUserBySiteId(List<Long> cityIds);

    /**
     * 初始化智能推荐用户
     */
    Boolean initAirecUsers();

    /**
     * 修改支付密码发送消息通知
     *
     * @param userId
     */
    void sendMsgOfUpdatePayPwd(Long userId);

    /**
     * 修改登录密码发送消息通知
     *
     * @param userId
     */
    void sendMsgOfUpdatePwd(Long userId);

    /**
     * 修改手机号发送消息通知
     *
     * @param userId
     */
    void sendMsgOfUpdatePhone(Long userId, String phone);

    /**
     * 修改邮箱发送消息通知
     *
     * @param userId
     * @param email
     */
    void sendMsgOfUpdateEmail(Long userId, String email);

    /**
     * 根据用户id获取用户信息
     *
     * @param userId 用户id
     * @return SysUserInfoVo
     */
    SysUserInfoVo getInfoByUserId(Long userId);

    /**
     * 更换手机号
     *
     * @param newPhoneNumber
     */
    Boolean updatePhoneNumber(String newPhoneNumber);

    /**
     * 获取时间区间内创建的人数
     *
     * @param startTime
     * @param endTime
     * @return
     */
    Long getUserNumByCreateTime(Date startTime, Date endTime);

    /**
     * 获取平台用户总数
     *
     * @return
     */
    Long getUserTotalNum();

    /**
     * 注册IM用户
     *
     * @param phone
     * @param serverCode
     * @return
     */
    Boolean registerImUser(String phone, String serverCode);

    /**
     * 获取租户注册人数
     *
     * @param tenantIds
     * @return
     */
    Map<Long, Integer> getTenantRegisterNum(List<Long> tenantIds);

    /**
     * 查询用户id集合
     *
     * @param bo
     * @return
     */
    List<Long> queryUserIdByBo(SysUserQueryBo bo);

    /**
     * 查询用户信息
     *
     * @param userId
     * @return
     */
    SysUser queryByUserId(Long userId);

    /**
     * 绑定邀请人信息
     *
     * @param userId
     * @param inviteUserId
     */
    void updateInviteUserId(Long userId, Long inviteUserId);

    /**
     * 查询角色用户收入总额
     *
     * @param userId    用户Id
     * @param siteType  站点类型
     * @param siteId    站点Id
     * @param userRole  用户查询角色 {@link com.sohu.common.core.enums.UserRoleEnum}
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    BigDecimal getUserIncome(Long userId, Integer siteType, Long siteId, String userRole, Integer independentStatus, Date startTime, Date endTime);

    /**
     * 查询角色用户提现总额
     *
     * @param userId    用户Id
     * @param siteType  站点类型
     * @param siteId    站点Id
     * @param userRole  用户查询角色 {@link com.sohu.common.core.enums.UserRoleEnum}
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    BigDecimal getUserWithdrawal(Long userId, Integer siteType, Long siteId, String userRole, Date startTime, Date endTime);

    /**
     * 获取用户拉新收益
     *
     * @param userId            用户Id
     * @param siteType          站点类型
     * @param siteId            站点Id
     * @param independentObject 分账对象 {@link  com.sohu.common.core.enums.SohuIndependentObject}
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @return
     */
    BigDecimal getUserInviteIncome(Long userId, Integer siteType, Long siteId, String independentObject, Date startTime, Date endTime);

    /**
     * 获取用户订单量与交易金额
     *
     * @param userId            用户Id
     * @param siteType          站点类型
     * @param siteId            站点Id
     * @param independentObject 分账对象 {@link  com.sohu.common.core.enums.SohuIndependentObject}
     * @param tradeType         订单类型
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @return
     */
    SohuUserOrderInfoVo getUserOrderInfo(Long userId, Integer siteType, Long siteId, List<String> independentObject, String tradeType, Date startTime, Date endTime);

    /**
     * 用户警告消息推送
     *
     * @param bo 警告消息对象
     */
    void pushWarningMsg(SohuWarnBo bo);

    /**
     * 用户禁用
     *
     * @param bo 禁用用户对象
     * @return 结果
     */
    Boolean disable(SohuDisableUserBo bo);

    /**
     * 处理到期的封禁任务（定时任务调用）
     */
    void processExpiredBanTasks();

    /**
     * 检查用户是否被封禁
     *
     * @param userId 用户ID
     * @return 是否被封禁
     */
    boolean isUserBanned(Long userId);
}
