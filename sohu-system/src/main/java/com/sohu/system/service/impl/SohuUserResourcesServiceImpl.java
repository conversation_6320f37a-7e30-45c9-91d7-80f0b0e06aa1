package com.sohu.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuFriendsBo;
import com.sohu.middle.api.service.RemoteMiddleFriendService;
import com.sohu.middle.api.vo.SohuFriendsVo;
import com.sohu.system.api.RemoteUserService;
import com.sohu.system.api.bo.SohuUserResourcesBo;
import com.sohu.system.api.vo.SohuUserResourcesVo;
import com.sohu.system.constant.ResourceConstants;
import com.sohu.system.domain.SohuUserResources;
import com.sohu.system.mapper.SohuUserResourcesMapper;
import com.sohu.system.service.ISohuUserResourcesService;
import com.sohu.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户人脉资源Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@RequiredArgsConstructor
@Service
public class SohuUserResourcesServiceImpl implements ISohuUserResourcesService {

    private final SohuUserResourcesMapper baseMapper;

    @DubboReference
    private RemoteMiddleFriendService remoteMiddleFriendService;
    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 查询用户人脉资源
     */
    @Override
    public SohuUserResourcesVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询用户人脉资源列表
     */
    @Override
    public TableDataInfo<SohuUserResourcesVo> queryPageList(SohuUserResourcesBo bo, PageQuery pageQuery) {
        // 1. 获取兜底数据 (提前获取，避免重复调用)
        List<SohuUserResourcesVo> sohuUserResourcesVoList = getSohuUserResourcesVos();

        // 兜底逻辑判空逻辑
        if (CollUtil.isEmpty(sohuUserResourcesVoList)) {
            return TableDataInfoUtils.build();
        }

        // 2. 用户未登录时，随机取两条记录
        if (Objects.isNull(LoginHelper.getUserId())) {
            return TableDataInfoUtils.build(sohuUserResourcesVoList);
        }

        // 3. 用户登录时，查询用户的好友的人脉资源
        SohuFriendsBo sohuFriendsBo = new SohuFriendsBo();
        sohuFriendsBo.setUserId(LoginHelper.getUserId());
        List<SohuFriendsVo> sohuFriendsVoList = remoteMiddleFriendService.queryList(sohuFriendsBo);

        if (CollUtil.isEmpty(sohuFriendsVoList)) {
            // 如果用户没有好友，则返回兜底数据
            return TableDataInfoUtils.build(sohuUserResourcesVoList);
        }

        // 4. 查询用户的好友的人脉资源
        LambdaQueryWrapper<SohuUserResources> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuUserResources::getUserId, sohuFriendsVoList.stream().map(SohuFriendsVo::getFriendId).collect(Collectors.toList()));
        lqw.eq(SohuUserResources::getIsDisplayed, Constants.ONE);
        lqw.eq(SohuUserResources::getIsDel, Constants.ZERO);
        lqw.orderByDesc(SohuUserResources::getCreateTime);

        Page<SohuUserResourcesVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuUserResourcesVo> friendResources = result.getRecords();

        if (CollUtil.isEmpty(friendResources)) {
            // 如果没有好友的人脉资源，则返回兜底数据
            return TableDataInfoUtils.build(sohuUserResourcesVoList);
        }

        // 根据人脉资源类型分类
        Map<String, List<SohuUserResourcesVo>> resourceTypeMap = result.getRecords().stream().collect(Collectors.groupingBy(SohuUserResourcesVo::getResourceType));
        List<SohuUserResourcesVo> provideList = resourceTypeMap.getOrDefault(ResourceConstants.RESOURCE_TYPE_PROVIDE, List.of());
        List<SohuUserResourcesVo> needList = resourceTypeMap.getOrDefault(ResourceConstants.RESOURCE_TYPE_NEED, List.of());
        // 5.构建最终结果，最多返回2条记录
        List<SohuUserResourcesVo> finalResult = buildFinalResult(provideList, needList, sohuUserResourcesVoList);

        // 6. 截取前两条数据
        List<SohuUserResourcesVo> subResult = finalResult.subList(Constants.ZERO, Math.min(Constants.TWO, finalResult.size()));

        // 7. 填充用户头像和昵称
        extendInfo(subResult);

        return TableDataInfoUtils.build(subResult);
    }

    /**
     * 信息拓展
     *
     * @param result 需要拓展的数据集合
     */
    private void extendInfo(List<SohuUserResourcesVo> result) {
        List<Long> userIds = result.stream().map(SohuUserResourcesVo::getUserId).collect(Collectors.toList());
        Map<Long, LoginUser> longLoginUserMap = remoteUserService.selectMap(userIds);

        for (SohuUserResourcesVo vo : result) {
            LoginUser loginUser = longLoginUserMap.get(vo.getUserId());
            if (loginUser != null) {
                vo.setAvatar(loginUser.getAvatar());
                vo.setNickname(loginUser.getNickname());
            }
        }
    }

    /**
     * 构建最终结果列表
     * 优先级：Provide类型 > Need类型 > 原始数据兜底
     *
     * @param provideList Provide类型资源列表
     * @param needList Need类型资源列表
     * @param allRecords 兜底数据
     * @return 构建好的结果列表
     */
    private List<SohuUserResourcesVo> buildFinalResult(List<SohuUserResourcesVo> provideList,
                                                       List<SohuUserResourcesVo> needList,
                                                       List<SohuUserResourcesVo> allRecords) {
        List<SohuUserResourcesVo> result = new ArrayList<>();

        // 1. 优先添加Provide类型数据
        addItemsToResult(result, provideList, Constants.TWO);

        // 2. 如果不足，补充Need类型数据
        if (result.size() < Constants.TWO) {
            addItemsToResult(result, needList, Constants.TWO - result.size());
        }

        // 3. 如果仍然不足，使用原始数据兜底
        if (result.size() < Constants.TWO) {
            // 使用 LinkedHashSet 去重
            Set<SohuUserResourcesVo> uniqueRecords = new LinkedHashSet<>(result);

            // 从原始数据中过滤掉已经添加到结果集的数据
            List<SohuUserResourcesVo> distinctAllRecords = allRecords.stream()
                    .filter(record -> !uniqueRecords.contains(record))
                    .collect(Collectors.toList());

            addItemsToResult(result, distinctAllRecords, Constants.TWO - result.size());
        }

        return result;
    }

    /**
     * 向结果列表中添加指定数量的元素
     *
     * @param result 结果列表
     * @param source 源数据列表
     * @param maxCount 最大添加数量
     */
    private void addItemsToResult(List<SohuUserResourcesVo> result,
                                  List<SohuUserResourcesVo> source,
                                  int maxCount) {
        if (CollUtil.isEmpty(source) || maxCount <= Constants.ZERO) {
            return;
        }

        int addCount = Math.min(maxCount, source.size());
        result.addAll(source.subList(Constants.ZERO, addCount));
    }

    /**
     * 兜底方法
     *
     * @return List<SohuUserResourcesVo>
     */
    private List<SohuUserResourcesVo> getSohuUserResourcesVos() {
        List<SohuUserResourcesVo> sohuUserResourcesVos = baseMapper.selectVoList(new LambdaQueryWrapper<SohuUserResources>()
                .eq(SohuUserResources::getIsDisplayed, Constants.ONE)
                .eq(SohuUserResources::getIsDel, Constants.ZERO)
                .orderByDesc(SohuUserResources::getCreateTime)
                .last("limit 2"));

        // 补充扩展信息
        extendInfo(sohuUserResourcesVos);

        return sohuUserResourcesVos;
    }

    /**
     * 查询用户人脉资源列表
     */
    @Override
    public List<SohuUserResourcesVo> queryList(SohuUserResourcesBo bo) {
        LambdaQueryWrapper<SohuUserResources> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuUserResources> buildQueryWrapper(SohuUserResourcesBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuUserResources> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuUserResources::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), SohuUserResources::getContent, bo.getContent());
        lqw.eq(StringUtils.isNotBlank(bo.getLabels()), SohuUserResources::getLabels, bo.getLabels());
        lqw.eq(bo.getIsDisplayed() != null, SohuUserResources::getIsDisplayed, bo.getIsDisplayed());
        return lqw;
    }

    /**
     * 新增用户人脉资源
     */
    @Override
    public Boolean insertByBo(SohuUserResourcesBo bo) {
        validUserIdent(bo);
        SohuUserResources add = BeanUtil.toBean(bo, SohuUserResources.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;

    }

    /**
     * 修改用户人脉资源
     */
    @Override
    public Boolean updateByBo(SohuUserResourcesBo bo) {
        validUserIdent(bo);
        SohuUserResources update = BeanUtil.toBean(bo, SohuUserResources.class);
//        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 校验用户信息
     *
     * @param bo SohuUserResourcesBo
     */
    private static void validUserIdent(SohuUserResourcesBo bo) {
        if (!Objects.equals(bo.getUserId(), LoginHelper.getUserId())) {
            throw new RuntimeException("不能操作不属于自己的资源");
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuUserResources entity) {
        //TODO 做一些数据校验,如唯一约束
        LambdaQueryWrapper<SohuUserResources> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuUserResources::getUserId, entity.getUserId());
        lqw.eq(SohuUserResources::getIsDel, Constants.ZERO);
        lqw.eq(SohuUserResources::getResourceType, entity.getResourceType());
        SohuUserResources existingRecord = baseMapper.selectOne(lqw);

        if (existingRecord != null) {
            throw new RuntimeException("该人脉资源已存在");
        }
    }

    /**
     * 批量删除用户人脉资源
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            ids.forEach(id -> {
                SohuUserResources userResources = baseMapper.selectById(id);
                if (Objects.equals(userResources.getUserId(), LoginHelper.getUserId())) {
                    // 执行删除操作
                    baseMapper.deleteById(id);
                } else {
                    throw new RuntimeException("不能删除他人的资源");
                }
            });
        }

        return true;
    }

    @Override
    public List<SohuUserResourcesVo> getUserResourceByUserId(Long userId, String resourceType) {
        LambdaQueryWrapper<SohuUserResources> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuUserResources::getUserId, userId);
        if (!Objects.equals(userId, LoginHelper.getUserId())) {
            lqw.eq(SohuUserResources::getIsDel, Constants.ZERO);
            lqw.eq(SohuUserResources::getIsDisplayed, Constants.ONE);
        }
        if (StringUtils.isNotBlank(resourceType)) {
            lqw.eq(SohuUserResources::getResourceType, resourceType);
        }

        return baseMapper.selectVoList(lqw);
    }
}
