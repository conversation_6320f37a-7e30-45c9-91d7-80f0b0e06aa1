package com.sohu.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.RemoteBanRecordService;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.bo.SohuBanRecordsBo;
import com.sohu.admin.api.bo.merchant.CategoryInfo;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.admin.api.vo.SohuBanRecordsVo;
import com.sohu.admin.api.vo.SohuMerchantSettledVo;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.BanConstants;
import com.sohu.common.core.constant.CacheNames;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.UserConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.mybatis.db.CacheMgr;
import com.sohu.common.mybatis.helper.DataBaseHelper;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.noticeContent.NoticeContentDetail;
import com.sohu.im.api.noticeContent.NoticeSystemContent;
import com.sohu.middle.api.bo.SohuUserLabelRelationBo;
import com.sohu.middle.api.bo.SohuUserSiteRelationBo;
import com.sohu.middle.api.bo.airec.SohuAirecUserBo;
import com.sohu.middle.api.enums.AiRecTag;
import com.sohu.middle.api.enums.LabelEnum;
import com.sohu.middle.api.service.*;
import com.sohu.middle.api.service.airec.RemoteMiddleAirecTagRelationService;
import com.sohu.middle.api.service.airec.RemoteMiddleAirecUserService;
import com.sohu.middle.api.service.im.RemoteMiddleImTenantService;
import com.sohu.middle.api.service.notice.RemoteMiddleSystemNoticeService;
import com.sohu.middle.api.vo.SohuUserSiteRelationVo;
import com.sohu.middle.api.vo.im.SohuImTenantVo;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.pay.api.RemoteIndependentOrderService;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.pay.api.vo.SohuUserOrderModel;
import com.sohu.system.api.bo.SohuDisableUserBo;
import com.sohu.system.api.bo.SohuWarnBo;
import com.sohu.system.api.bo.SysUserQueryBo;
import com.sohu.system.api.domain.SysDept;
import com.sohu.system.api.domain.SysRole;
import com.sohu.system.api.domain.SysUser;
import com.sohu.system.api.domain.UserVo;
import com.sohu.system.api.vo.*;
import com.sohu.system.domain.SysPlatformUser;
import com.sohu.system.domain.SysPost;
import com.sohu.system.domain.SysUserPost;
import com.sohu.system.domain.SysUserRole;
import com.sohu.system.enums.BanEnums;
import com.sohu.system.mapper.*;
import com.sohu.system.service.ISysPlatformRoleService;
import com.sohu.system.service.ISysRoleService;
import com.sohu.system.service.ISysUserService;
import com.sohu.system.util.BanDurationCalculatorUtil;
import com.sohu.third.aliyun.airec.constants.AliyunAirecConstant;
import com.sohu.third.aliyun.airec.enums.AirecGenderEnum;
import com.sohu.third.aliyun.airec.enums.AirecUserIdTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sohu.system.enums.BanEnums.UserOperationTypeEnum.*;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysUserServiceImpl implements ISysUserService {

    private final SysUserMapper baseMapper;
    private final SysDeptMapper deptMapper;
    private final SysRoleMapper roleMapper;
    private final SysPostMapper postMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final SysUserPostMapper userPostMapper;
    private final SysPlatformRoleMapper platformRoleMapper;
    private final SysPlatformUserMapper platformUserMapper;
    private final SysLogininforMapper logininforMapper;
    private final AsyncConfig asyncConfig;
    @DubboReference
    private RemoteMiddleUserSiteRelationService remoteMiddleUserSiteRelationService;
    @DubboReference
    private RemoteMiddleUserFollowService remoteMiddleUserFollowService;
    @DubboReference
    private RemoteMiddleAirecUserService remoteMiddleAirecUserService;
    @DubboReference
    private RemoteMiddleAirecTagRelationService remoteMiddleAirecTagRelationService;
    @DubboReference
    private RemoteMiddleSystemNoticeService remoteMiddleSystemNoticeService;
    @DubboReference
    private RemoteAccountService remoteAccountService;
    @DubboReference
    private RemoteMiddleDeleteService remoteMiddleDeleteService;
    @DubboReference
    private RemoteMiddleImTenantService imTenantService;
    @DubboReference
    private RemoteMiddleCommonLabelService remoteMiddleCommonLabelService;
    @DubboReference
    private RemoteIndependentOrderService remoteIndependentOrderService;
    @DubboReference
    private RemoteMiddleBillRecordService remoteMiddleBillRecordService;
    @DubboReference
    private RemoteBanRecordService remoteBanRecordService;
    @DubboReference
    private RemoteMerchantService remoteMerchantService;

    private final ISysPlatformRoleService platformRoleService;
    private final ISysRoleService sysRoleService;

    @Override
    public TableDataInfo<SysUser> selectPageUserList(SysUser user, PageQuery pageQuery) {
        Page<SysUser> page = baseMapper.selectPageUserList(PageQueryUtils.build(pageQuery), this.buildQueryWrapper(user));
        return TableDataInfoUtils.build(page);
    }

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUser> selectUserList(SysUser user) {
        return baseMapper.selectUserList(this.buildQueryWrapper(user));
    }

    private Wrapper<SysUser> buildQueryWrapper(SysUser user) {
        Map<String, Object> params = user.getParams();
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        if (user.getCommonLabelIds() != null && !user.getCommonLabelIds().isEmpty()) {
            String labelIds = user.getCommonLabelIds().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            wrapper.and(w -> w.inSql("user_id",
                    "SELECT user_id FROM sohu_user_label_relation WHERE label_id IN (" + labelIds + ")"
            ));
        }
        //        if (user.getIndustryLabelIds() != null && !user.getIndustryLabelIds().isEmpty()) {
//            wrapper.and(w -> wrapper.inSql("user_id",
//                    "SELECT user_id FROM sohu_user_label_relation WHERE label_id IN (" +
//                            String.join(",", user.getIndustryLabelIds().stream().map(String::valueOf).collect(Collectors.toList())) + ")"
//            ));
//        }
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL)
                .eq(ObjectUtil.isNotNull(user.getUserId()), "u.user_id", user.getUserId())
                .eq(StrUtil.isNotBlank(user.getBanStatus()), "u.ban_status", user.getBanStatus())
                .like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName())
                .like(StringUtils.isNotBlank(user.getNickName()), "u.nick_name", user.getNickName())
                .eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus())
                .eq(StringUtils.isNotBlank(user.getBanType()), "u.ban_type", user.getBanType())
                .like(StringUtils.isNotBlank(user.getPhoneNumber()), "u.phone_number", user.getPhoneNumber())
                .between(params.get("beginTime") != null && params.get("endTime") != null, "u.create_time", params.get("beginTime"), params.get("endTime"))
                .between(StringUtils.isNotBlank(user.getStartTime()) && StringUtils.isNotBlank(user.getEndTime()), "u.create_time", user.getStartTime(), user.getEndTime())
                .and(ObjectUtil.isNotNull(user.getDeptId()), w -> {
                    List<SysDept> deptList = deptMapper.selectList(new LambdaQueryWrapper<SysDept>().select(SysDept::getDeptId).apply(DataBaseHelper.findInSet(user.getDeptId(), "ancestors")));
                    List<Long> ids = StreamUtils.toList(deptList, SysDept::getDeptId);
                    ids.add(user.getDeptId());
                    w.in("u.dept_id", ids);
                });
        return wrapper;
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public TableDataInfo<SysUser> selectAllocatedList(SysUser user, PageQuery pageQuery) {
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL).eq(ObjectUtil.isNotNull(user.getRoleId()), "r.role_id", user.getRoleId()).like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName()).eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus()).like(StringUtils.isNotBlank(user.getPhoneNumber()), "u.phone_number", user.getPhoneNumber());
        Page<SysUser> page = baseMapper.selectAllocatedList(PageQueryUtils.build(pageQuery), wrapper);
        return TableDataInfoUtils.build(page);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public TableDataInfo<SysUser> selectUnallocatedList(SysUser user, PageQuery pageQuery) {
        List<Long> userIds = userRoleMapper.selectUserIdsByRoleId(user.getRoleId());
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL).and(w -> w.ne("r.role_id", user.getRoleId()).or().isNull("r.role_id")).notIn(CollUtil.isNotEmpty(userIds), "u.user_id", userIds).like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName()).like(StringUtils.isNotBlank(user.getPhoneNumber()), "u.phone_number", user.getPhoneNumber());
        Page<SysUser> page = baseMapper.selectUnallocatedList(PageQueryUtils.build(pageQuery), wrapper);
        return TableDataInfoUtils.build(page);
    }

    @Override
    public TableDataInfo<SysUser> selectAllocatedListOfPlatform(SysUserQueryBo bo, PageQuery pageQuery) {
        Page<SysUser> page = baseMapper.selectAllocatedListOfPlatform(PageQueryUtils.build(pageQuery), bo);
        return TableDataInfoUtils.build(page);
    }

    @Override
    public TableDataInfo<SysUser> selectUnallocatedListOfPlatform(SysUserQueryBo bo, PageQuery pageQuery) {
        Page<SysUser> page = baseMapper.selectUnallocatedListOfPlatform(PageQueryUtils.build(pageQuery), bo);
        return TableDataInfoUtils.build(page);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return baseMapper.selectUserByUserName(userName);
    }

    /**
     * 通过手机号查询用户
     *
     * @param phonenumber 手机号
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByPhonenumber(String phonenumber) {
        return baseMapper.selectUserByPhonenumber(phonenumber);
    }

    @Override
    public SysUser selectUserByEmail(String email) {
        return baseMapper.selectUserByEmail(email);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        SysUser user = baseMapper.selectUserById(userId);
        if (Objects.nonNull(user)) {
            List<SysPlatformRoleVo> platformRoleVos = platformRoleMapper.selectListByUserId(userId);
            user.setPlatformRoles(platformRoleVos);
        }
        return user;
    }

    @Override
    public SysUser selectAllUserById(Long userId) {
        return baseMapper.selectAllUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        //List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        List<SysRoleVo> list = roleMapper.selectListOfEnableByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return StreamUtils.join(list, SysRoleVo::getRoleName);
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return StreamUtils.join(list, SysPost::getPostName);
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUser user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, user.getUserName()).ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkPhoneUnique(SysUser user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>().eq(SysUser::getPhoneNumber, user.getPhoneNumber()).ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkEmailUnique(SysUser user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>().eq(SysUser::getEmail, user.getEmail()).ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (ObjectUtil.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!LoginHelper.isAdmin()) {
            SysUser user = new SysUser();
            user.setUserId(userId);
            List<SysUser> users = this.selectUserList(user);
            if (CollUtil.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(SysUser user) {
        if (StrUtil.isEmpty(user.getAvatar())) {
            user.setAvatar(Constants.DEFAULT_AVATAR);
        }
        // 新增用户信息
        int rows = baseMapper.insert(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        insertUserPlatformRole(user.getUserId(), user.getRoleIds());
        //保存智能推荐信息
        this.saveAirecUser(user);
        return rows;
    }

    @Override
    public void saveUserAge(SysUser user) {
        //TODO 请重写
        long total = remoteAccountService.selectPassAccountCount();
        final int PAGE_SIZE = AliyunAirecConstant.BATCH_SIZE;
        // 总页数
        long totalPages = (total + PAGE_SIZE - 1) / PAGE_SIZE;
        for (int i = 1; i <= totalPages; i++) {
            //Page<SohuAccountVo> page = new Page<>(i, PAGE_SIZE);
            TableDataInfo<SohuAccountVo> pageResult = remoteAccountService.selectPassAccountList(new PageQuery(i, PAGE_SIZE));
            List<SohuAccountVo> accountList = pageResult.getData();
            //根据身份证号计算出年龄
            if (CollUtil.isNotEmpty(accountList)) {
                for (SohuAccountVo sohuAccountVo : accountList) {
                    if (sohuAccountVo.getIdentityType().equals("1")) {
                        String identityNo = sohuAccountVo.getIdentityNo();
                        if (StrUtil.isNotBlank(identityNo)) {
                            int age = getAgeByIdentityNo(identityNo);
                            //更新到智能推荐表和用户表
                            SysUser sysUser = baseMapper.selectUserById(sohuAccountVo.getUserId());
                            sysUser.setAge(age);
                            baseMapper.updateById(sysUser);
                            this.saveAirecUser(user);
                        }
                    }
                }
            }
        }
    }

    private int getAgeByIdentityNo(String identityNo) {
        String birthday = identityNo.substring(6, 14);
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            Date birthDate = sdf.parse(birthday);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return calendar.get(Calendar.YEAR) - Integer.parseInt(birthday.substring(0, 4));
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public Long registerUser(SysUser user) {
        user.setCreateBy(user.getUserName());
        user.setUpdateBy(user.getUserName());
        user.setUid(user.getUid());
        if (StrUtil.isBlankIfStr(user.getAvatar())) {
            user.setAvatar(Constants.DEFAULT_AVATAR);
        }
        int insert = baseMapper.insert(user);
        //用户初始站点
        SohuUserSiteRelationBo relation = new SohuUserSiteRelationBo();
        relation.setSiteId(user.getSiteId() != null ? user.getSiteId() : 11L);
        relation.setUserId(user.getUserId());
        remoteMiddleUserSiteRelationService.insertByBo(relation);
        //保存智能推荐信息
        this.saveAirecUser(user);
        return insert > 0 ? user.getUserId() : 0L;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUser(SysUser user) {
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().eq(SysUserPost::getUserId, userId));
        // 新增用户与岗位管理
        insertUserPost(user);
        if (user.getPlatformRoleIds() != null && user.getPlatformRoleIds().length > 0) {
            platformUserMapper.delete(new LambdaQueryWrapper<SysPlatformUser>().eq(SysPlatformUser::getUserId, userId));
            insertUserPlatformRole(userId, user.getPlatformRoleIds());
        }
        // 新增用户标签关联
        insertUserLabelRelation(user);
        CacheMgr.evict(LoginUser.REGION, String.valueOf(userId));
        return baseMapper.updateById(user);
    }

    /**
     * 批量新增用户标签关联
     *
     * @param user
     */
    public void insertUserLabelRelation(SysUser user) {
        // 删除用户与标签关联
        List<Long> commonLabelIds = user.getCommonLabelIds();
        List<Long> industryLabelIds = user.getIndustryLabelIds();
        List<Long> contentTagIds = user.getContentTagIds();
        if (CollUtil.isEmpty(commonLabelIds) && CollUtil.isEmpty(industryLabelIds) && CollUtil.isEmpty(contentTagIds)) {
            // 删除用户与指定类型标签的关联
            remoteMiddleCommonLabelService.deleteUserLabelByUserId(user.getUserId(), null);
        }
        if (CollUtil.isEmpty(commonLabelIds)) {
            // 删除用户与指定类型标签的关联
            remoteMiddleCommonLabelService.deleteUserLabelByUserId(user.getUserId(), LabelEnum.COMMON.getCode());
        }
        if (CollUtil.isEmpty(industryLabelIds)) {
            remoteMiddleCommonLabelService.deleteUserLabelByUserId(user.getUserId(), LabelEnum.INDUSTRY.getCode());
        }
        if (CollUtil.isEmpty(commonLabelIds)) {
            remoteMiddleCommonLabelService.deleteUserLabelByUserId(user.getUserId(), LabelEnum.CONTENT.getCode());
        }
        List<SohuUserLabelRelationBo> userLabelList = new ArrayList<>();
        // 处理通用标签
        processLabels(user.getUserId(), commonLabelIds, LabelEnum.COMMON, userLabelList);
        // 处理行业标签
        processLabels(user.getUserId(), industryLabelIds, LabelEnum.INDUSTRY, userLabelList);
        // 处理内容标签
        processLabels(user.getUserId(), contentTagIds, LabelEnum.CONTENT, userLabelList);
        // 批量插入新的标签关联
        if (CollUtil.isNotEmpty(userLabelList)) {
            remoteMiddleCommonLabelService.insertBatch(userLabelList);
        }
    }

    /**
     * 处理标签并添加到用户标签列表中
     *
     * @param userId        用户ID
     * @param labelIds      标签ID集合
     * @param labelType     标签类型
     * @param userLabelList 用户标签关联列表
     */
    private void processLabels(Long userId, List<Long> labelIds, LabelEnum labelType, List<SohuUserLabelRelationBo> userLabelList) {
        if (CollUtil.isNotEmpty(labelIds)) {
            // 删除用户与指定类型标签的关联
            remoteMiddleCommonLabelService.deleteUserLabelByUserId(userId, labelType.getCode());

            // 创建新的标签关联并添加到列表中
            labelIds.stream()
                    .map(labelId -> createUserLabelRelationBo(userId, labelId, labelType))
                    .forEach(userLabelList::add);
        }
    }

    /**
     * 创建用户标签关联对象
     *
     * @param userId    用户ID
     * @param labelId   标签ID
     * @param labelType 标签类型
     * @return SohuUserLabelRelationBo 对象
     */
    private SohuUserLabelRelationBo createUserLabelRelationBo(Long userId, Long labelId, LabelEnum labelType) {
        SohuUserLabelRelationBo bo = new SohuUserLabelRelationBo();
        bo.setUserId(userId);
        bo.setLabelId(labelId);
        bo.setLabelType(labelType.getCode());
        return bo;
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(Long userId, String roleIds) {
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        insertUserRole(userId, Stream.of(roleIds.split(",")).map(Long::parseLong).toArray(Long[]::new));
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        CacheMgr.evict(LoginUser.REGION, String.valueOf(user.getUserId()));
        return baseMapper.updateById(user);
    }

    @Override
    public void updateUserStatus(Long userId, String status) {
        SysUser user = this.baseMapper.selectById(userId);
        if (Objects.nonNull(user)) {
            if (Objects.equals(user.getStatus(), status)) {
                return;
            }
            SysUser entity = new SysUser();
            entity.setUserId(userId);
            entity.setStatus(status);
            baseMapper.updateById(entity);
            CacheMgr.evict(LoginUser.REGION, String.valueOf(userId));
        }
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user) {
        CacheMgr.evict(LoginUser.REGION, String.valueOf(user.getUserId()));
        return baseMapper.updateById(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return baseMapper.update(new SysUser(), new LambdaUpdateWrapper<SysUser>().set(SysUser::getAvatar, avatar).eq(SysUser::getUserName, userName)) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        CacheMgr.evict(LoginUser.REGION, String.valueOf(user.getUserId()));
        return baseMapper.updateById(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return baseMapper.update(new SysUser(), new LambdaUpdateWrapper<SysUser>().set(SysUser::getPassword, password).eq(SysUser::getUserName, userName));
    }

    /**
     * 手机号重置用户支付密码
     *
     * @param phoneNumber 手机号
     * @param payPassword 支付密码
     * @return
     */
    @Override
    public int resetUserPayPasswordByPhone(String phoneNumber, String payPassword) {
        return baseMapper.update(new SysUser(), new LambdaUpdateWrapper<SysUser>().set(SysUser::getPayPassword, payPassword).eq(SysUser::getPhoneNumber, phoneNumber));
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (ArrayUtil.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<>(posts.length);
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.insertBatch(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (ArrayUtil.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<>(roleIds.length);
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleMapper.insertBatch(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    private void insertUserPlatformRole(Long userId, Long[] roleIds) {
        if (ArrayUtil.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysPlatformUser> list = new ArrayList<>(roleIds.length);
            for (Long roleId : roleIds) {
                SysPlatformUser ur = new SysPlatformUser();
                ur.setUserId(userId);
                ur.setPlatformRoleId(roleId);
                list.add(ur);
            }
            platformUserMapper.insertBatch(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        // 删除用户与岗位表
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().eq(SysUserPost::getUserId, userId));
        return baseMapper.deleteById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId));
            checkUserDataScope(userId);
            remoteMiddleDeleteService.deleteByUserId(userId);
        }
        List<Long> ids = Arrays.asList(userIds);
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getUserId, ids));
        // 删除用户与岗位表
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().in(SysUserPost::getUserId, ids));
        platformUserMapper.delete(new LambdaQueryWrapper<SysPlatformUser>().in(SysPlatformUser::getUserId, ids));
        return baseMapper.deleteBatchIds(ids);
    }

    @Cacheable(cacheNames = CacheNames.SYS_USER_NAME, key = "#userId")
    @Override
    public String selectUserNameById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>().select(SysUser::getUserName).eq(SysUser::getUserId, userId));
        return ObjectUtil.isNull(sysUser) ? null : sysUser.getUserName();
    }

    @Override
    public List<SysUser> selectUserList(Collection<Long> ids) {
        return this.baseMapper.selectBatchIds(ids);
    }

    @Override
    public Page<UserVo> page(Collection<Long> notInUserIds, String keyword, Page<UserVo> page) {
        LambdaQueryWrapper<SysUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SysUser::getDelFlag, 0);
        if (CollUtil.isNotEmpty(notInUserIds)) {
            notInUserIds = notInUserIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(notInUserIds)) {
                lqw.notIn(SysUser::getUserId, notInUserIds);
            }
        }
        if (StrUtil.isNotBlank(keyword)) {
            lqw.and(i -> i.like(SysUser::getNickName, keyword).or().like(SysUser::getUserName, keyword)).or().like(SysUser::getPhoneNumber, keyword);
        }
        IPage<SysUser> result = this.baseMapper.selectVoPage(new Page<>(page.getCurrent(), page.getSize()), lqw);
        List<UserVo> list = new ArrayList<>();
        page.setRecords(list);
        if (result != null && CollUtil.isNotEmpty(result.getRecords())) {
            result.getRecords().forEach(sysUser -> {
                UserVo userVo = new UserVo();
                userVo.setNickName(sysUser.getNickName());
                userVo.setAvatar(sysUser.getAvatar());
                userVo.setId(sysUser.getUserId());
                // todo 待优化查询
                Long count = remoteMiddleUserFollowService.fansCount(userVo.getId());
                userVo.setFocusUserFans(count != null ? count : 0L);
                list.add(userVo);
            });
            page.setRecords(list);
            page.setTotal(result.getTotal());
        }
        return page;
    }

    @Override
    public List<Long> getUserBySiteId(List<Long> cityIds) {
        if (CollUtil.isEmpty(cityIds)) {
            return null;
        }
        SohuUserSiteRelationBo relationBo = new SohuUserSiteRelationBo();
        relationBo.setSiteIds(cityIds);
        List<SohuUserSiteRelationVo> userSiteRelationList = remoteMiddleUserSiteRelationService.queryList(relationBo);
        if (CollUtil.isEmpty(userSiteRelationList)) {
            return null;
        }
        return userSiteRelationList.stream()
                .map(SohuUserSiteRelationVo::getUserId)
                .collect(Collectors.toList());
    }

    /**
     * 保存智能推荐信息
     *
     * @param user
     */
    private void saveAirecUser(SysUser user) {
        SohuAirecUserBo model = buildAirecUserModel(user);
        model.setTags(remoteMiddleAirecTagRelationService.saveTagStr(user.getUserId(), AiRecTag.BizTypeEnum.USER.getCode(), model.getTags()));
        remoteMiddleAirecUserService.saveAirecUser(model);
    }

    /**
     * 创建智能推荐用户数据
     *
     * @param user
     * @return
     */
    private SohuAirecUserBo buildAirecUserModel(SysUser user) {
        SohuAirecUserBo model = new SohuAirecUserBo();
        model.setUserId(user.getUserId().toString());
        model.setUserIdType(AirecUserIdTypeEnum.PHONE.getCode());
        model.setThirdUserName(user.getUserName());
        model.setThirdUserType(user.getUserType());
        if (StringUtils.isNotEmpty(user.getPhoneNumber())) {
            model.setPhoneMd5(MD5Utils.stringToMD5(user.getPhoneNumber()));
        }
        model.setGender(transientGender(user.getSex()));
        return model;
    }

    /**
     * 转换性别
     *
     * @param sex
     * @return
     */
    public String transientGender(Integer sex) {
        String gender = AirecGenderEnum.AIREC_UNKNOWN.getCode();
        if (ObjectUtil.isNotNull(sex)) {
            if (Objects.equals(SexEnum.MALE.getSex(), sex)) {
                gender = AirecGenderEnum.AIREC_MALE.getCode();
            } else if (Objects.equals(SexEnum.FEMALE.getSex(), sex)) {
                gender = AirecGenderEnum.AIREC_FEMALE.getCode();
            }
        }
        return gender;
    }

    @Override
    public Boolean initAirecUsers() {
        LambdaQueryWrapper<SysUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SysUser::getStatus, 0);
        lqw.eq(SysUser::getDelFlag, 0);
        //设置分页参数
        // 查询总数
        long total = baseMapper.selectCount(lqw);
        final int PAGE_SIZE = AliyunAirecConstant.BATCH_SIZE;
        // 总页数
        long totalPages = (total + PAGE_SIZE - 1) / PAGE_SIZE;
        for (int i = 1; i <= totalPages; i++) {
            // 分页查询
            Page<SysUser> page = new Page<>(i, PAGE_SIZE);
            IPage<SysUser> pageResult = baseMapper.selectPage(page, lqw);
            List<SysUser> list = pageResult.getRecords();
            // 处理查询结果
            if (CollUtil.isNotEmpty(list)) {
                //物料信息记录
                List<SohuAirecUserBo> modelList = new ArrayList<>();
                for (SysUser entity : list) {
                    SohuAirecUserBo model = buildAirecUserModel(entity);
                    model.setTags(remoteMiddleAirecTagRelationService.saveTagStr(entity.getUserId(), AiRecTag.BizTypeEnum.USER.getCode(), model.getTags()));
                    modelList.add(model);
                }
                //保存物料信息
                remoteMiddleAirecUserService.initAirecUsers(modelList);
            }
        }
        return true;
    }

    /**
     * 修改支付密码发送消息通知
     *
     * @param userId
     */
    @Override
    public void sendMsgOfUpdatePayPwd(Long userId) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.updatePayPwd);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.updatePayPwd.name());
        content.setDetailId(userId);
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setUserId(userId);
        detail.setDesc(SystemNoticeEnum.updatePayPwdDesc);
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.updatePayPwd, contentJson, SystemNoticeEnum.Type.account);
    }

    /**
     * 修改登录密码发送消息通知
     *
     * @param userId
     */
    @Override
    public void sendMsgOfUpdatePwd(Long userId) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.updatePwd);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.updatePwd.name());
        content.setDetailId(userId);
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setUserId(userId);
        detail.setDesc(SystemNoticeEnum.updatePwdDesc);
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.updatePwd, contentJson, SystemNoticeEnum.Type.account);
    }

    /**
     * 修改手机号发送消息通知
     *
     * @param userId
     * @param phone
     */
    @Override
    public void sendMsgOfUpdatePhone(Long userId, String phone) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.updatePhone);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.updatePhone.name());
        content.setDetailId(userId);
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setUserId(userId);
        phone = StrUtil.desensitized(phone, DesensitizedUtil.DesensitizedType.MOBILE_PHONE);
        detail.setDesc(String.format(SystemNoticeEnum.updatePhoneDesc, phone));
        detail.setKeyWord(new String[]{phone});
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.updatePhone, contentJson, SystemNoticeEnum.Type.account);
    }

    /**
     * 修改邮箱发送消息通知
     *
     * @param userId
     * @param email
     */
    @Override
    public void sendMsgOfUpdateEmail(Long userId, String email) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.updateEmail);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.updateEmail.name());
        content.setDetailId(userId);
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setUserId(userId);
        email = DesensitizedUtil.desensitized(email, DesensitizedUtil.DesensitizedType.EMAIL);
        detail.setDesc(String.format(SystemNoticeEnum.updateEmailDesc, email));
        detail.setKeyWord(new String[]{email});
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.updateEmail, contentJson, SystemNoticeEnum.Type.account);
    }

    @Override
    public SysUserInfoVo getInfoByUserId(Long userId) {
        if (userId == null) {
            return new SysUserInfoVo();
        }
        SysUser sysUser = this.selectUserById(userId);
        if (Objects.isNull(sysUser)) {
            return new SysUserInfoVo();
        }
        SysUserInfoVo sysUserInfoVo = BeanCopyUtils.copy(sysUser, SysUserInfoVo.class);
        // 获取用户认证信息
        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserId(userId);
        if (Objects.nonNull(sohuAccountVo)) {
            sysUserInfoVo.setSohuAccountVo(sohuAccountVo);
        }
        // 获取用户商户信息(获取全部or通过的)
        List<SohuMerchantModel> sohuMerchantModelList = remoteMerchantService.selectByUserId(userId);
        if (CollUtil.isNotEmpty(sohuMerchantModelList)) {
            // 聚合店铺名称并用逗号拼接
            List<String> merchantNames = sohuMerchantModelList.stream()
                    .map(SohuMerchantModel::getName)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(merchantNames)) {
                merchantNames = merchantNames.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                sysUserInfoVo.setMerchantNames(StringUtils.join(merchantNames, ","));
            }

            // 聚合商户分类名称
            List<String> merchantCategoryNames = sohuMerchantModelList.stream()
                    .map(SohuMerchantModel::getCategoryName)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(merchantCategoryNames)) {
                merchantCategoryNames = merchantCategoryNames.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                sysUserInfoVo.setMerchantCategorys(StringUtils.join(merchantCategoryNames, ","));
            }
            // 聚合店铺类型
            List<String> merchantTypes = sohuMerchantModelList.stream()
                    .map(SohuMerchantModel::getMerchantType)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(merchantTypes)) {
                merchantTypes = merchantTypes.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                sysUserInfoVo.setMerchantTypes(StringUtils.join(merchantTypes, ","));
            }
            // 聚合店铺关键字
            List<String> merchantKeywords = sohuMerchantModelList.stream()
                    .map(SohuMerchantModel::getKeywords)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(merchantKeywords)) {
                merchantKeywords = merchantKeywords.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                sysUserInfoVo.setMerchantKeywords(StringUtils.join(merchantKeywords, ","));
            }
            // 聚合商户id
            List<Long> merchantIds = sohuMerchantModelList.stream()
                    .map(SohuMerchantModel::getId)
                    .collect(Collectors.toList());
            ArrayList<SohuMerchantSettledVo> sohuMerchantVos = new ArrayList<>();
            List<CategoryInfo> categoryInfos = new ArrayList<>();
            merchantIds.forEach(
                    merchantId -> {
                        // 获取用户商户信息
                        SohuMerchantSettledVo sohuMerchantSettledVo = remoteMerchantService.detail(merchantId);
                        if (Objects.nonNull(sohuMerchantSettledVo)) {
                            sohuMerchantVos.add(sohuMerchantSettledVo);
                            categoryInfos.addAll(sohuMerchantSettledVo.getCategoryInfos());
                        }
                    }
            );
            sysUserInfoVo.setSohuMerchantSettledVos(sohuMerchantVos);
            sysUserInfoVo.setProductCategoryCount(categoryInfos.stream().count());
        }

        return sysUserInfoVo;
    }

    @Override
    public Boolean updatePhoneNumber(String newPhoneNumber) {
        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserId(LoginHelper.getUserId());
        if (Objects.nonNull(sohuAccountVo)) {
            throw new RuntimeException("当前手机号已经实名认证，不能修改绑定手机号");
        }
        LambdaQueryWrapper<SysUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SysUser::getPhoneNumber, newPhoneNumber);
        if (this.baseMapper.exists(lqw)) {
            throw new RuntimeException("当前手机号已注册，可将当前手机号注销后再进行绑定");
        }
        SysUser updateEntity = new SysUser();
        updateEntity.setUserId(LoginHelper.getUserId());
        updateEntity.setPhoneNumber(newPhoneNumber);
        if (this.baseMapper.updateById(updateEntity) > 0) {
            CacheMgr.evict(LoginUser.REGION, String.valueOf(LoginHelper.getUserId()));
            CompletableFuture.runAsync(() -> this.sendMsgOfUpdatePhone(LoginHelper.getUserId(), newPhoneNumber), asyncConfig.getAsyncExecutor());
        }
        return true;
    }

    @Override
    public Long getUserNumByCreateTime(Date startTime, Date endTime) {
        LambdaQueryWrapper<SysUser> lqw = new LambdaQueryWrapper<>();
        lqw.between(BaseEntity::getCreateTime, startTime, endTime);
        return this.baseMapper.getUserNum(lqw);
    }

    @Override
    public Long getUserTotalNum() {
        return this.baseMapper.selectCount();
    }

    @Override
    public Boolean registerImUser(String phone, String serverCode) {
        // 通过手机号查询用户是否存在
        SysUser sysUser = this.selectUserByPhonenumber(phone);
        if (Objects.nonNull(sysUser)) {
            return true;
        }
        //查询服务器id
        SohuImTenantVo sohuImTenantVo = imTenantService.queryByServerCode(serverCode);
        //新增用户
        sysUser = new SysUser();
        String name = RandomUtils.genUserName();
        sysUser.setUserName(name);
        sysUser.setNickName(name);
        sysUser.setPhoneNumber(phone);
        sysUser.setUserType(UserType.IM_USER.getUserType());
        sysUser.setTenantId(sohuImTenantVo.getId());
        this.baseMapper.insert(sysUser);
        // 添加用户角色
        this.giveRole(null, sysUser.getUserId());
        return true;
    }

    @Override
    public Map<Long, Integer> getTenantRegisterNum(List<Long> tenantIds) {
        List<TenantRegisterVo> list = this.baseMapper.getTenantRegisterNum(tenantIds);
        return list.stream().collect(Collectors.toMap(TenantRegisterVo::getTenantId, TenantRegisterVo::getRegisterNum));
    }

    @Override
    public List<Long> queryUserIdByBo(SysUserQueryBo bo) {
        LambdaQueryWrapper<SysUser> lqw = new LambdaQueryWrapper();
        lqw.like(StrUtil.isNotBlank(bo.getUserName()), SysUser::getUserName, bo.getUserName());
        lqw.like(StrUtil.isNotBlank(bo.getNickName()), SysUser::getNickName, bo.getNickName());
        lqw.like(!CalUtils.isNullOrZero(bo.getUserId()), SysUser::getUserId, bo.getUserId());
        List<SysUser> list = this.baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(SysUser::getUserId).collect(Collectors.toList());
    }

    @Override
    public SysUser queryByUserId(Long userId) {
        return this.baseMapper.selectById(userId);
    }

    @Override
    public void updateInviteUserId(Long userId, Long inviteUserId) {
        SysUser sysUser = this.baseMapper.selectById(userId);
        sysUser.setInviteUserId(inviteUserId);
        baseMapper.updateById(sysUser);
    }

    @Override
    public BigDecimal getUserIncome(Long userId, Integer siteType, Long siteId, String userRole, Integer independentStatus, Date startTime, Date endTime) {
        List<String> personRoles = Arrays.asList(SohuIndependentObject.rece.getKey(), SohuIndependentObject.invite.getKey(), SohuIndependentObject.distribution.getKey(), SohuIndependentObject.distributionInvite.getKey());
        List<String> siteRoles = Arrays.asList(SohuIndependentObject.country.getKey(), SohuIndependentObject.city.getKey(), SohuIndependentObject.entrance.getKey(), SohuIndependentObject.industrysite.getKey(), SohuIndependentObject.invitecity.getKey());
        List<String> agencyRoles = Arrays.asList(SohuIndependentObject.agency.getKey());
        List<String> selectRoles = personRoles;
        if (UserRoleEnum.CITYSTATION.getType().equals(userRole)) {
            selectRoles = siteRoles;
        } else if (UserRoleEnum.AGENCY.getType().equals(userRole)) {
            selectRoles = agencyRoles;
        }
        BigDecimal distributeAmount = remoteIndependentOrderService.selectIndependentingByUserId(userId, siteType, siteId, selectRoles, null, independentStatus, startTime, endTime);
        return distributeAmount;
    }

    @Override
    public BigDecimal getUserWithdrawal(Long userId, Integer siteType, Long siteId, String userRole, Date startTime, Date endTime) {
        return remoteMiddleBillRecordService.sumWithdrawalByUserType(userId, siteType, siteId, userRole, startTime, endTime);
    }

    @Override
    public BigDecimal getUserInviteIncome(Long userId, Integer siteType, Long siteId, String independentObject, Date startTime, Date endTime) {
        BigDecimal inviteAmount = remoteIndependentOrderService.selectIndependentingByUserId(userId, siteType, siteId, null, independentObject, IndependentStatusEnum.DISTRIBUTED.getCode(), startTime, endTime);
        return inviteAmount;
    }

    @Override
    public SohuUserOrderInfoVo getUserOrderInfo(Long userId, Integer siteType, Long siteId, List<String> independentObject, String tradeType, Date startTime, Date endTime) {
        SohuUserOrderInfoVo sohuUserOrderInfoVo = new SohuUserOrderInfoVo();
        SohuUserOrderModel sohuUserOrderModel = remoteIndependentOrderService.getUserOrderInfo(userId, siteType, siteId, independentObject, tradeType, startTime, endTime);
        if (Objects.nonNull(sohuUserOrderModel)) {
            sohuUserOrderInfoVo.setOrderAmount(sohuUserOrderModel.getOrderAmount());
            sohuUserOrderInfoVo.setOrderCount(sohuUserOrderModel.getOrderCount());
        }
        return sohuUserOrderInfoVo;
    }

    /**
     * 赋予角色
     *
     * @param roleKey
     * @param userId
     */
    private void giveRole(PlatformRoleCodeEnum roleKey, Long userId) {
        // 赋予用户角色列表
        List<String> giveRoleList = new ArrayList<>();
        // 赋予用户平台角色列表
        List<String> givePlatformRoleList = new ArrayList<>();
        // 默认赋予 创作者功能 普通功能
        giveRoleList.add(RoleCodeEnum.Article.getCode());
        giveRoleList.add(RoleCodeEnum.COMMON.getCode());
        // 默认赋予 创作者服务平台角色
        givePlatformRoleList.add(PlatformRoleCodeEnum.Article.getCode());

        if (Objects.nonNull(roleKey)) {
            PlatformRoleCodeEnum currentRole = platformRoleService.getCurrentExclusiveRole(userId);
            if (currentRole != null && !StrUtil.equalsAnyIgnoreCase(roleKey.getCode(), currentRole.getCode())) {
                throw new RuntimeException("您已认证其他角色");
            }
        }
        log.info("给用户授予功能角色:{}", JSONObject.toJSON(giveRoleList));
        savePlatformRoles(userId, givePlatformRoleList);
        saveUserRoles(userId, giveRoleList);
    }

    /**
     * 保存平台角色信息
     *
     * @param userId
     * @param giveRoleList
     */
    private void savePlatformRoles(Long userId, List<String> giveRoleList) {
        List<SysPlatformRoleVo> roleVos = platformRoleService.queryOfEnableByRoleKeys(giveRoleList);
        if (CollUtil.isNotEmpty(roleVos)) {
            // 保存用户角色关联信息
            platformRoleService.insertList(roleVos, userId);
        }
    }

    /**
     * 保存用户角色信息
     *
     * @param userId       用户ID
     * @param giveRoleList 赋予角色列表
     */
    private void saveUserRoles(Long userId, List<String> giveRoleList) {
        // 查询用户拥有的角色列表
        List<SysRole> roleDOS = sysRoleService.queryByRoleKeys(giveRoleList);
        if (CollUtil.isNotEmpty(roleDOS)) {
            // 保存用户角色关联信息
            sysRoleService.insertList(roleDOS, userId);
        }
    }

    @Override
    public void pushWarningMsg(SohuWarnBo bo) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.warnTitle);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.Type.warn.name());
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setUserId(bo.getUserId());
        detail.setDesc(String.format(SystemNoticeEnum.warnDesc, bo.getWarnMsg()));
        detail.setStatus(SystemNoticeEnum.Type.warn.name());
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(bo.getUserId(), SystemNoticeEnum.warnTitle, contentJson, SystemNoticeEnum.Type.warn);
    }

    @Override
//    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean disable(SohuDisableUserBo bo) {
        // 参数校验
        if (bo == null || bo.getUserId() == null || bo.getOperationType() == null) {
            log.error("disable方法参数不能为空");
            return false;
        }

        // 获取当前操作员ID
        Long operatorId = LoginHelper.getUserId();
        if (operatorId == null) {
            log.error("获取当前操作员ID失败");
            return false;
        }

        try {
            // 根据操作类型执行不同的逻辑
            if (StrUtil.equalsAnyIgnoreCase(bo.getOperationType(), RESET_AVATAR.getType())) {
                return resetUserAvatar(bo, operatorId);
            } else if (StrUtil.equalsAnyIgnoreCase(bo.getOperationType(), RESET_NICKNAME.getType())) {
                return resetUserNickname(bo, operatorId);
            } else if (StrUtil.equalsAnyIgnoreCase(bo.getOperationType(), RESET_REMARK.getType())) {
                return resetUserRemark(bo, operatorId);
            } else if (StrUtil.equalsAnyIgnoreCase(bo.getOperationType(), BAN_ACCOUNT.getType())) {
                return banUser(bo, operatorId);
            } else if (StrUtil.equalsAnyIgnoreCase(bo.getOperationType(), UNBAN_ACCOUNT.getType())) {
                return unbanUser(bo, operatorId);
            }
            log.warn("未知的操作类型: {}", bo.getOperationType());
            return false;
        } catch (Exception e) {
            log.error("执行用户操作失败, userId: {}, operationType: {}", bo.getUserId(), bo.getOperationType(), e);
            throw new ServiceException("操作失败: " + e.getMessage());
        }
    }

    /**
     * 重置用户头像
     */
    private Boolean resetUserAvatar(SohuDisableUserBo bo, Long operatorId) {
        if (StrUtil.isBlank(bo.getAvatar())) {
            log.error("重置头像时头像地址不能为空, userId: {}", bo.getUserId());
            return false;
        }

        SysUser user = new SysUser();
        user.setUserId(bo.getUserId());
        user.setAvatar(bo.getAvatar());
        user.setUpdateBy(operatorId.toString());
        user.setUpdateTime(new Date());

        int result = baseMapper.updateById(user);
        log.info("重置用户头像, userId: {}, operatorId: {}, result: {}", bo.getUserId(), operatorId, result > 0);
        return result > 0;
    }

    /**
     * 重置用户昵称
     */
    private Boolean resetUserNickname(SohuDisableUserBo bo, Long operatorId) {
        if (StrUtil.isBlank(bo.getNickName())) {
            log.error("重置昵称时昵称不能为空, userId: {}", bo.getUserId());
            return false;
        }

        SysUser user = new SysUser();
        user.setUserId(bo.getUserId());
        user.setNickName(bo.getNickName());
        user.setUpdateBy(operatorId.toString());
        user.setUpdateTime(new Date());

        int result = baseMapper.updateById(user);
        log.info("重置用户昵称, userId: {}, operatorId: {}, result: {}", bo.getUserId(), operatorId, result > 0);
        return result > 0;
    }

    /**
     * 重置用户签名
     */
    private Boolean resetUserRemark(SohuDisableUserBo bo, Long operatorId) {
        SysUser user = new SysUser();
        user.setUserId(bo.getUserId());
        user.setRemark(bo.getRemark());
        user.setUpdateBy(operatorId.toString());
        user.setUpdateTime(new Date());

        int result = baseMapper.updateById(user);
        log.info("重置用户签名, userId: {}, operatorId: {}, result: {}", bo.getUserId(), operatorId, result > 0);
        return result > 0;
    }

    /**
     * 封禁用户 - 支持多种封禁类型
     */
    private Boolean banUser(SohuDisableUserBo bo, Long operatorId) {
        // 参数校验
        if (StrUtil.isBlank(bo.getDurationDescription())) {
            log.error("封禁用户时封禁时长不能为空, userId: {}", bo.getUserId());
            return false;
        }

        // 获取封禁类型列表
        List<String> banTypes = bo.getBanTypes();
        if (banTypes.isEmpty()) {
            log.error("封禁类型不能为空, userId: {}", bo.getUserId());
            return false;
        }

        // 检查各种类型是否已经被封禁
        for (String banType : banTypes) {
            if (checkExistingBan(bo, banType)) {
                return false;
            }
        }

        // 为每种封禁类型创建封禁记录
        List<Long> banRecordIds = new ArrayList<>();
        for (String banType : banTypes) {
            // 创建每种类型缓存
            Long banRecordId = createBanRecord(bo, banType, operatorId);
            if (banRecordId != null) {
                banRecordIds.add(banRecordId);
            }
        }

        if (banRecordIds.isEmpty()) {
            log.error("创建封禁记录失败, userId: {}", bo.getUserId());
            return false;
        }

        log.info("多类型封禁用户成功, userId: {}, operatorId: {}, banTypes: {}, recordIds: {}",
                bo.getUserId(), operatorId, banTypes, banRecordIds);
        return true;
    }

    /**
     * 检查指定类型是否已经被封禁
     */
    private boolean checkExistingBan(SohuDisableUserBo bo, String banType) {
        switch (banType.toLowerCase()) {
            case "account":
                SohuBanRecordsVo existingAccountBan = remoteBanRecordService.findActiveBanByUserId(
                        bo.getUserId(), BanEnums.BanStatusEnum.ACTIVE.getType());
                if (existingAccountBan != null) {
                    log.warn("用户账号已经处于封禁状态, userId: {}, banId: {}", bo.getUserId(), existingAccountBan.getId());
                    throw new ServiceException("用户账号已经处于封禁状态，无法重复封禁");
                }
                break;
            case "ip":
                if (StrUtil.isBlank(bo.getIp())) {
                    log.error("IP封禁时IP地址不能为空, userId: {}", bo.getUserId());
                    throw new ServiceException("IP封禁时IP地址不能为空");
                }
                SohuBanRecordsVo existingIpBan = remoteBanRecordService.findActiveBanByIp(
                        bo.getIp(), BanEnums.BanStatusEnum.ACTIVE.getType());
                if (existingIpBan != null) {
                    log.warn("IP已经处于封禁状态, ip: {}, banId: {}", bo.getIp(), existingIpBan.getId());
                    throw new ServiceException("IP地址已经处于封禁状态，无法重复封禁");
                }
                break;
            case "device":
                if (StrUtil.isBlank(bo.getDevice())) {
                    log.error("设备封禁时设备号不能为空, userId: {}", bo.getUserId());
                    throw new ServiceException("设备封禁时设备号不能为空");
                }
                SohuBanRecordsVo existingDeviceBan = remoteBanRecordService.findActiveBanByDevice(
                        bo.getDevice(), BanEnums.BanStatusEnum.ACTIVE.getType());
                if (existingDeviceBan != null) {
                    log.warn("设备已经处于封禁状态, device: {}, banId: {}", bo.getDevice(), existingDeviceBan.getId());
                    throw new ServiceException("设备已经处于封禁状态，无法重复封禁");
                }
                break;
            default:
                log.error("不支持的封禁类型: {}", banType);
                throw new ServiceException("不支持的封禁类型: " + banType);
        }
        return false;
    }

    /**
     * 创建单个封禁记录
     */
    private Long createBanRecord(SohuDisableUserBo bo, String banType, Long operatorId) {

        try {
            // 计算封禁结束时间
            LocalDateTime nowForBan = LocalDateTime.now();
            LocalDateTime expectedEndTime = BanDurationCalculatorUtil.calculateExpectedEndTime(
                    nowForBan, bo.getDurationDescription());
            Date expectedEndDate = (expectedEndTime != null) ?
                    Date.from(expectedEndTime.atZone(ZoneId.systemDefault()).toInstant()) : null;
            Date currentDate = new Date();

            // 创建封禁记录
            SohuBanRecordsBo banRecord = new SohuBanRecordsBo();
            banRecord.setUserId(bo.getUserId());
            banRecord.setBanType(banType);
            banRecord.setDurationDescription(bo.getDurationDescription());
            banRecord.setBanDatetime(currentDate);
            banRecord.setExpectedEndDatetime(expectedEndDate);
            banRecord.setStatus(BanEnums.BanStatusEnum.ACTIVE.getType());
            banRecord.setBanReason(StrUtil.isNotBlank(bo.getReason()) ? bo.getReason() : "违规行为");
            banRecord.setBanOperatorId(operatorId);
            banRecord.setLastOperationDatetime(currentDate);
            // 与产品沟通,通过用户名搜索搜索快照名
            SysUser sysUser = this.selectUserById(bo.getUserId());
            banRecord.setNickName(sysUser.getNickName());
            banRecord.setPhoneNumber(sysUser.getPhoneNumber());
            banRecord.setBanPolicyId(CalUtils.isNullOrZero(bo.getBanPolicyId()) ? 1L : bo.getBanPolicyId());

            // 根据封禁类型设置相应的字段
            switch (banType.toLowerCase()) {
                case "ip":
                    banRecord.setIp(bo.getIp());
                    break;
                case "device":
                    banRecord.setDevice(bo.getDevice());
                    break;
                case "account":
                    break;
                default:
                    log.error("不支持的封禁类型: {}", banType);
                    return null;
            }

            Long banRecordId = remoteBanRecordService.insertRecord(banRecord);
            if (banRecordId == null) {
                log.error("插入封禁记录失败, userId: {}, banType: {}", bo.getUserId(), banType);
                return null;
            }

            // 如果有结束时间，添加到Redis定时任务
            if (expectedEndDate != null) {
                addTaskToRedisZSet(banRecordId, expectedEndDate);
            }

            // 根据封禁类型分别存储到Redis缓存
            switch (banType.toLowerCase()) {
                case "account":
                    setUserBanCache(bo.getUserId(), banRecordId,
                            banRecord.getBanReason(), expectedEndDate, banRecord.getBanPolicyId());
                    break;
                case "ip":
                    setIpBanCache(bo.getIp(), banRecordId,
                            banRecord.getBanReason(), expectedEndDate);
                    break;
                case "device":
                    setDeviceBanCache(bo.getDevice(), banRecordId,
                            banRecord.getBanReason(), expectedEndDate);
                    break;
            }

            // 冗余用户封禁状态
            updateUserBanStaus(bo.getUserId(), BanEnums.BanStatusEnum.ACTIVE.getType());
            log.info("创建封禁记录成功, userId: {}, banType: {}, banRecordId: {}, duration: {}",
                    bo.getUserId(), banType, banRecordId, bo.getDurationDescription());
            return banRecordId;

        } catch (Exception e) {
            log.error("创建封禁记录失败, userId: {}, banType: {}", bo.getUserId(), banType, e);
            return null;
        }
    }

    /**
     * 解封用户 - 支持多种封禁类型
     */
    private Boolean unbanUser(SohuDisableUserBo bo, Long operatorId) {
        try {
            // 获取要解封的类型列表
            List<String> banTypes = bo.getBanTypes();
            if (banTypes.isEmpty()) {
                // 如果没有指定类型，则解封所有类型
                banTypes = Arrays.asList("account", "ip", "device");
            }

            List<Long> unbanRecordIds = new ArrayList<>();
            boolean hasActiveBan = false;

            // 为每种类型执行解封操作
            for (String banType : banTypes) {
                SohuBanRecordsVo activeBan = findActiveBanByType(bo, banType);
                if (activeBan != null) {
                    hasActiveBan = true;
                    // 解封操作
                    Long unbanRecordId = unbanSingleType(activeBan, bo, operatorId);
                    if (unbanRecordId != null) {
                        unbanRecordIds.add(unbanRecordId);
                    }
                }
            }

            if (!hasActiveBan) {
                log.warn("用户当前未处于任何封禁状态, userId: {}", bo.getUserId());
                throw new ServiceException("用户当前未处于封禁状态，无法解封");
            }

            log.info("多类型解封用户成功, userId: {}, operatorId: {}, banTypes: {}, recordIds: {}",
                    bo.getUserId(), operatorId, banTypes, unbanRecordIds);
            return true;

        } catch (Exception e) {
            log.error("解封用户失败, userId: {}, operatorId: {}", bo.getUserId(), operatorId, e);
            throw new ServiceException("解封用户失败: " + e.getMessage());
        }
    }

    /**
     * 根据类型查找活跃的封禁记录
     */
    private SohuBanRecordsVo findActiveBanByType(SohuDisableUserBo bo, String banType) {
        switch (banType.toLowerCase()) {
            case "account":
                return remoteBanRecordService.findActiveBanByUserId(
                        bo.getUserId(), BanEnums.BanStatusEnum.ACTIVE.getType());
            case "ip":
                if (StrUtil.isNotBlank(bo.getIp())) {
                    return remoteBanRecordService.findActiveBanByIp(
                            bo.getIp(), BanEnums.BanStatusEnum.ACTIVE.getType());
                }
                break;
            case "device":
                if (StrUtil.isNotBlank(bo.getDevice())) {
                    return remoteBanRecordService.findActiveBanByDevice(
                            bo.getDevice(), BanEnums.BanStatusEnum.ACTIVE.getType());
                }
                break;
        }
        return null;
    }

    /**
     * 解封单个类型
     */
    private Long unbanSingleType(SohuBanRecordsVo activeBan, SohuDisableUserBo bo, Long operatorId) {
        try {
            // 更新封禁记录状态为已解封
            SohuBanRecordsBo banToLift = new SohuBanRecordsBo();
            banToLift.setId(activeBan.getId());
            banToLift.setUserId(bo.getUserId());
            banToLift.setStatus(BanEnums.BanStatusEnum.LIFTED.getType());
            banToLift.setUnbanDatetime(new Date());
            banToLift.setUnbanReason(StrUtil.isNotBlank(bo.getReason()) ? bo.getReason() : "管理员手动解封");
            banToLift.setUnbanOperatorId(operatorId);
            banToLift.setLastOperationDatetime(new Date());

            // 更新封禁记录
            remoteBanRecordService.updateBanRecordForUnban(banToLift);

            // 从Redis定时任务中移除
            removeTaskFromRedisZSet(activeBan.getId());

            // 根据封禁类型分别移除Redis缓存
            switch (activeBan.getBanType().toLowerCase()) {
                case "account":
                    removeUserBanCache(bo.getUserId());
                    break;
                case "ip":
                    if (StrUtil.isNotBlank(activeBan.getIp())) {
                        removeIpBanCache(activeBan.getIp());
                    }
                    break;
                case "device":
                    if (StrUtil.isNotBlank(activeBan.getDevice())) {
                        removeDeviceBanCache(activeBan.getDevice());
                    }
                    break;
            }

            // 冗余用户封禁状态
            updateUserBanStaus(bo.getUserId(), BanEnums.BanStatusEnum.LIFTED.getType());
            log.info("解封单个类型成功, userId: {}, banType: {}, banId: {}",
                    bo.getUserId(), activeBan.getBanType(), activeBan.getId());
            return activeBan.getId();

        } catch (Exception e) {
            log.error("解封单个类型失败, userId: {}, banType: {}, banId: {}",
                    bo.getUserId(), activeBan.getBanType(), activeBan.getId(), e);
            return null;
        }
    }

    /**
     * 添加封禁任务到Redis ZSet
     *
     * @param banRecordId 封禁记录ID
     * @param endTime     封禁结束时间
     */
    private void addTaskToRedisZSet(Long banRecordId, Date endTime) {
        try {
            // 使用结束时间的时间戳作为score，封禁记录ID作为member
            double score = endTime.getTime();
            RedisUtils.zSetAdd(BanConstants.BAN_TASKS_ZSET_KEY, score, banRecordId.toString(), endTime.getTime());

            // 同时缓存封禁记录信息，方便定时任务处理
            String cacheKey = BanConstants.BAN_RECORD_CACHE_KEY + banRecordId;
            Map<String, Object> banInfo = new HashMap<>();
            banInfo.put("banRecordId", banRecordId);
            banInfo.put("endTime", endTime.getTime());
            banInfo.put("addTime", System.currentTimeMillis());
            RedisUtils.setCacheObject(cacheKey, banInfo, Duration.ofMillis(endTime.getTime()));

            log.info("添加封禁任务到Redis成功, banRecordId: {}, endTime: {}", banRecordId, endTime);
        } catch (Exception e) {
            log.error("添加封禁任务到Redis失败, banRecordId: {}, endTime: {}", banRecordId, endTime, e);
        }
    }

    /**
     * 从Redis ZSet中移除封禁任务
     *
     * @param banRecordId 封禁记录ID
     */
    private void removeTaskFromRedisZSet(Long banRecordId) {
        try {
            // 从ZSet中移除
            zSetRemove(banRecordId.toString());

            // 删除缓存的封禁记录信息
            String cacheKey = BanConstants.BAN_RECORD_CACHE_KEY + banRecordId;
            RedisUtils.deleteObject(cacheKey);

            log.info("从Redis移除封禁任务成功, banRecordId: {}", banRecordId);
        } catch (Exception e) {
            log.error("从Redis移除封禁任务失败, banRecordId: {}", banRecordId, e);
        }
    }

    /**
     * 从ZSet中移除元素
     */
    private void zSetRemove(String member) {
        RedisUtils.getClient().getScoredSortedSet(BanConstants.BAN_TASKS_ZSET_KEY).remove(member);
    }

    /**
     * 根据分数范围获取ZSet元素
     */
    private Collection<Object> zSetRangeByScore(double max) {
        return RedisUtils.getClient().getScoredSortedSet(BanConstants.BAN_TASKS_ZSET_KEY)
                .valueRange(0, true, max, true);
    }

    /**
     * 冗余更新用户表中的封禁状态
     *
     * @param userId    用户ID
     * @param banStatus 封禁类型
     */
    private void updateUserBanStaus(Long userId, String banStatus) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setBanStatus(banStatus);
        baseMapper.updateById(sysUser);
    }

    /**
     * 处理到期的封禁任务（定时任务调用）
     * 每分钟五分钟执行一次
     */
    @Override
    public void processExpiredBanTasks() {
        try {
            long currentTime = System.currentTimeMillis();

            // 获取所有到期的封禁任务（score <= 当前时间）
            Collection<Object> expiredTasks = zSetRangeByScore(currentTime);

            if (expiredTasks.isEmpty()) {
                return;
            }

            log.info("开始处理到期的封禁任务, 数量: {}", expiredTasks.size());

            for (Object taskObj : expiredTasks) {
                try {
                    Long banRecordId = Long.valueOf(taskObj.toString());

                    // 查询封禁记录
                    SohuBanRecordsVo banRecord = remoteBanRecordService.findBanRecordById(banRecordId);
                    if (banRecord == null) {
                        log.warn("封禁记录不存在, banRecordId: {}", banRecordId);
                        removeTaskFromRedisZSet(banRecordId);
                        continue;
                    }

                    // 检查是否仍然是活跃状态
                    if (!BanEnums.BanStatusEnum.ACTIVE.getType().equals(banRecord.getStatus())) {
                        log.info("封禁记录已不是活跃状态, banRecordId: {}, status: {}",
                                banRecordId, banRecord.getStatus());
                        removeTaskFromRedisZSet(banRecordId);
                        continue;
                    }

                    // 更新封禁记录状态为到期解封
                    SohuBanRecordsBo updateRecord = new SohuBanRecordsBo();
                    updateRecord.setId(banRecordId);
                    updateRecord.setStatus(BanEnums.BanStatusEnum.LIFTED.getType());
                    updateRecord.setUnbanDatetime(new Date());
                    updateRecord.setUnbanReason("封禁时间到期自动解封");
                    updateRecord.setLastOperationDatetime(new Date());

                    // 同步封禁记录状态
                    remoteBanRecordService.updateBanRecordForUnban(updateRecord);
                    // 同步用户表封禁状态
                    this.updateUserBanStaus(banRecord.getUserId(), BanEnums.BanStatusEnum.LIFTED.getType());

                    // 根据封禁类型分别移除Redis缓存
                    switch (banRecord.getBanType().toLowerCase()) {
                        case "account":
                            removeUserBanCache(banRecord.getUserId());
                            break;
                        case "ip":
                            if (StrUtil.isNotBlank(banRecord.getIp())) {
                                removeIpBanCache(banRecord.getIp());
                            }
                            break;
                        case "device":
                            if (StrUtil.isNotBlank(banRecord.getDevice())) {
                                removeDeviceBanCache(banRecord.getDevice());
                            }
                            break;
                    }

                    // 从Redis中移除任务
                    removeTaskFromRedisZSet(banRecordId);

                    log.info("处理到期封禁任务成功, banRecordId: {}, userId: {}",
                            banRecordId, banRecord.getUserId());

                } catch (Exception e) {
                    log.error("处理单个到期封禁任务失败, task: {}", taskObj, e);
                }
            }

            log.info("处理到期封禁任务完成, 处理数量: {}", expiredTasks.size());

        } catch (Exception e) {
            log.error("处理到期封禁任务失败", e);
        }
    }

    /**
     * 检查用户是否被封禁
     *
     * @param userId 用户ID
     * @return 是否被封禁
     */
    @Override
    public boolean isUserBanned(Long userId) {
        try {
            // 先检查缓存
            String cacheKey = BanConstants.USER_BAN_KEY + userId;
            Object cachedStatus = RedisUtils.getCacheObject(cacheKey);
            if (cachedStatus != null) {
                Map<String, Object> statusMap = (Map<String, Object>) cachedStatus;
                return (Boolean) statusMap.getOrDefault("banned", false);
            }

            // 缓存未命中，查询数据库
            SohuBanRecordsVo activeBan = remoteBanRecordService.findActiveBanByUserId(
                    userId, BanEnums.BanStatusEnum.ACTIVE.getType());

            boolean isBanned = activeBan != null;

            return isBanned;

        } catch (Exception e) {
            log.error("检查用户封禁状态失败, userId: {}", userId, e);
            // 发生异常时，为了安全起见，查询数据库
            try {
                SohuBanRecordsVo activeBan = remoteBanRecordService.findActiveBanByUserId(
                        userId, BanEnums.BanStatusEnum.ACTIVE.getType());
                return activeBan != null;
            } catch (Exception ex) {
                log.error("查询数据库封禁状态也失败, userId: {}", userId, ex);
                return false;
            }
        }
    }

    /**
     * 设置用户封禁缓存
     *
     * @param userId          用户ID
     * @param banRecordId     封禁记录ID
     * @param banReason       封禁原因
     * @param expectedEndTime 预期封禁结束时间
     * @param banPolicyId
     */
    private void setUserBanCache(Long userId, Long banRecordId, String banReason, Date expectedEndTime, Long banPolicyId) {
        try {
            String cacheKey = BanConstants.USER_BAN_KEY + userId;

            Map<String, Object> banInfo = new HashMap<>();
            banInfo.put("banRecordId", userId);
            banInfo.put("banUserId", banRecordId);
            banInfo.put("banReason", banReason);
            banInfo.put("banTime", System.currentTimeMillis());
            banInfo.put("expectedEndTime", expectedEndTime != null ? expectedEndTime.getTime() : null);
            banInfo.put("isBanned", true);
            banInfo.put("banPolicyId", banPolicyId);

            // 设置缓存
            setCommonCache(expectedEndTime, cacheKey, banInfo);

            log.debug("设置用户封禁缓存成功, userId: {}, banRecordId: {}", userId, banRecordId);
        } catch (Exception e) {
            log.error("设置用户封禁缓存失败, userId: {}, banRecordId: {}", userId, banRecordId, e);
        }
    }

    /**
     * 设置IP封禁缓存
     *
     * @param ip              IP地址
     * @param banRecordId     封禁记录ID
     * @param banReason       封禁原因
     * @param expectedEndTime 预计解封时间
     */
    private void setIpBanCache(String ip, Long banRecordId, String banReason, Date expectedEndTime) {
        try {
            String cacheKey = BanConstants.IP_BAN_KEY + ip;

            Map<String, Object> banInfo = new HashMap<>();
            banInfo.put("banRecordId", banRecordId);
            banInfo.put("banIp", ip);
            banInfo.put("banReason", banReason);
            banInfo.put("banTime", System.currentTimeMillis());
            banInfo.put("expectedEndTime", expectedEndTime != null ? expectedEndTime.getTime() : null);
            banInfo.put("isBanned", true);

            // 设置缓存
            setCommonCache(expectedEndTime, cacheKey, banInfo);

            log.debug("设置IP封禁缓存成功, ip: {}, banRecordId: {}", ip, banRecordId);
        } catch (Exception e) {
            log.error("设置IP封禁缓存失败, ip: {}, banRecordId: {}", ip, banRecordId, e);
        }
    }

    /**
     * 设置设备封禁缓存
     *
     * @param device          设备号
     * @param banRecordId     封禁记录ID
     * @param banReason       封禁原因
     * @param expectedEndTime 预期结束时间
     */
    public void setDeviceBanCache(String device, Long banRecordId, String banReason, Date expectedEndTime) {
        try {
            String cacheKey = BanConstants.DEVICE_BAN_KEY + device;

            Map<String, Object> banInfo = new HashMap<>();
            banInfo.put("banRecordId", banRecordId);
            banInfo.put("banDevice", device);
            banInfo.put("banReason", banReason);
            banInfo.put("banTime", System.currentTimeMillis());
            banInfo.put("expectedEndTime", expectedEndTime != null ? expectedEndTime.getTime() : null);
            banInfo.put("isBanned", true);
            // 设置缓存
            setCommonCache(expectedEndTime, cacheKey, banInfo);

            log.debug("设置设备封禁缓存成功, device: {}, banRecordId: {}", device, banRecordId);
        } catch (Exception e) {
            log.error("设置设备封禁缓存失败, device: {}, banRecordId: {}", device, banRecordId, e);
        }
    }

    /**
     * 设置通用缓存
     *
     * @param expectedEndTime 预期结束时间
     * @param cacheKey        缓存键
     * @param banInfo         封禁信息
     */
    private static void setCommonCache(Date expectedEndTime, String cacheKey, Map<String, Object> banInfo) {
        if (expectedEndTime != null) {
            // 1. 计算从现在到过期时间的剩余毫秒数
            long remainingMillis = expectedEndTime.getTime() - System.currentTimeMillis();

            // 2. 只有当过期时间在未来时，才设置过期时间
            if (remainingMillis > 0) {
                RedisUtils.setCacheObject(cacheKey, banInfo, Duration.ofMillis(remainingMillis));
            } else {
                RedisUtils.deleteObject(cacheKey);
            }
        } else {
            // 如果没有指定过期时间，则调用不带过期参数的方法，实现永久存储
            RedisUtils.setCacheObject(cacheKey, banInfo);
        }
    }

    /**
     * 移除用户封禁缓存
     *
     * @param userId 用户ID
     */
    private static void removeUserBanCache(Long userId) {
        try {
            String cacheKey = BanConstants.USER_BAN_KEY + userId;
            RedisUtils.deleteObject(cacheKey);

            log.debug("移除用户封禁缓存成功, userId: {}", userId);
        } catch (Exception e) {
            log.error("移除用户封禁缓存失败, userId: {}", userId, e);
        }
    }

    /**
     * 移除IP封禁缓存
     *
     * @param ip IP地址
     */
    private static void removeIpBanCache(String ip) {
        try {
            String cacheKey = BanConstants.IP_BAN_KEY + ip;
            RedisUtils.deleteObject(cacheKey);

            log.debug("移除IP封禁缓存成功, ip: {}", ip);
        } catch (Exception e) {
            log.error("移除IP封禁缓存失败, ip: {}", ip, e);
        }
    }

    public void removeDeviceBanCache(String device) {
        try {
            String cacheKey = BanConstants.DEVICE_BAN_KEY + device;
            RedisUtils.deleteObject(cacheKey);

            log.debug("移除设备封禁缓存成功, device: {}", device);
        } catch (Exception e) {
            log.error("移除设备封禁缓存失败, device: {}", device, e);
        }
    }
}
