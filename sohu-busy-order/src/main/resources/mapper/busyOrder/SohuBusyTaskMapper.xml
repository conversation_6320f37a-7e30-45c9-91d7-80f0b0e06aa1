<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.busyOrder.mapper.SohuBusyTaskMapper">

    <resultMap type="com.sohu.busyOrder.domain.SohuBusyTask" id="SohuBusyTaskResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="countrySiteId" column="country_site_id"/>
        <result property="type" column="type"/>
        <result property="industryType" column="industry_type"/>
        <result property="pid" column="pid"/>
        <result property="title" column="title"/>
        <result property="siteIds" column="site_ids"/>
        <result property="content" column="content"/>
        <result property="annex" column="annex"/>
        <result property="state" column="state"/>
        <result property="templateId" column="template_id"/>
        <result property="receiveLimit" column="receive_limit"/>
        <result property="receiveOnlyUserId" column="receive_only_user_id"/>
        <result property="deliveryDay" column="delivery_day"/>
        <result property="deliveryMsg" column="delivery_msg"/>
        <result property="settleType" column="settle_type"/>
        <result property="deliveryStep" column="delivery_step"/>
        <result property="fullAmount" column="full_amount"/>
        <result property="fullCurrency" column="full_currency"/>
        <result property="kickbackType" column="kickback_type"/>
        <result property="kickbackValue" column="kickback_value"/>
        <result property="refuseMsg" column="refuse_msg"/>
        <result property="needSplit" column="need_split"/>
        <result property="templateId" column="template_id"/>
        <result property="taskNumber" column="task_number"/>
        <result property="isDraft" column="is_draft"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="countrySiteId" column="country_site_id"/>
        <result property="viewCount" column="view_count"/>
        <result property="relationId" column="relation_id"/>
        <result property="sortIndex" column="sort_index"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
    </resultMap>
    <update id="updateViewCount" parameterType="java.lang.String">
        UPDATE sohu_busy_task
        SET view_count = view_count + 1
        WHERE task_number = #{taskNumber}
    </update>

    <select id="selectTaskPage" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskVo">
        SELECT
        t.*,
        c.industry_name AS industry_name ,
        y.name AS type_name ,
        y.const_Mark AS constMark,
        u.nick_name AS audit_name
        FROM
        sohu_busy_task t
        INNER JOIN sohu_industry_category c ON t.industry_type = c.id
        INNER JOIN sohu_category y ON t.type = y.id
        LEFT JOIN sys_user u ON t.audit_user = u.user_id

        WHERE
        1=1
        <!--确保在主商单列表不包含售后中记录-->
        AND (SELECT COUNT(t_site.id)
        FROM sohu_busy_task_site t_site
        WHERE t_site.master_task_number = t.task_number
        and t_site.is_after_sales = 1
        ) = 0
        <if test="bo.siteId != null ">
            AND t.task_number IN (
                SELECT DISTINCT (master_task_number) FROM sohu_busy_task_site WHERE  site_id = #{bo.siteId}
            )
        </if>
        <if test="bo.title !=null and bo.title != '' ">
            AND t.title like concat('%', #{bo.title}, '%')
        </if>
        <if test="bo.userId !=null and bo.userId != '' ">
            AND t.user_id = ${bo.userId}
        </if>
        <if test="bo.type !=null and bo.type != '' ">
            AND t.type = ${bo.type}
        </if>
        <if test="bo.industryType !=null and bo.industryType != '' ">
            AND t.industry_type = ${bo.industryType}
        </if>
        <if test="bo.taskNumber !=null and bo.taskNumber != '' ">
            AND t.task_number = #{bo.taskNumber}
        </if>
        <if test="bo.taskNumbers != null and bo.taskNumbers.size > 0">
            AND t.task_number IN
            <foreach item="item" index="index" collection="bo.taskNumbers" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="bo.type != null ">
            AND t.type = #{bo.type}
        </if>
        <if test="bo.isDraft != null ">
            AND t.is_draft = #{bo.isDraft}
        </if>
        <if test="bo.splitState != null ">
            AND t.split_state = #{bo.splitState}
        </if>
        <if test="bo.needSplit != null ">
            AND t.need_split = #{bo.needSplit}
        </if>
        <if test="bo.countrySiteId != null ">
            AND t.country_site_id = #{bo.countrySiteId}
        </if>
        <if test="bo.state != null and bo.state != '' ">
            <choose>
                <when test="bo.state == 'WaitPay'">
                    AND t.state IN ('WaitPay', 'WaitFullAmountPay', 'WaitIndependentPay', 'WaitPromisePay')
                </when>
                <otherwise>
                    AND t.state = #{bo.state}
                </otherwise>
            </choose>
        </if>
        <if test="bo.shelfState != null and bo.shelfState != '' ">
            AND t.shelf_state = #{bo.shelfState}
        </if>
        <if test="bo.startTime != null and bo.startTime != ''">
            AND t.create_time between #{bo.createTime} and #{bo.updateTime}
        </if>
        <if test="bo.deliveryStartDay != null and bo.deliveryEndDay != null and bo.deliveryStartDay != '' and bo.deliveryEndDay != ''">
            AND t.delivery_day Between #{bo.deliveryStartDay} AND #{bo.deliveryEndDay}
        </if>
        <if test="bo.minAmount != null">
            AND t.full_amount &gt;= #{bo.minAmount}
        </if>
        <if test="bo.maxAmount != null">
            AND t.full_amount &lt;= #{bo.maxAmount}
        </if>
        <if test="bo.settleType != null">
            AND t.settle_type = #{bo.settleType}
        </if>
        <if test="bo.industryTypeList != null and bo.industryTypeList.size > 0">
            AND t.industry_type IN
            <foreach item="item" index="index" collection="bo.industryTypeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="bo.isBlack != null and bo.isBlack == true and bo.handleIds != null and bo.handleIds.size > 0">
            AND t.id IN
            <foreach item="item" index="index" collection="bo.handleIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="bo.isBlack != null and bo.isBlack == false and bo.handleIds != null and bo.handleIds.size > 0">
            AND t.id NOT IN
            <foreach item="item" index="index" collection="bo.handleIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY
        <choose>
            <when test="bo.sortBy == 'createTime'">
                t.create_time DESC
            </when>
            <when test="bo.sortBy == 'priceAsc'">
                t.full_amount ASC
            </when>
            <when test="bo.sortBy == 'priceDesc'">
                t.full_amount DESC
            </when>
            <when test="bo.sortBy == 'comprehensive'">
                t.create_time DESC, t.full_amount ASC
            </when>
            <when test="bo.sortBy == 'imGroupTask'">
                FIELD(t.state, 'WaitSplit', 'Execute', 'Over'), t.create_time DESC
            </when>
            <otherwise>
                t.create_time DESC
            </otherwise>
        </choose>
    </select>
    <select id="queryByTaskId" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskVo"
            parameterType="java.lang.Long">
        SELECT t.*,
               u.nick_name AS nick_name,u.avatar AS user_avatar,u.remark AS user_remark
        FROM sohu_busy_task t
                 LEFT JOIN sys_user u ON t.user_id = u.user_id
        WHERE t.id = #{id}
    </select>
    <select id="countSalesAmountByTime" resultType="java.math.BigDecimal">
        SELECT COUNT(full_amount) AS salesAmount
        FROM sohu_busy_task
        ${ew.getCustomSqlSegment}
    </select>
    <update id="updateTaskState">
        UPDATE
          sohu_busy_task
        SET
          state = #{state}
        WHERE
          task_number = #{taskNumber}
    </update>

    <select id="selectFlowBusyTaskOfState" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskVo">
        SELECT
            t.*
        FROM
            sohu_busy_task t
                JOIN sohu_category c ON t.type = c.id
                JOIN sohu_busy_task_pay p on p.task_number = t.task_number
        WHERE
            t.state NOT IN ('Cancel', 'CompelOff', 'OffShelf','OverSettle')
          and c.const_mark = 'FLOW_TASK'
          AND p.pay_status = 'Paid' and date_add(p.pay_time,INTERVAL 150 DAY) &lt; now()
    </select>
    <select id="getAllListWithRole" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskAllListVo">
        SELECT
            t.id,
            t.task_number AS masterTaskNumber,
            t.title,
            t.full_amount,
            t.audit_time,
            t.state,
            t.sort_index,
            t.start_time,
            t.type,
            t.end_time,
            t.user_id,
            u.nick_name AS nickName,
            y.NAME AS typeName
        FROM
            sohu_busy_task t
        LEFT JOIN sys_user u ON t.user_id = u.user_id
        LEFT JOIN sohu_category y ON t.type = y.id
        WHERE 1=1
        <if test="bo.masterTaskNumber!=null and bo.masterTaskNumber!=''">
            AND t.task_number = #{bo.masterTaskNumber}
        </if>
        <if test="bo.title!=null and bo.title!=''">
            AND t.title like concat('%', #{bo.title}, '%')
        </if>
        <if test="bo.type!=null and bo.type!=''">
            AND t.type = #{bo.type}
        </if>
        <if test="bo.state!=null and bo.state!=''">
            AND t.state = #{bo.state}
        </if>
        <if test="bo.userId!=null and bo.userId!=''">
            AND t.user_id = #{bo.userId}
        </if>
        <if test="bo.startTime!=null and bo.startTime!=''">
            AND t.audit_time between #{bo.startTime} and #{bo.endTime}
        </if>
        <if test="bo.nickName!=null and bo.nickName!=''">
            AND u.nick_name like concat('%', #{bo.nickName}, '%')
        </if>
        <if test="bo.sortIndex!=null and bo.sortIndex!=''">
            AND t.sort_index = #{bo.sortIndex}
        </if>
        <if test="bo.isTop!=null">
            <choose>
                <when test="bo.isTop">
                    AND t.sort_index &lt; 999
                </when>
                <otherwise>
                    AND t.sort_index = 999
                </otherwise>
            </choose>
        </if>

    </select>
    <select id="getTopicList" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo">
        SELECT
        sbts.*
        FROM sohu_topic_content_relation stcr
        INNER JOIN sohu_busy_task_site sbts ON sbts.task_number = stcr.content_id
        WHERE sbts.state = 'WaitReceive'
        AND stcr.del_flag = 0
        AND stcr.content_type = 'BusyTask'
        <if test="bo.topicPid!=null and bo.topicPid!=''">
            AND stcr.topic_pid = #{bo.topicPid}
        </if>
        <if test="bo.topicId!=null and bo.topicId!=''">
            AND stcr.topic_id = #{bo.topicId}
        </if>
        GROUP BY sbts.task_number
        <if test="bo.isSortByViewCount!=null and bo.isSortByViewCount!=''">
            ORDER BY sbts.view_count DESC
        </if>
    </select>

    <select id="queryTopTaskList" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo">
        SELECT
        t.master_task_number,
        MAX(t.id) AS id,
        t.user_id AS user_id,
        t.task_number AS task_number,
        t.site_id AS site_id,
        t.country_site_id AS country_site_id,
        t.type AS type,
        t.industry_type AS industry_type,
        t.title AS title,
        t.content AS content,
        t.annex AS annex,
        t.is_receive AS is_receive,
        t.is_independent AS is_independent,
        t.receive_limit AS receive_limit,
        t.delivery_day AS delivery_day,
        t.delivery_msg AS delivery_msg,
        t.settle_type AS settle_type,
        t.delivery_step AS delivery_step,
        t.template_id AS template_id,
        t.state AS state,
        t.shelf_state AS shelf_state,
        t.refuse_msg AS refuse_msg,
        t.audit_user AS audit_user,
        t.audit_time AS audit_time,
        t.shelf_user AS shelf_user,
        t.shelf_time AS shelf_time,
        t.full_amount AS full_amount,
        t.full_currency AS full_currency,
        t.receive_only_user_id AS receive_only_user_id,
        t.kickback_type AS kickback_type,
        t.kickback_value AS kickback_value,
        t.distribution_amount AS distribution_amount,
        t.create_time AS create_time,
        t.update_time AS update_time,
        t.view_count AS view_count,
        t.is_after_sales AS is_after_sales,
        sbt.delivery_time AS delivery_time,
        sbt.delivery_type AS delivery_type,
        sbt.delivery_standard AS delivery_standard,
        sbt.delivery_time_unit AS delivery_time_unit,
        sbt.receive_num AS receive_num,
        sbt.sort_index AS sortIndex,
        sbt.is_approve_receive AS isApproveReceive,
        sbt.id AS busyTaskId,
        s.`name` AS site_name,
        c.industry_name AS industry_name,
        y.name AS type_name,
        y.const_mark AS constMark,
        sim.id AS materialId
        FROM
        sohu_busy_task_site t
        INNER JOIN sohu_busy_task sbt ON sbt.task_number = t.master_task_number AND sbt.state != 'WaitPay' AND sbt.delivery_standard != sbt.pass_num
        INNER JOIN sohu_site s ON s.id = t.site_id
        INNER JOIN sohu_industry_category c ON t.industry_type = c.id
        INNER JOIN sohu_category y ON t.type = y.id
        LEFT JOIN
        sohu_independent_material sim
        ON t.task_number = sim.material_code
        OR (t.master_task_number = sim.material_code)
        AND sim.status = 'OnShelf'
        LEFT JOIN sohu_busy_task_label lbl ON lbl.task_number = sbt.task_number
        LEFT JOIN sys_user su ON t.user_id = su.user_id
        WHERE
        t.state = 'WaitReceive'
        AND t.shelf_state = 'OnShelf'
        AND NOW() BETWEEN sbt.start_time AND sbt.end_time
        <if test="bo.title !=null and bo.title != '' ">
            AND t.title like concat('%', #{bo.title}, '%')
        </if>
        <if test="bo.siteId !=null ">
            AND t.site_id = #{bo.siteId}
        </if>
        <if test="bo.countrySiteId !=null ">
            AND t.country_site_id = #{bo.countrySiteId}
        </if>
        <if test="bo.type != null ">
            AND t.type = #{bo.type}
        </if>
        GROUP BY
        t.master_task_number
        ORDER BY
        sbt.sort_index
    </select>

    <select id="countTasksByUser" resultType="com.sohu.busyorder.api.vo.TaskGroupVo">
        SELECT user_id AS userId, COUNT(*) AS taskCount
        FROM sohu_busy_task
        where 1=1
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;#{endTime}
        </if>
        GROUP BY user_id
    </select>


</mapper>
