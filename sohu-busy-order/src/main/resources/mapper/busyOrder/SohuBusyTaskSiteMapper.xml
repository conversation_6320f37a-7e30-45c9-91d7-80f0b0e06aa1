<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.busyOrder.mapper.SohuBusyTaskSiteMapper">

    <resultMap type="com.sohu.busyOrder.domain.SohuBusyTaskSite" id="SohuBusyTaskSiteResult">
        <result property="id" column="id"/>
        <result property="taskNumber" column="task_number"/>
        <result property="userId" column="user_id"/>
        <result property="siteId" column="site_id"/>
        <result property="countrySiteId" column="country_site_id"/>
        <result property="type" column="type"/>
        <result property="industryType" column="industry_type"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="annex" column="annex"/>
        <result property="receiveLimit" column="receive_limit"/>
        <result property="isReceive" column="is_receive"/>
        <result property="receiveOnlyUserId" column="receive_only_user_id"/>
        <result property="deliveryDay" column="delivery_day"/>
        <result property="deliveryMsg" column="delivery_msg"/>
        <result property="settleType" column="settle_type"/>
        <result property="deliveryStep" column="delivery_step"/>
        <result property="kickbackType" column="kickback_type"/>
        <result property="kickbackValue" column="kickback_value"/>
        <result property="templateId" column="template_id"/>
        <result property="state" column="state"/>
        <result property="shelfState" column="shelf_state"/>
        <result property="refuseMsg" column="refuse_msg"/>
        <result property="auditUser" column="audit_user"/>
        <result property="auditTime" column="audit_time"/>
        <result property="shelfUser" column="shelf_user"/>
        <result property="shelfTime" column="shelf_time"/>
        <result property="isIndependent" column="is_independent"/>
        <result property="distributionAmount" column="distribution_amount"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="fullAmount" column="full_amount"/>
        <result property="isAfterSales" column="is_after_sales"/>
        <result property="masterTaskNumber" column="master_task_number"/>
    </resultMap>

    <select id="selectTaskSitePage" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo">
        SELECT DISTINCT
        t.*,
        sbt.delivery_time,
        s.`name` AS site_name,
        c.industry_name AS industry_name ,
        u.nick_name AS audit_name,
        r.share_person AS share_person
        FROM
        sohu_busy_task_site t
        INNER JOIN sohu_busy_task sbt ON sbt.task_number = t.master_task_number
        INNER JOIN sohu_site s ON s.id = t.site_id
        INNER JOIN sohu_industry_category c ON t.industry_type = c.id
        INNER JOIN sohu_busy_task_receive r ON t.task_number = r.task_number
        LEFT JOIN sys_user u ON t.audit_user = u.user_id
        WHERE
        is_after_sales = 0
        <if test="bo.title !=null and bo.title != '' ">
            AND t.title like concat('%', #{bo.title}, '%')
        </if>
        <if test="bo.siteId !=null ">
            AND t.site_id = #{bo.siteId}
        </if>
        <if test="bo.userId != null and bo.userId != '' ">
            AND t.user_id = #{bo.userId}
        </if>
        <if test="bo.countrySiteId !=null ">
            AND t.country_site_id = #{bo.countrySiteId}
        </if>
        <if test="bo.masterTaskNumber !=null and bo.masterTaskNumber != '' ">
            AND t.master_task_number = #{bo.masterTaskNumber}
        </if>
        <if test="bo.taskNumber !=null and bo.taskNumber != '' ">
            AND t.task_number = #{bo.taskNumber}
        </if>
        <if test="bo.taskNumberLikeQuery !=null and bo.taskNumberLikeQuery != '' ">
            AND t.task_number like concat('%', #{bo.taskNumberLikeQuery}, '%')
        </if>
        <if test="bo.type != null ">
            AND t.type = #{bo.type}
        </if>
        <if test="bo.state != null and bo.state != '' ">
            AND t.state = #{bo.state}
        </if>
        <if test="bo.shelfState != null and bo.shelfState != '' ">
            AND t.shelf_state = #{bo.shelfState}
        </if>
        <if test="bo.startTime != null and bo.startTime != ''">
            AND t.create_time between #{bo.createTime} and #{bo.updateTime}
        </if>
        <if test="bo.startDeliveryTime != null and bo.endDeliveryTime != ''">
            AND sbt.delivery_time between #{bo.startDeliveryTime} and #{bo.endDeliveryTime}
        </if>
        ORDER BY sbt.delivery_time ASC,t.create_time DESC
    </select>

    <select id="selectTaskList" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo">
        SELECT DISTINCT
        t.*,
        s.`name` AS site_name,
        u.nick_name AS audit_name,
        i.industry_name AS industry_name,
        r.share_person AS share_person,
        r.user_id as receiveUserId
        FROM sohu_busy_task_site t
        INNER JOIN sohu_site s ON s.id = t.site_id
        LEFT JOIN sys_user u ON t.audit_user = u.user_id
        INNER JOIN sohu_busy_task_receive r ON t.task_number = r.task_number
        LEFT JOIN sohu_industry_category i ON t.industry_type = i.id
        WHERE t.master_task_number = #{masterTaskNumber}
        ORDER BY t.create_time DESC
    </select>

    <select id="queryByTaskNumber" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo">
        SELECT t.*,
               s.`name` AS site_name,
               u.nick_name AS nick_name,u.avatar AS avatar,u.remark AS user_remark,
               c.industry_name AS industry_name ,
               y.name      AS type_name,
               y.const_mark AS constMark,
               b.image_url AS imageUrl,
               b.video_url AS videoUrl,
               b.address   AS address,
               b.location  AS location,
               b.time_unit AS timeUnit,
               b.delivery_day AS deliveryDay,
               b.delivery_type AS deliveryType,
               b.delivery_standard AS deliveryStandard,
               b.delivery_standard AS waitPassPersonNum,
               b.delivery_time_unit AS deliveryTimeUnit,
               b.single_amount AS singleAmount,
               b.receive_num as receiveNum,
               b.id AS busyTaskId,
               b.is_approve_receive AS isApproveReceive,
               b.ip_address AS ipAddress,
               sim.id  AS materialId
        FROM sohu_busy_task_site t
                 INNER JOIN sohu_site s ON s.id = t.site_id
                 INNER JOIN sohu_busy_task b ON t.master_task_number = b.task_number
                 INNER JOIN sohu_industry_category c ON t.industry_type = c.id
                 INNER JOIN sohu_category y ON t.type = y.id
        LEFT JOIN sys_user u ON t.user_id = u.user_id
        LEFT JOIN
        sohu_independent_material sim
        ON t.task_number = sim.material_code
        AND sim.status = 'OnShelf'
        WHERE t.task_number = #{taskNumber}
        <if test="shareUserId != null">
            AND t.shelf_state = 'OnShelf'
        </if>
    </select>

    <select id="selectMyTaskSitePage" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo">
        SELECT DISTINCT
        t.*,
        s.`name` AS site_name,
        c.name AS type_name,
        i.industry_name AS industry_name,
        r.share_person AS share_person
        FROM
        sohu_busy_task_site t
        INNER JOIN sohu_site s ON s.id = t.site_id
        INNER JOIN sohu_category c ON t.type = c.id
        INNER JOIN sohu_busy_task_receive r ON t.task_number = r.task_number
        INNER JOIN sohu_industry_category i ON t.industry_type = i.id
        WHERE
        t.is_receive = 1 and t.is_after_sales=0
        <if test="bo.userId != null and bo.userId != ''">
            AND t.user_id = #{bo.userId}
        </if>
        <if test="bo.title !=null and bo.title != '' ">
            AND t.title like concat('%', #{bo.title}, '%')
        </if>
        <if test="bo.siteId !=null ">
            AND t.site_id = #{bo.siteId}
        </if>
        <if test="bo.countrySiteId !=null ">
            AND t.country_site_id = #{bo.countrySiteId}
        </if>
        <if test="bo.taskNumber !=null and bo.taskNumber != '' ">
            AND t.task_number = #{bo.taskNumber}
        </if>
        <if test="bo.type != null ">
            AND t.type = #{bo.type}
        </if>
        <if test="bo.state != null and bo.state != '' ">
            AND t.state = #{bo.state}
        </if>
        <if test="bo.shelfState != null and bo.shelfState != '' ">
            AND t.shelf_state = #{shelfState}
        </if>
        <if test="bo.startTime != null and bo.startTime != ''">
            AND t.create_time between #{bo.createTime} and #{bo.updateTime}
        </if>
        ORDER BY t.create_time DESC
    </select>

    <select id="queryPageTaskList" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo">
        SELECT
        t.master_task_number,
        MAX(t.id) AS id,
        t.user_id AS user_id,
        t.task_number AS task_number,
        t.site_id AS site_id,
        t.country_site_id AS country_site_id,
        t.type AS type,
        t.industry_type AS industry_type,
        t.title AS title,
        t.content AS content,
        t.annex AS annex,
        t.is_receive AS is_receive,
        t.is_independent AS is_independent,
        t.receive_limit AS receive_limit,
        t.delivery_day AS delivery_day,
        t.delivery_msg AS delivery_msg,
        t.settle_type AS settle_type,
        t.delivery_step AS delivery_step,
        t.template_id AS template_id,
        t.state AS state,
        t.shelf_state AS shelf_state,
        t.refuse_msg AS refuse_msg,
        t.audit_user AS audit_user,
        t.audit_time AS audit_time,
        t.shelf_user AS shelf_user,
        t.shelf_time AS shelf_time,
        t.full_amount AS full_amount,
        t.full_currency AS full_currency,
        t.receive_only_user_id AS receive_only_user_id,
        t.kickback_type AS kickback_type,
        t.kickback_value AS kickback_value,
        t.distribution_amount AS distribution_amount,
        t.create_time AS create_time,
        t.update_time AS update_time,
        t.view_count AS view_count,
        t.is_after_sales AS is_after_sales,
        sbt.delivery_time AS delivery_time,
        sbt.delivery_type AS delivery_type,
        sbt.delivery_standard AS delivery_standard,
        sbt.delivery_time_unit AS delivery_time_unit,
        sbt.receive_num AS receive_num,
        sbt.sort_index AS sortIndex,
        sbt.is_approve_receive AS isApproveReceive,
        sbt.id AS busyTaskId,
        s.`name` AS site_name,
        c.industry_name AS industry_name,
        y.name AS type_name,
        y.const_mark AS constMark,
        sim.id AS materialId
        <if test="bo.mcnId != null">
            ,#{bo.mcnId} AS mcnId
        </if>
        FROM
        sohu_busy_task_site t
        INNER JOIN sohu_busy_task sbt ON sbt.task_number = t.master_task_number AND sbt.state != 'WaitPay' AND sbt.delivery_standard != sbt.pass_num
        INNER JOIN sohu_site s ON s.id = t.site_id
        INNER JOIN sohu_industry_category c ON t.industry_type = c.id
        INNER JOIN sohu_category y ON t.type = y.id
        LEFT JOIN
        sohu_independent_material sim
        ON t.task_number = sim.material_code
        OR (t.master_task_number = sim.material_code)
        AND sim.status = 'OnShelf'
        LEFT JOIN sohu_busy_task_label lbl ON lbl.task_number = sbt.task_number
        <if test="bo.mcnId != null">
            INNER JOIN sohu_busy_task_win_mcn wm ON wm.task_number = t.task_number AND wm.mcn_id = #{bo.mcnId}
            LEFT JOIN sohu_busy_task_win_mcn_visual wmv ON wm.id = wmv.window_id
        </if>
        LEFT JOIN sys_user su ON t.user_id = su.user_id
        WHERE
        t.state = 'WaitReceive'
        AND t.shelf_state = 'OnShelf'
        <if test="bo.masterTaskNumber !=null and bo.masterTaskNumber != '' ">
            AND t.master_task_number like concat('%', #{bo.masterTaskNumber}, '%')
        </if>
        <if test="bo.handleIds != null and bo.handleIds.size > 0">
            AND t.id NOT IN
            <foreach item="item" index="index" collection="bo.handleIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="bo.taskNumber !=null and bo.taskNumber != '' ">
            AND t.task_number like concat('%', #{bo.taskNumber}, '%')
        </if>
        <if test="bo.receiveLimit != null">
            AND t.receive_limit = #{bo.receiveLimit}
        </if>
        <if test="bo.mcnId != null">
            AND wm.receive_state = 0
            AND (wm.visual_type = 'ALL' OR wmv.user_id = #{bo.userId})
        </if>
        <if test="bo.title !=null and bo.title != '' ">
            AND t.title like concat('%', #{bo.title}, '%')
        </if>
        <if test="bo.siteId !=null ">
            AND t.site_id = #{bo.siteId}
        </if>
        <if test="bo.countrySiteId !=null ">
            AND t.country_site_id = #{bo.countrySiteId}
        </if>
        <if test="bo.type != null ">
            AND t.type = #{bo.type}
        </if>
        <if test="bo.typeIds != null and bo.typeIds.size > 0">
            AND t.type in
            <foreach item="item" index="index" collection="bo.typeIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="bo.industryTypeIds != null and bo.industryTypeIds.size > 0">
            AND t.industry_type in
            <foreach item="item" index="index" collection="bo.industryTypeIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="bo.receiveOnlyUserId != null ">
            AND t.receive_only_user_id = #{bo.receiveOnlyUserId}
        </if>
        <if test="bo.receiveOnlyUserId == null">
            AND t.receive_only_user_id IS NULL
        </if>
        <if test="bo.industryType != null ">
            AND t.industry_type = #{bo.industryType}
        </if>
        <if test="bo.startTime != null and bo.startTime != ''">
            AND t.create_time between #{bo.createTime} and #{bo.updateTime}
        </if>
        <if test="bo.minAmount != null">
            AND t.full_amount &gt;= #{bo.minAmount}
        </if>
        <if test="bo.maxAmount != null">
            AND t.full_amount &lt;= #{bo.maxAmount}
        </if>
        <if test="bo.deliveryStartDay != null and bo.deliveryEndDay != null and bo.deliveryStartDay != '' and bo.deliveryEndDay != ''">
            AND t.delivery_day Between #{bo.deliveryStartDay} AND #{bo.deliveryEndDay}
        </if>
        <if test="bo.settleType != null">
            AND t.settle_type = #{bo.settleType}
        </if>
        <if test="bo.userId != null and bo.userId != ''">
            AND t.user_id = #{bo.userId}
        </if>
        <if test="bo.filterTask != null and bo.filterTask != ''">
            AND NOT (t.type = 'COMMON_TASK')
        </if>
        <if test="bo.hasKickType == true and bo.hasKickType != null and bo.hasKickType != ''">
            AND t.kickback_type != 'none'
        </if>
        <if test="bo.startDeliveryTime != null and bo.endDeliveryTime != ''">
            AND sbt.delivery_time between #{bo.startDeliveryTime} and #{bo.endDeliveryTime}
        </if>
        <if test="bo.focusUserIdList != null and bo.focusUserIdList.size > 0">
            AND t.user_id IN
            <foreach item="item" index="index" collection="bo.focusUserIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="bo.notUserIds != null and bo.notUserIds.size > 0">
            and t.user_id not in
            <foreach item="item" index="index" collection="bo.notUserIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="bo.nickName != null and bo.nickName != ''">
            AND su.nick_name LIKE CONCAT('%', #{bo.nickName}, '%')
        </if>
        GROUP BY
            t.master_task_number
        ORDER BY
        sbt.sort_index
        <choose>
            <when test="bo.sortBy == 'createTime'">
                ,t.create_time DESC
            </when>
            <when test="bo.sortBy == 'priceAsc'">
                ,t.full_amount ASC
            </when>
            <when test="bo.sortBy == 'priceDesc'">
                ,t.full_amount DESC
            </when>
            <when test="bo.sortBy == 'comprehensive'">
                ,t.create_time DESC, t.full_amount ASC
            </when>
            <when test="bo.sortBy == 'sort'">
                ,sbt.sort_index DESC
            </when>
            <when test="bo.sortBy == 'distributionAmountDesc'">
                ,t.distribution_amount DESC
            </when>
            <when test="bo.sortBy == 'distributionAmountAsc'">
                ,sbt.distribution_amount ASC
            </when>
            <otherwise>
                ,sbt.create_time DESC
            </otherwise>
        </choose>
        <if test="bo.labelList != null and bo.labelList.size > 0" >
            ,
            CASE
            WHEN lbl.label_id IS NOT NULL AND lbl.label_id IN
            <foreach item="item" index="index" collection="bo.labelList" open="(" separator="," close=")">
                #{item}
            </foreach> THEN 1
            ELSE 2
            END,
            lbl.label_id
        </if>
        <if test = "bo.industryTypeList != null and bo.industryTypeList.size > 0">
            ,
            CASE
            -- 排序优先级：如果 `industry_type` 在传入的优先 `industry_ids` 中，则排在前面
            WHEN c.id IN
            <foreach item="item" index="index" collection="bo.industryTypeList" open="(" separator="," close=")">
                #{item}
            </foreach> THEN 1
            ELSE 2
            END,
            c.id
        </if>

    </select>

    <select id="queryPageMcnTaskList" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo">
        SELECT
        t.*,
        s.`name` AS site_name,
        c.industry_name AS industry_name,
        y.name AS type_name
        FROM
        sohu_busy_task_site t
        INNER JOIN sohu_site s ON s.id = t.site_id
        INNER JOIN sohu_industry_category c ON t.industry_type = c.id
        INNER JOIN sohu_category y ON t.type = y.id
        LEFT JOIN sohu_busy_task_win_mcn wm ON wm.task_number = t.task_number AND wm.mcn_id = #{bo.mcnId}
        WHERE
        t.state = 'WaitReceive'
        AND t.shelf_state = 'OnShelf' AND t.receive_limit = 0
        AND wm.id IS NULL
        <if test="bo.title !=null and bo.title != '' ">
            AND t.title like concat('%', #{bo.title}, '%')
        </if>
        <if test="bo.siteId !=null ">
            AND t.site_id = #{bo.siteId}
        </if>
        <if test="bo.type != null ">
            AND t.type = #{bo.type}
        </if>
        <if test="bo.industryType != null ">
            AND t.industry_type = #{bo.industryType}
        </if>
        ORDER BY t.create_time DESC
    </select>

    <select id="queryPageMcnTaskWindowList" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo">
        SELECT
        t.*,
        s.`name` AS site_name,
        c.industry_name AS industry_name,
        y.name AS type_name,
        wm.visual_type AS visualType,
        wm.id AS windowId
        FROM
        sohu_busy_task_site t
        INNER JOIN sohu_site s ON s.id = t.site_id
        INNER JOIN sohu_industry_category c ON t.industry_type = c.id
        INNER JOIN sohu_category y ON t.type = y.id
        INNER JOIN sohu_busy_task_win_mcn wm ON wm.task_number = t.task_number
        WHERE wm.mcn_id = #{bo.mcnId}
        <if test="bo.title !=null and bo.title != '' ">
            AND t.title like concat('%', #{bo.title}, '%')
        </if>
        <if test="bo.siteId !=null ">
            AND t.site_id = #{bo.siteId}
        </if>
        <if test="bo.type != null ">
            AND t.type = #{bo.type}
        </if>
        <if test="bo.industryType != null ">
            AND t.industry_type = #{bo.industryType}
        </if>
        <if test="bo.receiveState != null ">
            AND wm.receive_state = #{bo.receiveState}
        </if>
        ORDER BY t.create_time DESC
    </select>

    <select id="selectTaskByTaskNumber" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT s.master_task_number
        FROM sohu_busy_task_site s
        WHERE s.task_number = #{taskNumber};
    </select>

    <select id="queryPageTaskStateLists" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo">
        SELECT
            t.title,t.full_amount,t.create_time,
            y.name AS type_name,
            b.view_count
        FROM
            sohu_busy_task_site t
                INNER JOIN sohu_category y ON t.type = y.id
                INNER JOIN sohu_busy_task b ON t.master_task_number = b.task_number
        AND t.state IN ('Execute','Over','WaitReceive')
        <if test="bo.userId != null and bo.userId != ''">
            AND t.user_id = #{bo.userId}
        </if>
        <if test="bo.siteId !=null and bo.userId != ''">
            AND t.site_id = #{bo.siteId}
        </if>
        <if test="bo.startTime != null and bo.startTime != ''">
            AND t.create_time between #{bo.createTime} and #{bo.updateTime}
        </if>
    </select>


    <update id="updateAfterSales">
        UPDATE sohu_busy_task_site
        SET is_after_sales = #{states}
        WHERE task_number = #{taskNumber}
    </update>

    <select id="busyTaskCollectList" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo">
        SELECT
        sbt.*,
        sc.NAME AS typeName,
        sic.industry_name AS industryName,
        sbts.task_number AS childTaskNumber
        FROM
        sohu_user_collect suc
        INNER JOIN sohu_busy_task sbt ON suc.task_number = sbt.task_number
        LEFT JOIN sohu_busy_task_site sbts ON sbt.task_number = sbts.master_task_number
        INNER JOIN sohu_category sc ON sbt.type = sc.id
        INNER JOIN sohu_industry_category sic ON sbt.industry_type = sic.id
        WHERE
        suc.user_id = #{userId}
        AND suc.busy_type = 'BusyTask'
        <if test="title != null and title != ''">
            AND sbts.title LIKE concat( '%',#{title}, '%')
        </if>
        ORDER BY
        suc.create_time DESC
    </select>
    <select id="queryReceiveWindowList" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo">
        SELECT
        t.*,
        s.`name` AS siteName,
        c.industry_name AS industryName,
        y.name AS typeName,
        wm.visual_type AS visualType,
        r.create_time AS receiveTime,
        r.user_id AS receiveUserId
        FROM
        sohu_busy_task_site t
        INNER JOIN sohu_site s ON s.id = t.site_id
        INNER JOIN sohu_industry_category c ON t.industry_type = c.id
        INNER JOIN sohu_category y ON t.type = y.id
        INNER JOIN sohu_busy_task_win_mcn wm ON wm.task_number = t.task_number AND wm.mcn_id = #{bo.mcnId}
        INNER JOIN sohu_busy_task_receive r on r.task_number = t.task_number
        WHERE wm.receive_state = 1
        <if test="bo.masterTaskNumber !=null and bo.masterTaskNumber != '' ">
            AND t.master_task_number like concat('%', #{bo.masterTaskNumber}, '%')
        </if>
        <if test="bo.title !=null and bo.title != '' ">
            AND t.title like concat('%', #{bo.title}, '%')
        </if>
        <if test="bo.state !=null and bo.state != '' ">
            AND t.state = #{bo.state}
        </if>
        <if test="bo.siteId !=null ">
            AND t.site_id = #{bo.siteId}
        </if>
        <if test="bo.type != null ">
            AND t.type = #{bo.type}
        </if>
        <if test="bo.startTime != null and bo.startTime != ''">
            and DATE_FORMAT(t.create_time, '%Y-%m-%d') >= DATE_FORMAT(#{bo.startTime}, '%Y-%m-%d')
        </if>
        <if test="bo.endTime != null and bo.endTime != ''">
            and DATE_FORMAT(t.create_time, '%Y-%m-%d') &lt;= DATE_FORMAT(#{bo.endTime}, '%Y-%m-%d')
        </if>
        ORDER BY t.create_time DESC
    </select>

    <select id="listDistribution" resultType="com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo">
        SELECT
            t.master_task_number,
            t.id,
            t.user_id,
            t.task_number,
            t.site_id,
            t.country_site_id,
            t.type,
            t.industry_type,
            t.title,
            t.content,
            t.annex,
            t.is_receive,
            t.state,
            t.shelf_state,
            t.full_amount,
            t.distribution_amount,
            t.create_time,
            t.update_time,
            t.view_count,
            sbt.delivery_time,
            sbt.delivery_type,
            sbt.delivery_standard,
            sbt.delivery_time_unit,
            s.`name` AS site_name,
            c.industry_name AS industryName,
            y.name AS type_name,
            y.const_mark AS constMark,
            sim.id AS materialId,
            sim.independent_price,
            imu.material_share_url
        FROM
            sohu_busy_task_site t
                INNER JOIN sohu_busy_task sbt ON sbt.task_number = t.master_task_number
                INNER JOIN sohu_site s ON s.id = t.site_id
                INNER JOIN sohu_industry_category c ON t.industry_type = c.id
                INNER JOIN sohu_category y ON t.type = y.id
                LEFT JOIN sohu_independent_material sim ON
                t.master_task_number = sim.material_code
                AND sim.material_type IN ('FLOW_TASK','COMMON_TASK')
                INNER JOIN sohu_independent_material_user imu ON sim.id = imu.material_id
        WHERE
            imu.material_share_user_id = #{userId}
        <if test="busyTitle != null and busyTitle != ''">
            AND t.title LIKE concat( '%',#{busyTitle}, '%')
        </if>
        GROUP BY
            t.master_task_number
         ORDER BY
             t.create_time DESC
    </select>

</mapper>
