package com.sohu.busyOrder.controller.app;

import com.sohu.busyOrder.service.ISohuBusyTaskService;
import com.sohu.busyorder.api.bo.SohuBusyTaskBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskReceiveApplyBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskReceiveApplyProcessBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskSiteBo;
import com.sohu.busyorder.api.vo.SohuBusyTaskReceiveApplyVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskVo;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.idempotent.annotation.RepeatSubmit;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.aspect.UserBehavior;
import com.sohu.middle.api.enums.behavior.BehaviorBusinessTypeEnum;
import com.sohu.middle.api.enums.behavior.OperaTypeEnum;
import com.sohu.middle.api.vo.CategoryVo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * APP商单新版-主任务/子任务
 * 前端访问路由地址为:/app/busy/task
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/busy/task")
public class AppBusyTaskController extends BaseController {

    private final ISohuBusyTaskService iSohuBusyTaskService;

    /**
     * 查询主任务分页列表
     */
    @GetMapping("/page")
    public TableDataInfo<SohuBusyTaskVo> listPage(SohuBusyTaskBo bo, PageQuery pageQuery) {
        return iSohuBusyTaskService.queryPageLists(bo, pageQuery);
    }

    /**
     * 查询子任务分页列表
     */
    @GetMapping("/child/page")
    public TableDataInfo<SohuBusyTaskSiteVo> listChildPage(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        return iSohuBusyTaskService.queryPageChildLists(bo, pageQuery);
    }

    /**
     * 查询子任务分页列表-任务广场
     */
    @GetMapping("/task/page")
    public TableDataInfo<SohuBusyTaskSiteVo> taskListPage(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        return iSohuBusyTaskService.queryPageTaskLists(bo, pageQuery);
    }

    /**
     * 查询子任务分页列表-数据中心
     */
    @GetMapping("/task/state/page")
    public TableDataInfo<SohuBusyTaskSiteVo> taskStateListPage(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        return iSohuBusyTaskService.queryPageTaskStateLists(bo, pageQuery);
    }

    /**
     * 查询我的子任务订单分页列表-任务方
     */
    @GetMapping("/child/my/page")
    public TableDataInfo<SohuBusyTaskSiteVo> listMyChildPage(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        return iSohuBusyTaskService.queryPageMyChildLists(bo, pageQuery);
    }

    /**
     * 查询主任务的子任务详情列表
     */
    @GetMapping("/child/list/{masterTaskNumber}")
    public R<List<SohuBusyTaskSiteVo>> getChildList(@NotNull(message = "愿望编号不能为空") @PathVariable String masterTaskNumber) {
        return R.ok(iSohuBusyTaskService.getChildList(masterTaskNumber));
    }

    /**
     * 获取子任务主体详细信息
     *
     * @param taskNumber 子任务编号
     * @param shareUserId 分销用户id
     * @param receiveUserId 接单用户id
     * @param userId  当前登录用户id
     * @param receiveId 接单记录id
     */
    @GetMapping("/child/{taskNumber}")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.BUSY_ORDER, operType = OperaTypeEnum.INFO)
    public R<SohuBusyTaskSiteVo> getChildInfo(@NotNull(message = "愿望编号不能为空") @PathVariable String taskNumber,
                                              @RequestParam(required = false) Long shareUserId,
                                              @RequestParam(required = false) Long userId,
                                              @RequestParam(required = false) Long receiveId,
                                              @RequestParam(required = false) Long receiveUserId) {
        return R.ok(iSohuBusyTaskService.getChildInfo(taskNumber, shareUserId, receiveId, receiveUserId, userId));
    }

    /**
     * 审核--子任务：超管、国家站、城市站使用
     *
     * @return {@link R}
     */
    @PostMapping("/child/audit")
    @Log(title = "审核子任务", businessType = BusinessType.UPDATE)
    public R<Boolean> auditChild(@RequestBody SohuBusyTaskSiteBo bo) {
        return R.ok(iSohuBusyTaskService.auditChild(bo));
    }

    /**
     * 获取主任务主体详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<SohuBusyTaskVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuBusyTaskService.queryById(id));
    }

    /**
     * 新增任务主体--任务方
     */
    @Log(title = "任务主体", businessType = BusinessType.INSERT)
    @PostMapping()
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.BUSY_ORDER, operType = OperaTypeEnum.ADD)
    @RepeatSubmit(interval = 5, timeUnit = TimeUnit.SECONDS, message = "请勿重复提交")
    public R<Long> add(@Validated(AddGroup.class) @RequestBody SohuBusyTaskBo bo) {
        return R.ok(iSohuBusyTaskService.insertByBo(bo));
    }

    /**
     * 修改任务主体--校验
     */
    @GetMapping("/exit/{taskNumber}")
    public R<Boolean> exitTask(@NotNull(message = "愿望编号不能为空") @PathVariable String taskNumber) {
        return R.ok(iSohuBusyTaskService.exitChildTask(taskNumber));
    }

    /**
     * 审核任务主体--校验
     */
    @GetMapping("/audit/{taskNumber}")
    public R<Boolean> exitAuditTask(@NotNull(message = "愿望编号不能为空") @PathVariable String taskNumber, @NotNull(message = "任务状态不能为空") @RequestParam(value = "state") String state) {
        return R.ok(iSohuBusyTaskService.exitAuditTask(taskNumber, state));
    }

    /**
     * 修改任务主体
     */
    @Log(title = "任务主体", businessType = BusinessType.UPDATE)
    @PutMapping()
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.BUSY_ORDER, operType = OperaTypeEnum.UPDATE)
    public R<Long> edit(@Validated(EditGroup.class) @RequestBody SohuBusyTaskBo bo) {
        return R.ok(iSohuBusyTaskService.updateByBo(bo));
    }

    /**
     * 修改子任务主体
     */
    @PutMapping("/child")
    @Log(title = "子任务修改", businessType = BusinessType.UPDATE)
    public R<Boolean> exitChildTask(@Validated(EditGroup.class) @RequestBody SohuBusyTaskSiteBo bo) {
        return R.ok(iSohuBusyTaskService.updateByChildBo(bo));
    }

    /**
     * 删除任务主体
     *
     * @param ids 主键串
     */
    @Log(title = "任务主体", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuBusyTaskService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 审核--主任务：超管、国家站使用
     *
     * @return {@link R}
     */
    @PostMapping("/audit")
    @Log(title = "审核", businessType = BusinessType.UPDATE)
    public R<Boolean> audit(@RequestBody SohuBusyTaskBo bo) {
        return R.ok(iSohuBusyTaskService.audit(bo));
    }

    /**
     * 上下架--主任务：超管、国家站使用
     *
     * @return {@link R}
     */
    @PostMapping("/shelf")
    @Log(title = "上下架", businessType = BusinessType.UPDATE)
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.BUSY_ORDER, operType = OperaTypeEnum.OFFSHELF)
    public R<Boolean> shelf(@RequestBody SohuBusyTaskBo bo) {
        return R.ok(iSohuBusyTaskService.shelf(bo));
    }

    /**
     * 上下架--子任务：超管、国家站、城市站（三个可以强制下架）、个人上下架
     *
     * @return {@link R}
     */
    @PostMapping("/child/shelf")
    @Log(title = "上下架", businessType = BusinessType.UPDATE)
    public R<Boolean> childShelf(@RequestBody SohuBusyTaskSiteBo bo) {
        return R.ok(iSohuBusyTaskService.childShelf(bo));
    }

    /**
     * 查询商单分类
     *
     * @return
     */
    @GetMapping("/category/list")
    public R<List<CategoryVo>> categoryList() {
        return R.ok(iSohuBusyTaskService.queryCategoryList(getLang()));
    }

    /**
     * 分页查询商单申请记录
     *
     * @param receiveApplyBo
     * @param pageQuery
     * @return
     */
    @GetMapping("/receive/apply/page")
    public TableDataInfo<SohuBusyTaskReceiveApplyVo> listTaskReceiveApplyPage(SohuBusyTaskReceiveApplyBo receiveApplyBo, PageQuery pageQuery) {
        return iSohuBusyTaskService.pageTaskReceiveApplyRecord(receiveApplyBo, pageQuery);
    }

    /**
     * 待接单数量
     *
     * @param id
     * @return
     */
    @GetMapping("/wait/receive/{id}")
    public R<Long> getWaitReceiveNum(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok("操作成功", iSohuBusyTaskService.getWaitReceiveNum(id));
    }

    /**
     * 审核商单申请记录
     */
    @PostMapping("/process")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.BUSY_ORDER, operType = OperaTypeEnum.UPDATE)
    @RepeatSubmit(interval = 5, timeUnit = TimeUnit.SECONDS, message = "请勿重复提交")
    public R<Boolean> process(@Validated @RequestBody SohuBusyTaskReceiveApplyProcessBo processBo) {
        return R.ok(iSohuBusyTaskService.process(processBo)) ;
    }
}
