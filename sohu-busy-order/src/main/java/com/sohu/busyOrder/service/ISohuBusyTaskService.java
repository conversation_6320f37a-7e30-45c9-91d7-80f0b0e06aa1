package com.sohu.busyOrder.service;

import com.sohu.busyorder.api.domain.SohuBusyTaskSiteReqBo;
import com.sohu.busyorder.api.model.SohuBusyTaskSiteModel;
import com.sohu.busyorder.api.vo.*;
import com.sohu.middle.api.bo.SohuTopicContentQueryBo;
import com.sohu.busyOrder.domain.SohuBusyTask;
import com.sohu.busyorder.api.bo.*;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.vo.CategoryVo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 任务主体Service接口
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
public interface ISohuBusyTaskService {

    /**
     * 查询任务主体
     */
    SohuBusyTaskVo queryById(Long id);

    /**
     * 查询任务主体列表
     */
    List<SohuBusyTaskVo> queryList(SohuBusyTaskBo bo);

    /**
     * 查询任务主体列表
     */
    List<SohuBusyTaskVo> queryList(List<Long> ids);

    /**
     * 查询任务主体列表-根据taskNumber
     */
    List<SohuBusyTaskVo> queryListByNumberList(Collection<? extends Serializable> taskNumberList);

    /**
     * 校验主任务是否可以编辑
     *
     * @param taskNumber
     */
    Boolean exitChildTask(String taskNumber);

    /**
     * 根据主任务单号查询主任务
     *
     * @param taskNumber
     */
    SohuBusyTaskVo getByTaskNo(String taskNumber);

    /**
     * 修改任务
     *
     * @param busyTask
     * @return
     */
    Boolean updateByTask(SohuBusyTask busyTask);

    /**
     * 校验并批量删除任务主体信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 审核
     *
     * @param bo {@link SohuBusyTaskBo}
     * @return {@link Boolean}
     */
    Boolean audit(SohuBusyTaskBo bo);

    /**
     * 上下架-主任务
     *
     * @param bo {@link SohuBusyTaskBo}
     * @return {@link Boolean}
     */
    Boolean shelf(SohuBusyTaskBo bo);

    /**
     * 主任务分页列表
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuBusyTaskVo> queryPageLists(SohuBusyTaskBo bo, PageQuery pageQuery);

    /**
     * 子任务列表
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuBusyTaskSiteVo> queryPageChildLists(SohuBusyTaskSiteBo bo, PageQuery pageQuery);

    /**
     * 子任务列表-待接单
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuBusyTaskSiteVo> queryPageOfOnShelf(SohuBusyTaskSiteBo bo, PageQuery pageQuery);

    /**
     * 我的子任务订单列表
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuBusyTaskSiteVo> queryPageMyChildLists(SohuBusyTaskSiteBo bo, PageQuery pageQuery);

    /**
     * 根据主任务编号查询所有子任务
     *
     * @param masterTaskNumber
     */
    List<SohuBusyTaskSiteVo> getChildList(String masterTaskNumber);

    /**
     * 获取子任务详情
     *
     * @param taskNumber
     */
    SohuBusyTaskSiteVo getChildInfo(String taskNumber, Long shareUserId, Long receiveId, Long receiveUserId, Long userId);

    /**
     * 获取子任务详情
     *
     * @param taskNumber
     */
    SohuBusyTaskSiteVo getChildInfo(String taskNumber);

    /**
     * 审核子任务
     *
     * @param bo
     */
    Boolean auditChild(SohuBusyTaskSiteBo bo);

    /**
     * 修改子任务
     *
     * @param bo
     */
    Boolean updateByChildBo(SohuBusyTaskSiteBo bo);

    /**
     * 上下架-子任务
     *
     * @param bo
     */
    Boolean childShelf(SohuBusyTaskSiteBo bo);

    /**
     * 任务审核校验接口
     *
     * @param taskNumber
     * @param state
     */
    Boolean exitAuditTask(String taskNumber, String state);

    /**
     * 任务广场
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuBusyTaskSiteVo> queryPageTaskLists(SohuBusyTaskSiteBo bo, PageQuery pageQuery);

    /**
     * 获取用户发布任务数量统计
     *
     * @param userId
     * @return
     */
    Long getBusyTaskOfPublishStat(Long userId);

    /**
     * 获取用户昨日发布任务数量统计
     *
     * @param userId
     * @return
     */
    Long getYesterdayBusyTaskOfPublishStat(Long userId);

    /**
     * 获取用户完结任务数量统计
     *
     * @param userId
     * @return
     */
    Long getBusyTaskOfFinishStat(Long userId);

    /**
     * 获取用户昨日完结任务数量统计
     *
     * @param userId
     * @return
     */
    Long getYesterdayBusyTaskOfFinishStat(Long userId);

    /**
     * 获取任务浏览量
     *
     * @param userId
     * @return
     */
    Long getBusyTaskViewStat(Long userId, String startDate, String endDate);

    /**
     * 获取任务接单人数
     *
     * @param userId
     * @return
     */
    Long getBusyTaskWithReceiveUserStat(Long userId, String startDate, String endDate);

    /**
     * 获取接单执行数
     *
     * @param userId
     * @return
     */
    Long getBusyTaskOfExecuteStat(Long userId, String startDate, String endDate);

    /**
     * 获取任务完结数
     *
     * @param userId
     * @return
     */
    Long getBusyTaskOfFinishWithLimitDayStat(Long userId, String startDate, String endDate);

    /**
     * 获取任务总金额
     *
     * @param userId
     * @return
     */
    BigDecimal getBusyTaskAmountStat(Long userId, String startDate, String endDate);

    /**
     * 初始化内容物料数据
     *
     * @return
     */
    Boolean initAirecContentItems();

    /**
     * 数据中心
     */
    TableDataInfo<SohuBusyTaskSiteVo> queryPageTaskStateLists(SohuBusyTaskSiteBo bo, PageQuery pageQuery);

    /**
     * 站点内容数
     */
    Long queryBusyTaskNumBySite(Long siteId, Long userId);

    /**
     * 查询非拆单类型当前状态的商单数量
     *
     * @param state
     * @return
     */
    Long countByState(String state);

    /**
     * 查询非拆单类型商单数量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    Long countByTime(String startTime, String endTime);

    /**
     * 根据时间范围统计执行中销售额
     */
    BigDecimal countSalesAmountByTime(Date startTime, Date endTime);

    /**
     * 根据时间范围统计获取订单量(待接单任务)
     */
    Long countOrderByTime(Date startTime, Date endTime);

    /**
     * 根据时间范围统计接单量(执行中任务)
     */
    Long countAcceptByTime(Date startTime, Date endTime);

    /**
     * 查询商单分类
     *
     * @return
     */
    List<CategoryVo> queryCategoryList(String lang);

    /**
     * 调整商单过期时间
     *
     * @param id
     * @param deliveryTime
     */
    void updateDeliveryTime(Long id, Long deliveryTime);

    /**
     * 新增商单
     *
     * @param sohuBusyTaskBo
     * @return
     */
    Long insertByBo(SohuBusyTaskBo sohuBusyTaskBo);

    /**
     * 编辑商单
     *
     * @param bo
     * @return
     */
    Long updateByBo(SohuBusyTaskBo bo);

    /**
     * 修改商单状态
     * @param taskNumber
     * @param state
     * @return
     */
    Boolean updateTaskState(String taskNumber, String state);

    /**
     * 根据子任务编号绑定IM进群用户标签
     *
     * @param masterTaskNumber 子任务编号
     * @Param userId 绑定用户id
     */
    void insertUserLabelByTaskNumber(String masterTaskNumber, Long userId);

    /**
     * 查询流量商单截止时间内的任务
     * @return
     */
    List<SohuBusyTaskVo> selectFlowBusyTaskOfState();

    /**
     * 根据主任务编号获取主任详情
     *
     * @param masterTaskNumber 主任务编号
     * @return SohuBusyTaskVo
     */
    SohuBusyTaskVo getMasterInfo(@NotNull(message = "主任务编号不能为空") String masterTaskNumber);
    /**
     * 调整排序和生效时间
     *
     * @param bo SohuBusyTaskSortBo
     * @return Boolean
     */
    Boolean updateSortAndEffectiveTimeById(SohuBusyTaskSortBo bo);

    /**
     * 调整置顶商单任务
     */
    void resetTopBusyTaskHandler();

    /**
     * 获取所有商单列表
     *
     * @param bo SohuBusyTaskAllListBo
     * @return TableDataInfo<SohuBusyTaskAllListVo>
     */
    TableDataInfo<SohuBusyTaskAllListVo> getAllListWithRole(SohuBusyTaskAllListBo bo, PageQuery pageQuery);

    /**
     * 待接单数量
     *
     * @param id
     * @return
     */
    Long getWaitReceiveNum(Long id);

    /**
     * 分页查询商单申请记录
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuBusyTaskReceiveApplyVo> pageTaskReceiveApplyRecord(SohuBusyTaskReceiveApplyBo bo, PageQuery pageQuery);

    /**
     * 审核商单申请记录
     *
     * @param processBo
     * @return
     */
    Boolean process(SohuBusyTaskReceiveApplyProcessBo processBo);

    /**
     * 结算商单
     *
     * @param taskNumber
     * @return
     */
    Boolean settleBusyTask(String taskNumber);

    /**
     * 结算订单(管理员审核)
     *
     * @param taskNumber
     * @param rejectReason
     * @return
     */
    Boolean applySettleBusyTask(String taskNumber, String rejectReason);

    /**
     * 更新达标数量
     *
     * @param busyTask
     */
    void updatePassNum(SohuBusyTask busyTask);

    /**
     * 处理达标数量
     *
     * @param taskNumber
     */
    void handleFlowBusyTaskPassNum(String taskNumber);

    /**
     * 根据主任务编号获取接单审核通过时间或接单时间
     *
     * @param masterTaskNumber 主单编号
     * @return Date
     */
    Date getTaskTimeWithMasterTaskNumber(String masterTaskNumber);

    /**
     * 查询专题内容列表
     */
    TableDataInfo<SohuBusyTaskSiteVo> getTopicList(SohuTopicContentQueryBo bo, PageQuery pageQuery);

    /**
     * 分页查询置顶数据
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuBusyTaskSiteVo> queryPageTopOfTaskSite(SohuBusyTaskSiteReqBo bo, PageQuery pageQuery);

    /**
     * 获取发单分组列表
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<TaskGroupVo> getSendTaskGroupList(Date startTime, Date endTime);
}
