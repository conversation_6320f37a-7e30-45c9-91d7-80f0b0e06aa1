package com.sohu.busyOrder.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.sohu.busyOrder.domain.*;
import com.sohu.busyOrder.mapper.*;
import com.sohu.busyOrder.service.ISohuBusyOrderBizService;
import com.sohu.busyOrder.service.ISohuBusyTaskSiteService;
import com.sohu.busyorder.api.enums.ReceiveStatus;
import com.sohu.busyorder.api.model.SohuBusyOrderModel;
import com.sohu.busyorder.api.model.SohuBusyOrderPayModel;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.utils.BigDecimalUtils;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.middle.api.bo.SohuBillRecordBo;
import com.sohu.middle.api.service.RemoteMiddleBillRecordService;
import com.sohu.middle.api.service.RemoteMiddleCurrencyService;
import com.sohu.middle.api.vo.SohuCurrencyVo;
import com.sohu.middle.api.vo.YiMaPayConfig;
import com.sohu.system.api.RemoteDictService;
import com.sohu.system.api.domain.SysDictData;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverse;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverseRequest;
import com.wangcaio2o.ipossa.sdk.response.barcodereverse.BarcodeReverseResponse;
import com.wangcaio2o.ipossa.sdk.test.Client;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 商单综合业务服务接口
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SohuBusyOrderBizServiceImpl implements ISohuBusyOrderBizService {

    private final SohuBusyOrderMapper sohuBusyOrderMapper;
    @DubboReference
    private final RemoteMiddleCurrencyService currencyService;
    private final SohuBusyOrderReceMapper sohuBusyOrderReceMapper;
    private final SohuBusyTaskMapper sohuBusyTaskMapper;
    private final SohuBusyTaskReceiveMapper sohuBusyTaskReceiveMapper;
    private final SohuBusyTaskSiteMapper sohuBusyTaskSiteMapper;
    private final ISohuBusyTaskSiteService busyTaskSiteService;
    private final SohuBusyOrderPayMapper sohuBusyOrderPayMapper;
    @DubboReference
    private final RemoteMiddleBillRecordService billRecordService;
    @DubboReference
    private RemoteDictService remoteDictService;

    @Override
    public Boolean mqRefundBusyOrder(SohuBusyOrderPayModel sohuBusyOrderPayModel) {
        //SohuBusyOrderModel sohuBusyOrderModel = remoteBusyOrderService.queryBusyOrder(1L);
        SohuBusyOrderModel sohuBusyOrderModel = this.queryBusyOrder(1L);
        if (sohuBusyOrderModel != null && StrUtil.equalsAnyIgnoreCase(sohuBusyOrderModel.getBusyOrderStatus(), BusyOrderStatus.Finished.name())) {
            return Boolean.TRUE;
        }
        // 商单接单人数上限
        Integer applyMax = sohuBusyOrderModel.getApplyMax();
        // todo
//        Long count = remoteBusyOrderService.receCount(busyOrder);
        Long count = this.receCount(1L);

        if (count != null && applyMax != null && count.intValue() == applyMax.intValue()) {

            if (sohuBusyOrderModel.getPrepayAmount() != null && CalUtils.isZero(sohuBusyOrderModel.getPrepayAmount())
                    && StrUtil.equalsAnyIgnoreCase(sohuBusyOrderModel.getPrepayState(), PayStatus.Paid.name())) {
                this.updateBusyOrderAndRece(sohuBusyOrderModel.getId(), true);
                return Boolean.TRUE;
            }
            // todo
//            SohuBusyOrderPayModel busyOrderPayModel = remoteBusyOrderService.queryBusyOrderPay(busyOrder,
//                    PayObject.BusyOrder.name(), PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.Paid.name());
            SohuBusyOrderPayModel busyOrderPayModel = this.queryBusyOrderPay("1L",
                    PayObject.BusyOrder.name(), PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.Paid.name());

            // 查询翼码支付配置
            YiMaPayConfig yiMaPayConfig = getYiMaPayConfig();
            // 组装微信小程序退款请求参数
            BarcodeReverseRequest request = new BarcodeReverseRequest();
            request.setPosId(yiMaPayConfig.getPosId());
            request.setIsspid(yiMaPayConfig.getIssPid());
            request.setSystemId(yiMaPayConfig.getSystemId());
            request.setStoreId(yiMaPayConfig.getStoreId());
            // 退款参数封装
            BarcodeReverse reverse = new BarcodeReverse();
            reverse.setPayType(yiMaPayConfig.getPayType());

            request.setPosSeq("R" + System.nanoTime());
            // 支付请求流水号
            reverse.setOrgPosSeq(busyOrderPayModel.getOrderNo());
            reverse.setTxAmt(BigDecimalUtils.yuanToFen(busyOrderPayModel.getPayAmount()));
            request.setBarcodeReverseRequest(reverse);
            log.info("商单预付款退款请求:{}", JSONUtil.toJsonStr(request));

            BarcodeReverseResponse response = Client.getClient().execute(request);
            List<String> resultList = com.google.common.collect.Lists.newArrayList("9998", "0000");
            if (ObjectUtils.isNull(response) || !resultList.contains(response.getResult().getId())) {
                log.error("商单预付款退款异常：{}", JSONUtil.toJsonStr(response));
                throw new RuntimeException(response.getResult().getComment());
            }
            this.savePrepaymentRefundBillRecord(sohuBusyOrderPayModel);
            // 更新商单状态完成，且正在合作中的接单改为终止
            this.updateBusyOrderAndRece(sohuBusyOrderModel.getId(), true);
            return Boolean.TRUE;
        }
        return Boolean.TRUE;
    }

    /**
     * 查询商单详情
     *
     * @param orderId
     * @return
     */
    private SohuBusyOrderModel queryBusyOrder(Long orderId) {
        SohuBusyOrder sohuBusyOrder = sohuBusyOrderMapper.selectById(orderId);
        if (Objects.isNull(sohuBusyOrder)) {
            return null;
        }
        SohuBusyOrderModel orderModel = BeanCopyUtils.copy(sohuBusyOrder, SohuBusyOrderModel.class);
        if (sohuBusyOrder.getPrepayCurrency() != null && sohuBusyOrder.getPrepayCurrency() > 0L) {
            SohuCurrencyVo prepayCurrency = currencyService.selectById(sohuBusyOrder.getPrepayCurrency());
            if (Objects.nonNull(prepayCurrency)) {
                orderModel.setPrepayCurrencyName(prepayCurrency.getName());
            }
        }
        if (sohuBusyOrder.getFullCurrency() != null && sohuBusyOrder.getFullCurrency() > 0L) {
            SohuCurrencyVo fullCurrency = currencyService.selectById(sohuBusyOrder.getFullCurrency());
            if (Objects.nonNull(fullCurrency)) {
                orderModel.setFullCurrencyName(fullCurrency.getName());
            }
        }

        return orderModel;
    }

    /**
     * 已接单人数
     *
     * @param orderId
     * @return
     */
    private Long receCount(Long orderId) {
        return sohuBusyOrderReceMapper.selectCount(SohuBusyOrderRece::getOrderId, orderId, SohuBusyOrderRece::getReceiveStatus, Arrays.asList(ReceiveStatus.Finished.name()));
    }

    /**
     * 更新商单状态完成，且正在合作中的接单改为终止
     *
     * @param orderId
     * @param isBusyOrder
     */
    private void updateBusyOrderAndRece(Long orderId, Boolean isBusyOrder) {
        SohuBusyTask sohuBusyTask = null;
        if (isBusyOrder) {
            sohuBusyTask = sohuBusyTaskMapper.selectById(orderId);
        } else {
            SohuBusyTaskReceive busyTaskReceive = sohuBusyTaskReceiveMapper.selectById(orderId);
            SohuBusyTaskSite busyTaskSite = sohuBusyTaskSiteMapper.selectOne(SohuBusyTaskSite::getTaskNumber, busyTaskReceive.getTaskNumber());
            sohuBusyTask = sohuBusyTaskMapper.selectOne(SohuBusyTask::getTaskNumber, busyTaskSite.getMasterTaskNumber());
        }
        sohuBusyTask.setState(SohuBusyTaskState.Over.name());
        sohuBusyTaskMapper.updateById(sohuBusyTask);
        sohuBusyOrderReceMapper.updateReceStatus(orderId, ReceiveStatus.End.name(), ReceiveStatus.Cooperating.name());
        //log.info("更新商单状态完成,商单id:{}", orderId);
    }

    /**
     * 查询商单付款记录
     *
     * @param busyOrderId 商单id
     * @return
     */
    private SohuBusyOrderPayModel queryBusyOrderPay(String busyOrderId, String busyType, String payType, String payStatus) {
        LambdaQueryWrapper<SohuBusyOrderPay> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuBusyOrderPay::getPayType, payType).eq(SohuBusyOrderPay::getPayStatus, payStatus);
        lqw.eq(SohuBusyOrderPay::getBusyType, busyType).eq(SohuBusyOrderPay::getBusyOrder, busyOrderId);
        SohuBusyOrderPay sohuBusyOrderPay = sohuBusyOrderPayMapper.selectOne(lqw);
        return Objects.nonNull(sohuBusyOrderPay) ? BeanCopyUtils.copy(sohuBusyOrderPay, SohuBusyOrderPayModel.class) : null;
    }

    /**
     * 获取翼码支付配置
     *
     * @return
     */
    protected YiMaPayConfig getYiMaPayConfig() {
        SysDictData dictData = remoteDictService.getDictData(DictEnum.payConfig.getKey(), DictEnum.YMPayConfig.getKey());
        Objects.requireNonNull(dictData, "翼码支付配置为空");
        String dictValue = dictData.getDictValue();
        YiMaPayConfig bean = JSONUtil.toBean(dictValue, YiMaPayConfig.class);
//        bean.setNotifyUrl("https://api-pre.sohuglobal.com/pay/pay/callback/yima");
        return bean;
    }

    /**
     * 保存商单预付款退款记录
     *
     * @param sohuBusyOrderPayModel
     */
    private void savePrepaymentRefundBillRecord(SohuBusyOrderPayModel sohuBusyOrderPayModel) {
        SohuBusyTaskSite busyTaskSite = busyTaskSiteService.getByNo(sohuBusyOrderPayModel.getBusyOrder());
        // 接单方
        SohuBillRecordBo billRecord = new SohuBillRecordBo();
        billRecord.setUserId(busyTaskSite.getUserId());
        billRecord.setPayerId(0L);
        billRecord.setPayeeId(busyTaskSite.getUserId());
        billRecord.setTitle("商单预付款退款");
        billRecord.setAmount(sohuBusyOrderPayModel.getPayableAmount());
        billRecord.setTransactionType(SohuTransactionTypeEnum.PrepaymentRefund.name());
        billRecord.setState(PayStatus.Paid.name());
        billRecord.setBusyCode(busyTaskSite.getId());
        billRecord.setBusyType(PayObject.BusyOrder.name());
        billRecord.setPayType(sohuBusyOrderPayModel.getPayType());
        billRecord.setBillType(SohuBillType.Prepayment.name());
        billRecordService.savePrepaymentRefundBillRecord(billRecord);
    }

}
