package com.sohu.busyOrder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.busyOrder.base.SohuTaskProcessor;
import com.sohu.busyOrder.domain.*;
import com.sohu.busyOrder.mapper.*;
import com.sohu.busyOrder.service.ISohuBusyTaskNoticeService;
import com.sohu.busyOrder.service.ISohuBusyTaskPayService;
import com.sohu.busyOrder.service.ISohuBusyTaskReceiveService;
import com.sohu.busyOrder.utils.BusyTaskUtil;
import com.sohu.busyorder.api.bo.SohuBusyTaskReceiveBo;
import com.sohu.busyorder.api.enums.BusyTaskTypeEnum;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;
import com.sohu.busyorder.api.vo.*;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.enums.ImGroupType;
import com.sohu.im.api.service.RemoteImGroupOrderUserService;
import com.sohu.im.api.service.RemoteImService;
import com.sohu.im.api.vo.SohuImGroupVo;
import com.sohu.middle.api.service.RemoteMiddleAuditService;
import com.sohu.middle.api.service.RemoteMiddleCategoryService;
import com.sohu.middle.api.service.airec.RemoteMiddleAirecContentItemService;
import com.sohu.middle.api.vo.SohuAuditVo;
import com.sohu.middle.api.vo.SohuCategoryVo;
import com.sohu.pay.api.RemotePayService;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.enums.PaySceceTypeEnum;
import com.sohu.third.aliyun.airec.enums.AliyunAirecContentItemTypeEnum;
import io.seata.common.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商单接单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuBusyTaskReceiveServiceImpl implements ISohuBusyTaskReceiveService {

    private final SohuBusyTaskReceiveMapper baseMapper;
    private final SohuBusyTaskMapper sohuBusyTaskMapper;
    private final SohuBusyTaskSiteMapper busyTaskSiteMapper;
    private final SohuBusyTaskDeliveryMapper busyTaskDeliveryMapper;
    private final SohuBusyTaskTemplateNumberMapper busyTaskTemplateNumberMapper;
    private final TransactionTemplate transactionTemplate;
    private final SohuBusyTaskWinMcnMapper sohuBusyTaskWinMcnMapper;
    private final AsyncConfig asyncConfig;
    private final ISohuBusyTaskNoticeService sohuBusyTaskNoticeService;
    private final ISohuBusyTaskPayService sohuBusyTaskPayService;

    @Resource
    private SohuTaskProcessor sohuTaskProcessor;
    @DubboReference
    private RemoteMiddleCategoryService remoteMiddleCategoryService;
    @DubboReference
    private final RemoteMiddleAirecContentItemService remoteMiddleAirecContentItemService;
    @DubboReference
    private RemotePayService remotePayService;
    @DubboReference
    private RemoteImGroupOrderUserService remoteImGroupOrderUserService;
    @DubboReference
    private RemoteImService remoteImService;
    @DubboReference
    private RemoteMiddleAuditService remoteMiddleAuditService;
    //    @DubboReference
//    private RemoteSohuAirecContentItemService remoteSohuAirecContentItemService;
    // 商单过期时间
    private static final Duration EXPIRE_DURATION = Duration.ofHours(7);

    /**
     * 查询商单接单
     */
    @Override
    public SohuBusyTaskReceiveVo queryById(Long id) {
        SohuBusyTaskReceiveVo sohuBusyTaskReceiveVo = baseMapper.selectVoById(id);
        if (Objects.isNull(sohuBusyTaskReceiveVo)) {
            return new SohuBusyTaskReceiveVo();
        }
        SohuBusyTaskSiteVo sohuBusyTaskSiteVo = busyTaskSiteMapper.queryByTaskNumber(sohuBusyTaskReceiveVo.getTaskNumber(), null);
        BeanUtil.copyProperties(sohuBusyTaskSiteVo, sohuBusyTaskReceiveVo, "state", "refuseMsg");

        return sohuBusyTaskReceiveVo;
    }

    /**
     * 查询任务方申请接单列表
     */
    @Override
    public TableDataInfo<SohuBusyTaskReceiveVo> queryPageList(SohuBusyTaskReceiveBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Objects.requireNonNull(loginUser, MessageUtils.message("WRONG_PARAMS"));
        bo.setTaskUserId(loginUser.getUserId());
        // 时间转换
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            DateTime dateTime = DateUtil.beginOfDay(DateUtil.parseDate(bo.getStartTime()));
            bo.setCreateTime(dateTime);
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            DateTime dateTime = DateUtil.endOfDay(DateUtil.parseDate(bo.getEndTime()));
            bo.setUpdateTime(dateTime);
        }
        Page<SohuBusyTaskReceiveVo> result = baseMapper.selectTaskReceivePage(PageQueryUtils.build(pageQuery), bo);
        if (CollectionUtils.isNotEmpty(result.getRecords())) {
            for (SohuBusyTaskReceiveVo record : result.getRecords()) {
                SohuBusyTaskVo sohuBusyTaskVo = sohuBusyTaskMapper.selectVoOne(Wrappers.<SohuBusyTask>lambdaQuery()
                        .eq(SohuBusyTask::getTaskNumber, record.getMasterTaskNumber()));
                if (Objects.nonNull(sohuBusyTaskVo)) {
                    SohuBusyTaskPayVo payVo = sohuBusyTaskPayService.queryByPayScene(sohuBusyTaskVo.getTaskNumber(), PayStatus.Paid.name(), PaySceceTypeEnum.BUSY_ORDER_PAY.getCode());
                    if (Objects.nonNull(payVo)) {
                        record.setPayAmount(payVo.getPayAmount());
                    }
                }
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public TableDataInfo<SohuBusyTaskReceiveVo> selectTaskReceiveUserPage(SohuBusyTaskReceiveBo bo, PageQuery pageQuery) {
        /* LoginUser loginUser = LoginHelper.getLoginUser(); */
        Long userId = LoginHelper.getUserId();
        if (userId == null) {
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
        bo.setUserId(userId);
        // 时间转换
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            DateTime dateTime = DateUtil.beginOfDay(DateUtil.parseDate(bo.getStartTime()));
            bo.setCreateTime(dateTime);
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            DateTime dateTime = DateUtil.endOfDay(DateUtil.parseDate(bo.getEndTime()));
            bo.setUpdateTime(dateTime);
        }
        Page<SohuBusyTaskReceiveVo> result = baseMapper.selectTaskReceiveUserPage(PageQueryUtils.build(pageQuery), bo);
        if (CollectionUtils.isNotEmpty(result.getRecords())) {
            for (SohuBusyTaskReceiveVo record : result.getRecords()) {
                SohuBusyTaskVo sohuBusyTaskVo = sohuBusyTaskMapper.selectVoOne(Wrappers.<SohuBusyTask>lambdaQuery()
                        .eq(SohuBusyTask::getTaskNumber, record.getMasterTaskNumber()));
                if (Objects.nonNull(sohuBusyTaskVo)) {
                    record.setDeliveryDay(sohuBusyTaskVo.getDeliveryDay());
                    record.setTimeUnit(sohuBusyTaskVo.getTimeUnit());
                    record.setExpiryTime(BusyTaskUtil.addTimeToCurrentTime(record.getDeliveryDay(), record.getTimeUnit(), record.getCreateTime()));
                    record.setBusinessId(sohuBusyTaskVo.getVideoUrl());
                    record.setImageUrl(sohuBusyTaskVo.getImageUrl());
                    record.setBusyTaskId(sohuBusyTaskVo.getId());
                    record.setSingleAmount(sohuBusyTaskVo.getSingleAmount());
                    SohuBusyTaskPayVo payVo = sohuBusyTaskPayService.queryByPayScene(sohuBusyTaskVo.getTaskNumber(), PayStatus.Paid.name(), PaySceceTypeEnum.BUSY_ORDER_PAY.getCode());
                    if (Objects.nonNull(payVo)) {
                        record.setPayAmount(payVo.getPayAmount());
                    }
                }
                if (record.getState().equals(SohuBusyTaskState.Execute.name())) {
                    // 查询最近的一条审核记录
                    SohuAuditVo sohuAuditVo = remoteMiddleAuditService.selectNearByObj(record.getId(), BusyType.SettleBusyTask.name(), record.getUserId());
                    if (Objects.nonNull(sohuAuditVo)) {
                        SohuBusyTaskReceiveAuditVo auditVo = new SohuBusyTaskReceiveAuditVo();
                        auditVo.setAuditTime(sohuAuditVo.getAuditTime());
                        auditVo.setAuditorName(sohuAuditVo.getAuditorName());
                        auditVo.setRejectReason(sohuAuditVo.getRejectReason());
                        record.setAudit(auditVo);
                    }
                }
                if (record.getState().equals(SohuBusyTaskState.Refuse.name())) {
                    // 通过接单表获取主任务id
                    SohuBusyTaskVo vo = sohuBusyTaskMapper.selectVoOne(Wrappers.<SohuBusyTask>lambdaQuery().eq(SohuBusyTask::getTaskNumber, record.getMasterTaskNumber()));
                    if (Objects.nonNull(vo)) {
                        // 查询最近的一条审核记录
                        SohuAuditVo sohuAuditVo = remoteMiddleAuditService.selectNearByObj(vo.getId(), BusyType.ReceiveBusyTask.name(), record.getUserId());
                        if (Objects.nonNull(sohuAuditVo)) {
                            SohuBusyTaskReceiveAuditVo auditVo = new SohuBusyTaskReceiveAuditVo();
                            auditVo.setAuditTime(sohuAuditVo.getAuditTime());
                            auditVo.setAuditorName(sohuAuditVo.getAuditorName());
                            auditVo.setRejectReason(sohuAuditVo.getRejectReason());
                            record.setAudit(auditVo);
                        }
                    }

                }
                record.setPassPersonNum(remoteImGroupOrderUserService.getPassPersonUserByTaskNumber(record.getTaskNumber()).longValue());
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public List<SohuBusyTaskReceiveVo> queryByReceive(String taskNumber) {
        return this.baseMapper.selectReceiveAndInfo(taskNumber, Boolean.TRUE);
    }

    /**
     * 查询商单接单列表
     */
    @Override
    public List<SohuBusyTaskReceiveVo> queryList(SohuBusyTaskReceiveBo bo) {
        LambdaQueryWrapper<SohuBusyTaskReceive> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuBusyTaskReceive> buildQueryWrapper(SohuBusyTaskReceiveBo bo) {
        LambdaQueryWrapper<SohuBusyTaskReceive> lqw = Wrappers.lambdaQuery();
        // todo
        lqw.eq(bo.getTaskNumber() != null, SohuBusyTaskReceive::getTaskNumber, bo.getTaskNumber());
        lqw.eq(bo.getUserId() != null, SohuBusyTaskReceive::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyMsg()), SohuBusyTaskReceive::getApplyMsg, bo.getApplyMsg());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyAnnex()), SohuBusyTaskReceive::getApplyAnnex, bo.getApplyAnnex());
        lqw.eq(bo.getAmount() != null, SohuBusyTaskReceive::getAmount, bo.getAmount());
        lqw.eq(bo.getTemplateId() != null, SohuBusyTaskReceive::getTemplateId, bo.getTemplateId());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuBusyTaskReceive::getState, bo.getState());
        lqw.eq(StringUtils.isNotBlank(bo.getRefuseMsg()), SohuBusyTaskReceive::getRefuseMsg, bo.getRefuseMsg());
        return lqw;
    }

    /**
     * 修改商单接单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(SohuBusyTaskReceiveBo bo) {
        SohuBusyTaskReceive update = BeanUtil.toBean(bo, SohuBusyTaskReceive.class);
        validEntityBeforeSave(update);
        // 当前子单
        LambdaQueryWrapper<SohuBusyTaskSite> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuBusyTaskSite::getTaskNumber, bo.getTaskNumber());
        SohuBusyTaskSite busyTaskSite = busyTaskSiteMapper.selectOne(lqw);
        if (bo.getState().equals(SohuBusyTaskState.Pass.name())) {
            busyTaskSite.setState(SohuBusyTaskState.Execute.name());
            //智能推荐更新状态
            this.updateAirecContentItemStatusToOffShelf(busyTaskSite);
            // 其他子单
            LambdaQueryWrapper<SohuBusyTaskSite> otherLqw = Wrappers.lambdaQuery();
            otherLqw.eq(SohuBusyTaskSite::getMasterTaskNumber, busyTaskSite.getMasterTaskNumber()).ne(SohuBusyTaskSite::getId, busyTaskSite.getId());
            List<SohuBusyTaskSite> busyTaskSiteList = busyTaskSiteMapper.selectList(otherLqw);
            if (CollectionUtils.isNotEmpty(busyTaskSiteList)) {
                for (SohuBusyTaskSite taskSite : busyTaskSiteList) {
                    taskSite.setState(SohuBusyTaskState.Over.name());
                    taskSite.setShelfState(SohuBusyTaskState.OffShelf.name());
                    //智能推荐更新状态
                    this.updateAirecContentItemStatusToOffShelf(busyTaskSite);
                }
            }
            // 主单
            LambdaQueryWrapper<SohuBusyTask> masterLqw = Wrappers.lambdaQuery();
            masterLqw.eq(SohuBusyTask::getTaskNumber, busyTaskSite.getMasterTaskNumber());
            SohuBusyTask busyTask = sohuBusyTaskMapper.selectOne(masterLqw);
            busyTask.setState(SohuBusyTaskState.Execute.name());
            // 阶段性任务关联子任务单号
//            LambdaQueryWrapper<SohuBusyTaskDelivery> deLqw = Wrappers.lambdaQuery();
//            deLqw.eq(SohuBusyTaskDelivery::getMasterTaskNumber, busyTaskSite.getMasterTaskNumber());
            return transactionTemplate.execute(e -> {
                // 修改接单状态
                this.baseMapper.updateById(update);
                // 修改接单子任务状态
                busyTaskSiteMapper.updateById(busyTaskSite);
                if (CollectionUtils.isNotEmpty(busyTaskSiteList)) {
                    // 修改其他子任务状态
                    busyTaskSiteMapper.updateBatchById(busyTaskSiteList);
                }
                // 修改主任务状态
                sohuBusyTaskMapper.updateById(busyTask);
//                // 阶段性任务关联子任务单号
//                if (busyTaskDeliveryMapper.selectCount(deLqw) > 0) {
//                    LambdaUpdateWrapper<SohuBusyTaskDelivery> luw = Wrappers.lambdaUpdate();
//                    luw.eq(SohuBusyTaskDelivery::getMasterTaskNumber, busyTaskSite.getMasterTaskNumber());
//                    luw.set(SohuBusyTaskDelivery::getTaskNumber, busyTaskSite.getTaskNumber());
//                    busyTaskDeliveryMapper.update(new SohuBusyTaskDelivery(), luw);
//                }
                //更新mcn任务库状态为已接单
                this.updateTaskWindowMcnState(update);
                sohuBusyTaskNoticeService.sendTaskSiteNotice(bo.getTaskNumber(), TaskNoticeEnum.TASK_APPLY_SUCCESS,
                        bo.getUserId(), null, null, Boolean.FALSE, null, null);
                return Boolean.TRUE;
            });
        } else {
            // 发送接单方申请驳回消息
            sohuBusyTaskNoticeService.sendTaskSiteNotice(bo.getTaskNumber(), TaskNoticeEnum.TASK_APPLY_REFUSE,
                    bo.getUserId(), bo.getRefuseMsg(), null, Boolean.TRUE, null, null);
            return this.baseMapper.updateById(update) > 0;
        }

    }

    /**
     * 更新mcn任务库状态为已接单
     *
     * @param entity
     */
    private void updateTaskWindowMcnState(SohuBusyTaskReceive entity) {
        LambdaUpdateWrapper<SohuBusyTaskWinMcn> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuBusyTaskWinMcn::getTaskNumber, entity.getTaskNumber())
                .eq(SohuBusyTaskWinMcn::getMcnId, entity.getMcnId())
                .set(SohuBusyTaskWinMcn::getReceiveState, true);
        this.sohuBusyTaskWinMcnMapper.update(new SohuBusyTaskWinMcn(), luw);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuBusyTaskReceive entity) {
        // 查询是否有这个任务
        LambdaQueryWrapper<SohuBusyTaskSite> taskLqw = Wrappers.lambdaQuery();
        Long busyTaskCount = busyTaskSiteMapper.selectCount(taskLqw.eq(SohuBusyTaskSite::getTaskNumber, entity.getTaskNumber()));
        if (busyTaskCount < 1L) {
            throw new ServiceException(MessageUtils.message("wrong_info"));
        }
        // 拆单模板ID
        Long templateId = entity.getTemplateId();
        if (templateId != null && templateId > 0L) {
            SohuBusyTaskTemplateNumber taskTemplate = busyTaskTemplateNumberMapper.selectById(templateId);
            Objects.requireNonNull(taskTemplate, MessageUtils.message("wrong_info"));
        }
    }

    /**
     * 批量删除商单接单
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public SohuBusyTaskReceive queryPass(Long busyTaskId) {
        // todo
        return this.baseMapper.selectOne(SohuBusyTaskReceive::getTaskNumber, busyTaskId, SohuBusyTaskReceive::getState, SohuBusyTaskState.Pass.name());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean audit(SohuBusyTaskReceiveBo bo) {
        Long userId = LoginHelper.getUserId();
        Objects.requireNonNull(userId, "未登录");
        SohuBusyTaskReceive taskReceive = this.baseMapper.selectById(bo.getId());
        if (Objects.isNull(taskReceive)) {
            throw new ServiceException(MessageUtils.message("当前接单记录不存在,请确认后再试"));
        }
        if (StrUtil.isBlankIfStr(bo.getState())) {
            throw new ServiceException(MessageUtils.message("审核状态不能为空"));
        }
        SohuBusyTaskSite sohuBusyTaskSite = busyTaskSiteMapper.selectOne(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getTaskNumber, taskReceive.getTaskNumber()));
        if (Objects.isNull(sohuBusyTaskSite)) {
            throw new ServiceException(MessageUtils.message("当前任务不存在,请确认后再试"));
        }
        SohuBusyTaskVo sohuBusyTaskVo = sohuBusyTaskMapper.selectVoOne(Wrappers.<SohuBusyTask>lambdaQuery()
                .eq(SohuBusyTask::getTaskNumber, sohuBusyTaskSite.getMasterTaskNumber()));
        if (Objects.isNull(sohuBusyTaskVo)) {
            return Boolean.FALSE;
        }
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(sohuBusyTaskVo.getType());
        if (Objects.isNull(sohuCategoryVo)) {
            return Boolean.FALSE;
        }
        taskReceive.setState(bo.getState());
        taskReceive.setRefuseMsg(bo.getRefuseMsg());
        return sohuTaskProcessor.getStrategy(sohuCategoryVo.getConstMark())
                .auditReceiveTask(taskReceive, sohuBusyTaskVo, sohuBusyTaskSite);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String pay(SohuBusyTaskReceiveBo bo) {
        SohuBusyTaskReceive busyTaskReceive = this.baseMapper.selectById(bo.getId());
        if (Objects.isNull(busyTaskReceive)) {
            throw new ServiceException(MessageUtils.message("wrong_info"));
        }
        // 阶段性任务关联子任务单号
        LambdaQueryWrapper<SohuBusyTaskDelivery> deLqw = Wrappers.lambdaQuery();
        deLqw.eq(SohuBusyTaskDelivery::getMasterTaskNumber, bo.getMasterTaskNumber());
        // 阶段性任务关联子任务单号
        if (busyTaskDeliveryMapper.selectCount(deLqw) > 0) {
            LambdaUpdateWrapper<SohuBusyTaskDelivery> luw = Wrappers.lambdaUpdate();
            luw.eq(SohuBusyTaskDelivery::getMasterTaskNumber, bo.getMasterTaskNumber());
            luw.set(SohuBusyTaskDelivery::getTaskNumber, bo.getTaskNumber());
            busyTaskDeliveryMapper.update(new SohuBusyTaskDelivery(), luw);
        }
        //如果是mcn机构接单 需要修改mcn接单状态
        LambdaQueryWrapper<SohuBusyTaskWinMcn> mcnLqw = Wrappers.lambdaQuery();
        mcnLqw.eq(SohuBusyTaskWinMcn::getTaskNumber, bo.getTaskNumber());
        // 阶段性任务关联子任务单号
        if (sohuBusyTaskWinMcnMapper.selectCount(mcnLqw) > 0) {
            LambdaUpdateWrapper<SohuBusyTaskWinMcn> muw = Wrappers.lambdaUpdate();
            muw.eq(SohuBusyTaskWinMcn::getTaskNumber, bo.getTaskNumber());
            muw.set(SohuBusyTaskWinMcn::getReceiveState, 1);
            sohuBusyTaskWinMcnMapper.update(new SohuBusyTaskWinMcn(), muw);
        }
        SohuPrePayBo prePayBo = new SohuPrePayBo();
        prePayBo.setPayType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus());
        prePayBo.setAmount(busyTaskReceive.getAmount());
        // PC还是mobile渠道支付
        if (StrUtil.isNotBlank(bo.getPayChannel()) && StrUtil.equalsAnyIgnoreCase(bo.getPayChannel(), Constants.CHANNEL_MOBILE)) {
            prePayBo.setPayChannel(Constants.CHANNEL_MOBILE);
        } else {
            prePayBo.setPayChannel(Constants.CHANNEL_PC);
        }
        prePayBo.setMasterOrderNo(busyTaskReceive.getTaskNumber());
        prePayBo.setMasterId(busyTaskReceive.getId());
        prePayBo.setPaySource(PaySourceEnum.BUSY_TASK_PROMISE_PAY.getCode());
        return remotePayService.payment(prePayBo);
    }

    /**
     * 智能推荐更新状态为不推荐
     *
     * @param busyTaskSite
     * @returnT
     */
    private void updateAirecContentItemStatusToOffShelf(SohuBusyTaskSite busyTaskSite) {
        if ((Objects.equals(SohuBusyTaskState.Execute.name(), busyTaskSite.getShelfState())
                || Objects.equals(SohuBusyTaskState.Over.name(), busyTaskSite.getShelfState()))
                && (!busyTaskSite.getReceiveLimit())) {
            remoteMiddleAirecContentItemService.updateStatusToOffShelf(busyTaskSite.getId().toString(), AliyunAirecContentItemTypeEnum.ITEM.getCode());
        }
    }

    @Override
    public Long getReceiveUserStat(Long userId, DateTime startDateTime, DateTime endDateTime) {
        return baseMapper.getReceiveUserStat(userId, startDateTime, endDateTime);
    }

    @Override
    public List<SohuBusyTaskReceiveVo> getExecuteBusyTaskByUserId(Long userId) {
        return baseMapper.getExecuteBusyTaskByUserId(userId);
    }

    @Override
    public SohuBusyTaskReceiveVo getInfoById(String id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public SohuBusyTaskReceiveVo getReceiveDetailByTaskNumber(String taskNumber) {
        LambdaQueryWrapper<SohuBusyTaskReceive> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuBusyTaskReceive::getTaskNumber, taskNumber);
        wrapper.eq(SohuBusyTaskReceive::getUserId, LoginHelper.getUserId());
        wrapper.notIn(SohuBusyTaskReceive::getState, SohuBusyTaskState.Cancel.name());
        wrapper.last("limit 1");
        return baseMapper.selectVoOne(wrapper);
    }

    @Override
    public Long countByState(String state) {
        return baseMapper.selectCount(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                .eq(SohuBusyTaskReceive::getState, state)
                .eq(SohuBusyTaskReceive::getUserId, LoginHelper.getUserId()));
    }

    @Override
    public void updateExpireTime(Long id, Date expireTime) {
        SohuBusyTaskReceive receive = new SohuBusyTaskReceive();
        receive.setId(id);
        receive.setExpireTime(expireTime);
        baseMapper.updateById(receive);
    }

    @Override
    public Boolean receiveTask(SohuBusyTaskReceiveBo bo) {
        Long userId = LoginHelper.getUserId();
        Objects.requireNonNull(userId, "未登录");
        SohuBusyTaskSite sohuBusyTaskSite = busyTaskSiteMapper.selectOne(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getTaskNumber, bo.getTaskNumber()));
        if (Objects.isNull(sohuBusyTaskSite)) {
            throw new ServiceException(MessageUtils.message("当前任务不存在,请确认后再试"));
        }
        SohuBusyTaskVo sohuBusyTaskVo = sohuBusyTaskMapper.selectVoOne(Wrappers.<SohuBusyTask>lambdaQuery()
                .eq(SohuBusyTask::getTaskNumber, sohuBusyTaskSite.getMasterTaskNumber()));
        if (Objects.isNull(sohuBusyTaskVo)) {
            throw new ServiceException(MessageUtils.message("当前商单不存在,请确认后再试"));
        }
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(sohuBusyTaskVo.getType());
        if (Objects.isNull(sohuCategoryVo)) {
            throw new ServiceException(MessageUtils.message("当前商单类型不存在,请确认后再试"));
        }
        return sohuTaskProcessor.getStrategy(sohuCategoryVo.getConstMark())
                .receiveTask(bo, sohuBusyTaskVo, sohuBusyTaskSite, userId);
    }

    @Override
    public TableDataInfo<SohuBusyTaskReceiveInfoVo> listInfoByMasterTaskNumber(SohuBusyTaskReceiveBo bo,
                                                                               PageQuery pageQuery) {
        if (Objects.isNull(bo.getMasterTaskNumber())) {
            throw new ServiceException("主单编号信息不能为空");
        }
        SohuBusyTask sohuBusyTask = sohuBusyTaskMapper.selectOne(Wrappers.<SohuBusyTask>lambdaQuery()
                .eq(SohuBusyTask::getTaskNumber, bo.getMasterTaskNumber()));
        List<SohuBusyTaskSiteVo> busyTaskSiteList = busyTaskSiteMapper.selectVoList(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, bo.getMasterTaskNumber()));
        if (CollectionUtils.isEmpty(busyTaskSiteList)) {
            throw new ServiceException("查询商单信息不存在");
        }
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(sohuBusyTask.getType());
        if (Objects.isNull(sohuCategoryVo)) {
            throw new ServiceException(MessageUtils.message("当前商单类型不存在,请确认后再试"));
        }
        Page<SohuBusyTaskReceiveInfoVo> result = baseMapper.selectTaskReceiveInfoPage(PageQueryUtils.build(pageQuery),
                bo, busyTaskSiteList.stream().map(SohuBusyTaskSiteVo::getTaskNumber).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(result.getRecords())) {
            for (SohuBusyTaskReceiveInfoVo record : result.getRecords()) {
                if (Objects.nonNull(record.getGroupId()) && !record.getState().equals(SohuBusyTaskState.Cancel.name())) {
                    if (sohuCategoryVo.getConstMark().equals(BusyTaskTypeEnum.FLOW_TASK.getCode())) {
                        // 查询群信息
                        SohuImGroupVo sohuImGroupVo = remoteImService.queryById(record.getGroupId());
                        if (Objects.nonNull(sohuImGroupVo)) {
                            int groupUserNum = StrUtil.equalsAnyIgnoreCase(sohuImGroupVo.getGroupType(), ImGroupType.groupFormCustom.name()) ?
                                    sohuImGroupVo.getGroupUserNum() - 2 : sohuImGroupVo.getGroupUserNum() - 1;
                            record.setGroupPersonNum(groupUserNum < 0 ? 0 : groupUserNum);
                            record.setGroupWord(sohuImGroupVo.getGroupWord());
                        }
                        // 查询合格人数
                        Integer passNum = remoteImGroupOrderUserService.getPassPersonUserByTaskNumber(record.getTaskNumber());
                        record.setPassPersonNum(passNum);
                        // 计算结算金额
                        if (sohuBusyTask.getSingleAmount() != null) {
                            record.setAmount(sohuBusyTask.getSingleAmount().multiply(new BigDecimal(passNum)));
                        }
                    } else {
                        record.setAmount(sohuBusyTask.getFullAmount());
                    }
                }
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public String checkReceiveTask(String taskNumber) {
        Long userId = LoginHelper.getUserId();
        if (Objects.isNull(userId)) {
            return taskNumber;
        }
        // 基于taskNumber 查询主的masterTaskNumber
        SohuBusyTaskSiteVo taskSiteVo = busyTaskSiteMapper.queryByTaskNumber(taskNumber, null);
        if (Objects.isNull(taskSiteVo)) {
            return taskNumber;
        }
        List<SohuBusyTaskSiteVo> siteList = busyTaskSiteMapper.selectVoList(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, taskSiteVo.getMasterTaskNumber()));
        List<String> taskNumberList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(siteList)) {
            taskNumberList = siteList.stream().map(SohuBusyTaskSiteVo::getTaskNumber).collect(Collectors.toList());
        } else {
            taskNumberList.add(taskNumber);
        }
        SohuBusyTaskReceiveVo receiveVo = baseMapper.selectVoOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                .in(SohuBusyTaskReceive::getTaskNumber, taskNumberList)
                .eq(SohuBusyTaskReceive::getUserId, userId)
                .ne(SohuBusyTaskReceive::getState, SohuBusyTaskState.Cancel.name())
                .last("limit 1"));
        if (Objects.isNull(receiveVo) && !taskSiteVo.getState().equals(SohuBusyTaskState.WaitReceive.name())) {
            // 需要随机取一个待接单商单编码返回
            List<SohuBusyTaskSiteVo> waitReceiveList = siteList.stream()
                    .filter(sohuBusyTaskSiteVo -> sohuBusyTaskSiteVo.getState().equals(SohuBusyTaskState.WaitReceive.name()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(waitReceiveList)) {
                return waitReceiveList.get(0).getTaskNumber();
            }
        }
        return Objects.isNull(receiveVo) ? taskNumber : receiveVo.getTaskNumber();
    }

    @Override
    public List<SohuBusyTaskReceiveVo> queryReceiveListByTaskNumber(List<String> taskNumbers) {
        if (CollectionUtils.isEmpty(taskNumbers)) {
            return new ArrayList<>();
        }
        return baseMapper.queryReceiveListByTaskNumber(taskNumbers);
    }

    @Override
    public List<SohuBusyTaskReceiveVo> queryListByState(String state) {
        return baseMapper.selectVoList(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                .eq(SohuBusyTaskReceive::getState, state));
    }

    @Override
    public SohuBusyTaskReceiveVo selectById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public SohuBusyTaskReceiveVo queryByTaskNumberAndReceiveUserId(String taskNumber, Long receiveUserId) {
        return baseMapper.selectVoOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                .eq(SohuBusyTaskReceive::getTaskNumber, taskNumber)
                .eq(SohuBusyTaskReceive::getUserId, receiveUserId).orderByDesc(SohuBusyTaskReceive::getCreateTime)
                .last("limit 1"));
    }

    @Override
    public SohuBusyTaskWaitSettleVo getWaitSettleList(Long id) {
        SohuBusyTaskVo sohuBusyTaskVo = sohuBusyTaskMapper.queryByTaskId(id);
        if (Objects.isNull(sohuBusyTaskVo)) {
            throw new ServiceException("该商单不存在");
        }
        List<String> stateList = new ArrayList<>();
        stateList.add(SohuBusyTaskState.WaitSettle.name());
        stateList.add(SohuBusyTaskState.WaitApproveSettle.name());
        stateList.add(SohuBusyTaskState.OverSettle.name());
        stateList.add(SohuBusyTaskState.Execute.name());
        List<SohuBusyTaskReceiveVo> receiveList = this.queryListByMasterTaskNumberAndStateList(sohuBusyTaskVo.getTaskNumber(), stateList);
        SohuBusyTaskWaitSettleVo waitSettleVo = new SohuBusyTaskWaitSettleVo();
        waitSettleVo.setTaskNumber(sohuBusyTaskVo.getTaskNumber());
        waitSettleVo.setAmount(sohuBusyTaskVo.getFullAmount());
        BigDecimal waitSettleAmount = BigDecimal.ZERO;
        BigDecimal refundAmount = sohuBusyTaskVo.getFullAmount();
        List<SohuBusyTaskWaitSettleVo.waitSettleVo> waitSettleList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(receiveList)) {
            for (SohuBusyTaskReceiveVo receiveVo : receiveList) {
                Integer passNum = remoteImGroupOrderUserService.getPassPersonUserByTaskNumber(receiveVo.getTaskNumber());
                BigDecimal settleAmount = CalUtils.multiply(BigDecimal.valueOf(passNum), sohuBusyTaskVo.getSingleAmount());
                refundAmount = CalUtils.sub(refundAmount, settleAmount);
                waitSettleAmount = CalUtils.add(waitSettleAmount, settleAmount);
                // 判断接单状态，如果是已完结,则不存入待结算数组中
                if (!receiveVo.getState().equals(SohuBusyTaskState.OverSettle.name())) {
                    SohuBusyTaskWaitSettleVo.waitSettleVo settleVo = new SohuBusyTaskWaitSettleVo.waitSettleVo();
                    settleVo.setUserName(receiveVo.getUserName());
                    settleVo.setPassPersonNum(passNum.longValue());
                    settleVo.setSettleAmount(settleAmount);
                    waitSettleList.add(settleVo);
                }
            }
        }
        waitSettleVo.setWaitSettleList(waitSettleList);
        waitSettleVo.setWaitSettleAmount(waitSettleAmount);
        waitSettleVo.setRefundAmount(refundAmount);
        return waitSettleVo;
    }

    @Override
    public List<SohuBusyTaskReceiveVo> queryListByMasterTaskNumberAndStateList(String masterTaskNumber, List<String> stateList) {
        // 查询子单
        List<SohuBusyTaskSiteVo> siteVoList = busyTaskSiteMapper.selectVoList(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, masterTaskNumber));
        if (CollectionUtils.isEmpty(siteVoList)) {
            throw new ServiceException("该商单子单不存在");
        }
        return baseMapper.listReceiveByTaskNumberListAndStateList(
                siteVoList.stream().map(SohuBusyTaskSiteVo::getTaskNumber).collect(Collectors.toList()), stateList);
    }

    @Override
    public Long inTransitTask(Long userId, List<String> status, Date freezeTime) {
        LambdaQueryWrapper<SohuBusyTaskReceive> lqw = new LambdaQueryWrapper();
        lqw.and(wp->wp.eq(SohuBusyTaskReceive::getUserId, userId).or().eq(SohuBusyTaskReceive::getSharePerson, userId));
        lqw.in(SohuBusyTaskReceive::getState, status);
        lqw.lt(SohuBusyTaskReceive::getCreateTime, freezeTime);
        return baseMapper.selectCount(lqw);
    }

    @Override
    public SohuBusyTaskReceiveVo queryByMasterTaskNumberrAndReceiveUserId(String taskNumber, Long receiveUserId) {
        //查询子任编号
        SohuBusyTaskSiteVo siteVo = busyTaskSiteMapper.selectVoOne(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, taskNumber).last(" limit 1"));
        return queryByTaskNumberAndReceiveUserId(siteVo.getTaskNumber(),receiveUserId);
    }

    @Override
    public List<TaskGroupVo> getReceiveTaskGroupList(Date startTime, Date endTime) {
        return baseMapper.countTasksByUser(startTime, endTime);
    }


}
