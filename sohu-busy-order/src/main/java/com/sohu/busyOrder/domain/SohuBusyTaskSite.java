package com.sohu.busyOrder.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.busyorder.api.enums.KickbackTypeEnums;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 商单与站点关联对象 sohu_busy_task_site
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Data
@TableName("sohu_busy_task_site")
public class SohuBusyTaskSite extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 当状态等于这些时，可以强制下架
     */
    public transient static final Map<String, String> CAN_UPDATE_STATES = new HashMap<String, String>() {
        {
            put("Edit", "草稿");
            put("WaitApprove", "待审核");
            put("WaitReceive", "待接单");
            put("CompelOff", "强制下架");
            put("OffShelf", "下架");
            put("Pass", "通过");
            put("Refuse", "审核拒绝");
            put("Receive", "接单中但未付保证金");
        }
    };

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 作者id
     */
    private Long userId;
    /**
     * 主任务编号
     */
    private String masterTaskNumber;
    /**
     * 子任务编号
     */
    private String taskNumber;
    /**
     * 是否有人申请接单：0没有，1有
     */
    private Boolean isReceive;
    /**
     * 站点id
     */
    private Long siteId;
    /**
     * 商单类型
     */
    private Long type;
    /**
     * 行业类型
     */
    private Long industryType;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容描述
     */
    private String content;
    /**
     * 附件（url列表，逗号隔开）
     */
    private String annex;
    /**
     * 是否有接单人限制;0=默认全部可以接单 1=部分人可以接单
     */
    private Boolean receiveLimit;
    /**
     * 指派接单人用户id
     */
    private Long receiveOnlyUserId;
    /**
     * 交付天数，从被接单时间开始计算
     */
    private Integer deliveryDay;
    /**
     * 交付说明
     */
    private String deliveryMsg;
    /**
     * 结算方式;1=先执行后付款 2=依据进度付款
     */
    private Integer settleType;
    /**
     * 是否是阶段性交付;0=否 1=是
     */
    private Boolean deliveryStep;
    /**
     * 佣金类型;none:无设置，percentage:百分比，price:一口价
     * {@link KickbackTypeEnums}
     */
    private String kickbackType;
    /**
     * 佣金类型值;佣金类型值
     */
//    @TableField(fill = FieldFill.UPDATE)
    private BigDecimal kickbackValue;
    /**
     * 价值金额
     */
    private BigDecimal fullAmount;
    /**
     * 价值币种
     */
    private Long fullCurrency;
    /**
     * 状态：Edit-草稿，WaitApprove-待审核, Pass-已通过，Refuse-审核拒绝 ，Discuss-商议中 ，Execute-执行中，WaitSettle-待结算，OverSettle-已结算即完成，Error-商单异常
     */
    private String state;
    /**
     * 子任务状态：OnShelf：上架，OffShelf：下架，CompelOff：强制下架
     */
    private String shelfState;
    /**
     * 拒绝理由
     */
    private String refuseMsg;
    /**
     * 审核人ID;审核人
     */
    private Long auditUser;
    /**
     * 审核时间
     */
    private Date auditTime;
    /**
     * 上下架人ID
     */
    private Long shelfUser;
    /**
     * 上下架时间;上下架时间
     */
    private Date shelfTime;

    /**
     * 发布的国家站点
     */
    private Long countrySiteId;

    /**
     * 是否支付佣金
     */
    private Boolean isIndependent;

    /**
     * 分销人金额
     */
    private BigDecimal distributionAmount;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 是否正在售后状态.1=是 0=否
     */
    private Boolean isAfterSales;

    /**
     * 是否群关联
     */
    @TableField(exist = false)
    private Boolean groupRelate;
}
