package com.sohu.busyOrder.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 任务主体对象 sohu_busy_task
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_busy_task")
public class SohuBusyTask extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 标题最大长度
     */
    public static final int TITLE_MAX_LENGTH = 40;

    /**
     * 内容最大长度
     */
    public static final int CONTENT_MAX_LENGTH = 5000;

    /**
     * 最多勾选三个站点
     */
    public static final int SITE_MAX_SELECT = 3;

    /**
     * 佣金最高多少
     */
    public static final BigDecimal KICKBACK_MAX_PERCENTAGE = new BigDecimal(100);

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 作者id
     */
    private Long userId;
    /**
     * 主任务编号
     */
    private String taskNumber;
    /**
     * 商单类型
     */
    private Long type;
    /**
     * 行业类型
     */
    private Long industryType;
    /**
     * 行业类型父id
     */
    private Long pid;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容描述
     */
    private String content;
    /**
     * 附件（url列表，逗号隔开）
     */
    private String annex;
    /**
     * 是否有接单人限制;0=默认全部可以接单 1=部分人可以接单
     */
    private Boolean receiveLimit;

    /**
     * 指派接单人用户id
     */
    private Long receiveOnlyUserId;

    /**
     * 交付天数，从被接单时间开始计算
     */
    private Integer deliveryDay;
    /**
     * 交付说明
     */
    private String deliveryMsg;
    /**
     * 结算方式;1=先执行后付款 2=依据进度付款
     */
    private Integer settleType;
    /**
     * 是否是阶段性交付;0=否 1=是
     */
    private Boolean deliveryStep;

    /**
     * 阶段性交付列表
     */
    @TableField(exist = false)
    private List<SohuBusyTaskDelivery> deliveryList;
    /**
     * 价值金额
     */
    private BigDecimal fullAmount;
    /**
     * 价值币种
     */
    private Long fullCurrency;
    /**
     * 佣金类型;none:无设置，percentage:百分比，price:一口价
     */
    private String kickbackType;
    /**
     * 佣金类型值;佣金类型值
     */
//    @TableField(fill = FieldFill.UPDATE)
    private BigDecimal kickbackValue;
    /**
     * 是否需要拆单;是-Y 否-N
     */
    private Boolean needSplit;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 发布站点ID集合.根据id逗号分割
     */
    private String siteIds;

    /**
     * 发布的国家站点
     */
    private Long countrySiteId;

    /**
     * 是否是草稿
     */
    private Boolean isDraft;

    /**
     * 拆单状态.1=已拆单 0=未拆单
     */
    private Boolean splitState;

    /**
     * 主任务状态：WaitPend-待拆单，WaitApprove-待审核，OnSale-出售中，OffShelf-下架，CompelOff-强制下架
     */
    private String state;

    /**
     * 拒绝理由
     */
    private String refuseMsg;

    /**
     * 审核人ID;审核人
     */
    private Long auditUser;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 阅读数
     */
    private Long viewCount;

    /**
     * 关联任务id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long relationId;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 地址
     */
    private String address;

    /**
     * 位置、经纬度
     */
    private String location;
    /**
     * 时间单位 1.天 2.小时 3.分钟
     */
    private Integer timeUnit;
    /**
     * 交付类型 1.按时间交付 2.按次数交付
     */
    private Integer deliveryType;
    /**
     * 交付标准 大于0的正整数
     */
    private Integer deliveryStandard;
    /**
     * 交付单位 按时间交付 1.秒 2.分钟 3.小时  按次数交付 1.次
     */
    private Integer deliveryTimeUnit;
    /**
     * 交付时间秒值
     */
    private Long deliveryTime;
    /**
     * 单个金额
     */
    private BigDecimal singleAmount;
    /**
     * 接单人数
     */
    private Integer receiveNum;
    /**
     * 是否需要审核接单  0.否  1.是
     */
    private Boolean isApproveReceive;
    /**
     * 任务排序-默认0
     */
    private Integer sortIndex;
    /**
     * 生效开始时间
     */
    private Date startTime;
    /**
     * 生效结束时间
     */
    private Date endTime;
    /**
     * 达标数量
     */
    private Integer passNum;
    /**
     * 发单时的ip地址
     */
    private String ipAddress;

    /**
     * 是否开启保证金 0 关闭 1 开启
     */
    private Boolean isReceiveDeposit;

    /**
     * 愿望分类id
     */
    private Long categoryType;

    /**
     * 供需类型id
     */
    private Long supplyType;
    /**
     * 是否已处理
     */
    private Boolean isHandle;
//    /**
//     * 交付开始时间
//     */
//    private String deliveryStartDay;
//
//    /**
//     * 交付结束时间
//     */
//    private String deliveryEndDay;

}
