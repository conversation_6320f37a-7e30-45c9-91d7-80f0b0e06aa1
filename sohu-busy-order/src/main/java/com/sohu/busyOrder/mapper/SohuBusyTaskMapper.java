package com.sohu.busyOrder.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.busyOrder.domain.SohuBusyTask;
import com.sohu.busyorder.api.bo.SohuBusyTaskAllListBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskBo;
import com.sohu.busyorder.api.domain.SohuBusyTaskSiteReqBo;
import com.sohu.busyorder.api.vo.SohuBusyTaskAllListVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskVo;
import com.sohu.busyorder.api.vo.TaskGroupVo;
import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.middle.api.bo.SohuTopicContentQueryBo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 任务主体Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Mapper
public interface SohuBusyTaskMapper extends BaseMapperPlus<SohuBusyTaskMapper, SohuBusyTask, SohuBusyTaskVo> {

    /**
     * 任务分页列表
     *
     * @param bo
     * @param build
     */
    Page<SohuBusyTaskVo> selectTaskPage(@Param("bo") SohuBusyTaskBo bo, @Param("build") Page<Object> build);

    /**
     * 根据主键查询详情
     *
     * @param id
     */
    SohuBusyTaskVo queryByTaskId(@Param("id") Long id);

    /**
     * 更新浏览量
     *
     * @param taskNumber 主任务订单编号
     */
    void updateViewCount(@Param("taskNumber") String taskNumber);

    /**
     * 根据时间范围统计执行中销售额
     *
     * @param lqw
     * @return
     */
    BigDecimal countSalesAmountByTime(@Param(Constants.WRAPPER) Wrapper<SohuBusyTask> lqw);

    /**
     * 修改商单状态
     *
     * @param taskNumber
     * @param state
     * @return
     */
    void updateTaskState(@Param("taskNumber") String taskNumber, @Param("state") String state);

    /**
     * 查询流量商单截止时间内的任务
     *
     * @return
     */
    List<SohuBusyTaskVo> selectFlowBusyTaskOfState();

    /**
     * 根据国家站长获取全部商单列表
     *
     * @param bo    SohuBusyTaskAllListBo
     * @param build Page<Object>
     * @return Page<SohuBusyTaskAllListVo>
     */
    Page<SohuBusyTaskAllListVo> getAllListWithRole(@Param("bo") SohuBusyTaskAllListBo bo, @Param("build") Page<Object> build);

    /**
     * 查询专题列表
     *
     * @param bo    SohuTopicContentQueryBo
     * @param build Page<Object>
     * @return Page<SohuBusyTaskSiteVo>
     */
    Page<SohuBusyTaskSiteVo> getTopicList(@Param("bo") SohuTopicContentQueryBo bo, @Param("build") Page<Object> build);

    /**
     * @param bo
     * @param build
     * @return
     */
    Page<SohuBusyTaskSiteVo> queryTopTaskList(@Param("bo") SohuBusyTaskSiteReqBo bo, @Param("build") Page<Object> build);

    /**
     * 获取任务组列表
     *
     * @param startTime
     * @param endTime
     * @return
     */
     List<TaskGroupVo> countTasksByUser(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
