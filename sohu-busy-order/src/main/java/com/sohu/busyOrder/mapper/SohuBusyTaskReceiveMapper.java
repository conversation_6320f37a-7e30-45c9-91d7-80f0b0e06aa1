package com.sohu.busyOrder.mapper;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.busyOrder.domain.SohuBusyTaskReceive;
import com.sohu.busyorder.api.bo.SohuBusyTaskReceiveBo;
import com.sohu.busyorder.api.vo.SohuBusyTaskReceiveInfoVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo;
import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商单接单Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Mapper
public interface SohuBusyTaskReceiveMapper extends BaseMapperPlus<SohuBusyTaskReceiveMapper, SohuBusyTaskReceive, SohuBusyTaskReceiveVo> {

    /**
     * 查询任务方申请接单列表
     *
     * @param page
     * @param bo
     */
    Page<SohuBusyTaskReceiveVo> selectTaskReceivePage(@Param("page") Page<Object> page, @Param("bo") SohuBusyTaskReceiveBo bo);

    /**
     * 查询任务方申请接单列表-接单方
     *
     * @param page
     * @param bo
     */
    Page<SohuBusyTaskReceiveVo> selectTaskReceiveUserPage(@Param("page") Page<Object> page, @Param("bo") SohuBusyTaskReceiveBo bo);

    /**
     * 查询出已接单的信息
     *
     * @param taskNumber
     * @param aTrue
     */
    List<SohuBusyTaskReceiveVo> selectReceiveAndInfo(@Param("taskNumber") String taskNumber,
                                                     @Param("aTrue") Boolean aTrue);

    /**
     * 统计接单人数
     *
     * @param userId        用户id
     * @param startDateTime 开始时间
     * @param endDateTime   结束时间
     * @return Long
     */
    Long getReceiveUserStat(@Param("userId") Long userId, @Param("startDateTime") DateTime startDateTime, @Param("endDateTime") DateTime endDateTime);

    /**
     * 获取用户执行中的任务
     *
     * @param userId 用户id
     * @return SohuBusyTaskReceiveVo
     */
    List<SohuBusyTaskReceiveVo> getExecuteBusyTaskByUserId(@Param("userId") Long userId);

    /**
     * 根据任务编号集合查询接单信息
     *
     * @param taskNumberList List<String>
     * @return List<SohuBusyTaskReceive>
     */
    List<SohuBusyTaskReceive> selectByTaskNumberList(@Param("taskNumberList") List<String> taskNumberList);

    /**
     * 查询接单明细
     *
     * @param page
     * @param bo
     * @return
     */
    Page<SohuBusyTaskReceiveInfoVo> selectTaskReceiveInfoPage(@Param("page") Page<Object> page,
                                                              @Param("bo") SohuBusyTaskReceiveBo bo,
                                                              @Param("taskNumberList") List<String> taskNumberList);

    /**
     * 根据任务编号集合查询接单信息
     *
     * @param taskNumbers List<String>
     * @return List<SohuBusyTaskReceiveVo>
     */
    List<SohuBusyTaskReceiveVo> queryReceiveListByTaskNumber(@Param("taskNumbers") List<String> taskNumbers);

    /**
     * 根据主任务编号查询接单数量
     *
     * @param taskNumber 主任务编号
     * @return Integer
     */
    Integer selectCountByTaskNumber(@Param("taskNumber") String taskNumber);

    /**
     * 统计数量
     *
     * @param state
     * @param userId
     * @return
     */
    Long selectOnlineNum(@Param("state") String state, @Param("userId") Long userId);

    /**
     * 基于商单编码数组及状态数组查询相关接单数据
     *
     * @param taskNumberList
     * @param stateList
     * @return
     */
    List<SohuBusyTaskReceiveVo> listReceiveByTaskNumberListAndStateList(@Param("taskNumberList") List<String> taskNumberList, @Param("stateList") List<String> stateList);
}
