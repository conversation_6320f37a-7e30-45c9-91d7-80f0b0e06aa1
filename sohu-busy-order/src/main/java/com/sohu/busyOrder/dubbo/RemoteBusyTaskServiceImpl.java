package com.sohu.busyOrder.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.sohu.busyOrder.base.FlowService;
import com.sohu.busyOrder.domain.SohuBusyTaskSite;
import com.sohu.busyOrder.service.*;
import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.busyorder.api.bo.*;
import com.sohu.busyorder.api.domain.SohuBusyTaskAfterSaleExecuteBo;
import com.sohu.busyorder.api.domain.SohuBusyTaskReqBo;
import com.sohu.busyorder.api.domain.SohuBusyTaskSiteReqBo;
import com.sohu.busyorder.api.domain.SohuMcnBusyTaskSiteReqBo;
import com.sohu.busyorder.api.model.SohuBusyTaskModel;
import com.sohu.busyorder.api.model.SohuBusyTaskSiteModel;
import com.sohu.busyorder.api.vo.*;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuTaskSiteAdInfoQueryBo;
import com.sohu.middle.api.bo.SohuTopicContentQueryBo;
import com.sohu.middle.api.vo.SohuTaskSiteAdInfoVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteBusyTaskServiceImpl implements RemoteBusyTaskService {

    private final ISohuBusyTaskService iSohuBusyTaskService;
    private final ISohuBusyTaskSiteService iSohuBusyTaskSiteService;
    private final ISohuBusyTaskAfterSalesService iSohuBusyTaskAfterSalesService;
    private final ISohuTaskOrderReportService iSohuTaskOrderReportService;
    private final ISohuBusyTaskPayService iSohuBusyTaskPayService;
    private final ISohuBusyTaskRefundService iSohuBusyTaskRefundService;
    private final FlowService flowService;

    @Override
    public SohuBusyTaskModel queryById(Long id) {
        SohuBusyTaskVo sohuBusyTaskVo = iSohuBusyTaskService.queryById(id);
        return BeanCopyUtils.copy(sohuBusyTaskVo, SohuBusyTaskModel.class);
    }

    @Override
    public List<SohuBusyTaskModel> queryList(SohuBusyTaskReqBo bo) {
        SohuBusyTaskBo sohuBusyTaskBo = BeanCopyUtils.copy(bo, SohuBusyTaskBo.class);
        List<SohuBusyTaskVo> sohuBusyTaskVoList = iSohuBusyTaskService.queryList(sohuBusyTaskBo);
        return BeanCopyUtils.copyList(sohuBusyTaskVoList, SohuBusyTaskModel.class);
    }

    @Override
    public List<SohuBusyTaskModel> queryList(List<Long> ids) {
        List<SohuBusyTaskVo> sohuBusyTaskVoList = iSohuBusyTaskService.queryList(ids);
        return BeanCopyUtils.copyList(sohuBusyTaskVoList, SohuBusyTaskModel.class);
    }

    @Override
    public List<SohuBusyTaskModel> queryListByNumberList(Collection<? extends Serializable> taskNumberList) {
        List<SohuBusyTaskVo> sohuBusyTaskVoList = iSohuBusyTaskService.queryListByNumberList(taskNumberList);
        return BeanCopyUtils.copyList(sohuBusyTaskVoList, SohuBusyTaskModel.class);
    }

    @Override
    public Boolean insertByBo(SohuBusyTaskReqBo bo) {
        SohuBusyTaskBo sohuBusyTaskBo = BeanCopyUtils.copy(bo, SohuBusyTaskBo.class);
        iSohuBusyTaskService.insertByBo(sohuBusyTaskBo);
        return Boolean.TRUE;
    }

    @Override
    public Long updateByBo(SohuBusyTaskReqBo bo) {
        SohuBusyTaskBo sohuBusyTaskBo = BeanCopyUtils.copy(bo, SohuBusyTaskBo.class);
        return iSohuBusyTaskService.updateByBo(sohuBusyTaskBo);
    }

    @Override
    public Boolean exitChildTask(String taskNumber) {
        return iSohuBusyTaskService.exitChildTask(taskNumber);
    }

    @Override
    public SohuBusyTaskVo getByTaskNo(String taskNumber) {
        return iSohuBusyTaskService.getByTaskNo(taskNumber);
    }

    @Override
    public Boolean updateTaskState(String taskNumber, String state) {
        return iSohuBusyTaskService.updateTaskState(taskNumber, state);
    }

    @Override
    public Boolean updateTaskStateAfterPay(String taskNumber, String state, Long userId) {
        return iSohuBusyTaskSiteService.updateTaskStateAfterPay(taskNumber, state, userId);
    }

    @Override
    public Date getTaskTimeWithMasterTaskNumber(String masterTaskNumber) {
        return iSohuBusyTaskService.getTaskTimeWithMasterTaskNumber(masterTaskNumber);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return iSohuBusyTaskService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public Boolean audit(SohuBusyTaskReqBo bo) {
        SohuBusyTaskBo sohuBusyTaskBo = BeanCopyUtils.copy(bo, SohuBusyTaskBo.class);
        return iSohuBusyTaskService.audit(sohuBusyTaskBo);
    }

    @Override
    public Boolean shelf(SohuBusyTaskReqBo bo) {
        SohuBusyTaskBo sohuBusyTaskBo = BeanCopyUtils.copy(bo, SohuBusyTaskBo.class);
        return iSohuBusyTaskService.shelf(sohuBusyTaskBo);
    }

    @Override
    public TableDataInfo<SohuBusyTaskModel> queryPageLists(SohuBusyTaskReqBo bo, PageQuery pageQuery) {
        SohuBusyTaskBo sohuBusyTaskBo = BeanCopyUtils.copy(bo, SohuBusyTaskBo.class);
        TableDataInfo<SohuBusyTaskVo> sohuBusyTaskVoTableDataInfo = iSohuBusyTaskService.queryPageLists(sohuBusyTaskBo, pageQuery);
        List<SohuBusyTaskModel> sohuBusyTaskModelList = BeanCopyUtils.copyList(sohuBusyTaskVoTableDataInfo.getData(), SohuBusyTaskModel.class);
        TableDataInfo<SohuBusyTaskModel> modelTableDataInfo = TableDataInfoUtils.build(sohuBusyTaskModelList);
        modelTableDataInfo.setTotal(sohuBusyTaskVoTableDataInfo.getTotal());
        return modelTableDataInfo;
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteModel> queryPageChildLists(SohuBusyTaskSiteReqBo bo, PageQuery pageQuery) {
        SohuBusyTaskSiteBo sohuBusyTaskSiteBo = BeanCopyUtils.copy(bo, SohuBusyTaskSiteBo.class);
        TableDataInfo<SohuBusyTaskSiteVo> sohuBusyTaskSiteVoTableDataInfo = iSohuBusyTaskService.queryPageChildLists(sohuBusyTaskSiteBo, pageQuery);
        List<SohuBusyTaskSiteModel> sohuBusyTaskSiteModelList = BeanCopyUtils.copyList(sohuBusyTaskSiteVoTableDataInfo.getData(), SohuBusyTaskSiteModel.class);
        TableDataInfo<SohuBusyTaskSiteModel> modelTableDataInfo = TableDataInfoUtils.build(sohuBusyTaskSiteModelList);
        modelTableDataInfo.setTotal(sohuBusyTaskSiteVoTableDataInfo.getTotal());
        return modelTableDataInfo;
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageOfOnShelf(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        return iSohuBusyTaskService.queryPageOfOnShelf(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteModel> queryPageMyChildLists(SohuBusyTaskSiteReqBo bo, PageQuery pageQuery) {
        SohuBusyTaskSiteBo sohuBusyTaskSiteBo = BeanCopyUtils.copy(bo, SohuBusyTaskSiteBo.class);
        TableDataInfo<SohuBusyTaskSiteVo> sohuBusyTaskSiteVoTableDataInfo = iSohuBusyTaskService.queryPageMyChildLists(sohuBusyTaskSiteBo, pageQuery);
        List<SohuBusyTaskSiteModel> sohuBusyTaskSiteModelList = BeanCopyUtils.copyList(sohuBusyTaskSiteVoTableDataInfo.getData(), SohuBusyTaskSiteModel.class);
        TableDataInfo<SohuBusyTaskSiteModel> modelTableDataInfo = TableDataInfoUtils.build(sohuBusyTaskSiteModelList);
        modelTableDataInfo.setTotal(sohuBusyTaskSiteVoTableDataInfo.getTotal());
        return modelTableDataInfo;
    }

    @Override
    public List<SohuBusyTaskSiteModel> getChildList(String masterTaskNumber) {
        List<SohuBusyTaskSiteVo> childList = iSohuBusyTaskService.getChildList(masterTaskNumber);
        return BeanCopyUtils.copyList(childList, SohuBusyTaskSiteModel.class);
    }

    @Override
    public SohuBusyTaskSiteModel getChildInfo(String taskNumber) {
        SohuBusyTaskSiteVo childInfo = iSohuBusyTaskService.getChildInfo(taskNumber);
        return BeanCopyUtils.copy(childInfo, SohuBusyTaskSiteModel.class);
    }

    @Override
    public SohuBusyTaskSiteModel getChildInfo(Long taskId) {
        SohuBusyTaskSiteVo childInfo = iSohuBusyTaskSiteService.queryById(taskId);
        return BeanCopyUtils.copy(childInfo, SohuBusyTaskSiteModel.class);
    }

    @Override
    public Boolean auditChild(SohuBusyTaskSiteReqBo bo) {
        SohuBusyTaskSiteBo busyTaskSiteBo = BeanCopyUtils.copy(bo, SohuBusyTaskSiteBo.class);
        return iSohuBusyTaskService.auditChild(busyTaskSiteBo);
    }

    @Override
    public Boolean updateByChildBo(SohuBusyTaskSiteReqBo bo) {
        SohuBusyTaskSiteBo busyTaskSiteBo = BeanCopyUtils.copy(bo, SohuBusyTaskSiteBo.class);
        return iSohuBusyTaskService.updateByChildBo(busyTaskSiteBo);
    }

    @Override
    public Boolean childShelf(SohuBusyTaskSiteReqBo bo) {
        SohuBusyTaskSiteBo busyTaskSiteBo = BeanCopyUtils.copy(bo, SohuBusyTaskSiteBo.class);
        return iSohuBusyTaskService.childShelf(busyTaskSiteBo);
    }

    @Override
    public Boolean exitAuditTask(String taskNumber, String state) {
        return iSohuBusyTaskService.exitAuditTask(taskNumber, state);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteModel> queryPageTaskLists(SohuBusyTaskSiteReqBo bo, PageQuery pageQuery) {
        SohuBusyTaskSiteBo sohuBusyTaskSiteBo = BeanCopyUtils.copy(bo, SohuBusyTaskSiteBo.class);
        TableDataInfo<SohuBusyTaskSiteVo> sohuBusyTaskSiteVoTableDataInfo = iSohuBusyTaskService.queryPageTaskLists(sohuBusyTaskSiteBo, pageQuery);
        List<SohuBusyTaskSiteModel> sohuBusyTaskSiteModelList = BeanCopyUtils.copyList(sohuBusyTaskSiteVoTableDataInfo.getData(), SohuBusyTaskSiteModel.class);
        TableDataInfo<SohuBusyTaskSiteModel> modelTableDataInfo = TableDataInfoUtils.build(sohuBusyTaskSiteModelList);
        modelTableDataInfo.setTotal(sohuBusyTaskSiteVoTableDataInfo.getTotal());
        return modelTableDataInfo;
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageTaskList(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        return iSohuBusyTaskService.queryPageTaskLists(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteModel> queryPageMcnTaskList(SohuMcnBusyTaskSiteReqBo bo, PageQuery pageQuery) {
        TableDataInfo<SohuBusyTaskSiteVo> dataInfo = iSohuBusyTaskSiteService.queryPageMcnTaskList(BeanUtil.copyProperties(bo, SohuMcnBusyTaskSiteBo.class), pageQuery);
        return TableDataInfoUtils.copyInfo(dataInfo, SohuBusyTaskSiteModel.class);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteModel> queryPageMcnTaskWindowList(SohuMcnBusyTaskSiteReqBo bo, PageQuery pageQuery) {
        TableDataInfo<SohuBusyTaskSiteVo> dataInfo = iSohuBusyTaskSiteService.queryPageMcnTaskWindowList(BeanUtil.copyProperties(bo, SohuMcnBusyTaskSiteBo.class), pageQuery);
        return TableDataInfoUtils.copyInfo(dataInfo, SohuBusyTaskSiteModel.class);
    }

    @Override
    public Long getBusyTaskOfPublishStat(Long userId) {
        return iSohuBusyTaskService.getBusyTaskOfPublishStat(userId);
    }

    @Override
    public Long getYesterdayBusyTaskOfPublishStat(Long userId) {
        return iSohuBusyTaskService.getYesterdayBusyTaskOfPublishStat(userId);
    }

    @Override
    public Long getBusyTaskOfFinishStat(Long userId) {
        return iSohuBusyTaskService.getBusyTaskOfFinishStat(userId);
    }

    @Override
    public Long getYesterdayBusyTaskOfFinishStat(Long userId) {
        return iSohuBusyTaskService.getYesterdayBusyTaskOfFinishStat(userId);
    }

    @Override
    public Long getBusyTaskViewStat(Long userId, String startDate, String endDate) {
        return iSohuBusyTaskService.getBusyTaskViewStat(userId, startDate, endDate);
    }

    @Override
    public Long getBusyTaskWithReceiveUserStat(Long userId, String startDate, String endDate) {
        return iSohuBusyTaskService.getBusyTaskWithReceiveUserStat(userId, startDate, endDate);
    }

    @Override
    public Long getBusyTaskOfExecuteStat(Long userId, String startDate, String endDate) {
        return iSohuBusyTaskService.getBusyTaskOfExecuteStat(userId, startDate, endDate);
    }

    @Override
    public Long getBusyTaskOfFinishWithLimitDayStat(Long userId, String startDate, String endDate) {
        return iSohuBusyTaskService.getBusyTaskOfFinishWithLimitDayStat(userId, startDate, endDate);
    }

    @Override
    public BigDecimal getBusyTaskAmountStat(Long userId, String startDate, String endDate) {
        return iSohuBusyTaskService.getBusyTaskAmountStat(userId, startDate, endDate);
    }

    @Override
    public Boolean initAirecContentItems() {
        return iSohuBusyTaskService.initAirecContentItems();
    }

    @Override
    public Boolean abortTask(SohuBusyTaskAfterSaleExecuteBo bo) {
        return iSohuBusyTaskAfterSalesService.abortTask(bo);
    }

    @Override
    public Boolean abortReplaceReceiveTask(SohuBusyTaskAfterSaleExecuteBo bo) {
        return iSohuBusyTaskAfterSalesService.abortReplaceReceiveTask(bo);
    }

    @Override
    public Boolean revokeTask(SohuBusyTaskAfterSaleExecuteBo bo) {
        return null;
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteModel> listCollect(String busyTitle, PageQuery pageQuery) {
        return iSohuBusyTaskSiteService.listCollect(busyTitle, pageQuery);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteModel> queryReceiveWindowList(SohuMcnBusyTaskSiteReqBo bo, PageQuery pageQuery) {
        TableDataInfo<SohuBusyTaskSiteVo> dataInfo = iSohuBusyTaskSiteService.queryReceiveWindowList(BeanUtil.copyProperties(bo, SohuMcnBusyTaskSiteBo.class), pageQuery);
        return TableDataInfoUtils.copyInfo(dataInfo, SohuBusyTaskSiteModel.class);
    }

    @Override
    public List<SohuBusyTaskSiteModel> queryMasterTaskNumber(List<String> taskNumberList) {
        List<SohuBusyTaskSiteVo> list = iSohuBusyTaskSiteService.queryMasterTaskNumber(taskNumberList);
        return CollUtil.isEmpty(list) ? null : BeanUtil.copyToList(list, SohuBusyTaskSiteModel.class);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryImGroupSiteTask(SohuBusyTaskSiteReqBo bo, PageQuery pageQuery) {
        SohuBusyTaskSiteBo sohuBusyTaskSiteBo = BeanCopyUtils.copy(bo, SohuBusyTaskSiteBo.class);
        return iSohuBusyTaskSiteService.queryImGroupSiteTask(sohuBusyTaskSiteBo, pageQuery);
    }

    @Override
    public SohuBusyTaskSiteModel queryMasterTaskNumber(String taskNumber) {
        SohuBusyTaskSite taskSite = iSohuBusyTaskSiteService.getByNo(taskNumber);
        return BeanUtil.copyProperties(taskSite, SohuBusyTaskSiteModel.class);
    }

    @Override
    public Long queryBusyTaskNumBySite(Long siteId, Long userId) {
        return iSohuBusyTaskService.queryBusyTaskNumBySite(siteId, userId);
    }

    @Override
    public Integer countByState(String state) {
        // 查询非拆单类型当前状态的商单数量
        Long taskCount = iSohuBusyTaskService.countByState(state);
        // 查询拆单类型子单的当前状态的商单数量
        Long taskSiteCount = iSohuBusyTaskSiteService.countByState(state);
        return taskCount.intValue() + taskSiteCount.intValue();
    }

    @Override
    public Long countByTime(String startTime, String endTime) {
        // 查询非拆单类型当前状态的商单数量
        Long taskCount = iSohuBusyTaskService.countByTime(startTime, endTime);
        // 查询拆单类型子单的当前状态的商单数量
        Long taskSiteCount = iSohuBusyTaskSiteService.countByTime(startTime, endTime);
        return taskCount + taskSiteCount;
    }

    @Override
    public List<SohuTaskOrderReportVo> getBusyTaskSalesTrendByTimeRange(Date startDate, Date endDate) {
        return iSohuTaskOrderReportService.getBusyTaskSalesTrendByTimeRange(startDate, endDate);
    }

    @Override
    public void saveDataByBo(SohuTaskOrderReportBo bo) {
        iSohuTaskOrderReportService.insertByBo(bo);
    }

    @Override
    public void updateDataByBo(SohuTaskOrderReportBo bo) {
        iSohuTaskOrderReportService.updateByBo(bo);
    }

    @Override
    public BigDecimal countSalesAmountByTime(Date startTime, Date endTime) {
        return iSohuBusyTaskService.countSalesAmountByTime(startTime, endTime);
    }

    @Override
    public Long countOrderByTime(Date startTime, Date endTime) {
        return iSohuBusyTaskService.countOrderByTime(startTime, endTime);
    }

    @Override
    public Long countAcceptByTime(Date startTime, Date endTime) {
        return iSohuBusyTaskService.countAcceptByTime(startTime, endTime);
    }

    @Override
    public SohuTaskOrderReportVo queryReportByTime(String dateTime) {
        return iSohuTaskOrderReportService.queryReportByTime(dateTime);
    }

    @Override
    public void updateDeliveryTime(Long id, Long deliveryTime) {
        iSohuBusyTaskService.updateDeliveryTime(id, deliveryTime);
    }

    @Override
    public Boolean saveBusyTaskPay(SohuBusyTaskPayBo bo) {
        return iSohuBusyTaskPayService.insertByBo(bo);
    }

    @Override
    public SohuBusyTaskPayVo queryBusyTaskPay(String outTradeNo, String payStatus) {
        return iSohuBusyTaskPayService.queryPayByOrderNo(outTradeNo, payStatus);
    }

    @Override
    public List<SohuBusyTaskPayVo> queryBusyTaskPayList(List<String> outTradeNos, String payStatus) {
        return iSohuBusyTaskPayService.queryBusyTaskPayList(outTradeNos, payStatus);
    }

    @Override
    public Boolean updateBusyTaskPay(SohuBusyTaskPayBo bo) {
        return iSohuBusyTaskPayService.updateBusyBo(bo);
    }

    @Override
    public List<SohuBusyTaskPayVo> queryByTaskNumber(String taskNumber, String payStatus) {
        return iSohuBusyTaskPayService.queryByTaskNumber(taskNumber, payStatus);
    }

    @Override
    public SohuBusyTaskPayVo queryByPayScene(String taskNumber, String payStatus, Integer paySceneType) {
        return iSohuBusyTaskPayService.queryByPayScene(taskNumber, payStatus, paySceneType);
    }

    @Override
    public List<SohuBusyTaskPayVo> payJobList(String payStatus, Integer paySceneType, Integer isExecute) {
        return iSohuBusyTaskPayService.payJobList(payStatus, paySceneType, isExecute);
    }

    @Override
    public Boolean saveBusyTaskRefund(SohuBusyTaskRefundBo bo) {
        return iSohuBusyTaskRefundService.insertByBo(bo);
    }

    @Override
    public void insertUserLabelByTaskNumber(String masterTaskNumber, Long userId) {
        iSohuBusyTaskService.insertUserLabelByTaskNumber(masterTaskNumber, userId);
    }

    @Override
    public void handleFlowBusyTask() {
        flowService.handleFlowBusyTask();
    }

    @Override
    public List<SohuBusyTaskVo> selectFlowBusyTaskOfState() {
        return iSohuBusyTaskService.selectFlowBusyTaskOfState();
    }

    @Override
    public Boolean updateSortAndEffectiveTimeById(SohuBusyTaskSortBo bo) {
        return iSohuBusyTaskService.updateSortAndEffectiveTimeById(bo);
    }

    @Override
    public void resetTopBusyTaskHandler() {
        iSohuBusyTaskService.resetTopBusyTaskHandler();
    }

    @Override
    public TableDataInfo<SohuBusyTaskAllListVo> getAllListWithRole(SohuBusyTaskAllListBo bo, PageQuery pageQuery) {
        return iSohuBusyTaskService.getAllListWithRole(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuPayBusyVo> busyFlowList(SohuPayBusyBo bo, PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        if (!LoginHelper.isAdmin(userId)) {
            bo.setUserId(userId);
        }
        if (bo.getType() == Constants.ONE) {
            return iSohuBusyTaskPayService.payList(bo, pageQuery);
        }
        return iSohuBusyTaskRefundService.refundList(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuPayBusyVo> busyAggrPayList(SohuPayBusyBo bo, PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        if (!LoginHelper.isAdmin(userId)) {
            bo.setUserId(userId);
        }
        TableDataInfo<SohuPayBusyVo> paidList = iSohuBusyTaskPayService.payList(bo, pageQuery);
        if (CollUtil.isNotEmpty(paidList.getData())){
            List<String> taskNumbers = paidList.getData().stream().map(SohuPayBusyVo::getTransactionId).collect(Collectors.toList());
            //查询退款记录
            List<SohuPayBusyVo> refundList = iSohuBusyTaskRefundService.queryByTaskNumbers(taskNumbers);
            Map<String, List<SohuPayBusyVo>> map = refundList.stream().collect(Collectors.groupingBy(SohuPayBusyVo::getTransactionId));
            paidList.getData().forEach(item->{
                item.setRefundList(map.get(item.getTransactionId()));
            });
        }
        return paidList;
    }

    @Override
    public Boolean settleBusyTask(String taskNumber) {
        return iSohuBusyTaskService.settleBusyTask(taskNumber);
    }

    @Override
    public Boolean applySettleBusyTask(String taskNumber, String rejectReason) {
        return iSohuBusyTaskService.applySettleBusyTask(taskNumber, rejectReason);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteModel> listDistribution(String busyTitle, PageQuery pageQuery) {
        TableDataInfo<SohuBusyTaskSiteVo> dataInfo = iSohuBusyTaskSiteService.listDistribution(busyTitle, pageQuery);
        return TableDataInfoUtils.copyInfo(dataInfo, SohuBusyTaskSiteModel.class);
    }

    @Override
    public void handleFlowBusyTaskPassNum(String taskNumber) {
        iSohuBusyTaskService.handleFlowBusyTaskPassNum(taskNumber);
    }

    @Override
    public Map<String, Long> getUserIdByMasterTaskNumber(List<String> masterTaskNumberList) {
        return iSohuBusyTaskSiteService.getUserIdByMasterTaskNumber(masterTaskNumberList);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> getTopicList(Long topicId, Long topicPid, Boolean isSortByViewCount, PageQuery pageQuery) {
        SohuTopicContentQueryBo bo = new SohuTopicContentQueryBo();
        bo.setTopicId(topicId);
        bo.setTopicPid(topicPid);
        bo.setIsSortByViewCount(isSortByViewCount);
        return iSohuBusyTaskService.getTopicList(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageTopOfTaskSite(SohuBusyTaskSiteReqBo bo, PageQuery pageQuery) {
            return iSohuBusyTaskService.queryPageTopOfTaskSite(bo, pageQuery);
    }

    @Override
    public List<TaskGroupVo> getSendTaskGroupList(Date startTime, Date endTime) {
        return iSohuBusyTaskService.getSendTaskGroupList(startTime, endTime);
    }
}
