package com.sohu.third.aliyun.airec.constants;

public class AliyunAirecConstant {

    /**
     * 如果想对某条数据的某个字段的值做删除操作，可以update该字段为none，不要update为空
     */
    public final static String FIELD_NONE_VALUE = "none";

    /**
     * 插入限制size
     */
    @Deprecated
    public static int BATCH_SIZE = 500;

    /**
     * 智能推荐获取最大数量
     */
    @Deprecated
    public static int AIREC_MAX = 20;

    /**
     * 默认单次请求返回的推荐结果数量
     */
    public static int RETURN_COUNT_DEFAULT_VALUE = 20;

    /**
     * 默认单次请求返回的最大结果数量
     */
    public static int RETURN_COUNT_MAX_VALUE = 50;

    /**
     * 逗号连接符
     */
    public static String CONNECTED_COMMA = ",";

    /**
     * 场景-所有
     */
    public static String SCENE_ALL = "100000";

    /**
     * 场景-图文-所有
     */
    public static String SCENE_ARTICLE_ALL = "101000";

    /**
     * 场景-图文-首页
     */
    public static String SCENE_ARTICLE_HOMEPAGE = "101001";

    /**
     * 场景-图文-赚钱
     */
    public static String SCENE_ARTICLE_MONEYMAKING = "101002";

    /**
     * 场景-视频-所有
     */
    public static String SCENE_VIDEO_ALL = "102000";

    /**
     * 场景-视频-首页
     */
    public static String SCENE_VIDEO_HOMEPAGE = "102001";

    /**
     * 场景-视频-我的-可能喜欢
     */
    public static String SCENE_VIDEO_MY_MAY_LIKE = "102002";

    /**
     * 场景-视频-赚钱
     */
    public static String SCENE_VIDEO_MONEYMAKING = "102003";

    /**
     * 场景-短剧-所有
     */
    public static String SCENE_SHORT_VIDEO_ALL = "103000";

    /**
     * 场景-短剧-生活推荐
     */
    public static String SCENE_SHORT_VIDEO_LIFE = "103001";

    /**
     * 场景-问答-所有
     */
    public static String SCENE_QUESTION_ALL = "104000";

    /**
     * 场景-问答-首页知识列表
     */
    public static String SCENE_QUESTION_HOMEPAGE = "104001";

    /**
     * 场景-问答-赚钱知识列表
     */
    public static String SCENE_QUESTION_MONEYMAKING = "104002";

    /**
     * 场景-任务-赚钱首页
     */
    public static String SCENE_TASK_HOMEPAGE = "105001";

    /**
     * 场景-任务-任务广场
     */
    public static String SCENE_TASK_MONEYMAKING = "105002";

    /**
     * 场景-任务-分销广场
     */
    public static String SCENE_TASK_DISTRIBUTION = "105003";

    /**
     * 截取CONTENT的最大长度
     */
    public static Integer CONTENT_SUBSTRING = 2000;
}
