package com.sohu.third.aliyun.airec.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 电商行业-行为类型枚举
 */
@Getter
@AllArgsConstructor
public enum AliyunAirecEcommerceBehaviorTypeEnum {

    /**
     * 必填行为，且曝光行为数要大于点击行为数
     */
    EXPOSE("expose","曝光"),
    /**
     * 必填行为
     */
    CLICK("click","点击"),
    LIKE("like","点赞"),
    UNLIKE("unlike","踩"),
    COMMENT("comment","评论"),
    COLLECT("collect","收藏"),
    STAY("stay","停留时长"),
    SHARE("share","分享"),
    /**
     * 件数,单价（（英文逗号隔开）示例：1,10000
     * 单价：RMB，可精确到百分位（分）。
     */
    DOWNLOAD("cart","加购"),
    /**
     * 件数,单价（英文逗号隔开）示例：1,10000
     * 单价：RMB，可精确到百分位（分）一条购买行为中，只能对应一个item_id，一个订单多个item_id，需要进行拆分。
     */
    TIP("buy","购买"),
    /**
     * 离散的递增或递减的整数
     * 举例：若使用星级评价，1-5星表示好评度递增，则可设置对应关系为1星值取1，2星2，5星5。务必保证增减逻辑与实际好差评趋势对应。
     */
    SUBSCRIBE("evaluate","评价"),
    DISLIKE("dislike","负反馈");


    private String code;
    private String msg;
}
