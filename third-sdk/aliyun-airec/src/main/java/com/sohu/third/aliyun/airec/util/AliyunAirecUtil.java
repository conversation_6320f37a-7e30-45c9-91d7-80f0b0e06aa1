package com.sohu.third.aliyun.airec.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.airec.model.v20201126.PushDocumentRequest;
import com.aliyuncs.airec.model.v20201126.PushDocumentResponse;
import com.aliyuncs.airec.model.v20201126.RecommendRequest;
import com.aliyuncs.airec.model.v20201126.RecommendResponse;
import com.aliyuncs.http.FormatType;
import com.google.gson.Gson;
import com.sohu.third.aliyun.airec.constants.AliyunAirecConstant;
import com.sohu.third.aliyun.airec.domain.*;
import com.sohu.third.aliyun.airec.domain.bo.ISohuAiRecReqBo;
import com.sohu.third.aliyun.airec.domain.vo.ISohuAiRecResVo;
import com.sohu.third.aliyun.airec.domain.vo.SohuAiRecResultItemVo;
import com.sohu.third.aliyun.airec.enums.AliyunAirecContentCmdEnum;
import com.sohu.third.aliyun.airec.enums.AliyunAirecTableNameEnum;
import com.sohu.third.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * AliyunAire工具包
 * 参考文档：https://help.aliyun.com/zh/airec/airec/developer-reference/sdk-for-java?spm=a2c4g.11186623.0.i39
 */
@Slf4j
public class AliyunAirecUtil {
    private static final AliyunAirecClient CLIENT = SpringUtil.getBean(AliyunAirecClient.class);

    public static DefaultAcsClient getAcsClient() {
        return CLIENT.createAcsClient();
    }

    /**
     * 实例id
     *
     * @return
     */
    public static String getInstanceId() {
        return CLIENT.getInstanceId();
    }

    public static Boolean getEnable() {
        return CLIENT.getEnable() != null && CLIENT.getEnable();
    }


    /**
     * 推送数据，行业运营版、算法配置版
     * 参考文档：https://help.aliyun.com/zh/airec/airec/user-guide/push-data?spm=a2c4g.11186623.0.0.1c794b70bGxoI1
     *
     * @param tableNameEnum 需要推送的表
     * @param content       文档，详情见上文
     */
    public static PushDocumentResponse pushData(AliyunAirecTableNameEnum tableNameEnum, String content) {
        log.info("智能推荐推送数据 content:{}", content);
        if (!getEnable()) {
            return null;
        }
        DefaultAcsClient client = getAcsClient();
        PushDocumentRequest request = new PushDocumentRequest();
        request.setSysAcceptFormat(FormatType.JSON);
        //填入实例id
        request.setInstanceId(getInstanceId());
        //填入要上报的数据表名：user/item/behavior
        request.setTableName(tableNameEnum.getCode());
        request.setHttpContent(content.getBytes(), "UTF-8", FormatType.JSON);
        try {
            PushDocumentResponse acsResponse = client.getAcsResponse(request);
            log.info("智能推荐结果，id: {}; code:{}; msg {}", acsResponse.getRequestId(), acsResponse.getCode(), acsResponse.getMessage());
            return acsResponse;
        } catch (Exception e) {
            log.error(tableNameEnum.getCode() + "推送异常 " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 推送数据
     * init初始化时contentList size已限制500
     *
     * @param tableNameEnum  需要推送的表
     * @param contentCmdEnum content中必填字段，操作类型。详情见上文
     * @param contentList    文档，详情见上文 强制size小于等于500
     */
    public static PushDocumentResponse pushDataOfEntity(AliyunAirecTableNameEnum tableNameEnum, AliyunAirecContentCmdEnum contentCmdEnum, List<?> contentList) {
        List<Map<String, Object>> pushList = new ArrayList<>();
        for (Object obj : contentList) {
            Map<String, Object> map = BeanUtil.beanToMap(obj, true, true);
            Map<String, Object> pushMap = new HashMap<>();
            pushMap.put("cmd", contentCmdEnum.getCode());
            pushMap.put("fields", map);
            pushList.add(pushMap);
        }
        return pushData(tableNameEnum, JSONUtil.toJsonStr(pushList));
    }

    /**
     * 推送数据-用户表
     *
     * @param contentCmdEnum content中必填字段，操作类型。详情见上文
     * @param content        文档，详情见上文
     */
    public static PushDocumentResponse pushDataOfUser(AliyunAirecContentCmdEnum contentCmdEnum, AliyunAirecUser content) {
        List<AliyunAirecUser> contentList = new ArrayList<>();
        contentList.add(content);
        return pushDataOfUsers(contentCmdEnum, contentList);
    }

    /**
     * 推送数据-用户表-多个
     *
     * @param contentCmdEnum content中必填字段，操作类型。详情见上文
     * @param contentList    文档，详情见上文
     */
    public static PushDocumentResponse pushDataOfUsers(AliyunAirecContentCmdEnum contentCmdEnum, List<AliyunAirecUser> contentList) {
        return pushDataOfEntity(AliyunAirecTableNameEnum.USER, contentCmdEnum, contentList);
    }

    /**
     * 推送数据-内容行业-物品表
     *
     * @param contentCmdEnum content中必填字段，操作类型。详情见上文
     * @param content        文档，详情见上文
     */
    public static PushDocumentResponse pushDataOfContentItem(AliyunAirecContentCmdEnum contentCmdEnum, AliyunAirecContentItem content) {
        List<AliyunAirecContentItem> contentList = new ArrayList<>();
        contentList.add(content);
        return pushDataOfContentItems(contentCmdEnum, contentList);
    }

    /**
     * 推送数据-内容行业-物品表-多个
     *
     * @param contentCmdEnum content中必填字段，操作类型。详情见上文
     * @param contentList    文档，详情见上文
     */
    public static PushDocumentResponse pushDataOfContentItems(AliyunAirecContentCmdEnum contentCmdEnum, List<AliyunAirecContentItem> contentList) {
        return pushDataOfEntity(AliyunAirecTableNameEnum.ITEM, contentCmdEnum, contentList);
    }

    /**
     * 推送数据-内容行业-行为表
     *
     * @param contentCmdEnum content中必填字段，操作类型。详情见上文
     * @param content        文档，详情见上文
     */
    public static PushDocumentResponse pushDataOfContentBehavior(AliyunAirecContentCmdEnum contentCmdEnum, AliyunAirecContentBehavior content) {
        List<AliyunAirecContentBehavior> contentList = new ArrayList<>();
        contentList.add(content);
        return pushDataOfContentBehaviors(contentCmdEnum, contentList);
    }

    /**
     * 推送数据-内容行业-行为表-多个
     *
     * @param contentCmdEnum content中必填字段，操作类型。详情见上文
     * @param contentList    文档，详情见上文
     */
    public static PushDocumentResponse pushDataOfContentBehaviors(AliyunAirecContentCmdEnum contentCmdEnum, List<AliyunAirecContentBehavior> contentList) {
        return pushDataOfEntity(AliyunAirecTableNameEnum.BEHAVIOR, contentCmdEnum, contentList);
    }

    /**
     * 推送数据-电商行业-物品表
     *
     * @param contentCmdEnum content中必填字段，操作类型。详情见上文
     * @param contentList    文档，详情见上文
     */
    public static PushDocumentResponse pushDataOfEcommerceItem(AliyunAirecContentCmdEnum contentCmdEnum, List<AliyunAirecEcommerceItem> contentList) {
        return pushDataOfEntity(AliyunAirecTableNameEnum.ITEM, contentCmdEnum, contentList);
    }

    /**
     * 推送数据-电商行业-行为表
     *
     * @param contentCmdEnum content中必填字段，操作类型。详情见上文
     * @param contentList    文档，详情见上文
     */
    public static PushDocumentResponse pushDataOfEcommerceBehavior(AliyunAirecContentCmdEnum contentCmdEnum, List<AliyunAirecEcommerceBehavior> contentList) {
        return pushDataOfEntity(AliyunAirecTableNameEnum.BEHAVIOR, contentCmdEnum, contentList);
    }

    /**
     * 获取推荐结果
     *
     * @param request
     */
    public static RecommendResponse recommend(RecommendRequest request) {
        // 检查是否启用
        if (!getEnable()) {
            return null;
        }
        DefaultAcsClient client = getAcsClient();
        if (Objects.isNull(request.getInstanceId())) {
            request.setInstanceId(getInstanceId());
        }
        if (Objects.isNull(request.getReturnCount()) || request.getReturnCount() < 1) {
            request.setReturnCount(AliyunAirecConstant.RETURN_COUNT_DEFAULT_VALUE);
        } else if (request.getReturnCount() > AliyunAirecConstant.RETURN_COUNT_MAX_VALUE) {
            request.setReturnCount(AliyunAirecConstant.RETURN_COUNT_MAX_VALUE);
        }
        request.setSysAcceptFormat(FormatType.JSON);
        try {
            RecommendResponse acsResponse = client.getAcsResponse(request);
            log.info("智能推荐结果，id: {}; code: {}; msg: {}", acsResponse.getRequestId(), acsResponse.getCode(), acsResponse.getMessage());
            return acsResponse;
        } catch (Exception e) {
            log.error(request + "获取结果异常" + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取推荐结果
     *
     * @param request
     * @param rootRule 组合过滤规则
     */
    public static RecommendResponse recommend(RecommendRequest request, AliyunAirecJoinFilterRule rootRule) {
        if (!Objects.isNull(rootRule)) {
            String filterRuleString = new Gson().toJson(rootRule);
            filterRuleString = new String(Base64.getEncoder().encode(filterRuleString.getBytes()));
            filterRuleString = filterRuleString.replaceAll("\\+", "-");
            filterRuleString = filterRuleString.replaceAll("/", "_");
            filterRuleString = filterRuleString.replaceAll("=", ".");
            request.putQueryParameter("filter", filterRuleString);  //  添加filter参数
        }
        return recommend(request);
    }

    /**
     * 获取推荐结果
     *
     * @param rootRule 组合过滤规则
     * @param bo       通用请求参数
     * @return
     */
    public static RecommendResponse aiRecommend(AliyunAirecJoinFilterRule rootRule, ISohuAiRecReqBo bo) {
        RecommendRequest request = new RecommendRequest();
        request.setUserId(bo.getAiUserId());
        if (Objects.nonNull(bo.getAiRecImei())) {
            request.setImei(DigestUtil.md5Hex(bo.getAiRecImei()));
        }
        request.setReturnCount(bo.getAiReturnCount());
        request.setSceneId(bo.getAiRecSceneId());
        return recommend(request, rootRule);
    }

    /**
     * 获取推荐结果
     *
     * @param rootRule          组合过滤规则
     * @param bo                通用请求参数
     * @param localDataFunction 本地查询到的数据
     * @param <T>
     * @return
     */
    public static <T extends ISohuAiRecResVo> List<T> aiRecommendSingleType(AliyunAirecJoinFilterRule rootRule, ISohuAiRecReqBo bo, Function<List<String>, List<T>> localDataFunction) {
        List<T> resultList = new ArrayList<>();
        log.info("智能推荐查询规则 rule:{}, reqBo:{}", JSONUtil.toJsonStr(rootRule), JsonUtils.toJsonString(bo));
        RecommendResponse recResponse = aiRecommend(rootRule, bo);
        log.info("智能推荐查询结果 response:{}", JSONUtil.toJsonStr(recResponse));
        if (Objects.isNull(recResponse) || CollUtil.isEmpty(recResponse.getResult())) {
            return resultList;
        }
        List<String> itemIds = recResponse.getResult().stream().map(p -> p.getItemId()).collect(Collectors.toList());
        //按推荐结果集，获取明细信息，并与推荐结果集组合返回
        List<T> voList = localDataFunction.apply(itemIds);
        if (CollUtil.isNotEmpty(voList)) {
            Map<String, T> mapVo = voList.stream().collect(Collectors.toMap(T::getAiItemId, p -> p));
            for (RecommendResponse.ResultItem resultItem : recResponse.getResult()) {
                T vo = mapVo.get(resultItem.getItemId());
                if (Objects.nonNull(vo)) {
                    vo.setAiResultItem(BeanUtil.copyProperties(resultItem, SohuAiRecResultItemVo.class));
                    resultList.add(vo);
                }
            }
        }
        return resultList;
    }

    /**
     * 构建自研推荐
     *
     * @param sourceList
     * @param itemType   内容的类型
     * @param <T>
     */
    public static <T extends ISohuAiRecResVo> void buildAiRecommendSingleType(List<T> sourceList, String itemType) {
        if (CollUtil.isEmpty(sourceList)) {
            return;
        }
        for (T t : sourceList) {
            buildAiRecommend(t, itemType);
        }
    }

    /**
     * 构建自研推荐
     *
     * @param t
     * @param itemType 内容的类型
     * @param <T>
     */
    public static <T extends ISohuAiRecResVo> void buildAiRecommend(T t, String itemType) {
        if (t == null) {
            return;
        }
        SohuAiRecResultItemVo item = new SohuAiRecResultItemVo();
        item.setItemId(t.getAiItemId());
        item.setItemType(itemType);
        item.setTraceId("selfhold");
        item.setTraceInfo("1");
        t.setAiResultItem(item);
    }

}
