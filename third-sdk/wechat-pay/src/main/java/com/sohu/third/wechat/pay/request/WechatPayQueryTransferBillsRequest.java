package com.sohu.third.wechat.pay.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sohu.third.wechat.pay.annotation.Required;
import com.sohu.third.wechat.pay.response.WechatPayQueryTransferBillsResponse;
import lombok.Data;

/**
 * 查询转账账单电子回单接口 及 转账账单电子回单申请受理接口
 *
 * @author: lyw
 * @date: 2023/5/6 19:31
 * @version: 1.0.0
 */
@Data
public class WechatPayQueryTransferBillsRequest extends BaseWechatPayRequest<WechatPayQueryTransferBillsResponse> {

    private static final long serialVersionUID = 522565152886671848L;

    /**
     * <pre>
     * 字段名：商家批次单号
     * 变量名：out_batch_no
     * 是否必填：必填
     * 类型：String(32)
     * 示例值：plfk2020042013
     * 描述：商户系统内部的商家批次单号，在商户系统内部唯一。需要电子回单的批次单号
     * </pre>
     */
    @Required
    @JsonProperty(value = "out_batch_no")
    private String outBatchNo;

}
