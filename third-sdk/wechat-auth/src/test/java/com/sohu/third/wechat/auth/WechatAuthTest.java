package com.sohu.third.wechat.auth;

import cn.hutool.json.JSONUtil;
import com.sohu.third.wechat.auth.bean.WechatMiniAppConfig;
import com.sohu.third.wechat.auth.response.WechatAccessTokenResponse;
import com.sohu.third.wechat.auth.service.WechatAuthService;
import org.junit.Test;

/**
 * 微信小程序直播-商品相关
 *
 * <AUTHOR>
 * @create 2021-08-02 14:46
 **/
public class WechatAuthTest {

    public WechatMiniAppConfig miniAppConfig() {
        WechatMiniAppConfig config = new WechatMiniAppConfig();
        config.setAppId("wxfc2c052f0e3befd8");
        config.setSecret("f4174fe2a02b224ada523e16c51233d8");
        config.setAccessToken("68_JC-FmOxeUotvGF4ub7SVFkJG21R_3HoyCSIisZzkbrYejZIoswYMgc5DkhLPiOm6oL8uTcvg8TjhnpO3PimDAB9U8t0PNvjeULE5auOgCRZrXR5WMUgZQ2Ne0j8HMDaADASXO");
//        config.setAppId("wxf515e5a94a7fac65");
//        config.setSecret("ba7b8dae471a42ca572455ec20a04c17");
//        config.setAccessToken("60_1N39Gk2v3gdGWfsabuBArqsh76_pYzFEMhm1TrP2LLVwPLGWkW9DyNz6L52_wiczSdZHuj0_qgWEm-OPK4nokLEDO7tMAlX6PlPwx137-OWZBvqd1R60Bdm9Egp9n3UWj_wTeRu7rLHC7IHuQYPaACAMUL");
        return config;
    }

    @Test
    public void getAccessToken() {
        WechatAccessTokenResponse response = WechatAuthService.getAccessToken(miniAppConfig());
        System.out.println(JSONUtil.toJsonStr(response));
    }
}
