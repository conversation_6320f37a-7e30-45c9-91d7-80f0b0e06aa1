package com.sohu.third.core.util;

import cn.hutool.core.codec.Base62;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * AES 加密 & Base62 短码编码
 */
public class AESBase62Util {
    private static final String AES_KEY = "0123456789abcdef"; // 必须是 16、24、32 字节
    private static final String ALGORITHM = "AES/ECB/PKCS5Padding"; // 确保有填充

    /**
     * 加密并使用 Base62 编码
     */
    public static String encrypt(String data) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base62.encode(encryptedBytes); // 使用 Base62 编码
    }

    /**
     * Base62 解码并解密
     */
    public static String decrypt(String encryptedData) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, keySpec);

        byte[] decodedBytes = Base62.decode(encryptedData); // 先 Base62 解码
        byte[] decryptedBytes = cipher.doFinal(decodedBytes);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

    public static void main(String[] args) throws Exception {
        String json = "{\"type\":\"inviteGroup\",\"id\":958}";

        String encrypted = encrypt(json);
        System.out.println("加密后短码: " + encrypted);

        String decrypted = decrypt(encrypted);
        System.out.println("解密后JSON: " + decrypted);

        String ak = "LTAI5t9zUyDrKg7y3zVvxvjt";
        encrypted = encrypt(ak);
        System.out.println(encrypted);
        System.out.println(decrypt(encrypted));
    }
}
