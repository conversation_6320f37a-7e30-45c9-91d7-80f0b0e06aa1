<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zwztf.sdk</groupId>
    <artifactId>third-sdk</artifactId>
    <version>1.0.1</version>
    <packaging>pom</packaging>
    <description>三方SDK</description>

    <modules>
        <module>third-sdk-core</module>
        <module>alipay-auth</module>
        <module>alipay-pay</module>
        <module>wechat-auth</module>
        <module>wechat-pay</module>
        <module>wechat-profitsharing</module>
        <module>pingpong-pay</module>
        <module>tiktok-login</module>
        <module>tiktok-pay</module>
        <module>baidu-translate</module>
        <module>aliyun-audit</module>
        <module>aliyun-airec</module>
        <module>aliyun-video</module>
        <module>baidu-ai</module>
        <module>aliyun-ocr</module>
        <module>apple-pay</module>
        <module>airwallex</module>
        <module>paypal</module>
        <module>migu-novel</module>
        <module>songshu-novel</module>
        <module>fzs-virtualgoods</module>
        <module>oa-vc</module>
        <module>ai-sdk</module>
        <module>zxhuixuan-supply</module>
        <module>yidun-sdk</module>
        <!--        <module>baidu-qianfan</module>-->
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.version>3.8.1</maven.compiler.version>
        <versions-maven.version>2.7</versions-maven.version>
        <lombok.version>1.18.24</lombok.version>
        <jackson.version>2.14.2</jackson.version>
        <hutool-all.version>5.8.5</hutool-all.version>
        <slf4j-api.version>1.7.26</slf4j-api.version>
        <commons-lang3.version>3.8.1</commons-lang3.version>
        <thumbnailator.version>0.4.11</thumbnailator.version>
        <junit.version>4.13.1</junit.version>
        <httpclient.version>4.5.9</httpclient.version>
        <commons-io.version>2.11.0</commons-io.version>
    </properties>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.version}</version>
                <configuration>
                    <target>${maven.compiler.target}</target>
                    <source>${maven.compiler.source}</source>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <!--版本切换-->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>${versions-maven.version}</version>
                <configuration>
                    <generateBackupPoms>true</generateBackupPoms>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!--<distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>Nexus Release Repository</name>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
        </snapshotRepository>
    </distributionManagement>-->

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <!--<url>http://192.168.130.47:8081/repository/maven-public/</url>-->
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <!--<distributionManagement>
        <repository>
            <id>nexus</id>
            <url>http://192.168.130.47:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus</id>
            <url>http://192.168.130.47:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>-->
</project>
