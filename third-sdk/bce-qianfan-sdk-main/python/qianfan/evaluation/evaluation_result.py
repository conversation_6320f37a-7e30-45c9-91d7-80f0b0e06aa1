# Copyright (c) 2023 Baidu, Inc. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


"""
The result of a evaluation
"""

from typing import Any, Dict, Optional

from qianfan.dataset import Dataset


class EvaluationResult:
    """Evaluation Result"""

    def __init__(
        self,
        result_dataset: Optional[Dataset] = None,
        metrics: Optional[Dict[str, Dict[str, Any]]] = None,
    ):
        """
        instantiate an evaluation result

        Args:
            result_dataset (Optional[Dataset]):
                a dataset containing evaluation result, default to None
            metrics (Optional[Dict[str, Dict[str, Any]]]):
                overall evaluation metric collections, default to None
        """
        self.result_dataset = result_dataset
        self.metrics = metrics
