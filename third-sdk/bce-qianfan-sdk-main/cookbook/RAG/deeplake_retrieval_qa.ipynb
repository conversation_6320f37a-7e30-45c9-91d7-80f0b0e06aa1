#%% md
# 千帆与 DeepLake
#%% md
Deep Lake 是一个 AI 领域的向量数据库，存储了大量数据集和向量，可以用于大模型应用的开发。

本文将介绍如何利用千帆 SDK 和 DeepLake 实现检索式问答。
#%% md
本文使用的千帆SDK版本为
````
qianfan>=0.1.4
````
#%% md
## 1. 准备
#%% md
为了能够使用 DeepLake，我们需要先安装 DeepLake 的库。
本文使用的DeepLake版本为：
```
deeplake==3.8.6
```
#%%
!pip install deeplake
#%% md
## 2. 鉴权
#%%
import os
import qianfan

# 初始化千帆 SDK
os.environ["QIANFAN_AK"] = "your_ak"
os.environ["QIANFAN_SK"] = "your_sk"
#%% md
## 3. 准备数据集
#%%
import deeplake

# 可以从 DeepLake 上拉取数据集
ds = deeplake.load("hub://activeloop/cohere-wikipedia-22-sample")
ds.summary()

#%% md
## 4. 构建文本索引
#%% md
为了之后能够进行检索，我们需要先将数据集中的数据转换成向量，所以这里我们用千帆 Embedding 对数据进行转换。
#%%
from langchain.embeddings.baidu_qianfan_endpoint import QianfanEmbeddingsEndpoint
from langchain.vectorstores import DeepLake

dataset_path = 'wikipedia-embeddings-deeplake'
embedding = QianfanEmbeddingsEndpoint()
db = DeepLake(dataset_path, embedding=embedding, overwrite=True)

#%%
# 向数据库中增加数据的向量信息
batch_size = 100

nsamples = 10  # 这里仅用作测试，仅索引少量文本
for i in range(0, nsamples, batch_size):
    # find end of batch
    i_end = min(nsamples, i + batch_size)

    batch = ds[i:i_end]
    id_batch = batch.ids.data()["value"]
    text_batch = batch.text.data()["value"]
    meta_batch = batch.metadata.data()["value"]

    db.add_texts(text_batch, metadatas=meta_batch, ids=id_batch)

db.vectorstore.summary()
#%% md
## 5. 实现检索式 QA
#%% md
这里我们通过 LangChain 中的实现帮助我们快速实现该功能，LangChain 帮助我们封装了检索相关文本的流程，这里我们只需要调整最终向模型进行请求时的 prompt 模版。
#%%
# 设置 prompt 模版
from langchain.prompts.chat import (
    ChatPromptTemplate,
    HumanMessagePromptTemplate,
)

template = """Use the following pieces of context to answer the user's question. 
If you don't know the answer, just say that you don't know, don't try to make up an answer.
----------------
{context}
----------------
Now answer the question:
{question}
"""

messages = [
    HumanMessagePromptTemplate.from_template(template),
]
CHAT_PROMPT = ChatPromptTemplate.from_messages(messages)
#%%
from langchain.chains import RetrievalQA
from langchain.chat_models import QianfanChatEndpoint

qa = RetrievalQA.from_chain_type(
    llm=QianfanChatEndpoint(model='ERNIE-Bot'),
    chain_type_kwargs={
        "prompt": CHAT_PROMPT
    },
    chain_type="stuff", 
    retriever=db.as_retriever(), 
    verbose=True
)
#%%

query = 'Why does the military not say 24:00?'
qa.run(query)
#%% md
搞定！