#%% md
# 使用千帆平台训练一个作文批改的大模型

在线教育场景中，对于学生作文，通常我们会从作文的内容是否符合题意、作文结构是否严谨、作文是否存在缺点和扣分项等方面对我们的作文做出评判，并给我们打出最终的得分。其实大模型也可以成为一名点评专家。大模型有很好的格式遵循和风格遵循能力，我们将点评的要求或者模板"调教"给大模型，大模型就能按照我们的要求对一篇作文做出点评。

使用大模型对作文做出点评，可以很好的运用到在线教育的场景中，即可以成为老师的得力助手，也能够让学生知道作文还可以从哪些方面提升，大大节省我们的成本和时间。但是，未经过训练的基础模型，很难在具体场景中发挥出好的效果。

为了运行以下的代码，请首先通过 pip 安装千帆 Python SDK，并且设置相关的环境变量
#%%
!pip install -U "qianfan[dataset_base]"
#%%
import os

# 以下环境变量供千帆 OpenAI Adapter 使用
os.environ["QIANFAN_ACCESS_KEY"] = "your_qianfan_console_access_key"
os.environ["QIANFAN_SECRET_KEY"] = "your_qianfan_console_secret_key"
#%% md


举例来说，我们有以下的一个作文评分模板 Prompt 和待批改的作文：

#%%
from qianfan.common import Prompt

correction_template = """
你是一个高考语文阅卷老师，现在有一个高考作文题目和一篇待批改论文，需要你对这篇待批改论文进行评分。
要求：
1）请认真阅读作文批改要求和作文题目，对这篇待批改作文进行公正严格的批改和打分；
2）评分一定要严格，不能轻易给出高分。
3）最后返回内容要严格按照最后的输出格式。

一、作文批改要求：
高考作文评分批改分为基础等级、发展等级、关于作文的其他项评定
1、基础等级
基础等级分内容和表达两项。
1）内容项
具体评分规则如下：符合题意、中心突出、内容充实、思想健康、感情真挚为一等，可按16-20分酌情给分；符合题意、主题明确、内容较充实、思想健康、感情真实为二等，可按11-15分酌情给分；基本符合题意、中心基本明确、内容单薄、思想基本健康、感情基本真实为三等，可按6-10分酌情给分；偏离题意、中心不明确、内容不当、思想不健康、感情虚假为四等，可按0-5分酌情给分。
2）表达项
具体评分规则如下：符合文体要求、结构严谨、语言流畅、字迹工整为一等，可按16-20分酌情给分；符合文体要求、结构完整、语言通顺、字迹清楚为二等，可按11-15分酌情给分；基本符合文体要求、结构基本完整、语言基本通顺、字迹基本清楚为三等，可按6-10分酌情给分；不符合文体要求、结构混乱、语言不通顺语病多、字迹潦草难辨为四等，可按0-5分酌情给分。
2、发展等级
基础等级分要与发展等级分相匹配，发展等级分不能跨越基础等级的得分等级。
具体评分规则如下：深刻、丰富、有文采、有创意为一等，可按16-20分酌情给分；较深刻、较丰富、较有文采、较有创意为二等，可按11-15分酌情给分；略显深刻、略显丰富、略显文采、略显创意为三等，可按6-10分酌情给分；个别语句有深意、个别例子较好、个别语句较精彩、个别地方有深意为四等，可按0-5分酌情给分。
3、关于作文的其他项评定
1）扣分项评定
出现错别字，1个错别字扣1分，重复不计，扣完5分为止;标点符号出现3处以上错误的酌情扣分;不足字数者，每少50字扣1分;无标题扣2分。
2）残篇评定
400字以上的文章，按评分标准评分，扣字数分。(少50个字扣1分)
400字以下的文章，20分以下评分，不再扣字数分。
200字以下的文章，10分以下评分，不再扣字数分。
只写一两句话的，给1分或2分，不评0分。
只写标题的，给1分或2分，不评0分。
完全空白的，评0分。

二、作文题目：
{{title}}

三、待批改作文
{{content}}

四、输出格式
{"详细解析":{"内容项": {"解析": "xxxxxx。","等级": "xx等","得分": "xx分"},"表达项": {"解析": "xxxxxx。","等级": "xx等","得分": "xx分"},"发展等级": {"解析": "xxxxxx。","等级": "xx等","得分": "xx分"},"扣分项和残篇评定": {"解析": "xxxxxx。","扣分": "xx分"}},"缺点和改进意见": {"缺点": "xxxxxx。","改进意见": "xxxxxxx。"},"最终得分": "xx分"}
"""

correction_prompt = Prompt(correction_template, identifier="{{}}")

render_dict = {
    "title": "你注意到了吗？装鲜牛奶的容器一般是方盒子，装矿泉水的容器一般是圆瓶子，装酒圆瓶子又一般放在方盒子里，方圆之间，各得其妙，古诗云：方圆虽异器，功用信具呈。人生也是如此，所谓：上善若水任方圆。以方圆为话题，根据此材料，题目自拟写作文，字数不少于800字。",
    "content": """
方圆之间的人生智慧

“方有止，圆有旋。”这句古人的智慧结晶，揭示了方与圆两种形态背后的深刻内涵。在生活中，我们常常见到方形的容器装着鲜牛奶，圆形的瓶子则装着矿泉水，而圆形的酒瓶又常常被放置在方形的盒子里。这些看似简单的形状，实际上蕴含着人生的哲理。

方，代表着规矩、原则和稳定。它象征着秩序和安定，是我们生活中不可或缺的一部分。在人的成长过程中，我们需要遵循各种规矩，学会遵守社会的秩序，这样才能在社会中立足。正如牛奶需要方形的容器来保持稳定一样，我们的人生也需要方正的品格来支撑。

然而，人生并非只有方的一面。圆，代表着变通、灵活和包容。它象征着和谐与圆满，是我们在面对复杂世界时的有力武器。我们需要学会圆滑处事，善于变通，这样才能在人生的道路上走得更远。就像矿泉水需要圆形的瓶子来适应各种环境一样，我们的人生也需要圆润的智慧来应对各种挑战。

方圆之间，各得其妙。在人生的道路上，我们需要既要方正又要圆润。我们要有坚定的原则和信念，同时也要学会适应环境，灵活应对。这样才能在人生的舞台上大放异彩。

上善若水任方圆。水，是世界上最柔软的物质，却能穿透坚硬的石头。这就是因为水懂得方圆之间的智慧。它既可以是方形的湖泊，也可以是圆形的河流，还可以是无形的雾气。水无常形，但却能包容万物。同样，我们也要有水的智慧，懂得在方圆之间寻找平衡，这样才能在人生的道路上游刃有余。

总之，方圆之间的人生智慧是我们每个人都需要学习和领悟的。我们要学会在坚持原则和灵活变通之间找到平衡，这样才能在人生的道路上不断前行。同时，我们也要像水一样包容万物，接纳不同的观点和文化，让自己的人生更加丰富多彩。

在这个充满变化和挑战的世界里，我们需要不断学习和成长，不断提升自己的能力和素质。只有这样，我们才能在方圆之间的人生舞台上展现出自己的风采和智慧。让我们一起努力，成为拥有方圆智慧的人，为自己的人生添彩！
    """
}
#%% md
然后我们使用该 Prompt，对基础模型进行提问，要求它对上述作文按照要求进行批改。此处我们使用 ERNIE-Speed-8K 作为基础模型
#%%
import re

from qianfan import ChatCompletion

cc = ChatCompletion(model="ERNIE-Speed-8K")

result = cc.do([{"content": correction_prompt.render(**render_dict)[0], "role": "user"}])

print(result.body["result"] + "\n")

print(re.search("^```json([\s\S]*)\n```$", result.body["result"]).group(1))
#%% md
对于该篇文章，直接调用模型的评分为52分。从实际情况看，文章针对方和圆的含义及举例阐释了方圆的含义，但是内容的深度、文章的文采、表现力都不足以达到高分的水平。因此，为了获得更好的效果，以及实现更高效的生成过程，我们需要专门训练一个模型。
#%% md
# 1. 准备数据集

众所周知，训练模型时需要提前准备好相关的数据集，而数据集的获取通常是一个耗时耗力的过程。这不仅对于数据的数量有要求，为了让大模型的输出质量更符合我们的预期，数据的文本质量也有一定要求。

针对数据获取难的问题，千帆平台针对一众细分领域场景提供了预置数据集，用户开箱即可用来训练大模型。本次的作文批改场景中，我们也会使用千帆平台提供的作文批改训练数据集和评估数据集，分别用于作文批改模型的训练和评估。

使用千帆 Python SDK，我们可以很方便地加载数据集。
#%%
import json
from qianfan.dataset import Dataset

# 加载训练用的预置数据集
train_ds = Dataset.load(qianfan_dataset_id="ds-553hczysf3um4cc9")
# 加载评估用的预置数据集
eval_ds = Dataset.load(qianfan_dataset_id="ds-6ubasnsry5pa4azi")
#%% md
# 2. 准备训练参数

训练模型之前，我们需要定义的是一些超参数，比如学习率、训练轮数等，不同的超参数会导致最终训练出来的模型在测试集上的表现不同。基于经验，我们在下面提供了一个，基于百度的轻量级基础模型 ERNIE-Speed 的 SFT 超参数配置。

ERNIE Speed是百度2024年最新发布的自研高性能大语言模型，通用能力优异，适合作为基座模型进行精调，更好地处理特定场景问题，同时具备极佳的推理性能。SFT 则代表监督微调。
#%%
from qianfan.trainer.configs import TrainConfig

# 针对模型进行 SFT 有监督微调的参数配置
train_config=TrainConfig(
    peft_type="FullFineTuning",
    max_seq_len=4096,
    epoch=5,
    learning_rate=0.00003,
    logging_steps=1,
    warmup_ratio=0.1,
    weight_decay=0.0001,
)

# 如果用户想尝试使用 LoRA 的方式进行微调，可以尝试使用下面的配置
# 取消注释使用

# train_config=TrainConfig(
#     peft_type="LoRA",
#     max_seq_len=4096,
#     epoch=5,
#     learning_rate=0.00003,
#     logging_steps=1,
#     warmup_ratio=0.1,
#     weight_decay=0.0001,
#     lora_rank=8,
#     lora_all_linear=True
# )
#%% md
# 3. 发起训练

在准备好上述三种组件之后，我们就可以开始训练了。
#%%
from qianfan.trainer import LLMFinetune

trainer = LLMFinetune(
    train_type="ERNIE-Speed-8K",
    dataset=train_ds,
    eval_dataset=eval_ds,
    train_config=train_config,
)

training_result = trainer.run()
#%% md
# 4. 查看结果

训练完成后，我们可以从返回的对象中拿到一系列的信息：
#%%
print(training_result.output)
#%% md
例如，我们可以查看训练出来的模型的版本 ID
#%%
print(training_result.output["model_version_id"])
#%% md
# 5. 准备评估

在训练完成之后，我们还需要对微调后的模型进行评估，以确定模型是否已经收敛且能实现我们所期望的效果。千帆 Python SDK 提供了模型评估的能力，用户可以使用千帆平台的预置评估能力，或者自行编写评估代码，来满足自身的评估需求。

在这里，我们选择实现一个简单的自定义评估器，来评估微调后的模型是否有遵循我们的输出格式，以及各项评估指标，大模型输出和预期输出之间的差距。
#%%
from typing import Any, Dict, List, Union

import numpy as np

from qianfan.dataset import Dataset
from qianfan.evaluation.evaluator import LocalEvaluator
from qianfan.resources import Embedding

def _convert_str_to_int(str_score: str) -> int:
    try:
        return int(str_score[:-1])
    except:
        return 0
    
embedding = Embedding(query_per_second=5)
    
def get_qianfan_embedding(content: str) -> np.array:
    return np.array(embedding.do([content]).body["data"][0]["embedding"])

def get_cosine_similarity(content1: str, content2: str) -> float:
    vec1 = get_qianfan_embedding(content1)
    vec2 = get_qianfan_embedding(content2)

    return vec1.dot(vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))

class EssayEvaluator(LocalEvaluator):

    def evaluate(self, input: Union[str, List[Dict[str, Any]]], reference: str, output: str) -> Dict[str, Any]:
        try:
            try:
                judge_result: Dict[str, Any] = json.loads(output)
            except:
                # 兼容可能的 Markdown 输出
                judge_result: Dict[str, Any] = json.loads(re.search(r"```json\n([\s\S]*)\n```", output).group(1))

            reference_result: Dict[str, Any] = json.loads(reference)
            return {
                "遵守格式": True,

                "内容评分等级一致": judge_result["详细解析"]["内容项"]["等级"] ==
                                    reference_result["详细解析"]["内容项"]["等级"],
                "内容点评相似度": get_cosine_similarity(judge_result["详细解析"]["内容项"]["解析"],
                                                        reference_result["详细解析"]["内容项"]["解析"]),
                "内容评分分差": abs(
                    _convert_str_to_int(judge_result["详细解析"]["内容项"]["得分"]) - _convert_str_to_int(
                        reference_result["详细解析"]["内容项"]["得分"])),

                "表达评分等级一致": judge_result["详细解析"]["表达项"]["等级"] ==
                                    reference_result["详细解析"]["表达项"]["等级"],
                "表达点评相似度": get_cosine_similarity(judge_result["详细解析"]["表达项"]["解析"],
                                                        reference_result["详细解析"]["表达项"]["解析"]),
                "表达评分分差": abs(
                    _convert_str_to_int(judge_result["详细解析"]["表达项"]["得分"]) - _convert_str_to_int(
                        reference_result["详细解析"]["表达项"]["得分"])),

                "发展评分等级一致": judge_result["详细解析"]["发展等级"]["等级"] ==
                                    reference_result["详细解析"]["发展等级"]["等级"],
                "发展点评相似度": get_cosine_similarity(judge_result["详细解析"]["发展等级"]["解析"],
                                                        reference_result["详细解析"]["发展等级"]["解析"]),
                "发展评分分差": abs(
                    _convert_str_to_int(judge_result["详细解析"]["发展等级"]["得分"]) - _convert_str_to_int(
                        reference_result["详细解析"]["发展等级"]["得分"])),

                "扣分解析相似度": get_cosine_similarity(judge_result["详细解析"]["扣分项和残篇评定"]["解析"],
                                                        reference_result["详细解析"]["扣分项和残篇评定"]["解析"]),
                "扣分项扣分分差": abs(
                    _convert_str_to_int(judge_result["详细解析"]["扣分项和残篇评定"]["扣分"]) - _convert_str_to_int(
                        reference_result["详细解析"]["扣分项和残篇评定"]["扣分"])),

                "总分分差": abs(
                    _convert_str_to_int(judge_result["最终得分"]) - _convert_str_to_int(reference_result["最终得分"])),
            }
        except:
            print(output)
            return {
                "遵守格式": False,
                "内容评分等级一致": False,
                "内容点评相似度": -1,
                "内容评分分差": -1,
                "表达评分等级一致": False,
                "表达点评相似度": -1,
                "表达评分分差": -1,
                "发展评分等级一致": False,
                "发展点评相似度": -1,
                "发展评分分差": -1,
                "扣分解析相似度": -1,
                "扣分项扣分分差": -1,
                "总分分差": -1
            }

    def summarize(self, metric_dataset: Dataset) -> Optional[Dict[str, Any]]:
        statistics_dict: Dict[str, Any] = {}
        count_dict: Dict[str, int] = {}

        for line in metric_dataset.list():
            for k, v in line.items():
                if isinstance(v, bool):
                    if f"{k}占比" not in statistics_dict:
                        statistics_dict[f"{k}占比"] = 0
                        count_dict[f"{k}占比"] = 0

                    statistics_dict[f"{k}占比"] += 1 if v else 0
                    count_dict[f"{k}占比"] += 1

                elif isinstance(v, (int, float)):
                    if f"{k}平均值" not in statistics_dict:
                        statistics_dict[f"{k}平均值"] = 0
                        count_dict[f"{k}平均值"] = 0

                    if v != -1 and v != -1.0:
                        statistics_dict[f"{k}平均值"] += v
                        count_dict[f"{k}平均值"] += 1

        for k, v in statistics_dict.items():
            statistics_dict[k] = v / count_dict[k]

        return statistics_dict
#%%
from qianfan.evaluation import EvaluationManager
from qianfan.model import Model

em = EvaluationManager(local_evaluators=[EssayEvaluator()])

# 这一步骤会使用模型进行批量推理，再对批量推理的结果进行批量评估
eval_result = em.eval([Model(version_id=training_result.output["model_version_id"])], eval_ds)
#%%
print(json.dumps(eval_result.metrics, ensure_ascii=False))
#%% md
可以看到，评估得到的模型，在回答的稳定性上，较之前的基础模型有所提升，且打分结果更贴近人工打分的结果。

我们还可以将评估的结果数据集保存到本地，方便我们进行进一步的分析
#%%
eval_result.result_dataset.save(data_file="local.json")
#%% md
比如说，我们可以将各项指标分差进行汇总计算，最后使用可视化的方式进行展示。

为了使得数据能够有对比，我们还可以使用基础模型的 ERNIE-Speed-8K ，在评估集上也做相同的评估，以佐证我们的训练效果
#%%
og_model_eval_result = em.eval([Model(version_id="amv-pzqtzdspm77m")], eval_ds)
#%%
!pip install tabulate
#%%
from tabulate import tabulate

sft_model_tag = list(eval_result.metrics.keys())[0]

cmp_dict = {k: [eval_result.metrics[sft_model_tag][k], v] for k, v in list(og_model_eval_result.metrics.values())[0].items()}

print(tabulate(cmp_dict, headers='keys', tablefmt='fancy_grid', showindex=("EB-Speed-SFT", "EB-Speed")))

cmp_entry_dict = {
    "输入的 Prompt": [eval_result.result_dataset[0]["input_prompt"], None, None],
    "预期回答与大模型回答": [eval_result.result_dataset[0]["expected_output"], eval_result.result_dataset[0]["llm_output"], og_model_eval_result.result_dataset[0]["llm_output"]],
}

print(tabulate(cmp_entry_dict, headers='keys', tablefmt='fancy_grid', showindex=("原始数据", "EB-Speed-SFT", "EB-Speed")))

#%% md
在以上训练评估的基础上，我们可以对模型的能力进行系统的评价->优化，直到我们的模型达到我们的期望，就通过以下方式进行服务的部署以实现线上的生产调用：
#%%
#-# cell_skip
from qianfan.model import Service, DeployConfig
from qianfan.model.consts import ServiceType
from qianfan.resources.console.consts import DeployPoolType

sft_svc: Service = Model(version_id="amv-pzqtzdspm77m").deploy(DeployConfig(
    name="essay_correct",
    endpoint_prefix="essaycor",
    replicas=1,
    pool_type=DeployPoolType.PrivateResource,
    service_type=ServiceType.Chat,
    # step: x,
))

chat_comp: ChatCompletion = sft_svc.get_res()
sft_chat_resp = chat_comp.do([{"content": correction_prompt.render(**render_dict)[0], "role": "user"}])
sft_chat_resp["result"]
