#%% md
# SDK 自动遗忘过长的对话历史

模型对于输入的对话 token 长度有限制，当长度过长时会报错，为解决该问题，SDK 支持自动遗忘过长的对话历史，仅保留最近的对话历史。

注意：需要 SDK 版本 >= 0.3.3
#%%
import qianfan

messages = [
    {
        "role": "user",
        "content": "你好" * 10000, # 设置了特别长的回复
    },
    {
        "role": "assistant",
        "content": "你好",
    },
    {
        "role": "user",
        "content": "介绍一下文心一言",
    }
]
#%% md
只需要在调用模型时，传入 `truncate_overlong_msgs=True`，SDK 就会自动遗忘过长的的消息。
#%%
resp = qianfan.ChatCompletion(model="ERNIE-Bot").do(messages, truncate_overlong_msgs=True)
#%% md
从日志中可以看到，最早的两条消息被截断了，其中第一条是过长的消息，第二条则由于其为模型的回复，不能作为第一条消息而被截断。同时，我们也可以从调试信息中确定所发送的内容。
#%%
resp.request.json_body['messages']
#%% md
还可以通过 `get_model_info` 方法获取模型所支持的最大长度。
#%%
qianfan.ChatCompletion.get_model_info("ERNIE-Bot").max_input_chars
#%%
qianfan.ChatCompletion.get_model_info("ERNIE-Speed").max_input_tokens
#%%
