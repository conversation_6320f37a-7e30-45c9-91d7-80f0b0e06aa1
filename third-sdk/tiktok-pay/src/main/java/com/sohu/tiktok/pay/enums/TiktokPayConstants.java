package com.sohu.tiktok.pay.enums;

/**
 * 抖音支付链接
 *
 * @author: zc
 * @date: 2023/6/3 09:23
 * @version: 1.0.0
 */
public interface TiktokPayConstants {

    /**
     * 抖音交易相关请求地址
     */
    interface TradeUrl {

        /**
         * 正式环境抖音支付预下单请求地址--POST
         * app_id维度限流150QPS，thirdparty_id维度限流150QPS
         */
        String TIK_TOK_PAY_URL = "https://developer.toutiao.com/api/apps/ecpay/v1/create_order";

        /**
         * 正式抖音支付结果查询--POST
         * app_id维度限流150QPS，thirdparty_id维度限流150QPS
         */
        String TIK_TOK_QUERY_ORDER_URL = "https://developer.toutiao.com/api/apps/ecpay/v1/query_order";

        /**
         * 正式环境抖音退款请求地址--POST
         * 50QPS(小程序app_id维度)
         */
        String TIK_TOK_REFUND_URL = "https://developer.toutiao.com/api/apps/ecpay/v1/create_refund";

        /**
         * 正式环境抖音退款结果查询请求地址--POST
         * 30QPS(小程序app_id维度)
         */
        String TIK_TOK_REFUND_QUERY_URL = "https://developer.toutiao.com/api/apps/ecpay/v1/query_refund";

    }

}
