<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zwztf.sdk</groupId>
        <artifactId>third-sdk</artifactId>
        <version>1.0.1</version>
    </parent>

    <artifactId>yidun-sdk</artifactId>
    <description>易盾 sdk</description>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-boot.version>2.7.13</spring-boot.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.zwztf.sdk</groupId>
            <artifactId>third-sdk-core</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <!-- json处理 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.78</version>
        </dependency>
        <dependency>
            <groupId>com.netease.yidun</groupId>
            <artifactId>yidun-java-sdk</artifactId>
            <version>1.4.14</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <version>${spring-boot.version}</version>
        </dependency>
    </dependencies>
</project>
