package com.sohu.third.yidun.service;

import com.netease.yidun.sdk.antispam.AntispamRequester;
import com.netease.yidun.sdk.antispam.audio.AudioClient;
import com.netease.yidun.sdk.antispam.audio.check.async.v4.request.AudioAsyncCheckRequest;
import com.netease.yidun.sdk.antispam.audio.check.async.v4.response.AudioAsyncCheckResponse;
import com.sohu.third.yidun.object.TransformObjet;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/6/17 17:17
 */
@Component
public class AudioCheckService extends AbstractService {

    public static AudioAsyncCheckResponse.AudioSubmitV4Result asyncAudio(String secretId, String secretKey, String businessId, TransformObjet object, String callbackUrl) {
        // 实例化一个requester，入参需要传入易盾内容安全分配的secretId，secretKey
        AntispamRequester antispamRequester = createAntispamRequester(secretId, secretKey);

        // 实例化发起请求的client对象
        AudioClient audioClient = AudioClient.getInstance(antispamRequester);

        // 实例化请求对象
        AudioAsyncCheckRequest checkRequest = new AudioAsyncCheckRequest();
        // 根据需要设置请求的检测节点，默认杭州
        checkRequest.setRegionCode("cn-hangzhou");
        // 设置易盾内容安全分配的businessId
        checkRequest.setBusinessId(businessId);
        // 必填，要检测的音频 url
        checkRequest.setUrl(object.getContent());
        checkRequest.setDataId(object.getDataId());
        // 非必填，建议设置获取最新的检测结果。主动回调数据接口超时时间默认设置为2s，为了保证顺利接收数据，需保证接收接口性能稳定并且保证幂等性。
        checkRequest.setCallbackUrl(callbackUrl);
//        // 非必填，检测去重字段，填入后使用此字段去重，如果没填入则默认使用url进行去重
//        checkRequest.setUniqueKey("检测去重字段");
        // 其他字段详细填写信息可参考官网文档 帮助中心 -> 音频检测 -> 点播音频接口
        AudioAsyncCheckResponse checkResponse = null;
        try {
            // 发起异步检测的请求
            checkResponse = audioClient.asyncCheckAudio(checkRequest);
        } catch (Exception e) {
            return null;
        }

        if (checkResponse != null && checkResponse.getCode() == 200) {
            AudioAsyncCheckResponse.AudioSubmitV4Result submitResult = checkResponse.getResult();
            if (submitResult != null && submitResult.getStatus() != null) {
                // 提交成功
//                if (submitResult.getStatus() == 0) {
//                    // 获取检测任务 id
//                    String taskId = submitResult.getTaskId();
//                    // 获取缓冲池数量
//                    Integer dealingCount = submitResult.getDealingCount();
//                } else if (submitResult.getStatus() == 1) {
//                    // 提交失败，可重试提交
//                }
                return submitResult;
            }
        }
        return null;
    }
}
