package com.sohu.third.yidun.service;

import com.alibaba.fastjson.JSONObject;
import com.netease.yidun.sdk.antispam.AntispamRequester;
import com.netease.yidun.sdk.antispam.text.TextClient;
import com.netease.yidun.sdk.antispam.text.v5.check.async.single.TextAsyncCheckRequest;
import com.netease.yidun.sdk.antispam.text.v5.check.async.single.TextAsyncCheckResponse;
import com.netease.yidun.sdk.antispam.text.v5.check.async.single.TextAsyncCheckResult;
import com.netease.yidun.sdk.antispam.text.v5.check.sync.batch.TextBatchCheckRequest;
import com.netease.yidun.sdk.antispam.text.v5.check.sync.batch.TextBatchCheckResponse;
import com.netease.yidun.sdk.antispam.text.v5.check.sync.single.TextCheckRequest;
import com.netease.yidun.sdk.antispam.text.v5.check.sync.single.TextCheckResponse;
import com.netease.yidun.sdk.antispam.text.v5.check.sync.single.TextCheckResult;
import com.netease.yidun.sdk.antispam.text.v5.check.sync.single.TextCheckSceneRequest;
import com.sohu.third.yidun.object.TransformObjet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


@Component
@Slf4j
public class TextCheckService extends AbstractService {


    public static TextCheckResult.Antispam singleSyncText(String secretId, String secretKey, String businessId, TransformObjet object) {
        // 实例化一个requester，入参需要传入易盾内容安全分配的secretId，secretKey
        AntispamRequester antispamRequester = createAntispamRequester(secretId, secretKey);
        // 实例化发起请求的client对象
        TextClient textClient = TextClient.getInstance(antispamRequester);
        // 实例化请求对象
        TextCheckRequest checkRequest = new TextCheckRequest();
        // 设置易盾内容安全分配的businessId
        checkRequest.setBusinessId(businessId);
        // 根据需要设置请求的检测节点，默认杭州，现阶段只支持检测接口
        checkRequest.setRegionCode("cn-hangzhou");
        // 设置检测内容
        checkRequest.setDataId(object.getDataId());
        checkRequest.setContent(object.getContent());
        // 请求对象中的其他参数如果有需要，请参考官方接口文档中字段说明，按需添加
        TextCheckResponse checkResponse = null;
        try {
            // 发起同步检测的请求
            checkResponse = textClient.syncCheckText(checkRequest);
        } catch (Exception e) {
            return null;
        }
        if (checkResponse != null && checkResponse.getCode() == 200) {
            // 获取文本的检测结果，具体返回字段的说明，请参考官方接口文档中字段说明
            TextCheckResult textResult = checkResponse.getResult();
            return textResult.getAntispam();
        }
        return null;
    }

    public static TextAsyncCheckResult.CheckText singleAsyncText(String secretId, String secretKey, String businessId, TransformObjet object, String callbackUrl) {
        // 实例化一个requester，入参需要传入易盾内容安全分配的secretId，secretKey
        AntispamRequester antispamRequester = createAntispamRequester(secretId, secretKey);
        // 实例化发起请求的client对象
        TextClient textClient = TextClient.getInstance(antispamRequester);
        // 实例化请求对象
        TextAsyncCheckRequest checkRequest = new TextAsyncCheckRequest();
        // 设置易盾内容安全分配的businessId
        checkRequest.setBusinessId(businessId);
        // 根据需要设置请求的检测节点，默认杭州，现阶段只支持检测接口
        checkRequest.setRegionCode("cn-hangzhou");
        // 设置检测内容
        checkRequest.setDataId(object.getDataId());
        checkRequest.setContent(object.getContent());
        checkRequest.setCallbackUrl(callbackUrl);
        // 请求对象中的其他参数如果有需要，请参考官方接口文档中字段说明，按需添加
        TextAsyncCheckResponse checkResponse = null;
        try {
            // 发起同步检测的请求
            checkResponse = textClient.asyncCheckText(checkRequest);
        } catch (Exception e) {
            return null;
        }
        if (checkResponse != null && checkResponse.getCode() == 200) {
            return checkResponse.getResult().getCheckTexts().get(0);
//            for (TextAsyncCheckResult.CheckText textResult : checkResponse.getResult().getCheckTexts()) {
//                // 根据需要获取每个文本的检测结果，具体返回字段的说明，请参考官方接口文档中字段说明
//
//            }
        }
        return null;
    }

    public static void batchText(String secretId, String secretKey, String businessId, List<TransformObjet> objects) {
        // 实例化一个requester，入参需要传入易盾内容安全分配的secretId，secretKey
        AntispamRequester antispamRequester = createAntispamRequester(secretId, secretKey);
        // 实例化发起请求的client对象
        TextClient textClient = TextClient.getInstance(antispamRequester);
        // 实例化请求对象
        TextBatchCheckRequest checkRequest = new TextBatchCheckRequest();
        // 设置易盾内容安全分配的businessId
        checkRequest.setBusinessId(businessId);

        // 根据需要设置请求的检测节点，默认杭州，现阶段只支持检测接口
        checkRequest.setRegionCode("cn-hangzhou");
        // 设置检测内容
        checkRequest.setTexts(createTexts(objects));
        // 请求对象中的其他参数如果有需要，请参考官方接口文档中字段说明，按需添加
        TextBatchCheckResponse checkResponse = null;
        try {
            // 发起批量同步检测的请求
            checkResponse = textClient.syncBatchCheckText(checkRequest);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (checkResponse != null && checkResponse.getCode() == 200) {
            // 获取文本的检测结果，具体返回字段的说明，请参考官方接口文档中字段说明
            for (TextCheckResult textResult : checkResponse.getResult()) {
                // 根据需要获取每个文本的检测结果，具体返回字段的说明，请参考官方接口文档中字段说明
            }
        }
    }

    /**
     * 创建批量文本数据
     */
    private static List<TextCheckSceneRequest> createTexts(List<TransformObjet> contents) {
        List<TextCheckSceneRequest> textObj = new ArrayList<>();
        for (TransformObjet content : contents
        ) {
            TextCheckSceneRequest request = new TextCheckSceneRequest();
            request.setDataId(content.getDataId());
            request.setContent(content.getContent());
            // 请求对象中的其他参数如果有需要，请参考官方接口文档中字段说明，按需添加
            textObj.add(request);
        }
        return textObj;
    }

    public static void main(String[] args) {
        // 实例化一个requester，入参需要传入易盾内容安全分配的secretId，secretKey
        AntispamRequester antispamRequester = createAntispamRequester("f87f85a5fbb21b2f93f666f3a886c653", "97ca9e910741790bfcb4ab99b9f25fc3");
        // 实例化发起请求的client对象
        TextClient textClient = TextClient.getInstance(antispamRequester);
        // 实例化请求对象
        TextCheckRequest checkRequest = new TextCheckRequest();
        // 设置易盾内容安全分配的businessId
        checkRequest.setBusinessId("efa4785f183812fd9f4127a8adbd36d8");
        // 根据需要设置请求的检测节点，默认杭州，现阶段只支持检测接口
        checkRequest.setRegionCode("cn-hangzhou");
        // 设置检测内容
        checkRequest.setDataId(UUID.randomUUID().toString());
        checkRequest.setContent("33333");
        // 请求对象中的其他参数如果有需要，请参考官方接口文档中字段说明，按需添加
        TextCheckResponse checkResponse = null;
        try {
            // 发起同步检测的请求
            System.out.println("入参:" + JSONObject.toJSONString(checkRequest));
            checkResponse = textClient.syncCheckText(checkRequest);
            System.out.println("返回结果:" + JSONObject.toJSONString(checkResponse));
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (checkResponse != null && checkResponse.getCode() == 200) {
            // 获取文本的检测结果，具体返回字段的说明，请参考官方接口文档中字段说明
            TextCheckResult textResult = checkResponse.getResult();
        }
    }
}
