package com.sohu.third.yidun.service;

import com.netease.yidun.sdk.antispam.AntispamRequester;
import com.netease.yidun.sdk.antispam.crawler.CrawlerClient;
import com.netease.yidun.sdk.antispam.crawler.v3.submit.request.CrawlerResourceSubmitV3Request;
import com.netease.yidun.sdk.antispam.crawler.v3.submit.response.CrawlerResourceSubmitV3Response;
import com.sohu.third.yidun.object.TransformObjet;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/6/17 17:43
 */
@Component
public class LinkCheckService extends AbstractService {

    public static CrawlerResourceSubmitV3Response.CrawlerResourceSubmitResult asyncLink(String secretId, String secretKey, String businessId, TransformObjet object, String callbackUrl) {

        // 实例化一个requester，入参需要传入易盾内容安全分配的secretId，secretKey
        AntispamRequester antispamRequester = createAntispamRequester(secretId, secretKey);

        // 实例化发起请求的client对象
        CrawlerClient crawlerClient = CrawlerClient.getInstance(antispamRequester);

        // 实例化请求对象
        CrawlerResourceSubmitV3Request request = new CrawlerResourceSubmitV3Request();
        request.setUrl(object.getContent());
        request.setCheckFlags(Arrays.asList(1, 2, 4, 5, 6));
        request.setDataId(object.getDataId());
        request.setCallbackUrl(callbackUrl);
//        request.setAccount("account_123");
        // 请求对象中的其他参数如果有需要，请参考官方接口文档中字段说明，按需添加
        CrawlerResourceSubmitV3Response response = null;
        try {
            // 发起同步检测的请求
            response = crawlerClient.submitResource(request);
        } catch (Exception e) {
            return null;
        }

        if (response != null && response.getCode() == 200) {
            CrawlerResourceSubmitV3Response.CrawlerResourceSubmitResult result = response.getResult();
            if (result != null) {
                return result;
            }
        }
        return null;
    }
}
