package com.sohu.third.yidun.service;

import com.netease.yidun.sdk.antispam.AntispamRequester;
import com.netease.yidun.sdk.antispam.image.v5.ImageClient;
import com.netease.yidun.sdk.antispam.image.v5.check.ImageV5CheckRequest;
import com.netease.yidun.sdk.antispam.image.v5.check.async.request.ImageV5AsyncCheckRequest;
import com.netease.yidun.sdk.antispam.image.v5.check.async.response.ImageV5AsyncCheckResp;
import com.netease.yidun.sdk.antispam.image.v5.check.sync.request.ImageV5SyncCheckRequest;
import com.netease.yidun.sdk.antispam.image.v5.check.sync.response.ImageV5AntispamResp;
import com.netease.yidun.sdk.antispam.image.v5.check.sync.response.ImageV5CheckResponse;
import com.netease.yidun.sdk.antispam.image.v5.check.sync.response.ImageV5Result;
import com.sohu.third.yidun.object.TransformObjet;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/6/12 14:44
 */
@Component
public class ImageCheckService extends AbstractService {


    public static ImageV5AntispamResp singleSyncImage(String secretId, String secretKey, String businessId, TransformObjet content) {
        AntispamRequester antispamRequester = createAntispamRequester(secretId, secretKey);
        // 实例化发起请求的client对象
        ImageClient imageClient = ImageClient.getInstance(antispamRequester);

        // 实例化请求对象
        ImageV5SyncCheckRequest checkRequest = new ImageV5SyncCheckRequest();
        // 设置易盾内容安全分配的businessId
        checkRequest.setBusinessId(businessId);

        // 根据需要设置请求的检测节点，默认杭州，现阶段只支持检测接口
        checkRequest.setRegionCode("cn-hangzhou");

        // 实例化要检测的图片对象
        ImageV5CheckRequest.ImageBeanRequest image = new ImageV5CheckRequest.ImageBeanRequest();
        image.setData(content.getContent());
        image.setName(content.getContent());
        image.setDataId(content.getDataId());
        // 设置图片数据的类型，1：图片URL，2:图片BASE64
        image.setType(1);
//        // 非必填，强烈建议通过轮询回调或者主动回调（2选1）的方式，来获取最新的检测结果。主动回调数据接口超时时间默认设置为2s，为了保证顺利接收数据，需保证接收接口性能稳定并且保证幂等性。
//        image.setCallbackUrl("主动回调地址");
        // 设置需要检测的图片列表，最大32张或者10M
        List<ImageV5CheckRequest.ImageBeanRequest> images = new ArrayList<>();
        images.add(image);
        checkRequest.setImages(images);

        // 请求对象中的其他参数如果有需要，请参考官方接口文档中字段说明，按需添加

        ImageV5CheckResponse checkResponse = null;
        try {
            // 发起同步检测的请求
            checkResponse = imageClient.syncCheckImage(checkRequest);
        } catch (Exception e) {
            return null;
        }

        if (checkResponse != null && checkResponse.getCode() == 200) {
            return checkResponse.getResult().get(0).getAntispam();
//            for (ImageV5Result imageV5Result : checkResponse.getResult()) {
//                // 根据需要获取每张图片的检测结果，具体返回字段的说明，请参考官方接口文档中字段说明
//            }
        }
        return null;

    }

    public static ImageV5AsyncCheckResp.ImageRespDetail asyncBatchImage(String secretId, String secretKey, String businessId, List<TransformObjet> contents, String callbackUrl) {
        AntispamRequester antispamRequester = createAntispamRequester(secretId, secretKey);
        // 实例化发起请求的client对象
        ImageClient imageClient = ImageClient.getInstance(antispamRequester);

        // 实例化请求对象
        ImageV5AsyncCheckRequest checkRequest = new ImageV5AsyncCheckRequest();
        // 设置易盾内容安全分配的businessId
        checkRequest.setBusinessId(businessId);
        // 根据需要设置请求的检测节点，默认杭州，现阶段只支持检测接口
        checkRequest.setRegionCode("cn-hangzhou");
        // 设置需要检测的图片列表，最大32张或者10M
        List<ImageV5CheckRequest.ImageBeanRequest> images = createImages(contents, callbackUrl);
        checkRequest.setImages(images);

        // 请求对象中的其他参数如果有需要，请参考官方接口文档中字段说明，按需添加
        ImageV5AsyncCheckResp checkResponse = null;
        try {
            // 发起异步检测的请求
            checkResponse = imageClient.asyncCheckImage(checkRequest);
        } catch (Exception e) {
            return null;
        }

        if (checkResponse != null && checkResponse.getCode() == 200) {
            ImageV5AsyncCheckResp.ImageV5AsyncCheckResult checkResult = checkResponse.getResult();
            return checkResult.getCheckImages().get(0);
//            if (checkResult != null && checkResult.getCheckImages() != null) {
//                for (ImageV5AsyncCheckResp.ImageRespDetail imageRespDetail : checkResult.getCheckImages()) {
//                    // 根据需要获取每张图片的检测结果，具体返回字段的说明，请参考官方接口文档中字段说明
//                }
//            }
        }
        return null;

    }

    private static List<ImageV5CheckRequest.ImageBeanRequest> createImages(List<TransformObjet> contents, String callbackUrl) {
        // 设置需要检测的图片列表，最大32张或者10M
        List<ImageV5CheckRequest.ImageBeanRequest> images = new ArrayList<>();
        for (TransformObjet content : contents) {
            // 实例化要检测的图片对象
            ImageV5CheckRequest.ImageBeanRequest image = new ImageV5CheckRequest.ImageBeanRequest();
            image.setData(content.getContent());
            image.setName(content.getContent());
            // 设置图片数据的类型，1：图片URL。base64暂不支持
            image.setType(1);
            // 非必填，建议设置获取最新的图片检测结果。主动回调数据接口超时时间默认设置为2s，为了保证顺利接收数据，需保证接收接口性能稳定并且保证幂等性。
            image.setCallbackUrl(callbackUrl);
            image.setDataId(content.getDataId());
            images.add(image);
        }
        return images;
    }

    public static void main(String[] args) {
        String secretId = "f87f85a5fbb21b2f93f666f3a886c653";
        String secretKey = "97ca9e910741790bfcb4ab99b9f25fc3";
        String businessId = "15c6c1ebbac6f33077171e7e1782744f";
        TransformObjet content = new TransformObjet();
        content.setDataId(UUID.randomUUID().toString());
        content.setContent("http://111.175.90.254:9000/sohuglobal/2025/06/20/6e03867ac8c74adda647a8aba2812b8c_588x665.png");
        String callbackUrl = "http://111.175.90.254:18080/app/api/risk/callback/image";
        ImageV5AsyncCheckResp.ImageRespDetail asyncImage = asyncBatchImage(secretId, secretKey, businessId, Arrays.asList(content), callbackUrl);
    }
}
