package com.sohu.third.aliyun.audit.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.imageaudit20191230.Client;
import com.aliyun.imageaudit20191230.models.ScanTextRequest;
import com.aliyun.imageaudit20191230.models.ScanTextResponse;
import com.aliyun.imageaudit20191230.models.ScanTextResponseBody;
import com.aliyun.teautil.models.RuntimeOptions;
import com.sohu.third.aliyun.audit.constants.AliyunAuditLabelEnum;
import com.sohu.third.aliyun.audit.util.AliyunAuditUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class AliyunAuditService {

    @Autowired
    private AliyunAuditUtil aliyunAuditUtil;

    /**
     * 指定文本检测的应用场景，label可选值包括：
     * spam：文字垃圾内容识别
     * politics：文字敏感内容识别
     * abuse：文字辱骂内容识别
     * terrorism：文字暴恐内容识别
     * porn：文字鉴黄内容识别
     * flood：文字灌水内容识别
     * contraband：文字违禁内容识别
     * ad：文字广告内容识别
     *
     * @param content
     * @return 返回检测结果，文档地址：https://help.aliyun.com/zh/viapi/developer-reference/api-bwn123
     */
    public String scanText(String content, List<AliyunAuditLabelEnum> labelEnums) {
        if (StrUtil.isBlankIfStr(content)) {
            return null;
        }
        Client client = aliyunAuditUtil.getClient();
        ScanTextRequest.ScanTextRequestTasks tasks = new ScanTextRequest.ScanTextRequestTasks();
        tasks.setContent(content);
        ScanTextRequest scanTextRequest = new ScanTextRequest();
        List<ScanTextRequest.ScanTextRequestLabels> labels = new ArrayList<>();
        for (AliyunAuditLabelEnum labelEnum : labelEnums) {
            labels.add(new ScanTextRequest.ScanTextRequestLabels().setLabel(labelEnum.getLabel()));
        }
        scanTextRequest.setLabels(labels);
        scanTextRequest.setTasks(Arrays.asList(tasks));
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            ScanTextResponse response = client.scanTextWithOptions(scanTextRequest, runtime);
            if (response.getStatusCode() != 200) {
                log.error("阿里云内容审核报错：{}", JSONUtil.toJsonStr(response));
                return null;
            }
            log.info("阿里云内容审核结果：{}", JSONUtil.toJsonStr(response));
            ScanTextResponseBody scanTextResponseBody = response.getBody();
            ScanTextResponseBody.ScanTextResponseBodyData textResponseBodyData = scanTextResponseBody.getData();
            List<ScanTextResponseBody.ScanTextResponseBodyDataElements> elements = textResponseBodyData.getElements();
            boolean normal = false;
            String badMsg = null;
            for (ScanTextResponseBody.ScanTextResponseBodyDataElements element : elements) {
                List<ScanTextResponseBody.ScanTextResponseBodyDataElementsResults> results = element.getResults();
                if (CollUtil.isEmpty(results)) {
                    continue;
                }
                for (ScanTextResponseBody.ScanTextResponseBodyDataElementsResults result : results) {
                    /**
                     * pass：文本正常。
                     * review：需要人工审核。
                     * block：文本违规，可以直接删除或者做限制处理
                     */
                    String suggestion = result.getSuggestion();
                    if (StrUtil.equalsAnyIgnoreCase(suggestion, "pass")) {
                        normal = true;
                    } else {
                        badMsg = JSONUtil.toJsonStr(result.getDetails());
                    }
                }
            }
            if (normal) {
                return null;
            }
            return badMsg;
        } catch (Exception error) {
            // 获取整体报错信息
            log.error(com.aliyun.teautil.Common.toJSONString(error));
        }
        return null;
    }

    public static void main(String[] args) {
        String string = new AliyunAuditService().scanText("维修管道，联系weixin,习近平",
                Arrays.asList(AliyunAuditLabelEnum.ad, AliyunAuditLabelEnum.politics));
        System.out.println(string);
    }

}
