package com.sohu.third.aliyun.audit.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 阿里云文本检测应用场景说明
 */
public enum AliyunAuditLabelEnum {

    spam("spam", "文字垃圾内容识别"),

    politics("politics", "文字敏感内容识别"),

    abuse("abuse", "文字辱骂内容识别"),

    terrorism("terrorism", "文字暴恐内容识别"),

    porn("porn", "文字鉴黄内容识别"),

    flood("flood", "文字灌水内容识别"),

    contraband("contraband", "文字违禁内容识别"),

    ad("ad", "文字广告内容识别");

    private String label;
    private String description;

    public String getLabel() {
        return label;
    }

    public String getDescription() {
        return description;
    }

    private AliyunAuditLabelEnum(String label, String description) {
        this.label = label;
        this.description = description;
    }

    /**
     * 文本检测
     */
    public static final List<AliyunAuditLabelEnum> textCheck = Arrays.asList(
            AliyunAuditLabelEnum.terrorism,
            AliyunAuditLabelEnum.porn,
            AliyunAuditLabelEnum.politics,
            AliyunAuditLabelEnum.ad,
            AliyunAuditLabelEnum.spam);


    /**
     * 根据 label 获取枚举的描述
     */
    public static Optional<String> getDescriptionByLabel(String label) {
        for (AliyunAuditLabelEnum enumValue : AliyunAuditLabelEnum.values()) {
            if (enumValue.getLabel().equalsIgnoreCase(label)) {
                return Optional.of(enumValue.getDescription());
            }
        }
        return Optional.empty();
    }
}
