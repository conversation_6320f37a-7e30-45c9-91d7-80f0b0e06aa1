package com.sohu.airwallex.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
public class AirWallexCaptureResponse implements Serializable {

    @JSONField(name = "amount")
    private Integer amount;
    @JSONField(name = "cancellation_reason")
    private String cancellationReason;
    @JSONField(name = "cancelled_at")
    private String cancelledAt;
    @JSONField(name = "captured_amount")
    private Integer capturedAmount;
    @JSONField(name = "client_secret")
    private String clientSecret;
    @J<PERSON>NField(name = "connected_account_id")
    private String connectedAccountId;
    @JSONField(name = "created_at")
    private String createdAt;
    @JSONField(name = "currency")
    private String currency;
    @JSONField(name = "customer")
    private CustomerDTO customer;
    @JSONField(name = "customer_id")
    private String customerId;
    @JSONField(name = "descriptor")
    private String descriptor;
    @JSONField(name = "funds_split_data")
    private List<FundsSplitDataDTO> fundsSplitData;
    @JSONField(name = "id")
    private String id;
    @JSONField(name = "invoice_id")
    private String invoiceId;
    @JSONField(name = "latest_payment_attempt")
    private LatestPaymentAttemptDTO latestPaymentAttempt;
    @JSONField(name = "merchant_order_id")
    private String merchantOrderId;
    @JSONField(name = "metadata")
    private MetadataDTO metadata;
    @JSONField(name = "next_action")
    private NextActionDTO nextAction;
    @JSONField(name = "order")
    private OrderDTO order;
    @JSONField(name = "payment_consent_id")
    private String paymentConsentId;
    @JSONField(name = "payment_link_id")
    private String paymentLinkId;
    @JSONField(name = "payment_method_options")
    private PaymentMethodOptionsDTO paymentMethodOptions;
    @JSONField(name = "request_id")
    private String requestId;
    @JSONField(name = "return_url")
    private String returnUrl;
    @JSONField(name = "risk_control_options")
    private RiskControlOptionsDTO riskControlOptions;
    @JSONField(name = "status")
    private String status;
    @JSONField(name = "updated_at")
    private String updatedAt;

    @NoArgsConstructor
    @Data
    public static class CustomerDTO {
        @JSONField(name = "additional_info")
        private AdditionalInfoDTO additionalInfo;
        @JSONField(name = "address")
        private AddressDTO address;
        @JSONField(name = "business_name")
        private String businessName;
        @JSONField(name = "email")
        private String email;
        @JSONField(name = "first_name")
        private String firstName;
        @JSONField(name = "last_name")
        private String lastName;
        @JSONField(name = "merchant_customer_id")
        private String merchantCustomerId;
        @JSONField(name = "phone_number")
        private String phoneNumber;

        @NoArgsConstructor
        @Data
        public static class AdditionalInfoDTO {
            @JSONField(name = "account_type")
            private String accountType;
            @JSONField(name = "first_successful_order_date")
            private String firstSuccessfulOrderDate;
            @JSONField(name = "last_login_ip_address")
            private String lastLoginIpAddress;
            @JSONField(name = "last_modified_at")
            private String lastModifiedAt;
            @JSONField(name = "linked_social_networks")
            private List<LinkedSocialNetworksDTO> linkedSocialNetworks;
            @JSONField(name = "purchase_summaries")
            private List<PurchaseSummariesDTO> purchaseSummaries;
            @JSONField(name = "registered_via_social_media")
            private Boolean registeredViaSocialMedia;
            @JSONField(name = "registration_date")
            private String registrationDate;
            @JSONField(name = "registration_ip_address")
            private String registrationIpAddress;

            @NoArgsConstructor
            @Data
            public static class LinkedSocialNetworksDTO {
                @JSONField(name = "email")
                private String email;
                @JSONField(name = "name")
                private String name;
                @JSONField(name = "profile_id")
                private String profileId;
            }

            @NoArgsConstructor
            @Data
            public static class PurchaseSummariesDTO {
                @JSONField(name = "currency")
                private String currency;
                @JSONField(name = "first_successful_purchase_date")
                private String firstSuccessfulPurchaseDate;
                @JSONField(name = "last_successful_purchase_date")
                private String lastSuccessfulPurchaseDate;
                @JSONField(name = "payment_method_type")
                private String paymentMethodType;
                @JSONField(name = "successful_purchase_amount")
                private Double successfulPurchaseAmount;
                @JSONField(name = "successful_purchase_count")
                private Integer successfulPurchaseCount;
            }
        }

        @NoArgsConstructor
        @Data
        public static class AddressDTO {
            @JSONField(name = "city")
            private String city;
            @JSONField(name = "country_code")
            private String countryCode;
            @JSONField(name = "postcode")
            private String postcode;
            @JSONField(name = "state")
            private String state;
            @JSONField(name = "street")
            private String street;
        }
    }

    @NoArgsConstructor
    @Data
    public static class LatestPaymentAttemptDTO {
        @JSONField(name = "amount")
        private Double amount;
        @JSONField(name = "authentication_data")
        private AuthenticationDataDTO authenticationData;
        @JSONField(name = "authorization_code")
        private String authorizationCode;
        @JSONField(name = "captured_amount")
        private Double capturedAmount;
        @JSONField(name = "created_at")
        private String createdAt;
        @JSONField(name = "currency")
        private String currency;
        @JSONField(name = "dcc_data")
        private DccDataDTO dccData;
        @JSONField(name = "failure_code")
        private String failureCode;
        @JSONField(name = "id")
        private String id;
        @JSONField(name = "merchant_advice_code")
        private String merchantAdviceCode;
        @JSONField(name = "merchant_order_id")
        private String merchantOrderId;
        @JSONField(name = "payment_consent_id")
        private String paymentConsentId;
        @JSONField(name = "payment_intent_id")
        private String paymentIntentId;
        @JSONField(name = "payment_method")
        private PaymentMethodDTO paymentMethod;
        @JSONField(name = "payment_method_options")
        private PaymentMethodOptionsDTO paymentMethodOptions;
        @JSONField(name = "payment_method_transaction_id")
        private String paymentMethodTransactionId;
        @JSONField(name = "provider_original_response_code")
        private String providerOriginalResponseCode;
        @JSONField(name = "provider_transaction_id")
        private String providerTransactionId;
        @JSONField(name = "refunded_amount")
        private Double refundedAmount;
        @JSONField(name = "settle_via")
        private String settleVia;
        @JSONField(name = "status")
        private String status;
        @JSONField(name = "updated_at")
        private String updatedAt;

        @NoArgsConstructor
        @Data
        public static class AuthenticationDataDTO {
            @JSONField(name = "avs_result")
            private String avsResult;
            @JSONField(name = "cvc_result")
            private String cvcResult;
            @JSONField(name = "ds_data")
            private DsDataDTO dsData;
            @JSONField(name = "fraud_data")
            private FraudDataDTO fraudData;

            @NoArgsConstructor
            @Data
            public static class DsDataDTO {
                @JSONField(name = "cavv")
                private String cavv;
                @JSONField(name = "challenge_cancellation_reason")
                private String challengeCancellationReason;
                @JSONField(name = "eci")
                private String eci;
                @JSONField(name = "enrolled")
                private String enrolled;
                @JSONField(name = "frictionless")
                private String frictionless;
                @JSONField(name = "liability_shift_indicator")
                private String liabilityShiftIndicator;
                @JSONField(name = "pa_res_status")
                private String paResStatus;
                @JSONField(name = "version")
                private String version;
                @JSONField(name = "xid")
                private String xid;
            }

            @NoArgsConstructor
            @Data
            public static class FraudDataDTO {
                @JSONField(name = "action")
                private String action;
                @JSONField(name = "risk_factors")
                private List<RiskFactorsDTO> riskFactors;
                @JSONField(name = "score")
                private String score;

                @NoArgsConstructor
                @Data
                public static class RiskFactorsDTO {
                    @JSONField(name = "description")
                    private String description;
                }
            }
        }

        @NoArgsConstructor
        @Data
        public static class DccDataDTO {
            @JSONField(name = "amount")
            private Double amount;
            @JSONField(name = "currency")
            private String currency;
        }

        @NoArgsConstructor
        @Data
        public static class PaymentMethodDTO {
            @JSONField(name = "ach_direct_debit")
            private AchDirectDebitDTO achDirectDebit;
            @JSONField(name = "airwallex_pay")
            private AirwallexPayDTO airwallexPay;
            @JSONField(name = "alfamart")
            private AlfamartDTO alfamart;
            @JSONField(name = "alipaycn")
            private AlipaycnDTO alipaycn;
            @JSONField(name = "alipayhk")
            private AlipayhkDTO alipayhk;
            @JSONField(name = "applepay")
            private ApplepayDTO applepay;
            @JSONField(name = "atome")
            private AtomeDTO atome;
            @JSONField(name = "axs_kiosk")
            private AxsKioskDTO axsKiosk;
            @JSONField(name = "bacs_direct_debit")
            private BacsDirectDebitDTO bacsDirectDebit;
            @JSONField(name = "bancontact")
            private BancontactDTO bancontact;
            @JSONField(name = "bank_transfer")
            private BankTransferDTO bankTransfer;
            @JSONField(name = "becs_direct_debit")
            private BecsDirectDebitDTO becsDirectDebit;
            @JSONField(name = "bitpay")
            private BitpayDTO bitpay;
            @JSONField(name = "blik")
            private BlikDTO blik;
            @JSONField(name = "boost")
            private BoostDTO boost;
            @JSONField(name = "card")
            private CardDTO card;
            @JSONField(name = "card_present")
            private CardPresentDTO cardPresent;
            @JSONField(name = "created_at")
            private String createdAt;
            @JSONField(name = "customer_id")
            private String customerId;
            @JSONField(name = "dana")
            private DanaDTO dana;
            @JSONField(name = "doku_ewallet")
            private DokuEwalletDTO dokuEwallet;
            @JSONField(name = "dragonpay")
            private DragonpayDTO dragonpay;
            @JSONField(name = "duit_now")
            private DuitNowDTO duitNow;
            @JSONField(name = "eft_direct_debit")
            private EftDirectDebitDTO eftDirectDebit;
            @JSONField(name = "enets")
            private EnetsDTO enets;
            @JSONField(name = "eps")
            private EpsDTO eps;
            @JSONField(name = "esun")
            private EsunDTO esun;
            @JSONField(name = "family_mart")
            private FamilyMartDTO familyMart;
            @JSONField(name = "fps")
            private FpsDTO fps;
            @JSONField(name = "fpx")
            private FpxDTO fpx;
            @JSONField(name = "gcash")
            private GcashDTO gcash;
            @JSONField(name = "go_pay")
            private GoPayDTO goPay;
            @JSONField(name = "googlepay")
            private GooglepayDTO googlepay;
            @JSONField(name = "grabpay")
            private GrabpayDTO grabpay;
            @JSONField(name = "hi_life")
            private HiLifeDTO hiLife;
            @JSONField(name = "id")
            private String id;
            @JSONField(name = "ideal")
            private IdealDTO ideal;
            @JSONField(name = "indomaret")
            private IndomaretDTO indomaret;
            @JSONField(name = "jenius_pay")
            private JeniusPayDTO jeniusPay;
            @JSONField(name = "kakaopay")
            private KakaopayDTO kakaopay;
            @JSONField(name = "klarna")
            private KlarnaDTO klarna;
            @JSONField(name = "konbini")
            private KonbiniDTO konbini;
            @JSONField(name = "linkaja")
            private LinkajaDTO linkaja;
            @JSONField(name = "maxima")
            private MaximaDTO maxima;
            @JSONField(name = "metadata")
            private MetadataDTO metadata;
            @JSONField(name = "multibanco")
            private MultibancoDTO multibanco;
            @JSONField(name = "mybank")
            private MybankDTO mybank;
            @JSONField(name = "narvesen")
            private NarvesenDTO narvesen;
            @JSONField(name = "online_banking")
            private OnlineBankingDTO onlineBanking;
            @JSONField(name = "ovo")
            private OvoDTO ovo;
            @JSONField(name = "p24")
            private P24DTO p24;
            @JSONField(name = "pay_now")
            private PayNowDTO payNow;
            @JSONField(name = "paybybankapp")
            private PaybybankappDTO paybybankapp;
            @JSONField(name = "payeasy")
            private PayeasyDTO payeasy;
            @JSONField(name = "paypal")
            private PaypalDTO paypal;
            @JSONField(name = "paypost")
            private PaypostDTO paypost;
            @JSONField(name = "paysafecard")
            private PaysafecardDTO paysafecard;
            @JSONField(name = "paysafecash")
            private PaysafecashDTO paysafecash;
            @JSONField(name = "paysera")
            private PayseraDTO paysera;
            @JSONField(name = "payu")
            private PayuDTO payu;
            @JSONField(name = "perlas_terminals")
            private PerlasTerminalsDTO perlasTerminals;
            @JSONField(name = "prompt_pay")
            private PromptPayDTO promptPay;
            @JSONField(name = "rabbit_line_pay")
            private RabbitLinePayDTO rabbitLinePay;
            @JSONField(name = "sam_kiosk")
            private SamKioskDTO samKiosk;
            @JSONField(name = "satispay")
            private SatispayDTO satispay;
            @JSONField(name = "sepa_direct_debit")
            private SepaDirectDebitDTO sepaDirectDebit;
            @JSONField(name = "seven_eleven")
            private SevenElevenDTO sevenEleven;
            @JSONField(name = "shopee_pay")
            private ShopeePayDTO shopeePay;
            @JSONField(name = "skrill")
            private SkrillDTO skrill;
            @JSONField(name = "sofort")
            private SofortDTO sofort;
            @JSONField(name = "status")
            private String status;
            @JSONField(name = "tng")
            private TngDTO tng;
            @JSONField(name = "truemoney")
            private TruemoneyDTO truemoney;
            @JSONField(name = "trustly")
            private TrustlyDTO trustly;
            @JSONField(name = "type")
            private String type;
            @JSONField(name = "updated_at")
            private String updatedAt;
            @JSONField(name = "verkkopankki")
            private VerkkopankkiDTO verkkopankki;
            @JSONField(name = "wechatpay")
            private WechatpayDTO wechatpay;
            @JSONField(name = "zip")
            private ZipDTO zip;

            @NoArgsConstructor
            @Data
            public static class AchDirectDebitDTO {
                @JSONField(name = "aba_routing_number")
                private String abaRoutingNumber;
                @JSONField(name = "account_number")
                private String accountNumber;
                @JSONField(name = "business_account")
                private Boolean businessAccount;
                @JSONField(name = "micro_debit")
                private MicroDebitDTO microDebit;
                @JSONField(name = "micro_deposit")
                private MicroDepositDTO microDeposit;
                @JSONField(name = "owner_email")
                private String ownerEmail;
                @JSONField(name = "owner_name")
                private String ownerName;

                @NoArgsConstructor
                @Data
                public static class MicroDebitDTO {
                    @JSONField(name = "status")
                    private String status;
                }

                @NoArgsConstructor
                @Data
                public static class MicroDepositDTO {
                    @JSONField(name = "status")
                    private String status;
                }
            }

            @NoArgsConstructor
            @Data
            public static class AirwallexPayDTO {
                @JSONField(name = "payer_name")
                private String payerName;
            }

            @NoArgsConstructor
            @Data
            public static class AlfamartDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class AlipaycnDTO {
                @JSONField(name = "flow")
                private String flow;
                @JSONField(name = "os_type")
                private String osType;
                @JSONField(name = "user_id")
                private String userId;
            }

            @NoArgsConstructor
            @Data
            public static class AlipayhkDTO {
                @JSONField(name = "flow")
                private String flow;
                @JSONField(name = "os_type")
                private String osType;
                @JSONField(name = "user_id")
                private String userId;
            }

            @NoArgsConstructor
            @Data
            public static class ApplepayDTO {
                @JSONField(name = "billing")
                private BillingDTO billing;
                @JSONField(name = "payment_data_type")
                private String paymentDataType;
                @JSONField(name = "tokenized_card")
                private TokenizedCardDTO tokenizedCard;

                @NoArgsConstructor
                @Data
                public static class BillingDTO {
                    @JSONField(name = "address")
                    private AddressDTO address;
                    @JSONField(name = "date_of_birth")
                    private String dateOfBirth;
                    @JSONField(name = "email")
                    private String email;
                    @JSONField(name = "first_name")
                    private String firstName;
                    @JSONField(name = "last_name")
                    private String lastName;
                    @JSONField(name = "phone_number")
                    private String phoneNumber;

                    @NoArgsConstructor
                    @Data
                    public static class AddressDTO {
                        @JSONField(name = "city")
                        private String city;
                        @JSONField(name = "country_code")
                        private String countryCode;
                        @JSONField(name = "postcode")
                        private String postcode;
                        @JSONField(name = "state")
                        private String state;
                        @JSONField(name = "street")
                        private String street;
                    }
                }

                @NoArgsConstructor
                @Data
                public static class TokenizedCardDTO {
                    @JSONField(name = "authentication_method")
                    private AuthenticationMethodDTO authenticationMethod;
                    @JSONField(name = "bin")
                    private String bin;
                    @JSONField(name = "brand")
                    private String brand;
                    @JSONField(name = "device_manufacturer_identifier")
                    private String deviceManufacturerIdentifier;
                    @JSONField(name = "expiry_month")
                    private String expiryMonth;
                    @JSONField(name = "expiry_year")
                    private String expiryYear;
                    @JSONField(name = "fingerprint")
                    private String fingerprint;
                    @JSONField(name = "is_commercial")
                    private Boolean isCommercial;
                    @JSONField(name = "issuer_country_code")
                    private String issuerCountryCode;
                    @JSONField(name = "issuer_name")
                    private String issuerName;
                    @JSONField(name = "last4")
                    private String last4;
                    @JSONField(name = "name")
                    private String name;
                    @JSONField(name = "type")
                    private String type;

                    @NoArgsConstructor
                    @Data
                    public static class AuthenticationMethodDTO {
                        @JSONField(name = "emv")
                        private EmvDTO emv;
                        @JSONField(name = "three_ds")
                        private ThreeDsDTO threeDs;
                        @JSONField(name = "type")
                        private String type;

                        @NoArgsConstructor
                        @Data
                        public static class EmvDTO {
                            @JSONField(name = "emv_data")
                            private String emvData;
                            @JSONField(name = "encrypted_pin_data")
                            private String encryptedPinData;
                        }

                        @NoArgsConstructor
                        @Data
                        public static class ThreeDsDTO {
                            @JSONField(name = "eci_indicator")
                            private String eciIndicator;
                            @JSONField(name = "online_payment_cryptogram")
                            private String onlinePaymentCryptogram;
                        }
                    }
                }
            }

            @NoArgsConstructor
            @Data
            public static class AtomeDTO {
                @JSONField(name = "shopper_phone")
                private String shopperPhone;
            }

            @NoArgsConstructor
            @Data
            public static class AxsKioskDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
                @JSONField(name = "shopper_phone")
                private String shopperPhone;
            }

            @NoArgsConstructor
            @Data
            public static class BacsDirectDebitDTO {
                @JSONField(name = "account_number")
                private String accountNumber;
                @JSONField(name = "address")
                private AddressDTO address;
                @JSONField(name = "bank_name")
                private String bankName;
                @JSONField(name = "business_account")
                private Boolean businessAccount;
                @JSONField(name = "micro_deposit")
                private MicroDepositDTO microDeposit;
                @JSONField(name = "owner_email")
                private String ownerEmail;
                @JSONField(name = "owner_name")
                private String ownerName;
                @JSONField(name = "sort_code")
                private String sortCode;

                @NoArgsConstructor
                @Data
                public static class AddressDTO {
                    @JSONField(name = "country_code")
                    private String countryCode;
                    @JSONField(name = "line1")
                    private String line1;
                    @JSONField(name = "line2")
                    private String line2;
                    @JSONField(name = "postcode")
                    private String postcode;
                    @JSONField(name = "state")
                    private String state;
                    @JSONField(name = "town")
                    private String town;
                }

                @NoArgsConstructor
                @Data
                public static class MicroDepositDTO {
                    @JSONField(name = "status")
                    private String status;
                }
            }

            @NoArgsConstructor
            @Data
            public static class BancontactDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class BankTransferDTO {
                @JSONField(name = "bank_name")
                private String bankName;
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class BecsDirectDebitDTO {
                @JSONField(name = "account_number")
                private String accountNumber;
                @JSONField(name = "bsb_number")
                private String bsbNumber;
                @JSONField(name = "business_account")
                private Boolean businessAccount;
                @JSONField(name = "micro_debit")
                private MicroDebitDTO microDebit;
                @JSONField(name = "micro_deposit")
                private MicroDepositDTO microDeposit;
                @JSONField(name = "owner_email")
                private String ownerEmail;
                @JSONField(name = "owner_name")
                private String ownerName;

                @NoArgsConstructor
                @Data
                public static class MicroDebitDTO {
                    @JSONField(name = "status")
                    private String status;
                }

                @NoArgsConstructor
                @Data
                public static class MicroDepositDTO {
                    @JSONField(name = "status")
                    private String status;
                }
            }

            @NoArgsConstructor
            @Data
            public static class BitpayDTO {
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class BlikDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class BoostDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
                @JSONField(name = "shopper_phone")
                private String shopperPhone;
            }

            @NoArgsConstructor
            @Data
            public static class CardDTO {
                @JSONField(name = "additional_info")
                private AdditionalInfoDTO additionalInfo;
                @JSONField(name = "avs_check")
                private String avsCheck;
                @JSONField(name = "billing")
                private BillingDTO billing;
                @JSONField(name = "bin")
                private String bin;
                @JSONField(name = "brand")
                private String brand;
                @JSONField(name = "card_type")
                private String cardType;
                @JSONField(name = "cvc_check")
                private String cvcCheck;
                @JSONField(name = "expiry_month")
                private String expiryMonth;
                @JSONField(name = "expiry_year")
                private String expiryYear;
                @JSONField(name = "fingerprint")
                private String fingerprint;
                @JSONField(name = "is_commercial")
                private Boolean isCommercial;
                @JSONField(name = "issuer_country_code")
                private String issuerCountryCode;
                @JSONField(name = "issuer_name")
                private String issuerName;
                @JSONField(name = "last4")
                private String last4;
                @JSONField(name = "name")
                private String name;
                @JSONField(name = "number_type")
                private String numberType;

                @NoArgsConstructor
                @Data
                public static class AdditionalInfoDTO {
                    @JSONField(name = "merchant_verification_value")
                    private String merchantVerificationValue;
                    @JSONField(name = "token_requestor_id")
                    private String tokenRequestorId;
                }

                @NoArgsConstructor
                @Data
                public static class BillingDTO {
                    @JSONField(name = "address")
                    private AddressDTO address;
                    @JSONField(name = "date_of_birth")
                    private String dateOfBirth;
                    @JSONField(name = "email")
                    private String email;
                    @JSONField(name = "first_name")
                    private String firstName;
                    @JSONField(name = "last_name")
                    private String lastName;
                    @JSONField(name = "phone_number")
                    private String phoneNumber;

                    @NoArgsConstructor
                    @Data
                    public static class AddressDTO {
                        @JSONField(name = "city")
                        private String city;
                        @JSONField(name = "country_code")
                        private String countryCode;
                        @JSONField(name = "postcode")
                        private String postcode;
                        @JSONField(name = "state")
                        private String state;
                        @JSONField(name = "street")
                        private String street;
                    }
                }
            }

            @NoArgsConstructor
            @Data
            public static class CardPresentDTO {
                @JSONField(name = "bin")
                private String bin;
                @JSONField(name = "brand")
                private String brand;
                @JSONField(name = "card_sequence_number")
                private String cardSequenceNumber;
                @JSONField(name = "card_type")
                private String cardType;
                @JSONField(name = "cardholder_verification_method")
                private String cardholderVerificationMethod;
                @JSONField(name = "emv_tags")
                private String emvTags;
                @JSONField(name = "expiry_month")
                private String expiryMonth;
                @JSONField(name = "expiry_year")
                private String expiryYear;
                @JSONField(name = "fallback")
                private Boolean fallback;
                @JSONField(name = "fallback_reason")
                private String fallbackReason;
                @JSONField(name = "fingerprint")
                private String fingerprint;
                @JSONField(name = "is_commercial")
                private Boolean isCommercial;
                @JSONField(name = "issuer_country_code")
                private String issuerCountryCode;
                @JSONField(name = "issuer_name")
                private String issuerName;
                @JSONField(name = "last4")
                private String last4;
                @JSONField(name = "name")
                private String name;
                @JSONField(name = "pan_entry_mode")
                private String panEntryMode;
                @JSONField(name = "terminal_info")
                private TerminalInfoDTO terminalInfo;

                @NoArgsConstructor
                @Data
                public static class TerminalInfoDTO {
                    @JSONField(name = "mobile_device")
                    private Boolean mobileDevice;
                    @JSONField(name = "pin_entry_capability")
                    private String pinEntryCapability;
                    @JSONField(name = "supported_pan_entry_modes")
                    private List<String> supportedPanEntryModes;
                    @JSONField(name = "terminal_id")
                    private String terminalId;
                    @JSONField(name = "use_embedded_reader")
                    private Boolean useEmbeddedReader;
                }
            }

            @NoArgsConstructor
            @Data
            public static class DanaDTO {
                @JSONField(name = "flow")
                private String flow;
                @JSONField(name = "os_type")
                private String osType;
                @JSONField(name = "user_id")
                private String userId;
            }

            @NoArgsConstructor
            @Data
            public static class DokuEwalletDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class DragonpayDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
                @JSONField(name = "shopper_phone")
                private String shopperPhone;
            }

            @NoArgsConstructor
            @Data
            public static class DuitNowDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class EftDirectDebitDTO {
                @JSONField(name = "account_number")
                private String accountNumber;
                @JSONField(name = "business_account")
                private Boolean businessAccount;
                @JSONField(name = "institution_number")
                private String institutionNumber;
                @JSONField(name = "micro_debit")
                private MicroDebitDTO microDebit;
                @JSONField(name = "micro_deposit")
                private MicroDepositDTO microDeposit;
                @JSONField(name = "owner_email")
                private String ownerEmail;
                @JSONField(name = "owner_name")
                private String ownerName;
                @JSONField(name = "transit_number")
                private String transitNumber;

                @NoArgsConstructor
                @Data
                public static class MicroDebitDTO {
                    @JSONField(name = "status")
                    private String status;
                }

                @NoArgsConstructor
                @Data
                public static class MicroDepositDTO {
                    @JSONField(name = "status")
                    private String status;
                }
            }

            @NoArgsConstructor
            @Data
            public static class EnetsDTO {
                @JSONField(name = "bank_name")
                private String bankName;
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
                @JSONField(name = "shopper_phone")
                private String shopperPhone;
            }

            @NoArgsConstructor
            @Data
            public static class EpsDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class EsunDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
                @JSONField(name = "shopper_phone")
                private String shopperPhone;
            }

            @NoArgsConstructor
            @Data
            public static class FamilyMartDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
                @JSONField(name = "shopper_phone")
                private String shopperPhone;
            }

            @NoArgsConstructor
            @Data
            public static class FpsDTO {
                @JSONField(name = "flow")
                private String flow;
            }

            @NoArgsConstructor
            @Data
            public static class FpxDTO {
                @JSONField(name = "bank_name")
                private String bankName;
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
                @JSONField(name = "shopper_phone")
                private String shopperPhone;
            }

            @NoArgsConstructor
            @Data
            public static class GcashDTO {
                @JSONField(name = "flow")
                private String flow;
                @JSONField(name = "os_type")
                private String osType;
                @JSONField(name = "user_id")
                private String userId;
            }

            @NoArgsConstructor
            @Data
            public static class GoPayDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class GooglepayDTO {
                @JSONField(name = "billing")
                private BillingDTO billing;
                @JSONField(name = "payment_data_type")
                private String paymentDataType;
                @JSONField(name = "tokenized_card")
                private TokenizedCardDTO tokenizedCard;

                @NoArgsConstructor
                @Data
                public static class BillingDTO {
                    @JSONField(name = "address")
                    private AddressDTO address;
                    @JSONField(name = "date_of_birth")
                    private String dateOfBirth;
                    @JSONField(name = "email")
                    private String email;
                    @JSONField(name = "first_name")
                    private String firstName;
                    @JSONField(name = "last_name")
                    private String lastName;
                    @JSONField(name = "phone_number")
                    private String phoneNumber;

                    @NoArgsConstructor
                    @Data
                    public static class AddressDTO {
                        @JSONField(name = "city")
                        private String city;
                        @JSONField(name = "country_code")
                        private String countryCode;
                        @JSONField(name = "postcode")
                        private String postcode;
                        @JSONField(name = "state")
                        private String state;
                        @JSONField(name = "street")
                        private String street;
                    }
                }

                @NoArgsConstructor
                @Data
                public static class TokenizedCardDTO {
                    @JSONField(name = "authentication_method")
                    private AuthenticationMethodDTO authenticationMethod;
                    @JSONField(name = "bin")
                    private String bin;
                    @JSONField(name = "brand")
                    private String brand;
                    @JSONField(name = "device_manufacturer_identifier")
                    private String deviceManufacturerIdentifier;
                    @JSONField(name = "expiry_month")
                    private String expiryMonth;
                    @JSONField(name = "expiry_year")
                    private String expiryYear;
                    @JSONField(name = "fingerprint")
                    private String fingerprint;
                    @JSONField(name = "is_commercial")
                    private Boolean isCommercial;
                    @JSONField(name = "issuer_country_code")
                    private String issuerCountryCode;
                    @JSONField(name = "issuer_name")
                    private String issuerName;
                    @JSONField(name = "last4")
                    private String last4;
                    @JSONField(name = "name")
                    private String name;
                    @JSONField(name = "type")
                    private String type;

                    @NoArgsConstructor
                    @Data
                    public static class AuthenticationMethodDTO {
                        @JSONField(name = "emv")
                        private EmvDTO emv;
                        @JSONField(name = "three_ds")
                        private ThreeDsDTO threeDs;
                        @JSONField(name = "type")
                        private String type;

                        @NoArgsConstructor
                        @Data
                        public static class EmvDTO {
                            @JSONField(name = "emv_data")
                            private String emvData;
                            @JSONField(name = "encrypted_pin_data")
                            private String encryptedPinData;
                        }

                        @NoArgsConstructor
                        @Data
                        public static class ThreeDsDTO {
                            @JSONField(name = "eci_indicator")
                            private String eciIndicator;
                            @JSONField(name = "online_payment_cryptogram")
                            private String onlinePaymentCryptogram;
                        }
                    }
                }
            }

            @NoArgsConstructor
            @Data
            public static class GrabpayDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class HiLifeDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
                @JSONField(name = "shopper_phone")
                private String shopperPhone;
            }

            @NoArgsConstructor
            @Data
            public static class IdealDTO {
                @JSONField(name = "bank_name")
                private String bankName;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class IndomaretDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class JeniusPayDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class KakaopayDTO {
                @JSONField(name = "flow")
                private String flow;
                @JSONField(name = "os_type")
                private String osType;
                @JSONField(name = "user_id")
                private String userId;
            }

            @NoArgsConstructor
            @Data
            public static class KlarnaDTO {
                @JSONField(name = "billing")
                private BillingDTO billing;
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "language")
                private String language;

                @NoArgsConstructor
                @Data
                public static class BillingDTO {
                    @JSONField(name = "address")
                    private AddressDTO address;
                    @JSONField(name = "date_of_birth")
                    private String dateOfBirth;
                    @JSONField(name = "email")
                    private String email;
                    @JSONField(name = "first_name")
                    private String firstName;
                    @JSONField(name = "last_name")
                    private String lastName;
                    @JSONField(name = "phone_number")
                    private String phoneNumber;

                    @NoArgsConstructor
                    @Data
                    public static class AddressDTO {
                        @JSONField(name = "city")
                        private String city;
                        @JSONField(name = "country_code")
                        private String countryCode;
                        @JSONField(name = "postcode")
                        private String postcode;
                        @JSONField(name = "state")
                        private String state;
                        @JSONField(name = "street")
                        private String street;
                    }
                }
            }

            @NoArgsConstructor
            @Data
            public static class KonbiniDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
                @JSONField(name = "shopper_phone")
                private String shopperPhone;
            }

            @NoArgsConstructor
            @Data
            public static class LinkajaDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class MaximaDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class MetadataDTO {
                @JSONField(name = "id")
                private Integer id;
            }

            @NoArgsConstructor
            @Data
            public static class MultibancoDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class MybankDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class NarvesenDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class OnlineBankingDTO {
                @JSONField(name = "bank_name")
                private String bankName;
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
                @JSONField(name = "shopper_phone")
                private String shopperPhone;
            }

            @NoArgsConstructor
            @Data
            public static class OvoDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class P24DTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class PayNowDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class PaybybankappDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class PayeasyDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
                @JSONField(name = "shopper_phone")
                private String shopperPhone;
            }

            @NoArgsConstructor
            @Data
            public static class PaypalDTO {
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class PaypostDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class PaysafecardDTO {
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class PaysafecashDTO {
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class PayseraDTO {
                @JSONField(name = "bank_name")
                private String bankName;
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class PayuDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class PerlasTerminalsDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class PromptPayDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class RabbitLinePayDTO {
                @JSONField(name = "flow")
                private String flow;
                @JSONField(name = "os_type")
                private String osType;
                @JSONField(name = "user_id")
                private String userId;
            }

            @NoArgsConstructor
            @Data
            public static class SamKioskDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
                @JSONField(name = "shopper_phone")
                private String shopperPhone;
            }

            @NoArgsConstructor
            @Data
            public static class SatispayDTO {
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class SepaDirectDebitDTO {
                @JSONField(name = "bank_name")
                private String bankName;
                @JSONField(name = "business_account")
                private Boolean businessAccount;
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "iban")
                private String iban;
                @JSONField(name = "micro_debit")
                private MicroDebitDTO microDebit;
                @JSONField(name = "micro_deposit")
                private MicroDepositDTO microDeposit;
                @JSONField(name = "owner_email")
                private String ownerEmail;
                @JSONField(name = "owner_name")
                private String ownerName;

                @NoArgsConstructor
                @Data
                public static class MicroDebitDTO {
                    @JSONField(name = "status")
                    private String status;
                }

                @NoArgsConstructor
                @Data
                public static class MicroDepositDTO {
                    @JSONField(name = "status")
                    private String status;
                }
            }

            @NoArgsConstructor
            @Data
            public static class SevenElevenDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
                @JSONField(name = "shopper_phone")
                private String shopperPhone;
            }

            @NoArgsConstructor
            @Data
            public static class ShopeePayDTO {
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
                @JSONField(name = "shopper_phone")
                private String shopperPhone;
            }

            @NoArgsConstructor
            @Data
            public static class SkrillDTO {
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class SofortDTO {
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class TngDTO {
                @JSONField(name = "flow")
                private String flow;
                @JSONField(name = "os_type")
                private String osType;
                @JSONField(name = "user_id")
                private String userId;
            }

            @NoArgsConstructor
            @Data
            public static class TruemoneyDTO {
                @JSONField(name = "flow")
                private String flow;
                @JSONField(name = "os_type")
                private String osType;
                @JSONField(name = "user_id")
                private String userId;
            }

            @NoArgsConstructor
            @Data
            public static class TrustlyDTO {
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class VerkkopankkiDTO {
                @JSONField(name = "bank_name")
                private String bankName;
                @JSONField(name = "shopper_email")
                private String shopperEmail;
                @JSONField(name = "shopper_name")
                private String shopperName;
            }

            @NoArgsConstructor
            @Data
            public static class WechatpayDTO {
                @JSONField(name = "flow")
                private String flow;
            }

            @NoArgsConstructor
            @Data
            public static class ZipDTO {
                @JSONField(name = "shopper_name")
                private String shopperName;
            }
        }

        @NoArgsConstructor
        @Data
        public static class PaymentMethodOptionsDTO {
            @JSONField(name = "card")
            private CardDTO card;

            @NoArgsConstructor
            @Data
            public static class CardDTO {
                @JSONField(name = "authorization_type")
                private String authorizationType;
                @JSONField(name = "auto_capture")
                private Boolean autoCapture;
            }
        }
    }

    @NoArgsConstructor
    @Data
    public static class MetadataDTO {
        @JSONField(name = "id")
        private Integer id;
    }

    @NoArgsConstructor
    @Data
    public static class NextActionDTO {
        @JSONField(name = "content_type")
        private String contentType;
        @JSONField(name = "data")
        private DataDTO data;
        @JSONField(name = "dcc_data")
        private DccDataDTO dccData;
        @JSONField(name = "fallback_url")
        private String fallbackUrl;
        @JSONField(name = "method")
        private String method;
        @JSONField(name = "package_name")
        private String packageName;
        @JSONField(name = "qrcode")
        private String qrcode;
        @JSONField(name = "type")
        private String type;
        @JSONField(name = "url")
        private String url;

        @NoArgsConstructor
        @Data
        public static class DataDTO {
        }

        @NoArgsConstructor
        @Data
        public static class DccDataDTO {
            @JSONField(name = "amount")
            private Double amount;
            @JSONField(name = "client_rate")
            private Double clientRate;
            @JSONField(name = "currency")
            private String currency;
            @JSONField(name = "currency_pair")
            private String currencyPair;
            @JSONField(name = "rate_expiry")
            private String rateExpiry;
            @JSONField(name = "rate_timestamp")
            private String rateTimestamp;
        }
    }

    @NoArgsConstructor
    @Data
    public static class OrderDTO {
        @JSONField(name = "discount")
        private DiscountDTO discount;
        @JSONField(name = "itineraries")
        private List<ItinerariesDTO> itineraries;
        @JSONField(name = "products")
        private List<ProductsDTO> products;
        @JSONField(name = "sellers")
        private List<SellersDTO> sellers;
        @JSONField(name = "shipping")
        private ShippingDTO shipping;
        @JSONField(name = "supplier")
        private SupplierDTO supplier;
        @JSONField(name = "travelers")
        private List<TravelersDTO> travelers;
        @JSONField(name = "type")
        private String type;

        @NoArgsConstructor
        @Data
        public static class DiscountDTO {
            @JSONField(name = "coupon_code")
            private String couponCode;
        }

        @NoArgsConstructor
        @Data
        public static class ShippingDTO {
            @JSONField(name = "address")
            private AddressDTO address;
            @JSONField(name = "fee_amount")
            private Double feeAmount;
            @JSONField(name = "first_name")
            private String firstName;
            @JSONField(name = "last_name")
            private String lastName;
            @JSONField(name = "phone_number")
            private String phoneNumber;
            @JSONField(name = "shipping_delayed_at")
            private String shippingDelayedAt;
            @JSONField(name = "shipping_method")
            private String shippingMethod;

            @NoArgsConstructor
            @Data
            public static class AddressDTO {
                @JSONField(name = "city")
                private String city;
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "postcode")
                private String postcode;
                @JSONField(name = "state")
                private String state;
                @JSONField(name = "street")
                private String street;
            }
        }

        @NoArgsConstructor
        @Data
        public static class SupplierDTO {
            @JSONField(name = "address")
            private AddressDTO address;
            @JSONField(name = "business_name")
            private String businessName;
            @JSONField(name = "email")
            private String email;
            @JSONField(name = "first_name")
            private String firstName;
            @JSONField(name = "last_name")
            private String lastName;
            @JSONField(name = "phone_number")
            private String phoneNumber;

            @NoArgsConstructor
            @Data
            public static class AddressDTO {
                @JSONField(name = "city")
                private String city;
                @JSONField(name = "country_code")
                private String countryCode;
                @JSONField(name = "postcode")
                private String postcode;
                @JSONField(name = "state")
                private String state;
                @JSONField(name = "street")
                private String street;
            }
        }

        @NoArgsConstructor
        @Data
        public static class ItinerariesDTO {
            @JSONField(name = "airline_carrier_code")
            private String airlineCarrierCode;
            @JSONField(name = "arrival_airport_code")
            private String arrivalAirportCode;
            @JSONField(name = "arrival_city")
            private String arrivalCity;
            @JSONField(name = "arrive_at")
            private String arriveAt;
            @JSONField(name = "depart_at")
            private String departAt;
            @JSONField(name = "departure_airport_code")
            private String departureAirportCode;
            @JSONField(name = "departure_city")
            private String departureCity;
            @JSONField(name = "insurance")
            private List<InsuranceDTO> insurance;
            @JSONField(name = "price")
            private Integer price;
            @JSONField(name = "service_class")
            private String serviceClass;
            @JSONField(name = "traveler_identifier")
            private String travelerIdentifier;

            @NoArgsConstructor
            @Data
            public static class InsuranceDTO {
                @JSONField(name = "company")
                private String company;
                @JSONField(name = "price")
                private Integer price;
                @JSONField(name = "type")
                private String type;
            }
        }

        @NoArgsConstructor
        @Data
        public static class ProductsDTO {
            @JSONField(name = "category")
            private String category;
            @JSONField(name = "code")
            private String code;
            @JSONField(name = "desc")
            private String desc;
            @JSONField(name = "effective_end_at")
            private String effectiveEndAt;
            @JSONField(name = "effective_start_at")
            private String effectiveStartAt;
            @JSONField(name = "image_url")
            private String imageUrl;
            @JSONField(name = "name")
            private String name;
            @JSONField(name = "quantity")
            private Integer quantity;
            @JSONField(name = "seller")
            private SellerDTO seller;
            @JSONField(name = "sku")
            private String sku;
            @JSONField(name = "type")
            private String type;
            @JSONField(name = "unit_price")
            private Double unitPrice;
            @JSONField(name = "url")
            private String url;

            @NoArgsConstructor
            @Data
            public static class SellerDTO {
                @JSONField(name = "identifier")
                private String identifier;
                @JSONField(name = "name")
                private String name;
            }
        }

        @NoArgsConstructor
        @Data
        public static class SellersDTO {
            @JSONField(name = "additional_info")
            private AdditionalInfoDTO additionalInfo;
            @JSONField(name = "business_info")
            private BusinessInfoDTO businessInfo;
            @JSONField(name = "identifier")
            private String identifier;
            @JSONField(name = "name")
            private String name;

            @NoArgsConstructor
            @Data
            public static class AdditionalInfoDTO {
                @JSONField(name = "address_updated_at")
                private String addressUpdatedAt;
                @JSONField(name = "email_updated_at")
                private String emailUpdatedAt;
                @JSONField(name = "password_updated_at")
                private String passwordUpdatedAt;
                @JSONField(name = "products_updated_at")
                private String productsUpdatedAt;
                @JSONField(name = "sales_summary")
                private SalesSummaryDTO salesSummary;

                @NoArgsConstructor
                @Data
                public static class SalesSummaryDTO {
                    @JSONField(name = "currency")
                    private String currency;
                    @JSONField(name = "period")
                    private String period;
                    @JSONField(name = "sales_amount")
                    private Integer salesAmount;
                    @JSONField(name = "sales_count")
                    private Integer salesCount;
                }
            }

            @NoArgsConstructor
            @Data
            public static class BusinessInfoDTO {
                @JSONField(name = "address")
                private AddressDTO address;
                @JSONField(name = "email")
                private String email;
                @JSONField(name = "phone_number")
                private String phoneNumber;
                @JSONField(name = "postcode")
                private String postcode;
                @JSONField(name = "rating")
                private Double rating;
                @JSONField(name = "registration_date")
                private String registrationDate;

                @NoArgsConstructor
                @Data
                public static class AddressDTO {
                    @JSONField(name = "city")
                    private String city;
                    @JSONField(name = "country_code")
                    private String countryCode;
                    @JSONField(name = "postcode")
                    private String postcode;
                    @JSONField(name = "state")
                    private String state;
                    @JSONField(name = "street")
                    private String street;
                }
            }
        }

        @NoArgsConstructor
        @Data
        public static class TravelersDTO {
            @JSONField(name = "identifier")
            private String identifier;
            @JSONField(name = "name")
            private String name;
            @JSONField(name = "title")
            private String title;
        }
    }

    @NoArgsConstructor
    @Data
    public static class PaymentMethodOptionsDTO {
        @JSONField(name = "card")
        private CardDTO card;

        @NoArgsConstructor
        @Data
        public static class CardDTO {
            @JSONField(name = "authorization_type")
            private String authorizationType;
            @JSONField(name = "auto_capture")
            private Boolean autoCapture;
            @JSONField(name = "risk_control")
            private RiskControlDTO riskControl;
            @JSONField(name = "three_ds_action")
            private String threeDsAction;

            @NoArgsConstructor
            @Data
            public static class RiskControlDTO {
                @JSONField(name = "skip_risk_processing")
                private Boolean skipRiskProcessing;
                @JSONField(name = "three_domain_secure_action")
                private String threeDomainSecureAction;
                @JSONField(name = "three_ds_action")
                private String threeDsAction;
            }
        }
    }

    @NoArgsConstructor
    @Data
    public static class RiskControlOptionsDTO {
        @JSONField(name = "skip_risk_processing")
        private Boolean skipRiskProcessing;
        @JSONField(name = "tra_applicable")
        private Boolean traApplicable;
    }

    @NoArgsConstructor
    @Data
    public static class FundsSplitDataDTO {
        @JSONField(name = "amount")
        private Double amount;
        @JSONField(name = "destination")
        private String destination;
    }
}
