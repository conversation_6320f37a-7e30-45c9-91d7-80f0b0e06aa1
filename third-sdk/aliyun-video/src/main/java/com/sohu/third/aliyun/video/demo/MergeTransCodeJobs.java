package com.sohu.third.aliyun.video.demo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.mts.model.v20140618.SubmitJobsRequest;
import com.aliyuncs.mts.model.v20140618.SubmitJobsResponse;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * *****   使用须知     ******
 * 本demo为视频拼接和简单剪辑示例。
 *
 * *****   方法介绍     ******
 * mergrUrlListJob  URL方式视频拼接
 * mergrConfigFileJob   配置文件方式视频拼接
 * openAndTailJob   拼接开板、尾板视频
 *
 * 更多参数请参考  https://help.aliyun.com/document_detail/29253.html#section-ic9-6n4-ebc
 */
public class MergeTransCodeJobs {


    /**管道ID, 可以在控制台 【全局设置】->【管道】查看*/
    private static String pipelineId = "83134ae5f44341b080851c2357c968d2";
    /**模板ID, 预置模板参考 https://help.aliyun.com/document_detail/29256.html*/
    private static String templateId = "S00000001-200010";

    public static void main(String[] args) throws ClientException {

        //初始化调用 client
//        DefaultAcsClient acsClient = AliyunVideoUtil.getAcsClient();
//        SubmitJobsResponse response;
//        try {
//            response = AliyunVideoUtil.mergrUrlListJob(acsClient);
//            System.out.println("RequestId is:" + response.getRequestId());
//            System.out.println("JobId is:" + JSON.toJSON(response.getJobResultList().get(0).getJob().getJobId()));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }

        //初始化调用 client
//        DefaultAcsClient client = AliyunVideoClient.initMpsClient();
//        SubmitJobsResponse response;
//        try {
//            response = mergrUrlListJob(acsClient);
//            System.out.println("RequestId is:" + response.getRequestId());
//            System.out.println("JobId is:" + JSON.toJSON(response.getJobResultList().get(0).getJob().getJobId()));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }

    }

    /**
     * 普通视频拼接任务  URL方式
     * @param client
     * @return
     * @throws Exception
     */
    public static SubmitJobsResponse mergrUrlListJob(DefaultAcsClient client) throws Exception {

        SubmitJobsRequest request = new SubmitJobsRequest();
        //构建输出参数
        JSONArray outputs = new JSONArray();

        //构建input, 需要保证Location区域和服务client区域一致
        JSONObject input = new JSONObject();
        // Bucket Name
        input.put("Bucket", "test-sohuvideo");
        input.put("Location", "oss-cn-beijing");

        //构建一个输出对象
        JSONObject output = new JSONObject();
        try {
            // 正片(需要与阿里云Bucket路径对应,否则会失败)
            input.put("Object", URLEncoder.encode("abc.mp4", "utf-8"));
            // 拼接之后的视频(需要与阿里云Bucket路径对应,否则会失败)
            String outPutObject = URLEncoder.encode("merge.mp4", "utf-8");
            output.put("OutputObject", outPutObject);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("input URL encode failed");
        }
        // 模板id,用来构建视频转码率
        output.put("TemplateId", templateId);

        //构建一个素材截取数据
        JSONObject clip = new JSONObject();
        //代表从01秒000毫秒开始，截取到第5秒30毫秒为止(正片(截取时间段进行拼接))
        clip.put("TimeSpan", "{\"Seek\":\"00:00:00.000\",\"Duration\":\"02.30\"}");
        //代表从01秒000毫秒开始，截取到距离片尾剩余5秒30毫秒为止(正片(预留时间段拼接))
//        clip.put("TimeSpan", "{\"Seek\":\"00:00:01.000\",\"End\":\"5.30\"}");
        //true：先剪辑第一个片段，再拼接（转码）
        clip.put("ConfigToClipFirstPart", true);

        //构建一个拼接数据(需要拼接的片尾视频),数组的方式可以拼接五个视频
        JSONArray mergeList = new JSONArray();
        JSONObject merge = new JSONObject();
        // (需要与阿里云Bucket路径对应,否则会失败)
    	merge.put("MergeURL", "http://test-sohuvideo.oss-cn-beijing.aliyuncs.com/" + URLEncoder.encode("test.mp4", "utf-8"));
        // 截取片尾开始时间
        merge.put("Start", "00:00:00.000");
        // 截取片尾结束时间
        merge.put("Duration", "00:00:23.000");

        mergeList.add(merge);
        output.put("Clip", clip);
        output.put("MergeList", mergeList);
        
        outputs.add(output);
        request.setInput(input.toJSONString());
        request.setOutputs(outputs.toJSONString());
        //指定输出bucket
        request.setOutputBucket("test-sohuvideo");
        //输出bucket的所在区域,格式: oss-cn-****, 例如北京  oss-cn-beijing
        request.setOutputLocation("oss-cn-beijing");
        // PipelineId
        request.setPipelineId(pipelineId);

        return client.getAcsResponse(request);
    }

    /**
     * 普通视频拼接任务  ConfigFile方式
     * @param client
     * @return
     * @throws Exception
     */
    public static SubmitJobsResponse mergrConfigFileJob(DefaultAcsClient client) throws Exception {

        SubmitJobsRequest request = new SubmitJobsRequest();
        //构建输出参数
        JSONArray outputs = new JSONArray();

        //构建input, 需要保证Location区域和服务client区域一致
        JSONObject input = new JSONObject();
        input.put("Bucket", "test-sohuvideo");
        input.put("Location", "oss-cn-beijing");

        //构建一个输出对象
        JSONObject output = new JSONObject();
        try {
            input.put("Object", URLEncoder.encode("test.mp4", "utf-8"));
            String outPutObject = URLEncoder.encode("merge.mp4", "utf-8");
            output.put("OutputObject", outPutObject);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("input URL encode failed");
        }
        output.put("TemplateId", templateId);

        //构建一个素材截取数据
        JSONObject clip = new JSONObject();
        //代表从01秒000毫秒开始，截取到第5秒30毫秒为止
        //clip.put("TimeSpan", "{\"Seek\":\"00:00:01.000\",\"Duration\":\"5.30\"}");
        //代表从01秒000毫秒开始，截取到距离片尾剩余5秒30毫秒为止
        clip.put("TimeSpan", "{\"Seek\":\"00:00:00.000\",\"End\":\"5.30\"}");
        //true：先剪辑第一个片段，再拼接（转码）
        clip.put("ConfigToClipFirstPart", true);

        output.put("Clip", clip);
        //MergeConfigUrl的地址必须为HTTP地址
        output.put("MergeConfigUrl", "http://test-sohuvideo.oss-cn-beijing.aliyuncs.com/abc.mp4");
        
        outputs.add(output);
        request.setInput(input.toJSONString());
        request.setOutputs(outputs.toJSONString());
        //指定输出bucket
        request.setOutputBucket("test-sohuvideo");
        //输出bucket的所在区域,格式: oss-cn-****, 例如北京  oss-cn-beijing
        request.setOutputLocation("oss-cn-beijing");
        // PipelineId
        request.setPipelineId(pipelineId);

        return client.getAcsResponse(request);
    }


    /**
     * 开板尾板拼接任务
     * @param client
     * @return
     * @throws Exception
     */
    public static SubmitJobsResponse openAndTailJob(DefaultAcsClient client) throws Exception {

        SubmitJobsRequest request = new SubmitJobsRequest();
        //构建输出参数
        JSONArray outputs = new JSONArray();

        //构建input, 需要保证Location区域和服务client区域一致
        JSONObject input = new JSONObject();
        input.put("Location", "oss-cn-beijing");
        input.put("Bucket", "<your bucket name>");

        //构建一个输出对象
        JSONObject output = new JSONObject();
        try {
            input.put("Object", URLEncoder.encode("mps-test/demo/test.mp4", "utf-8"));
            String outPutObject = URLEncoder.encode("mps-test/demo/test-open-tail.mp4", "utf-8");
            output.put("OutputObject", outPutObject);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("input URL encode failed");
        }
        output.put("TemplateId", templateId);

        JSONArray openingList = new JSONArray();
        JSONObject opening = new JSONObject();
        opening.put("OpenUrl", "http://bucket-name.oss-cn-beijing.aliyuncs.com/" + URLEncoder.encode("mps-test/demo/open.mp4", "utf-8"));
        opening.put("Start", "3");
        opening.put("Width", "680");
        opening.put("Height", "480");
        openingList.add(opening);

        JSONArray tailSlateList = new JSONArray();
        JSONObject tailSlate = new JSONObject();
        tailSlate.put("TailUrl", "http://bucket-name.oss-cn-beijing.aliyuncs.com/" + URLEncoder.encode("mps-test/demo/tail.mp4", "utf-8"));
        tailSlate.put("BlendDuration", "2");
        tailSlate.put("Width", "680");
        tailSlate.put("Height", "480");
        tailSlate.put("IsMergeAudio", true);
        tailSlate.put("BgColor", "White");
        tailSlateList.add(tailSlate);

        output.put("OpeningList", openingList);
        output.put("TailSlateList", tailSlateList);
        outputs.add(output);

        request.setInput(input.toJSONString());
        request.setOutputs(outputs.toJSONString());
        //指定输出bucket
        request.setOutputBucket("<your bucket name>");
        //输出bucket的所在区域,格式: oss-cn-****, 例如北京  oss-cn-beijing
        request.setOutputLocation("oss-cn-beijing");
        // PipelineId
        request.setPipelineId(pipelineId);

        return client.getAcsResponse(request);
    }

}