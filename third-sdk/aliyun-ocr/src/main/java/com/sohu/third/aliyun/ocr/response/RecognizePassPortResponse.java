package com.sohu.third.aliyun.ocr.response;

import cn.hutool.core.date.DateUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 阿里云的护照识别转化后
 * <AUTHOR>
 * @date 2024/9/21 10:43
 **/
@NoArgsConstructor
@Data
public class RecognizePassPortResponse {

    /**
     * 证件号码
     */
    private String passportNumber;

    /**
     * 英文名称
     */
    private String nameEn;

    /**
     * 中文名称
     */
    private String name;

    /**
     * 签发国家
     */
    private String country;
    /**
     * 证件有效期
     */
    private String validToDate;
    /**
     * 签发日期
     */
    private String issueDateYmd;

    public RecognizePassPortResponse build(AliyunOcrRecognizePassPortResponse response) {
        RecognizePassPortResponse result = new RecognizePassPortResponse();
        AliyunOcrRecognizePassPortResponse.DataBean dataDetail= response.getData();
        result.setName(dataDetail.getName());
        result.setPassportNumber(dataDetail.getPassportNumber());
        result.setCountry(dataDetail.getCountry());
        result.setIssueDateYmd(DateUtil.format(DateUtil.parse(dataDetail.getIssueDateYmd()), "yyyyMMdd"));
        result.setValidToDate(DateUtil.format(DateUtil.parse(dataDetail.getValidToDate()),"yyyyMMdd"));
        result.setNameEn(dataDetail.getNameEn());
        return result;
    }
}
