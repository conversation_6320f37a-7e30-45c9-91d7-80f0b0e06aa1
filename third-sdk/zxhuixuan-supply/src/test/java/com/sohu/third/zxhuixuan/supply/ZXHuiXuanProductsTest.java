package com.sohu.third.zxhuixuan.supply;

import cn.hutool.json.JSONUtil;
import com.sohu.third.zxhuixuan.supply.constant.ZXHuiXuanConstant;
import com.sohu.third.zxhuixuan.supply.model.ZXHuiXuanConfig;
import com.sohu.third.zxhuixuan.supply.request.product.*;
import com.sohu.third.zxhuixuan.supply.response.product.*;
import com.sohu.third.zxhuixuan.supply.service.ZXHuiXuanProductsService;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * 商品单元测试
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 16:33
 */
public class ZXHuiXuanProductsTest {

    private static ZXHuiXuanConfig config;

    static {
        config = new ZXHuiXuanConfig();
        config.setAppId(ZXHuiXuanConstant.APPID);
        config.setSecret(ZXHuiXuanConstant.SECRET);
    }

    /**
     * 获取商品品牌单元测试
     */
    @Test
    public void getBrandsTest() {
        ZXHuiXuanProductBrandRequest request = new ZXHuiXuanProductBrandRequest();
        request.setPage(String.valueOf(1));
        request.setPerPage(String.valueOf(10));
        request.setBrandId("1");
        ZXHuiXuanProductBrandResponse response = ZXHuiXuanProductsService.getBrands(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 获取分类列表单元测试
     */
    @Test
    public void getCategoryTest() {
        ZXHuiXuanProductCategoryRequest request = new ZXHuiXuanProductCategoryRequest();
//        request.setClassId("63");
        ZXHuiXuanProductCategoryResponse response = ZXHuiXuanProductsService.getCategory(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 查询商品库SPUID列表单元测试
     */
    @Test
    public void getSpuIdListTest() {
        ZXHuiXuanProductSpuListRequest request = new ZXHuiXuanProductSpuListRequest();
        ZXHuiXuanProductSpuListResponse response = ZXHuiXuanProductsService.getSpuIdList(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 查询商品库SPUID列表单元测试
     */
    @Test
    public void getSpuIdDetailsTest() {
        ZXHuiXuanProductSpuDetailsRequest request = new ZXHuiXuanProductSpuDetailsRequest();
        request.setSpuId(559847L);
        ZXHuiXuanProductSpuDetailsResponse response = ZXHuiXuanProductsService.getSpuIdDetails(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 查询商品库SPUID列表单元测试
     */
    @Test
    public void getSkuInfoTest() {
        ZXHuiXuanProductSkuInfoRequest request = new ZXHuiXuanProductSkuInfoRequest();
        request.setSkuId("2009082");
        ZXHuiXuanProductSkuInfoResponse response = ZXHuiXuanProductsService.getSkuInfo(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 查询商品价格单元测试
     */
    @Test
    public void getSkuSalePriceTest() {
        ZXHuiXuanProductSkuSalePriceRequest request = new ZXHuiXuanProductSkuSalePriceRequest();
        request.setSkuId("2009082");
        ZXHuiXuanProductSkuSalePriceResponse response = ZXHuiXuanProductsService.getSkuSalePrice(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 查询商品库存单元测试
     */
    @Test
    public void getSkuStockTest() {
        ZXHuiXuanProductSkuStockRequest request = new ZXHuiXuanProductSkuStockRequest();
        List<ZXHuiXuanProductSkuStockRequest.SkuItem> skuList = new ArrayList<>();
        ZXHuiXuanProductSkuStockRequest.SkuItem skuItem = new ZXHuiXuanProductSkuStockRequest.SkuItem();
        skuItem.setSkuId("2009082");
        skuItem.setSkuNum("1");
        skuList.add(skuItem);
        request.setSkuList(skuList);
        request.setShipAreaCode("************,************,************");
        ZXHuiXuanProductSkuStockResponse response = ZXHuiXuanProductsService.getSkuStock(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 查询商品运费模板(优选版块)单元测试
     */
    @Test
    public void getTemplateFreightTest() {
        ZXHuiXuanProductTemplateFreightRequest request = new ZXHuiXuanProductTemplateFreightRequest();
        request.setSpuId("478861");
        ZXHuiXuanProductTemplateFreightResponse response = ZXHuiXuanProductsService.getTemplateFreight(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 查询商品获取板块标识单元测试
     */
    @Test
    public void getTypesTest() {
        ZXHuiXuanProductTypesResponse response = ZXHuiXuanProductsService.getTypes(config);
        System.out.println(JSONUtil.toJsonStr(response));
    }
}
