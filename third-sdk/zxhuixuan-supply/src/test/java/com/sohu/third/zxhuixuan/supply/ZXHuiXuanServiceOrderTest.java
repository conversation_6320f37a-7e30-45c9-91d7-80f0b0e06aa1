package com.sohu.third.zxhuixuan.supply;

import cn.hutool.json.JSONUtil;
import com.sohu.third.zxhuixuan.supply.constant.ZXHuiXuanConstant;
import com.sohu.third.zxhuixuan.supply.model.ZXHuiXuanConfig;
import com.sohu.third.zxhuixuan.supply.request.service.ZXHuiXuanServiceCancelRequest;
import com.sohu.third.zxhuixuan.supply.request.service.ZXHuiXuanServiceExpressSubmitRequest;
import com.sohu.third.zxhuixuan.supply.request.service.ZXHuiXuanServiceInfoRequest;
import com.sohu.third.zxhuixuan.supply.request.service.ZXHuiXuanServiceSubmitRequest;
import com.sohu.third.zxhuixuan.supply.response.service.ZXHuiXuanServiceCancelResponse;
import com.sohu.third.zxhuixuan.supply.response.service.ZXHuiXuanServiceExpressSubmitResponse;
import com.sohu.third.zxhuixuan.supply.response.service.ZXHuiXuanServiceInfoResponse;
import com.sohu.third.zxhuixuan.supply.response.service.ZXHuiXuanServiceSubmitResponse;
import com.sohu.third.zxhuixuan.supply.service.ZXHuiXuanServiceOrderService;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;

/**
 * 售后订单单元测试
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 16:33
 */
public class ZXHuiXuanServiceOrderTest {

    private static ZXHuiXuanConfig config;

    static {
        config = new ZXHuiXuanConfig();
        config.setAppId(ZXHuiXuanConstant.APPID);
        config.setSecret(ZXHuiXuanConstant.SECRET);
    }

    /**
     * 售后申请单元测试
     */
    @Test
    public void serviceSubmitTest() {
        ZXHuiXuanServiceSubmitRequest request = new ZXHuiXuanServiceSubmitRequest();
        request.setImages(new ArrayList<>());
        request.setServiceType(1);
        request.setChannelOrderNo("VN" + new Date().getTime());
        request.setRemark("张三");
        request.setSkuId(2009082L);
        request.setSkuNum(1);
        request.setAmountMoney(null);
        ZXHuiXuanServiceSubmitResponse response = ZXHuiXuanServiceOrderService.serviceSubmit(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 售后订单详情单元测试
     */
    @Test
    public void getServiceDetailsTest() {
        ZXHuiXuanServiceInfoRequest request = new ZXHuiXuanServiceInfoRequest();
        request.setChannelServiceNo("VN1751858849072");
        ZXHuiXuanServiceInfoResponse response = ZXHuiXuanServiceOrderService.getServiceDetails(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 取消售后申请单元测试
     */
    @Test
    public void serviceCancelTest() {
        ZXHuiXuanServiceCancelRequest request = new ZXHuiXuanServiceCancelRequest();
        request.setChannelServiceNo("VN1751858849072");
        ZXHuiXuanServiceCancelResponse response = ZXHuiXuanServiceOrderService.serviceCancel(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 退换货填写运单号单元测试
     */
    @Test
    public void serviceExpressSubmitTest() {
        ZXHuiXuanServiceExpressSubmitRequest request = new ZXHuiXuanServiceExpressSubmitRequest();
        request.setChannelServiceNo("VN1751858849072");
        request.setExpressId("1");
        request.setExpressNo("VN1751858849072");
        request.setAmountMoney(new BigDecimal("5"));
        ZXHuiXuanServiceExpressSubmitResponse response = ZXHuiXuanServiceOrderService.serviceExpressSubmit(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }
}
