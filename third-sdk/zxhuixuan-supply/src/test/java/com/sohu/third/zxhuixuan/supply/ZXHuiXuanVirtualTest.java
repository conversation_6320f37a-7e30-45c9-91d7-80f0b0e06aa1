package com.sohu.third.zxhuixuan.supply;

import cn.hutool.json.JSONUtil;
import com.sohu.third.zxhuixuan.supply.constant.ZXHuiXuanConstant;
import com.sohu.third.zxhuixuan.supply.model.ZXHuiXuanConfig;
import com.sohu.third.zxhuixuan.supply.request.virtual.ZXHuiXuanVirtualListRequest;
import com.sohu.third.zxhuixuan.supply.request.virtual.ZXHuiXuanVirtualOrderInfoRequest;
import com.sohu.third.zxhuixuan.supply.request.virtual.ZXHuiXuanVirtualOrderSubmitRequest;
import com.sohu.third.zxhuixuan.supply.response.virtual.ZXHuiXuanVirtualListResponse;
import com.sohu.third.zxhuixuan.supply.response.virtual.ZXHuiXuanVirtualOrderInfoResponse;
import com.sohu.third.zxhuixuan.supply.response.virtual.ZXHuiXuanVirtualOrderSubmitResponse;
import com.sohu.third.zxhuixuan.supply.service.ZXHuiXuanVirtualService;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 虚拟商品单元测试
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 16:33
 */
public class ZXHuiXuanVirtualTest {

    private static ZXHuiXuanConfig config;

    static {
        config = new ZXHuiXuanConfig();
        config.setAppId(ZXHuiXuanConstant.APPID);
        config.setSecret(ZXHuiXuanConstant.SECRET);
    }

    /**
     * 获取虚拟商品列表单元测试
     */
    @Test
    public void getListTest() {
        ZXHuiXuanVirtualListRequest request = new ZXHuiXuanVirtualListRequest();
        request.setPage(String.valueOf(1));
        request.setLimit(String.valueOf(10));
        ZXHuiXuanVirtualListResponse response = ZXHuiXuanVirtualService.getList(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 虚拟商品下单单元测试
     */
    @Test
    public void orderSubmitTest() {
        ZXHuiXuanVirtualOrderSubmitRequest request = new ZXHuiXuanVirtualOrderSubmitRequest();
        request.setVirtualId(6);
        request.setUsername("18086690824");
        request.setQuantity(1);
        request.setPrice(new BigDecimal("1"));
        request.setOrderNo("VN" + new Date().getTime());
        ZXHuiXuanVirtualOrderSubmitResponse response = ZXHuiXuanVirtualService.orderSubmit(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 查询虚拟订单详情单元测试
     */
    @Test
    public void getOrderInfoTest() {
        ZXHuiXuanVirtualOrderInfoRequest request = new ZXHuiXuanVirtualOrderInfoRequest();
        request.setChannelOrderNo("XN2024072398515757525");
        ZXHuiXuanVirtualOrderInfoResponse response = ZXHuiXuanVirtualService.getOrderInfo(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

}