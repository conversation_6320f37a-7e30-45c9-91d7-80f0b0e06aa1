package com.sohu.third.zxhuixuan.supply;

import cn.hutool.json.JSONUtil;
import com.sohu.third.zxhuixuan.supply.constant.ZXHuiXuanConstant;
import com.sohu.third.zxhuixuan.supply.model.ZXHuiXuanConfig;
import com.sohu.third.zxhuixuan.supply.request.comm.ZXHuiXuanCommAreaRequest;
import com.sohu.third.zxhuixuan.supply.response.comm.ZXHuiXuanCommAreaResponse;
import com.sohu.third.zxhuixuan.supply.response.comm.ZXHuiXuanCommExpressResponse;
import com.sohu.third.zxhuixuan.supply.service.ZXHuiXuanCommService;
import org.junit.Test;

/**
 * 公共单元测试
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 16:33
 */
public class ZXHuiXuanCommTest {

    private static ZXHuiXuanConfig config;

    static {
        config = new ZXHuiXuanConfig();
        config.setAppId(ZXHuiXuanConstant.APPID);
        config.setSecret(ZXHuiXuanConstant.SECRET);
    }

    /**
     * 获取地区相关单元测试
     */
    @Test
    public void getListTest() {
        ZXHuiXuanCommAreaRequest request = new ZXHuiXuanCommAreaRequest();
//        request.setAreaId("4201");
        ZXHuiXuanCommAreaResponse response = ZXHuiXuanCommService.getAreaList(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 获取物流公司信息单元测试
     */
    @Test
    public void getExpressTest() {
        ZXHuiXuanCommExpressResponse response = ZXHuiXuanCommService.getExpress(config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

}