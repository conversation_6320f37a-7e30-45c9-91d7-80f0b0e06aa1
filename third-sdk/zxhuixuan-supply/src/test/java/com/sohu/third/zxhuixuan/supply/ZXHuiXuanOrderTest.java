package com.sohu.third.zxhuixuan.supply;

import cn.hutool.json.JSONUtil;
import com.sohu.third.zxhuixuan.supply.constant.ZXHuiXuanConstant;
import com.sohu.third.zxhuixuan.supply.model.ZXHuiXuanConfig;
import com.sohu.third.zxhuixuan.supply.request.order.*;
import com.sohu.third.zxhuixuan.supply.response.order.*;
import com.sohu.third.zxhuixuan.supply.service.ZXHuiXuanOrderService;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 订单单元测试
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 16:33
 */
public class ZXHuiXuanOrderTest {

    private static ZXHuiXuanConfig config;

    static {
        config = new ZXHuiXuanConfig();
        config.setAppId(ZXHuiXuanConstant.APPID);
        config.setSecret(ZXHuiXuanConstant.SECRET);
    }

    /**
     * 商品下单单元测试
     */
    @Test
    public void orderSubmitTest() {
        ZXHuiXuanOrderSubmitRequest request = new ZXHuiXuanOrderSubmitRequest();
        request.setUsername("张三");
        request.setUserMobile("***********");
        request.setUserAddress("湖北省武汉市武昌区桃源国际1栋9楼");
        request.setShipAreaCode("************,************,************");
        List<ZXHuiXuanOrderSubmitRequest.Sku> skuList = new ArrayList<>();
        ZXHuiXuanOrderSubmitRequest.Sku sku = new ZXHuiXuanOrderSubmitRequest.Sku();
        sku.setSkuId(2009082L);
        sku.setSkuNum(1);
        sku.setPrice(new BigDecimal("1.64"));
        skuList.add(sku);
        request.setSkuList(skuList);
        request.setOrderNo("VN" + new Date().getTime());
        ZXHuiXuanOrderSubmitResponse response = ZXHuiXuanOrderService.orderSubmit(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 查询订单状态单元测试
     */
    @Test
    public void getOrderStatusTest() {
        ZXHuiXuanOrderStatusRequest request = new ZXHuiXuanOrderStatusRequest();
        request.setChannelOrderNo("VN1751*********");
        ZXHuiXuanOrderStatusResponse response = ZXHuiXuanOrderService.getOrderStatus(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 查询订单详情单元测试
     */
    @Test
    public void getOrderInfoTest() {
        ZXHuiXuanOrderInfoRequest request = new ZXHuiXuanOrderInfoRequest();
        request.setChannelOrderNo("VN1751*********");
        ZXHuiXuanOrderInfoResponse response = ZXHuiXuanOrderService.getOrderInfo(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 订单确认收货单元测试
     */
    @Test
    public void orderConfirmTest() {
        ZXHuiXuanOrderInfoRequest request = new ZXHuiXuanOrderInfoRequest();
        request.setChannelOrderNo("VN1751*********");
        ZXHuiXuanOrderConfirmResponse response = ZXHuiXuanOrderService.orderConfirm(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 查询多商品查询运费单元测试
     */
    @Test
    public void getOrderPostageTest() {
        ZXHuiXuanOrderPostageRequest request = new ZXHuiXuanOrderPostageRequest();
        request.setShipAreaCode("************,************,************");
        List<ZXHuiXuanOrderPostageRequest.Sku> skuList = new ArrayList<>();
        ZXHuiXuanOrderPostageRequest.Sku sku = new ZXHuiXuanOrderPostageRequest.Sku();
        sku.setSkuId(2009082L);
        sku.setSkuNum(1);
        skuList.add(sku);
        request.setSkuList(skuList);
        ZXHuiXuanOrderPostageResponse response = ZXHuiXuanOrderService.getOrderPostage(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 订单收件人信息更新（待发货状态）单元测试
     */
    @Test
    public void orderShipUpdateTest() {
        ZXHuiXuanOrderShipUpdateRequest request = new ZXHuiXuanOrderShipUpdateRequest();
        request.setChannelOrderNo("VN1751*********");
        request.setShipAreaCode("************,************,************");
        request.setUserName("张三");
        request.setUserMobile("***********");
        request.setUserAddress("湖北省武汉市武昌区桃源国际1栋9楼");
        ZXHuiXuanOrderShipUpdateResponse response = ZXHuiXuanOrderService.orderShipUpdate(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 校验账户余额单元测试
     */
    @Test
    public void getAccountBalanceTest() {
        ZXHuiXuanOrderAccountBalanceResponse response = ZXHuiXuanOrderService.getAccountBalance(config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

}