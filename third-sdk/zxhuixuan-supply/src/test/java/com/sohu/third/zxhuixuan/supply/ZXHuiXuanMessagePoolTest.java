package com.sohu.third.zxhuixuan.supply;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.third.zxhuixuan.supply.constant.ZXHuiXuanConstant;
import com.sohu.third.zxhuixuan.supply.model.ZXHuiXuanConfig;
import com.sohu.third.zxhuixuan.supply.request.messagepool.ZXHuiXuanMessagePoolRemoveBatchRequest;
import com.sohu.third.zxhuixuan.supply.request.messagepool.ZXHuiXuanMessagePoolRemoveRequest;
import com.sohu.third.zxhuixuan.supply.request.messagepool.ZXHuiXuanMessagePoolRequest;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import com.sohu.third.zxhuixuan.supply.response.messagepool.ZXHuiXuanMessagePoolResponse;
import com.sohu.third.zxhuixuan.supply.service.ZXHuiXuanMessagePoolService;
import org.junit.Test;

/**
 * 消息池单元测试
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 16:33
 */
public class ZXHuiXuanMessagePoolTest {

    private static ZXHuiXuanConfig config;

    static {
        config = new ZXHuiXuanConfig();
        config.setAppId(ZXHuiXuanConstant.APPID);
        config.setSecret(ZXHuiXuanConstant.SECRET);
    }

    /**
     * 获取查询消息池数据单元测试
     */
    @Test
    public void getMessagePoolTest() {
        ZXHuiXuanMessagePoolRequest request = new ZXHuiXuanMessagePoolRequest();
        request.setType(0);
        request.setPageLimit(1000);
        ZXHuiXuanMessagePoolResponse response = ZXHuiXuanMessagePoolService.getMessagePool(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 获取分类列表单元测试
     */
    @Test
    public void removeMessagePoolTest() {
        ZXHuiXuanMessagePoolRemoveRequest request = new ZXHuiXuanMessagePoolRemoveRequest();
        request.setMessageId(62430);
        ZXHuiXuanBaseResponse response = ZXHuiXuanMessagePoolService.removeMessagePool(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    /**
     * 查询商品库SPUID列表单元测试
     */
    @Test
    public void removeBatchMessagePoolTest() {
        ZXHuiXuanMessagePoolRemoveBatchRequest request = new ZXHuiXuanMessagePoolRemoveBatchRequest();
        request.setIds(CollectionUtil.toList(62887));
        ZXHuiXuanBaseResponse response = ZXHuiXuanMessagePoolService.removeBatchMessagePool(request, config);
        System.out.println(JSONUtil.toJsonStr(response));
    }
}