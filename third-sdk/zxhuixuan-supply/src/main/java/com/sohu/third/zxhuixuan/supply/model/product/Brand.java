package com.sohu.third.zxhuixuan.supply.model.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 品牌
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 16:13
 */
@Data
public class Brand implements Serializable {

    /**
     * 品牌ID
     */
    private Long id;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 关联分类ID
     */
    @JsonProperty(value = "class_id")
    private String classId;

    /**
     * 品牌LOGO
     */
    @JsonProperty(value = "thumbnail_img")
    private String thumbnailImg;
}
