package com.sohu.third.zxhuixuan.supply.request.messagepool;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 甄新汇选供应链批量删除消息池数据请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanMessagePoolRemoveBatchRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 消息IDs
     */
    private List<Integer> ids;

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (CollectionUtil.isNotEmpty(ids)) {
            map.put("idArr", ids);
        }
        return map;
    }

}
