package com.sohu.third.zxhuixuan.supply.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * 售后状态枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 8/7/2025 上午 11:47
 */
@Getter
public enum AfterSaleStatusEnum {

    NO_AFTER_SALE(1, "无售后"),
    IN_AFTER_SALE(2, "售后中"),
    AFTER_SALE_COMPLETED(3, "售后完成");

    private final Integer code;

    private final String description;

    AfterSaleStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据Code获取枚举
     *
     * @param code
     * @return
     */
    public static AfterSaleStatusEnum getCode(Integer code) {
        for (AfterSaleStatusEnum status : AfterSaleStatusEnum.values()) {
            if (Objects.equals(status.code, code)) {
                return status;
            }
        }
        return null;
    }
}
