package com.sohu.third.zxhuixuan.supply.model.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 可配送区域（不包邮）
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/4 15:05
 */
@Data
public class TemplatesRegion {

    /**
     * 可配送区域（不包邮）
     */
    private List<String> areas;
    /**
     * 续件
     */
    private String templatesRegionContinue;
    /**
     * 续件运费
     */
    private BigDecimal continuePrice;
    /**
     * 首件
     */
    private String first;
    /**
     * 首件运费
     */
    private BigDecimal firstPrice;
    /**
     * 模板id
     */
    @JsonProperty(value = "temp_id")
    private Long tempId;
    /**
     * 计费方式，计费方式（1=按件数，2=按重量(KG)，3=按体积(m³)）
     */
    private Long type;
}
