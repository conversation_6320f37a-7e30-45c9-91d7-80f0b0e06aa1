package com.sohu.third.zxhuixuan.supply.request.virtual;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 甄新汇选供应链获取虚拟商品下单请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanVirtualOrderSubmitRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 虚拟商品id
     */
    private Integer virtualId;

    /**
     * 当前页
     */
    private String username;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 第三方订单号
     */
    private String orderNo;

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (Objects.nonNull(this.virtualId)) {
            map.put("virtual_id", this.virtualId);
        }
        if (StringUtils.isNotEmpty(this.username)) {
            map.put("username", this.username);
        }
        if (Objects.nonNull(this.quantity)) {
            map.put("quantity", this.quantity);
        }
        if (StringUtils.isNotEmpty(this.orderNo)) {
            map.put("third_sn", this.orderNo);
        }
        if (Objects.nonNull(this.price)) {
            map.put("price", this.price);
        }
        return map;
    }

}
