package com.sohu.third.zxhuixuan.supply.model.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 运费模板
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/4 15:04
 */
@Data
public class TemplatesFreight {

    /**
     * 模板id
     */
    private Long id;
    /**
     * 指定包邮区域数据组
     */
    @JsonProperty(value = "templates_free")
    private List<TemplatesFree> templatesFree;

    /**
     * 指定不送达区域数据组
     */
    @JsonProperty(value = "templates_no_delivery")
    private List<TemplatesNoDelivery> templatesNoDelivery;

    /**
     * 可配送区域（不包邮）数据组
     */
    @JsonProperty(value = "templates_region")
    private List<TemplatesRegion> templatesRegion;

    /**
     * 计费方式，计费方式（1=按件数，2=按重量(KG)，3=按体积(m³)）
     */
    private Integer type;
}
