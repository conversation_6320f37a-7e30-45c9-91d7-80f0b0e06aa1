package com.sohu.third.zxhuixuan.supply.util;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;

/**
 * 签名工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 15:12
 */
public class SignUtils {

    /**
     * 签名生成
     *
     * @param data
     * @param secret
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String buildSign(Map<String, Object> data, String secret) {
        try {
            // 步骤1：参数排序
            Map<String, Object> sorted = new TreeMap<>(data);
            // 步骤2：构造类似 http_build_query 的字符串
            String queryString = buildQuery(sorted);
            // 步骤3：URL解码并去除空格
            queryString = URLDecoder.decode(queryString, "UTF-8").replace(" ", "");
            // 步骤4：拼接 secret 并加密为 MD5（大写）
            return md5(queryString + secret).toUpperCase();
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("签名生成异常");
        }
    }

    /**
     * 构建参数
     *
     * @param map
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String buildQuery(Map<String, Object> map) {
        StringBuilder sb = new StringBuilder();
        buildQuery(sb, "", map);
        return sb.toString();
    }

    /**
     * 构建参数
     *
     * @param sb
     * @param prefix
     * @param obj
     * @throws UnsupportedEncodingException
     */
    private static void buildQuery(StringBuilder sb, String prefix, Object obj) {
        try {
            if (obj instanceof Map) {
                Map<String, Object> map = (Map<String, Object>) obj;
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    String newPrefix = prefix.isEmpty() ? entry.getKey() : String.format("%s[%s]", prefix, entry.getKey());
                    buildQuery(sb, newPrefix, entry.getValue());
                }
            } else if (obj instanceof List) {
                List<Object> list = (List<Object>) obj;
                for (int i = 0; i < list.size(); i++) {
                    String newPrefix = String.format("%s[%d]", prefix, i);
                    buildQuery(sb, newPrefix, list.get(i));
                }
            } else {
                if (sb.length() > 0) sb.append("&");
                sb.append(URLEncoder.encode(prefix, "UTF-8"))
                        .append("=")
                        .append(URLEncoder.encode(obj.toString(), "UTF-8"));
            }
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("请求参数构建异常");
        }
    }

    /**
     * md5加密
     *
     * @param str
     * @return
     */
    public static String md5(String str) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(str.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5 Error", e);
        }
    }

    /**
     * 获取随机数
     *
     * @return
     */
    public static String getNonce() {
        return String.valueOf(new Random().nextInt(9999) + 1);
    }

    /**
     * 获取时间戳
     *
     * @return
     */
    public static String getTime() {
        return String.valueOf(System.currentTimeMillis() / 1000);
    }
}
