package com.sohu.third.zxhuixuan.supply.response.messagepool;

import com.sohu.third.zxhuixuan.supply.model.messagepool.Message;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 甄新汇选供应链消息池返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanMessagePoolResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 消息数据
     */
    private List<Message> messageList;

    public ZXHuiXuanMessagePoolResponse() {
    }

    public ZXHuiXuanMessagePoolResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

}
