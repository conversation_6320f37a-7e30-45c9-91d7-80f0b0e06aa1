package com.sohu.third.zxhuixuan.supply.service;

import cn.hutool.json.JSONUtil;
import com.sohu.third.zxhuixuan.supply.constant.ZXHuiXuanConstant;
import com.sohu.third.zxhuixuan.supply.model.ZXHuiXuanConfig;
import com.sohu.third.zxhuixuan.supply.model.comm.Area;
import com.sohu.third.zxhuixuan.supply.model.comm.Express;
import com.sohu.third.zxhuixuan.supply.request.comm.ZXHuiXuanCommAreaRequest;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import com.sohu.third.zxhuixuan.supply.response.comm.ZXHuiXuanCommAreaResponse;
import com.sohu.third.zxhuixuan.supply.response.comm.ZXHuiXuanCommExpressResponse;
import com.sohu.third.zxhuixuan.supply.util.ApiClient;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;

/**
 * 甄新汇选供应链-公共
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 15:12
 */
@Slf4j
public class ZXHuiXuanCommService {

    /**
     * 地区相关
     */
    public static ZXHuiXuanCommAreaResponse getAreaList(ZXHuiXuanCommAreaRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.OrderUrl.GET_AREA;
        String result = ApiClient.request(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanCommAreaResponse brandResponse = new ZXHuiXuanCommAreaResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            String dataListStr = JSONUtil.parseObj(result).getStr("data");
            List<Area> dataList = JSONUtil.toList(dataListStr, Area.class);
            brandResponse.setAreaList(dataList);
            return brandResponse;
        }
        return brandResponse;
    }

    /**
     * 获取物流公司信息
     */
    public static ZXHuiXuanCommExpressResponse getExpress(ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.OrderUrl.GET_EXPRESS;
        String result = ApiClient.request(url, new HashMap<>(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanCommExpressResponse orderInfoResponse = new ZXHuiXuanCommExpressResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            String dataStr = JSONUtil.parseObj(result).getStr("data");
            orderInfoResponse.setExpressList(JSONUtil.toList(dataStr, Express.class));
            return orderInfoResponse;
        }
        return orderInfoResponse;
    }
}
