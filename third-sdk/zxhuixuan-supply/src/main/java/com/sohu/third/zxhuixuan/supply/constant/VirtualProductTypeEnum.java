package com.sohu.third.zxhuixuan.supply.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 8/7/2025 上午 11:47
 */
@Getter
public enum VirtualProductTypeEnum {

    DIRECT_TOP_UP(1, "直冲"),
    CARD_CODE(2, "卡密");

    private final Integer code;

    private final String description;

    VirtualProductTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据Code获取枚举
     *
     * @param code
     * @return
     */
    public static VirtualProductTypeEnum getCode(Integer code) {
        for (VirtualProductTypeEnum status : VirtualProductTypeEnum.values()) {
            if (Objects.equals(status.code, code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据description获取枚举
     *
     * @param description
     * @return
     */
    public static VirtualProductTypeEnum getDescription(String description) {
        for (VirtualProductTypeEnum status : VirtualProductTypeEnum.values()) {
            if (Objects.equals(status.description, description)) {
                return status;
            }
        }
        return null;
    }
}
