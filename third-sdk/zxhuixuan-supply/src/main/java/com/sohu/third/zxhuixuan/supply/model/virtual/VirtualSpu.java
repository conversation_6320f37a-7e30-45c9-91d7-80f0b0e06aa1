package com.sohu.third.zxhuixuan.supply.model.virtual;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 虚拟商品
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/5 9:55
 */
@Data
public class VirtualSpu implements Serializable {

    /**
     * 虚拟商品ID
     */
    @JsonProperty(value = "virtual_id")
    private Long virtualId;

    /**
     * 商品明显
     */
    @JsonProperty(value = "store_name")
    private String storeName;

    /**
     * 商品图
     */
    private String image;

    /**
     * 零售价
     */
    @JsonProperty(value = "market_price")
    private BigDecimal marketPrice;

    /**
     * 商品类型，直冲或卡密
     */
    @JsonProperty(value = "product_type")
    private String productType;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 库存状态，断货、警报、充足（商品列表显示的商品都是非断货状态的商品，断货商品会自动下架）
     */
    @JsonProperty(value = "stock_status")
    private String stockStatus;

    /**
     * 是否虚拟商品
     */
    @JsonProperty(value = "is_virtual")
    private Integer isVirtual;

    /**
     * 详情
     */
    private String details;

    /**
     * 服务费
     */
    @JsonProperty(value = "service_price")
    private BigDecimal servicePrice;

    /**
     * 建议价
     */
    @JsonProperty(value = "group_price")
    private String groupPrice;

    /**
     * 上下架状态，1：下架 2：上架
     */
    private Integer status;

}
