package com.sohu.third.zxhuixuan.supply.model.order;

import cn.hutool.core.annotation.Alias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单详情
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/5 9:55
 */
@Data
public class OrderInfo implements Serializable {

    /**
     * 订单号
     */
    @JsonProperty(value = "order_sn")
    @Alias(value = "order_sn")
    private String channelOrderNo;

    /**
     * 第三方单号
     */
    @JsonProperty(value = "third_sn")
    @Alias(value = "third_sn")
    private String orderNo;

    /**
     * 支付价格
     */
    @JsonProperty(value = "pay_price")
    private String payPrice;

    /**
     * 订单状态
     * 2：推单成功 5：下单失败 6：完结
     */
    @JsonProperty(value = "order_status")
    private Integer orderStatus;

    /**
     * 充值状态
     */
    @JsonProperty(value = "third_remark")
    private String thirdRemark;

    /**
     * 完成时间
     */
    @JsonProperty(value = "over_at")
    private Date overAt;

    /**
     * 完成时间
     */
    @JsonProperty(value = "created_at")
    private Date createdAt;

}
