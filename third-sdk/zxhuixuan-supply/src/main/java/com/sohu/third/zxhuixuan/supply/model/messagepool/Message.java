package com.sohu.third.zxhuixuan.supply.model.messagepool;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息体
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/5 8:46
 */
@Data
public class Message implements Serializable {

    /**
     * 消息内容
     */
    private String content;
    /**
     * 创建时间
     */
    @JsonProperty(value = "created_at")
    private Date createdAt;
    /**
     * 消息ID
     */
    private Long id;
    /**
     * 消息类型
     */
    private Integer type;
}
