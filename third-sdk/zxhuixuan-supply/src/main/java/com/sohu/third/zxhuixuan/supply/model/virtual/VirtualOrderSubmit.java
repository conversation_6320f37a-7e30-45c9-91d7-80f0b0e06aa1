package com.sohu.third.zxhuixuan.supply.model.virtual;

import cn.hutool.core.annotation.Alias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 虚拟商品下单
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/5 9:55
 */
@Data
public class VirtualOrderSubmit implements Serializable {

    /**
     * 订单号
     */
    @JsonProperty(value = "order_sn")
    @Alias(value = "order_sn")
    private String channelOrderNo;

    /**
     * 虚拟商品ID
     */
    @JsonProperty(value = "virtual_id")
    private Long virtualId;

    /**
     * 下单数量
     */
    @JsonProperty(value = "amount")
    @Alias(value = "amount")
    private Integer quantity;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 服务费
     */
    @JsonProperty(value = "service_price")
    private BigDecimal servicePrice;

    /**
     * 支付价格
     */
    @JsonProperty(value = "pay_price")
    private String payPrice;

    /**
     * 第三方单号
     */
    @JsonProperty(value = "third_sn")
    @Alias(value = "third_sn")
    private String orderNo;

}
