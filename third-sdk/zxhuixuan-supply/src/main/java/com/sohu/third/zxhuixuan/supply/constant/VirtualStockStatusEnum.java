package com.sohu.third.zxhuixuan.supply.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 8/7/2025 上午 11:47
 */
@Getter
public enum VirtualStockStatusEnum {

    OUT_OF_STOCK(0, "断货"),
    LOW_STOCK(1, "警报"),
    IN_STOCK(2, "充足");

    private final Integer code;

    private final String description;

    VirtualStockStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据Code获取枚举
     *
     * @param code
     * @return
     */
    public static VirtualStockStatusEnum getCode(Integer code) {
        for (VirtualStockStatusEnum status : VirtualStockStatusEnum.values()) {
            if (Objects.equals(status.code, code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据description获取枚举
     *
     * @param description
     * @return
     */
    public static VirtualStockStatusEnum getDescription(String description) {
        for (VirtualStockStatusEnum status : VirtualStockStatusEnum.values()) {
            if (Objects.equals(status.description, description)) {
                return status;
            }
        }
        return null;
    }
}
