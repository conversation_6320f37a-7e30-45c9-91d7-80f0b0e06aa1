package com.sohu.third.zxhuixuan.supply.response.service;

import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * 甄新汇选供应链售后申请返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanServiceCancelResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    public ZXHuiXuanServiceCancelResponse() {
    }

    public ZXHuiXuanServiceCancelResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

}
