package com.sohu.third.zxhuixuan.supply.response.virtual;

import com.sohu.third.zxhuixuan.supply.model.product.Brand;
import com.sohu.third.zxhuixuan.supply.model.virtual.VirtualSpu;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 甄新汇选供应链虚拟商品列表返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanVirtualListResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 虚拟商品列表
     */
    private List<VirtualSpu> spuList;

    /**
     * 总条数
     */
    private Long total;

    public ZXHuiXuanVirtualListResponse() {
    }

    public ZXHuiXuanVirtualListResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

}
