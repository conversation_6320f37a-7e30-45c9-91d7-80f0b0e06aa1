package com.sohu.third.zxhuixuan.supply.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * 甄新汇选供应链默认返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;
    /**
     * 状态值
     */
    protected String status;

    /**
     * 状态码
     */
    protected String code;

    /**
     * 消息体
     */
    protected String message;

    public boolean isSuccess() {
        return Objects.equals("success", this.status) || Objects.equals("200", this.code);
    }
}
