package com.sohu.third.zxhuixuan.supply.response.product;

import com.sohu.third.zxhuixuan.supply.model.product.SkuSalePrice;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * 甄新汇选供应链查询sku价格返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanProductSkuSalePriceResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * sku价格
     */
    private SkuSalePrice price;

    public ZXHuiXuanProductSkuSalePriceResponse() {
    }

    public ZXHuiXuanProductSkuSalePriceResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }
}
