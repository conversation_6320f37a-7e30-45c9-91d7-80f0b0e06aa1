package com.sohu.third.zxhuixuan.supply.request.order;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 甄新汇选供应链订单收件人信息更新（待发货状态）请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanOrderShipUpdateRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 订单号
     */
    private String channelOrderNo;

    /**
     * 收货地区编码，例如:省ID,市ID,区ID 以英文‘,’逗号隔开
     */
    private String shipAreaCode;

    /**
     * 收件人详细地址
     */
    private String userAddress;

    /**
     * 收件人手机号码
     */
    private String userMobile;

    /**
     * 收件人姓名
     */
    private String userName;

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (StringUtils.isNotEmpty(this.channelOrderNo)) {
            map.put("order_sn", this.channelOrderNo);
        }
        if (StringUtils.isNotEmpty(this.shipAreaCode)) {
            map.put("shipAreaCode", this.shipAreaCode);
        }
        if (StringUtils.isNotEmpty(this.userName)) {
            map.put("user_name", this.userName);
        }
        if (StringUtils.isNotEmpty(this.userMobile)) {
            map.put("user_mobile", this.userMobile);
        }
        if (StringUtils.isNotEmpty(this.userAddress)) {
            map.put("user_address", this.userAddress);
        }
        return map;
    }

}
