package com.sohu.third.zxhuixuan.supply.request.service;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 甄新汇选供应链售后申请请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanServiceSubmitRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 售后图片，数组格式
     */
    private List<String> images;

    /**
     * 售后类型，售后类型 1仅退款 2退货退款 3换货 4补发 5部分退款（待发货订单请申请1仅退款）
     */
    private Integer serviceType;

    /**
     * 订单号
     */
    private String channelOrderNo;

    /**
     * 申请原因
     */
    private String remark;

    /**
     * 售后skuid
     */
    private Long skuId;

    /**
     * 售后数量
     */
    private Integer skuNum;

    /**
     * 退款金额，当售后类型为部分退款时必填
     */
    private BigDecimal amountMoney;

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (CollectionUtil.isNotEmpty(this.images)) {
            map.put("images", this.images);
        }
        if (Objects.nonNull(this.serviceType)) {
            map.put("service_type", this.serviceType);
        }
        if (Objects.nonNull(this.channelOrderNo)) {
            map.put("order_sn", this.channelOrderNo);
        }
        if (StringUtils.isNotEmpty(this.remark)) {
            map.put("remark", this.remark);
        }
        if (Objects.nonNull(this.skuId)) {
            map.put("sku_id", this.skuId);
        }
        if (Objects.nonNull(this.skuNum)) {
            map.put("sku_num", this.skuNum);
        }
        if (Objects.nonNull(this.amountMoney)) {
            map.put("amount_money", this.amountMoney);
        }
        return map;
    }

}
