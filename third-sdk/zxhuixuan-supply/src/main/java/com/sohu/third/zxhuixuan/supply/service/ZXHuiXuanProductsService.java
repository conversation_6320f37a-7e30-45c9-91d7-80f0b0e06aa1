package com.sohu.third.zxhuixuan.supply.service;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.sohu.third.zxhuixuan.supply.constant.ZXHuiXuanConstant;
import com.sohu.third.zxhuixuan.supply.model.ZXHuiXuanConfig;
import com.sohu.third.zxhuixuan.supply.model.base.Meta;
import com.sohu.third.zxhuixuan.supply.model.product.*;
import com.sohu.third.zxhuixuan.supply.request.product.*;
import com.sohu.third.zxhuixuan.supply.response.*;
import com.sohu.third.zxhuixuan.supply.response.product.*;
import com.sohu.third.zxhuixuan.supply.util.ApiClient;
import com.sohu.third.zxhuixuan.supply.util.SignUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * 甄新汇选供应链-商品
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 15:12
 */
@Slf4j
public class ZXHuiXuanProductsService {

    /**
     * 获取品牌列表
     */
    public static ZXHuiXuanProductBrandResponse getBrands(ZXHuiXuanProductBrandRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.ProductUrl.GET_BRANDS;
        String result = ApiClient.request(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanProductBrandResponse brandResponse = new ZXHuiXuanProductBrandResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            JSONObject dataObj = JSONUtil.parseObj(result).getJSONObject("data");
            // 获取 meta 对象
            Meta meta = JSONUtil.toBean(dataObj.getJSONObject("meta"), Meta.class);
            // 解析 brand list 字符串为数组
            String dataListStr = dataObj.getStr("data");
            List<Brand> brandList = JSONUtil.toList(dataListStr, Brand.class);
            // 构建响应对象
            brandResponse.setMeta(meta);
            brandResponse.setBrandList(brandList);
            return brandResponse;
        }
        return brandResponse;
    }

    /**
     * 获取分类列表
     */
    public static ZXHuiXuanProductCategoryResponse getCategory(ZXHuiXuanProductCategoryRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.ProductUrl.GET_CATEGORY;
        String result = ApiClient.request(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanProductCategoryResponse brandResponse = new ZXHuiXuanProductCategoryResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            String dataListStr = JSONUtil.parseObj(result).getStr("data");
            List<Category> categoryList = JSONUtil.toList(dataListStr, Category.class);
            brandResponse.setCategoryList(categoryList);
            return brandResponse;
        }
        return brandResponse;
    }

    /**
     * 查询商品库SPUID列表
     */
    public static ZXHuiXuanProductSpuListResponse getSpuIdList(ZXHuiXuanProductSpuListRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.ProductUrl.GET_SPUID_LIST;
        String result = ApiClient.request(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanProductSpuListResponse spuListResponse = new ZXHuiXuanProductSpuListResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            JSONObject dataObj = JSONUtil.parseObj(result).getJSONObject("data");
            spuListResponse.setSpuIdList(dataObj.getBeanList("spuIdList", Long.class));
            spuListResponse.setTotal(dataObj.getLong("total"));
            return spuListResponse;
        }
        return spuListResponse;
    }

    /**
     * 查询SPUID商品详情
     */
    public static ZXHuiXuanProductSpuDetailsResponse getSpuIdDetails(ZXHuiXuanProductSpuDetailsRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.ProductUrl.GET_SPUID_DETAILS;
        String result = ApiClient.request(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanProductSpuDetailsResponse spuListResponse = new ZXHuiXuanProductSpuDetailsResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            String dataObj = JSONUtil.parseObj(result).getStr("data");
            spuListResponse.setDetails(JSONUtil.toBean(dataObj, SpuDetails.class));
            return spuListResponse;
        }
        return spuListResponse;
    }

    /**
     * 查询SKU规格信息
     */
    public static ZXHuiXuanProductSkuInfoResponse getSkuInfo(ZXHuiXuanProductSkuInfoRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.ProductUrl.GET_SKU_INFO;
        String result = ApiClient.request(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanProductSkuInfoResponse spuListResponse = new ZXHuiXuanProductSkuInfoResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            String dataObj = JSONUtil.parseObj(result).getStr("data");
            spuListResponse.setInfo(JSONUtil.toBean(dataObj, Sku.class));
            return spuListResponse;
        }
        return spuListResponse;
    }

    /**
     * 查询商品价格
     */
    public static ZXHuiXuanProductSkuSalePriceResponse getSkuSalePrice(ZXHuiXuanProductSkuSalePriceRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.ProductUrl.GET_SKU_SALE_PRICE;
        String result = ApiClient.request(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanProductSkuSalePriceResponse spuListResponse = new ZXHuiXuanProductSkuSalePriceResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            String dataObj = JSONUtil.parseObj(result).getStr("data");
            spuListResponse.setPrice(JSONUtil.toBean(dataObj, SkuSalePrice.class));
            return spuListResponse;
        }
        return spuListResponse;
    }

    /**
     * 查询商品库存
     */
    public static ZXHuiXuanProductSkuStockResponse getSkuStock(ZXHuiXuanProductSkuStockRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.ProductUrl.GET_SKU_STOCK;
        String result = ApiClient.requestQueryAndBodyEmpty(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanProductSkuStockResponse spuListResponse = new ZXHuiXuanProductSkuStockResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            return spuListResponse;
        }
        return spuListResponse;
    }

    /**
     * 查询商品运费模板(优选版块)
     */
    public static ZXHuiXuanProductTemplateFreightResponse getTemplateFreight(ZXHuiXuanProductTemplateFreightRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.ProductUrl.GET_TEMPLATE_FREIGHT;
        String result = ApiClient.request(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanProductTemplateFreightResponse spuListResponse = new ZXHuiXuanProductTemplateFreightResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            String dataObj = JSONUtil.parseObj(result).getStr("data");
            if (!Objects.equals(dataObj, "[]")) {
                spuListResponse.setTemplatesFreight(JSONUtil.toBean(dataObj, TemplatesFreight.class));
            }
            return spuListResponse;
        }
        return spuListResponse;
    }

    /**
     * 获取板块标识
     */
    public static ZXHuiXuanProductTypesResponse getTypes(ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.ProductUrl.GET_TYPES;
        String result = ApiClient.request(url, new HashMap<>(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanProductTypesResponse spuListResponse = new ZXHuiXuanProductTypesResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            String dataObj = JSONUtil.parseObj(result).getStr("data");
            spuListResponse.setTypesList(JSONUtil.toList(dataObj, Types.class));
            return spuListResponse;
        }
        return spuListResponse;
    }
}
