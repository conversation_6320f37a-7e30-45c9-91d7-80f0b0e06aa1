package com.sohu.third.zxhuixuan.supply.response.order;

import com.sohu.third.zxhuixuan.supply.model.order.OrderInfo;
import com.sohu.third.zxhuixuan.supply.model.virtual.VirtualOrderInfo;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * 甄新汇选供应链订单详情返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanOrderInfoResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 订单详情
     */
    private OrderInfo orderInfo;


    public ZXHuiXuanOrderInfoResponse() {
    }

    public ZXHuiXuanOrderInfoResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

}
