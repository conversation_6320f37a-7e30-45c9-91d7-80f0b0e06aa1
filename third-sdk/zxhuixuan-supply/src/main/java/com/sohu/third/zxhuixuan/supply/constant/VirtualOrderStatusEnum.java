package com.sohu.third.zxhuixuan.supply.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 8/7/2025 上午 11:47
 */
@Getter
public enum VirtualOrderStatusEnum {

    PUSH_SUCCESS(2, "推单成功"),
    ORDER_FAILED(5, "下单失败"),
    COMPLETED(6, "完结");

    private final Integer code;

    private final String description;

    VirtualOrderStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据Code获取枚举
     *
     * @param code
     * @return
     */
    public static VirtualOrderStatusEnum getCode(Integer code) {
        for (VirtualOrderStatusEnum status : VirtualOrderStatusEnum.values()) {
            if (Objects.equals(status.code, code)) {
                return status;
            }
        }
        return null;
    }
}
