package com.sohu.third.zxhuixuan.supply.request.virtual;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 甄新汇选供应链获取虚拟商品列表请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanVirtualListRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 虚拟商品id
     */
    private Integer virtualId;

    /**
     * 当前页
     */
    private String page;

    /**
     * 每页条数
     */
    private String limit;

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (Objects.nonNull(this.virtualId)) {
            map.put("virtual_id", this.virtualId);
        }
        if (StringUtils.isNotEmpty(this.page)) {
            map.put("page", this.page);
        }
        if (StringUtils.isNotEmpty(this.limit)) {
            map.put("limit", this.limit);
        }
        return map;
    }

}
