package com.sohu.third.zxhuixuan.supply.model.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 品牌
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 16:13
 */
@Data
public class SpuDetails implements Serializable {

    /**
     * SPUID
     */
    @JsonProperty(value = "spu_id")
    private Long spuId;

    /**
     * 供应商ID
     */
    @JsonProperty(value = "supplier_id")
    private Long supplierId;

    /**
     * 渠道id
     */
    @JsonProperty(value = "channel_id")
    private Long channelId;

    /**
     * 品牌ID
     */
    @JsonProperty(value = "brand_id")
    private Long brandId;

    /**
     * 分类ID
     */
    @JsonProperty(value = "class_id")
    private Long classId;

    /**
     * 商品主图
     */
    private String image;

    /**
     * 商品图
     */
    @JsonProperty(value = "slider_image")
    private List<String> sliderImage;

    /**
     * 商品名称
     */
    @JsonProperty(value = "store_name")
    private String storeName;

    /**
     * 商品简介
     */
    @JsonProperty(value = "store_info")
    private String storeInfo;

    /**
     * 三方链接
     */
    @JsonProperty(value = "thirdparty_link")
    private Object thirdpartyLink;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 商品零售价
     */
    @JsonProperty(value = "market_price")
    private BigDecimal marketPrice;

    /**
     * 商品最低控价
     */
    @JsonProperty(value = "group_price")
    private BigDecimal groupPrice;

    /**
     * 商品利润
     */
    private BigDecimal rate;

    /**
     * 上下架（1=下架 2=上架）
     */
    private Integer status;

    /**
     * 板块id
     */
    private String type;

    /**
     * 渠道名称
     */
    @JsonProperty(value = "channel_name")
    private String channelName;

    /**
     * 所属板块名称
     */
    @JsonProperty(value = "type_name")
    private String typeName;

    /**
     * 是否跨境完税商品（需要填写身份证信息）1 是 0 否
     */
    @JsonProperty(value = "is_overseas")
    private Integer isOverseas;

    /**
     * 来源
     */
    @JsonProperty(value = "source_supply")
    private String sourceSupply;

    /**
     * 分类信息
     */
    private Category category;

    /**
     * 品牌信息
     */
    private Brand brand;

    /**
     * sku信息
     */
    private List<Sku> sku;

    /**
     * 属性组合josn
     */
    @JsonProperty(value = "attr_result")
    private AttrResult attrResult;

    /**
     * 商品详情
     */
    private String description;

    /**
     * 规格 0单 1多
     */
    @JsonProperty(value = "spec_type")
    private Integer specType;

    private String spu;

    /**
     * 商品编码
     */
    private String code;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 单位
     */
    @JsonProperty(value = "unit_name")
    private String unitName;

    /**
     * 售后信息
     */
    private String info;

}
