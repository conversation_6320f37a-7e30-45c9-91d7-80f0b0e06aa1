package com.sohu.third.zxhuixuan.supply.request.product;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 甄新汇选供应链查询商品库SPUID列表请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanProductSpuListRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 当前页
     */
    private Long page;

    /**
     * 每页条数  最高100 条
     */
    private Long perPage;

    /**
     * 分类id（可多个 【,】 号分割）
     */
    private String classId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 规格（0=单规格， 多规格=1）
     */
    private Integer specType;

    /**
     * 商品名称（模糊匹配）
     */
    private String storeName;

    /**
     * 更新时间排序（desc=降序,asc=升序）
     */
    private String timeSort;

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (Objects.nonNull(this.page)) {
            map.put("page", this.page);
        }
        if (Objects.nonNull(this.perPage)) {
            map.put("perPage", this.perPage);
        }
        if (StringUtils.isNotEmpty(this.classId)) {
            map.put("class_id", this.classId);
        }
        if (StringUtils.isNotEmpty(this.storeName)) {
            map.put("store_name", this.storeName);
        }
        if (Objects.nonNull(this.specType)) {
            map.put("spec_type", this.specType);
        }
        if (StringUtils.isNotEmpty(this.brandName)) {
            map.put("brand_name", this.brandName);
        }
        if (StringUtils.isNotEmpty(this.timeSort)) {
            map.put("time_sort", this.timeSort);
        }
        return map;
    }

}
