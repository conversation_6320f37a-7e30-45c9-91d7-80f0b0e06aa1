package com.sohu.third.zxhuixuan.supply.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * 商品上下架状态枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 8/7/2025 上午 11:53
 */
@Getter
public enum ShelfStatusEnum {

    OFF_SHELF(1, "下架"),
    ON_SHELF(2, "上架");

    private final Integer code;

    private final String description;

    ShelfStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据Code获取枚举
     *
     * @param code
     * @return
     */
    public static ShelfStatusEnum getCode(Integer code) {
        for (ShelfStatusEnum status : ShelfStatusEnum.values()) {
            if (Objects.equals(status.code, code)) {
                return status;
            }
        }
        return null;
    }
}
