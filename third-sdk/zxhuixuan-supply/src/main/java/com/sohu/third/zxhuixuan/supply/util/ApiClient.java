package com.sohu.third.zxhuixuan.supply.util;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.third.zxhuixuan.supply.model.ZXHuiXuanConfig;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 请求封装
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/7 10:22
 */
public class ApiClient {

    /**
     * 请求
     *
     * @param url
     * @param data
     * @param method
     * @param config
     * @return
     */
    public static String request(String url, Map<String, Object> data, String method, ZXHuiXuanConfig config) {
        return request(url, data, method, Boolean.FALSE, Boolean.FALSE, config);
    }

    /**
     * 请求
     * (请求参数需要拼接在URL, 和Body参数都存在)
     *
     * @param url
     * @param data
     * @param method
     * @param config
     * @return
     */
    public static String requestQuery(String url, Map<String, Object> data, String method, ZXHuiXuanConfig config) {
        return request(url, data, method, Boolean.TRUE, Boolean.FALSE, config);
    }

    /**
     * 请求
     * (请求参数需要拼接在URL, Body参数为空)
     *
     * @param url
     * @param data
     * @param method
     * @param config
     * @return
     */
    public static String requestQueryAndBodyEmpty(String url, Map<String, Object> data, String method,
                                                  ZXHuiXuanConfig config) {
        return request(url, data, method, Boolean.TRUE, Boolean.TRUE, config);
    }

    /**
     * 请求
     *
     * @param url
     * @param data
     * @param method
     * @param isQueryParams
     * @param isParamEmpty
     * @param config
     * @return
     */
    private static String request(String url, Map<String, Object> data, String method, Boolean isQueryParams,
                                  Boolean isParamEmpty, ZXHuiXuanConfig config) {
        Map<String, Object> header = new HashMap<>();
        header.put("appId", config.getAppId());
        header.put("timestamp", SignUtils.getTime());
        header.put("onceString", SignUtils.getNonce());
        header.put("versions", "v2");
        // 参与签名的所有参数
        Map<String, Object> allParams = new TreeMap<>();
        allParams.putAll(data);
        allParams.putAll(header);
        // 生成签名
        String sign = SignUtils.buildSign(allParams, config.getSecret());
        header.put("sign", sign);
        // 发送参数
        Map<String, Object> sendParams = new HashMap<>(data);
        sendParams.putAll(header);
        if (isQueryParams) {
            url += "?" + SignUtils.buildQuery(sendParams);
        }
        try {
            if ("post".equalsIgnoreCase(method)) {
                if (isParamEmpty) {
                    return HttpUtil.post(url, new HashMap<>());
                } else {
                    return HttpUtil.post(url, JSONUtil.toJsonStr(sendParams));
                }
            } else {
                if (isParamEmpty) {
                    return HttpUtil.get(url, new HashMap<>());
                } else {
                    return HttpUtil.get(url, sendParams);
                }
            }
        } catch (Exception e) {
            System.err.println("请求异常: " + e.getMessage());
            return null;
        }
    }
}
