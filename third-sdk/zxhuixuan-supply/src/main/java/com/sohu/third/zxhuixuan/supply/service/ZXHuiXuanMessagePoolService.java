package com.sohu.third.zxhuixuan.supply.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.sohu.third.zxhuixuan.supply.constant.ZXHuiXuanConstant;
import com.sohu.third.zxhuixuan.supply.model.ZXHuiXuanConfig;
import com.sohu.third.zxhuixuan.supply.model.messagepool.Message;
import com.sohu.third.zxhuixuan.supply.request.messagepool.ZXHuiXuanMessagePoolRemoveBatchRequest;
import com.sohu.third.zxhuixuan.supply.request.messagepool.ZXHuiXuanMessagePoolRemoveRequest;
import com.sohu.third.zxhuixuan.supply.request.messagepool.ZXHuiXuanMessagePoolRequest;
import com.sohu.third.zxhuixuan.supply.response.*;
import com.sohu.third.zxhuixuan.supply.response.messagepool.ZXHuiXuanMessagePoolResponse;
import com.sohu.third.zxhuixuan.supply.util.ApiClient;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 甄新汇选供应链-消息池
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 15:12
 */
@Slf4j
public class ZXHuiXuanMessagePoolService {

    /**
     * 获取查询消息池数据
     */
    public static ZXHuiXuanMessagePoolResponse getMessagePool(ZXHuiXuanMessagePoolRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.MessagePoolUrl.GET_MESSAGE_POOL;
        String result = ApiClient.request(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanMessagePoolResponse brandResponse = new ZXHuiXuanMessagePoolResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            JSONObject dataObj = JSONUtil.parseObj(result);
            String dataListStr = dataObj.getStr("data");
            List<Message> messageList = JSONUtil.toList(dataListStr, Message.class);
            // 构建响应对象
            brandResponse.setMessageList(messageList);
            return brandResponse;
        }
        return brandResponse;
    }

    /**
     * 删除消息
     */
    public static ZXHuiXuanBaseResponse removeMessagePool(ZXHuiXuanMessagePoolRemoveRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.MessagePoolUrl.REMOVE_MESSAGE_POOL;
        String result = ApiClient.requestQueryAndBodyEmpty(url, request.toSignParamMap(), "post", config);
        return JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
    }

    /**
     * 删除多个消息
     */
    public static ZXHuiXuanBaseResponse removeBatchMessagePool(ZXHuiXuanMessagePoolRemoveBatchRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.MessagePoolUrl.BATCH_REMOVE_MESSAGE_POOL;
        String result = ApiClient.requestQuery(url, request.toSignParamMap(), "post", config);
        return JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
    }
}
