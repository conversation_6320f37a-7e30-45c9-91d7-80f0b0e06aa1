package com.sohu.third.zxhuixuan.supply.model.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 分类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 16:13
 */
@Data
public class Category implements Serializable {

    /**
     * 分类ID
     */
    private Long id;
    /**
     * 分类名称
     */
    private String name;
    /**
     * 父级ID
     */
    @JsonProperty(value = "parent_id")
    private long parentId;
}
