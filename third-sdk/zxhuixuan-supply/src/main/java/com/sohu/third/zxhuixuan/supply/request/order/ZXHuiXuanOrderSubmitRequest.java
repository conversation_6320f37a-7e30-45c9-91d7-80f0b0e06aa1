package com.sohu.third.zxhuixuan.supply.request.order;

import cn.hutool.core.annotation.Alias;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 甄新汇选供应链商品下单请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanOrderSubmitRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 收件人姓名
     */
    @JsonProperty(value = "user_name")
    @Alias(value = "user_name")
    private String username;

    /**
     * 收件人电话
     */
    @JsonProperty(value = "user_mobile")
    @Alias(value = "user_mobile")
    private String userMobile;

    /**
     * 收货地区行政编码，例如:省编码,市编码,区编码 以英文‘,’逗号隔开
     */
    private String shipAreaCode;

    /**
     * 收件人详细地址(注：街道地址需要拼接到详细地址里)
     */
    @JsonProperty(value = "user_address")
    @Alias(value = "user_address")
    private String userAddress;

    /**
     * 商品信息（数组）sku和购买数量
     */
    @JsonProperty(value = "sku_list")
    @Alias(value = "sku_list")
    private List<Sku> skuList;

    /**
     * 第三方订单号
     */
    @JsonProperty(value = "third_sn")
    @Alias(value = "third_sn")
    private String orderNo;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 真实姓名
     * <p>
     * 跨境商品必填
     */
    @JsonProperty(value = "user_realname")
    @Alias(value = "user_realname")
    private String realName;

    /**
     * 身份证号码
     * 跨境商品必填
     */
    @JsonProperty(value = "user_idcard")
    @Alias(value = "user_idcard")
    private String idCard;

    /**
     * 提单规格
     */
    @Data
    public static class Sku implements Serializable {

        /**
         * 规格ID
         */
        @JsonProperty(value = "sku_id")
        @Alias(value = "sku_id")
        private Long skuId;

        /**
         * 数量
         */
        @JsonProperty(value = "sku_num")
        @Alias(value = "sku_num")
        private Integer skuNum;

        /**
         * 单价
         */
        private BigDecimal price;
    }

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (Objects.nonNull(this.username)) {
            map.put("user_name", this.username);
        }
        if (Objects.nonNull(this.userMobile)) {
            map.put("user_mobile", this.userMobile);
        }
        if (Objects.nonNull(this.shipAreaCode)) {
            map.put("shipAreaCode", this.shipAreaCode);
        }
        if (Objects.nonNull(this.userAddress)) {
            map.put("user_address", this.userAddress);
        }
        if (CollectionUtil.isNotEmpty(this.skuList)) {
            map.put("sku_list", this.skuList.stream().map(BeanUtil::beanToMap).collect(Collectors.toList()));
        }
        if (StringUtils.isNotEmpty(this.orderNo)) {
            map.put("third_sn", this.orderNo);
        }
        if (StringUtils.isNotEmpty(this.remark)) {
            map.put("remark", this.remark);
        }
        if (Objects.nonNull(this.realName)) {
            map.put("user_realname", this.realName);
        }
        if (Objects.nonNull(this.idCard)) {
            map.put("user_idcard", this.idCard);
        }
        return map;
    }
}
