package com.sohu.third.zxhuixuan.supply.response.service;

import com.sohu.third.zxhuixuan.supply.model.order.AfterSaleOrderInfo;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * 甄新汇选供应链售后订单详情返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanServiceInfoResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 售后订单详情
     */
    private AfterSaleOrderInfo orderInfo;


    public ZXHuiXuanServiceInfoResponse() {
    }

    public ZXHuiXuanServiceInfoResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

}
