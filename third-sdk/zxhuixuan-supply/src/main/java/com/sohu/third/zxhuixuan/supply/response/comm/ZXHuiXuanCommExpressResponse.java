package com.sohu.third.zxhuixuan.supply.response.comm;

import com.sohu.third.zxhuixuan.supply.model.comm.Express;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 甄新汇选供应链获取物流公司信息返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanCommExpressResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 物流公司信息
     */
    private List<Express> expressList;

    public ZXHuiXuanCommExpressResponse() {
    }

    public ZXHuiXuanCommExpressResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

}
