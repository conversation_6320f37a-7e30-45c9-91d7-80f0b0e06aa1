package com.sohu.third.zxhuixuan.supply.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 8/7/2025 上午 11:47
 */
@Getter
public enum OrderStatusEnum {

    PENDING_PAYMENT(1, "待付款"),
    PENDING_SHIPMENT(2, "待发货"),
    PENDING_RECEIPT(3, "待收货"),
    CONFIRMED_RECEIPT(4, "确认收货"),
    CANCELED(5, "已取消"),
    COMPLETED(6, "已完结"),
    REJECTED(7, "拒收");

    private final Integer code;

    private final String description;

    OrderStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据Code获取枚举
     *
     * @param code
     * @return
     */
    public static OrderStatusEnum getCode(Integer code) {
        for (OrderStatusEnum status : OrderStatusEnum.values()) {
            if (Objects.equals(status.code, code)) {
                return status;
            }
        }
        return null;
    }
}
