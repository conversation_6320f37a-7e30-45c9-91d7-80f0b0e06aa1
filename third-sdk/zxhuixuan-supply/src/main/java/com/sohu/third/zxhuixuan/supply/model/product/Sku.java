package com.sohu.third.zxhuixuan.supply.model.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * sku信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/3 14:32
 */
@Data
public class Sku implements Serializable {

    /**
     * SKUID
     */
    @JsonProperty(value = "sku_id")
    private Long skuId;

    /**
     * SPUID
     */
    @JsonProperty(value = "spu_id")
    private Long spuId;

    private String sku;

    /**
     * 属性定位
     */
    private List<SkuAttr> attr;

    /**
     * 库存
     */
    private Long stock;

    /**
     * 销量
     */
    private Long sales;

    /**
     * 成本价格
     */
    private BigDecimal price;

    /**
     * 图片
     */
    private String image;

    /**
     * 条形码
     */
    @JsonProperty(value = "bar_code")
    private String barCode;

    /**
     * 市场价
     */
    @JsonProperty(value = "market_price")
    private BigDecimal marketPrice;

    /**
     * 最低控价
     */
    @JsonProperty(value = "group_price")
    private BigDecimal groupPrice;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 体积
     */
    private BigDecimal volume;

    /**
     * 上下架 0下架 1上架
     */
    @JsonProperty(value = "is_show")
    private Integer isShow;

    /**
     * 起购数量
     */
    @JsonProperty(value = "buy_start_qty")
    private Integer buyStartQty;

    /**
     * 是否跨境完税商品（需要填写身份证信息）1 是 0 否
     */
    @JsonProperty(value = "is_overseas")
    private Integer isOverseas;

    /**
     * 服务费
     */
    @JsonProperty(value = "service_price")
    private BigDecimal servicePrice;

    /**
     * sku详情
     */
    private SkuDesc skuDesc;

    /**
     * 商品属性索引值
     */
    private String suk;

}
