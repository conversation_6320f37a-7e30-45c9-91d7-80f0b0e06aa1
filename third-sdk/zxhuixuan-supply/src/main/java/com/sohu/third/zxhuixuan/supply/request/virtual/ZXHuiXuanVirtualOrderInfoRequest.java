package com.sohu.third.zxhuixuan.supply.request.virtual;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 甄新汇选供应链获取虚拟商品订单详情请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanVirtualOrderInfoRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 订单号
     */
    private String channelOrderNo;

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (StringUtils.isNotEmpty(this.channelOrderNo)) {
            map.put("order_sn", this.channelOrderNo);
        }
        return map;
    }

}
