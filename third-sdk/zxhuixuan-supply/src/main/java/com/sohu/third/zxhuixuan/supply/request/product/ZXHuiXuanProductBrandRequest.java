package com.sohu.third.zxhuixuan.supply.request.product;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 甄新汇选供应链获取品牌列表请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanProductBrandRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 当前页
     */
    private String page;

    /**
     * 每页条数
     */
    private String perPage;

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (StringUtils.isNotEmpty(this.brandId)) {
            map.put("brand_id", this.brandId);
        }
        if (StringUtils.isNotEmpty(this.page)) {
            map.put("page", this.page);
        }
        if (StringUtils.isNotEmpty(this.perPage)) {
            map.put("perPage", this.perPage);
        }
        return map;
    }

}
