package com.sohu.third.zxhuixuan.supply.model.order;

import cn.hutool.core.annotation.Alias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 售后订单对象
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/5 9:55
 */
@Data
public class AfterSaleOrderInfo implements Serializable {

    /**
     * 售后id
     */
    @JsonProperty("id")
    @Alias("id")
    private Long id;

    /**
     * 售后单号
     */
    @JsonProperty("service_sn")
    @Alias("service_sn")
    private String serviceNo;

    /**
     * 订单号
     */
    @JsonProperty("order_sn")
    @Alias("order_sn")
    private String orderNo;

    /**
     * 订单id
     */
    @JsonProperty("order_id")
    @Alias("order_id")
    private Long orderId;

    /**
     * SKU id
     */
    @JsonProperty("sku_id")
    @Alias("sku_id")
    private Integer skuId;

    /**
     * 售后数量
     */
    @JsonProperty("sku_num")
    @Alias("sku_num")
    private Integer skuNum;

    /**
     * 售后类型：1仅退款 2退货退款 3换货 4补发 5部分退款
     */
    @JsonProperty("service_type")
    @Alias("service_type")
    private Integer serviceType;

    /**
     * 服务费
     */
    @JsonProperty("service_price")
    @Alias("service_price")
    private BigDecimal servicePrice;

    /**
     * 运费
     */
    private BigDecimal postage;

    /**
     * 是否退服务费：1退还 2不退
     */
    @JsonProperty("service_price_status")
    @Alias("service_price_status")
    private Integer servicePriceStatus;

    /**
     * 是否退运费：1退还 2不退
     */
    @JsonProperty("is_refund_postage")
    @Alias("is_refund_postage")
    private Integer isRefundPostage;

    /**
     * 商品价格
     */
    @JsonProperty("sku_price")
    @Alias("sku_price")
    private BigDecimal skuPrice;

    /**
     * 实际退款金额
     */
    @JsonProperty("merchant_refund_money")
    @Alias("merchant_refund_money")
    private BigDecimal merchantRefundMoney;

    /**
     * 申请原因
     */
    private String remark;

    /**
     * 申请图片
     */
    private String images;

    /**
     * 售后状态：
     * 1：审核中，
     * 2：审核通过，
     * 3：售后完结，
     * 4：审核驳回，
     * 5：已取消，
     * 10：待卖家处理，
     * 20：已处理，
     * 50：卖家填写物流单号，
     * 60：买家填写物流单号，
     * 70：待卖家确认
     */
    private Integer status;

    /**
     * 厂家售后快递单号（换补）
     */
    @JsonProperty("after_sale_express_no")
    @Alias("after_sale_express_no")
    private String afterSaleExpressNo;

    /**
     * 商家售后快递单号（退换）
     */
    @JsonProperty("m_after_sale_express_no")
    @Alias("m_after_sale_express_no")
    private String mAfterSaleExpressNo;

    /**
     * 商家退货运费
     */
    @JsonProperty("freight_money")
    @Alias("freight_money")
    private BigDecimal freightMoney;

    /**
     * 拒绝退款原因
     */
    @JsonProperty("refuse_remark")
    @Alias("refuse_remark")
    private String refuseRemark;

    /**
     * 厂家售后快递公司（换补）
     */
    @JsonProperty("after_sale_express")
    @Alias("after_sale_express")
    private String afterSaleExpress;

    /**
     * 商家售后快递公司（退换）
     */
    @JsonProperty("m_after_sale_express")
    @Alias("m_after_sale_express")
    private String mAfterSaleExpress;

    /**
     * 退货地址，若为 null 则需联系客服
     */
    @JsonProperty("return_address")
    @Alias("return_address")
    private String returnAddress;

    /**
     * 售后处理记录
     */
    private List<AfterSaleEvent> events;

}
