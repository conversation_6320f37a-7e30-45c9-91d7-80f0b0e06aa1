package com.sohu.third.zxhuixuan.supply.model.comm;

import cn.hutool.core.annotation.Alias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 物流公司信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/5 15:34
 */
@Data
public class Express implements Serializable {

    /**
     * 编码
     */
    private String code;

    private Long id;

    /**
     * 物流名称
     */
    private String name;

    /**
     * 短链
     */
    @JsonProperty(value = "short_name")
    @Alias(value = "short_name")
    private String shortName;
}
