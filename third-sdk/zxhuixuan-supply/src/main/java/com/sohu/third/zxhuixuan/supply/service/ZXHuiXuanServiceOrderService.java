package com.sohu.third.zxhuixuan.supply.service;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.third.zxhuixuan.supply.constant.ZXHuiXuanConstant;
import com.sohu.third.zxhuixuan.supply.model.ZXHuiXuanConfig;
import com.sohu.third.zxhuixuan.supply.model.order.AfterSaleOrderInfo;
import com.sohu.third.zxhuixuan.supply.request.service.ZXHuiXuanServiceCancelRequest;
import com.sohu.third.zxhuixuan.supply.request.service.ZXHuiXuanServiceExpressSubmitRequest;
import com.sohu.third.zxhuixuan.supply.request.service.ZXHuiXuanServiceInfoRequest;
import com.sohu.third.zxhuixuan.supply.request.service.ZXHuiXuanServiceSubmitRequest;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import com.sohu.third.zxhuixuan.supply.response.service.ZXHuiXuanServiceCancelResponse;
import com.sohu.third.zxhuixuan.supply.response.service.ZXHuiXuanServiceExpressSubmitResponse;
import com.sohu.third.zxhuixuan.supply.response.service.ZXHuiXuanServiceInfoResponse;
import com.sohu.third.zxhuixuan.supply.response.service.ZXHuiXuanServiceSubmitResponse;
import com.sohu.third.zxhuixuan.supply.util.ApiClient;
import com.sohu.third.zxhuixuan.supply.util.SignUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;

/**
 * 甄新汇选供应链-售后订单
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 15:12
 */
@Slf4j
public class ZXHuiXuanServiceOrderService {

    /**
     * 售后申请
     */
    public static ZXHuiXuanServiceSubmitResponse serviceSubmit(ZXHuiXuanServiceSubmitRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.OrderUrl.SERVICE_SUBMIT;
        String result = ApiClient.request(url, request.toSignParamMap(), "post", config);
        return JSONUtil.toBean(result, ZXHuiXuanServiceSubmitResponse.class);
    }

    /**
     * 售后订单详情
     */
    public static ZXHuiXuanServiceInfoResponse getServiceDetails(ZXHuiXuanServiceInfoRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.OrderUrl.GET_SERVICE_DETAILS;
        String result = ApiClient.request(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanServiceInfoResponse orderInfoResponse = new ZXHuiXuanServiceInfoResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            String dataStr = JSONUtil.parseObj(result).getStr("data");
            orderInfoResponse.setOrderInfo(JSONUtil.toBean(dataStr, AfterSaleOrderInfo.class));
            return orderInfoResponse;
        }
        return orderInfoResponse;
    }

    /**
     * 取消售后申请
     */
    public static ZXHuiXuanServiceCancelResponse serviceCancel(ZXHuiXuanServiceCancelRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.OrderUrl.SERVICE_CANCEL;
        String result = ApiClient.requestQueryAndBodyEmpty(url, request.toSignParamMap(), "post", config);
        return JSONUtil.toBean(result, ZXHuiXuanServiceCancelResponse.class);
    }

    /**
     * 退换货填写运单号
     */
    public static ZXHuiXuanServiceExpressSubmitResponse serviceExpressSubmit(ZXHuiXuanServiceExpressSubmitRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.OrderUrl.SERVICE_EXPRESS_SUBMIT;
        String result = ApiClient.requestQueryAndBodyEmpty(url, request.toSignParamMap(), "post", config);
        return JSONUtil.toBean(result, ZXHuiXuanServiceExpressSubmitResponse.class);
    }

}
