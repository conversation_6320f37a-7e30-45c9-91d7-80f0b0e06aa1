package com.sohu.third.zxhuixuan.supply.response.product;

import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 甄新汇选供应链查询商品库SPUID列表返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanProductSpuListResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * SPUID列表
     */
    private List<Long> spuIdList;

    /**
     * 选品库SPU总数
     */
    private Long total;

    public ZXHuiXuanProductSpuListResponse() {
    }

    public ZXHuiXuanProductSpuListResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }
}
