package com.sohu.third.zxhuixuan.supply.model.order;

import cn.hutool.core.annotation.Alias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 售后处理记录
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/5 16:38
 */
@Data
public class AfterSaleEvent implements Serializable {

    /**
     * 记录id
     */
    private Long id;

    /**
     * 订单id
     */
    @JsonProperty("order_id")
    @Alias("order_id")
    private Long orderId;

    /**
     * 售后id
     */
    @JsonProperty("service_id")
    @Alias("service_id")
    private Long serviceId;

    /**
     * 操作备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    @Alias("created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @JsonProperty("updated_at")
    @Alias("updated_at")
    private Date updatedAt;
}
