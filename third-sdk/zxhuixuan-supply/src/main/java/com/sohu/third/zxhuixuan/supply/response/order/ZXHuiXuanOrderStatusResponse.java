package com.sohu.third.zxhuixuan.supply.response.order;

import cn.hutool.core.annotation.Alias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sohu.third.zxhuixuan.supply.model.order.OrderSubmit;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * 甄新汇选供应链商品下单返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanOrderStatusResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 状态
     */
    private Status statusInfo;

    /**
     * 状态
     */
    @Data
    public static class Status {

        /**
         * 订单状态 1:待付款 2:待发货  3:待收货 4：确认收货 5：已取消  6：已完结 7：拒收
         */
        @JsonProperty(value = "order_status")
        @Alias(value = "order_status")
        private Integer orderStatus;

        /**
         * 状态说明
         */
        @JsonProperty(value = "status_text")
        @Alias(value = "status_text")
        private String statusText;
    }


    public ZXHuiXuanOrderStatusResponse() {
    }

    public ZXHuiXuanOrderStatusResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

}
