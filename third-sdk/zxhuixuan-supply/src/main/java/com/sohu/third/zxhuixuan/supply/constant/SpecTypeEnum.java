package com.sohu.third.zxhuixuan.supply.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * 是否跨境完税商品枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 8/7/2025 上午 11:53
 */
@Getter
public enum SpecTypeEnum {

    SINGLE(0, "单规格"),
    MULTIPLE(1, "多规格");

    private final Integer code;

    private final String description;

    SpecTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据Code获取枚举
     *
     * @param code
     * @return
     */
    public static SpecTypeEnum getCode(Integer code) {
        for (SpecTypeEnum status : SpecTypeEnum.values()) {
            if (Objects.equals(status.code, code)) {
                return status;
            }
        }
        return null;
    }
}
