package com.sohu.third.zxhuixuan.supply.response.virtual;

import com.sohu.third.zxhuixuan.supply.model.virtual.VirtualOrderSubmit;
import com.sohu.third.zxhuixuan.supply.model.virtual.VirtualSpu;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 甄新汇选供应链虚拟商品下单返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanVirtualOrderSubmitResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 虚拟商品列表
     */
    private VirtualOrderSubmit orderSubmit;


    public ZXHuiXuanVirtualOrderSubmitResponse() {
    }

    public ZXHuiXuanVirtualOrderSubmitResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

}
