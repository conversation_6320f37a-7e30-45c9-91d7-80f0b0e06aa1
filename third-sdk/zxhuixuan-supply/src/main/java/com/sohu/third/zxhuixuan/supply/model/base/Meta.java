package com.sohu.third.zxhuixuan.supply.model.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 分页信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 16:09
 */
@Data
public class Meta implements Serializable {

    /**
     * 总条数
     */
    private Long total;

    /**
     * 每页条数
     */
    @JsonProperty(value = "per_page")
    private Long perPage;

    /**
     * 当前页
     */
    @JsonProperty(value = "current_page")
    private Long currentPage;

    /**
     * 总页数
     */
    @JsonProperty(value = "total_pages")
    private Long totalPages;

}
