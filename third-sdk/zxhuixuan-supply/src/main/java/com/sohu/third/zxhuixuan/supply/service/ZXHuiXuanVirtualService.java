package com.sohu.third.zxhuixuan.supply.service;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.sohu.third.zxhuixuan.supply.constant.ZXHuiXuanConstant;
import com.sohu.third.zxhuixuan.supply.model.ZXHuiXuanConfig;
import com.sohu.third.zxhuixuan.supply.model.virtual.VirtualOrderInfo;
import com.sohu.third.zxhuixuan.supply.model.virtual.VirtualOrderSubmit;
import com.sohu.third.zxhuixuan.supply.model.virtual.VirtualSpu;
import com.sohu.third.zxhuixuan.supply.request.virtual.ZXHuiXuanVirtualOrderInfoRequest;
import com.sohu.third.zxhuixuan.supply.request.virtual.ZXHuiXuanVirtualOrderSubmitRequest;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import com.sohu.third.zxhuixuan.supply.request.virtual.ZXHuiXuanVirtualListRequest;
import com.sohu.third.zxhuixuan.supply.response.virtual.ZXHuiXuanVirtualListResponse;
import com.sohu.third.zxhuixuan.supply.response.virtual.ZXHuiXuanVirtualOrderInfoResponse;
import com.sohu.third.zxhuixuan.supply.response.virtual.ZXHuiXuanVirtualOrderSubmitResponse;
import com.sohu.third.zxhuixuan.supply.util.ApiClient;
import com.sohu.third.zxhuixuan.supply.util.SignUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 甄新汇选供应链-虚拟商品
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 15:12
 */
@Slf4j
public class ZXHuiXuanVirtualService {

    /**
     * 虚拟商品列表
     */
    public static ZXHuiXuanVirtualListResponse getList(ZXHuiXuanVirtualListRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.VirtualUrl.GET_LIST;
        String result = ApiClient.request(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanVirtualListResponse brandResponse = new ZXHuiXuanVirtualListResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            JSONObject dataObj = JSONUtil.parseObj(result).getJSONObject("data");
            String dataListStr = dataObj.getStr("spuIdList");
            List<VirtualSpu> spuList = JSONUtil.toList(dataListStr, VirtualSpu.class);
            brandResponse.setSpuList(spuList);
            brandResponse.setTotal(dataObj.getLong("total"));
            return brandResponse;
        }
        return brandResponse;
    }

    /**
     * 虚拟商品下单
     */
    public static ZXHuiXuanVirtualOrderSubmitResponse orderSubmit(ZXHuiXuanVirtualOrderSubmitRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.VirtualUrl.ORDER_SUBMIT;
        String result = ApiClient.request(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanVirtualOrderSubmitResponse brandResponse = new ZXHuiXuanVirtualOrderSubmitResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            String dataStr = JSONUtil.parseObj(result).getStr("data");
            brandResponse.setOrderSubmit(JSONUtil.toBean(dataStr, VirtualOrderSubmit.class));
            return brandResponse;
        }
        return brandResponse;
    }

    /**
     * 查询虚拟订单详情
     */
    public static ZXHuiXuanVirtualOrderInfoResponse getOrderInfo(ZXHuiXuanVirtualOrderInfoRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.VirtualUrl.GET_ORDER_INFO;
        String result = ApiClient.request(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanVirtualOrderInfoResponse orderInfoResponse = new ZXHuiXuanVirtualOrderInfoResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            String dataStr = JSONUtil.parseObj(result).getStr("data");
            orderInfoResponse.setOrderInfo(JSONUtil.toBean(dataStr, VirtualOrderInfo.class));
            return orderInfoResponse;
        }
        return orderInfoResponse;
    }

}
