package com.sohu.third.zxhuixuan.supply.model.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * sku信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/3 14:32
 */
@Data
public class SkuSalePrice implements Serializable {

    /**
     * SKUID
     */
    @JsonProperty(value = "sku_id")
    private Long skuId;

    /**
     * 成本价格
     */
    private BigDecimal price;

    /**
     * 市场价
     */
    @JsonProperty(value = "market_price")
    private BigDecimal marketPrice;

    /**
     * 最低控价
     */
    @JsonProperty(value = "group_price")
    private BigDecimal groupPrice;

    /**
     * 服务费
     */
    @JsonProperty(value = "service_price")
    private BigDecimal servicePrice;

}
