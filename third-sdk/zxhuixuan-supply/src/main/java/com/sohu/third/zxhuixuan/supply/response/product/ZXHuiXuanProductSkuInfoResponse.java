package com.sohu.third.zxhuixuan.supply.response.product;

import com.sohu.third.zxhuixuan.supply.model.product.Sku;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * 甄新汇选供应链查询Sku商品详情返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanProductSkuInfoResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * sku详情
     */
    private Sku info;

    public ZXHuiXuanProductSkuInfoResponse() {
    }

    public ZXHuiXuanProductSkuInfoResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }
}
