package com.sohu.third.zxhuixuan.supply.request.order;

import cn.hutool.core.annotation.Alias;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 甄新汇选供应链多商品查询运费请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanOrderPostageRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 收货地区行政编码，例如:省编码,市编码,区编码 以英文‘,’逗号隔开
     */
    private String shipAreaCode;

    /**
     * 规格
     */
    private List<Sku> skuList;

    /**
     * 规格
     */
    @Data
    public static class Sku implements Serializable {

        /**
         * 规格ID
         */
        @JsonProperty(value = "sku_id")
        @Alias(value = "sku_id")
        private Long skuId;

        /**
         * 数量
         */
        @JsonProperty(value = "sku_num")
        @Alias(value = "sku_num")
        private Integer skuNum;
    }

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (StringUtils.isNotEmpty(this.shipAreaCode)) {
            map.put("shipAreaCode", this.shipAreaCode);
        }
        if (CollectionUtil.isNotEmpty(this.skuList)) {
            map.put("sku_list", this.skuList.stream().map(BeanUtil::beanToMap).collect(Collectors.toList()));
        }
        return map;
    }

}
