package com.sohu.third.zxhuixuan.supply.request.messagepool;

import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 甄新汇选供应链获取查询消息池数据请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanMessagePoolRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 类型说明
     * 0: //返回所有类型消息
     * 1: //订单状态变动 order_sn：订单号 order_status：订单状态 3：已发货 4：确认收货 5：订单取消 6 ：订单完结
     * 2: //售后订单状态变动 service_sn:售后单号 status：2：审核通过 3：售后完结 4：审核驳回 5：取消申请 20：已处理 50：卖家填写物流单号 60：买家填写物流单号 70：待卖家确认
     * 4://选品库商品添加 spu_id：商品ID
     * 5://选品库商品删除 spu_id：商品ID
     * 6://商品信息变动 spu_id：商品ID
     * 7://商品库存变动
     * 8://商品价格变动
     * 9://商品spu上下架变动 spu_id：商品ID status：状态 1:下架 2：上架
     * 10://商品sku上下架变动 sku_id：商品规格ID status：状态 0：下架 1：上架
     */
    private Integer type;

    /**
     * 单次最多返回1000条数据
     */
    private Integer pageLimit;

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (Objects.nonNull(this.type)) {
            map.put("type", this.type);
        }
        if (Objects.nonNull(this.pageLimit)) {
            map.put("pageLimit", this.pageLimit);
        }
        return map;
    }

}
