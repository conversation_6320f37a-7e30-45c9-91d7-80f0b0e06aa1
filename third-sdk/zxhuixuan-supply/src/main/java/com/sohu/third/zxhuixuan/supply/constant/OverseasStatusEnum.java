package com.sohu.third.zxhuixuan.supply.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * 是否跨境完税商品枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 8/7/2025 上午 11:53
 */
@Getter
public enum OverseasStatusEnum {

    NO(0, "否"),
    YES(1, "是");

    private final Integer code;

    private final String description;

    OverseasStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据Code获取枚举
     *
     * @param code
     * @return
     */
    public static OverseasStatusEnum getCode(Integer code) {
        for (OverseasStatusEnum status : OverseasStatusEnum.values()) {
            if (Objects.equals(status.code, code)) {
                return status;
            }
        }
        return null;
    }
}
