package com.sohu.third.zxhuixuan.supply.request.comm;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 甄新汇选供应链地区相关请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanCommAreaRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 地区id
     */
    private String areaId;

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (StringUtils.isNotEmpty(this.areaId)) {
            map.put("area_id", this.areaId);
        }
        return map;
    }

}
