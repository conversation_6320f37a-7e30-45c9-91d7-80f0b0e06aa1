package com.sohu.third.zxhuixuan.supply.request.service;

import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 甄新汇选供应链取消售后申请请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanServiceCancelRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 订单号
     */
    private String channelServiceNo;

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (Objects.nonNull(this.channelServiceNo)) {
            map.put("service_sn", this.channelServiceNo);
        }
        return map;
    }

}
