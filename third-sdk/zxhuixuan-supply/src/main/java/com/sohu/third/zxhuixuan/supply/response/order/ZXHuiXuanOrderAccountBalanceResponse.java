package com.sohu.third.zxhuixuan.supply.response.order;

import cn.hutool.core.annotation.Alias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 甄新汇选供应链校验账户余额返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanOrderAccountBalanceResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 余额
     */
    @JsonProperty(value = "data")
    @Alias(value = "data")
    private BigDecimal balance;

    public ZXHuiXuanOrderAccountBalanceResponse() {
    }

    public ZXHuiXuanOrderAccountBalanceResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

}
