package com.sohu.third.zxhuixuan.supply.response.order;

import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 甄新汇选供应链多商品查询运费返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanOrderPostageResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 运费
     */
    private BigDecimal postage;


    public ZXHuiXuanOrderPostageResponse() {
    }

    public ZXHuiXuanOrderPostageResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

}
