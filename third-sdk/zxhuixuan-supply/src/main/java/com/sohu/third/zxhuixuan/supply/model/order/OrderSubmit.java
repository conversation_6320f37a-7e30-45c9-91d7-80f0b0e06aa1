package com.sohu.third.zxhuixuan.supply.model.order;

import cn.hutool.core.annotation.Alias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品下单
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/5 9:55
 */
@Data
public class OrderSubmit implements Serializable {

    /**
     * 订单号
     */
    @JsonProperty(value = "order_sn")
    @Alias(value = "order_sn")
    private String channelOrderNo;

    /**
     * sku信息
     */
    @JsonProperty(value = "sku_list")
    @Alias(value = "sku_list")
    private List<Sku> skuList;

    /**
     * 运费
     */
    private BigDecimal postage;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 服务费
     */
    @JsonProperty(value = "service_money")
    private BigDecimal servicePrice;

    /**
     * 支付价格
     */
    @JsonProperty(value = "pay_price")
    private String payPrice;


    /**
     * 规格
     */
    @Data
    public static class Sku implements Serializable {

        /**
         * 规格ID
         */
        @JsonProperty(value = "sku_id")
        @Alias(value = "sku_id")
        private Long skuId;

        /**
         * 规格名称
         */
        private String sku;

        /**
         * 数量
         */
        @JsonProperty(value = "sku_num")
        @Alias(value = "sku_num")
        private Long skuNum;

        /**
         * 单价
         */
        @JsonProperty(value = "unit_price")
        @Alias(value = "unit_price")
        private BigDecimal unitPrice;
    }

}
