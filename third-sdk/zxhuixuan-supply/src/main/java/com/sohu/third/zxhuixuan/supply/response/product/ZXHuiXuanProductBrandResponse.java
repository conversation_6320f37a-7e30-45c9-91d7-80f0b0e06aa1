package com.sohu.third.zxhuixuan.supply.response.product;

import com.sohu.third.zxhuixuan.supply.model.base.Meta;
import com.sohu.third.zxhuixuan.supply.model.product.Brand;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 甄新汇选供应链商品品牌返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanProductBrandResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;
    /**
     * 状态值
     */
    private List<Brand> brandList;

    /**
     * 分页信息
     */
    private Meta meta;

    public ZXHuiXuanProductBrandResponse() {
    }

    public ZXHuiXuanProductBrandResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

}
