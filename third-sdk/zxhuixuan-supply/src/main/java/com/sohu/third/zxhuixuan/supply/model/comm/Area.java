package com.sohu.third.zxhuixuan.supply.model.comm;

import cn.hutool.core.annotation.Alias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 地区
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/5 15:34
 */
@Data
public class Area implements Serializable {

    /**
     * 地区ID
     */
    private Long id;

    /**
     * 层级
     */
    private Integer level;
    /**
     * 地区名称
     */
    private String name;

    /**
     * 上级ID
     */
    private Long pid;

    /**
     * 地区编码
     */
    @JsonProperty(value = "zip_code")
    @Alias(value = "zip_code")
    private String zipCode;
}
