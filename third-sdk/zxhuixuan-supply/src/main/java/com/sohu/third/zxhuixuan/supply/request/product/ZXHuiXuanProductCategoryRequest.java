package com.sohu.third.zxhuixuan.supply.request.product;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 甄新汇选供应链获取分类列表请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanProductCategoryRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 分类id
     */
    private String classId;

    /**
     * 上级类目ID一级类目的上级为0
     */
    private String parentId;

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (StringUtils.isNotEmpty(this.classId)) {
            map.put("class_id", this.classId);
        }
        if (StringUtils.isNotEmpty(this.parentId)) {
            map.put("parent_id", this.parentId);
        }
        return map;
    }
}
