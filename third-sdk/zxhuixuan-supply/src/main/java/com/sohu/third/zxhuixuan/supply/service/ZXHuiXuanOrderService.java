package com.sohu.third.zxhuixuan.supply.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.sohu.third.zxhuixuan.supply.constant.ZXHuiXuanConstant;
import com.sohu.third.zxhuixuan.supply.model.ZXHuiXuanConfig;
import com.sohu.third.zxhuixuan.supply.model.order.OrderInfo;
import com.sohu.third.zxhuixuan.supply.model.order.OrderSubmit;
import com.sohu.third.zxhuixuan.supply.request.order.*;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import com.sohu.third.zxhuixuan.supply.response.order.*;
import com.sohu.third.zxhuixuan.supply.util.ApiClient;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;

/**
 * 甄新汇选供应链-订单
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 15:12
 */
@Slf4j
public class ZXHuiXuanOrderService {

    /**
     * 正式下单
     */
    public static ZXHuiXuanOrderSubmitResponse orderSubmit(ZXHuiXuanOrderSubmitRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.OrderUrl.ORDER_SUBMIT + "?versions=v2";
        String result = ApiClient.request(url, request.toSignParamMap(), "post", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanOrderSubmitResponse brandResponse = new ZXHuiXuanOrderSubmitResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            String dataStr = JSONUtil.parseObj(result).getStr("data");
            brandResponse.setOrderSubmit(JSONUtil.toBean(dataStr, OrderSubmit.class));
            return brandResponse;
        }
        return brandResponse;
    }

    /**
     * 查询订单状态
     */
    public static ZXHuiXuanOrderStatusResponse getOrderStatus(ZXHuiXuanOrderStatusRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.OrderUrl.GET_ORDER_STATUS;
        String result = ApiClient.request(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanOrderStatusResponse orderInfoResponse = new ZXHuiXuanOrderStatusResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            String dataStr = JSONUtil.parseObj(result).getStr("data");
            orderInfoResponse.setStatusInfo(JSONUtil.toBean(dataStr, ZXHuiXuanOrderStatusResponse.Status.class));
            return orderInfoResponse;
        }
        return orderInfoResponse;
    }

    /**
     * 查询订单详情
     */
    public static ZXHuiXuanOrderInfoResponse getOrderInfo(ZXHuiXuanOrderInfoRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.OrderUrl.GET_ORDER_DETAILS;
        String result = ApiClient.request(url, request.toSignParamMap(), "get", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanOrderInfoResponse orderInfoResponse = new ZXHuiXuanOrderInfoResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            String dataStr = JSONUtil.parseObj(result).getStr("data");
            orderInfoResponse.setOrderInfo(JSONUtil.toBean(dataStr, OrderInfo.class));
            return orderInfoResponse;
        }
        return orderInfoResponse;
    }

    /**
     * 订单确认收货
     */
    public static ZXHuiXuanOrderConfirmResponse orderConfirm(ZXHuiXuanOrderInfoRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.OrderUrl.ORDER_CONFIRM;
        String result = ApiClient.requestQueryAndBodyEmpty(url, request.toSignParamMap(), "post", config);
        return JSONUtil.toBean(result, ZXHuiXuanOrderConfirmResponse.class);
    }

    /**
     * 多商品查询运费
     */
    public static ZXHuiXuanOrderPostageResponse getOrderPostage(ZXHuiXuanOrderPostageRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.OrderUrl.GET_ORDER_POSTAGE;
        String result = ApiClient.request(url, request.toSignParamMap(), "post", config);
        ZXHuiXuanBaseResponse response = JSONUtil.toBean(result, ZXHuiXuanBaseResponse.class);
        ZXHuiXuanOrderPostageResponse orderInfoResponse = new ZXHuiXuanOrderPostageResponse(response.getStatus(), response.getCode(), response.getMessage());
        if (response.isSuccess()) {
            JSONObject dataObj = JSONUtil.parseObj(result).getJSONObject("data");
            orderInfoResponse.setPostage(dataObj.getBigDecimal("postage"));
            return orderInfoResponse;
        }
        return JSONUtil.toBean(result, ZXHuiXuanOrderPostageResponse.class);
    }

    /**
     * 订单收件人信息更新（待发货状态）
     */
    public static ZXHuiXuanOrderShipUpdateResponse orderShipUpdate(ZXHuiXuanOrderShipUpdateRequest request, ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.OrderUrl.ORDER_SHIP_UPDATE;
        String result = ApiClient.requestQueryAndBodyEmpty(url, request.toSignParamMap(), "post", config);
        return JSONUtil.toBean(result, ZXHuiXuanOrderShipUpdateResponse.class);
    }

    /**
     * 校验账户余额
     */
    public static ZXHuiXuanOrderAccountBalanceResponse getAccountBalance(ZXHuiXuanConfig config) {
        String url = ZXHuiXuanConstant.BASE_URL + ZXHuiXuanConstant.OrderUrl.ORDER_CHECK_ACCOUNT_BALANCE;
        String result = ApiClient.request(url, new HashMap<>(), "get", config);
        return JSONUtil.toBean(result, ZXHuiXuanOrderAccountBalanceResponse.class);
    }

}
