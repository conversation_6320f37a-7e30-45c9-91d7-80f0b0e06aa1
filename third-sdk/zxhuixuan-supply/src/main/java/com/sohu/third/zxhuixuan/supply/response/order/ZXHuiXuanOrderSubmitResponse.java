package com.sohu.third.zxhuixuan.supply.response.order;

import com.sohu.third.zxhuixuan.supply.model.order.OrderSubmit;
import com.sohu.third.zxhuixuan.supply.model.virtual.VirtualOrderSubmit;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * 甄新汇选供应链商品下单返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanOrderSubmitResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 商品下单
     */
    private OrderSubmit orderSubmit;


    public ZXHuiXuanOrderSubmitResponse() {
    }

    public ZXHuiXuanOrderSubmitResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

}
