package com.sohu.third.zxhuixuan.supply.response.product;

import com.sohu.third.zxhuixuan.supply.model.product.Category;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 甄新汇选供应链商品分类返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanProductCategoryResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 分类列表
     */
    private List<Category> categoryList;

    public ZXHuiXuanProductCategoryResponse() {
    }

    public ZXHuiXuanProductCategoryResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }
}
