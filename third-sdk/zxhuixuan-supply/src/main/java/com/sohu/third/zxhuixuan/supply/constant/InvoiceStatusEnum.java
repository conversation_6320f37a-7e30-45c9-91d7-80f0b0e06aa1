package com.sohu.third.zxhuixuan.supply.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * 开票状态枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 8/7/2025 上午 11:47
 */
@Getter
public enum InvoiceStatusEnum {

    PENDING_APPLICATION(1, "待申请开票"),
    NOT_AVAILABLE(2, "不可开票"),
    APPLICATION_IN_PROGRESS(3, "申请开票中"),
    INVOICED(4, "已开票");

    private final Integer code;

    private final String description;

    InvoiceStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据Code获取枚举
     *
     * @param code
     * @return
     */
    public static InvoiceStatusEnum getCode(Integer code) {
        for (InvoiceStatusEnum status : InvoiceStatusEnum.values()) {
            if (Objects.equals(status.code, code)) {
                return status;
            }
        }
        return null;
    }
}
