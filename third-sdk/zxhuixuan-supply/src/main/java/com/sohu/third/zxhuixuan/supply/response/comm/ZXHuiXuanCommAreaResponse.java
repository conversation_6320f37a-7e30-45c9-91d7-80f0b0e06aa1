package com.sohu.third.zxhuixuan.supply.response.comm;

import com.sohu.third.zxhuixuan.supply.model.comm.Area;
import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 甄新汇选供应链地区相关返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanCommAreaResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;
    /**
     * 状态值
     */
    private List<Area> areaList;

    public ZXHuiXuanCommAreaResponse() {
    }

    public ZXHuiXuanCommAreaResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

}
