package com.sohu.third.zxhuixuan.supply.request.product;

import cn.hutool.core.annotation.Alias;
import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 甄新汇选供应链查询商品库存请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanProductSkuStockRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * SKU列表
     */
    private List<SkuItem> skuList;

    /**
     * 收货地区编码，例如:省ID,市ID,区ID 以英文‘,’逗号隔开
     */
    private String shipAreaCode;

    /**
     * sku信息
     */
    @Data
    public static class SkuItem {

        @JsonProperty(value = "sku_id")
        @Alias(value = "sku_id")
        private String skuId;

        @JsonProperty(value = "sku_num")
        @Alias(value = "sku_num")
        private String skuNum;
    }

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (StringUtils.isNotEmpty(this.shipAreaCode)) {
            map.put("shipAreaCode", this.shipAreaCode);
        }
        if (CollectionUtil.isNotEmpty(this.skuList)) {
            List<Map<String, Object>> skuListMapped = this.skuList.stream().map(row -> {
                Map<String, Object> skuMap = new TreeMap<>();
                skuMap.put("sku_id", row.getSkuId());
                skuMap.put("sku_num", row.getSkuNum());
                return skuMap;
            }).collect(Collectors.toList());
            map.put("sku_list", skuListMapped);
        }
        return map;
    }

}
