package com.sohu.third.zxhuixuan.supply.response.order;

import com.sohu.third.zxhuixuan.supply.response.ZXHuiXuanBaseResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * 甄新汇选供应链订单确认收货返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanOrderConfirmResponse extends ZXHuiXuanBaseResponse implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;


    public ZXHuiXuanOrderConfirmResponse() {
    }

    public ZXHuiXuanOrderConfirmResponse(String status, String code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

}
