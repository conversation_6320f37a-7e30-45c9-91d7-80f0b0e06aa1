package com.sohu.third.zxhuixuan.supply.constant;

/**
 * 甄新汇选供应链常量
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/2 15:12
 */
public interface ZXHuiXuanConstant {

    String APPID = "175021695595";

    String SECRET = "PUifPUV7K7x14zcuJAFLfMqLgZo3ijrB";

    String BASE_URL = ZXHuiXuanConstant.Env.TEST;

    /**
     * 环境地址
     */
    interface Env {
        /**
         * 开发环境
         */
        String DEV = "http://175.0.39.170/open";

        /**
         * 测试环境
         */
        String TEST = "https://api-test.zhenxinhuixuan.com/open";

        /**
         * 正式环境
         */
        String PROD = "https://b.zhenxinhuixuan.com/open";
    }

    /**
     * 商品地址
     */
    interface ProductUrl {

        /**
         * 获取品牌列表
         */
        String GET_BRANDS = "/products/getBrands";

        /**
         * 获取分类列表
         */
        String GET_CATEGORY = "/products/getCategory";

        /**
         * 查询商品库SPUID列表
         */
        String GET_SPUID_LIST = "/products/getSpuIdList";

        /**
         * 查询SPUID商品详情
         */
        String GET_SPUID_DETAILS = "/products/getSpuIdByDetails";

        /**
         * 查询SKU规格信息
         */
        String GET_SKU_INFO = "/products/getSkuIdInfo";

        /**
         * 查询商品价格
         */
        String GET_SKU_SALE_PRICE = "/products/findSkuSalePrice";

        /**
         * 查询商品库存
         */
        String GET_SKU_STOCK = "/products/stock";

        /**
         * 查询商品运费模板(优选版块)
         */
        String GET_TEMPLATE_FREIGHT = "/products/getTemplateFreight";

        /**
         * 获取板块标识
         */
        String GET_TYPES = "/products/getTypes";
    }

    /**
     * 消息池地址
     */
    interface MessagePoolUrl {

        /**
         * 获取查询消息池数据
         */
        String GET_MESSAGE_POOL = "/messagepool/getMessagepool";

        /**
         * 删除消息
         */
        String REMOVE_MESSAGE_POOL = "/messagepool/removeMessagepool";

        /**
         * 批量删除消息
         */
        String BATCH_REMOVE_MESSAGE_POOL = "/messagepool/removeMoreMessagepool";
    }

    /**
     * 订单地址
     */
    interface OrderUrl {

        /**
         * 地区相关列表
         */
        String GET_AREA = "/comm/getArea";

        /**
         * 正式下单
         */
        String ORDER_SUBMIT = "/order/submit";

        /**
         * 查询订单状态
         */
        String GET_ORDER_STATUS = "/order/status";

        /**
         * 订单详情
         */
        String GET_ORDER_DETAILS = "/order/details";

        /**
         * 订单确认收货
         */
        String ORDER_CONFIRM = "/order/confirm";

        /**
         * 多商品查询运费
         */
        String GET_ORDER_POSTAGE = "/order/postage";

        /**
         * 售后申请
         */
        String SERVICE_SUBMIT = "/service/submit";

        /**
         * 售后订单详情
         */
        String GET_SERVICE_DETAILS = "/service/details";

        /**
         * 取消售后申请
         */
        String SERVICE_CANCEL = "/service/cancel";

        /**
         * 退换货填写运单号
         */
        String SERVICE_EXPRESS_SUBMIT = "/service/expressSubmit";

        /**
         * 获取物流公司信息
         */
        String GET_EXPRESS = "/comm/getExpress";

        /**
         * 订单收件人信息更新（待发货状态）
         */
        String ORDER_SHIP_UPDATE = "/order/shipUpdate";

        /**
         * 校验账户余额
         */
        String ORDER_CHECK_ACCOUNT_BALANCE = "/order/checkAccountBalance";
    }

    /**
     * 虚拟商品地址
     */
    interface VirtualUrl {

        /**
         * 虚拟商品列表
         */
        String GET_LIST = "/virtual/getList";

        /**
         * 虚拟商品下单
         */
        String ORDER_SUBMIT = "/virtual/orderSubmit";

        /**
         * 查询虚拟订单详情
         */
        String GET_ORDER_INFO = "/virtual/getOrderInfo";
    }
}
