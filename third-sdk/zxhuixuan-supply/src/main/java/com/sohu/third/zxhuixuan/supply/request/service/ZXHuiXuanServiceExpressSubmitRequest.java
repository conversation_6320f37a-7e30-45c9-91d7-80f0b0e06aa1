package com.sohu.third.zxhuixuan.supply.request.service;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 甄新汇选供应链退换货填写运单号请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 11:58
 */
@Data
public class ZXHuiXuanServiceExpressSubmitRequest implements Serializable {

    private static final long serialVersionUID = -1700684337062323114L;

    /**
     * 订单号
     */
    private String channelServiceNo;

    /**
     * 快递公司ID
     */
    private String expressId;

    /**
     * 快递单号
     */
    private String expressNo;

    /**
     * 退款金额，当售后类型为部分退款时必填
     */
    private BigDecimal amountMoney;

    /**
     * 签名专用字段映射（自然排序）
     *
     * @return
     */
    public Map<String, Object> toSignParamMap() {
        Map<String, Object> map = new LinkedHashMap<>();
        if (Objects.nonNull(this.channelServiceNo)) {
            map.put("service_sn", this.channelServiceNo);
        }
        if (StringUtils.isNotEmpty(this.expressNo)) {
            map.put("express_no", this.expressNo);
        }
        if (Objects.nonNull(this.expressId)) {
            map.put("express_id", this.expressId);
        }
        if (Objects.nonNull(this.amountMoney)) {
            map.put("amount_money", this.amountMoney);
        }
        return map;
    }

}
