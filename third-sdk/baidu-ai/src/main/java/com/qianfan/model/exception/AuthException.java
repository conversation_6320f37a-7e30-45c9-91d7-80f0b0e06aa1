/*
 * Copyright (c) 2024 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.qianfan.model.exception;

import java.io.Serializable;

public class AuthException extends QianfanException {
    private final Serializable errorResponse;

    public AuthException(String message) {
        super(message);
        this.errorResponse = null;
    }

    public AuthException(String message, Throwable cause) {
        super(message, cause);
        this.errorResponse = null;
    }

    public AuthException(String message, Serializable errorResponse) {
        super(String.format("%s: %s", message, errorResponse));
        this.errorResponse = errorResponse;
    }

    public Serializable getErrorResponse() {
        return errorResponse;
    }
}
