package com.sohu.job.service;

import com.sohu.system.api.RemoteUserService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BanTaskJobService {

    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 处理到期的封禁任务
     * 每分钟执行一次
     */
    @XxlJob(value = "banTaskJobHandler", init = "init", destroy = "destroy")
    public void banTaskJobHandler() throws Exception {
        remoteUserService.processExpiredBanTasks();
    }

    public void init() {
        log.info("BanTaskJobService定时任务启动");
        XxlJobHelper.log("BanTaskJobService定时任务启动");
    }

    public void destroy() {
        log.info("BanTaskJobService定时任务结束");
    }
}
