package com.sohu.job.service;

import cn.hutool.core.date.DateUtil;
import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.busyorder.api.bo.SohuBusyTaskBo;
import com.sohu.busyorder.api.bo.SohuTaskOrderReportBo;
import com.sohu.busyorder.api.vo.SohuTaskOrderReportVo;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.utils.BigDecimalUtils;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.middle.api.bo.SohuUserDayReportBo;
import com.sohu.middle.api.service.RemoteMiddleTradeRecordService;
import com.sohu.middle.api.service.RemoteUserDayReportService;
import com.sohu.middle.api.service.airec.RemoteMiddleAirecBehaviorService;
import com.sohu.middle.api.vo.SohuAuditTaskReportVo;
import com.sohu.system.api.RemoteSysLogininforService;
import com.sohu.system.api.RemoteUserService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 任务订单及销量趋势（Bean模式）
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TaskOrderStatJobService {

    @DubboReference
    private RemoteBusyTaskService remoteBusyTaskService;

    /**
     * 1、简单任务示例（Bean模式）
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @XxlJob(value = "taskOrderStatJobHandler", init = "init", destroy = "destroy")
    public void taskOrderStatJobHandler() throws Exception {
        Date yesterday = DateUtil.yesterday().toJdkDate();
        Date startTime = DateUtil.beginOfDay(yesterday).toJdkDate();
        Date endTime = DateUtil.endOfDay(yesterday).toJdkDate();
        this.handleTaskOrderStat(yesterday, startTime,endTime);
        startTime = DateUtil.beginOfDay(new Date()).toJdkDate();
        endTime = DateUtil.endOfDay(new Date()).toJdkDate();
        this.handleTaskOrderStat(new Date(), startTime,endTime);
    }

    public void init() {
        log.info("TaskOrderStatJobService定时任务启动");
        XxlJobHelper.log("TaskOrderStatJobService定时任务启动");
    }

    public void destroy() {
        log.info("TaskOrderStatJobService定时任务结束");
        XxlJobHelper.log("TaskOrderStatJobService定时任务结束");
    }

    /**
     * 处理逻辑
     *
     * @param time
     * @param startTime
     * @param endTime
     */
    private void handleTaskOrderStat(Date time, Date startTime, Date endTime) {
        SohuTaskOrderReportBo bo = new SohuTaskOrderReportBo();
        bo.setRecordDate(DateUtils.dateTime(time));
        // 获取任务销售额
        bo.setSalesAmount(remoteBusyTaskService.countSalesAmountByTime(startTime, endTime));
        // 获取订单量(待接单任务)
        Long orderCount = remoteBusyTaskService.countOrderByTime(startTime, endTime);
        bo.setOrderCount(orderCount);
        bo.setDispatchCount(orderCount);
        // 获取接单量(执行中任务)
        Long acceptCount = remoteBusyTaskService.countAcceptByTime(startTime, endTime);
        bo.setAcceptCount(acceptCount);
        // 接单率=执行中任务/待接单任务数
        bo.setAcceptRate(BigDecimalUtils.divide(BigDecimal.valueOf(acceptCount),BigDecimal.valueOf(orderCount)).multiply(BigDecimal.valueOf(100)));
        // 基于时间查询统计表中数据是否存在
        SohuTaskOrderReportVo report = remoteBusyTaskService.queryReportByTime(DateUtils.dateTime(time));
        // 判断是做新增还是更新操作
        if (Objects.isNull(report)) {
            remoteBusyTaskService.saveDataByBo(bo);
        } else {
            bo.setId(report.getId());
            remoteBusyTaskService.updateDataByBo(bo);
        }




    }

}
