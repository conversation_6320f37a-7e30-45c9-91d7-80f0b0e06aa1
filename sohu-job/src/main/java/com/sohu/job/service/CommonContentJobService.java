package com.sohu.job.service;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.busyorder.api.RemoteBusyTaskReceiveService;
import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.busyorder.api.RemoteBusyTaskSiteService;
import com.sohu.busyorder.api.domain.SohuBusyTaskReceiveReqBo;
import com.sohu.busyorder.api.domain.SohuBusyTaskReqBo;
import com.sohu.busyorder.api.enums.BusyTaskTypeEnum;
import com.sohu.busyorder.api.model.SohuBusyTaskModel;
import com.sohu.busyorder.api.model.SohuBusyTaskReceiveModel;
import com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.SohuBusyTaskState;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.middle.api.bo.SohuArticleBo;
import com.sohu.middle.api.bo.SohuVideoBo;
import com.sohu.middle.api.service.RemoteMiddleArticleService;
import com.sohu.middle.api.service.RemoteMiddlePlayletService;
import com.sohu.middle.api.service.RemoteMiddleVideoService;
import com.sohu.middle.api.vo.SohuArticleVo;
import com.sohu.middle.api.vo.SohuCommonContentVo;
import com.sohu.middle.api.vo.SohuPlayletVo;
import com.sohu.middle.api.vo.SohuVideoVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通用内容处理定时任务
 *
 * @Author: leibo
 * @Date: 2025/06/21 10:09
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CommonContentJobService {

    private static final String CONTENT_PREFIX = "sohu:common:content:batch:";

    @DubboReference
    private RemoteMiddleVideoService middleVideoService;

    @DubboReference
    private RemoteMiddleArticleService middleArticleService;

    @DubboReference
    private RemoteMiddlePlayletService middlePlayletService;


    /**
     * 1天执行一次
     *
     * @throws Exception
     */
    @XxlJob(value = "commonContentJobHandler", init = "init", destroy = "destroy")
    public void commonContentJobHandler() throws Exception {
        // 执行10次循环
        for (int batchId = 1; batchId <= 10; batchId++) {
            PageQuery pageQuery = new PageQuery();
            pageQuery.setPageNum(batchId);
            pageQuery.setPageSize(20);
            SohuVideoBo videoBo = new SohuVideoBo();
            videoBo.setState(CommonState.OnShelf.name());
            // 视频查询
            TableDataInfo<SohuVideoVo> videoList = middleVideoService.queryPageOfAirec(videoBo, pageQuery);
            // 图文查询
            SohuArticleBo articleBo = new SohuArticleBo();
            articleBo.setState(CommonState.OnShelf.name());
            TableDataInfo<SohuArticleVo> articleList = middleArticleService.queryPageOfAirec(articleBo, pageQuery);
            // 短剧
            List<SohuPlayletVo> playletList = middlePlayletService.topTen(null, null);
            // 小说

            // 游戏


            // 合并结果
            List<SohuCommonContentVo> combinedContent = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(videoList.getData())) {
                combinedContent.addAll(videoList.getData().stream()
                        .map(SohuCommonContentVo::new)
                        .collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(articleList.getData())) {
                combinedContent.addAll(articleList.getData().stream()
                        .map(SohuCommonContentVo::new)
                        .collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(playletList)) {
                combinedContent.addAll(playletList.stream()
                        .map(SohuCommonContentVo::new)
                        .collect(Collectors.toList()));
            }
            // 随机排序
            Collections.shuffle(combinedContent);
            // 存储到Redis
            String key = CONTENT_PREFIX + batchId;
            RedisUtils.setCacheList(key, combinedContent);
        }
    }


    public void init() {
        log.info("CommonContentJobService定时任务启动");
        XxlJobHelper.log("CommonContentJobService定时任务启动");
    }

    public void destroy() {
        log.info("CommonContentJobService定时任务结束");
        XxlJobHelper.log("CommonContentJobService定时任务结束");
    }

}
