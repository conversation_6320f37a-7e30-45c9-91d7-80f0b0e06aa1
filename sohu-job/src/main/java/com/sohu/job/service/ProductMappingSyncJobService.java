package com.sohu.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.sohu.admin.api.RemoteProductBrandMappingService;
import com.sohu.admin.api.RemoteProductCategoryMappingService;
import com.sohu.admin.api.RemoteThirdPartyBrandService;
import com.sohu.admin.api.RemoteThirdPartyCategoryService;
import com.sohu.admin.api.bo.ProductBrandMappingBo;
import com.sohu.admin.api.bo.ProductCategoryMappingBo;
import com.sohu.admin.api.bo.ThirdPartyCategoryBo;
import com.sohu.admin.api.vo.ProductBrandMappingVo;
import com.sohu.admin.api.vo.ProductCategoryMappingVo;
import com.sohu.admin.api.vo.ThirdPartyBrandVo;
import com.sohu.admin.api.vo.ThirdPartyCategoryVo;
import com.sohu.shopgoods.api.RemoteProductBrandService;
import com.sohu.shopgoods.api.RemoteProductCategoryService;
import com.sohu.shopgoods.api.model.SohuProductCategoryModel;
import com.sohu.shopgoods.api.vo.SohuProductBrandVo;
import com.sohu.shopgoods.api.vo.SohuProductCategoryVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品分类和品牌映射同步定时任务
 * 
 * 功能说明：
 * 1. 同步三方分类与我方分类的映射关系（末级映射）
 * 2. 同步三方品牌与我方品牌的映射关系（末级映射）
 * 3. 支持多渠道映射（如：YOUXUAN、ZHENXIN等）
 * 4. 自动创建缺失的映射关系
 * 5. 更新已存在的映射关系
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductMappingSyncJobService {

    @DubboReference
    private RemoteThirdPartyCategoryService remoteThirdPartyCategoryService;
    
    @DubboReference
    private RemoteThirdPartyBrandService remoteThirdPartyBrandService;
    
    @DubboReference
    private RemoteProductCategoryService remoteProductCategoryService;
    
    @DubboReference
    private RemoteProductBrandService remoteProductBrandService;
    
    @DubboReference
    private RemoteProductCategoryMappingService remoteProductCategoryMappingService;
    
    @DubboReference
    private RemoteProductBrandMappingService remoteProductBrandMappingService;

    /**
     * 商品分类和品牌映射同步定时任务
     * 执行频率：每天凌晨2点执行一次
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @XxlJob(value = "productMappingSyncJobHandler", init = "init", destroy = "destroy")
    public void productMappingSyncJobHandler() throws Exception {
        log.info("开始执行商品分类和品牌映射同步定时任务");
        XxlJobHelper.log("开始执行商品分类和品牌映射同步定时任务");
        
        try {
            // 1. 同步分类映射
            syncCategoryMapping();
            
            // 2. 同步品牌映射
            syncBrandMapping();
            
            log.info("商品分类和品牌映射同步定时任务执行完成");
            XxlJobHelper.log("商品分类和品牌映射同步定时任务执行完成");
            
        } catch (Exception e) {
            log.error("商品分类和品牌映射同步定时任务执行失败", e);
            XxlJobHelper.log("商品分类和品牌映射同步定时任务执行失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 同步分类映射关系
     */
    private void syncCategoryMapping() {
        log.info("开始同步分类映射关系");
        XxlJobHelper.log("开始同步分类映射关系");
        
        try {
            // 1. 获取所有三方末级分类（level=3且isLeaf=1）
            List<ThirdPartyCategoryVo> thirdPartyLeafCategories = getThirdPartyLeafCategories();
            log.info("获取到三方末级分类数量: {}", thirdPartyLeafCategories.size());
            XxlJobHelper.log("获取到三方末级分类数量: " + thirdPartyLeafCategories.size());
            
            if (CollUtil.isEmpty(thirdPartyLeafCategories)) {
                log.warn("未找到三方末级分类，跳过分类映射同步");
                XxlJobHelper.log("未找到三方末级分类，跳过分类映射同步");
                return;
            }
            
            // 2. 获取我方所有二级分类
            List<SohuProductCategoryVo> ourSecondLevelCategories = getOurSecondLevelCategories();
            log.info("获取到我方二级分类数量: {}", ourSecondLevelCategories.size());
            XxlJobHelper.log("获取到我方二级分类数量: " + ourSecondLevelCategories.size());
            
            if (CollUtil.isEmpty(ourSecondLevelCategories)) {
                log.warn("未找到我方二级分类，跳过分类映射同步");
                XxlJobHelper.log("未找到我方二级分类，跳过分类映射同步");
                return;
            }
            
            // 3. 获取现有的分类映射关系
            List<ProductCategoryMappingVo> existingMappings = getExistingCategoryMappings();
            Map<String, ProductCategoryMappingVo> existingMappingMap = existingMappings.stream()
                    .collect(Collectors.toMap(
                            mapping -> mapping.getChannel() + "_" + mapping.getThirdPartyCategoryId(),
                            mapping -> mapping,
                            (existing, replacement) -> existing
                    ));
            
            // 4. 按渠道分组处理三方分类
            Map<String, List<ThirdPartyCategoryVo>> categoryByChannel = thirdPartyLeafCategories.stream()
                    .filter(category -> StrUtil.isNotBlank(category.getChannel()))
                    .collect(Collectors.groupingBy(ThirdPartyCategoryVo::getChannel));
            
            int createdCount = 0;
            int updatedCount = 0;
            
            // 5. 为每个渠道的三方分类创建或更新映射
            for (Map.Entry<String, List<ThirdPartyCategoryVo>> entry : categoryByChannel.entrySet()) {
                String channel = entry.getKey();
                List<ThirdPartyCategoryVo> categories = entry.getValue();
                
                log.info("处理渠道 {} 的分类映射，分类数量: {}", channel, categories.size());
                XxlJobHelper.log("处理渠道 " + channel + " 的分类映射，分类数量: " + categories.size());
                
                for (ThirdPartyCategoryVo thirdPartyCategory : categories) {
                    String mappingKey = channel + "_" + thirdPartyCategory.getId();
                    ProductCategoryMappingVo existingMapping = existingMappingMap.get(mappingKey);
                    
                    // 6. 智能匹配我方分类
                    SohuProductCategoryVo matchedOurCategory = findBestMatchingOurCategory(
                            thirdPartyCategory, ourSecondLevelCategories);
                    
                    if (matchedOurCategory == null) {
                        log.warn("未找到匹配的我方分类，三方分类: {} - {}", 
                                thirdPartyCategory.getId(), thirdPartyCategory.getName());
                        continue;
                    }
                    
                    if (existingMapping == null) {
                        // 创建新映射
                        createCategoryMapping(channel, thirdPartyCategory, matchedOurCategory);
                        createdCount++;
                    } else {
                        // 更新现有映射
                        updateCategoryMapping(existingMapping, thirdPartyCategory, matchedOurCategory);
                        updatedCount++;
                    }
                }
            }
            
            log.info("分类映射同步完成，新增: {}, 更新: {}", createdCount, updatedCount);
            XxlJobHelper.log("分类映射同步完成，新增: " + createdCount + ", 更新: " + updatedCount);
            
        } catch (Exception e) {
            log.error("同步分类映射关系失败", e);
            XxlJobHelper.log("同步分类映射关系失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 同步品牌映射关系
     */
    private void syncBrandMapping() {
        log.info("开始同步品牌映射关系");
        XxlJobHelper.log("开始同步品牌映射关系");
        
        try {
            // 1. 获取所有三方品牌
            List<ThirdPartyBrandVo> thirdPartyBrands = getAllThirdPartyBrands();
            log.info("获取到三方品牌数量: {}", thirdPartyBrands.size());
            XxlJobHelper.log("获取到三方品牌数量: " + thirdPartyBrands.size());
            
            if (CollUtil.isEmpty(thirdPartyBrands)) {
                log.warn("未找到三方品牌，跳过品牌映射同步");
                XxlJobHelper.log("未找到三方品牌，跳过品牌映射同步");
                return;
            }
            
            // 2. 获取我方所有品牌
            List<SohuProductBrandVo> ourBrands = getAllOurBrands();
            log.info("获取到我方品牌数量: {}", ourBrands.size());
            XxlJobHelper.log("获取到我方品牌数量: " + ourBrands.size());
            
            if (CollUtil.isEmpty(ourBrands)) {
                log.warn("未找到我方品牌，跳过品牌映射同步");
                XxlJobHelper.log("未找到我方品牌，跳过品牌映射同步");
                return;
            }
            
            // 3. 获取现有的品牌映射关系
            List<ProductBrandMappingVo> existingMappings = getExistingBrandMappings();
            Map<String, ProductBrandMappingVo> existingMappingMap = existingMappings.stream()
                    .collect(Collectors.toMap(
                            mapping -> mapping.getChannel() + "_" + mapping.getThirdPartyBrandId(),
                            mapping -> mapping,
                            (existing, replacement) -> existing
                    ));
            
            // 4. 按渠道分组处理三方品牌
            Map<String, List<ThirdPartyBrandVo>> brandByChannel = thirdPartyBrands.stream()
                    .filter(brand -> StrUtil.isNotBlank(brand.getChannel()))
                    .collect(Collectors.groupingBy(ThirdPartyBrandVo::getChannel));
            
            int createdCount = 0;
            int updatedCount = 0;
            
            // 5. 为每个渠道的三方品牌创建或更新映射
            for (Map.Entry<String, List<ThirdPartyBrandVo>> entry : brandByChannel.entrySet()) {
                String channel = entry.getKey();
                List<ThirdPartyBrandVo> brands = entry.getValue();
                
                log.info("处理渠道 {} 的品牌映射，品牌数量: {}", channel, brands.size());
                XxlJobHelper.log("处理渠道 " + channel + " 的品牌映射，品牌数量: " + brands.size());
                
                for (ThirdPartyBrandVo thirdPartyBrand : brands) {
                    String mappingKey = channel + "_" + thirdPartyBrand.getId();
                    ProductBrandMappingVo existingMapping = existingMappingMap.get(mappingKey);
                    
                    // 6. 智能匹配我方品牌
                    SohuProductBrandVo matchedOurBrand = findBestMatchingOurBrand(
                            thirdPartyBrand, ourBrands);
                    
                    if (matchedOurBrand == null) {
                        log.warn("未找到匹配的我方品牌，三方品牌: {} - {}", 
                                thirdPartyBrand.getId(), thirdPartyBrand.getName());
                        continue;
                    }
                    
                    if (existingMapping == null) {
                        // 创建新映射
                        createBrandMapping(channel, thirdPartyBrand, matchedOurBrand);
                        createdCount++;
                    } else {
                        // 更新现有映射
                        updateBrandMapping(existingMapping, thirdPartyBrand, matchedOurBrand);
                        updatedCount++;
                    }
                }
            }
            
            log.info("品牌映射同步完成，新增: {}, 更新: {}", createdCount, updatedCount);
            XxlJobHelper.log("品牌映射同步完成，新增: " + createdCount + ", 更新: " + updatedCount);
            
        } catch (Exception e) {
            log.error("同步品牌映射关系失败", e);
            XxlJobHelper.log("同步品牌映射关系失败: " + e.getMessage());
            throw e;
        }
    }

    public void init() {
        log.info("ProductMappingSyncJobService定时任务启动");
        XxlJobHelper.log("ProductMappingSyncJobService定时任务启动");
    }

    public void destroy() {
        log.info("ProductMappingSyncJobService定时任务结束");
        XxlJobHelper.log("ProductMappingSyncJobService定时任务结束");
    }

    // ==================== 分类相关辅助方法 ====================

    /**
     * 获取所有三方末级分类（level=3且isLeaf=1）
     */
    private List<ThirdPartyCategoryVo> getThirdPartyLeafCategories() {
        try {
            // 查询所有末级分类
            return remoteThirdPartyCategoryService.queryList(new ThirdPartyCategoryBo()).stream()
                    .filter(category -> category.getLevel() != null && category.getLevel() == 3)
                    .filter(category -> category.getIsLeaf() != null && category.getIsLeaf() == 1)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取三方末级分类失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取我方所有二级分类
     */
    private List<SohuProductCategoryVo> getOurSecondLevelCategories() {
        try {
            // 查询所有分类，然后过滤出二级分类（pid不为0且不为null）
            List<SohuProductCategoryModel> allCategories = remoteProductCategoryService.pageList();
            return allCategories.stream()
                    .filter(category -> category.getPid() != null && category.getPid() != 0)
                    .filter(category -> category.getIsDel() == null || !category.getIsDel())
                    .map(this::convertToVo)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取我方二级分类失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取现有的分类映射关系
     */
    private List<ProductCategoryMappingVo> getExistingCategoryMappings() {
        try {
            return remoteProductCategoryMappingService.queryList(null);
        } catch (Exception e) {
            log.error("获取现有分类映射关系失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 智能匹配我方分类
     * 匹配规则：
     * 1. 优先精确匹配分类名称
     * 2. 其次模糊匹配分类名称
     * 3. 最后根据关键词匹配
     */
    private SohuProductCategoryVo findBestMatchingOurCategory(
            ThirdPartyCategoryVo thirdPartyCategory,
            List<SohuProductCategoryVo> ourCategories) {

        if (StrUtil.isBlank(thirdPartyCategory.getName()) || CollUtil.isEmpty(ourCategories)) {
            return null;
        }

        String thirdPartyName = thirdPartyCategory.getName().trim();

        // 1. 精确匹配
        Optional<SohuProductCategoryVo> exactMatch = ourCategories.stream()
                .filter(category -> thirdPartyName.equals(category.getName()))
                .findFirst();
        if (exactMatch.isPresent()) {
            return exactMatch.get();
        }

        // 2. 模糊匹配（包含关系）
        Optional<SohuProductCategoryVo> fuzzyMatch = ourCategories.stream()
                .filter(category -> thirdPartyName.contains(category.getName()) ||
                                   category.getName().contains(thirdPartyName))
                .findFirst();
        if (fuzzyMatch.isPresent()) {
            return fuzzyMatch.get();
        }

        // 3. 关键词匹配（基于分类路径）
        if (StrUtil.isNotBlank(thirdPartyCategory.getFullPath())) {
            String[] pathKeywords = thirdPartyCategory.getFullPath().split("[/\\-_\\s]+");
            for (String keyword : pathKeywords) {
                if (StrUtil.isNotBlank(keyword) && keyword.length() > 1) {
                    Optional<SohuProductCategoryVo> keywordMatch = ourCategories.stream()
                            .filter(category -> category.getName().contains(keyword))
                            .findFirst();
                    if (keywordMatch.isPresent()) {
                        return keywordMatch.get();
                    }
                }
            }
        }

        // 4. 如果都没有匹配到，返回默认分类（可以配置）
        return ourCategories.stream()
                .filter(category -> "其他".equals(category.getName()) ||
                                   "默认".equals(category.getName()) ||
                                   "通用".equals(category.getName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 创建分类映射
     */
    private void createCategoryMapping(String channel,
                                     ThirdPartyCategoryVo thirdPartyCategory,
                                     SohuProductCategoryVo ourCategory) {
        try {
            ProductCategoryMappingBo mappingBo = new ProductCategoryMappingBo();
            mappingBo.setChannel(channel);
            mappingBo.setMerId(ourCategory.getMerId() != null ? ourCategory.getMerId().toString() : "");
            mappingBo.setOurCategoryId(ourCategory.getId());
            mappingBo.setThirdPartyCategoryId(thirdPartyCategory.getId());
            mappingBo.setOurCategoryPath(buildOurCategoryPath(ourCategory));
            mappingBo.setThirdPartyCategoryPath(thirdPartyCategory.getFullPath());
            mappingBo.setOperator(1L);

            remoteProductCategoryMappingService.insertByBo(mappingBo);

            log.debug("创建分类映射成功: {} -> {}", thirdPartyCategory.getName(), ourCategory.getName());

        } catch (Exception e) {
            log.error("创建分类映射失败: {} -> {}", thirdPartyCategory.getName(), ourCategory.getName(), e);
        }
    }

    /**
     * 更新分类映射
     */
    private void updateCategoryMapping(ProductCategoryMappingVo existingMapping,
                                     ThirdPartyCategoryVo thirdPartyCategory,
                                     SohuProductCategoryVo ourCategory) {
        try {
            // 检查是否需要更新
            boolean needUpdate = false;

            if (!Objects.equals(existingMapping.getOurCategoryId(), ourCategory.getId())) {
                needUpdate = true;
            }

            String newOurCategoryPath = buildOurCategoryPath(ourCategory);
            if (!Objects.equals(existingMapping.getOurCategoryPath(), newOurCategoryPath)) {
                needUpdate = true;
            }

            if (!Objects.equals(existingMapping.getThirdPartyCategoryPath(), thirdPartyCategory.getFullPath())) {
                needUpdate = true;
            }

            if (needUpdate) {
                ProductCategoryMappingBo mappingBo = new ProductCategoryMappingBo();
                mappingBo.setId(existingMapping.getId());
                mappingBo.setChannel(existingMapping.getChannel());
                mappingBo.setMerId(existingMapping.getMerId());
                mappingBo.setOurCategoryId(ourCategory.getId());
                mappingBo.setThirdPartyCategoryId(thirdPartyCategory.getId());
                mappingBo.setOurCategoryPath(newOurCategoryPath);
                mappingBo.setThirdPartyCategoryPath(thirdPartyCategory.getFullPath());
                mappingBo.setOperator(1L);

                remoteProductCategoryMappingService.updateByBo(mappingBo);

                log.debug("更新分类映射成功: {} -> {}", thirdPartyCategory.getName(), ourCategory.getName());
            }

        } catch (Exception e) {
            log.error("更新分类映射失败: {} -> {}", thirdPartyCategory.getName(), ourCategory.getName(), e);
        }
    }

    /**
     * 构建我方分类路径
     */
    private String buildOurCategoryPath(SohuProductCategoryVo category) {
        if (category == null) {
            return "";
        }

        try {
            // 如果有父分类，构建完整路径
            if (category.getPid() != null && category.getPid() != 0) {
                List<SohuProductCategoryModel> allCategories = remoteProductCategoryService.pageList();
                Optional<SohuProductCategoryModel> parentCategory = allCategories.stream()
                        .filter(cat -> Objects.equals(cat.getId(), category.getPid()))
                        .findFirst();

                if (parentCategory.isPresent()) {
                    return parentCategory.get().getName() + "/" + category.getName();
                }
            }

            return category.getName();

        } catch (Exception e) {
            log.error("构建分类路径失败", e);
            return category.getName();
        }
    }

    /**
     * 转换Model到Vo
     */
    private SohuProductCategoryVo convertToVo(SohuProductCategoryModel model) {
        if (model == null) {
            return null;
        }

        SohuProductCategoryVo vo = new SohuProductCategoryVo();
        vo.setId(model.getId());
        vo.setMerId(model.getMerId());
        vo.setUserId(model.getUserId());
        vo.setPid(model.getPid());
        vo.setName(model.getName());
        vo.setIcon(model.getIcon());
        vo.setSort(model.getSort());
        vo.setIsShow(model.getIsShow());
        vo.setIsDel(model.getIsDel());

        return vo;
    }

    // ==================== 品牌相关辅助方法 ====================

    /**
     * 获取所有三方品牌
     */
    private List<ThirdPartyBrandVo> getAllThirdPartyBrands() {
        try {
            return remoteThirdPartyBrandService.queryList(null).stream()
                    .filter(brand -> brand.getIsDeleted() == null || brand.getIsDeleted() == 0)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取三方品牌失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取我方所有品牌
     */
    private List<SohuProductBrandVo> getAllOurBrands() {
        try {
            // 使用list方法获取所有品牌，传入null表示获取所有
            return remoteProductBrandService.list(null);
        } catch (Exception e) {
            log.error("获取我方品牌失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取现有的品牌映射关系
     */
    private List<ProductBrandMappingVo> getExistingBrandMappings() {
        try {
            return remoteProductBrandMappingService.queryList(null);
        } catch (Exception e) {
            log.error("获取现有品牌映射关系失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 智能匹配我方品牌
     * 匹配规则：
     * 1. 优先精确匹配品牌名称
     * 2. 其次模糊匹配品牌名称（忽略大小写）
     * 3. 最后根据品牌名称关键词匹配
     */
    private SohuProductBrandVo findBestMatchingOurBrand(
            ThirdPartyBrandVo thirdPartyBrand,
            List<SohuProductBrandVo> ourBrands) {

        if (StrUtil.isBlank(thirdPartyBrand.getName()) || CollUtil.isEmpty(ourBrands)) {
            return null;
        }

        String thirdPartyName = thirdPartyBrand.getName().trim();

        // 1. 精确匹配
        Optional<SohuProductBrandVo> exactMatch = ourBrands.stream()
                .filter(brand -> thirdPartyName.equals(brand.getName()))
                .findFirst();
        if (exactMatch.isPresent()) {
            return exactMatch.get();
        }

        // 2. 忽略大小写的精确匹配
        Optional<SohuProductBrandVo> caseInsensitiveMatch = ourBrands.stream()
                .filter(brand -> thirdPartyName.equalsIgnoreCase(brand.getName()))
                .findFirst();
        if (caseInsensitiveMatch.isPresent()) {
            return caseInsensitiveMatch.get();
        }

        // 3. 模糊匹配（包含关系）
        Optional<SohuProductBrandVo> fuzzyMatch = ourBrands.stream()
                .filter(brand -> thirdPartyName.toLowerCase().contains(brand.getName().toLowerCase()) ||
                                brand.getName().toLowerCase().contains(thirdPartyName.toLowerCase()))
                .findFirst();
        if (fuzzyMatch.isPresent()) {
            return fuzzyMatch.get();
        }

        // 4. 关键词匹配（去除常见后缀）
        String cleanedThirdPartyName = cleanBrandName(thirdPartyName);
        if (!cleanedThirdPartyName.equals(thirdPartyName)) {
            Optional<SohuProductBrandVo> cleanedMatch = ourBrands.stream()
                    .filter(brand -> cleanedThirdPartyName.equalsIgnoreCase(cleanBrandName(brand.getName())))
                    .findFirst();
            if (cleanedMatch.isPresent()) {
                return cleanedMatch.get();
            }
        }

        // 5. 如果都没有匹配到，返回默认品牌（可以配置）
        return ourBrands.stream()
                .filter(brand -> "其他品牌".equals(brand.getName()) ||
                                "默认品牌".equals(brand.getName()) ||
                                "通用品牌".equals(brand.getName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 清理品牌名称，去除常见后缀和前缀
     */
    private String cleanBrandName(String brandName) {
        if (StrUtil.isBlank(brandName)) {
            return "";
        }

        String cleaned = brandName.trim();

        // 去除常见的品牌后缀
        String[] suffixesToRemove = {
                "旗舰店", "官方店", "专卖店", "品牌店", "直营店",
                "有限公司", "股份有限公司", "集团", "公司", "企业",
                "品牌", "商标", "trademark", "brand", "official",
                "旗舰", "官方", "专营", "直营"
        };

        for (String suffix : suffixesToRemove) {
            if (cleaned.endsWith(suffix)) {
                cleaned = cleaned.substring(0, cleaned.length() - suffix.length()).trim();
            }
        }

        // 去除常见的品牌前缀
        String[] prefixesToRemove = {
                "正品", "官方", "原装", "authentic", "official", "genuine"
        };

        for (String prefix : prefixesToRemove) {
            if (cleaned.startsWith(prefix)) {
                cleaned = cleaned.substring(prefix.length()).trim();
            }
        }

        return cleaned;
    }

    /**
     * 创建品牌映射
     */
    private void createBrandMapping(String channel,
                                  ThirdPartyBrandVo thirdPartyBrand,
                                  SohuProductBrandVo ourBrand) {
        try {
            ProductBrandMappingBo mappingBo = new ProductBrandMappingBo();
            mappingBo.setChannel(channel);
            mappingBo.setOurBrandId(ourBrand.getId());
            mappingBo.setThirdPartyBrandId(thirdPartyBrand.getId());
            mappingBo.setOurBrandName(ourBrand.getName());
            mappingBo.setThirdPartyBrandName(thirdPartyBrand.getName());
            mappingBo.setOperator(1L);

            remoteProductBrandMappingService.insertByBo(mappingBo);

            log.debug("创建品牌映射成功: {} -> {}", thirdPartyBrand.getName(), ourBrand.getName());

        } catch (Exception e) {
            log.error("创建品牌映射失败: {} -> {}", thirdPartyBrand.getName(), ourBrand.getName(), e);
        }
    }

    /**
     * 更新品牌映射
     */
    private void updateBrandMapping(ProductBrandMappingVo existingMapping,
                                  ThirdPartyBrandVo thirdPartyBrand,
                                  SohuProductBrandVo ourBrand) {
        try {
            // 检查是否需要更新
            boolean needUpdate = false;

            if (!Objects.equals(existingMapping.getOurBrandId(), ourBrand.getId())) {
                needUpdate = true;
            }

            if (!Objects.equals(existingMapping.getOurBrandName(), ourBrand.getName())) {
                needUpdate = true;
            }

            if (!Objects.equals(existingMapping.getThirdPartyBrandName(), thirdPartyBrand.getName())) {
                needUpdate = true;
            }

            if (needUpdate) {
                ProductBrandMappingBo mappingBo = new ProductBrandMappingBo();
                mappingBo.setId(existingMapping.getId());
                mappingBo.setChannel(existingMapping.getChannel());
                mappingBo.setOurBrandId(ourBrand.getId());
                mappingBo.setThirdPartyBrandId(thirdPartyBrand.getId());
                mappingBo.setOurBrandName(ourBrand.getName());
                mappingBo.setThirdPartyBrandName(thirdPartyBrand.getName());
                mappingBo.setOperator(1L);

                remoteProductBrandMappingService.updateByBo(mappingBo);

                log.debug("更新品牌映射成功: {} -> {}", thirdPartyBrand.getName(), ourBrand.getName());
            }

        } catch (Exception e) {
            log.error("更新品牌映射失败: {} -> {}", thirdPartyBrand.getName(), ourBrand.getName(), e);
        }
    }
}
