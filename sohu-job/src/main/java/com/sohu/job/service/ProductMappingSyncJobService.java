package com.sohu.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.admin.api.RemoteProductCategoryMappingService;
import com.sohu.admin.api.RemoteProductBrandMappingService;
import com.sohu.admin.api.RemoteThirdPartyCategoryService;
import com.sohu.admin.api.RemoteThirdPartyBrandService;
import com.sohu.admin.api.bo.ProductCategoryMappingBo;
import com.sohu.admin.api.bo.ProductBrandMappingBo;
import com.sohu.admin.api.vo.ThirdPartyCategoryVo;
import com.sohu.admin.api.vo.ThirdPartyBrandVo;
import com.sohu.admin.api.vo.ProductCategoryMappingVo;
import com.sohu.admin.api.vo.ProductBrandMappingVo;
import com.sohu.shopgoods.api.RemoteProductCategoryService;
import com.sohu.shopgoods.api.RemoteProductBrandService;
import com.sohu.shopgoods.api.vo.SohuProductCategoryVo;
import com.sohu.shopgoods.api.vo.SohuProductBrandVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品分类和品牌映射同步定时任务
 * 
 * 功能说明：
 * 1. 同步三方分类与我方分类的映射关系（末级映射）
 * 2. 同步三方品牌与我方品牌的映射关系（末级映射）
 * 3. 支持多渠道映射（如：YOUXUAN、ZHENXIN等）
 * 4. 自动创建缺失的映射关系
 * 5. 更新已存在的映射关系
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductMappingSyncJobService {

    @DubboReference
    private RemoteThirdPartyCategoryService remoteThirdPartyCategoryService;
    
    @DubboReference
    private RemoteThirdPartyBrandService remoteThirdPartyBrandService;
    
    @DubboReference
    private RemoteProductCategoryService remoteProductCategoryService;
    
    @DubboReference
    private RemoteProductBrandService remoteProductBrandService;
    
    @DubboReference
    private RemoteProductCategoryMappingService remoteProductCategoryMappingService;
    
    @DubboReference
    private RemoteProductBrandMappingService remoteProductBrandMappingService;

    /**
     * 商品分类和品牌映射同步定时任务
     * 执行频率：每天凌晨2点执行一次
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @XxlJob(value = "productMappingSyncJobHandler", init = "init", destroy = "destroy")
    public void productMappingSyncJobHandler() throws Exception {
        log.info("开始执行商品分类和品牌映射同步定时任务");
        XxlJobHelper.log("开始执行商品分类和品牌映射同步定时任务");
        
        try {
            // 1. 同步分类映射
            syncCategoryMapping();
            
            // 2. 同步品牌映射
            syncBrandMapping();
            
            log.info("商品分类和品牌映射同步定时任务执行完成");
            XxlJobHelper.log("商品分类和品牌映射同步定时任务执行完成");
            
        } catch (Exception e) {
            log.error("商品分类和品牌映射同步定时任务执行失败", e);
            XxlJobHelper.log("商品分类和品牌映射同步定时任务执行失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 同步分类映射关系
     */
    private void syncCategoryMapping() {
        log.info("开始同步分类映射关系");
        XxlJobHelper.log("开始同步分类映射关系");
        
        try {
            // 1. 获取所有三方末级分类（level=3且isLeaf=1）
            List<ThirdPartyCategoryVo> thirdPartyLeafCategories = getThirdPartyLeafCategories();
            log.info("获取到三方末级分类数量: {}", thirdPartyLeafCategories.size());
            XxlJobHelper.log("获取到三方末级分类数量: " + thirdPartyLeafCategories.size());
            
            if (CollUtil.isEmpty(thirdPartyLeafCategories)) {
                log.warn("未找到三方末级分类，跳过分类映射同步");
                XxlJobHelper.log("未找到三方末级分类，跳过分类映射同步");
                return;
            }
            
            // 2. 获取我方所有二级分类
            List<SohuProductCategoryVo> ourSecondLevelCategories = getOurSecondLevelCategories();
            log.info("获取到我方二级分类数量: {}", ourSecondLevelCategories.size());
            XxlJobHelper.log("获取到我方二级分类数量: " + ourSecondLevelCategories.size());
            
            if (CollUtil.isEmpty(ourSecondLevelCategories)) {
                log.warn("未找到我方二级分类，跳过分类映射同步");
                XxlJobHelper.log("未找到我方二级分类，跳过分类映射同步");
                return;
            }
            
            // 3. 获取现有的分类映射关系
            List<ProductCategoryMappingVo> existingMappings = getExistingCategoryMappings();
            Map<String, ProductCategoryMappingVo> existingMappingMap = existingMappings.stream()
                    .collect(Collectors.toMap(
                            mapping -> mapping.getChannel() + "_" + mapping.getThirdPartyCategoryId(),
                            mapping -> mapping,
                            (existing, replacement) -> existing
                    ));
            
            // 4. 按渠道分组处理三方分类
            Map<String, List<ThirdPartyCategoryVo>> categoryByChannel = thirdPartyLeafCategories.stream()
                    .filter(category -> StrUtil.isNotBlank(category.getChannel()))
                    .collect(Collectors.groupingBy(ThirdPartyCategoryVo::getChannel));
            
            int createdCount = 0;
            int updatedCount = 0;
            
            // 5. 为每个渠道的三方分类创建或更新映射
            for (Map.Entry<String, List<ThirdPartyCategoryVo>> entry : categoryByChannel.entrySet()) {
                String channel = entry.getKey();
                List<ThirdPartyCategoryVo> categories = entry.getValue();
                
                log.info("处理渠道 {} 的分类映射，分类数量: {}", channel, categories.size());
                XxlJobHelper.log("处理渠道 " + channel + " 的分类映射，分类数量: " + categories.size());
                
                for (ThirdPartyCategoryVo thirdPartyCategory : categories) {
                    String mappingKey = channel + "_" + thirdPartyCategory.getId();
                    ProductCategoryMappingVo existingMapping = existingMappingMap.get(mappingKey);
                    
                    // 6. 智能匹配我方分类
                    SohuProductCategoryVo matchedOurCategory = findBestMatchingOurCategory(
                            thirdPartyCategory, ourSecondLevelCategories);
                    
                    if (matchedOurCategory == null) {
                        log.warn("未找到匹配的我方分类，三方分类: {} - {}", 
                                thirdPartyCategory.getId(), thirdPartyCategory.getName());
                        continue;
                    }
                    
                    if (existingMapping == null) {
                        // 创建新映射
                        createCategoryMapping(channel, thirdPartyCategory, matchedOurCategory);
                        createdCount++;
                    } else {
                        // 更新现有映射
                        updateCategoryMapping(existingMapping, thirdPartyCategory, matchedOurCategory);
                        updatedCount++;
                    }
                }
            }
            
            log.info("分类映射同步完成，新增: {}, 更新: {}", createdCount, updatedCount);
            XxlJobHelper.log("分类映射同步完成，新增: " + createdCount + ", 更新: " + updatedCount);
            
        } catch (Exception e) {
            log.error("同步分类映射关系失败", e);
            XxlJobHelper.log("同步分类映射关系失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 同步品牌映射关系
     */
    private void syncBrandMapping() {
        log.info("开始同步品牌映射关系");
        XxlJobHelper.log("开始同步品牌映射关系");
        
        try {
            // 1. 获取所有三方品牌
            List<ThirdPartyBrandVo> thirdPartyBrands = getAllThirdPartyBrands();
            log.info("获取到三方品牌数量: {}", thirdPartyBrands.size());
            XxlJobHelper.log("获取到三方品牌数量: " + thirdPartyBrands.size());
            
            if (CollUtil.isEmpty(thirdPartyBrands)) {
                log.warn("未找到三方品牌，跳过品牌映射同步");
                XxlJobHelper.log("未找到三方品牌，跳过品牌映射同步");
                return;
            }
            
            // 2. 获取我方所有品牌
            List<SohuProductBrandVo> ourBrands = getAllOurBrands();
            log.info("获取到我方品牌数量: {}", ourBrands.size());
            XxlJobHelper.log("获取到我方品牌数量: " + ourBrands.size());
            
            if (CollUtil.isEmpty(ourBrands)) {
                log.warn("未找到我方品牌，跳过品牌映射同步");
                XxlJobHelper.log("未找到我方品牌，跳过品牌映射同步");
                return;
            }
            
            // 3. 获取现有的品牌映射关系
            List<ProductBrandMappingVo> existingMappings = getExistingBrandMappings();
            Map<String, ProductBrandMappingVo> existingMappingMap = existingMappings.stream()
                    .collect(Collectors.toMap(
                            mapping -> mapping.getChannel() + "_" + mapping.getThirdPartyBrandId(),
                            mapping -> mapping,
                            (existing, replacement) -> existing
                    ));
            
            // 4. 按渠道分组处理三方品牌
            Map<String, List<ThirdPartyBrandVo>> brandByChannel = thirdPartyBrands.stream()
                    .filter(brand -> StrUtil.isNotBlank(brand.getChannel()))
                    .collect(Collectors.groupingBy(ThirdPartyBrandVo::getChannel));
            
            int createdCount = 0;
            int updatedCount = 0;
            
            // 5. 为每个渠道的三方品牌创建或更新映射
            for (Map.Entry<String, List<ThirdPartyBrandVo>> entry : brandByChannel.entrySet()) {
                String channel = entry.getKey();
                List<ThirdPartyBrandVo> brands = entry.getValue();
                
                log.info("处理渠道 {} 的品牌映射，品牌数量: {}", channel, brands.size());
                XxlJobHelper.log("处理渠道 " + channel + " 的品牌映射，品牌数量: " + brands.size());
                
                for (ThirdPartyBrandVo thirdPartyBrand : brands) {
                    String mappingKey = channel + "_" + thirdPartyBrand.getId();
                    ProductBrandMappingVo existingMapping = existingMappingMap.get(mappingKey);
                    
                    // 6. 智能匹配我方品牌
                    SohuProductBrandVo matchedOurBrand = findBestMatchingOurBrand(
                            thirdPartyBrand, ourBrands);
                    
                    if (matchedOurBrand == null) {
                        log.warn("未找到匹配的我方品牌，三方品牌: {} - {}", 
                                thirdPartyBrand.getId(), thirdPartyBrand.getName());
                        continue;
                    }
                    
                    if (existingMapping == null) {
                        // 创建新映射
                        createBrandMapping(channel, thirdPartyBrand, matchedOurBrand);
                        createdCount++;
                    } else {
                        // 更新现有映射
                        updateBrandMapping(existingMapping, thirdPartyBrand, matchedOurBrand);
                        updatedCount++;
                    }
                }
            }
            
            log.info("品牌映射同步完成，新增: {}, 更新: {}", createdCount, updatedCount);
            XxlJobHelper.log("品牌映射同步完成，新增: " + createdCount + ", 更新: " + updatedCount);
            
        } catch (Exception e) {
            log.error("同步品牌映射关系失败", e);
            XxlJobHelper.log("同步品牌映射关系失败: " + e.getMessage());
            throw e;
        }
    }

    public void init() {
        log.info("ProductMappingSyncJobService定时任务启动");
        XxlJobHelper.log("ProductMappingSyncJobService定时任务启动");
    }

    public void destroy() {
        log.info("ProductMappingSyncJobService定时任务结束");
        XxlJobHelper.log("ProductMappingSyncJobService定时任务结束");
    }

    // ==================== 分类相关辅助方法 ====================

    /**
     * 获取所有三方末级分类（level=3且isLeaf=1）
     */
    private List<ThirdPartyCategoryVo> getThirdPartyLeafCategories() {
        try {
            // 查询所有末级分类
            return remoteThirdPartyCategoryService.queryList(null).stream()
                    .filter(category -> category.getLevel() != null && category.getLevel() == 3)
                    .filter(category -> category.getIsLeaf() != null && category.getIsLeaf() == 1)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取三方末级分类失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取我方所有二级分类
     */
    private List<SohuProductCategoryVo> getOurSecondLevelCategories() {
        try {
            // 查询所有二级分类（pid不为0且不为null）
            return remoteProductCategoryService.queryList(null).stream()
                    .filter(category -> category.getPid() != null && category.getPid() != 0)
                    .filter(category -> category.getIsDel() == null || !category.getIsDel())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取我方二级分类失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取现有的分类映射关系
     */
    private List<ProductCategoryMappingVo> getExistingCategoryMappings() {
        try {
            return remoteProductCategoryMappingService.queryList(null);
        } catch (Exception e) {
            log.error("获取现有分类映射关系失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 智能匹配我方分类
     * 匹配规则：
     * 1. 优先精确匹配分类名称
     * 2. 其次模糊匹配分类名称
     * 3. 最后根据关键词匹配
     */
    private SohuProductCategoryVo findBestMatchingOurCategory(
            ThirdPartyCategoryVo thirdPartyCategory,
            List<SohuProductCategoryVo> ourCategories) {

        if (StrUtil.isBlank(thirdPartyCategory.getName()) || CollUtil.isEmpty(ourCategories)) {
            return null;
        }

        String thirdPartyName = thirdPartyCategory.getName().trim();

        // 1. 精确匹配
        Optional<SohuProductCategoryVo> exactMatch = ourCategories.stream()
                .filter(category -> thirdPartyName.equals(category.getName()))
                .findFirst();
        if (exactMatch.isPresent()) {
            return exactMatch.get();
        }

        // 2. 模糊匹配（包含关系）
        Optional<SohuProductCategoryVo> fuzzyMatch = ourCategories.stream()
                .filter(category -> thirdPartyName.contains(category.getName()) ||
                                   category.getName().contains(thirdPartyName))
                .findFirst();
        if (fuzzyMatch.isPresent()) {
            return fuzzyMatch.get();
        }

        // 3. 关键词匹配（基于分类路径）
        if (StrUtil.isNotBlank(thirdPartyCategory.getFullPath())) {
            String[] pathKeywords = thirdPartyCategory.getFullPath().split("[/\\-_\\s]+");
            for (String keyword : pathKeywords) {
                if (StrUtil.isNotBlank(keyword) && keyword.length() > 1) {
                    Optional<SohuProductCategoryVo> keywordMatch = ourCategories.stream()
                            .filter(category -> category.getName().contains(keyword))
                            .findFirst();
                    if (keywordMatch.isPresent()) {
                        return keywordMatch.get();
                    }
                }
            }
        }

        // 4. 如果都没有匹配到，返回默认分类（可以配置）
        return ourCategories.stream()
                .filter(category -> "其他".equals(category.getName()) ||
                                   "默认".equals(category.getName()) ||
                                   "通用".equals(category.getName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 创建分类映射
     */
    private void createCategoryMapping(String channel,
                                     ThirdPartyCategoryVo thirdPartyCategory,
                                     SohuProductCategoryVo ourCategory) {
        try {
            ProductCategoryMappingBo mappingBo = new ProductCategoryMappingBo();
            mappingBo.setChannel(channel);
            mappingBo.setMerId(ourCategory.getMerId() != null ? ourCategory.getMerId().toString() : "");
            mappingBo.setOurCategoryId(ourCategory.getId());
            mappingBo.setThirdPartyCategoryId(thirdPartyCategory.getId());
            mappingBo.setOurCategoryPath(buildOurCategoryPath(ourCategory));
            mappingBo.setThirdPartyCategoryPath(thirdPartyCategory.getFullPath());
            mappingBo.setOperator("SYSTEM_AUTO_SYNC");

            remoteProductCategoryMappingService.insertByBo(mappingBo);

            log.debug("创建分类映射成功: {} -> {}", thirdPartyCategory.getName(), ourCategory.getName());

        } catch (Exception e) {
            log.error("创建分类映射失败: {} -> {}", thirdPartyCategory.getName(), ourCategory.getName(), e);
        }
    }

    /**
     * 更新分类映射
     */
    private void updateCategoryMapping(ProductCategoryMappingVo existingMapping,
                                     ThirdPartyCategoryVo thirdPartyCategory,
                                     SohuProductCategoryVo ourCategory) {
        try {
            // 检查是否需要更新
            boolean needUpdate = false;

            if (!Objects.equals(existingMapping.getOurCategoryId(), ourCategory.getId())) {
                needUpdate = true;
            }

            String newOurCategoryPath = buildOurCategoryPath(ourCategory);
            if (!Objects.equals(existingMapping.getOurCategoryPath(), newOurCategoryPath)) {
                needUpdate = true;
            }

            if (!Objects.equals(existingMapping.getThirdPartyCategoryPath(), thirdPartyCategory.getFullPath())) {
                needUpdate = true;
            }

            if (needUpdate) {
                ProductCategoryMappingBo mappingBo = new ProductCategoryMappingBo();
                mappingBo.setId(existingMapping.getId());
                mappingBo.setChannel(existingMapping.getChannel());
                mappingBo.setMerId(existingMapping.getMerId());
                mappingBo.setOurCategoryId(ourCategory.getId());
                mappingBo.setThirdPartyCategoryId(thirdPartyCategory.getId());
                mappingBo.setOurCategoryPath(newOurCategoryPath);
                mappingBo.setThirdPartyCategoryPath(thirdPartyCategory.getFullPath());
                mappingBo.setOperator("SYSTEM_AUTO_SYNC");

                remoteProductCategoryMappingService.updateByBo(mappingBo);

                log.debug("更新分类映射成功: {} -> {}", thirdPartyCategory.getName(), ourCategory.getName());
            }

        } catch (Exception e) {
            log.error("更新分类映射失败: {} -> {}", thirdPartyCategory.getName(), ourCategory.getName(), e);
        }
    }

    /**
     * 构建我方分类路径
     */
    private String buildOurCategoryPath(SohuProductCategoryVo category) {
        if (category == null) {
            return "";
        }

        try {
            // 如果有父分类，构建完整路径
            if (category.getPid() != null && category.getPid() != 0) {
                List<SohuProductCategoryVo> allCategories = remoteProductCategoryService.queryList(null);
                Optional<SohuProductCategoryVo> parentCategory = allCategories.stream()
                        .filter(cat -> Objects.equals(cat.getId(), category.getPid()))
                        .findFirst();

                if (parentCategory.isPresent()) {
                    return parentCategory.get().getName() + "/" + category.getName();
                }
            }

            return category.getName();

        } catch (Exception e) {
            log.error("构建分类路径失败", e);
            return category.getName();
        }
    }
}
