package com.sohu.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.middle.api.bo.SohuUserDayReportBo;
import com.sohu.middle.api.service.RemoteMiddleTradeRecordService;
import com.sohu.middle.api.service.RemoteUserBehaviorService;
import com.sohu.middle.api.service.RemoteUserDayReportService;
import com.sohu.middle.api.service.airec.RemoteMiddleAirecBehaviorService;
import com.sohu.middle.api.vo.UserBehaviorGroupInfoVo;
import com.sohu.report.api.RemoteUserReportService;
import com.sohu.report.api.bo.SohuUserViewReportBo;
import com.sohu.system.api.RemoteSysLogininforService;
import com.sohu.system.api.RemoteUserService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户总览统计（Bean模式）
 * <p>
 * 开发步骤：
 * 1、任务开发：在Spring Bean实例中，开发Job方法；
 * 2、注解配置：为Job方法添加注解 "@XxlJob(value="自定义jobhandler名称", init = "JobHandler初始化方法", destroy = "JobHandler销毁方法")"，注解value值对应的是调度中心新建任务的JobHandler属性的值。
 * 3、执行日志：需要通过 "XxlJobHelper.log" 打印执行日志；
 * 4、任务结果：默认任务结果为 "成功" 状态，不需要主动设置；如有诉求，比如设置任务结果为失败，可以通过 "XxlJobHelper.handleFail/handleSuccess" 自主设置任务结果；
 */
@Slf4j
@Service
public class UserStatJobService {

    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteMiddleTradeRecordService remoteMiddleTradeRecordService;
    @DubboReference
    private RemoteMiddleAirecBehaviorService remoteMiddleAirecBehaviorService;
    @DubboReference
    private RemoteUserDayReportService remoteUserDayReportService;
    @DubboReference
    private RemoteSysLogininforService remoteSysLogininforService;
    @DubboReference
    private RemoteUserBehaviorService remoteUserBehaviorService;
    @DubboReference
    private RemoteUserReportService remoteUserReportService;

    /**
     * 1、简单任务示例（Bean模式）
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @XxlJob(value = "userStatJobHandler", init = "init", destroy = "destroy")
    public void userStatJobHandler() throws Exception {
        SohuUserDayReportBo bo = new SohuUserDayReportBo();
        Date yesterday = DateUtil.yesterday().toJdkDate();
        bo.setDate(DateUtils.dateTime(yesterday));
        bo.setYear(String.valueOf(DateUtil.year(yesterday)));
        bo.setMonth(String.valueOf(DateUtil.month(yesterday) + 1));
        bo.setDay(String.valueOf(DateUtil.dayOfMonth(yesterday)));
        bo.setQuarter(String.valueOf(DateUtil.quarter(yesterday)));
        Date startTime = DateUtil.beginOfDay(yesterday).toJdkDate();
        Date endTime = DateUtil.endOfDay(yesterday).toJdkDate();
        //当天新增用户数
        Long newUserNum = remoteUserService.getUserNumByCreateTime(startTime, endTime);
        bo.setNewUserNum(newUserNum);
        //当天访问人数
        //Long newVisitNum = remoteMiddleAirecBehaviorService.getNewExposeUserNumByCreateTime(startTime,endTime);
        Long newVisitNum = remoteSysLogininforService.getUserNumByCreateTime(startTime, endTime);
        bo.setVisitNum(newVisitNum);
        //当天分享人数
        Long newShareNum = remoteMiddleAirecBehaviorService.getNewShareUserNumByCreateTime(startTime, endTime);
        bo.setShareNum(newShareNum);
        //当天付费人数
        Long newPayNum = remoteMiddleTradeRecordService.getNewPayUserNumByCreateTime(startTime, endTime);
        bo.setPayNum(newPayNum);
        remoteUserDayReportService.saveByBo(bo);
        // default success
    }

    @XxlJob(value = "userBehaviorStatJobHandler", init = "init", destroy = "destroy")
    public void userBehaviorStatJobHandler() throws Exception {
        Date yesterday = DateUtil.yesterday().toJdkDate();
        Date startTime = DateUtil.beginOfDay(yesterday).toJdkDate();
        Date endTime = DateUtil.endOfDay(yesterday).toJdkDate();
        List<UserBehaviorGroupInfoVo> groupList = remoteUserBehaviorService.list("HSS", Arrays.asList("view_click", "page_view"), startTime, endTime);
        if (CollUtil.isNotEmpty(groupList)){
            List<SohuUserViewReportBo> bos = groupList.stream().map(vo -> {
                SohuUserViewReportBo bo = new SohuUserViewReportBo();
                bo.setUserId(vo.getUserId());
                bo.setViewCount(vo.getViewCount());
                bo.setBusinessType(vo.getBusinessType());
                bo.setRecordDate(DateUtils.dateTime(yesterday));
                return bo;
            }).collect(Collectors.toList());
            remoteUserReportService.userBehaviorBatchSave(bos);
        }
    }

    public void init() {
        log.info("UserStatJobService定时任务启动");
        XxlJobHelper.log("UserStatJobService定时任务启动");
    }

    public void destroy() {
        log.info("UserStatJobService定时任务结束");
        XxlJobHelper.log("UserStatJobService定时任务结束");
    }

}
