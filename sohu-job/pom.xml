<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sohu</groupId>
        <artifactId>sohu-dependency</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sohu-job</artifactId>

    <description>
        sohu-job 任务调度模块
    </description>

    <dependencies>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- sohu Common Log -->
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-dict</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-seata</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-job</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-security</artifactId>
        </dependency>

        <!-- sohu Api System -->
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-system</artifactId>
        </dependency>

        <!-- sohu Api Admin -->
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-admin</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-busy-order</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-shop-order</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-resource</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-im</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-middle</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-pay</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zwztf.sdk</groupId>
            <artifactId>migu-novel</artifactId>
            <version>1.0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-novel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-shop-goods</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zwztf.sdk</groupId>
            <artifactId>songshu-novel</artifactId>
            <version>1.0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-focus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-report</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
