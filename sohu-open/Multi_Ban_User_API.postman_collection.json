{"info": {"name": "多种封禁类型用户管理接口", "description": "支持多种封禁类型的用户管理和封禁相关接口集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:9288", "type": "string"}], "item": [{"name": "用户管理模块", "item": [{"name": "获取所有用户列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/user/all_user_list?pageNum=1&pageSize=10&keyword=&status=0", "host": ["{{baseUrl}}"], "path": ["api", "user", "all_user_list"], "query": [{"key": "pageNum", "value": "1", "description": "页码"}, {"key": "pageSize", "value": "10", "description": "每页大小"}, {"key": "keyword", "value": "", "description": "搜索关键词"}, {"key": "status", "value": "0", "description": "用户状态"}]}}}, {"name": "更新用户信息", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": 1,\n  \"userName\": \"testuser\",\n  \"nickName\": \"测试用户\",\n  \"phoneNumber\": \"***********\",\n  \"email\": \"<EMAIL>\",\n  \"sex\": \"1\",\n  \"avatar\": \"http://example.com/avatar.jpg\",\n  \"status\": \"0\",\n  \"remark\": \"用户备注\"\n}"}, "url": {"raw": "{{baseUrl}}/api/user/update_user", "host": ["{{baseUrl}}"], "path": ["api", "user", "update_user"]}}}, {"name": "用户封禁 - 多种类型同时封禁", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operationType\": \"BAN_ACCOUNT\",\n  \"userId\": 123,\n  \"reason\": \"严重违规行为\",\n  \"banTypes\": [\"ACCOUNT\", \"IP\", \"DEVICE\"],\n  \"ip\": \"*************\",\n  \"device\": \"DEVICE123456789\",\n  \"durationDescription\": \"30天\",\n  \"disableTime\": \"2024-12-31 23:59:59\"\n}"}, "url": {"raw": "{{baseUrl}}/api/user/user_ban", "host": ["{{baseUrl}}"], "path": ["api", "user", "user_ban"]}}}, {"name": "用户封禁 - 仅账号封禁", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operationType\": \"BAN_ACCOUNT\",\n  \"userId\": 124,\n  \"reason\": \"违规行为\",\n  \"banTypes\": [\"ACCOUNT\"],\n  \"durationDescription\": \"7天\"\n}"}, "url": {"raw": "{{baseUrl}}/api/user/user_ban", "host": ["{{baseUrl}}"], "path": ["api", "user", "user_ban"]}}}, {"name": "用户封禁 - 仅IP封禁", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operationType\": \"BAN_ACCOUNT\",\n  \"userId\": 125,\n  \"reason\": \"恶意攻击\",\n  \"banTypes\": [\"IP\"],\n  \"ip\": \"*************\",\n  \"durationDescription\": \"永封\"\n}"}, "url": {"raw": "{{baseUrl}}/api/user/user_ban", "host": ["{{baseUrl}}"], "path": ["api", "user", "user_ban"]}}}, {"name": "用户封禁 - 兼容旧版本", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operationType\": \"BAN_ACCOUNT\",\n  \"userId\": 126,\n  \"reason\": \"违规行为\",\n  \"type\": \"ACCOUNT\",\n  \"durationDescription\": \"30天\",\n  \"disableTime\": \"2024-12-31 23:59:59\"\n}"}, "url": {"raw": "{{baseUrl}}/api/user/user_ban", "host": ["{{baseUrl}}"], "path": ["api", "user", "user_ban"]}}}, {"name": "用户解封 - 多种类型", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operationType\": \"UNBAN_ACCOUNT\",\n  \"userId\": 123,\n  \"reason\": \"申诉成功\",\n  \"banTypes\": [\"ACCOUNT\", \"IP\", \"DEVICE\"],\n  \"ip\": \"*************\",\n  \"device\": \"DEVICE123456789\"\n}"}, "url": {"raw": "{{baseUrl}}/api/user/user_ban", "host": ["{{baseUrl}}"], "path": ["api", "user", "user_ban"]}}}, {"name": "用户解封 - 解封所有类型", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operationType\": \"UNBAN_ACCOUNT\",\n  \"userId\": 123,\n  \"reason\": \"申诉成功\"\n}"}, "url": {"raw": "{{baseUrl}}/api/user/user_ban", "host": ["{{baseUrl}}"], "path": ["api", "user", "user_ban"]}}}, {"name": "用户封禁列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/user/user_ban_list?pageNum=1&pageSize=10&userId=123&banType=&status=active", "host": ["{{baseUrl}}"], "path": ["api", "user", "user_ban_list"], "query": [{"key": "pageNum", "value": "1", "description": "页码"}, {"key": "pageSize", "value": "10", "description": "每页大小"}, {"key": "userId", "value": "123", "description": "用户ID"}, {"key": "banType", "value": "", "description": "封禁类型: account/ip/device"}, {"key": "status", "value": "active", "description": "封禁状态: active/lifted"}]}}}]}, {"name": "用户封禁模块", "item": [{"name": "获取所有用户列表(封禁模块)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/user_ban/all_user_list?pageNum=1&pageSize=10&keyword=", "host": ["{{baseUrl}}"], "path": ["api", "user_ban", "all_user_list"], "query": [{"key": "pageNum", "value": "1", "description": "页码"}, {"key": "pageSize", "value": "10", "description": "每页大小"}, {"key": "keyword", "value": "", "description": "搜索关键词"}]}}}, {"name": "执行封禁操作 - 多设备封禁", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operationType\": \"BAN_ACCOUNT\",\n  \"userId\": 456,\n  \"reason\": \"批量违规\",\n  \"banTypes\": [\"ACCOUNT\", \"DEVICE\"],\n  \"device\": \"DEVICE987654321\",\n  \"durationDescription\": \"7天\",\n  \"disableTime\": \"2024-12-25 23:59:59\"\n}"}, "url": {"raw": "{{baseUrl}}/api/user_ban/ban", "host": ["{{baseUrl}}"], "path": ["api", "user_ban", "ban"]}}}, {"name": "封禁记录列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/user_ban/user_ban_list?pageNum=1&pageSize=10&banType=device", "host": ["{{baseUrl}}"], "path": ["api", "user_ban", "user_ban_list"], "query": [{"key": "pageNum", "value": "1", "description": "页码"}, {"key": "pageSize", "value": "10", "description": "每页大小"}, {"key": "banType", "value": "device", "description": "封禁类型筛选"}]}}}]}]}