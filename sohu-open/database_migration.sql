-- 多种封禁类型数据库迁移脚本
-- 执行前请备份数据库

-- 1. 为封禁记录表添加IP和设备字段
ALTER TABLE sohu_ban_records 
ADD COLUMN ban_ip VARCHAR(45) COMMENT '被封禁的IP地址 (当封禁类型为IP时使用)' AFTER last_operation_datetime,
ADD COLUMN ban_device VARCHAR(255) COMMENT '被封禁的设备号 (当封禁类型为DEVICE时使用)' AFTER ban_ip;

-- 2. 为IP和设备字段添加索引以提高查询性能
CREATE INDEX idx_ban_ip ON sohu_ban_records(ban_ip, status);
CREATE INDEX idx_ban_device ON sohu_ban_records(ban_device, status);
CREATE INDEX idx_ban_type_status ON sohu_ban_records(ban_type, status);

-- 3. 更新现有数据的封禁类型（如果需要）
-- 将现有的封禁记录的封禁类型标准化
UPDATE sohu_ban_records 
SET ban_type = CASE 
    WHEN ban_type = 'IP' THEN 'ip'
    WHEN ban_type = 'ACCOUNT' THEN 'account'
    WHEN ban_type = 'DEVICE' THEN 'device'
    ELSE LOWER(ban_type)
END
WHERE ban_type IS NOT NULL;

-- 4. 创建封禁类型枚举约束（可选）
-- ALTER TABLE sohu_ban_records 
-- ADD CONSTRAINT chk_ban_type 
-- CHECK (ban_type IN ('account', 'ip', 'device'));

-- 5. 验证数据完整性
-- 检查是否有IP封禁记录缺少IP地址
SELECT COUNT(*) as ip_ban_without_ip 
FROM sohu_ban_records 
WHERE ban_type = 'ip' AND (ban_ip IS NULL OR ban_ip = '');

-- 检查是否有设备封禁记录缺少设备号
SELECT COUNT(*) as device_ban_without_device 
FROM sohu_ban_records 
WHERE ban_type = 'device' AND (ban_device IS NULL OR ban_device = '');

-- 6. 创建视图以便于查询不同类型的封禁记录
CREATE OR REPLACE VIEW v_active_bans AS
SELECT 
    id,
    user_id,
    ban_type,
    ban_ip,
    ban_device,
    duration_description,
    ban_datetime,
    expected_end_datetime,
    ban_reason,
    ban_operator_id,
    create_time
FROM sohu_ban_records 
WHERE status = 'active'
ORDER BY create_time DESC;

-- 7. 创建按类型分组的统计视图
CREATE OR REPLACE VIEW v_ban_statistics AS
SELECT 
    ban_type,
    status,
    COUNT(*) as count,
    MIN(ban_datetime) as earliest_ban,
    MAX(ban_datetime) as latest_ban
FROM sohu_ban_records 
GROUP BY ban_type, status;

-- 8. 示例查询 - 查找某用户的所有活跃封禁
-- SELECT * FROM v_active_bans WHERE user_id = 123;

-- 9. 示例查询 - 查找某IP的封禁记录
-- SELECT * FROM sohu_ban_records WHERE ban_ip = '*************' AND status = 'active';

-- 10. 示例查询 - 查找某设备的封禁记录
-- SELECT * FROM sohu_ban_records WHERE ban_device = 'DEVICE123456' AND status = 'active';

-- 迁移完成后的验证步骤：
-- 1. 确认新字段已添加：DESCRIBE sohu_ban_records;
-- 2. 确认索引已创建：SHOW INDEX FROM sohu_ban_records;
-- 3. 测试新的封禁功能
-- 4. 验证查询性能
