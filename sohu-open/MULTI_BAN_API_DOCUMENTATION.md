# 多种封禁类型用户管理接口文档

## 概述
本文档描述了支持多种封禁类型的用户管理接口，包括账号封禁、IP封禁和设备封禁。

## 新增功能特性

### 1. 多种封禁类型支持
- **账号封禁 (ACCOUNT)**: 封禁用户账号，用户无法登录
- **IP封禁 (IP)**: 封禁IP地址，该IP无法访问系统
- **设备封禁 (DEVICE)**: 封禁设备号，该设备无法访问系统

### 2. 同时封禁多种类型
支持在一次请求中同时对用户进行多种类型的封禁，例如同时封禁账号、IP和设备。

## 接口详情

### 用户封禁接口

#### 接口地址
`POST /api/user/user_ban`

#### 请求参数

**多种封禁类型示例**:
```json
{
  "operationType": "BAN_ACCOUNT",
  "userId": 123,
  "reason": "严重违规行为",
  "banTypes": ["ACCOUNT", "IP", "DEVICE"],
  "ip": "*************",
  "device": "DEVICE123456789",
  "durationDescription": "30天",
  "disableTime": "2024-12-31 23:59:59"
}
```

**单一封禁类型示例 (兼容旧版本)**:
```json
{
  "operationType": "BAN_ACCOUNT",
  "userId": 123,
  "reason": "违规行为",
  "type": "ACCOUNT",
  "durationDescription": "7天"
}
```

#### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| operationType | String | 是 | 操作类型，固定值 "BAN_ACCOUNT" |
| userId | Long | 是 | 被封禁的用户ID |
| reason | String | 否 | 封禁原因 |
| banTypes | Array | 否 | 封禁类型数组，支持 ["ACCOUNT", "IP", "DEVICE"] |
| type | String | 否 | 单一封禁类型，兼容旧版本 |
| ip | String | 条件必填 | 当banTypes包含"IP"时必填 |
| device | String | 条件必填 | 当banTypes包含"DEVICE"时必填 |
| durationDescription | String | 是 | 封禁时长描述，如"永封"、"30天"、"1年" |
| disableTime | String | 否 | 封禁结束时间 |

#### 封禁类型详细说明

1. **ACCOUNT (账号封禁)**
   - 封禁用户账号，用户无法登录系统
   - 不需要额外参数

2. **IP (IP地址封禁)**
   - 封禁指定IP地址
   - 需要提供 `ip` 参数
   - 该IP地址的所有访问都会被拒绝

3. **DEVICE (设备封禁)**
   - 封禁指定设备
   - 需要提供 `device` 参数
   - 该设备的所有访问都会被拒绝

#### 响应示例

**成功响应**:
```json
{
  "code": 200,
  "msg": "多类型封禁用户成功",
  "data": true
}
```

**失败响应**:
```json
{
  "code": 500,
  "msg": "用户账号已经处于封禁状态，无法重复封禁",
  "data": false
}
```

### 用户解封接口

#### 接口地址
`POST /api/user/user_ban` (operationType: "UNBAN_ACCOUNT")

#### 请求参数

**多种类型解封示例**:
```json
{
  "operationType": "UNBAN_ACCOUNT",
  "userId": 123,
  "reason": "申诉成功",
  "banTypes": ["ACCOUNT", "IP", "DEVICE"],
  "ip": "*************",
  "device": "DEVICE123456789"
}
```

**解封所有类型示例**:
```json
{
  "operationType": "UNBAN_ACCOUNT",
  "userId": 123,
  "reason": "申诉成功"
}
```

注意：如果不指定 `banTypes`，系统会自动解封该用户的所有活跃封禁记录。

## 封禁记录查询

### 接口地址
`GET /api/user/user_ban_list`

### 查询参数
- `pageNum`: 页码
- `pageSize`: 每页大小
- `userId`: 用户ID
- `banType`: 封禁类型 (account/ip/device)
- `status`: 封禁状态 (active/lifted)

### 响应示例
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 3,
    "rows": [
      {
        "id": 1,
        "userId": 123,
        "banType": "account",
        "durationDescription": "30天",
        "banDatetime": "2024-01-01 10:00:00",
        "expectedEndDatetime": "2024-01-31 10:00:00",
        "status": "active",
        "banReason": "违规行为"
      },
      {
        "id": 2,
        "userId": 123,
        "banType": "ip",
        "banIp": "*************",
        "durationDescription": "30天",
        "banDatetime": "2024-01-01 10:00:00",
        "expectedEndDatetime": "2024-01-31 10:00:00",
        "status": "active",
        "banReason": "违规行为"
      },
      {
        "id": 3,
        "userId": 123,
        "banType": "device",
        "banDevice": "DEVICE123456789",
        "durationDescription": "30天",
        "banDatetime": "2024-01-01 10:00:00",
        "expectedEndDatetime": "2024-01-31 10:00:00",
        "status": "active",
        "banReason": "违规行为"
      }
    ]
  }
}
```

## 数据库变更

为支持多种封禁类型，需要在 `sohu_ban_records` 表中添加以下字段：

```sql
ALTER TABLE sohu_ban_records 
ADD COLUMN ban_ip VARCHAR(45) COMMENT '被封禁的IP地址 (当封禁类型为IP时使用)',
ADD COLUMN ban_device VARCHAR(255) COMMENT '被封禁的设备号 (当封禁类型为DEVICE时使用)';
```

## 注意事项

1. **向后兼容**: 新版本完全兼容旧版本的单一封禁类型接口
2. **参数验证**: 当指定IP或DEVICE封禁时，必须提供对应的ip或device参数
3. **重复封禁检查**: 系统会检查每种类型是否已经被封禁，避免重复封禁
4. **自动解封**: 支持定时任务自动解封到期的封禁记录
5. **缓存更新**: 只有账号封禁会更新用户状态缓存，IP和设备封禁有独立的检查机制

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误，如重复封禁、参数缺失等 |

## 使用建议

1. **批量封禁**: 对于严重违规用户，建议同时使用多种封禁类型
2. **IP封禁**: 适用于恶意攻击或批量注册的情况
3. **设备封禁**: 适用于移动端应用的设备级别控制
4. **账号封禁**: 最常用的封禁方式，适用于一般违规行为
