package com.sohu.open.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sohu.middle.api.bo.SohuAdInfoByPlaceQueryBo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 广告控制器测试类
 */
@SpringBootTest
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class AdControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testGetAdListByPlaceCode() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 测试GET请求获取广告列表
        mockMvc.perform(get("/api/ad/list/APP-TWXQ-MOUNT")
                .param("userId", "123")
                .param("orderBy", "exposure_asc")
                .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    public void testGetAdListByPlaceCodePost() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 构建请求参数
        SohuAdInfoByPlaceQueryBo bo = new SohuAdInfoByPlaceQueryBo();
        bo.setAdPlaceCode("APP-TWXQ-MOUNT");
        bo.setUserId(123L);
        bo.setOrderBy("exposure_asc");
        bo.setLimit(10);
        bo.setFilterViewed(true);

        String requestBody = objectMapper.writeValueAsString(bo);

        // 测试POST请求获取广告列表
        mockMvc.perform(post("/api/ad/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    public void testGetSimpleAdList() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 测试简化版本的广告列表获取
        mockMvc.perform(get("/api/ad/simple/APP-TWXQ-MOUNT")
                .param("userId", "123"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    public void testGetAdListWithInvalidPlaceCode() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 测试无效的广告位标识
        mockMvc.perform(get("/api/ad/list/INVALID-PLACE-CODE")
                .param("userId", "123"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data").isEmpty());
    }

    @Test
    public void testGetAdListWithDifferentOrderBy() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 测试不同的排序方式
        String[] orderByOptions = {"exposure_asc", "exposure_desc", "sort_index_asc", "sort_index_desc"};

        for (String orderBy : orderByOptions) {
            mockMvc.perform(get("/api/ad/list/APP-TWXQ-MOUNT")
                    .param("orderBy", orderBy)
                    .param("limit", "5"))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.code").value(200));
        }
    }

    @Test
    public void testGetAdListWithUuid() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 测试使用UUID（游客用户）
        mockMvc.perform(get("/api/ad/list/APP-TWXQ-MOUNT")
                .param("uuid", "test-uuid-123456")
                .param("filterViewed", "true"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    public void testGetAdListWithPort() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 测试指定端口类型
        mockMvc.perform(get("/api/ad/list/APP-TWXQ-MOUNT")
                .param("port", "android")
                .param("limit", "5"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    public void testGetAdListWithoutFilter() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 测试不过滤已看过的广告
        mockMvc.perform(get("/api/ad/list/APP-TWXQ-MOUNT")
                .param("userId", "123")
                .param("filterViewed", "false"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));
    }
}
