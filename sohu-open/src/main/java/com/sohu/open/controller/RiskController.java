package com.sohu.open.controller;

import com.sohu.common.core.domain.R;
import com.sohu.common.encrypt.annotation.SkipApiEncrypt;
import com.sohu.open.api.bo.SohuRiskCheckBo;
import com.sohu.open.api.vo.SohuRiskResultVo;
import com.sohu.open.service.ISohuRiskService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: leibo
 * @Date: 2025/6/17 10:26
 **/
@RequiredArgsConstructor
@RestController
@RequestMapping("/risk")
public class RiskController {

    private final ISohuRiskService sohuRiskService;

    @Operation(summary = "获取用户微信信息",description = "负责人：汪伟 获取用户微信信息")
    @PostMapping("/check")
    @SkipApiEncrypt
    public R<SohuRiskResultVo> check(@RequestBody SohuRiskCheckBo checkBo) {
      return R.ok(sohuRiskService.check(checkBo));
    }
}
