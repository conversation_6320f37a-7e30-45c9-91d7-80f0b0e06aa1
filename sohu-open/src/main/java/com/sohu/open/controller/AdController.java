package com.sohu.open.controller;

import com.sohu.common.core.domain.R;
import com.sohu.middle.api.RemoteMiddleAdInfoService;
import com.sohu.middle.api.bo.SohuAdInfoByPlaceQueryBo;
import com.sohu.middle.api.vo.SohuAdInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 广告相关接口
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/ad")
@Tag(name = "广告管理", description = "广告相关接口")
public class AdController {

    @DubboReference
    private RemoteMiddleAdInfoService remoteMiddleAdInfoService;

    /**
     * 根据广告位标识获取广告列表
     */
    @Operation(summary = "根据广告位标识获取广告列表", description = "按曝光数升序排序，过滤用户已看过的广告")
    @GetMapping("/list/{adPlaceCode}")
    public R<List<SohuAdInfoVo>> getAdListByPlaceCode(
            @Parameter(description = "广告位标识", example = "APP-TWXQ-MOUNT", required = true)
            @PathVariable @NotBlank(message = "广告位标识不能为空") String adPlaceCode,
            
            @Parameter(description = "用户ID", example = "123")
            @RequestParam(required = false) Long userId,
            
            @Parameter(description = "设备UUID", example = "uuid-123456")
            @RequestParam(required = false) String uuid,
            
            @Parameter(description = "排序方式", example = "exposure_asc")
            @RequestParam(defaultValue = "exposure_asc") String orderBy,
            
            @Parameter(description = "返回数量限制", example = "10")
            @RequestParam(defaultValue = "10") Integer limit,
            
            @Parameter(description = "是否过滤已看过的广告", example = "true")
            @RequestParam(defaultValue = "true") Boolean filterViewed,
            
            @Parameter(description = "端口类型", example = "android")
            @RequestParam(required = false) String port) {
        
        try {
            log.info("获取广告列表请求，adPlaceCode: {}, userId: {}, uuid: {}, orderBy: {}, limit: {}", 
                    adPlaceCode, userId, uuid, orderBy, limit);
            
            // 构建查询参数
            SohuAdInfoByPlaceQueryBo bo = new SohuAdInfoByPlaceQueryBo();
            bo.setAdPlaceCode(adPlaceCode);
            bo.setUserId(userId);
            bo.setUuid(uuid);
            bo.setOrderBy(orderBy);
            bo.setLimit(limit);
            bo.setFilterViewed(filterViewed);
            bo.setPort(port);
            
            // 调用服务获取广告列表
            List<SohuAdInfoVo> adList = remoteMiddleAdInfoService.getAdListByPlaceCode(bo);
            
            log.info("获取广告列表成功，adPlaceCode: {}, 返回数量: {}", adPlaceCode, adList.size());
            return R.ok(adList);
            
        } catch (Exception e) {
            log.error("获取广告列表失败，adPlaceCode: {}", adPlaceCode, e);
            return R.fail("获取广告列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据广告位标识获取广告列表（POST方式）
     */
    @Operation(summary = "根据广告位标识获取广告列表（POST）", description = "按曝光数升序排序，过滤用户已看过的广告")
    @PostMapping("/list")
    public R<List<SohuAdInfoVo>> getAdListByPlaceCodePost(@Valid @RequestBody SohuAdInfoByPlaceQueryBo bo) {
        try {
            log.info("获取广告列表请求（POST），参数: {}", bo);
            
            // 调用服务获取广告列表
            List<SohuAdInfoVo> adList = remoteMiddleAdInfoService.getAdListByPlaceCode(bo);
            
            log.info("获取广告列表成功，adPlaceCode: {}, 返回数量: {}", bo.getAdPlaceCode(), adList.size());
            return R.ok(adList);
            
        } catch (Exception e) {
            log.error("获取广告列表失败，adPlaceCode: {}", bo.getAdPlaceCode(), e);
            return R.fail("获取广告列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定广告位的广告（简化版本）
     */
    @Operation(summary = "获取指定广告位的广告（简化版本）", description = "只需要传递广告位标识，其他参数使用默认值")
    @GetMapping("/simple/{adPlaceCode}")
    public R<List<SohuAdInfoVo>> getSimpleAdList(
            @Parameter(description = "广告位标识", example = "APP-TWXQ-MOUNT", required = true)
            @PathVariable @NotBlank(message = "广告位标识不能为空") String adPlaceCode,
            
            @Parameter(description = "用户ID", example = "123")
            @RequestParam(required = false) Long userId,
            
            @Parameter(description = "设备UUID", example = "uuid-123456")
            @RequestParam(required = false) String uuid) {
        
        try {
            log.info("获取简化广告列表请求，adPlaceCode: {}, userId: {}, uuid: {}", adPlaceCode, userId, uuid);
            
            // 构建查询参数（使用默认值）
            SohuAdInfoByPlaceQueryBo bo = new SohuAdInfoByPlaceQueryBo();
            bo.setAdPlaceCode(adPlaceCode);
            bo.setUserId(userId);
            bo.setUuid(uuid);
            bo.setOrderBy("exposure_asc"); // 默认按曝光数升序
            bo.setLimit(10); // 默认返回10条
            bo.setFilterViewed(true); // 默认过滤已看过的
            
            // 调用服务获取广告列表
            List<SohuAdInfoVo> adList = remoteMiddleAdInfoService.getAdListByPlaceCode(bo);
            
            log.info("获取简化广告列表成功，adPlaceCode: {}, 返回数量: {}", adPlaceCode, adList.size());
            return R.ok(adList);
            
        } catch (Exception e) {
            log.error("获取简化广告列表失败，adPlaceCode: {}", adPlaceCode, e);
            return R.fail("获取广告列表失败: " + e.getMessage());
        }
    }
}
