package com.sohu.open.service.impl;

import com.sohu.middle.api.bo.risk.RiskSyncCheckBo;
import com.sohu.middle.api.enums.DetectTypeEnum;
import com.sohu.middle.api.service.RemoteRiskService;
import com.sohu.open.api.bo.SohuRiskCheckBo;
import com.sohu.open.api.vo.SohuRiskResultVo;
import com.sohu.open.service.ISohuRiskService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * @Author: leibo
 * @Date: 2025/6/18 16:46
 **/
@RequiredArgsConstructor
@Service
public class SohuRiskServiceImpl implements ISohuRiskService {

    @DubboReference
    private RemoteRiskService remoteRiskService;

    @Override
    public SohuRiskResultVo check(SohuRiskCheckBo checkBo) {
        RiskSyncCheckBo riskSyncCheckBo = new RiskSyncCheckBo();
        riskSyncCheckBo.setPlatform(checkBo.getPlatform());
        riskSyncCheckBo.setBusyCode(checkBo.getProcessId());
        riskSyncCheckBo.setBusyType("Other");
        riskSyncCheckBo.setContent(checkBo.getValue());
        Integer detectType = DetectTypeEnum.TEXT.getCode();
        String fieldName = DetectTypeEnum.TEXT.getDesc();
        if (checkBo.getType() == 2) {
            detectType = DetectTypeEnum.IMAGE.getCode();
            fieldName = DetectTypeEnum.IMAGE.getDesc();
        }
        riskSyncCheckBo.setDetectType(detectType);
        riskSyncCheckBo.setFieldName(fieldName);
        Boolean isPass = remoteRiskService.syncCheck(riskSyncCheckBo);
        SohuRiskResultVo riskResultVo = new SohuRiskResultVo();
        riskResultVo.setProcessId(checkBo.getProcessId());
        if (isPass) {
            riskResultVo.setStatus("PASS");
            riskResultVo.setProcess("PASS");
        } else {
            riskResultVo.setStatus("NO_PASS");
            riskResultVo.setProcess("NO_PASS");
            String reason = "内容不合规";
            if (checkBo.getType() == 2) {
                reason = "图片或链接不合规";
            }
            riskResultVo.setReason(reason);
        }
        return riskResultVo;
    }
}
