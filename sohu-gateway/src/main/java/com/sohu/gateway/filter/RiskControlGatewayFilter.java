package com.sohu.gateway.filter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.sohu.common.core.constant.BanConstants;
import com.sohu.common.core.utils.CalUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
@Component
public class RiskControlGatewayFilter extends AbstractGatewayFilterFactory<Object> {

    private static final String REDIS_KEY_PREFIX = "ban:user:";

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public GatewayFilter apply(Object config) {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();

            String authorization = request.getHeaders().getFirst("Authorization");
            String token = extractToken(authorization);
            if (!StringUtils.hasText(token)) {
                return chain.filter(exchange); // 未登录用户不拦截
            }

            Long userId = extractUserIdFromToken(token);
            if (userId == null) {
                return chain.filter(exchange);
            }

            String key = REDIS_KEY_PREFIX + userId;
            String json = redisTemplate.opsForValue().get(key);

            if (StringUtils.hasText(json)) {
                /**
                 * json格式 ：
                 * {
                 *   "isBanned": true,
                 *   "banReason": "涉嫌违规内容",
                 *   "banTime": "2025-12-31T23:59:59",
                 *   "banPolicyId": 12345
                 * }
                 */
                JSONObject banInfo = JSON.parseObject(json);
                boolean banned = banInfo.getBooleanValue("isBanned");
                String reason = banInfo.getString("banReason");

                // 从数组结构中获取实际值
                Long banPolicyId = extractLongFromArray(banInfo.getJSONArray("banPolicyId"));
                String endTime = extractStringFromArray(banInfo.getJSONArray("expectedEndTime"));

                if (banned) {
                    if (!CalUtils.isNullOrZero(banPolicyId)) {
                        String policyKey = BanConstants.BAN_POLICY + banPolicyId;
                        List<String> interfaceListJson = redisTemplate.opsForList().range(policyKey, 0, -1);

                        if (CollUtil.isNotEmpty(interfaceListJson)) {
                            String path = request.getPath().toString();
                            String method = request.getMethodValue();

                            boolean hit = interfaceListJson.stream().anyMatch(jsonStr -> {
                                JSONObject obj = JSON.parseObject(jsonStr);
                                String url = obj.getString("interfaceUrl");
                                String httpMethod = obj.getString("httpMethod");
                                return path.equals(url)
                                        && ("ALL".equalsIgnoreCase(httpMethod) || method.equalsIgnoreCase(httpMethod));
                            });

                            if (hit) {
                                log.warn("用户 [{}] 命中封禁策略接口 [{} {}]，策略ID [{}]，拒绝访问", userId, method, path, banPolicyId);
                                String msg = "封禁接口受限，限制操作";
                                return forbiddenResponse(500, exchange, reason, endTime, msg);
                            }
                        }
                    }
                    // 如果不是策略接口匹配，普通封禁拦截
                    //log.warn("用户 [{}] 已被封禁， {}", userId, json);
                    //return forbiddenResponse(exchange, reason, endTime);
                    return chain.filter(exchange);
                }
            }

            return chain.filter(exchange);
        };
    }

    private Long extractLongFromArray(JSONArray arr) {
        if (arr != null && arr.size() >= 2) {
            try {
                return ((Number) arr.get(1)).longValue();
            } catch (Exception ignored) {
            }
        }
        return null;
    }

    private String extractStringFromArray(JSONArray arr) {
        if (arr != null && arr.size() >= 2) {
            return String.valueOf(arr.get(1));
        }
        return null;
    }

    /**
     * Bearer token 去掉前缀
     */
    private String extractToken(String authorization) {
        if (!StringUtils.hasText(authorization)) {
            return null;
        }
        if (authorization.startsWith("Bearer ")) {
            return authorization.substring(7);
        }
        return authorization;
    }

    /**
     * 解析 userId 从 Redis token-session 信息中
     */
    private Long extractUserIdFromToken(String token) {
        String userRedis = redisTemplate.opsForValue().get("Authorization:login:token-session:" + token);
        if (StrUtil.isBlank(userRedis)) {
            return null;
        }

        try {
            Object userObject = JSONUtil.parseObj(userRedis).getByPath("dataMap.loginUser.userId[1]");
            if (userObject != null) {
                return Long.valueOf(userObject.toString());
            }
        } catch (Exception e) {
            log.error("解析 userId 失败，token: {}, 异常: {}", token, e.getMessage());
        }

        return null;
    }

    /**
     * 返回 403 响应
     */
    private Mono<Void> forbiddenResponse(int errorCode, ServerWebExchange exchange, String message, String endTime, String msg) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.FORBIDDEN);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");

        JSONObject result = new JSONObject();
        result.put("code", errorCode);
        String finalMsg = StrUtil.isNotBlank(msg) ? msg : String.format("账号封禁：%s，解封时间：%s", message, endTime);
        result.put("msg", finalMsg);

        byte[] bytes = result.toJSONString().getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = response.bufferFactory().wrap(bytes);
        return response.writeWith(Mono.just(buffer));
    }
}
