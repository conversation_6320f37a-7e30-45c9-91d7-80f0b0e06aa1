package com.sohu.report.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 商单用户统计对象 sohu_user_task_report
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_user_task_report")
public class SohuUserTaskReport extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 发单量
     */
    private Long dispatchCount;
    /**
     * 接单量
     */
    private Long acceptCount;
    /**
     * 统计日期
     */
    private String recordDate;

}
