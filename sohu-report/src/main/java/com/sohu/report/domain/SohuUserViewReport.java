package com.sohu.report.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户浏览量统计对象 sohu_user_view_report
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_user_view_report")
public class SohuUserViewReport extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 浏览次数
     */
    private Long viewCount;
    /**
     * 业务类型  Article 图文  Video 视频  Question 问答 ShortPlay 短剧  BusyOrder  商单 Novel 小说  Game 游戏 Literature 诗歌散文 Other 其他
     */
    private String businessType;
    /**
     * 统计日期
     */
    private String recordDate;

}
