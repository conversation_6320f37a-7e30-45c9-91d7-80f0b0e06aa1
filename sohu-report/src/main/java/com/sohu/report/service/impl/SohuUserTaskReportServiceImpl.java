package com.sohu.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.report.api.bo.SohuUserTaskReportBo;
import com.sohu.report.api.vo.SohuUserTaskReportVo;
import com.sohu.report.domain.SohuUserTaskReport;
import com.sohu.report.mapper.SohuUserTaskReportMapper;
import com.sohu.report.service.ISohuUserTaskReportService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 商单用户统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@RequiredArgsConstructor
@Service
public class SohuUserTaskReportServiceImpl implements ISohuUserTaskReportService {

    private final SohuUserTaskReportMapper baseMapper;

    /**
     * 查询商单用户统计列表
     */
    @Override
    public TableDataInfo<SohuUserTaskReportVo> queryPageList(SohuUserTaskReportBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuUserTaskReport> lqw = buildQueryWrapper(bo);
        Page<SohuUserTaskReportVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询商单用户统计列表
     */
    @Override
    public List<SohuUserTaskReportVo> queryList(SohuUserTaskReportBo bo) {
        LambdaQueryWrapper<SohuUserTaskReport> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuUserTaskReport> buildQueryWrapper(SohuUserTaskReportBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuUserTaskReport> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuUserTaskReport::getUserId, bo.getUserId());
        lqw.eq(bo.getDispatchCount() != null, SohuUserTaskReport::getDispatchCount, bo.getDispatchCount());
        lqw.eq(bo.getAcceptCount() != null, SohuUserTaskReport::getAcceptCount, bo.getAcceptCount());
        lqw.eq(StringUtils.isNotBlank(bo.getRecordDate()), SohuUserTaskReport::getRecordDate, bo.getRecordDate());
        return lqw;
    }

    /**
     * 新增商单用户统计
     */
    @Override
    public Boolean insertByBo(SohuUserTaskReportBo bo) {
        SohuUserTaskReport add = BeanUtil.toBean(bo, SohuUserTaskReport.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商单用户统计
     */
    @Override
    public Boolean updateByBo(SohuUserTaskReportBo bo) {
        SohuUserTaskReport update = BeanUtil.toBean(bo, SohuUserTaskReport.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean batchInsert(List<SohuUserTaskReportBo> bos) {
        return baseMapper.insertBatch(BeanUtil.copyToList(bos, SohuUserTaskReport.class));
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuUserTaskReport entity){
        //TODO 做一些数据校验,如唯一约束
    }


}
