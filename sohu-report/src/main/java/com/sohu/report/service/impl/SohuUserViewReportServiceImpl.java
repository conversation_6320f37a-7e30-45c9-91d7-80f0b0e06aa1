package com.sohu.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.report.api.bo.SohuUserViewReportBo;
import com.sohu.report.api.vo.SohuUserViewReportVo;
import com.sohu.report.domain.SohuUserViewReport;
import com.sohu.report.mapper.SohuUserViewReportMapper;
import com.sohu.report.service.ISohuUserViewReportService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 用户浏览量统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@RequiredArgsConstructor
@Service
public class SohuUserViewReportServiceImpl implements ISohuUserViewReportService {

    private final SohuUserViewReportMapper baseMapper;


    /**
     * 查询用户浏览量统计列表
     */
    @Override
    public TableDataInfo<SohuUserViewReportVo> queryPageList(SohuUserViewReportBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuUserViewReport> lqw = buildQueryWrapper(bo);
        Page<SohuUserViewReportVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询用户浏览量统计列表
     */
    @Override
    public List<SohuUserViewReportVo> queryList(SohuUserViewReportBo bo) {
        LambdaQueryWrapper<SohuUserViewReport> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuUserViewReport> buildQueryWrapper(SohuUserViewReportBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuUserViewReport> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuUserViewReport::getUserId, bo.getUserId());
        lqw.eq(bo.getViewCount() != null, SohuUserViewReport::getViewCount, bo.getViewCount());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessType()), SohuUserViewReport::getBusinessType, bo.getBusinessType());
        lqw.eq(StringUtils.isNotBlank(bo.getRecordDate()), SohuUserViewReport::getRecordDate, bo.getRecordDate());
        return lqw;
    }

    /**
     * 新增用户浏览量统计
     */
    @Override
    public Boolean insertByBo(SohuUserViewReportBo bo) {
        SohuUserViewReport add = BeanUtil.toBean(bo, SohuUserViewReport.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户浏览量统计
     */
    @Override
    public Boolean updateByBo(SohuUserViewReportBo bo) {
        SohuUserViewReport update = BeanUtil.toBean(bo, SohuUserViewReport.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean batchInsert(List<SohuUserViewReportBo> list) {
        return baseMapper.insertBatch(BeanUtil.copyToList(list, SohuUserViewReport.class));
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuUserViewReport entity){
        //TODO 做一些数据校验,如唯一约束
    }
}
