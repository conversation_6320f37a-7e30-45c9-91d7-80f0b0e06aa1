package com.sohu.report.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.report.api.bo.SohuUserTaskReportBo;
import com.sohu.report.api.bo.SohuUserViewReportBo;
import com.sohu.report.api.vo.SohuUserViewReportVo;

import java.util.List;

/**
 * 用户浏览量统计Service接口
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface ISohuUserViewReportService {


    /**
     * 查询用户浏览量统计列表
     */
    TableDataInfo<SohuUserViewReportVo> queryPageList(SohuUserViewReportBo bo, PageQuery pageQuery);

    /**
     * 查询用户浏览量统计列表
     */
    List<SohuUserViewReportVo> queryList(SohuUserViewReportBo bo);

    /**
     * 修改用户浏览量统计
     */
    Boolean insertByBo(SohuUserViewReportBo bo);

    /**
     * 修改用户浏览量统计
     */
    Boolean updateByBo(SohuUserViewReportBo bo);

    /**
     * 批量插入用户浏览量统计
     * @param list
     * @return
     */
    Boolean batchInsert(List<SohuUserViewReportBo>  list);

}
