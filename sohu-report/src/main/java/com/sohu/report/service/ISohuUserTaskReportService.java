package com.sohu.report.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.report.api.bo.SohuUserTaskReportBo;
import com.sohu.report.api.vo.SohuUserTaskReportVo;

import java.util.List;

/**
 * 商单用户统计Service接口
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface ISohuUserTaskReportService {

    /**
     * 查询商单用户统计列表
     */
    TableDataInfo<SohuUserTaskReportVo> queryPageList(SohuUserTaskReportBo bo, PageQuery pageQuery);

    /**
     * 查询商单用户统计列表
     */
    List<SohuUserTaskReportVo> queryList(SohuUserTaskReportBo bo);

    /**
     * 修改商单用户统计
     */
    Boolean insertByBo(SohuUserTaskReportBo bo);

    /**
     * 修改商单用户统计
     */
    Boolean updateByBo(SohuUserTaskReportBo bo);

    /**
     * 批量保存用户任务数据
     * @param bos
     */
    Boolean batchInsert(List<SohuUserTaskReportBo> bos);
}
