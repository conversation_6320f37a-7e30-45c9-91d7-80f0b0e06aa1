<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.admin.mapper.ThirdPartyBrandMapper">

    <resultMap type="com.sohu.admin.domain.ThirdPartyBrand" id="ThirdPartyBrandResult">
        <result property="id" column="id"/>
        <result property="channel" column="channel"/>
        <result property="name" column="name"/>
        <result property="logoUrl" column="logo_url"/>
        <result property="relatedCategoryIds" column="related_category_ids"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
