<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.admin.mapper.SohuBanPolicyMapper">

    <resultMap type="com.sohu.admin.domain.SohuBanPolicy" id="SohuBanPolicyResult">
        <result property="id" column="id"/>
        <result property="policyName" column="policy_name"/>
        <result property="policyLevel" column="policy_level"/>
        <result property="policyType" column="policy_type"/>
        <result property="isGlobal" column="is_global"/>
        <result property="description" column="description"/>
        <result property="activeStatus" column="active_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
