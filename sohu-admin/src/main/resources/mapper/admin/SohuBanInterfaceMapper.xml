<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.admin.mapper.SohuBanInterfaceMapper">

    <resultMap type="com.sohu.admin.domain.SohuBanInterface" id="SohuBanInterfaceResult">
        <result property="id" column="id"/>
        <result property="interfaceName" column="interface_name"/>
        <result property="interfaceUrl" column="interface_url"/>
        <result property="httpMethod" column="http_method"/>
        <result property="riskWeight" column="risk_weight"/>
        <result property="description" column="description"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="selectByPolicyId" resultType="com.sohu.admin.domain.SohuBanInterface"
            parameterType="java.lang.Long">
        SELECT i.*
        FROM sohu_policy_interface p
                 JOIN sohu_ban_interface i ON p.interface_id = i.id
        WHERE p.policy_id = #{policyId}
    </select>


</mapper>
