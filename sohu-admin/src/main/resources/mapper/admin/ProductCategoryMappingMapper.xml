<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.admin.mapper.ProductCategoryMappingMapper">

    <resultMap type="com.sohu.admin.domain.ProductCategoryMapping" id="ProductCategoryMappingResult">
        <result property="id" column="id"/>
        <result property="channel" column="channel"/>
        <result property="merId" column="mer_id"/>
        <result property="ourCategoryId" column="our_category_id"/>
        <result property="thirdPartyCategoryId" column="third_party_category_id"/>
        <result property="ourCategoryPath" column="our_category_path"/>
        <result property="thirdPartyCategoryPath" column="third_party_category_path"/>
        <result property="operator" column="operator"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
