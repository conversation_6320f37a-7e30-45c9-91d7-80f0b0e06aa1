<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.admin.mapper.SohuBanRecordsMapper">

    <resultMap type="com.sohu.admin.domain.SohuBanRecords" id="SohuBanRecordsResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="nickName" column="nick_name"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="ip" column="ip"/>
        <result property="device" column="device"/>
        <result property="banType" column="ban_type"/>
        <result property="durationDescription" column="duration_description"/>
        <result property="banDatetime" column="ban_datetime"/>
        <result property="expectedEndDatetime" column="expected_end_datetime"/>
        <result property="status" column="status"/>
        <result property="banReason" column="ban_reason"/>
        <result property="banOperatorId" column="ban_operator_id"/>
        <result property="unbanDatetime" column="unban_datetime"/>
        <result property="unbanReason" column="unban_reason"/>
        <result property="unbanOperatorId" column="unban_operator_id"/>
        <result property="lastOperationDatetime" column="last_operation_datetime"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="banPolicyId" column="ban_policy_id"/>
    </resultMap>


</mapper>
