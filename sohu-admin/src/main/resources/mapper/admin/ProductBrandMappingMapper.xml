<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.admin.mapper.ProductBrandMappingMapper">

    <resultMap type="com.sohu.admin.domain.ProductBrandMapping" id="ProductBrandMappingResult">
        <result property="id" column="id"/>
        <result property="channel" column="channel"/>
        <result property="ourBrandId" column="our_brand_id"/>
        <result property="thirdPartyBrandId" column="third_party_brand_id"/>
        <result property="ourBrandName" column="our_brand_name"/>
        <result property="thirdPartyBrandName" column="third_party_brand_name"/>
        <result property="operator" column="operator"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
