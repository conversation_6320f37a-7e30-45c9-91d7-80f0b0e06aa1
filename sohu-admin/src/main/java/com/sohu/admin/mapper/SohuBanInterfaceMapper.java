package com.sohu.admin.mapper;

import com.sohu.admin.api.vo.SohuBanInterfaceVo;
import com.sohu.admin.domain.SohuBanInterface;
import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 受控接口资源Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface SohuBanInterfaceMapper extends BaseMapperPlus<SohuBanInterfaceMapper, SohuBanInterface, SohuBanInterfaceVo> {

    /**
     * 根据策略ID查询受控接口列表
     *
     * @param policyId 策略ID
     * @return 受控接口列表
     */
    List<SohuBanInterface> selectByPolicyId(@Param("policyId") Long policyId);

}
