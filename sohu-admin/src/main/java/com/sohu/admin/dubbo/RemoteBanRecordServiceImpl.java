package com.sohu.admin.dubbo;

import com.sohu.admin.api.RemoteBanRecordService;
import com.sohu.admin.api.bo.SohuBanRecordsBo;
import com.sohu.admin.api.vo.SohuBanRecordsVo;
import com.sohu.admin.service.ISohuBanRecordsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteBanRecordServiceImpl implements RemoteBanRecordService {

    private final ISohuBanRecordsService sohuBanRecordsService;

    @Override
    public void insertBatch(List<SohuBanRecordsBo> banRecordsBos) {
        sohuBanRecordsService.insertByBoList(banRecordsBos);
    }

    @Override
    public Long insertRecord(SohuBanRecordsBo banRecord) {
        return sohuBanRecordsService.insertByBo(banRecord);
    }

    @Override
    public void updateBanRecordForUnban(SohuBanRecordsBo updateRecord) {
        sohuBanRecordsService.updateByBo(updateRecord);
    }

    @Override
    public SohuBanRecordsVo findActiveBanByUserId(Long userId, String status) {
        return sohuBanRecordsService.findActiveBanByUserId(userId, status);
    }

    @Override
    public SohuBanRecordsVo findBanRecordById(Long banRecordId) {
        return sohuBanRecordsService.queryById(banRecordId);
    }

    @Override
    public SohuBanRecordsVo findActiveBanByIp(String ip, String type) {
        return sohuBanRecordsService.findActiveBanByIp(ip, type);
    }

    @Override
    public SohuBanRecordsVo findActiveBanByDevice(String device, String type) {
        return sohuBanRecordsService.findActiveBanByDevice(device, type);
    }

    @Override
    public List<SohuBanRecordsVo> queryList(SohuBanRecordsBo bo) {
        return sohuBanRecordsService.queryList(bo);
    }
}
