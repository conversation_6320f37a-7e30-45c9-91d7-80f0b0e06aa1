package com.sohu.admin.dubbo;

import com.sohu.admin.api.RemoteThirdPartyBrandService;
import com.sohu.admin.api.bo.ThirdPartyBrandBo;
import com.sohu.admin.api.vo.ThirdPartyBrandVo;
import com.sohu.admin.service.IThirdPartyBrandService;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 三方平台商品品牌远程服务实现
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteThirdPartyBrandServiceImpl implements RemoteThirdPartyBrandService {

    private final IThirdPartyBrandService thirdPartyBrandService;

    @Override
    public ThirdPartyBrandVo queryById(Long id) {
        return thirdPartyBrandService.queryById(id);
    }

    @Override
    public TableDataInfo<ThirdPartyBrandVo> queryPageList(ThirdPartyBrandBo bo, PageQuery pageQuery) {
        return thirdPartyBrandService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<ThirdPartyBrandVo> queryList(ThirdPartyBrandBo bo) {
        return thirdPartyBrandService.queryList(bo);
    }

    @Override
    public Boolean insertByBo(ThirdPartyBrandBo bo) {
        return thirdPartyBrandService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(ThirdPartyBrandBo bo) {
        return thirdPartyBrandService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return thirdPartyBrandService.deleteWithValidByIds(ids, isValid);
    }
}
