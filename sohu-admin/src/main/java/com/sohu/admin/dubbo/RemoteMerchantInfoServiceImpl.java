package com.sohu.admin.dubbo;

import com.sohu.admin.api.RemoteMerchantInfoService;
import com.sohu.admin.api.model.SohuMerchantInfoModel;
import com.sohu.admin.service.ISohuMerchantInfoService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * 商户信息-dubbo狐少少Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteMerchantInfoServiceImpl implements RemoteMerchantInfoService {

    private final ISohuMerchantInfoService merchantInfoService;

    @Override
    public SohuMerchantInfoModel selectByMerId(Long merId) {
        return merchantInfoService.queryByMerId(merId);
    }

    @Override
    public void handleRobot(String busyCode, Boolean isPass, String reason) {
        // 基于机审结果进行后续处理
         merchantInfoService.handleRobot(busyCode, isPass, reason);
    }
}
