package com.sohu.admin.dubbo;

import com.sohu.admin.api.RemoteProductCategoryMappingService;
import com.sohu.admin.api.bo.ProductCategoryMappingBo;
import com.sohu.admin.api.vo.ProductCategoryMappingVo;
import com.sohu.admin.service.IProductCategoryMappingService;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 商品分类映射远程服务实现
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteProductCategoryMappingServiceImpl implements RemoteProductCategoryMappingService {

    private final IProductCategoryMappingService productCategoryMappingService;

    @Override
    public ProductCategoryMappingVo queryById(Long id) {
        return productCategoryMappingService.queryById(id);
    }

    @Override
    public TableDataInfo<ProductCategoryMappingVo> queryPageList(ProductCategoryMappingBo bo, PageQuery pageQuery) {
        return productCategoryMappingService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<ProductCategoryMappingVo> queryList(ProductCategoryMappingBo bo) {
        return productCategoryMappingService.queryList(bo);
    }

    @Override
    public Boolean insertByBo(ProductCategoryMappingBo bo) {
        return productCategoryMappingService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(ProductCategoryMappingBo bo) {
        return productCategoryMappingService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return productCategoryMappingService.deleteWithValidByIds(ids, isValid);
    }
}
