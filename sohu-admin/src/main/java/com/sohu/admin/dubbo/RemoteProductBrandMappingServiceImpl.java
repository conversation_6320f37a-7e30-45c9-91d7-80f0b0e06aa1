package com.sohu.admin.dubbo;

import com.sohu.admin.api.RemoteProductBrandMappingService;
import com.sohu.admin.api.bo.ProductBrandMappingBo;
import com.sohu.admin.api.vo.ProductBrandMappingVo;
import com.sohu.admin.service.IProductBrandMappingService;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 商品品牌映射远程服务实现
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteProductBrandMappingServiceImpl implements RemoteProductBrandMappingService {

    private final IProductBrandMappingService productBrandMappingService;

    @Override
    public ProductBrandMappingVo queryById(Long id) {
        return productBrandMappingService.queryById(id);
    }

    @Override
    public TableDataInfo<ProductBrandMappingVo> queryPageList(ProductBrandMappingBo bo, PageQuery pageQuery) {
        return productBrandMappingService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<ProductBrandMappingVo> queryList(ProductBrandMappingBo bo) {
        return productBrandMappingService.queryList(bo);
    }

    @Override
    public Boolean insertByBo(ProductBrandMappingBo bo) {
        return productBrandMappingService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(ProductBrandMappingBo bo) {
        return productBrandMappingService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return productBrandMappingService.deleteWithValidByIds(ids, isValid);
    }
}
