package com.sohu.admin.dubbo;

import com.sohu.admin.api.RemoteThirdPartyCategoryService;
import com.sohu.admin.api.bo.ThirdPartyCategoryBo;
import com.sohu.admin.api.vo.ThirdPartyCategoryVo;
import com.sohu.admin.service.IThirdPartyCategoryService;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 三方平台商品分类远程服务实现
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteThirdPartyCategoryServiceImpl implements RemoteThirdPartyCategoryService {

    private final IThirdPartyCategoryService thirdPartyCategoryService;

    @Override
    public ThirdPartyCategoryVo queryById(Long id) {
        return thirdPartyCategoryService.queryById(id);
    }

    @Override
    public TableDataInfo<ThirdPartyCategoryVo> queryPageList(ThirdPartyCategoryBo bo, PageQuery pageQuery) {
        return thirdPartyCategoryService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<ThirdPartyCategoryVo> queryList(ThirdPartyCategoryBo bo) {
        return thirdPartyCategoryService.queryList(bo);
    }

    @Override
    public Boolean insertByBo(ThirdPartyCategoryBo bo) {
        return thirdPartyCategoryService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(ThirdPartyCategoryBo bo) {
        return thirdPartyCategoryService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return thirdPartyCategoryService.deleteWithValidByIds(ids, isValid);
    }
}
