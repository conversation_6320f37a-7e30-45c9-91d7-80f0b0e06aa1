package com.sohu.admin.dubbo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.bo.SohuMerchantCateBo;
import com.sohu.admin.api.bo.SohuMerchantEnableBo;
import com.sohu.admin.api.bo.SohuMerchantSettledBo;
import com.sohu.admin.api.bo.playlet.PlayletMerchantSearchBo;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.admin.api.vo.*;
import com.sohu.admin.api.vo.playlet.PlayletMerchantApplyListVo;
import com.sohu.admin.domain.SohuMerchant;
import com.sohu.admin.domain.SohuMerchantCategory;
import com.sohu.admin.service.*;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.vo.NodeVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuMerchantBo;
import com.sohu.middle.api.bo.SohuMerchantSearchBo;
import com.sohu.middle.api.enums.AuditState;
import com.sohu.middle.api.vo.SohuMerchantVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商户服务
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteMerchantServiceImpl implements RemoteMerchantService {

    private final ISohuMerchantService sohuMerchantService;
    private final ISohuMerchantCategoryService sohuMerchantCategoryService;
    private final SohuMerchantSettledService sohuMerchantSettledService;

    private final ISohuMerchantClassificationService sohuMerchantClassificationService;

    private final ISohuMerchantBrandService sohuMerchantBrandService;

    @Override
    public Boolean insertByBo(SohuMerchantModel bo) {
        return sohuMerchantService.insertByBo(BeanCopyUtils.copy(bo, SohuMerchantBo.class));
    }

    @Override
    public SohuMerchantModel selectById(Long id) {
        SohuMerchantVo vo = sohuMerchantService.queryById(id);
        SohuMerchantModel sohuMerchantModel = new SohuMerchantModel();
        BeanUtils.copyProperties(vo, sohuMerchantModel);
        return sohuMerchantModel;
    }

    @Override
    public List<SohuMerchantModel> selectByUserId(Long userId) {
        SohuMerchantBo model = new SohuMerchantBo();
        model.setUserId(userId);
        List<SohuMerchantVo> vo = sohuMerchantService.queryList(model);
        List<SohuMerchantModel> merchantModelList = Lists.newArrayList();
        for (SohuMerchantVo sohuMerchantVo : vo) {
            SohuMerchantModel sohuMerchantModel = new SohuMerchantModel();
            BeanUtils.copyProperties(sohuMerchantVo, sohuMerchantModel);
            merchantModelList.add(sohuMerchantModel);
        }
        Set<Long> categoryIdSet = merchantModelList.stream().map(p -> p.getCategoryId()).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(categoryIdSet)) {
            return merchantModelList;
        }
        Map<Long, SohuMerchantCategory> categoryMap = sohuMerchantCategoryService.getMapByIds(categoryIdSet);
        merchantModelList.forEach(merchant -> {
            SohuMerchantCategory category = categoryMap.get(merchant.getCategoryId());
            if (Objects.nonNull(category)) {
                merchant.setCategoryName(category.getName());
            }
        });
        return merchantModelList;
    }

    @Override
    public List<SohuMerchantModel> queryList(String name) {
        SohuMerchantBo model = new SohuMerchantBo();
        if (StrUtil.isNotBlank(name)) {
            model.setName(name);
        }
        model.setAuditStatus(CommonState.Pass.getCode());
        model.setIsDel(false);
        model.setIsSwitch(true);
        List<SohuMerchantVo> vo = sohuMerchantService.queryList(model);
        List<SohuMerchantModel> merchantModelList = Lists.newArrayList();
        for (SohuMerchantVo sohuMerchantVo : vo) {
            SohuMerchantModel sohuMerchantModel = new SohuMerchantModel();
            BeanUtils.copyProperties(sohuMerchantVo, sohuMerchantModel);
            merchantModelList.add(sohuMerchantModel);
        }
        return merchantModelList;
    }

    @Override
    public SohuMerchantModel selectByUserIdAndCitySiteId(Long userId, Long siteId) {
        SohuMerchantVo vo = sohuMerchantService.selectByUserIdAndCitySiteId(userId, siteId);
        return BeanCopyUtils.copy(vo, SohuMerchantModel.class);
    }

    @Override
    public List<SohuMerchantModel> selectByUserIdAndSiteId(Long userId, Long siteId, Boolean flag) {
        return sohuMerchantService.selectByUserIdAndSiteId(userId, siteId, flag);
    }

    @Override
    public Map<Long, SohuMerchantModel> getMerIdMapByIdList(Collection<Long> merIdList) {
        List<SohuMerchant> sohuMerchants = sohuMerchantService.getListByIds(merIdList);
        List<SohuMerchantModel> sohuMerchantModels = BeanCopyUtils.copyList(sohuMerchants, SohuMerchantModel.class);
        if (CollectionUtils.isEmpty(sohuMerchantModels)) {
            return new HashMap<>();
        }
        Set<Long> categoryIdSet = sohuMerchantModels.stream().map(p -> p.getCategoryId()).collect(Collectors.toSet());
        Map<Long, SohuMerchantCategory> categoryMap = sohuMerchantCategoryService.getMapByIds(categoryIdSet);
        Map<Long, SohuMerchantModel> merchantMap = new HashMap<>();
        sohuMerchantModels.forEach(merchant -> {
            SohuMerchantCategory category = categoryMap.get(merchant.getCategoryId());
            if (Objects.nonNull(category)) {
                merchant.setCategoryName(category.getName());
            }
            merchantMap.put(merchant.getId(), merchant);
        });
        return merchantMap;
    }

    @Override
    public TableDataInfo<SohuMerchantModel> pageBySiteId(String keyword, String sort, Long siteId, Long citySiteId, PageQuery pageQuery) {
        SohuMerchantSearchBo bo = new SohuMerchantSearchBo();
        bo.setSiteId(siteId);
        bo.setCitySiteId(citySiteId);
        bo.setKeywords(keyword);
        bo.setSaleNumsSort(sort);
        bo.setIsSwitch(Boolean.TRUE);
        TableDataInfo<SohuMerchantVo> info = sohuMerchantService.getPageList(bo, pageQuery);
        List<SohuMerchantModel> modelList = BeanCopyUtils.copyList(info.getData(), SohuMerchantModel.class);
        return new TableDataInfo<>(modelList, info.getTotal());
    }

    @Override
    public SohuMerchantModel getInfo(Long id) {
        SohuMerchantVo info = sohuMerchantService.getInfo(id);
        return BeanCopyUtils.copy(info, SohuMerchantModel.class);
    }

    @Override
    public SohuMerchantModel getByPhone(String phone) {
        SohuMerchantVo info = sohuMerchantService.getByPhone(phone);
        return BeanCopyUtils.copy(info, SohuMerchantModel.class);
    }

    @Override
    public Boolean updateBatch(List<SohuMerchantModel> merchantModelList) {
        List<SohuMerchant> merchants = BeanCopyUtils.copyList(merchantModelList, SohuMerchant.class);
        return sohuMerchantService.updateBatch(merchants);
    }

    @Override
    public List<SohuMerchantModel> selectByIds(Collection<Long> merIds) {
        List<SohuMerchant> merchants = sohuMerchantService.getListByIds(merIds);
        List<SohuMerchantModel> modelList = BeanCopyUtils.copyList(merchants, SohuMerchantModel.class);
        return modelList;
    }

    @Override
    public TableDataInfo<PlayletMerchantApplyListVo> getMerchantApplyList(PlayletMerchantSearchBo bo, PageQuery pageQuery) {
        bo.setAuditStatus(AuditState.Pass.name());
        pageQuery.setIsAsc("desc");
        pageQuery.setOrderByColumn("sale_nums");
        return sohuMerchantService.getMerchantApplyList(bo, pageQuery, false);
    }

    @Override
    public List<SohuMerchantModel> getMerchantOpenList(String sysSource) {
        List<SohuMerchantVo> merchantCloseList = sohuMerchantService.getMerchantOpenList(sysSource);
        return BeanCopyUtils.copyList(merchantCloseList, SohuMerchantModel.class);
    }

    @Override
    public List<SohuMerchantModel> getProductMerchantList(String sysSource) {
        List<SohuMerchantVo> productMerchantList = sohuMerchantService.getProductMerchantList(sysSource);
        return BeanCopyUtils.copyList(productMerchantList, SohuMerchantModel.class);
    }

    @Override
    public Boolean settled(SohuMerchantSettledBo bo) {
        return sohuMerchantSettledService.settled(bo);
    }

    @Override
    public Boolean settledEdit(SohuMerchantSettledBo bo) {
        return sohuMerchantSettledService.settledEdit(bo, false);
    }

    @Override
    public SohuMerchantSettledVo detail(Long id) {
        return sohuMerchantSettledService.detail(id);
    }

    @Override
    public SohuUserCondVo getUserCond(Long userId) {
        return sohuMerchantSettledService.getUserCond(userId);
    }

    @Override
    public Boolean upgrage(SohuMerchantSettledBo upgradeBo) {
        return sohuMerchantSettledService.upgrage(upgradeBo);
    }

    @Override
    public Boolean updateMerchantStatus(Long userId, Long merId, String status, String reason) {
        return sohuMerchantSettledService.updateMerchantStatus(userId, merId, status, reason);
    }

    @Override
    public Boolean addClass(SohuMerchantCateBo bo) {
        return sohuMerchantSettledService.addClass(bo);
    }

    @Override
    public Boolean addBrand(SohuMerchantCateBo bo) {
        return sohuMerchantSettledService.addBrand(bo);
    }

    @Override
    public Boolean close(Long id) {
        return sohuMerchantSettledService.close(id);
    }

    @Override
    public List<SohuMerchantBondVo> bondList(Long id) {
        return sohuMerchantSettledService.bondList(id);
    }

    @Override
    public List<NodeVo> getCateList(Long id) {
        return sohuMerchantClassificationService.getCateList(id);
    }

    @Override
    public List<SohuMerchantBrandVo> getBrandList(Long id) {
        return sohuMerchantBrandService.getBrandList(id);
    }

    @Override
    public Boolean enableClass(SohuMerchantEnableBo bo) {
        return sohuMerchantClassificationService.enableClass(bo);
    }

    @Override
    public Boolean enableBrand(SohuMerchantEnableBo bo) {
        return sohuMerchantBrandService.enableBrand(bo);
    }

    @Override
    public SohuMerchantQualificationInfoVo getMerchantQualificationInfo(Long merId) {
        return sohuMerchantService.getMerchantQualificationInfo(merId);
    }

    @Override
    public List<SohuMerchantModel> selectByStatus(List<String> status) {
        List<SohuMerchantVo> merchantCloseList = sohuMerchantService.selectByStatus(status);
        return BeanCopyUtils.copyList(merchantCloseList, SohuMerchantModel.class);
    }

    @Override
    public Boolean existMerchantName(String merchantName, Long merchantId) {
        return sohuMerchantService.existMerchantName(merchantName, merchantId);
    }

    @Override
    public Boolean merchantUpgradeHandler(Long userId) {
        return sohuMerchantSettledService.merchantUpgradeHandler(userId);
    }

    @Override
    public List<Long> getCateIdByPass(Long merchantId) {
        return sohuMerchantClassificationService.getCateIdByPass(merchantId);
    }

    @Override
    public List<Long> getBrandIdByPass(Long merchantId, Long cateId) {
        return sohuMerchantBrandService.getBrandIdByPass(merchantId, cateId);
    }

    @Override
    public SohuMerchantVo getInfoById(Long id) {
        return sohuMerchantService.getInfo(id);
    }

    @Override
    public void handleRobot(String busyCode, Boolean isPass, String reason) {
        // 基于机审结果进行后续处理
        sohuMerchantService.handleRobot(busyCode, isPass, reason);
    }

    @Override
    public Map<Long, SohuMerchantModel> selectMapByIds(Collection<Long> merIds) {
        List<SohuMerchantModel> list = selectByIds(merIds);
        return CollUtil.isEmpty(list) ? new HashMap<>() : list.stream().collect(Collectors.toMap(SohuMerchantModel::getId, u -> u));
    }

}