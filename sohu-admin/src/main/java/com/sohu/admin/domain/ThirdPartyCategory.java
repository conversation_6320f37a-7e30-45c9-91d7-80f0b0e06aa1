package com.sohu.admin.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 三方平台商品分类对象 third_party_category
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("third_party_category")
public class ThirdPartyCategory extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * 三方分类ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 三方父分类ID
     */
    private Long parentId;
    /**
     * 三方分类名称
     */
    private String name;
    /**
     * 层级 (1, 2, 3)
     */
    private Long level;
    /**
     * 分类全路径
     */
    private String fullPath;
    /**
     * 是否叶子节点 (level=3的节点)
     */
    private Integer isLeaf;

}
