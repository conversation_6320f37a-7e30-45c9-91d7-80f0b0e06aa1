package com.sohu.admin.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * [多渠道]三方平台商品品牌对象 third_party_brand
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("third_party_brand")
public class ThirdPartyBrand extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * 三方品牌ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 渠道标识 (如: ZHENXIN, PLATFORM_B)
     */
    @TableId(value = "channel")
    private String channel;
    /**
     * 三方品牌名称
     */
    private String name;
    /**
     * 品牌logo的URL (对应thumbnail_img)
     */
    private String logoUrl;
    /**
     * 关联的三方分类ID列表 (以逗号分隔的字符串)
     */
    private String relatedCategoryIds;
    /**
     * 是否被逻辑删除
     */
    private Integer isDeleted;

}
