package com.sohu.admin.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * [多渠道]商品品牌映射对象 product_brand_mapping
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_brand_mapping")
public class ProductBrandMapping extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 渠道标识
     */
    private String channel;
    /**
     * 我方品牌ID (sohu_product_brand.id)
     */
    private Long ourBrandId;
    /**
     * 三方品牌ID (third_party_brand.id)
     */
    private Long thirdPartyBrandId;
    /**
     * 
     */
    private String ourBrandName;
    /**
     * 
     */
    private String thirdPartyBrandName;
    /**
     * 最后操作人
     */
    private Long operator;

}
