package com.sohu.admin.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 风控策略核心对象 sohu_ban_policy
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_ban_policy")
public class SohuBanPolicy extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 策略ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 策略名称（例如：轻度限制策略）
     */
    private String policyName;
    /**
     * 策略级别（1-10级，数字越大限制越严）
     */
    private Long policyLevel;
    /**
     * 策略应用类型
     */
    private String policyType;
    /**
     * 是否全局策略（0：否 1：是）
     */
    private Integer isGlobal;
    /**
     * 策略详细描述
     */
    private String description;
    /**
     * 策略状态
     */
    private String activeStatus;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

}
