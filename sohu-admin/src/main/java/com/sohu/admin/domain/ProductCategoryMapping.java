package com.sohu.admin.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 商品分类映射对象 product_category_mapping
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("product_category_mapping")
public class ProductCategoryMapping extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 渠道标识 (如: YOUXUAN, A, B)
     */
    private String channel;
    /**
     * 我方商户Id
     */
    private String merId;
    /**
     * 我方二级分类ID (sohu_product_category.id)
     */
    private Long ourCategoryId;
    /**
     * 三方三级分类ID (third_party_category.id)
     */
    private Long thirdPartyCategoryId;
    /**
     * 我方分类路径，如: 服装/男装T恤
     */
    private String ourCategoryPath;
    /**
     * 三方分类路径，如: 服饰穿搭/男装/T恤
     */
    private String thirdPartyCategoryPath;
    /**
     * 最后操作人
     */
    private Long operator;

}
