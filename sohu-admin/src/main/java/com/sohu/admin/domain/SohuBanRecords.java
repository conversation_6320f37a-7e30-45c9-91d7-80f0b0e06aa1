package com.sohu.admin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 封禁记录对象 sohu_ban_records
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_ban_records")
public class SohuBanRecords extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 封禁记录的唯一ID，自增主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 被封禁用户的ID (关联用户表)
     */
    private Long userId;
    /**
     * 被封禁的IP地址
     */
    private String ip;
    /**
     * 被封禁的设备标识 (例如: 设备ID, 设备指纹)
     */
    private String device;
    /**
     * 封禁类型 (例如: IP, 设备, ACCOUNT账号)
     */
    private String banType;
    /**
     * 封禁时长描述 (例如: 永封, 1年, 30天)
     */
    private String durationDescription;
    /**
     * 封禁执行/开始的时间戳
     */
    private Date banDatetime;
    /**
     * 封禁预计自然结束的时间戳 (永久封禁时为NULL)
     */
    private Date expectedEndDatetime;
    /**
     * 当前封禁状态 (ACTIVE:封禁中, LIFTED:已解封-手动, EXPIRED:已解封-到期)
     */
    private String status;
    /**
     * 执行封禁的原因 (例如: 涉嫌诈骗)
     */
    private String banReason;
    /**
     * 执行封禁的管理员ID/用户名或系统标识
     */
    private Long banOperatorId;
    /**
     * 解封的时间戳
     */
    private Date unbanDatetime;
    /**
     * 解封的原因 (例如: 到期自动解封, 已整改)
     */
    private String unbanReason;
    /**
     * 执行解封的管理员ID/用户名或系统/自动标识
     */
    private Long unbanOperatorId;
    /**
     * 此记录最后一次重要操作的时间戳 (封禁或解封时间)
     */
    private Date lastOperationDatetime;
    /**
     * 用户昵称
     */
    private String nickName;
    /**
     * 用户手机
     */
    private String phoneNumber;
    /**
     * 封禁策略ID (sohu_ban_policy主键ID)
     */
    private Long banPolicyId;

}
