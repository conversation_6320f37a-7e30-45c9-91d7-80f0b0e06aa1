package com.sohu.admin.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 策略与接口关联对象 sohu_policy_interface
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@TableName("sohu_policy_interface")
public class SohuPolicyInterface implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private String id;
    /**
     * sohu_ban_policy表ID
     */
    private String policyId;
    /**
     * sohu_policy_interface表ID
     */
    private String interfaceId;
    /**
     * 限制类型
     */
    private String restrictionType;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

}
