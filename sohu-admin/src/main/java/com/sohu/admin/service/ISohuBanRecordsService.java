package com.sohu.admin.service;


import com.sohu.admin.api.bo.SohuBanRecordsBo;
import com.sohu.admin.api.vo.SohuBanRecordsVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 封禁记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface ISohuBanRecordsService {

    /**
     * 查询封禁记录
     */
    SohuBanRecordsVo queryById(Long id);

    /**
     * 查询封禁记录列表
     */
    TableDataInfo<SohuBanRecordsVo> queryPageList(SohuBanRecordsBo bo, PageQuery pageQuery);

    /**
     * 查询封禁记录列表
     */
    List<SohuBanRecordsVo> queryList(SohuBanRecordsBo bo);

    /**
     * 修改封禁记录
     */
    Long insertByBo(SohuBanRecordsBo bo);

    /**
     * 修改封禁记录
     */
    Boolean updateByBo(SohuBanRecordsBo bo);

    /**
     * 校验并批量删除封禁记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量新增封禁记录
     */
    void insertByBoList(List<SohuBanRecordsBo> banRecordsBos);

    /**
     * 根据用户id和类型查询封禁记录
     *
     * @param userId 用户id
     * @param status 状态
     * @return {@link SohuBanRecordsBo}
     */
    SohuBanRecordsVo findActiveBanByUserId(Long userId, String status);

    /**
     * 根据封禁记录ID查询详情
     *
     * @param banRecordId 封禁记录ID
     * @return {@link SohuBanRecordsVo}
     */
    SohuBanRecordsVo findBanRecordById(Long banRecordId);

    /**
     * 根据ip和类型查询封禁记录
     *
     * @param ip   ip
     * @param type 类型
     * @return {@link SohuBanRecordsBo}
     */
    SohuBanRecordsVo findActiveBanByIp(String ip, String type);

    /**
     * 根据设备和类型查询封禁记录
     *
     * @param device 设备
     * @param type   类型
     * @return {@link SohuBanRecordsBo}
     */
    SohuBanRecordsVo findActiveBanByDevice(String device, String type);
}
