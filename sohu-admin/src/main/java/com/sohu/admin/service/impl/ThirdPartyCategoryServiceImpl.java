package com.sohu.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.bo.ThirdPartyCategoryBo;
import com.sohu.admin.api.vo.ThirdPartyCategoryVo;
import com.sohu.admin.domain.ThirdPartyCategory;
import com.sohu.admin.mapper.ThirdPartyCategoryMapper;
import com.sohu.admin.service.IThirdPartyCategoryService;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 三方平台商品分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RequiredArgsConstructor
@Service
public class ThirdPartyCategoryServiceImpl implements IThirdPartyCategoryService {

    private final ThirdPartyCategoryMapper baseMapper;

    /**
     * 查询三方平台商品分类
     */
    @Override
    public ThirdPartyCategoryVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询三方平台商品分类列表
     */
    @Override
    public TableDataInfo<ThirdPartyCategoryVo> queryPageList(ThirdPartyCategoryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ThirdPartyCategory> lqw = buildQueryWrapper(bo);
        Page<ThirdPartyCategoryVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询三方平台商品分类列表
     */
    @Override
    public List<ThirdPartyCategoryVo> queryList(ThirdPartyCategoryBo bo) {
        LambdaQueryWrapper<ThirdPartyCategory> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ThirdPartyCategory> buildQueryWrapper(ThirdPartyCategoryBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ThirdPartyCategory> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getParentId() != null, ThirdPartyCategory::getParentId, bo.getParentId());
        lqw.like(StringUtils.isNotBlank(bo.getName()), ThirdPartyCategory::getName, bo.getName());
        lqw.eq(bo.getLevel() != null, ThirdPartyCategory::getLevel, bo.getLevel());
        lqw.eq(StringUtils.isNotBlank(bo.getFullPath()), ThirdPartyCategory::getFullPath, bo.getFullPath());
        lqw.eq(bo.getIsLeaf() != null, ThirdPartyCategory::getIsLeaf, bo.getIsLeaf());
        return lqw;
    }

    /**
     * 新增三方平台商品分类
     */
    @Override
    public Boolean insertByBo(ThirdPartyCategoryBo bo) {
        ThirdPartyCategory add = BeanUtil.toBean(bo, ThirdPartyCategory.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改三方平台商品分类
     */
    @Override
    public Boolean updateByBo(ThirdPartyCategoryBo bo) {
        ThirdPartyCategory update = BeanUtil.toBean(bo, ThirdPartyCategory.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ThirdPartyCategory entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除三方平台商品分类
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
