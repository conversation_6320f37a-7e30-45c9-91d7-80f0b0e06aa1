package com.sohu.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.bo.SohuMerchantAuditBo;
import com.sohu.admin.api.bo.SohuMerchantCloseStoreQueryBo;
import com.sohu.admin.api.bo.playlet.PlayletAuditMerchantBo;
import com.sohu.admin.api.bo.playlet.PlayletMerchantRemarkBo;
import com.sohu.admin.api.bo.playlet.PlayletMerchantSearchBo;
import com.sohu.admin.api.bo.playlet.PlayletUpdateMerchantInfoBo;
import com.sohu.admin.api.model.SohuAccountModel;
import com.sohu.admin.api.model.SohuAuditLogModel;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.admin.api.vo.*;
import com.sohu.admin.api.vo.playlet.PlayletMerchantApplyListVo;
import com.sohu.admin.domain.*;
import com.sohu.admin.mapper.*;
import com.sohu.admin.service.ISohuMerchantAuditLogService;
import com.sohu.admin.service.ISohuMerchantService;
import com.sohu.admin.service.SohuMerchantSettledService;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuAuditLogBo;
import com.sohu.middle.api.bo.SohuMerchantApplyBo;
import com.sohu.middle.api.bo.SohuMerchantBo;
import com.sohu.middle.api.bo.SohuMerchantSearchBo;
import com.sohu.middle.api.bo.risk.RiskSyncCheckBo;
import com.sohu.middle.api.enums.AuditState;
import com.sohu.middle.api.enums.DetectTypeEnum;
import com.sohu.middle.api.enums.ProductTypeEnum;
import com.sohu.middle.api.service.RemoteMiddleSiteService;
import com.sohu.middle.api.service.RemoteMiddleUserCollectService;
import com.sohu.middle.api.service.RemoteRiskService;
import com.sohu.middle.api.service.RemoteSohuAuditLogService;
import com.sohu.middle.api.service.notice.RemoteMiddleSystemNoticeService;
import com.sohu.middle.api.vo.SohuAuditLogVo;
import com.sohu.middle.api.vo.SohuMerchantVo;
import com.sohu.middle.api.vo.SohuSiteVo;
import com.sohu.middle.api.vo.SohuUserCollectVo;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.pay.api.RemoteMerchantBondRefundService;
import com.sohu.pay.api.RemoteYmService;
import com.sohu.pay.api.bo.SohuAccountBo;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.resource.api.RemoteMailService;
import com.sohu.resource.api.RemoteSmsService;
import com.sohu.shopgoods.api.RemoteProductBrandService;
import com.sohu.shopgoods.api.RemoteProductCategoryPcService;
import com.sohu.shopgoods.api.RemoteProductPcService;
import com.sohu.shopgoods.api.RemoteProductService;
import com.sohu.shopgoods.api.domain.SohuProductPcReqNewBo;
import com.sohu.shopgoods.api.model.SohuProductModel;
import com.sohu.shopgoods.api.vo.SohuProductBrandVo;
import com.sohu.shopgoods.api.vo.SohuProductCategoryPcVo;
import com.sohu.system.api.RemotePlatformRoleService;
import com.sohu.system.api.RemoteSysRoleService;
import com.sohu.system.api.RemoteUserService;
import com.sohu.system.api.domain.SysRole;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商户-狐少少Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
@RequiredArgsConstructor
@Service
public class SohuMerchantServiceImpl implements ISohuMerchantService {

    private final SohuMerchantMapper baseMapper;
    private final SohuMerchantCategoryMapper sohuMerchantCategoryMapper;
    private final SohuMerchantTypeMapper sohuMerchantTypeMapper;
    private final TransactionTemplate transactionTemplate;
    private final SohuMerchantCategoryMapper.SohuMerchantInfoMapper sohuMerchantInfoMapper;
    private final SohuMerchantClassificationMapper sohuMerchantClassificationMapper;
    private final SohuMerchantBrandQualificationMapper sohuMerchantBrandQualificationMapper;
    private final SohuMerchantBrandMapper sohuMerchantBrandMapper;

    private final SohuMerchantSettledService sohuMerchantSettledService;

    private final ISohuMerchantAuditLogService sohuMerchantAuditLogService;

    private final AsyncConfig asyncConfig;
    @DubboReference
    private RemoteMiddleSiteService remoteMiddleSiteService;
    @DubboReference
    private RemoteProductService remoteProductService;
    @DubboReference
    private RemoteSysRoleService remoteSysRoleService;
    @DubboReference
    private RemoteMiddleUserCollectService remoteMiddleUserCollectService;
    @DubboReference
    private RemoteProductPcService remoteProductPcService;
    @DubboReference
    private RemoteSmsService remoteSmsService;
    @DubboReference
    private RemoteMailService remoteMailService;
    @DubboReference
    private RemotePlatformRoleService remotePlatformRoleService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteAccountService remoteAccountService;
    @DubboReference
    private RemoteMerchantBondRefundService remoteMerchantBondRefundService;
    @DubboReference
    private RemoteYmService remoteYmService;
    @DubboReference
    private RemoteProductBrandService remoteProductBrandService;
    @DubboReference
    private RemoteProductCategoryPcService remoteProductCategoryPcService;
    @DubboReference
    private RemoteSohuAuditLogService remoteSohuAuditLogService;
    @DubboReference
    private RemoteMiddleSystemNoticeService remoteMiddleSystemNoticeService;
    @DubboReference
    private RemoteRiskService remoteRiskService;

    /**
     * 查询商户-狐少少
     */
    @Override
    public SohuMerchantVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商户-狐少少列表
     */
    @Override
    public TableDataInfo<SohuMerchantVo> queryPageList(SohuMerchantBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuMerchant> lqw = buildQueryWrapper(bo);
        Page<SohuMerchantVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询商户-狐少少列表
     */
    @Override
    public List<SohuMerchantVo> queryList(SohuMerchantBo bo) {
        LambdaQueryWrapper<SohuMerchant> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuMerchant> buildQueryWrapper(SohuMerchantBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuMerchant> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), SohuMerchant::getName, bo.getName());
        lqw.eq(bo.getUserId() != null, SohuMerchant::getUserId, bo.getUserId());
        lqw.eq(bo.getCategoryId() != null, SohuMerchant::getCategoryId, bo.getCategoryId());
        lqw.eq(bo.getSiteId() != null, SohuMerchant::getSiteId, bo.getSiteId());
        lqw.eq(bo.getCitySiteId() != null, SohuMerchant::getCitySiteId, bo.getCitySiteId());
        lqw.eq(bo.getTypeId() != null, SohuMerchant::getTypeId, bo.getTypeId());
        lqw.like(StringUtils.isNotBlank(bo.getRealName()), SohuMerchant::getRealName, bo.getRealName());
        lqw.eq(StringUtils.isNotBlank(bo.getEmail()), SohuMerchant::getEmail, bo.getEmail());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), SohuMerchant::getPhone, bo.getPhone());
        lqw.eq(bo.getHandlingFee() != null, SohuMerchant::getHandlingFee, bo.getHandlingFee());
        lqw.eq(StringUtils.isNotBlank(bo.getKeywords()), SohuMerchant::getKeywords, bo.getKeywords());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), SohuMerchant::getAddress, bo.getAddress());
        lqw.eq(bo.getIsSelf() != null, SohuMerchant::getIsSelf, bo.getIsSelf());
        lqw.eq(bo.getIsRecommend() != null, SohuMerchant::getIsRecommend, bo.getIsRecommend());
        lqw.eq(bo.getIsSwitch() != null, SohuMerchant::getIsSwitch, bo.getIsSwitch());
        lqw.eq(StringUtils.isNotBlank(bo.getAuditStatus()), SohuMerchant::getAuditStatus, bo.getAuditStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getDenialReason()), SohuMerchant::getDenialReason, bo.getDenialReason());
        lqw.eq(bo.getAuditorId() != null, SohuMerchant::getAuditorId, bo.getAuditorId());
        lqw.eq(bo.getProductSwitch() != null, SohuMerchant::getProductSwitch, bo.getProductSwitch());
        lqw.eq(StringUtils.isNotBlank(bo.getSort()), SohuMerchant::getSort, bo.getSort());
        lqw.eq(StringUtils.isNotBlank(bo.getQualificationPicture()), SohuMerchant::getQualificationPicture, bo.getQualificationPicture());
        lqw.eq(StringUtils.isNotBlank(bo.getBackImage()), SohuMerchant::getBackImage, bo.getBackImage());
        lqw.eq(StringUtils.isNotBlank(bo.getAvatar()), SohuMerchant::getAvatar, bo.getAvatar());
        lqw.eq(StringUtils.isNotBlank(bo.getStreetBackImage()), SohuMerchant::getStreetBackImage, bo.getStreetBackImage());
        lqw.eq(StringUtils.isNotBlank(bo.getIntro()), SohuMerchant::getIntro, bo.getIntro());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateType()), SohuMerchant::getCreateType, bo.getCreateType());
        lqw.eq(StringUtils.isNotBlank(bo.getPcBanner()), SohuMerchant::getPcBanner, bo.getPcBanner());
        lqw.eq(StringUtils.isNotBlank(bo.getPcBackImage()), SohuMerchant::getPcBackImage, bo.getPcBackImage());
        lqw.eq(bo.getCopyProductNum() != null, SohuMerchant::getCopyProductNum, bo.getCopyProductNum());
        lqw.eq(bo.getBalance() != null, SohuMerchant::getBalance, bo.getBalance());
        lqw.eq(bo.getStarLevel() != null, SohuMerchant::getStarLevel, bo.getStarLevel());
        lqw.eq(bo.getIsDel() != null, SohuMerchant::getIsDel, bo.getIsDel());
        lqw.eq(StringUtils.isNotBlank(bo.getSysSource()), SohuMerchant::getSysSource, bo.getSysSource());
        return lqw;
    }

    /**
     * 新增商户-狐少少
     */
    @Override
    public Boolean insertByBo(SohuMerchantBo bo) {
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        bo.setUserId(userId);
        SohuMerchant merchant = BeanUtil.toBean(bo, SohuMerchant.class);
        validEntityBeforeSave(merchant);
        validateSmsCode(bo.getSmsCode(), bo.getPhone());
        if (merchant.getId() != null) {
            merchant.setAuditStatus(AuditState.WaitApprove.name());
            return baseMapper.updateById(merchant) > 0;
        } else {
            return baseMapper.insert(merchant) > 0;
        }
    }

    @Override
    public Boolean applyByBo(SohuMerchantApplyBo bo) {
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        bo.setUserId(userId);
        SohuMerchant merchant = BeanUtil.toBean(bo, SohuMerchant.class);
        validEntityBeforeSave(merchant);
        validateSmsCode(bo.getSmsCode(), bo.getPhone());
        if (merchant.getId() != null) {
            merchant.setAuditStatus(AuditState.WaitApprove.name());
            return baseMapper.updateById(merchant) > 0;
        } else {
            return baseMapper.insert(merchant) > 0;
        }
    }

    /**
     * 修改商户-狐少少
     */
    @Override
    public Boolean updateByBo(SohuMerchantBo bo) {
        SohuMerchant update = BeanUtil.toBean(bo, SohuMerchant.class);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean addAuditRemark(PlayletMerchantRemarkBo bo) {
        // TODO 修改安全性校验
        return baseMapper.updateById(BeanUtil.toBean(bo, SohuMerchant.class)) > 0;
    }

    /**
     * 校验验证码
     */
    private void validateSmsCode(String smsCode, String phone) {
        // 验证码校验
        if (smsCode != null && phone != null) {
            String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + phone);
            if (StringUtils.isBlank(code) || !code.equals(smsCode)) {
                throw new RuntimeException("输入的验证码有误");
            }
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuMerchant entity) {
        //商户名唯一校验
        LambdaQueryWrapper<SohuMerchant> query = Wrappers.lambdaQuery();
        query.eq(SohuMerchant::getName, entity.getName());
        if (entity.getId() != null) {
            query.ne(SohuMerchant::getId, entity.getId());
        }
        SohuMerchantVo sohuMerchant = baseMapper.selectVoOne(query);
        if (Objects.nonNull(sohuMerchant)) {
            throw new RuntimeException(MessageUtils.message("merchant.name.exists"));
        }
        //商户类型校验
        if (entity.getTypeId() != null) {
            SohuMerchantType sohuMerchantType = sohuMerchantTypeMapper.selectById(entity.getTypeId());
            if (Objects.isNull(sohuMerchantType)) {
                throw new RuntimeException(MessageUtils.message("merchant.type.not.exist"));
            }
        }
        //商户分类校验
        if (entity.getCategoryId() != null) {
            SohuMerchantCategory sohuMerchantCategory = sohuMerchantCategoryMapper.selectById(entity.getCategoryId());
            if (Objects.isNull(sohuMerchantCategory)) {
                throw new RuntimeException(MessageUtils.message("merchant.category.not.exist"));
            }
            entity.setHandlingFee(sohuMerchantCategory.getHandlingFee());
        }
        //城市站只有一个商户校验
        if (entity.getUserId() != null && entity.getCitySiteId() != null) {
            LambdaQueryWrapper<SohuMerchant> lqw = Wrappers.lambdaQuery();
            lqw.eq(SohuMerchant::getUserId, entity.getUserId());
            lqw.eq(SohuMerchant::getCitySiteId, entity.getCitySiteId());
            lqw.ne(SohuMerchant::getAuditStatus, AuditState.Refuse.name());
//            SohuMerchantVo vo = baseMapper.selectVoOne(lqw);
//            if (Objects.nonNull(vo)) {
//                throw new RuntimeException(MessageUtils.message("merchant.already.exists"));
//            }
            if (this.baseMapper.exists(lqw)) {
                throw new RuntimeException(MessageUtils.message("merchant.already.exists"));
            }
        }
    }

    /**
     * 批量删除商户-狐少少
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 获取用户商户信息
     */
    @Override
    public SohuMerchantVo getInfo(Long id) {
        //获取商户信息
        SohuMerchant merchant = baseMapper.selectById(id);
        if (Objects.isNull(merchant)) {
            throw new ServiceException("用户商户不存在");
        }
        SohuMerchantCategory merchantCategory = sohuMerchantCategoryMapper.selectById(merchant.getCategoryId());
        SohuMerchantType merchantType = sohuMerchantTypeMapper.selectById(merchant.getTypeId());
        SohuMerchantVo info = BeanUtil.toBean(merchant, SohuMerchantVo.class);
        info.setMerCategory(Objects.nonNull(merchantCategory) ? merchantCategory.getName() : null);
        info.setMerType(Objects.nonNull(merchantType) ? merchantType.getName() : "");
        //设置商户信息
        LambdaQueryWrapper<SohuMerchantInfo> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuMerchantInfo::getMerId, merchant.getId());
        info.setInfoVo(sohuMerchantInfoMapper.selectVoOne(lqw));
        return info;
    }

    /**
     * 商户端商户开关
     */
    @Override
    public Boolean updateSwitch() {
        Long userId = LoginHelper.getUserId();
        //获取商户信息
        SohuMerchant merchant = baseMapper.selectOne(SohuMerchant::getUserId, userId);
        if (Objects.isNull(merchant)) {
            throw new ServiceException("用户商户不存在");
        }
        openCheck(merchant);
        merchant.setIsSwitch(!merchant.getIsSwitch());
        return baseMapper.updateById(merchant) > 0;
    }

    /**
     * 修改用户信息
     */
    @Override
    public Boolean updateInfo(SohuMerchantBo bo) {
        //商户信息
        if (Objects.nonNull(bo.getInfoBo())) {
            SohuMerchantInfo info = BeanUtil.toBean(bo.getInfoBo(), SohuMerchantInfo.class);
            if (info.getId() != null) {
                sohuMerchantInfoMapper.updateById(info);
            } else {
                info.setMerId(bo.getId());
                sohuMerchantInfoMapper.insert(info);
            }
            if (StrUtil.isNotEmpty(info.getServiceLink())){
                // 发布异步机审消息
                RiskSyncCheckBo riskSyncCheckBo = new RiskSyncCheckBo();
                riskSyncCheckBo.setBusyType(BusyType.ShopInfo.getType());
                riskSyncCheckBo.setBusyCode(info.getId().toString());
                riskSyncCheckBo.setDetectType(DetectTypeEnum.Link.getCode());
                riskSyncCheckBo.setPlatform(Constants.SOHUGLOBAL);
                riskSyncCheckBo.setFieldName("店铺信息-客服H5链接");
                riskSyncCheckBo.setContent(info.getServiceLink());
                remoteRiskService.asyncCheck(riskSyncCheckBo);
            }
        }
        SohuMerchant update = BeanUtil.toBean(bo, SohuMerchant.class);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public TableDataInfo<SohuMerchantVo> getPageList(SohuMerchantSearchBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuMerchant> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSysSource()), SohuMerchant::getSysSource, bo.getSysSource());
        if (ObjectUtil.isNotNull(bo.getCategoryId())) {
            lqw.eq(SohuMerchant::getCategoryId, bo.getCategoryId());
        }
        if (ObjectUtil.isNotNull(bo.getTypeId())) {
            lqw.eq(SohuMerchant::getTypeId, bo.getTypeId());
        }
        if (ObjectUtil.isNotNull(bo.getAuditStatus())) {
            lqw.eq(SohuMerchant::getAuditStatus, bo.getAuditStatus());
        }
        if (StrUtil.isNotEmpty(bo.getKeywords())) {
            lqw.and(i -> i.like(SohuMerchant::getName, bo.getKeywords()).or().like(SohuMerchant::getKeywords, bo.getKeywords()));
        }
        if (StrUtil.isNotBlank(bo.getStartTime()) && StrUtil.isNotBlank(bo.getEndTime())) {
            SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
            try {
                Date start = dateFormat.parse(bo.getStartTime());
                Date end = dateFormat.parse(bo.getEndTime());
                lqw.between(SohuMerchant::getCreateTime, start, end);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (bo.getSiteId() != null) {
            lqw.eq(SohuMerchant::getSiteId, bo.getSiteId());
        }
        if (bo.getCitySiteId() != null) {
            lqw.eq(SohuMerchant::getCitySiteId, bo.getCitySiteId());
        }
        if (bo.getIsSelf() != null) {
            lqw.eq(SohuMerchant::getIsSelf, bo.getIsSelf());
        }
        if (bo.getIsSwitch() != null) {
            lqw.eq(SohuMerchant::getIsSwitch, bo.getIsSwitch());
            lqw.eq(SohuMerchant::getAuditStatus, AuditState.Pass.name());
        }
        if (StringUtils.isNotBlank(bo.getSaleNumsSort())) {
            if ((bo.getSaleNumsSort().equals("desc") || bo.getSaleNumsSort().equals("DESC"))) {
                lqw.orderByDesc(SohuMerchant::getSaleNums);
            } else {
                lqw.orderByAsc(SohuMerchant::getSaleNums);
            }

        } else {
            lqw.orderByDesc(SohuMerchant::getId);
        }
        IPage<SohuMerchantVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean audit(SohuMerchantBo bo) {
        boolean status = bo.getAuditStatus().equals(AuditState.Refuse.name());
        if (status && StrUtil.isEmpty(bo.getDenialReason())) {
            throw new ServiceException(MessageUtils.message("rejection.not.blank"));
        }
        SohuMerchant sohuMerchant = baseMapper.selectById(bo.getId());
        if (!sohuMerchant.getAuditStatus().equals(AuditState.WaitApprove.name())) {
            throw new ServiceException(MessageUtils.message("already.audit"));
        }
        SohuMerchant merchant = BeanUtil.toBean(bo, SohuMerchant.class);
        Long userId = LoginHelper.getUserId();
        merchant.setAuditorId(userId);
        boolean flag = baseMapper.updateById(merchant) > 0;
        if (flag && !status) {
            //给用户狐少少角色
            List<SysRole> roleDOS;
            //if (StringUtils.equals(Constants.SOHUGLOBAL, bo.getSysSource())) {
            roleDOS = remoteSysRoleService.listByRoleCodes(Collections.singletonList(RoleCodeEnum.SHOP.getCode()));
//            } else {
//                roleDOS = remoteSysRoleService.listByRoleCodes(Collections.singletonList(RoleCodeEnum.MERCHANT.getCode()));
//            }
            if (CollUtil.isEmpty(roleDOS)) {
                return Boolean.FALSE;
            }
            // 发送短信以及邮箱通知
            CompletableFuture.runAsync(() -> sendAuditSuccessNotice(sohuMerchant.getPhone(), sohuMerchant.getEmail()), asyncConfig.getAsyncExecutor());
            //获取申请用户id
            remoteSysRoleService.insertList(roleDOS, sohuMerchant.getUserId());
            // 兼容海外短剧处理
            //if (StringUtils.equals(Constants.SOHUGLOBAL, bo.getSysSource())) {
            this.remotePlatformRoleService.insertUserRole(PlatformRoleCodeEnum.SHOP.getCode(), sohuMerchant.getUserId());
//            } else {
//                this.remotePlatformRoleService.insertUserRole(PlatformRoleCodeEnum.MERCHANT.getCode(), sohuMerchant.getUserId());
//            }
            this.remoteUserService.flushLoginCacheByUserId(sohuMerchant.getUserId());
        }
        return flag;
    }

    private void sendAuditSuccessNotice(String phone, String email) {
        try {
            sendMsg(phone, email);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            sendEmail(phone, email);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void sendEmail(String phone, String email) {
        remoteMailService.send(email, "商户入驻成功", String.format("\n" + "尊敬的商家用户，您好，恭喜您您提交的入驻申请审核已通过。账号：%s邮箱地址：%s初始密码：123456可登录http://store.minglereels.com/平台修改初始密码，谢谢您的支持", phone, email));
    }

    private void sendMsg(String phone, String email) {
        LinkedHashMap<String, String> param = new LinkedHashMap<>();
        param.put("account", phone);
        param.put("email_address", email);
        remoteSmsService.send(phone, "SMS_473900036", param);
    }

    @Override
    public Boolean auditMerchant(PlayletAuditMerchantBo bo) {

        return this.audit(BeanCopyUtils.copy(bo, SohuMerchantBo.class));
    }

    @Override
    public SohuMerchantVo recommendOne() {
        LambdaQueryWrapper<SohuMerchant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuMerchant::getIsRecommend, true);
        wrapper.last(" limit 1");
        return this.baseMapper.selectVoOne(wrapper);
    }

    @Override
    public List<SohuMerchantVo> getMerchantOpenList(String sysSource) {
        LambdaQueryWrapper<SohuMerchant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuMerchant::getIsSwitch, Constants.ONE);
        wrapper.eq(SohuMerchant::getIsDel, Boolean.FALSE);
        wrapper.eq(StringUtils.isNotBlank(sysSource), SohuMerchant::getSysSource, sysSource);
        return this.baseMapper.selectVoList(wrapper);
    }

    @Override
    public List<SohuMerchantVo> getProductMerchantList(String sysSource) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        LambdaQueryWrapper<SohuMerchant> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(sysSource), SohuMerchant::getSysSource, sysSource);
        // 判断是否有狐少少店铺商家的角色
//        if (loginUser.getRoles().stream().anyMatch(role -> RoleCodeEnum.MERCHANT.getCode().equals(role.getRoleKey()))) {
//            lqw.eq(SohuMerchant::getUserId, loginUser.getUserId());
//        }
        if (loginUser.getRoles().stream().anyMatch(role -> RoleCodeEnum.SHOP.getCode().equals(role.getRoleKey()))) {
            lqw.eq(SohuMerchant::getUserId, loginUser.getUserId());
        }
        lqw.eq(SohuMerchant::getIsDel, Boolean.FALSE);
        lqw.eq(SohuMerchant::getIsSwitch, Constants.ONE);
        lqw.eq(SohuMerchant::getAuditStatus, AuditState.Pass.name());
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public Boolean closeStore(SohuMerchantAuditBo bo) {
        if (StrUtil.isBlankIfStr(bo.getAuditStatus())) {
            bo.setAuditStatus(bo.getState());
        }
        //校验
        boolean status = bo.getAuditStatus().equals(AuditState.Refuse.name());
        if (status && StrUtil.isEmpty(bo.getDenialReason())) {
            throw new ServiceException(MessageUtils.message("rejection.not.blank"));
        }
        SohuMerchant sohuMerchant = baseMapper.selectById(bo.getId());
        if (Objects.isNull(sohuMerchant)) {
            throw new ServiceException(MessageUtils.message("merchant.not.exist"));
        }
        if (!StrUtil.equalsAnyIgnoreCase(sohuMerchant.getCloseStatus(), CommonState.CloseApprove.name())) {
            throw new ServiceException(MessageUtils.message("merchant.not.close.status"));
        }
        SohuAuditLogBo auditLogBo = SohuAuditLogBo.busy(AuditLogBusyType.MERCHANT_CLOSE_AUDIT, bo.getId());
        //更新店铺信息
        if (StrUtil.equalsAnyIgnoreCase(bo.getAuditStatus(), CommonState.ClosePass.name())) {
            sohuMerchant.setCloseStatus(CommonState.ClosePass.getCode());
            // 闭店审核通过
            sohuMerchant.setIsSwitch(Boolean.FALSE);
            // 闭店保证金处理逻辑
            remoteMerchantBondRefundService.handleCloseMerchantBond(sohuMerchant.getId());
            auditLogBo.setBusyTitle("闭店审核通过");
        }
        // 闭店审核拒绝，改回通过状态
        if (StrUtil.equalsAnyIgnoreCase(bo.getAuditStatus(), CommonState.CloseRefuse.name())) {
            sohuMerchant.setCloseStatus(CommonState.CloseRefuse.getCode());
            sohuMerchant.setIsSwitch(Boolean.TRUE);
            auditLogBo.setBusyTitle("闭店审核驳回");
        }

        CompletableFuture.runAsync(() -> {
            if (StrUtil.equalsAnyIgnoreCase(bo.getAuditStatus(), CommonState.ClosePass.name())) {
                // 发送升级失败通知
                remoteMiddleSystemNoticeService.sendSystemNotice(sohuMerchant.getUserId(), sohuMerchant.getId(), SysNoticeEnum.MERCHANT_CLOSE_SUCCESS, Arrays.asList(sohuMerchant.getName(), DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN)));
            } else if (StrUtil.equalsAnyIgnoreCase(bo.getAuditStatus(), CommonState.CloseRefuse.name())) {
                // 发送入驻失败通知
                remoteMiddleSystemNoticeService.sendSystemNotice(sohuMerchant.getUserId(), sohuMerchant.getId(), SysNoticeEnum.MERCHANT_CLOSE_FAIL, Arrays.asList(sohuMerchant.getName(), bo.getDenialReason()));
            }
        }, asyncConfig.getAsyncExecutor());

        sohuMerchant.setDenialReason(bo.getDenialReason());
        Long userId = LoginHelper.getUserId();
        sohuMerchant.setAuditorId(userId);

        auditLogBo.setState(bo.getState());
        auditLogBo.setRefuseReason(bo.getDenialReason());
        auditLogBo.setOperateUserId(LoginHelper.getUserId());
        // 插入审核记录
        remoteSohuAuditLogService.insertByBo(auditLogBo);
        return baseMapper.updateById(sohuMerchant) > 0;
    }

    @Override
    public TableDataInfo<SohuMerchantCloseStoreAuditVo> closeStorePage(SohuMerchantCloseStoreQueryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuMerchant> lqw = new LambdaQueryWrapper<>();
        List<String> statusList = Arrays.asList(CommonState.ClosePass.getCode(), CommonState.CloseApprove.getCode(), CommonState.CloseRefuse.getCode());
        lqw.in(SohuMerchant::getCloseStatus, statusList);
        lqw.like(StrUtil.isNotBlank(bo.getMerchantName()), SohuMerchant::getName, bo.getMerchantName());
        lqw.eq(StrUtil.isNotBlank(bo.getAuditStatus()), SohuMerchant::getAuditStatus, bo.getAuditStatus());
        lqw.eq(!CalUtils.isNullOrZero(bo.getCitySiteId()), SohuMerchant::getCitySiteId, bo.getCitySiteId());
        lqw.eq(StrUtil.isNotBlank(bo.getMerchantType()), SohuMerchant::getMerchantType, bo.getMerchantType());
        SohuAccountBo accountBo = new SohuAccountBo();
        accountBo.setLicenseProvinceCode(bo.getLicenseProvinceCode());
        accountBo.setLicenseCityCode(bo.getLicenseCityCode());
        accountBo.setLicenseAreaCode(bo.getLicenseAreaCode());
        List<SohuAccountVo> accountVos = remoteAccountService.queryList(accountBo);
        Set<Long> userIdList = CollUtil.isEmpty(accountVos) ? Collections.emptySet() : accountVos.stream().map(SohuAccountVo::getUserId).collect(Collectors.toSet());
        lqw.in(!userIdList.isEmpty(), SohuMerchant::getUserId, userIdList);
        lqw.orderByDesc(BaseEntity::getUpdateTime);
        IPage<SohuMerchantVo> page = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        if (CollUtil.isEmpty(page.getRecords())) {
            return TableDataInfoUtils.build();
        }
        TableDataInfo<SohuMerchantCloseStoreAuditVo> result = new TableDataInfo<>();
        List<SohuMerchantCloseStoreAuditVo> list = new LinkedList<>();
        List<SohuMerchantVo> records = page.getRecords();
        Set<Long> userIds = new HashSet<>();
        Set<Long> siteIds = new HashSet<>();
        for (SohuMerchantVo record : records) {
            userIds.add(record.getUserId());
            siteIds.add(record.getCitySiteId());
        }
        Map<Long, LoginUser> userMap = remoteUserService.selectMap(userIds);
        Map<Long, SohuAccountVo> accountVoMap = remoteAccountService.selectAccountMapByUserIds(userIds);
        for (SohuMerchantVo record : records) {
            SohuMerchantCloseStoreAuditVo convert = convert(record, SohuMerchantCloseStoreAuditVo.class);
            convert.setCloseStatus(record.getCloseStatus());
            convert.setId(record.getId());
            convert.setAuditStatus(record.getAuditStatus());
            convert.setApplyTime(record.getUpdateTime());
            LoginUser user = userMap.get(record.getUserId());
            convert.setUserName(Objects.nonNull(user) ? StringUtils.getValidString(user.getNickname(), user.getUsername()) : null);
            // 查找已通过的的经营类目
            List<SohuMerchantClassificationQualificationVo> categoryPassInfos = baseMapper.selectCategoryList(record.getId(), null, Arrays.asList(CommonState.Pass.name()));
            // 查找已通过的的经营品牌
            List<SohuMerchantBrandQualificationVo> brandPassInfos = baseMapper.selectBrandList(record.getId(), null, Arrays.asList(CommonState.Pass.name()));
            if (CollUtil.isNotEmpty(brandPassInfos)) {
                Set<Long> brandIds = brandPassInfos.stream().map(SohuMerchantBrandQualificationVo::getBrandId).collect(Collectors.toSet());
                List<SohuProductBrandVo> brandList = remoteProductBrandService.list(brandIds);
                Map<Long, SohuProductBrandVo> brandVoMap = brandList.stream().collect(Collectors.toMap(SohuProductBrandVo::getId, Function.identity()));
                for (SohuMerchantBrandQualificationVo brandPassInfo : brandPassInfos) {
                    SohuProductBrandVo productBrandVo = brandVoMap.get(brandPassInfo.getBrandId());
                    brandPassInfo.setBrandName(Objects.nonNull(productBrandVo) ? productBrandVo.getName() : null);
                }
            }
            if (CollUtil.isNotEmpty(categoryPassInfos)) {
                //查询类目名称
                List<Long> cateIds = categoryPassInfos.stream().map(SohuMerchantClassificationQualificationVo::getCateId).collect(Collectors.toList());
                List<SohuProductCategoryPcVo> productCategoryPcVos = remoteProductCategoryPcService.selectByIds(cateIds);
                Map<Long, SohuProductCategoryPcVo> categoryPcVoMap = CollUtil.isEmpty(productCategoryPcVos) ? new HashMap<>() :
                        productCategoryPcVos.stream().collect(Collectors.toMap(SohuProductCategoryPcVo::getId, Function.identity()));
                for (SohuMerchantClassificationQualificationVo qualificationVo : categoryPassInfos) {
                    SohuProductCategoryPcVo categoryPcVo = categoryPcVoMap.get(qualificationVo.getCateId());
                    qualificationVo.setCateName(Objects.nonNull(categoryPcVo) ? categoryPcVo.getName() : null);
                }
            }
            convert.setCategoryPassInfos(categoryPassInfos);
            convert.setBrandPassInfos(brandPassInfos);
            //convert.setAccountBalance(getYmBalance(record.getUserId()));
            SohuAccountVo accountVo = accountVoMap.get(record.getUserId());
            if (Objects.nonNull(accountVo)) {
                SohuAccountModel accountModel = BeanCopyUtils.copy(accountVo, SohuAccountModel.class);
                convert.setAccountInfo(accountModel);
            }
            list.add(convert);
        }
        result.setData(list);
        result.setTotal(page.getTotal());
        return result;
    }

    @Override
    public SohuMerchantCategoryDetailAuditVo categoryDetail(Long id) {
        SohuMerchantClassification merchantClassification = sohuMerchantClassificationMapper.selectById(id);
        if (Objects.isNull(merchantClassification)) {
            return new SohuMerchantCategoryDetailAuditVo();
        }
        // 商户ID
        Long merId = merchantClassification.getMerId();
        // 新增的类目id
        Long cateId = merchantClassification.getCateId();
        SohuMerchantVo merchantVo = baseMapper.selectVoById(merId);
        Objects.requireNonNull(merchantVo, "店铺不存在");
        SohuMerchantCategoryDetailAuditVo result = convert(merchantVo, SohuMerchantCategoryDetailAuditVo.class);
        result.setId(id);
        result.setState(merchantClassification.getState());
        result.setApplyTime(merchantClassification.getCreateTime());
        LoginUser user = remoteUserService.queryById(merchantVo.getUserId());
        result.setUserName(StringUtils.getValidString(user.getNickname(), user.getUsername()));
        // 查询审核记录列表
        List<SohuAuditLogVo> auditLogVos = remoteSohuAuditLogService.queryList(SohuAuditLogBo.busy(AuditLogBusyType.MERCHANT_CATEGORY_AUDIT, id));
        List<SohuAuditLogModel> sohuAuditLogModels = BeanCopyUtils.copyList(auditLogVos, SohuAuditLogModel.class);
        result.setAuditLogs(sohuAuditLogModels);

        // 查找已通过,待审核的的经营类目
        List<SohuMerchantClassificationQualificationVo> qualificationVoList = baseMapper.selectCategoryList(merId, merchantClassification.getRelateNo(),
                Arrays.asList(CommonState.Pass.name(), CommonState.WaitApprove.name(), CommonState.Refuse.name()));
        if (CollUtil.isEmpty(qualificationVoList)) {
            return result;
        }
        //查询类目名称
        List<Long> cateIds = qualificationVoList.stream().map(SohuMerchantClassificationQualificationVo::getCateId).collect(Collectors.toList());
        List<SohuProductCategoryPcVo> productCategoryPcVos = remoteProductCategoryPcService.selectByIds(cateIds);
        Map<Long, SohuProductCategoryPcVo> categoryPcVoMap = CollUtil.isEmpty(productCategoryPcVos) ? new HashMap<>() :
                productCategoryPcVos.stream().collect(Collectors.toMap(SohuProductCategoryPcVo::getId, Function.identity()));
        for (SohuMerchantClassificationQualificationVo qualificationVo : qualificationVoList) {
            SohuProductCategoryPcVo categoryPcVo = categoryPcVoMap.get(qualificationVo.getCateId());
            qualificationVo.setCateName(Objects.nonNull(categoryPcVo) ? categoryPcVo.getName() : null);
        }

        // 已通过的经营类目
        List<SohuMerchantClassificationQualificationVo> categoryPassInfos = new LinkedList<>();
        // 待审核的经营类目
        List<SohuMerchantClassificationQualificationVo> categoryWaitApproveInfos = new LinkedList<>();
        for (SohuMerchantClassificationQualificationVo item : qualificationVoList) {
            if (StrUtil.equalsAnyIgnoreCase(item.getState(), CommonState.Pass.name()) && !Objects.equals(merchantClassification.getCateId(), item.getCateId())) {
                categoryPassInfos.add(item);
            } else {
                categoryWaitApproveInfos.add(item);
            }
        }
        // 查找已通过的的经营品牌
        List<SohuMerchantBrandQualificationVo> brandPassInfos = baseMapper.selectBrandList(merId, null, Arrays.asList(CommonState.Pass.name()));
        if (CollUtil.isNotEmpty(brandPassInfos)) {
            // 品牌ID集合
            List<Long> brandIds = brandPassInfos.stream().map(SohuMerchantBrandQualificationVo::getBrandId).collect(Collectors.toList());
            List<SohuProductBrandVo> brandList = remoteProductBrandService.list(brandIds);
            Map<Long, SohuProductBrandVo> brandVoMap = brandList.stream().collect(Collectors.toMap(SohuProductBrandVo::getId, Function.identity()));
            for (SohuMerchantBrandQualificationVo qualificationVo : brandPassInfos) {
                SohuProductBrandVo productBrandVo = brandVoMap.get(qualificationVo.getBrandId());
                qualificationVo.setBrandName(Objects.nonNull(productBrandVo) ? productBrandVo.getName() : null);
            }
            result.setBrandPassInfos(mergeBrandQualifications(brandPassInfos));
        }
        result.setCategoryPassInfos(mergeQualifications(categoryPassInfos));
        result.setCategoryWaitApproveInfos(mergeQualifications(categoryWaitApproveInfos));
        SohuAccountVo accountVo = remoteAccountService.queryByUserId(merchantVo.getUserId());
        SohuAccountModel accountModel = BeanCopyUtils.copy(accountVo, SohuAccountModel.class);
        result.setAccountInfo(accountModel);
        return result;
    }

    @Override
    public SohuMerchantBrandDetailAuditVo brandDetail(Long id) {
        SohuMerchantBrand merchantBrand = sohuMerchantBrandMapper.selectById(id);
        if (Objects.isNull(merchantBrand)) {
            return new SohuMerchantBrandDetailAuditVo();
        }
        // 商户ID
        Long merId = merchantBrand.getMerId();
        // 新增的品牌id
        Long cateId = merchantBrand.getBrandId();
        SohuMerchantVo merchantVo = baseMapper.selectVoById(merId);
        Objects.requireNonNull(merchantVo, "店铺不存在");
        SohuMerchantBrandDetailAuditVo result = convert(merchantVo, SohuMerchantBrandDetailAuditVo.class);
        result.setId(id);
        result.setState(merchantBrand.getState());
        result.setApplyTime(merchantBrand.getCreateTime());
        LoginUser user = remoteUserService.queryById(merchantVo.getUserId());
        result.setUserName(StringUtils.getValidString(user.getNickname(), user.getUsername()));
        // 查询审核记录列表
        List<SohuAuditLogVo> auditLogVos = remoteSohuAuditLogService.queryList(SohuAuditLogBo.busy(AuditLogBusyType.MERCHANT_BRAND_AUDIT, id));
        List<SohuAuditLogModel> sohuAuditLogModels = BeanCopyUtils.copyList(auditLogVos, SohuAuditLogModel.class);
        result.setAuditLogs(sohuAuditLogModels);
        // 查找已通过,待审核的的经营品牌
        List<SohuMerchantBrandQualificationVo> brandQualificationVoList = baseMapper.selectBrandList(merId, merchantBrand.getRelateNo(),
                Arrays.asList(CommonState.Pass.name(), CommonState.WaitApprove.name(), CommonState.Refuse.name()));
        if (CollUtil.isEmpty(brandQualificationVoList)) {
            return result;
        }
        // 品牌ID集合
        List<Long> brandIds = brandQualificationVoList.stream().map(SohuMerchantBrandQualificationVo::getBrandId).collect(Collectors.toList());
        List<SohuProductBrandVo> brandList = remoteProductBrandService.list(brandIds);
        Map<Long, SohuProductBrandVo> brandVoMap = brandList.stream().collect(Collectors.toMap(SohuProductBrandVo::getId, Function.identity()));
        for (SohuMerchantBrandQualificationVo qualificationVo : brandQualificationVoList) {
            SohuProductBrandVo productBrandVo = brandVoMap.get(qualificationVo.getBrandId());
            qualificationVo.setBrandName(Objects.nonNull(productBrandVo) ? productBrandVo.getName() : null);
        }

        // 已审核通过的品牌信息
        List<SohuMerchantBrandQualificationVo> brandPassInfos = new LinkedList<>();
        // 待审核的品牌信息
        List<SohuMerchantBrandQualificationVo> brandWaitApproveInfos = new LinkedList<>();
        for (SohuMerchantBrandQualificationVo item : brandQualificationVoList) {
            if (StrUtil.equalsAnyIgnoreCase(item.getState(), CommonState.Pass.name()) && !Objects.equals(merchantBrand.getBrandId(), item.getBrandId())) {
                brandPassInfos.add(item);
            } else {
                brandWaitApproveInfos.add(item);
            }
        }
        // 查找已通过的的经营类目
        List<SohuMerchantClassificationQualificationVo> categoryPassInfos = baseMapper.selectCategoryList(merId, null, Arrays.asList(CommonState.Pass.name()));
        if (CollUtil.isNotEmpty(categoryPassInfos)) {
            //查询类目名称
            List<Long> cateIds = categoryPassInfos.stream().map(SohuMerchantClassificationQualificationVo::getCateId).collect(Collectors.toList());
            List<SohuProductCategoryPcVo> productCategoryPcVos = remoteProductCategoryPcService.selectByIds(cateIds);
            Map<Long, SohuProductCategoryPcVo> categoryPcVoMap = CollUtil.isEmpty(productCategoryPcVos) ? new HashMap<>() :
                    productCategoryPcVos.stream().collect(Collectors.toMap(SohuProductCategoryPcVo::getId, Function.identity()));
            for (SohuMerchantClassificationQualificationVo qualificationVo : categoryPassInfos) {
                SohuProductCategoryPcVo categoryPcVo = categoryPcVoMap.get(qualificationVo.getCateId());
                qualificationVo.setCateName(Objects.nonNull(categoryPcVo) ? categoryPcVo.getName() : null);
            }
        }

        result.setCategoryPassInfos(mergeQualifications(categoryPassInfos));
        result.setBrandPassInfos(mergeBrandQualifications(brandPassInfos));
        result.setBrandWaitApproveInfos(mergeBrandQualifications(brandWaitApproveInfos));
        SohuAccountVo accountVo = remoteAccountService.queryByUserId(merchantVo.getUserId());
        SohuAccountModel accountModel = BeanCopyUtils.copy(accountVo, SohuAccountModel.class);
        result.setAccountInfo(accountModel);
        return result;
    }

    @Override
    public SohuMerchantCloseStoreDetailAuditVo closeStoreDetail(Long id) {
        SohuMerchantVo merchantVo = baseMapper.selectVoById(id);
        Objects.requireNonNull(merchantVo, "店铺不存在");
        SohuMerchantCloseStoreDetailAuditVo result = convert(merchantVo, SohuMerchantCloseStoreDetailAuditVo.class);
        result.setId(id);
        result.setState(merchantVo.getAuditStatus());
        result.setApplyTime(merchantVo.getUpdateTime());
        LoginUser user = remoteUserService.queryById(merchantVo.getUserId());
        result.setUserName(StringUtils.getValidString(user.getNickname(), user.getUsername()));
        //查询实名入驻信息
        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserId(merchantVo.getUserId());
        if (Objects.nonNull(sohuAccountVo)) {
            SohuAccountModel accountInfo = BeanUtil.copyProperties(sohuAccountVo, SohuAccountModel.class);
            result.setAccountInfo(accountInfo);
        }
        // 查找已通过的经营品牌
        List<SohuMerchantBrandQualificationVo> brandQualificationVoList = baseMapper.selectBrandList(id, null, Arrays.asList(CommonState.Pass.name()));
        if (CollUtil.isNotEmpty(brandQualificationVoList)) {
            // 品牌ID集合
            List<Long> brandIds = brandQualificationVoList.stream().map(SohuMerchantBrandQualificationVo::getBrandId).collect(Collectors.toList());
            List<SohuProductBrandVo> brandList = remoteProductBrandService.list(brandIds);
            Map<Long, SohuProductBrandVo> brandVoMap = brandList.stream().collect(Collectors.toMap(SohuProductBrandVo::getId, Function.identity()));
            for (SohuMerchantBrandQualificationVo qualificationVo : brandQualificationVoList) {
                SohuProductBrandVo productBrandVo = brandVoMap.get(qualificationVo.getBrandId());
                qualificationVo.setBrandName(Objects.nonNull(productBrandVo) ? productBrandVo.getName() : null);
            }
            result.setBrandInfos(mergeBrandQualifications(brandQualificationVoList));
        }

        // 查找已通过的经营类目
        List<SohuMerchantClassificationQualificationVo> qualificationVoList = baseMapper.selectCategoryList(id, null, Arrays.asList(CommonState.Pass.name()));
        //查询类目名称
        List<Long> cateIds = qualificationVoList.stream().map(SohuMerchantClassificationQualificationVo::getCateId).collect(Collectors.toList());
        List<SohuProductCategoryPcVo> productCategoryPcVos = remoteProductCategoryPcService.selectByIds(cateIds);
        Map<Long, SohuProductCategoryPcVo> categoryPcVoMap = CollUtil.isEmpty(productCategoryPcVos) ? new HashMap<>() :
                productCategoryPcVos.stream().collect(Collectors.toMap(SohuProductCategoryPcVo::getId, Function.identity()));
        for (SohuMerchantClassificationQualificationVo qualificationVo : qualificationVoList) {
            SohuProductCategoryPcVo categoryPcVo = categoryPcVoMap.get(qualificationVo.getCateId());
            qualificationVo.setCateName(Objects.nonNull(categoryPcVo) ? categoryPcVo.getName() : null);
        }

        if (CollUtil.isNotEmpty(qualificationVoList)) {
            result.setCategoryInfos(mergeQualifications(qualificationVoList));
        }

        //result.setAccountBalance(getYmBalance(merchantVo.getUserId()));
        SohuAccountVo accountVo = remoteAccountService.queryByUserId(merchantVo.getUserId());
        SohuAccountModel accountModel = BeanCopyUtils.copy(accountVo, SohuAccountModel.class);
        result.setAccountInfo(accountModel);
        // 已上架商品
        Long productCount = remoteProductService.productCount(merchantVo.getId(), CommonState.Pass.getCode().toUpperCase());
        result.setOnShelfGoodNum(CalUtils.isNullOrZero(productCount) ? 0L : productCount);
        // 查询审核记录列表
        List<SohuAuditLogVo> auditLogVos = remoteSohuAuditLogService.queryList(SohuAuditLogBo.busy(AuditLogBusyType.MERCHANT_CLOSE_AUDIT, id));
        List<SohuAuditLogModel> sohuAuditLogModels = BeanCopyUtils.copyList(auditLogVos, SohuAuditLogModel.class);
        result.setAuditLogs(sohuAuditLogModels);
        return result;
    }

    @Override
    public SohuMerchantQualificationInfoVo getMerchantQualificationInfo(Long merId) {
        SohuMerchantVo merchantVo = baseMapper.selectVoById(merId);
        Objects.requireNonNull(merchantVo, "店铺不存在");
        SohuMerchantQualificationInfoVo result = new SohuMerchantQualificationInfoVo();
        result.setMerchantId(merId);
        result.setMerchantName(merchantVo.getName());
        result.setAvatar(merchantVo.getAvatar());
        result.setMerchantType(merchantVo.getMerchantType());
        result.setUserId(merchantVo.getUserId());
        result.setMerchantPhone(merchantVo.getPhone());
        LoginUser user = remoteUserService.queryById(merchantVo.getUserId());
        result.setUserName(StringUtils.getValidString(user.getNickname(), user.getUsername()));

        // 查找已通过,待审核的的经营品牌
        List<SohuMerchantBrandQualificationVo> brandPassInfos = baseMapper.selectBrandList(merId, null, Arrays.asList(CommonState.Pass.name()));
        // 查找已通过的的经营类目
        List<SohuMerchantClassificationQualificationVo> categoryPassInfos = baseMapper.selectCategoryList(merId, null, Arrays.asList(CommonState.Pass.name()));
        result.setCategoryPassInfos(mergeQualifications(categoryPassInfos));
        result.setBrandPassInfos(mergeBrandQualifications(brandPassInfos));
        return result;
    }

    @Override
    public List<SohuMerchantVo> selectByStatus(List<String> status) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<SohuMerchant>()
                .in(SohuMerchant::getAuditStatus, status).eq(SohuMerchant::getIsSwitch, Boolean.TRUE));
    }

    @Override
    public Boolean existMerchantName(String merchantName, Long merchantId) {
        LambdaQueryWrapper<SohuMerchant> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuMerchant::getName, merchantName);
        lqw.last(" limit 1");
        SohuMerchant sohuMerchant = baseMapper.selectOne(lqw);
        if (Objects.nonNull(sohuMerchant)) {
            //如果存在店铺且店铺Id不同说明名称已存在
            if (merchantId != null && !merchantId.equals(sohuMerchant.getId())) {
                return Boolean.TRUE;
            }
            if (merchantId == null) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public void handleRobot(String busyCode, Boolean isPass, String reason) {
        SohuMerchant sohuMerchant = baseMapper.selectById(busyCode);
        if (Objects.isNull(sohuMerchant)) {
            return;
        }
        if (isPass && sohuMerchant.getAuditStatus().equals(CommonState.WaitRobotApprove.getCode())) {
            sohuMerchant.setAuditStatus(CommonState.WaitApprove.getCode());
        } else {
            sohuMerchant.setAuditStatus(CommonState.Refuse.getCode());
            sohuMerchant.setDenialReason(reason);
            //记录审核日志
            sohuMerchantAuditLogService.save(sohuMerchant.getId(), sohuMerchant.getMerchantType(), sohuMerchant.getAuditStatus(), sohuMerchant.getDenialReason());
        }
        baseMapper.updateById(sohuMerchant);
    }

    /**
     * 商户开关切换事件
     */
    @Override
    public Boolean updateSwitch(Long id) {
        SohuMerchant merchant = baseMapper.selectById(id);
        if (Objects.isNull(merchant)) {
            throw new ServiceException("商户不存在");
        }
        openCheck(merchant);
        merchant.setIsSwitch(!merchant.getIsSwitch());
        //超管下架 下架店铺商品
        if (!merchant.getIsSwitch() && LoginHelper.isAdmin(LoginHelper.getUserId())) {
            return transactionTemplate.execute(e -> {
                baseMapper.updateById(merchant);
                remoteProductService.forcedRemovalAll(merchant.getId(), merchant.getSiteId());
                return Boolean.TRUE;
            });
        }
        return baseMapper.updateById(merchant) > 0;
    }

    /**
     * 商户开启验证
     *
     * @param merchant
     */
    private void openCheck(SohuMerchant merchant) {
        if (!merchant.getIsSwitch()) {
            //当前关闭，准备开启时
            if (StrUtil.isEmpty(merchant.getAvatar()) || StrUtil.isEmpty(merchant.getBackImage()) || StrUtil.isEmpty(merchant.getStreetBackImage())) {
                throw new ServiceException("请先进行商户头像、背景图配置");
            }
            SohuMerchantInfo merchantInfo = sohuMerchantInfoMapper.selectOne(SohuMerchantInfo::getMerId, merchant.getId());
            if (StrUtil.isEmpty(merchantInfo.getServiceLink()) && StrUtil.isEmpty(merchantInfo.getServicePhone()) && StrUtil.isEmpty(merchantInfo.getServiceMessage()) && StrUtil.isEmpty(merchantInfo.getServiceEmail())) {
                throw new ServiceException("请先进行客服配置");
            }
        }
    }

    /**
     * 商户推荐切换事件
     */
    @Override
    public Boolean recommend(Long id) {
        SohuMerchant merchant = baseMapper.selectById(id);
        if (Objects.isNull(merchant)) {
            throw new ServiceException("商户不存在");
        }
        merchant.setIsRecommend(!merchant.getIsRecommend());
        return baseMapper.updateById(merchant) > 0;
    }

    @Override
    public SohuMerchantVo selectByUserIdAndCitySiteId(Long userId, Long siteId) {
        LambdaQueryWrapper<SohuMerchant> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuMerchant::getUserId, userId);
        lqw.eq(SohuMerchant::getCitySiteId, siteId);
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public List<SohuMerchantModel> selectByUserIdAndSiteId(Long userId, Long siteId, Boolean flag) {
        LambdaQueryWrapper<SohuMerchant> lqw = Wrappers.lambdaQuery();
        if (flag) {
            lqw.eq(SohuMerchant::getAuditStatus, AuditState.Pass.name());
        }
        lqw.eq(userId != null, SohuMerchant::getUserId, userId);
        lqw.eq(siteId != null, SohuMerchant::getSiteId, siteId);
        lqw.ne(SohuMerchant::getCloseStatus,  CommonState.ClosePass.getCode());
        List<SohuMerchantVo> sohuMerchantVoList = baseMapper.selectVoList(lqw);
        List<SohuMerchantModel> merchantModels = BeanCopyUtils.copyList(sohuMerchantVoList, SohuMerchantModel.class);
        if (CollUtil.isEmpty(merchantModels)) {
            return new ArrayList<>();
        }
        List<Long> ids = merchantModels.stream().map(SohuMerchantModel::getId).collect(Collectors.toList());
        Map<Long, List<SohuUserCollectVo>> collectMap = remoteMiddleUserCollectService.countMap(BusyType.Shop.name(), ids);
        merchantModels.forEach(merchant -> {
            List<SohuUserCollectVo> collectVos = collectMap.get(merchant.getId());
            merchant.setCollectCount(CollUtil.isEmpty(collectVos) ? 0 : collectVos.size());
            if (CommonState.CloseApprove.getCode().equals(merchant.getCloseStatus())){
                merchant.setAuditStatus(CommonState.CloseApprove.getCode());
            }
        });
        return merchantModels;
    }

    @Override
    public Boolean updateCopyProduct(Long id, String type, Long num) {
        SohuMerchant merchant = baseMapper.selectById(id);
        if (type.equals(ProductTypeEnum.sub.name()) && merchant.getCopyProductNum() - num < 0) {
            throw new ServiceException("扣减后的数量不能小于0");
        }
        UpdateWrapper<SohuMerchant> updateWrapper = new UpdateWrapper<>();
        if (type.equals(ProductTypeEnum.add.name())) {
            updateWrapper.setSql(StrUtil.format("copy_product_num = copy_product_num + {}", num));
        }
        if (type.equals(ProductTypeEnum.sub.name())) {
            updateWrapper.setSql(StrUtil.format("copy_product_num = copy_product_num - {}", num));
            updateWrapper.last(StrUtil.format("and (copy_product_num - {} >= 0)", num));
        }
        updateWrapper.eq("id", id);
        return baseMapper.update(merchant, updateWrapper) > 0;
    }

    @Override
    public TableDataInfo<SohuMerchantVo> getInfoList(SohuMerchantSearchBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isNull(loginUser) || loginUser.getUserId() <= 0L) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        LambdaQueryWrapper<SohuMerchant> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSysSource()), SohuMerchant::getSysSource, bo.getSysSource());
        // 判断是否有狐少少店铺商家的角色
//        if (loginUser.getRoles().stream().anyMatch(role -> RoleCodeEnum.MERCHANT.getCode().equals(role.getRoleKey()))) {
//            lqw.eq(SohuMerchant::getUserId, loginUser.getUserId());
//        }
        if (loginUser.getRoles().stream().anyMatch(role -> RoleCodeEnum.SHOP.getCode().equals(role.getRoleKey()))) {
            lqw.eq(SohuMerchant::getUserId, loginUser.getUserId());
        }
        if (StrUtil.isNotBlank(bo.getName())) {
            lqw.like(SohuMerchant::getName, bo.getName());
        }
        if (bo.getCategoryId() != null) {
            lqw.eq(SohuMerchant::getCategoryId, bo.getCategoryId());
        }
        if (bo.getSiteId() != null) {
            lqw.eq(SohuMerchant::getSiteId, bo.getSiteId());
        }
        if (bo.getAuditStatus() != null) {
            lqw.eq(SohuMerchant::getAuditStatus, bo.getAuditStatus());
        }
        lqw.ne(SohuMerchant::getCloseStatus, CommonState.ClosePass.getCode());
        Page<SohuMerchantVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        //站点名称
        if (CollUtil.isNotEmpty(result.getRecords())) {
            Set<Long> siteIds = result.getRecords().stream().map(SohuMerchantVo::getCitySiteId).collect(Collectors.toSet());
            Map<Long, SohuSiteVo> siteMap = remoteMiddleSiteService.queryMap(siteIds);
            for (SohuMerchantVo record : result.getRecords()) {
                SohuSiteVo site = siteMap.get(record.getCitySiteId());
                if (Objects.isNull(site)) {
                    continue;
                }
                record.setCitySiteName(site.getName());
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public List<SohuMerchant> getListByIds(Collection<Long> merIdList) {
        LambdaQueryWrapper<SohuMerchant> wrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isNotEmpty(merIdList)) {
            wrapper.in(SohuMerchant::getId, merIdList);
        }
        return this.baseMapper.selectList(wrapper);
    }

    @Override
    public SohuMerchantVo getByPhone(String phone) {
        LambdaQueryWrapper<SohuMerchant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuMerchant::getPhone, phone);
        return this.baseMapper.selectVoOne(wrapper);
    }

    @Override
    public Boolean updateBatch(List<SohuMerchant> merchants) {
        return this.baseMapper.updateBatchById(merchants);
    }

    @Override
    public TableDataInfo<PlayletMerchantApplyListVo> getMerchantApplyList(PlayletMerchantSearchBo bo, PageQuery pageQuery, boolean isShowProduct) {
        LambdaQueryWrapper<SohuMerchant> lqw = buildQueryWrapper(bo, isShowProduct);
        IPage<SohuMerchantVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return convertSohuMerchantVoToPlayletMerchantApplyListVo(result, isShowProduct);
    }

    /**
     * 对象转换
     *
     * @param result
     * @return TableDataInfo<PlayletMerchantApplyListVo>
     */
    private TableDataInfo<PlayletMerchantApplyListVo> convertSohuMerchantVoToPlayletMerchantApplyListVo(IPage<SohuMerchantVo> result, boolean isShowProduct) {
        List<SohuMerchantVo> merchantVoList = result.getRecords();
        if (CollUtil.isEmpty(merchantVoList)) {
            return TableDataInfoUtils.build();
        }

        List<PlayletMerchantApplyListVo> playletMerchantApplyList = merchantVoList.stream().map(sohuMerchantVo -> {
            PlayletMerchantApplyListVo playletMerchantApplyListVo = new PlayletMerchantApplyListVo();
            playletMerchantApplyListVo.setId(sohuMerchantVo.getId());
            playletMerchantApplyListVo.setMerchantName(sohuMerchantVo.getName());
            playletMerchantApplyListVo.setMerchantUserName(sohuMerchantVo.getRealName());
            playletMerchantApplyListVo.setMerchantUserId(sohuMerchantVo.getUserId());
            playletMerchantApplyListVo.setMerchantTypeId(sohuMerchantVo.getTypeId());
            playletMerchantApplyListVo.setMerchantEmail(sohuMerchantVo.getEmail());
            playletMerchantApplyListVo.setSort(sohuMerchantVo.getSort());
            playletMerchantApplyListVo.setIsSwitch(sohuMerchantVo.getIsSwitch());
            playletMerchantApplyListVo.setIsRecommend(sohuMerchantVo.getIsRecommend());
            playletMerchantApplyListVo.setCreateTime(sohuMerchantVo.getCreateTime());
            playletMerchantApplyListVo.setMerchantPhone(sohuMerchantVo.getPhone());
            playletMerchantApplyListVo.setMerchantCategoryId(sohuMerchantVo.getCategoryId());
            playletMerchantApplyListVo.setHandlingFee(sohuMerchantVo.getHandlingFee());
            playletMerchantApplyListVo.setMerchantKeywords(sohuMerchantVo.getKeywords());
            playletMerchantApplyListVo.setMerchantQualificationPicture(sohuMerchantVo.getQualificationPicture());
            playletMerchantApplyListVo.setRemark(sohuMerchantVo.getRemark());
            playletMerchantApplyListVo.setMerchantStarLevel(sohuMerchantVo.getStarLevel());
            playletMerchantApplyListVo.setIsSelf(sohuMerchantVo.getIsSelf());
            playletMerchantApplyListVo.setAuditStatus(sohuMerchantVo.getAuditStatus());
            playletMerchantApplyListVo.setProductSwitch(sohuMerchantVo.getProductSwitch());
            playletMerchantApplyListVo.setAvatar(sohuMerchantVo.getAvatar());
            playletMerchantApplyListVo.setCountryCode(sohuMerchantVo.getCountryCode());
            playletMerchantApplyListVo.setCountryName(sohuMerchantVo.getCountryName());
            if (isShowProduct) {
                //查询商品
                SohuProductPcReqNewBo sohuProductPcReqNewBo = new SohuProductPcReqNewBo();
                sohuProductPcReqNewBo.setMerId(sohuMerchantVo.getId());
                PageQuery pageQuery = new PageQuery(1, 3);
                TableDataInfo<SohuProductModel> productPageListNew = remoteProductPcService.queryPcPageListNew(sohuProductPcReqNewBo, pageQuery);
                if (CollectionUtil.isNotEmpty(productPageListNew.getData())) {
                    playletMerchantApplyListVo.setProductList(productPageListNew.getData());
                }
            }
            return playletMerchantApplyListVo;
        }).collect(Collectors.toList());
        // 封装分页数据
        Page<PlayletMerchantApplyListVo> merchantApplyListVoPage = new Page<>();
        merchantApplyListVoPage.setRecords(playletMerchantApplyList);
        merchantApplyListVoPage.setTotal(result.getTotal());

        return TableDataInfoUtils.build(merchantApplyListVoPage);
    }

    @Override
    public Boolean updateMerchantInfo(PlayletUpdateMerchantInfoBo bo) {
        if (Objects.isNull(bo)) {
            throw new ServiceException("修改商户信息异常,请重试");
        }

        return baseMapper.updateById(convertPlayletUpdateMerchantInfoBoToSohuMerchant(bo)) > 0;
    }

    /**
     * @param bo PlayletUpdateMerchantInfoBo
     * @return SohuMerchant
     */
    private SohuMerchant convertPlayletUpdateMerchantInfoBoToSohuMerchant(PlayletUpdateMerchantInfoBo bo) {
        SohuMerchant sohuMerchant = new SohuMerchant();
        sohuMerchant.setId(bo.getId());
        sohuMerchant.setUserId(bo.getMerchantUserId());
        sohuMerchant.setName(bo.getMerchantName());
        sohuMerchant.setRealName(bo.getMerchantUserName());
        sohuMerchant.setEmail(bo.getMerchantEmail());
        sohuMerchant.setPhone(bo.getMerchantPhone());
        sohuMerchant.setCategoryId(bo.getMerchantCategoryId());
        sohuMerchant.setHandlingFee(bo.getHandlingFee());
        sohuMerchant.setQualificationPicture(bo.getMerchantQualificationPicture());
        sohuMerchant.setKeywords(bo.getMerchantKeywords());
        sohuMerchant.setTypeId(bo.getMerchantTypeId());
        sohuMerchant.setAddress(bo.getMerchantAddress());
        sohuMerchant.setSort(bo.getSort());
        if (Objects.nonNull(bo.getRemark())) {
            sohuMerchant.setRemark(bo.getRemark());
        }
        if (Objects.nonNull(bo.getMerchantStarLevel())) {
            sohuMerchant.setStarLevel(bo.getMerchantStarLevel());
        }
        if (Objects.nonNull(bo.getProductSwitch())) {
            sohuMerchant.setProductSwitch(bo.getProductSwitch());
        }
        if (Objects.nonNull(bo.getIsSelf())) {
            sohuMerchant.setIsSelf(bo.getIsSelf());
        }

        return sohuMerchant;
    }

    private LambdaQueryWrapper<SohuMerchant> buildQueryWrapper(PlayletMerchantSearchBo bo, boolean flag) {
        LambdaQueryWrapper<SohuMerchant> lqw = Wrappers.lambdaQuery();
        // 时间转换
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            DateTime dateTime = DateUtil.beginOfDay(DateUtil.parseDate(bo.getStartTime()));
            lqw.ge(SohuMerchant::getCreateTime, dateTime);
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            DateTime dateTime = DateUtil.endOfDay(DateUtil.parseDate(bo.getEndTime()));
            lqw.le(SohuMerchant::getCreateTime, dateTime);
        }
        if (StrUtil.isNotEmpty(bo.getMerchantKeywords())) {
            lqw.and(i -> i.like(SohuMerchant::getName, bo.getMerchantKeywords()).or().like(SohuMerchant::getKeywords, bo.getMerchantKeywords()));
        }
        if (flag) {
            lqw.eq(SohuMerchant::getIsSwitch, true);
        }
        lqw.eq(bo.getIsSelf() != null, SohuMerchant::getIsSelf, bo.getIsSelf());
        lqw.eq(StringUtils.isNotBlank(bo.getAuditStatus()), SohuMerchant::getAuditStatus, bo.getAuditStatus());
        lqw.eq(bo.getIsSwitch() != null, SohuMerchant::getIsSwitch, bo.getIsSwitch());
        lqw.eq(bo.getUserId() != null, SohuMerchant::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getSysSource()), SohuMerchant::getSysSource, bo.getSysSource());
        lqw.eq(SohuMerchant::getIsDel, false);
        return lqw;
    }

    public <T extends SohuMerchantBaseVo> T convert(SohuMerchantVo source, Class<T> targetClass) {
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            copyBaseFields(source, target);
            return target;
        } catch (Exception e) {
            throw new RuntimeException("VO 转换失败", e);
        }
    }

    public void copyBaseFields(SohuMerchantVo source, SohuMerchantBaseVo target) {
        target.setMerchantId(source.getId());
        target.setAvatar(source.getAvatar());
        target.setMerchantName(source.getName());
        target.setMerchantType(source.getMerchantType());
        target.setUserId(source.getUserId());
        target.setMerchantPhone(source.getPhone());
        target.setAddress(source.getAddress());
        target.setCitySiteId(source.getCitySiteId());
    }

    /**
     * 根据cateId合并资质
     *
     * @param inputList
     * @return
     */
    public static List<SohuMerchantClassificationQualificationVo> mergeQualifications(List<SohuMerchantClassificationQualificationVo> inputList) {
        if (CollUtil.isEmpty(inputList)) {
            return new ArrayList<SohuMerchantClassificationQualificationVo>();
        }
        Map<Long, SohuMerchantClassificationQualificationVo> mergedMap = new LinkedHashMap<>();

        for (SohuMerchantClassificationQualificationVo item : inputList) {
            Long cateId = item.getCateId();

            // 构建 QualificationVo 对象
            SohuMerchantClassificationQualificationVo.QualificationVo qualificationVo = new SohuMerchantClassificationQualificationVo.QualificationVo();
            qualificationVo.setQualificateId(item.getQualificateId());
            qualificationVo.setQualificateName(item.getQualificateName());
            qualificationVo.setProveUrl(item.getProveUrl());

            if (mergedMap.containsKey(cateId)) {
                mergedMap.get(cateId).getQualificationInfos().add(qualificationVo);
            } else {
                // 创建新类目信息
                SohuMerchantClassificationQualificationVo newVo = new SohuMerchantClassificationQualificationVo();
                newVo.setId(item.getId());
                newVo.setMerId(item.getMerId());
                newVo.setCateId(item.getCateId());
                newVo.setCateName(item.getCateName());
                newVo.setQualificationInfos(new ArrayList<>(Collections.singletonList(qualificationVo)));
                mergedMap.put(cateId, newVo);
            }
        }

        return new ArrayList<>(mergedMap.values());
    }

    /**
     * 根据品牌ID合并资质
     *
     * @param inputList
     * @return
     */
    public static List<SohuMerchantBrandQualificationVo> mergeBrandQualifications(List<SohuMerchantBrandQualificationVo> inputList) {
        if (CollUtil.isEmpty(inputList)) {
            return new ArrayList<SohuMerchantBrandQualificationVo>();
        }
        Map<Long, SohuMerchantBrandQualificationVo> mergedMap = new LinkedHashMap<>();

        for (SohuMerchantBrandQualificationVo item : inputList) {
            Long brandId = item.getBrandId();

            // 构建 QualificationVo 对象
            SohuMerchantBrandQualificationVo.QualificationVo qualificationVo = new SohuMerchantBrandQualificationVo.QualificationVo();
            qualificationVo.setQualificateId(item.getQualificateId());
            qualificationVo.setQualificateName(item.getQualificateName());
            qualificationVo.setProveUrl(item.getProveUrl());

            if (mergedMap.containsKey(brandId)) {
                mergedMap.get(brandId).getQualificationInfos().add(qualificationVo);
            } else {
                // 创建新品牌条目，复制基础字段
                SohuMerchantBrandQualificationVo newVo = new SohuMerchantBrandQualificationVo();
                newVo.setId(item.getId());
                newVo.setMerId(item.getMerId());
                newVo.setBrandId(item.getBrandId());
                newVo.setBrandName(item.getBrandName());
                newVo.setQualificationInfos(new ArrayList<>(Collections.singletonList(qualificationVo)));

                mergedMap.put(brandId, newVo);
            }
        }

        return new ArrayList<>(mergedMap.values());
    }


}
