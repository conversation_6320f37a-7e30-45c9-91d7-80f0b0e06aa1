package com.sohu.admin.service;

import com.sohu.admin.api.bo.SohuMerchantAuditBo;
import com.sohu.admin.api.bo.SohuMerchantCloseStoreQueryBo;
import com.sohu.admin.api.bo.playlet.PlayletAuditMerchantBo;
import com.sohu.admin.api.bo.playlet.PlayletMerchantRemarkBo;
import com.sohu.admin.api.bo.playlet.PlayletMerchantSearchBo;
import com.sohu.admin.api.bo.playlet.PlayletUpdateMerchantInfoBo;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.admin.api.vo.*;
import com.sohu.admin.api.vo.playlet.PlayletMerchantApplyListVo;
import com.sohu.admin.domain.SohuMerchant;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuMerchantApplyBo;
import com.sohu.middle.api.bo.SohuMerchantBo;
import com.sohu.middle.api.bo.SohuMerchantSearchBo;
import com.sohu.middle.api.vo.SohuMerchantVo;

import java.util.Collection;
import java.util.List;

/**
 * 商户-狐少少Service接口
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
public interface ISohuMerchantService {

    /**
     * 查询商户-狐少少
     */
    SohuMerchantVo queryById(Long id);

    /**
     * 查询商户-狐少少列表
     */
    TableDataInfo<SohuMerchantVo> queryPageList(SohuMerchantBo bo, PageQuery pageQuery);

    /**
     * 查询商户-狐少少列表
     */
    List<SohuMerchantVo> queryList(SohuMerchantBo bo);

    /**
     * 修改商户-狐少少
     */
    Boolean insertByBo(SohuMerchantBo bo);

    /**
     * 商户申请
     *
     * @param bo
     * @return
     */
    Boolean applyByBo(SohuMerchantApplyBo bo);

    /**
     * 修改商户-狐少少
     */
    Boolean updateByBo(SohuMerchantBo bo);

    /**
     * 校验并批量删除商户-狐少少信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取用户商户信息
     */
    SohuMerchantVo getInfo(Long id);

    /**
     * 修改商户端商户开关
     */
    Boolean updateSwitch();

    /**
     * 修改用户信息
     */
    Boolean updateInfo(SohuMerchantBo bo);

    /**
     * 商户入驻分页列表
     */
    TableDataInfo<SohuMerchantVo> getPageList(SohuMerchantSearchBo bo, PageQuery pageQuery);

    /**
     * 狐少少入驻审核
     */
    Boolean audit(SohuMerchantBo bo);

    /**
     * 商户开关切换事件
     */
    Boolean updateSwitch(Long id);

    /**
     * 商户推荐切换事件
     */
    Boolean recommend(Long id);

    /**
     * 根据用户和城市站点查询商户
     */
    SohuMerchantVo selectByUserIdAndCitySiteId(Long userId, Long siteId);

    /**
     * 根据用户和国家站点查询商户（flag只查询通过）
     */
    List<SohuMerchantModel> selectByUserIdAndSiteId(Long userId, Long siteId, Boolean flag);

    /**
     * 修改复制次数
     */
    Boolean updateCopyProduct(Long id, String type, Long num);

    /**
     * 用户商户信息-列表
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuMerchantVo> getInfoList(SohuMerchantSearchBo bo, PageQuery pageQuery);

    /**
     * 根据ids查询集合
     *
     * @param merIdList
     */
    List<SohuMerchant> getListByIds(Collection<Long> merIdList);

    /**
     * 通过电话查询
     *
     * @param phone
     * @return {@link SohuMerchantVo}
     */
    SohuMerchantVo getByPhone(String phone);

    /**
     * 根据主订单号批量修改数量
     *
     * @param merchants
     */
    Boolean updateBatch(List<SohuMerchant> merchants);

    /**
     * 商户入驻分页列表--短剧
     *
     * @param bo        PlayletMerchantSearchBo
     * @param pageQuery PageQuery
     * @return TableDataInfo<PlayletMerchantApplyListVo>
     */
    TableDataInfo<PlayletMerchantApplyListVo> getMerchantApplyList(PlayletMerchantSearchBo bo, PageQuery pageQuery, boolean isShowProduct);

    /**
     * 平台修改商户信息
     *
     * @param bo PlayletUpdateMerchantInfoBo
     * @return {@link Boolean}
     */
    Boolean updateMerchantInfo(PlayletUpdateMerchantInfoBo bo);

    /**
     * 新增商户审核备注
     *
     * @param bo PlayletMerchantRemarkBo
     * @return {@link Boolean}
     */
    Boolean addAuditRemark(PlayletMerchantRemarkBo bo);

    /**
     * 商户审核
     *
     * @param bo PlayletAuditMerchantBo
     * @return {@link Boolean}
     */
    Boolean auditMerchant(PlayletAuditMerchantBo bo);

    /**
     * 商城首页推荐店铺
     *
     * @return {@link SohuMerchantVo}
     */
    SohuMerchantVo recommendOne();

    /**
     * 查询所有开启的商户
     *
     * @return {@link List}
     */
    List<SohuMerchantVo> getMerchantOpenList(String sysSource);

    /**
     * 查询商品店铺
     *
     * @param sysSource
     * @return {@link List}
     */
    List<SohuMerchantVo> getProductMerchantList(String sysSource);

    /**
     * 商家闭店审核
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean closeStore(SohuMerchantAuditBo bo);

    /**
     * 分页查询闭店审核列表
     *
     * @param bo        查询入参
     * @param pageQuery 分页参数
     * @return {@link TableDataInfo}
     */
    TableDataInfo<SohuMerchantCloseStoreAuditVo> closeStorePage(SohuMerchantCloseStoreQueryBo bo, PageQuery pageQuery);

    /**
     * 商家经营类目审核详情
     *
     * @param id sohu_merchant_classification主键ID
     * @return {@link SohuMerchantCategoryDetailAuditVo}
     */
    SohuMerchantCategoryDetailAuditVo categoryDetail(Long id);

    /**
     * 商家经营品牌审核详情
     *
     * @param id sohu_merchant_brand_qualification表主键ID
     * @return {@link SohuMerchantBrandDetailAuditVo}
     */
    SohuMerchantBrandDetailAuditVo brandDetail(Long id);

    /**
     * 商家闭店审核详情
     *
     * @param id sohu_merchant表主键ID
     * @return {@link SohuMerchantCloseStoreDetailAuditVo}
     */
    SohuMerchantCloseStoreDetailAuditVo closeStoreDetail(Long id);

    /**
     * 获取商家经营基本信息
     * @param merId 商家ID
     * @return {@link SohuMerchantQualificationInfoVo}
     */
    SohuMerchantQualificationInfoVo getMerchantQualificationInfo(Long merId);

    /**
     * 根据状态查询商家
     * @param status
     * @return
     */
    List<SohuMerchantVo> selectByStatus(List<String> status);


    /**
     * 检测商家名称是否重复
     * @param merchantName
     * @return
     */
    Boolean existMerchantName(String merchantName,Long merchantId);

    /**
     * 处理机审结果
     *
     * @param busyCode
     * @param isPass
     * @param reason
     */
    void handleRobot(String busyCode, Boolean isPass, String reason);
}