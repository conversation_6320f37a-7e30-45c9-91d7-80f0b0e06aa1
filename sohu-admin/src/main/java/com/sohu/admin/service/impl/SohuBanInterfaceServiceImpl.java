package com.sohu.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.bo.SohuBanInterfaceBo;
import com.sohu.admin.api.vo.SohuBanInterfaceVo;
import com.sohu.admin.domain.SohuBanInterface;
import com.sohu.admin.mapper.SohuBanInterfaceMapper;
import com.sohu.admin.service.ISohuBanInterfaceService;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 受控接口资源Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@RequiredArgsConstructor
@Service
public class SohuBanInterfaceServiceImpl implements ISohuBanInterfaceService {

    private final SohuBanInterfaceMapper baseMapper;

    /**
     * 查询受控接口资源
     */
    @Override
    public SohuBanInterfaceVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询受控接口资源列表
     */
    @Override
    public TableDataInfo<SohuBanInterfaceVo> queryPageList(SohuBanInterfaceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuBanInterface> lqw = buildQueryWrapper(bo);
        Page<SohuBanInterfaceVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询受控接口资源列表
     */
    @Override
    public List<SohuBanInterfaceVo> queryList(SohuBanInterfaceBo bo) {
        LambdaQueryWrapper<SohuBanInterface> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuBanInterface> buildQueryWrapper(SohuBanInterfaceBo bo) {
        LambdaQueryWrapper<SohuBanInterface> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getInterfaceName()), SohuBanInterface::getInterfaceName, bo.getInterfaceName());
        lqw.eq(StringUtils.isNotBlank(bo.getInterfaceUrl()), SohuBanInterface::getInterfaceUrl, bo.getInterfaceUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getHttpMethod()), SohuBanInterface::getHttpMethod, bo.getHttpMethod());
        lqw.eq(bo.getRiskWeight() != null, SohuBanInterface::getRiskWeight, bo.getRiskWeight());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), SohuBanInterface::getDescription, bo.getDescription());
        return lqw;
    }

    /**
     * 新增受控接口资源
     */
    @Override
    public Boolean insertByBo(SohuBanInterfaceBo bo) {
        SohuBanInterface add = BeanUtil.toBean(bo, SohuBanInterface.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改受控接口资源
     */
    @Override
    public Boolean updateByBo(SohuBanInterfaceBo bo) {
        SohuBanInterface update = BeanUtil.toBean(bo, SohuBanInterface.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuBanInterface entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除受控接口资源
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
