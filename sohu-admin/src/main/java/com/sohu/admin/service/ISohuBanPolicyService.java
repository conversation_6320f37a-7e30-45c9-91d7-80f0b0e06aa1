package com.sohu.admin.service;

import com.sohu.admin.api.bo.SohuBanPolicyBo;
import com.sohu.admin.api.vo.SohuBanPolicyVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 风控策略核心Service接口
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface ISohuBanPolicyService {

    /**
     * 查询风控策略核心
     */
    SohuBanPolicyVo queryById(Long id);

    /**
     * 查询风控策略核心列表
     */
    TableDataInfo<SohuBanPolicyVo> queryPageList(SohuBanPolicyBo bo, PageQuery pageQuery);

    /**
     * 查询风控策略核心列表
     */
    List<SohuBanPolicyVo> queryList(SohuBanPolicyBo bo);

    /**
     * 修改风控策略核心
     */
    Boolean insertByBo(SohuBanPolicyBo bo);

    /**
     * 修改风控策略核心
     */
    Boolean updateByBo(SohuBanPolicyBo bo);

    /**
     * 校验并批量删除风控策略核心信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
