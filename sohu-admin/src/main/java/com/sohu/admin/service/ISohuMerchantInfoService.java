package com.sohu.admin.service;

import com.sohu.admin.api.model.SohuMerchantInfoModel;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuMerchantInfoBo;
import com.sohu.middle.api.vo.SohuMerchantInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 商户信息Service接口
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
public interface ISohuMerchantInfoService {

    /**
     * 查询商户信息
     */
    SohuMerchantInfoVo queryById(Long id);

    /**
     * 查询商户信息列表
     */
    TableDataInfo<SohuMerchantInfoVo> queryPageList(SohuMerchantInfoBo bo, PageQuery pageQuery);

    /**
     * 查询商户信息列表
     */
    List<SohuMerchantInfoVo> queryList(SohuMerchantInfoBo bo);

    /**
     * 修改商户信息
     */
    Boolean insertByBo(SohuMerchantInfoBo bo);

    /**
     * 修改商户信息
     */
    Boolean updateByBo(SohuMerchantInfoBo bo);

    /**
     * 校验并批量删除商户信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 转账信息修改或添加
     */
    Boolean updateTransfer(SohuMerchantInfoBo bo);

    /**
     * 根据商户id查询商户信息
     * @param merId
     */
    SohuMerchantInfoModel queryByMerId(Long merId);

    /**
     * 处理机审结果
     *
     * @param busyCode
     * @param isPass
     * @param reason
     */
    void handleRobot(String busyCode, Boolean isPass, String reason);

}
