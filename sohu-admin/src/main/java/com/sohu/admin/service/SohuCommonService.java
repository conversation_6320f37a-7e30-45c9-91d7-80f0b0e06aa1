package com.sohu.admin.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.RelationPageBO;
import com.sohu.middle.api.bo.SohuBusyBO;
import com.sohu.middle.api.vo.RelationRespVo;
import com.sohu.system.api.domain.UserVo;

import javax.validation.constraints.NotNull;

public interface SohuCommonService {

    /**
     * 关联内容分页查询
     *
     * @param relationPageVO
     * @return
     */
    TableDataInfo<RelationRespVo> relationPage(RelationPageBO relationPageVO);

    /**
     * 草稿提交至审核
     *
     * @param sohuBusyBO
     */
    void commit(SohuBusyBO sohuBusyBO);


    /**
     * 用户列表
     *
     * @param keyword
     * @param pageQuery
     * @return
     */
    TableDataInfo<UserVo> userPage(String keyword, PageQuery pageQuery);

    Object aiScore(@NotNull(message = "内容不能为空") String content);

}
