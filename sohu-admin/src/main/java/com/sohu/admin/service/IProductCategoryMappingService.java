package com.sohu.admin.service;


import com.sohu.admin.api.bo.ProductCategoryMappingBo;
import com.sohu.admin.api.vo.ProductCategoryMappingVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 商品分类映射Service接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IProductCategoryMappingService {

    /**
     * 查询商品分类映射
     */
    ProductCategoryMappingVo queryById(Long id);

    /**
     * 查询商品分类映射列表
     */
    TableDataInfo<ProductCategoryMappingVo> queryPageList(ProductCategoryMappingBo bo, PageQuery pageQuery);

    /**
     * 查询商品分类映射列表
     */
    List<ProductCategoryMappingVo> queryList(ProductCategoryMappingBo bo);

    /**
     * 修改商品分类映射
     */
    Boolean insertByBo(ProductCategoryMappingBo bo);

    /**
     * 修改商品分类映射
     */
    Boolean updateByBo(ProductCategoryMappingBo bo);

    /**
     * 校验并批量删除商品分类映射信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
