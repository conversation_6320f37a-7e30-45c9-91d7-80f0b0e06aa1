package com.sohu.admin.service;


import com.sohu.admin.api.bo.ThirdPartyBrandBo;
import com.sohu.admin.api.vo.ThirdPartyBrandVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * [多渠道]三方平台商品品牌Service接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IThirdPartyBrandService {

    /**
     * 查询[多渠道]三方平台商品品牌
     */
    ThirdPartyBrandVo queryById(Long id);

    /**
     * 查询[多渠道]三方平台商品品牌列表
     */
    TableDataInfo<ThirdPartyBrandVo> queryPageList(ThirdPartyBrandBo bo, PageQuery pageQuery);

    /**
     * 查询[多渠道]三方平台商品品牌列表
     */
    List<ThirdPartyBrandVo> queryList(ThirdPartyBrandBo bo);

    /**
     * 修改[多渠道]三方平台商品品牌
     */
    Boolean insertByBo(ThirdPartyBrandBo bo);

    /**
     * 修改[多渠道]三方平台商品品牌
     */
    Boolean updateByBo(ThirdPartyBrandBo bo);

    /**
     * 校验并批量删除[多渠道]三方平台商品品牌信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
