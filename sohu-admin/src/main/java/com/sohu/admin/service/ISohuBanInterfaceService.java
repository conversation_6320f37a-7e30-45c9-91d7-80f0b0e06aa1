package com.sohu.admin.service;

import com.sohu.admin.api.bo.SohuBanInterfaceBo;
import com.sohu.admin.api.vo.SohuBanInterfaceVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 受控接口资源Service接口
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface ISohuBanInterfaceService {

    /**
     * 查询受控接口资源
     */
    SohuBanInterfaceVo queryById(Long id);

    /**
     * 查询受控接口资源列表
     */
    TableDataInfo<SohuBanInterfaceVo> queryPageList(SohuBanInterfaceBo bo, PageQuery pageQuery);

    /**
     * 查询受控接口资源列表
     */
    List<SohuBanInterfaceVo> queryList(SohuBanInterfaceBo bo);

    /**
     * 修改受控接口资源
     */
    Boolean insertByBo(SohuBanInterfaceBo bo);

    /**
     * 修改受控接口资源
     */
    Boolean updateByBo(SohuBanInterfaceBo bo);

    /**
     * 校验并批量删除受控接口资源信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
