package com.sohu.admin.service;


import com.sohu.admin.api.bo.SohuPolicyInterfaceBo;
import com.sohu.admin.api.vo.SohuPolicyInterfaceVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 策略与接口关联Service接口
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface ISohuPolicyInterfaceService {

    /**
     * 查询策略与接口关联
     */
    SohuPolicyInterfaceVo queryById(String id);

    /**
     * 查询策略与接口关联列表
     */
    TableDataInfo<SohuPolicyInterfaceVo> queryPageList(SohuPolicyInterfaceBo bo, PageQuery pageQuery);

    /**
     * 查询策略与接口关联列表
     */
    List<SohuPolicyInterfaceVo> queryList(SohuPolicyInterfaceBo bo);

    /**
     * 修改策略与接口关联
     */
    Boolean insertByBo(SohuPolicyInterfaceBo bo);

    /**
     * 修改策略与接口关联
     */
    Boolean updateByBo(SohuPolicyInterfaceBo bo);

    /**
     * 校验并批量删除策略与接口关联信息
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
