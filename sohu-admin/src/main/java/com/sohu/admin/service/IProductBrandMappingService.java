package com.sohu.admin.service;


import com.sohu.admin.api.bo.ProductBrandMappingBo;
import com.sohu.admin.api.vo.ProductBrandMappingVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * [多渠道]商品品牌映射Service接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IProductBrandMappingService {

    /**
     * 查询[多渠道]商品品牌映射
     */
    ProductBrandMappingVo queryById(Long id);

    /**
     * 查询[多渠道]商品品牌映射列表
     */
    TableDataInfo<ProductBrandMappingVo> queryPageList(ProductBrandMappingBo bo, PageQuery pageQuery);

    /**
     * 查询[多渠道]商品品牌映射列表
     */
    List<ProductBrandMappingVo> queryList(ProductBrandMappingBo bo);

    /**
     * 修改[多渠道]商品品牌映射
     */
    Boolean insertByBo(ProductBrandMappingBo bo);

    /**
     * 修改[多渠道]商品品牌映射
     */
    Boolean updateByBo(ProductBrandMappingBo bo);

    /**
     * 校验并批量删除[多渠道]商品品牌映射信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
