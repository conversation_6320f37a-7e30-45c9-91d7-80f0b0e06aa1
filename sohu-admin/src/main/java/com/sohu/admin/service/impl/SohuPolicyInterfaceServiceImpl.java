package com.sohu.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.bo.SohuPolicyInterfaceBo;
import com.sohu.admin.api.vo.SohuPolicyInterfaceVo;
import com.sohu.admin.domain.SohuPolicyInterface;
import com.sohu.admin.mapper.SohuPolicyInterfaceMapper;
import com.sohu.admin.service.ISohuPolicyInterfaceService;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 策略与接口关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@RequiredArgsConstructor
@Service
public class SohuPolicyInterfaceServiceImpl implements ISohuPolicyInterfaceService {

    private final SohuPolicyInterfaceMapper baseMapper;

    /**
     * 查询策略与接口关联
     */
    @Override
    public SohuPolicyInterfaceVo queryById(String id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询策略与接口关联列表
     */
    @Override
    public TableDataInfo<SohuPolicyInterfaceVo> queryPageList(SohuPolicyInterfaceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuPolicyInterface> lqw = buildQueryWrapper(bo);
        Page<SohuPolicyInterfaceVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询策略与接口关联列表
     */
    @Override
    public List<SohuPolicyInterfaceVo> queryList(SohuPolicyInterfaceBo bo) {
        LambdaQueryWrapper<SohuPolicyInterface> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuPolicyInterface> buildQueryWrapper(SohuPolicyInterfaceBo bo) {
        LambdaQueryWrapper<SohuPolicyInterface> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPolicyId()), SohuPolicyInterface::getPolicyId, bo.getPolicyId());
        lqw.eq(StringUtils.isNotBlank(bo.getInterfaceId()), SohuPolicyInterface::getInterfaceId, bo.getInterfaceId());
        lqw.eq(StringUtils.isNotBlank(bo.getRestrictionType()), SohuPolicyInterface::getRestrictionType, bo.getRestrictionType());
        return lqw;
    }

    /**
     * 新增策略与接口关联
     */
    @Override
    public Boolean insertByBo(SohuPolicyInterfaceBo bo) {
        SohuPolicyInterface add = BeanUtil.toBean(bo, SohuPolicyInterface.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改策略与接口关联
     */
    @Override
    public Boolean updateByBo(SohuPolicyInterfaceBo bo) {
        SohuPolicyInterface update = BeanUtil.toBean(bo, SohuPolicyInterface.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuPolicyInterface entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除策略与接口关联
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
