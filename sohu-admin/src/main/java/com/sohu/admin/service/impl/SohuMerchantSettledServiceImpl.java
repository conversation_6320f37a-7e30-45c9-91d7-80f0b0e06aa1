package com.sohu.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sohu.admin.api.bo.*;
import com.sohu.admin.api.bo.merchant.AccountInfo;
import com.sohu.admin.api.bo.merchant.CategoryInfo;
import com.sohu.admin.api.bo.merchant.QualificationInfo;
import com.sohu.admin.api.vo.*;
import com.sohu.admin.domain.SohuMerchant;
import com.sohu.admin.mapper.SohuMerchantMapper;
import com.sohu.admin.service.*;
import com.sohu.busyorder.api.RemoteBusyTaskReceiveService;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuAuditLogBo;
import com.sohu.middle.api.bo.risk.SohuRiskMqBo;
import com.sohu.middle.api.enums.AuditState;
import com.sohu.middle.api.service.RemoteSohuAuditLogService;
import com.sohu.middle.api.service.notice.RemoteMiddleSystemNoticeService;
import com.sohu.middle.api.vo.SohuMerchantVo;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.pay.api.RemoteMerchantBondPayService;
import com.sohu.pay.api.RemoteYmService;
import com.sohu.pay.api.bo.SohuAccountBo;
import com.sohu.pay.api.bo.SohuAccountUpgradeBo;
import com.sohu.pay.api.enums.AccountEnum;
import com.sohu.pay.api.vo.SohuAccountBankVo;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.shopgoods.api.RemoteProductBrandService;
import com.sohu.shopgoods.api.RemoteProductCategoryPcService;
import com.sohu.shopgoods.api.vo.SohuProductBrandVo;
import com.sohu.shopgoods.api.vo.SohuProductCategoryPcVo;
import com.sohu.shoporder.api.RemoteShopOrderService;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemotePlatformRoleService;
import com.sohu.system.api.RemoteSysRoleService;
import com.sohu.system.api.RemoteUserService;
import com.sohu.system.api.domain.SysRole;
import com.wangcaio2o.ipossa.sdk.response.accountbalancequery.AccountBalanceQueryList;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商家入驻服务实现
 *
 * <AUTHOR>
 * @date 2025/4/28 9:38
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class SohuMerchantSettledServiceImpl implements SohuMerchantSettledService {
    private final AsyncConfig asyncConfig;
    private final SohuMerchantMapper sohuMerchantMapper;
    private final ISohuMerchantClassificationQualificationService sohuMerchantClassificationQualificationService;
    private final ISohuMerchantBrandQualificationService sohuMerchantBrandQualificationService;
    private final ISohuMerchantClassificationService sohuMerchantClassificationService;
    private final ISohuMerchantBrandService sohuMerchantBrandService;
    private final ISohuMerchantBondService sohuMerchantBondService;
    private final ISohuMerchantAuditLogService sohuMerchantAuditLogService;
    @DubboReference
    private RemoteAccountService remoteAccountService;
    @DubboReference
    private RemoteYmService remoteYmService;
    @DubboReference
    private RemoteSysRoleService remoteSysRoleService;
    @DubboReference
    private RemotePlatformRoleService remotePlatformRoleService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteBusyTaskReceiveService remoteBusyTaskReceiveService;
    @DubboReference
    private RemoteShopOrderService remoteShopOrderService;
    @DubboReference
    private RemoteProductCategoryPcService remoteProductCategoryPcService;
    @DubboReference
    private RemoteProductBrandService remoteProductBrandService;
    @DubboReference
    private RemoteMiddleSystemNoticeService remoteMiddleSystemNoticeService;
    @DubboReference
    private RemoteMerchantBondPayService remoteMerchantBondPayService;
    @DubboReference
    private RemoteSohuAuditLogService remoteSohuAuditLogService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;

    private final ISohuQualificationService SohuQualificationService;

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean settled(SohuMerchantSettledBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (Objects.isNull(loginUser)) {
            log.warn("当前用户未登录！");
        }
        try {
            //校验店铺名
            checkMerchantName(bo.getName());
            //判定是否已经实名认证通过
            SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserId(loginUser.getUserId());
            boolean existAccount = existAccount(sohuAccountVo, bo.getMerchantType());

            List<SohuMerchantClassificationQualificationBo> cqInfo = new ArrayList<>();
            List<SohuMerchantBrandQualificationBo> bqInfos = new ArrayList<>();
            List<SohuMerchantClassificationBo> classificationInfos = new ArrayList<>();
            List<SohuMerchantBrandBo> brandInfos = new ArrayList<>();
            //保存店铺
            Long merchantId = saveOrUpdateMerchant(bo, null, true, false);
            //封装类目资质对象
            handleCategoryQualificationObject(bo.getCategoryInfos(), merchantId, cqInfo, classificationInfos, CommonState.Pass.getCode());
            //封装品牌资质对象
            handleBrandQualificationObject(bo.getBrandInfos(), merchantId, bqInfos, brandInfos, CommonState.Pass.getCode());
            //保存类目资质
            if (CollUtil.isNotEmpty(classificationInfos)) {
                sohuMerchantClassificationService.batchInsert(classificationInfos);
                sohuMerchantClassificationQualificationService.batchInsert(cqInfo);
            }
            //保存品牌资质
            if (CollUtil.isNotEmpty(brandInfos)) {
                sohuMerchantBrandService.batchInsert(brandInfos);
                sohuMerchantBrandQualificationService.batchInsert(bqInfos);
            }
            if (!existAccount) {
                //异步实名认证
//                CompletableFuture.runAsync(() -> {
                SohuAccountBo accountBo = BeanUtil.copyProperties(bo.getAccountInfo(), SohuAccountBo.class);
                accountBo.setUserId(loginUser.getUserId());
                accountBo.setNeedMsgNotify(true);
                accountBo.setMerId(merchantId);
                remoteAccountService.insertByBo(accountBo);
//                }, asyncConfig.getAsyncExecutor());
            }
            //记载审核记录表
            sohuMerchantAuditLogService.save(merchantId, bo.getMerchantType(), CommonState.WaitApprove.getCode(), null);
        } catch (Exception e) {
            log.error("商户入驻异常！", e);
            return false;
        }
        return true;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean settledEdit(SohuMerchantSettledBo bo, Boolean isUpgrade) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (Objects.isNull(loginUser)) {
            log.warn("当前用户未登录！");
        }
        //判定店铺是否存在,审核不通过才可编辑
        SohuMerchantVo sohuMerchatVo = sohuMerchantMapper.selectVoById(bo.getId());
        if (Objects.isNull(sohuMerchatVo) ||
                (!List.of(CommonState.Refuse.getCode(), CommonState.AuthFail.getCode()).contains(sohuMerchatVo.getAuditStatus())) && !isUpgrade) {
            throw new ServiceException("该商户不可编辑");
        }
        long merchantId = sohuMerchatVo.getId();
        //校验店铺名
        if (!isUpgrade) {
            checkMerchantName(bo.getName());
        }
        //判定是否已经实名认证通过
        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserId(loginUser.getUserId());
        boolean existAccount = existAccount(sohuAccountVo, bo.getMerchantType());
        //封装类目资质对象
        List<SohuMerchantClassificationQualificationBo> cqInfo = new ArrayList<>();
        List<SohuMerchantBrandQualificationBo> bqInfos = new ArrayList<>();
        List<SohuMerchantClassificationBo> classificationInfos = new ArrayList<>();
        List<SohuMerchantBrandBo> brandInfos = new ArrayList<>();
        if (CollUtil.isNotEmpty(bo.getCategoryInfos())) {
            //删除历史类目资质信息
            sohuMerchantClassificationService.deleteByMerchantId(bo.getId());
            sohuMerchantClassificationQualificationService.deleteByMerchantId(bo.getId());
            //封装类目资质对象
            handleCategoryQualificationObject(bo.getCategoryInfos(), merchantId, cqInfo, classificationInfos, CommonState.Pass.getCode());
        }
        if (CollUtil.isNotEmpty(bo.getBrandInfos())) {
            //删除历史品牌资质信息
            sohuMerchantBrandService.deleteByMerchantId(bo.getId());
            sohuMerchantBrandQualificationService.deleteByMerchantId(bo.getId());
            //封装品牌资质对象
            handleBrandQualificationObject(bo.getBrandInfos(), merchantId, bqInfos, brandInfos, CommonState.Pass.getCode());
        }
        //保存类目资质
        if (CollUtil.isNotEmpty(classificationInfos)) {
            sohuMerchantClassificationService.batchInsert(classificationInfos);
            sohuMerchantClassificationQualificationService.batchInsert(cqInfo);
        }
        //保存品牌资质
        if (CollUtil.isNotEmpty(brandInfos)) {
            sohuMerchantBrandService.batchInsert(brandInfos);
            sohuMerchantBrandQualificationService.batchInsert(bqInfos);
        }
        //更新店铺信息
        saveOrUpdateMerchant(bo, bo.getId(), false, isUpgrade);
        if (!existAccount && !isUpgrade) {
            //异步实名认证
            CompletableFuture.runAsync(() -> {
                SohuAccountBo accountBo = BeanUtil.copyProperties(bo.getAccountInfo(), SohuAccountBo.class);
                accountBo.setUserId(loginUser.getUserId());
                accountBo.setNeedMsgNotify(true);
                accountBo.setMerId(merchantId);
                remoteAccountService.insertByBo(accountBo);
            }, asyncConfig.getAsyncExecutor());
        }
        //记载审核记录表
        sohuMerchantAuditLogService.save(merchantId, bo.getMerchantType(), CommonState.WaitApprove.getCode(), null);
        return true;
    }

    @Override
    public SohuMerchantSettledVo detail(Long id) {
        //查询店铺基础信息
        SohuMerchantVo sohuMerchatVo = sohuMerchantMapper.selectVoById(id);
        Objects.requireNonNull(sohuMerchatVo, "店铺不存在");
        if (CommonState.CloseApprove.getCode().equals(sohuMerchatVo.getCloseStatus())) {
            sohuMerchatVo.setAuditStatus(CommonState.CloseApprove.getCode());
        }
        if (CommonState.ClosePass.getCode().equals(sohuMerchatVo.getCloseStatus())) {
            sohuMerchatVo.setAuditStatus(CommonState.ClosePass.getCode());
        }
        //查询实名入驻信息
        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserId(sohuMerchatVo.getUserId());
        //查询用户信息
        LoginUser loginUser = remoteUserService.queryByUserId(sohuMerchatVo.getUserId());
        //查询类目资质信息
        List<SohuMerchantClassificationQualificationVo> cqInfos = sohuMerchantClassificationQualificationService.selectListByMerchantId(id);
        //查询品牌资质信息
        List<SohuMerchantBrandQualificationVo> bqInfos = sohuMerchantBrandQualificationService.selectListByMerchantId(id);
        SohuMerchantSettledVo vo = BeanUtil.copyProperties(sohuMerchatVo, SohuMerchantSettledVo.class);
        AccountInfo accountInfo = new AccountInfo();
        if (Objects.nonNull(sohuAccountVo)) {
            accountInfo = BeanUtil.copyProperties(sohuAccountVo, AccountInfo.class);
        }
        accountInfo.setBindPhone(Objects.nonNull(loginUser) ? loginUser.getPhoneNumber() : "");
        vo.setAccountInfo(accountInfo);
        List<CategoryInfo> categoryInfos = new ArrayList<>();
        List<CategoryInfo> brandInfos = new ArrayList<>();
        if (CollUtil.isNotEmpty(cqInfos)) {
            Map<Long, List<SohuMerchantClassificationQualificationVo>> cqMap = cqInfos.stream().collect(Collectors.groupingBy(SohuMerchantClassificationQualificationVo::getCateId));
            List<SohuMerchantClassificationVo> classificationVos = sohuMerchantClassificationService.selectListByCateIds(id, cqMap.keySet());
            if (CollUtil.isNotEmpty(classificationVos)) {
                List<Long> cateIds = new ArrayList<>();
                Map<Long, SohuMerchantClassificationVo> classificationMap = new HashMap<>();
                classificationVos.forEach(item -> {
                    classificationMap.put(item.getCateId(), item);
                    cateIds.add(item.getCateId());
                    if (item.getParentId() != null && item.getParentId() != 0) {
                        cateIds.add(item.getParentId());
                    }
                });
                List<SohuProductCategoryPcVo> pcCateList = remoteProductCategoryPcService.selectByIds(cateIds);
                Map<Long, SohuProductCategoryPcVo> pcCateMap = pcCateList.stream().collect(Collectors.toMap(SohuProductCategoryPcVo::getId, Function.identity()));
                cqMap.forEach((k, v) -> {
                    CategoryInfo categoryInfo = new CategoryInfo();
                    categoryInfo.setCateId(k);
                    categoryInfo.setCateName(pcCateMap.get(k).getName());
                    if (classificationMap.get(k).getParentId() != null && classificationMap.get(k).getParentId() != 0) {
                        categoryInfo.setParentId(classificationMap.get(k).getParentId());
                        categoryInfo.setParentName(pcCateMap.get(classificationMap.get(k).getParentId()).getName());
                    }
                    categoryInfo.setState(classificationMap.get(k).getState());
                    categoryInfo.setEnable(classificationMap.get(k).getEnable());
                    categoryInfo.setQualificationInfos(BeanUtil.copyToList(v, QualificationInfo.class));
                    categoryInfos.add(categoryInfo);
                });
            }
        }
        if (CollUtil.isNotEmpty(bqInfos)) {
            Map<Long, List<SohuMerchantBrandQualificationVo>> bqMap = bqInfos.stream().collect(Collectors.groupingBy(SohuMerchantBrandQualificationVo::getBrandId));
            List<SohuMerchantBrandVo> brandVos = sohuMerchantBrandService.selectListByCateIds(id, bqMap.keySet());
            if (CollUtil.isNotEmpty(brandVos)) {
                List<Long> brandIds = new ArrayList<>();
                Map<Long, SohuMerchantBrandVo> brandMap = new HashMap<>();
                brandVos.forEach(item -> {
                    brandIds.add(item.getBrandId());
                    brandMap.put(item.getBrandId(), item);
                });
                List<SohuProductBrandVo> brandList = remoteProductBrandService.list(brandIds);
                Map<Long, SohuProductBrandVo> brandVoMap = brandList.stream().collect(Collectors.toMap(SohuProductBrandVo::getId, Function.identity()));
                bqMap.forEach((k, v) -> {
                    CategoryInfo categoryInfo = new CategoryInfo();
                    categoryInfo.setCateId(brandVoMap.get(k).getCateId());
                    categoryInfo.setBrandId(k);
                    categoryInfo.setCateName(brandVoMap.get(k).getName());
                    categoryInfo.setIcon(brandVoMap.get(k).getIcon());
                    categoryInfo.setState(brandMap.get(k).getState());
                    categoryInfo.setEnable(brandMap.get(k).getEnable());
                    categoryInfo.setQualificationInfos(BeanUtil.copyToList(v, QualificationInfo.class));
                    brandInfos.add(categoryInfo);
                });
            }
        }
        //保证金封装
        boolean hasMerchantBondPay = sohuMerchantBondService.checkMerchantBondPayStatus(id);
        vo.setHasMerchantBondPay(hasMerchantBondPay);
        vo.setCategoryInfos(categoryInfos);
        vo.setBrandInfos(brandInfos);
        return vo;
    }

    @Override
    public SohuUserCondVo getUserCond(Long userId) {
        //查询用户余额
        int balance = getYmBalance(userId);
        //查询在途商单(能分账的订单)
        List<String> taskStates = Arrays.asList(SohuBusyTaskState.WaitPay.name(),
                SohuBusyTaskState.WaitApprove.name(),
                SohuBusyTaskState.ReceiveTask.name(),
                SohuBusyTaskState.WaitApproveSettle.name(),
                SohuBusyTaskState.WaitSettle.name(),
                SohuBusyTaskState.Execute.name(),
                SohuBusyTaskState.WaitIndependentPay.name(),
                SohuBusyTaskState.WaitFullAmountPay.name(),
                SohuBusyTaskState.WaitPromisePay.name());
        long taskCount = remoteBusyTaskReceiveService.inTransitTask(userId, taskStates, new Date());
        //查询该用户下所有的店铺
        Long shopCount = 0L;
        List<SohuMerchant> sohuMerchants = sohuMerchantMapper.selectList(new LambdaQueryWrapper<SohuMerchant>()
                .eq(SohuMerchant::getUserId, userId));
        if (CollUtil.isNotEmpty(sohuMerchants)) {
            List<String> validShopstates = Arrays.asList(OrderConstants.MERCHANT_ORDER_STATUS_UNPAID, OrderConstants.ORDER_STATUS_SHIPPING, OrderConstants.ORDER_STATUS_AWAIT_RECEIVING, OrderConstants.ORDER_STATUS_RECEIVE);
            shopCount = remoteShopOrderService.inTransitOrder(sohuMerchants.stream().map(SohuMerchant::getId).collect(Collectors.toList()), validShopstates);
        }
        long orderCount = taskCount + shopCount;
        SohuUserCondVo vo = new SohuUserCondVo();
        vo.setBalance(CalUtils.centToYuan(BigDecimal.valueOf(balance)));
        vo.setOrderCount(orderCount);
        return vo;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean upgrage(SohuMerchantSettledBo upgradeBo) {
        SohuMerchantVo sohuMerchatVo = sohuMerchantMapper.selectVoById(upgradeBo.getId());
        if (Objects.isNull(sohuMerchatVo) || !sohuMerchatVo.getAuditStatus().equals(CommonState.Pass.getCode()) || !sohuMerchatVo.getMerchantType().equals(AccountEnum.AccountTypeEnum.PERSONAL.getCode())) {
            throw new ServiceException("状态异常，当前商户不可升级");
        }
        //校验是否存在余额与在途订单
//        SohuUserCondVo userCondVo = getUserCond(LoginHelper.getUserId());
//        if (userCondVo.getBalance().compareTo(BigDecimal.ZERO) > 0 || userCondVo.getOrderCount() > 0) {
//            throw new ServiceException("当前用户存在余额或在途订单，请先处理");
//        }
        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserId(LoginHelper.getUserId());
        Objects.requireNonNull(sohuAccountVo, "实名信息不存在");
        //升级账户
        SohuAccountUpgradeBo accountUpgradeBo = BeanUtil.copyProperties(upgradeBo.getAccountInfo(), SohuAccountUpgradeBo.class);
        accountUpgradeBo.setMerId(sohuMerchatVo.getId());
        accountUpgradeBo.setId(sohuAccountVo.getId());
        remoteAccountService.upgrage(accountUpgradeBo);
        //修改店铺状态
        upgradeBo.setIsUpgrage(Constants.ONE);
        settledEdit(upgradeBo, true);
        //冻结该用户名下的商户
        sohuMerchantMapper.freezeMerchant(LoginHelper.getUserId(), upgradeBo.getId());
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateMerchantStatus(Long userId, Long merId, String status, String reason) {
        SohuMerchant sohuMerchant = sohuMerchantMapper.selectById(merId);
        Objects.requireNonNull(sohuMerchant, "商户不存在");
        //更新商户状态
        sohuMerchant.setAuditStatus(status);
        sohuMerchant.setDenialReason(reason);
        sohuMerchantMapper.updateById(sohuMerchant);
        // 发送系统入驻失败通知
        CompletableFuture.runAsync(
                () -> remoteMiddleSystemNoticeService.sendSystemNotice(userId, merId, SysNoticeEnum.MERCHANT_ENTER_FAIL, Arrays.asList(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN), reason)),
                asyncConfig.getAsyncExecutor()
        );
        //记录审核日志
        sohuMerchantAuditLogService.save(sohuMerchant.getId(), sohuMerchant.getMerchantType(), status, reason);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addClass(SohuMerchantCateBo bo) {
        List<SohuMerchantClassificationQualificationBo> cqInfo = new ArrayList<>();
        List<SohuMerchantClassificationBo> classificationInfos = new ArrayList<>();
        SohuMerchantVo sohuMerchatVo = sohuMerchantMapper.selectVoById(bo.getId());
        if (Objects.nonNull(sohuMerchatVo) && sohuMerchatVo.getAuditStatus().equals(CommonState.Pass.getCode()) &&
                !Arrays.asList(CommonState.CloseApprove.getCode(), CommonState.ClosePass.getCode()).contains(sohuMerchatVo.getCloseStatus())) {
            //封装类目资质对象
            handleCategoryQualificationObject(bo.getCateInfos(), bo.getId(), cqInfo, classificationInfos, CommonState.WaitApprove.getCode());
            //保存类目资质
            if (CollUtil.isNotEmpty(classificationInfos)) {
                Set<Long> cateIds = classificationInfos.stream().map(SohuMerchantClassificationBo::getCateId).collect(Collectors.toSet());
                List<SohuMerchantClassificationVo> existList = sohuMerchantClassificationService.selectListByCateIds(bo.getId(), cateIds);
                if (CollUtil.isNotEmpty(existList)) {
                    throw new ServiceException("类目已存在");
                }
                sohuMerchantClassificationService.batchInsert(classificationInfos);
                sohuMerchantClassificationQualificationService.batchInsert(cqInfo);
            }
        } else {
            throw new ServiceException("当前商户状态异常，请稍后再试");
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean addBrand(SohuMerchantCateBo bo) {
        List<SohuMerchantBrandQualificationBo> bqInfos = new ArrayList<>();
        List<SohuMerchantBrandBo> brandInfos = new ArrayList<>();
        SohuMerchantVo sohuMerchatVo = sohuMerchantMapper.selectVoById(bo.getId());
        if (Objects.nonNull(sohuMerchatVo) && sohuMerchatVo.getAuditStatus().equals(CommonState.Pass.getCode()) &&
                !Arrays.asList(CommonState.CloseApprove.getCode(), CommonState.ClosePass.getCode()).contains(sohuMerchatVo.getCloseStatus())) {
            //封装品牌资质对象
            handleBrandQualificationObject(bo.getCateInfos(), bo.getId(), bqInfos, brandInfos, CommonState.WaitApprove.getCode());
            //保存品牌资质
            if (CollUtil.isNotEmpty(brandInfos)) {
                Set<Long> cateIds = brandInfos.stream().map(SohuMerchantBrandBo::getBrandId).collect(Collectors.toSet());
                List<SohuMerchantBrandVo> existList = sohuMerchantBrandService.selectListByCateIds(bo.getId(), cateIds);
                if (CollUtil.isNotEmpty(existList)) {
                    throw new ServiceException("品牌已存在");
                }
                sohuMerchantBrandService.batchInsert(brandInfos);
                sohuMerchantBrandQualificationService.batchInsert(bqInfos);
            }
        } else {
            throw new ServiceException("当前商户状态异常，请稍后再试");
        }
        return Boolean.TRUE;
    }

    @Override
    public TableDataInfo<SohuMerchantAdminVo> queryPageList(SohuMerchantAdminBo bo, PageQuery pageQuery) {
        IPage<SohuMerchantAdminVo> page = sohuMerchantMapper.getPageList(PageQueryUtils.build(pageQuery), bo);
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<Long> merIds = page.getRecords().stream().map(SohuMerchantAdminVo::getId).collect(Collectors.toList());
            Map<Long, List<String>> cateMap = sohuMerchantClassificationService.getCateNamesGroupById(merIds, Arrays.asList(CommonState.Pass.getCode()), Constants.ONE);
            Map<Long, List<String>> brandMap = sohuMerchantBrandService.getBrandNamesGroupById(merIds, Arrays.asList(CommonState.Pass.getCode()), Constants.ONE);
            page.getRecords().forEach(item -> {
                item.setCateNames(cateMap.get(item.getId()));
                item.setBrandNames(brandMap.get(item.getId()));
            });
        }
        return TableDataInfoUtils.build(page);
    }

    @Override
    public Boolean audit(SohuMerchantAuditBo bo) {
        //校验
        boolean status = false;
        SohuMerchant sohuMerchant = sohuMerchantMapper.selectById(bo.getId());
        if (!sohuMerchant.getAuditStatus().equals(AuditState.WaitApprove.name())) {
            throw new ServiceException(MessageUtils.message("already.audit"));
        }
        Long userId = LoginHelper.getUserId();
        Integer isUpgrage = sohuMerchant.getIsUpgrage();
        if (bo.getAuditStatus().equals(AuditState.Refuse.name())) {
            sohuMerchant.setDenialReason(bo.getDenialReason());
        } else {
            sohuMerchant.setDenialReason("");
            status = true;
            if (isUpgrage == Constants.ONE) {
                sohuMerchant.setIsSwitch(Boolean.TRUE);
            }
        }
        //更新店铺信息
        sohuMerchant.setAuditStatus(bo.getAuditStatus());
        sohuMerchant.setAuditorId(userId);
        boolean flag = sohuMerchantMapper.updateById(sohuMerchant) > 0;
        if (flag && status) {
            //给用户分配商户角色
            List<SysRole> roleDOS = remoteSysRoleService.listByRoleCodes(Collections.singletonList(RoleCodeEnum.SHOP.getCode()));
            if (CollUtil.isEmpty(roleDOS)) {
                throw new ServiceException("商户角色不存在");
            }
            //获取申请用户id
            remoteSysRoleService.insertList(roleDOS, sohuMerchant.getUserId());
            remotePlatformRoleService.insertUserRole(PlatformRoleCodeEnum.SHOP.getCode(), sohuMerchant.getUserId());
            //刷新用户缓存
            remoteUserService.flushLoginCacheByUserId(sohuMerchant.getUserId());
            CompletableFuture.runAsync(() -> {
                if (isUpgrage == Constants.ONE) {
                    // 发送升级通过通知
                    remoteMiddleSystemNoticeService.sendSystemNotice(sohuMerchant.getUserId(), sohuMerchant.getId(), SysNoticeEnum.MERCHANT_UPGRADE_SUCCESS, null);
                } else {
                    // 发送入驻通过通知
                    remoteMiddleSystemNoticeService.sendSystemNotice(sohuMerchant.getUserId(), sohuMerchant.getId(), SysNoticeEnum.MERCHANT_ENTER_SUCCESS, Arrays.asList(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN)));
                }
            }, asyncConfig.getAsyncExecutor());
            //处理升级店铺逻辑
            merchantUpgradeHandler(sohuMerchant.getUserId());
        } else {
            CompletableFuture.runAsync(() -> {
                if (isUpgrage == Constants.ONE) {
                    // 发送升级失败通知
                    remoteMiddleSystemNoticeService.sendSystemNotice(sohuMerchant.getUserId(), sohuMerchant.getId(), SysNoticeEnum.MERCHANT_UPGRADE_FAIL, Arrays.asList(bo.getDenialReason()));
                } else {
                    // 发送入驻失败通知
                    remoteMiddleSystemNoticeService.sendSystemNotice(sohuMerchant.getUserId(), sohuMerchant.getId(), SysNoticeEnum.MERCHANT_ENTER_FAIL, Arrays.asList(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN), bo.getDenialReason()));
                }
            }, asyncConfig.getAsyncExecutor());
        }
        //记录审核日志
        sohuMerchantAuditLogService.save(sohuMerchant.getId(), sohuMerchant.getMerchantType(), bo.getAuditStatus(), bo.getDenialReason());
        return flag;
    }

    @Override
    public Boolean close(Long id) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        SohuMerchantVo sohuMerchantVo = sohuMerchantMapper.selectVoById(id);
        Objects.requireNonNull(sohuMerchantVo, "店铺不存在");
        if (!loginUser.getUserId().equals(sohuMerchantVo.getUserId())) {
            throw new ServiceException("无权限操作");
        }
        //校验是否存在余额与在途订单
        SohuUserCondVo userCondVo = getUserCond(LoginHelper.getUserId());
        if (userCondVo.getBalance().compareTo(BigDecimal.ZERO) > 0 || userCondVo.getOrderCount() > 0) {
            throw new ServiceException("当前用户存在余额或在途订单，请先处理");
        }
        //保证金封装
        boolean hasMerchantBondPay = sohuMerchantBondService.checkMerchantBondPayStatus(id);
        if (hasMerchantBondPay) {
            throw new ServiceException("当前用户存在待支付保证金，请先处理");
        }
        this.closeEntity(id);
        return Boolean.TRUE;
    }

    private void closeEntity(Long merchantId) {
        LambdaUpdateWrapper<SohuMerchant> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SohuMerchant::getId, merchantId)
                .set(SohuMerchant::getIsSwitch, Boolean.FALSE)
                .set(SohuMerchant::getCloseStatus, CommonState.CloseApprove.getCode());
        sohuMerchantMapper.update(null, updateWrapper);
        SohuAuditLogBo auditLogBo = SohuAuditLogBo.busy(AuditLogBusyType.MERCHANT_CLOSE_AUDIT, merchantId);
        auditLogBo.setState(CommonState.CloseApprove.getCode());
        auditLogBo.setBusyTitle(AuditLogBusyType.WAIT_APPROVE);
        remoteSohuAuditLogService.insertByBo(auditLogBo);
    }

    @Override
    public List<SohuMerchantBondVo> bondList(Long id) {
        List<SohuMerchantBondVo> list = sohuMerchantBondService.selectListByStatus(id, List.of(PayStatus.WaitPay.name(), CommonState.Paid.getCode()));
        if (CollUtil.isNotEmpty(list)) {
            //查询类目名称
            Map<Long, String> categoryNameMap = remoteProductCategoryPcService.buildCategoryNames(list.stream().map(SohuMerchantBondVo::getCateId).collect(Collectors.toList()));
            for (SohuMerchantBondVo merchantBondVo : list) {
                merchantBondVo.setWaitPayAmount(CalUtils.sub(merchantBondVo.getBondAmount(), merchantBondVo.getPaidAmount()));
                if (Objects.nonNull(categoryNameMap.get(merchantBondVo.getCateId()))) {
                    merchantBondVo.setCateName(categoryNameMap.get(merchantBondVo.getCateId()));
                }
            }
        }
        return list;
    }

    @Override
    public Boolean merchantUpgradeHandler(Long userId) {
        //查询实名信息
        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserId(userId);
        log.info("查询实名信息{}", JSONObject.toJSONString(sohuAccountVo));
        if (Objects.nonNull(sohuAccountVo) && !sohuAccountVo.getState().equals(CommonState.Pass.getCode())) {
            log.info("实名信息未通过，不处理店铺升级,用户id：{}", userId);
            return Boolean.TRUE;
        }
        //查询用户下所有冻结的店铺
        List<SohuMerchant> sohuMerchants = sohuMerchantMapper.selectList(new LambdaQueryWrapper<SohuMerchant>()
                .eq(SohuMerchant::getUserId, userId)
                .eq(SohuMerchant::getAuditStatus, CommonState.FreezeUpgrade.getCode()));
        log.info("查询用户下所有冻结的店铺{}", JSONObject.toJSONString(sohuMerchants));
        if (CollUtil.isEmpty(sohuMerchants)) {
            log.info("用户下没有冻结的店铺，不处理店铺升级,用户id：{}", userId);
            return Boolean.TRUE;
        }
        for (SohuMerchant sohuMerchant : sohuMerchants) {
            //判断是否是升级店铺，是，则直接升级，否，判断店铺类目资质是否符合企业级，是，升级为企业店铺，否，走闭店流程
            if (sohuMerchant.getIsUpgrage() == Constants.ONE) {
                sohuMerchant.setAuditStatus(CommonState.Pass.getCode());
                sohuMerchant.setIsSwitch(true);
                sohuMerchantMapper.updateById(sohuMerchant);
            } else {
                //查询店铺类目与品牌
                List<SohuMerchantClassificationQualificationVo> cqInfos = sohuMerchantClassificationQualificationService.selectListByMerchantId(sohuMerchant.getId());
                List<SohuMerchantBrandQualificationVo> bqInfos = sohuMerchantBrandQualificationService.selectListByMerchantId(sohuMerchant.getId());
                List<Long> cateIds = cqInfos.stream().map(SohuMerchantClassificationQualificationVo::getCateId).collect(Collectors.toList());
                List<Long> brandIds = bqInfos.stream().map(SohuMerchantBrandQualificationVo::getBrandId).collect(Collectors.toList());
                log.info("查询店铺类目与品牌{}", JSONObject.toJSONString(cqInfos));
                log.info("查询店铺类目与品牌{}", JSONObject.toJSONString(bqInfos));
                //查询企业类目与品牌资质
                List<SohuQualificationVo> cateQ = getQualificationList("Category", cateIds);
                log.info("查询企业类目与品牌资质{}", JSONObject.toJSONString(cateQ));
//                List<SohuQualificationVo> brandQ = getQualificationList("Brand", brandIds);
//                log.info("查询企业类目与品牌资质{}", JSONObject.toJSONString(brandQ));
                if (cqInfos.size() == cateQ.size()) {
                    sohuMerchant.setMerchantType("business");
                    sohuMerchant.setAuditStatus(CommonState.Pass.getCode());
                    sohuMerchantMapper.updateById(sohuMerchant);
                } else {
                    this.closeEntity(sohuMerchant.getId());
                }
            }
        }
        return Boolean.TRUE;
    }

    private void checkMerchantName(String merchantName) {
        SohuMerchant sohuMerchant = sohuMerchantMapper.selectOne(new LambdaQueryWrapper<SohuMerchant>()
                .eq(SohuMerchant::getName, merchantName)
                .in(SohuMerchant::getAuditStatus, List.of(CommonState.Pass.getCode(), CommonState.WaitApprove.getCode()))
                .last("limit 1"));
        if (Objects.nonNull(sohuMerchant)) {
            throw new ServiceException("商家名称已存在");
        }
    }

    private Boolean existAccount(SohuAccountVo sohuAccountVo, String merchantType) {
        boolean existAccount = false;
        if (Objects.nonNull(sohuAccountVo)) {
            if (sohuAccountVo.getState().equals(CommonState.Pass.getCode())) {
                existAccount = true;
                if (List.of("business", "person_business").contains(merchantType)) {
                    merchantType = AccountEnum.AccountTypeEnum.BUSINESS.getCode();
                    log.info("商户类型={}", merchantType);
                }
                if (!merchantType.equals(sohuAccountVo.getAccountType())) {
                    throw new ServiceException("商家类型与实名认证类型不一致");
                }
            }
        }
        return existAccount;
    }

    private Long saveOrUpdateMerchant(SohuMerchantSettledBo bo, Long id, Boolean isSave, Boolean isUpgrade) {
        SohuMerchant sohuMerchant = new SohuMerchant();
        sohuMerchant.setName(bo.getName());
        sohuMerchant.setMerchantType(bo.getMerchantType());
        sohuMerchant.setRealName(StrUtil.isNotEmpty(bo.getAccountInfo().getContactName()) ? bo.getAccountInfo().getContactName() : bo.getAccountInfo().getLegalName());
        sohuMerchant.setEmail(bo.getAccountInfo().getContactEmail());
        sohuMerchant.setPhone(bo.getAccountInfo().getContactPhone());
        if (isUpgrade) {
            //判断是否绑定银行卡
            List<SohuAccountBankVo> accountBankVos = remoteAccountService.queryPassListByUserId(Arrays.asList(LoginHelper.getUserId()));
            if (CollUtil.isEmpty(accountBankVos)) {
                sohuMerchant.setAuditStatus(CommonState.WaitRobotApprove.getCode());
            } else {
                sohuMerchant.setAuditStatus(CommonState.FreezeUpgrade.getCode());
            }
            sohuMerchant.setIsSwitch(false);
        } else {
            sohuMerchant.setAuditStatus(CommonState.WaitRobotApprove.getCode());
        }
        sohuMerchant.setUserId(LoginHelper.getUserId());
        sohuMerchant.setIsUpgrage(bo.getIsUpgrage());
        if (isSave) {
            sohuMerchant.setSiteId(5L);
            sohuMerchant.setCitySiteId(11L);
            sohuMerchantMapper.insert(sohuMerchant);
        } else {
            sohuMerchant.setId(id);
            sohuMerchantMapper.updateById(sohuMerchant);
        }
        if (StrUtil.equalsAnyIgnoreCase(sohuMerchant.getAuditStatus(), CommonState.WaitRobotApprove.name())) {
            // 发布异步机审消息
            SohuRiskMqBo riskMqBo = new SohuRiskMqBo();
            riskMqBo.setBusyType(BusyType.Shop);
            riskMqBo.setBusyCode(sohuMerchant.getId().toString());
            MqMessaging mqMessaging = new MqMessaging(JSONObject.toJSONString(riskMqBo), MqKeyEnum.HANDLE_RISK_MANAGEMENT.getKey());
            remoteStreamMqService.sendDelayMsg(mqMessaging, 2L);
        }
        return sohuMerchant.getId();
    }

    private void handleCategoryQualificationObject(List<CategoryInfo> categoryInfos, Long merchantId, List<SohuMerchantClassificationQualificationBo> cqInfos,
                                                   List<SohuMerchantClassificationBo> classificationInfos, String state) {
        if (CollUtil.isNotEmpty(categoryInfos)) {
            for (CategoryInfo categoryInfo : categoryInfos) {
                Long cateId = categoryInfo.getCateId();
                String relateNo = IdUtil.fastSimpleUUID();
                for (QualificationInfo qualificationInfo : categoryInfo.getQualificationInfos()
                ) {
                    SohuMerchantClassificationQualificationBo categoryQualificationBo = new SohuMerchantClassificationQualificationBo();
                    categoryQualificationBo.setMerId(merchantId);
                    categoryQualificationBo.setCateId(cateId);
                    categoryQualificationBo.setQualificateId(qualificationInfo.getQualificateId());
                    categoryQualificationBo.setQualificateName(qualificationInfo.getQualificateName());
                    categoryQualificationBo.setProveUrl(qualificationInfo.getProveUrl());
                    categoryQualificationBo.setRelateNo(relateNo);
                    cqInfos.add(categoryQualificationBo);
                }
                SohuMerchantClassificationBo classificationBo = new SohuMerchantClassificationBo();
                classificationBo.setMerId(merchantId);
                classificationBo.setParentId(categoryInfo.getParentId());
                classificationBo.setCateId(cateId);
                classificationBo.setState(state);
                classificationBo.setRelateNo(relateNo);
                if (state.equals(CommonState.Pass.getCode())) {
                    classificationBo.setType(Constants.ONE);
                }
                classificationInfos.add(classificationBo);
            }
        }
    }

    private void handleBrandQualificationObject(List<CategoryInfo> categoryInfos, Long merchantId, List<SohuMerchantBrandQualificationBo> bqInfos, List<SohuMerchantBrandBo> brandInfos, String state) {
        if (CollUtil.isNotEmpty(categoryInfos)) {
            for (CategoryInfo categoryInfo : categoryInfos) {
                Long cateId = categoryInfo.getCateId();
                Long brandId = categoryInfo.getBrandId();
                String relateNo = IdUtil.fastSimpleUUID();
                for (QualificationInfo qualificationInfo : categoryInfo.getQualificationInfos()
                ) {
                    SohuMerchantBrandQualificationBo brandQualificationBo = new SohuMerchantBrandQualificationBo();
                    brandQualificationBo.setMerId(merchantId);
                    brandQualificationBo.setCateId(cateId);
                    brandQualificationBo.setBrandId(brandId);
                    brandQualificationBo.setQualificateId(qualificationInfo.getQualificateId());
                    brandQualificationBo.setQualificateName(qualificationInfo.getQualificateName());
                    brandQualificationBo.setProveUrl(qualificationInfo.getProveUrl());
                    brandQualificationBo.setRelateNo(relateNo);
                    bqInfos.add(brandQualificationBo);
                }
                SohuMerchantBrandBo brandBo = new SohuMerchantBrandBo();
                brandBo.setMerId(merchantId);
                brandBo.setBrandId(brandId);
                brandBo.setRelateNo(relateNo);
                brandBo.setCateId(cateId);
                brandBo.setState(state);
                if (state.equals(CommonState.Pass.getCode())) {
                    brandBo.setType(Constants.ONE);
                }
                brandInfos.add(brandBo);
            }
        }
    }

    private List<SohuQualificationVo> getQualificationList(String qualificationType, List<Long> ids) {
        SohuQualificationListBo qualificationListBo = new SohuQualificationListBo();
        qualificationListBo.setQualificationType(qualificationType);
        qualificationListBo.setRelatedIds(ids);
        qualificationListBo.setShopType("business");
        List<SohuQualificationVo> cateQ = SohuQualificationService.getQualificationList(qualificationListBo);
        return cateQ;
    }

    protected Integer getYmBalance(Long userId) {
        int totalAvlAmt = 0;
        List<AccountBalanceQueryList> balanceQueryList = remoteYmService.queryUserBalance(userId);
        if (CollUtil.isNotEmpty(balanceQueryList)) {
            totalAvlAmt = balanceQueryList.stream().mapToInt(AccountBalanceQueryList::getAvlAmt).sum();
        }
        return totalAvlAmt;
    }
}
