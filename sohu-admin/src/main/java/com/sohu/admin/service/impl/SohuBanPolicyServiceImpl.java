package com.sohu.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.bo.SohuBanPolicyBo;
import com.sohu.admin.api.vo.SohuBanPolicyVo;
import com.sohu.admin.domain.SohuBanInterface;
import com.sohu.admin.domain.SohuBanPolicy;
import com.sohu.admin.mapper.SohuBanInterfaceMapper;
import com.sohu.admin.mapper.SohuBanPolicyMapper;
import com.sohu.admin.service.ISohuBanPolicyService;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.sohu.common.core.constant.BanConstants.BAN_POLICY;

/**
 * 风控策略核心Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuBanPolicyServiceImpl implements ISohuBanPolicyService {

    private final SohuBanPolicyMapper baseMapper;
    private final SohuBanInterfaceMapper sohuBanInterfaceMapper;

    /**
     * 查询风控策略核心
     */
    @Override
    public SohuBanPolicyVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询风控策略核心列表
     */
    @Override
    public TableDataInfo<SohuBanPolicyVo> queryPageList(SohuBanPolicyBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuBanPolicy> lqw = buildQueryWrapper(bo);
        Page<SohuBanPolicyVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询风控策略核心列表
     */
    @Override
    public List<SohuBanPolicyVo> queryList(SohuBanPolicyBo bo) {
        LambdaQueryWrapper<SohuBanPolicy> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuBanPolicy> buildQueryWrapper(SohuBanPolicyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuBanPolicy> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getPolicyName()), SohuBanPolicy::getPolicyName, bo.getPolicyName());
        lqw.eq(bo.getPolicyLevel() != null, SohuBanPolicy::getPolicyLevel, bo.getPolicyLevel());
        lqw.eq(StringUtils.isNotBlank(bo.getPolicyType()), SohuBanPolicy::getPolicyType, bo.getPolicyType());
        lqw.eq(bo.getIsGlobal() != null, SohuBanPolicy::getIsGlobal, bo.getIsGlobal());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), SohuBanPolicy::getDescription, bo.getDescription());
        lqw.eq(StringUtils.isNotBlank(bo.getActiveStatus()), SohuBanPolicy::getActiveStatus, bo.getActiveStatus());
        return lqw;
    }

    /**
     * 新增风控策略核心
     */
    @Override
    public Boolean insertByBo(SohuBanPolicyBo bo) {
        SohuBanPolicy add = BeanUtil.toBean(bo, SohuBanPolicy.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改风控策略核心
     */
    @Override
    public Boolean updateByBo(SohuBanPolicyBo bo) {
        SohuBanPolicy update = BeanUtil.toBean(bo, SohuBanPolicy.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuBanPolicy entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除风控策略核心
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @PostConstruct
    public void initPolicyCache() {
        List<SohuBanPolicy> list = this.baseMapper.selectList(SohuBanPolicy::getActiveStatus, SohuBanPolicyBo.ACTIVE);
        if (CollUtil.isEmpty(list)) {
            log.warn("没有找到任何激活的封禁策略");
            return;
        }
        for (SohuBanPolicy policy : list) {
            Long policyId = policy.getId();

            // 查询策略绑定的接口列表
            List<SohuBanInterface> interfaceList = sohuBanInterfaceMapper.selectByPolicyId(policyId);
            if (CollUtil.isEmpty(interfaceList)) {
                log.info("策略 [{}] 无绑定接口", policyId);
                continue;
            }
            // 缓存到 Redis，Key: ban:policy:{policyId}，Value: JSON List<SohuBanInterface>
            String redisKey = BAN_POLICY + policyId;
            RedisUtils.setCacheList(redisKey, interfaceList);
        }
        log.info("封禁策略初始化完成，策略数: {}", list.size());
    }
}
