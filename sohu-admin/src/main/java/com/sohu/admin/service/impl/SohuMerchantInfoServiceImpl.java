package com.sohu.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.model.SohuMerchantInfoModel;
import com.sohu.admin.domain.SohuMerchantInfo;
import com.sohu.admin.mapper.SohuMerchantCategoryMapper;
import com.sohu.admin.service.ISohuMerchantInfoService;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuMerchantInfoBo;
import com.sohu.middle.api.vo.SohuMerchantInfoVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 商户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
@RequiredArgsConstructor
@Service
public class SohuMerchantInfoServiceImpl implements ISohuMerchantInfoService {

    private final SohuMerchantCategoryMapper.SohuMerchantInfoMapper baseMapper;

    /**
     * 查询商户信息
     */
    @Override
    public SohuMerchantInfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商户信息列表
     */
    @Override
    public TableDataInfo<SohuMerchantInfoVo> queryPageList(SohuMerchantInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuMerchantInfo> lqw = buildQueryWrapper(bo);
        Page<SohuMerchantInfoVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询商户信息列表
     */
    @Override
    public List<SohuMerchantInfoVo> queryList(SohuMerchantInfoBo bo) {
        LambdaQueryWrapper<SohuMerchantInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuMerchantInfo> buildQueryWrapper(SohuMerchantInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuMerchantInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getMerId() != null, SohuMerchantInfo::getMerId, bo.getMerId());
        lqw.eq(StringUtils.isNotBlank(bo.getTransferType()), SohuMerchantInfo::getTransferType, bo.getTransferType());
        lqw.like(StringUtils.isNotBlank(bo.getTransferName()), SohuMerchantInfo::getTransferName, bo.getTransferName());
        lqw.eq(StringUtils.isNotBlank(bo.getTransferBank()), SohuMerchantInfo::getTransferBank, bo.getTransferBank());
        lqw.eq(StringUtils.isNotBlank(bo.getTransferBankCard()), SohuMerchantInfo::getTransferBankCard, bo.getTransferBankCard());
        lqw.eq(bo.getAlertStock() != null, SohuMerchantInfo::getAlertStock, bo.getAlertStock());
        lqw.eq(StringUtils.isNotBlank(bo.getServiceType()), SohuMerchantInfo::getServiceType, bo.getServiceType());
        lqw.eq(StringUtils.isNotBlank(bo.getServiceLink()), SohuMerchantInfo::getServiceLink, bo.getServiceLink());
        lqw.eq(StringUtils.isNotBlank(bo.getServicePhone()), SohuMerchantInfo::getServicePhone, bo.getServicePhone());
        lqw.eq(StringUtils.isNotBlank(bo.getServiceMessage()), SohuMerchantInfo::getServiceMessage, bo.getServiceMessage());
        lqw.eq(StringUtils.isNotBlank(bo.getServiceEmail()), SohuMerchantInfo::getServiceEmail, bo.getServiceEmail());
        return lqw;
    }

    /**
     * 新增商户信息
     */
    @Override
    public Boolean insertByBo(SohuMerchantInfoBo bo) {
        SohuMerchantInfo add = BeanUtil.toBean(bo, SohuMerchantInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商户信息
     */
    @Override
    public Boolean updateByBo(SohuMerchantInfoBo bo) {
        SohuMerchantInfo update = BeanUtil.toBean(bo, SohuMerchantInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuMerchantInfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商户信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 转账信息修改或添加
     */
    @Override
    public Boolean updateTransfer(SohuMerchantInfoBo bo) {
        SohuMerchantInfo info = BeanUtil.toBean(bo, SohuMerchantInfo.class);
        if (info.getId() == null) {
            return baseMapper.insert(info) > 0;
        } else {
            return baseMapper.updateById(info) > 0;
        }
    }

    @Override
    public SohuMerchantInfoModel queryByMerId(Long merId) {
        // 根据merId查询商户信息
        SohuMerchantInfoVo sohuMerchantInfoVo = this.baseMapper.selectVoOne(new LambdaQueryWrapper<SohuMerchantInfo>().eq(SohuMerchantInfo::getMerId, merId));
        SohuMerchantInfoModel sohuMerchantInfoModel = new SohuMerchantInfoModel();
        BeanUtil.copyProperties(sohuMerchantInfoVo, sohuMerchantInfoModel);
        return sohuMerchantInfoModel;
    }

    @Override
    public void handleRobot(String busyCode, Boolean isPass, String reason) {
        SohuMerchantInfo sohuMerchantInfo = this.baseMapper.selectById(busyCode);
        if (Objects.nonNull(sohuMerchantInfo) && !isPass){
            //客服H5链接,清空链接
            sohuMerchantInfo.setServiceLink("");
            this.baseMapper.updateById(sohuMerchantInfo);
        }
    }

}
