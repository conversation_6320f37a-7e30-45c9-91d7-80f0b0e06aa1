package com.sohu.admin.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.admin.api.bo.SohuMerchantBondBo;
import com.sohu.admin.api.vo.SohuMerchantBondVo;
import com.sohu.admin.domain.SohuMerchantBond;
import com.sohu.admin.mapper.SohuMerchantBondMapper;
import com.sohu.admin.service.ISohuMerchantBondService;
import com.sohu.common.core.enums.CommonState;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商户分类保证金Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@RequiredArgsConstructor
@Service
public class SohuMerchantBondServiceImpl implements ISohuMerchantBondService {

    private final SohuMerchantBondMapper baseMapper;

    /**
     * 查询商户分类保证金
     */
    @Override
    public SohuMerchantBondVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 新增商户分类保证金
     */
    @Override
    public Boolean insertByBo(SohuMerchantBondBo bo) {
        SohuMerchantBond add = BeanUtil.toBean(bo, SohuMerchantBond.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商户分类保证金
     */
    @Override
    public Boolean updateByBo(SohuMerchantBondBo bo) {
        SohuMerchantBond update = BeanUtil.toBean(bo, SohuMerchantBond.class);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public List<SohuMerchantBondVo> selectListByStatus(Long merchantId, List<String> payStatus) {
        return baseMapper.selectListByStatus(merchantId, payStatus);
    }

    @Override
    public SohuMerchantBondVo queryByMerchantIdAndCateId(Long merchantId, Long cateId) {
        return baseMapper.selectVoOne(Wrappers.<SohuMerchantBond>lambdaQuery()
                .eq(SohuMerchantBond::getMerId, merchantId)
                .eq(SohuMerchantBond::getCateId, cateId));
    }

    @Override
    public Boolean checkMerchantBondPayStatus(Long merchantId) {
        return baseMapper.selectCount(Wrappers.<SohuMerchantBond>lambdaQuery()
                .eq(SohuMerchantBond::getMerId, merchantId)
                .eq(SohuMerchantBond::getPayStatus, CommonState.WaitPay.getCode())) > 0;
    }
}
