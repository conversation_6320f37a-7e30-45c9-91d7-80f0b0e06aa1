package com.sohu.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.bo.ProductBrandMappingBo;
import com.sohu.admin.api.vo.ProductBrandMappingVo;
import com.sohu.admin.domain.ProductBrandMapping;
import com.sohu.admin.mapper.ProductBrandMappingMapper;
import com.sohu.admin.service.IProductBrandMappingService;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * [多渠道]商品品牌映射Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RequiredArgsConstructor
@Service
public class ProductBrandMappingServiceImpl implements IProductBrandMappingService {

    private final ProductBrandMappingMapper baseMapper;

    /**
     * 查询[多渠道]商品品牌映射
     */
    @Override
    public ProductBrandMappingVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询[多渠道]商品品牌映射列表
     */
    @Override
    public TableDataInfo<ProductBrandMappingVo> queryPageList(ProductBrandMappingBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductBrandMapping> lqw = buildQueryWrapper(bo);
        Page<ProductBrandMappingVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询[多渠道]商品品牌映射列表
     */
    @Override
    public List<ProductBrandMappingVo> queryList(ProductBrandMappingBo bo) {
        LambdaQueryWrapper<ProductBrandMapping> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductBrandMapping> buildQueryWrapper(ProductBrandMappingBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductBrandMapping> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getChannel()), ProductBrandMapping::getChannel, bo.getChannel());
        lqw.eq(bo.getOurBrandId() != null, ProductBrandMapping::getOurBrandId, bo.getOurBrandId());
        lqw.eq(bo.getThirdPartyBrandId() != null, ProductBrandMapping::getThirdPartyBrandId, bo.getThirdPartyBrandId());
        lqw.like(StringUtils.isNotBlank(bo.getOurBrandName()), ProductBrandMapping::getOurBrandName, bo.getOurBrandName());
        lqw.like(StringUtils.isNotBlank(bo.getThirdPartyBrandName()), ProductBrandMapping::getThirdPartyBrandName, bo.getThirdPartyBrandName());
        lqw.eq(bo.getOperator() != null, ProductBrandMapping::getOperator, bo.getOperator());
        return lqw;
    }

    /**
     * 新增[多渠道]商品品牌映射
     */
    @Override
    public Boolean insertByBo(ProductBrandMappingBo bo) {
        ProductBrandMapping add = BeanUtil.toBean(bo, ProductBrandMapping.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改[多渠道]商品品牌映射
     */
    @Override
    public Boolean updateByBo(ProductBrandMappingBo bo) {
        ProductBrandMapping update = BeanUtil.toBean(bo, ProductBrandMapping.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductBrandMapping entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除[多渠道]商品品牌映射
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
