package com.sohu.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.bo.ThirdPartyBrandBo;
import com.sohu.admin.api.vo.ThirdPartyBrandVo;
import com.sohu.admin.domain.ThirdPartyBrand;
import com.sohu.admin.mapper.ThirdPartyBrandMapper;
import com.sohu.admin.service.IThirdPartyBrandService;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * [多渠道]三方平台商品品牌Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RequiredArgsConstructor
@Service
public class ThirdPartyBrandServiceImpl implements IThirdPartyBrandService {

    private final ThirdPartyBrandMapper baseMapper;

    /**
     * 查询[多渠道]三方平台商品品牌
     */
    @Override
    public ThirdPartyBrandVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询[多渠道]三方平台商品品牌列表
     */
    @Override
    public TableDataInfo<ThirdPartyBrandVo> queryPageList(ThirdPartyBrandBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ThirdPartyBrand> lqw = buildQueryWrapper(bo);
        Page<ThirdPartyBrandVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询[多渠道]三方平台商品品牌列表
     */
    @Override
    public List<ThirdPartyBrandVo> queryList(ThirdPartyBrandBo bo) {
        LambdaQueryWrapper<ThirdPartyBrand> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ThirdPartyBrand> buildQueryWrapper(ThirdPartyBrandBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ThirdPartyBrand> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), ThirdPartyBrand::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getLogoUrl()), ThirdPartyBrand::getLogoUrl, bo.getLogoUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getRelatedCategoryIds()), ThirdPartyBrand::getRelatedCategoryIds, bo.getRelatedCategoryIds());
        lqw.eq(bo.getIsDeleted() != null, ThirdPartyBrand::getIsDeleted, bo.getIsDeleted());
        return lqw;
    }

    /**
     * 新增[多渠道]三方平台商品品牌
     */
    @Override
    public Boolean insertByBo(ThirdPartyBrandBo bo) {
        ThirdPartyBrand add = BeanUtil.toBean(bo, ThirdPartyBrand.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改[多渠道]三方平台商品品牌
     */
    @Override
    public Boolean updateByBo(ThirdPartyBrandBo bo) {
        ThirdPartyBrand update = BeanUtil.toBean(bo, ThirdPartyBrand.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ThirdPartyBrand entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除[多渠道]三方平台商品品牌
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
