package com.sohu.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.bo.ProductCategoryMappingBo;
import com.sohu.admin.api.vo.ProductCategoryMappingVo;
import com.sohu.admin.domain.ProductCategoryMapping;
import com.sohu.admin.mapper.ProductCategoryMappingMapper;
import com.sohu.admin.service.IProductCategoryMappingService;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品分类映射Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RequiredArgsConstructor
@Service
public class ProductCategoryMappingServiceImpl implements IProductCategoryMappingService {

    private final ProductCategoryMappingMapper baseMapper;

    /**
     * 查询商品分类映射
     */
    @Override
    public ProductCategoryMappingVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商品分类映射列表
     */
    @Override
    public TableDataInfo<ProductCategoryMappingVo> queryPageList(ProductCategoryMappingBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductCategoryMapping> lqw = buildQueryWrapper(bo);
        Page<ProductCategoryMappingVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询商品分类映射列表
     */
    @Override
    public List<ProductCategoryMappingVo> queryList(ProductCategoryMappingBo bo) {
        LambdaQueryWrapper<ProductCategoryMapping> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductCategoryMapping> buildQueryWrapper(ProductCategoryMappingBo bo) {
        LambdaQueryWrapper<ProductCategoryMapping> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getChannel()), ProductCategoryMapping::getChannel, bo.getChannel());
        lqw.eq(StringUtils.isNotBlank(bo.getMerId()), ProductCategoryMapping::getMerId, bo.getMerId());
        lqw.eq(bo.getOurCategoryId() != null, ProductCategoryMapping::getOurCategoryId, bo.getOurCategoryId());
        lqw.eq(bo.getThirdPartyCategoryId() != null, ProductCategoryMapping::getThirdPartyCategoryId, bo.getThirdPartyCategoryId());
        lqw.eq(StringUtils.isNotBlank(bo.getOurCategoryPath()), ProductCategoryMapping::getOurCategoryPath, bo.getOurCategoryPath());
        lqw.eq(StringUtils.isNotBlank(bo.getThirdPartyCategoryPath()), ProductCategoryMapping::getThirdPartyCategoryPath, bo.getThirdPartyCategoryPath());
        lqw.eq(StringUtils.isNotBlank(bo.getOperator()), ProductCategoryMapping::getOperator, bo.getOperator());
        return lqw;
    }

    /**
     * 新增商品分类映射
     */
    @Override
    public Boolean insertByBo(ProductCategoryMappingBo bo) {
        ProductCategoryMapping add = BeanUtil.toBean(bo, ProductCategoryMapping.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商品分类映射
     */
    @Override
    public Boolean updateByBo(ProductCategoryMappingBo bo) {
        ProductCategoryMapping update = BeanUtil.toBean(bo, ProductCategoryMapping.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductCategoryMapping entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品分类映射
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
