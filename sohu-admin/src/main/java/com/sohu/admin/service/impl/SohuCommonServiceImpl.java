package com.sohu.admin.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.sohu.admin.service.SohuCommonService;
import com.sohu.busyorder.api.RemoteBusyOrderService;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.ApplyStateEnum;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.entry.api.RemoteEntryService;
import com.sohu.entry.api.model.RelationRespModel;
import com.sohu.middle.api.bo.RelationPageBO;
import com.sohu.middle.api.bo.SohuBusyBO;
import com.sohu.middle.api.service.*;
import com.sohu.middle.api.vo.RelationRespVo;
import com.sohu.middle.api.vo.SohuFriendsVo;
import com.sohu.middle.api.vo.SohuUserFollowVo;
import com.sohu.system.api.RemoteUserService;
import com.sohu.system.api.domain.UserVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RequiredArgsConstructor
@Service
@Slf4j
public class SohuCommonServiceImpl implements SohuCommonService {

    @DubboReference
    private RemoteMiddleArticleService remoteMiddleArticleService;
    @DubboReference
    private RemoteMiddleVideoService remoteMiddleVideoService;
    @DubboReference
    private RemoteMiddleQuestionService remoteMiddleQuestionService;
    @DubboReference
    private RemoteMiddleUserFollowService remoteMiddleUserFollowService;
    @DubboReference
    private RemoteMiddleFriendService remoteMiddleFriendService;
    @DubboReference
    private RemoteMiddleHistoryWordService remoteMiddleHistoryWordService;
    @DubboReference
    private RemoteMiddleContentMainService remoteMiddleContentMainService;
    @DubboReference
    private RemoteEntryService remoteEntryService;
    @DubboReference
    private RemoteBusyOrderService remoteBusyOrderService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteMiddleLiteratureService remoteMiddleLiteratureService;

    @Override
    public TableDataInfo<RelationRespVo> relationPage(RelationPageBO relationPageVO) {
        BusyType busyType = relationPageVO.getBusyType();
        TableDataInfo<RelationRespVo> result = null;
        switch (busyType) {
            case Project:
                List<RelationRespModel> list = remoteEntryService.page(LoginHelper.getUserId(), relationPageVO.getBusyTitle(), relationPageVO.getPageNum(), relationPageVO.getPageSize());
                result = TableDataInfoUtils.build();
                result.setData(BeanCopyUtils.copyList(list, RelationRespVo.class));
                break;
            case BusyOrder:
                List<com.sohu.busyorder.api.model.RelationRespModel> page = remoteBusyOrderService.page(LoginHelper.getUserId(), relationPageVO.getBusyTitle(), relationPageVO.getPageNum(), relationPageVO.getPageSize());
                result = TableDataInfoUtils.build();
                result.setData(BeanCopyUtils.copyList(page, RelationRespVo.class));
                break;
            default:
                log.info("关联分页查询");
                //todo
                //RelationPageEnum relationEnum = RelationPageEnum.valueOf(relationPageVO.getBusyType().name());
                //result = relationEnum.page(relationPageVO);
        }
        return result;
    }

    @Override
    public void commit(SohuBusyBO sohuBusyBO) {
        switch (sohuBusyBO.getBusyType()) {
            case Article:
                remoteMiddleArticleService.commit(sohuBusyBO.getBusyCode());
                break;
            case Video:
                remoteMiddleVideoService.commit(sohuBusyBO.getBusyCode());
                break;
            case Question:
                remoteMiddleQuestionService.commit(sohuBusyBO.getBusyCode());
                break;
            case Poetry:
                remoteMiddleLiteratureService.commit(sohuBusyBO.getBusyCode());
                break;
            case Prose:
                remoteMiddleLiteratureService.commit(sohuBusyBO.getBusyCode());
                break;
            default:
                log.error("草稿提交至审核，类型错误");
        }
    }

    @Override
    public TableDataInfo<UserVo> userPage(String keyword, PageQuery pageQuery) {
        // 非游客状态记录搜索历史
        Long userId = LoginHelper.getUserId();
        if (StringUtils.isNotBlank(keyword) && (userId != null && userId > 0L)) {
            remoteMiddleHistoryWordService.insertWord(userId, keyword);
        }
        //Page<UserVo> userVoPage = remoteUserService.page(null, keyword, null, pageQuery.getPageNum(), pageQuery.getPageSize());Page<UserVo> userVoPage = remoteUserService.page(null, keyword, null, pageQuery.getPageNum(), pageQuery.getPageSize());
        TableDataInfo<UserVo> userVoPage = remoteUserService.page(null, keyword, null, pageQuery.getPageNum(), pageQuery.getPageSize());
        if (CollUtil.isEmpty(userVoPage.getData())) {
            return TableDataInfoUtils.build();
        }
        if (userId != null && userId > 0L) {
            List<UserVo> records = userVoPage.getData();
            List<Long> userIds = new ArrayList<>();
            for (UserVo record : records) {
                userIds.add(record.getId());
            }
            Map<Long, SohuUserFollowVo> userFollowMap = remoteMiddleUserFollowService.mapUserFollows(userId, userIds);
            Map<Long, SohuUserFollowVo> mapFocusFans = remoteMiddleUserFollowService.mapFocusFans(userIds, userId);
            Map<Long, SohuFriendsVo> myfriendMap = remoteMiddleFriendService.friendMap(userId, userIds, ApplyStateEnum.pass.name());
            Map<Long, SohuFriendsVo> himFriendMap = remoteMiddleFriendService.friendMap(userIds, userId, ApplyStateEnum.pass.name());
            for (UserVo record : records) {
                // 他关注我状态，true = 关注
                record.setFollowMe(Objects.nonNull(mapFocusFans.get(record.getId())));
                // 我关注他状态，true = 关注
                record.setFollowHim(Objects.nonNull(userFollowMap.get(record.getId())));
                record.setPraiseCount(remoteMiddleContentMainService.count(record.getId()));
                if (StrUtil.isBlankIfStr(record.getAvatar())) {
                    record.setAvatar(Constants.DEFAULT_USER_AVATAR);
                }
                // 他是否是我的好友
                record.setMeFriend(Objects.nonNull(myfriendMap.get(record.getId())));
                // 是否我是他的好友
                record.setHimFriend(Objects.nonNull(himFriendMap.get(record.getId())));
            }
            //userVoPage.setRecords(records);
        }
        //return TableDataInfoUtils.build(userVoPage);
        return userVoPage;
    }

    @Override
    public Object aiScore(String content) {
        if (StrUtil.isBlankIfStr(content)) {
            return null;
        }
        String URL = "http://192.168.150.51:8000/score";
        String params = "content=" + content;
        String body = HttpRequest.post(URL)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .body(params, ContentType.APPLICATION_FORM_URLENCODED.getMimeType()).execute().body();
        JSONObject json = JSONObject.parseObject(body);
        return json;
    }

}
