package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.admin.api.bo.ThirdPartyBrandBo;
import com.sohu.admin.api.vo.ThirdPartyBrandVo;
import com.sohu.admin.service.IThirdPartyBrandService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * [多渠道]三方平台商品品牌控制器
 * 前端访问路由地址为:/system/partyBrand
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/partyBrand")
public class ThirdPartyBrandController extends BaseController {

    private final IThirdPartyBrandService iThirdPartyBrandService;

    /**
     * 查询[多渠道]三方平台商品品牌列表
     */
    @SaCheckPermission("system:partyBrand:list")
    @GetMapping("/list")
    public TableDataInfo<ThirdPartyBrandVo> list(ThirdPartyBrandBo bo, PageQuery pageQuery) {
        return iThirdPartyBrandService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出[多渠道]三方平台商品品牌列表
     */
    @SaCheckPermission("system:partyBrand:export")
    @Log(title = "[多渠道]三方平台商品品牌", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ThirdPartyBrandBo bo, HttpServletResponse response) {
        List<ThirdPartyBrandVo> list = iThirdPartyBrandService.queryList(bo);
        ExcelUtil.exportExcel(list, "[多渠道]三方平台商品品牌", ThirdPartyBrandVo.class, response);
    }

    /**
     * 获取[多渠道]三方平台商品品牌详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:partyBrand:query")
    @GetMapping("/{id}")
    public R<ThirdPartyBrandVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iThirdPartyBrandService.queryById(id));
    }

    /**
     * 新增[多渠道]三方平台商品品牌
     */
    @SaCheckPermission("system:partyBrand:add")
    @Log(title = "[多渠道]三方平台商品品牌", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody ThirdPartyBrandBo bo) {
        return toAjax(iThirdPartyBrandService.insertByBo(bo));
    }

    /**
     * 修改[多渠道]三方平台商品品牌
     */
    @SaCheckPermission("system:partyBrand:edit")
    @Log(title = "[多渠道]三方平台商品品牌", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody ThirdPartyBrandBo bo) {
        return toAjax(iThirdPartyBrandService.updateByBo(bo));
    }

    /**
     * 删除[多渠道]三方平台商品品牌
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:partyBrand:remove")
    @Log(title = "[多渠道]三方平台商品品牌", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iThirdPartyBrandService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
