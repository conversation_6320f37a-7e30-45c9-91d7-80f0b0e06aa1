package com.sohu.admin.controller;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuTradeRecordIndependentBo;
import com.sohu.middle.api.service.RemoteMiddleTradeRecordService;
import com.sohu.middle.api.vo.*;
import com.sohu.pay.api.RemoteIndependentOrderService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用户流水明细
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/trade")
@ResponseBody
public class AdminTradeRecordController extends BaseController {

    @DubboReference
    private RemoteMiddleTradeRecordService remoteMiddleTradeRecordService;

    /**
     * 分账单分页列表
     */
    @GetMapping("/independent/list")
    public TableDataInfo<SohuTradeRecordIndependentVo> independentList(SohuTradeRecordIndependentBo independentBo) {
        return remoteMiddleTradeRecordService.queryIndependentList(independentBo);
    }

    /**
     * 分账单总金额查询
     *
     * @return 返回符合条件查询的总金额
     */
    @GetMapping("/independent/total")
    public R<BigDecimal> independentTotal(SohuTradeRecordIndependentBo independentBo) {
        return R.ok(remoteMiddleTradeRecordService.independentTotal(independentBo));
    }

    /**
     * 分账单详情
     *
     * @param id 列表的id
     */
    @GetMapping("/detail/{id}")
    public R<SohuTradeRecordDetailIndependentVo> detail(@PathVariable Long id) {
        return R.ok(remoteMiddleTradeRecordService.taskDetail(id, null, null, null));
    }

    @Operation(summary = "代理商今日/累计收益", description = "负责人：张明明，代理商首页-代理商今日/累计收益")
    @GetMapping("/income")
    public R<SohuInComeVo> income() {
        return R.ok(remoteMiddleTradeRecordService.income());
    }

    @Operation(summary = "代理商收益-按天数", description = "负责人：张明明，代理商首页-代理商收益")
    @GetMapping("/incomeList/{days}")
    public R<List<SohuInComeListVo>> incomeList(@PathVariable Long days) {
        return R.ok(remoteMiddleTradeRecordService.incomeList(days));
    }

    @Operation(summary = "代理商收益-按时间", description = "负责人：张明明，代理商首页-代理商收益")
    @GetMapping("/incomeListByDate/{date}")
    public R<List<SohuInComeListVo>> incomeListByDate(@PathVariable String date) {
        return R.ok(remoteMiddleTradeRecordService.incomeListByDate(date));
    }

    @Operation(summary = "销售额排名", description = "负责人：张明明，代理商首页-销售额排名")
    @GetMapping("/sales/rank")
    public R<List<SohuSalesRankVo>> salesRank() {
        return R.ok(remoteMiddleTradeRecordService.salesRank());
    }

    @Log(title = "账单流水导出", businessType = BusinessType.EXPORT)
    @PostMapping("/independent/export")
    public void export(SohuTradeRecordIndependentBo bo, HttpServletResponse response) {
        List<SohuTradeRecordIndependentVo> list = remoteMiddleTradeRecordService.getList(bo);
        ExcelUtil.exportExcel(list, "账单流水", SohuTradeRecordIndependentVo.class, response);
    }


}
