package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.admin.api.bo.SohuBanRecordsBo;
import com.sohu.admin.api.vo.SohuBanRecordsVo;
import com.sohu.admin.service.ISohuBanRecordsService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 封禁记录控制器
 * 前端访问路由地址为:/system/banRecords
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/banRecords")
public class SohuBanRecordsController extends BaseController {

    private final ISohuBanRecordsService iSohuBanRecordsService;

    /**
     * 查询封禁记录列表
     */
    @SaCheckPermission("system:banRecords:list")
    @GetMapping("/list")
    public TableDataInfo<SohuBanRecordsVo> list(SohuBanRecordsBo bo, PageQuery pageQuery) {
        return iSohuBanRecordsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出封禁记录列表
     */
    @SaCheckPermission("system:banRecords:export")
    @Log(title = "封禁记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuBanRecordsBo bo, HttpServletResponse response) {
        List<SohuBanRecordsVo> list = iSohuBanRecordsService.queryList(bo);
        ExcelUtil.exportExcel(list, "封禁记录", SohuBanRecordsVo.class, response);
    }

    /**
     * 获取封禁记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:banRecords:query")
    @GetMapping("/{id}")
    public R<SohuBanRecordsVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuBanRecordsService.queryById(id));
    }

    /**
     * 新增封禁记录
     */
    @SaCheckPermission("system:banRecords:add")
    @Log(title = "封禁记录", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Long> add(@Validated(AddGroup.class) @RequestBody SohuBanRecordsBo bo) {
        return R.ok(iSohuBanRecordsService.insertByBo(bo));
    }

    /**
     * 修改封禁记录
     */
    @SaCheckPermission("system:banRecords:edit")
    @Log(title = "封禁记录", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuBanRecordsBo bo) {
        return toAjax(iSohuBanRecordsService.updateByBo(bo));
    }

    /**
     * 删除封禁记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:banRecords:remove")
    @Log(title = "封禁记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuBanRecordsService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
