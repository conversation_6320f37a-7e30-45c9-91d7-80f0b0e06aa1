package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.admin.api.bo.ProductCategoryMappingBo;
import com.sohu.admin.api.vo.ProductCategoryMappingVo;
import com.sohu.admin.service.IProductCategoryMappingService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 商品分类映射控制器
 * 前端访问路由地址为:/system/categoryMapping
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/categoryMapping")
public class ProductCategoryMappingController extends BaseController {

    private final IProductCategoryMappingService iProductCategoryMappingService;

    /**
     * 查询商品分类映射列表
     */
    @SaCheckPermission("system:categoryMapping:list")
    @GetMapping("/list")
    public TableDataInfo<ProductCategoryMappingVo> list(ProductCategoryMappingBo bo, PageQuery pageQuery) {
        return iProductCategoryMappingService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出商品分类映射列表
     */
    @SaCheckPermission("system:categoryMapping:export")
    @Log(title = "商品分类映射", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductCategoryMappingBo bo, HttpServletResponse response) {
        List<ProductCategoryMappingVo> list = iProductCategoryMappingService.queryList(bo);
        ExcelUtil.exportExcel(list, "商品分类映射", ProductCategoryMappingVo.class, response);
    }

    /**
     * 获取商品分类映射详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:categoryMapping:query")
    @GetMapping("/{id}")
    public R<ProductCategoryMappingVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iProductCategoryMappingService.queryById(id));
    }

    /**
     * 新增商品分类映射
     */
    @SaCheckPermission("system:categoryMapping:add")
    @Log(title = "商品分类映射", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody ProductCategoryMappingBo bo) {
        return toAjax(iProductCategoryMappingService.insertByBo(bo));
    }

    /**
     * 修改商品分类映射
     */
    @SaCheckPermission("system:categoryMapping:edit")
    @Log(title = "商品分类映射", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody ProductCategoryMappingBo bo) {
        return toAjax(iProductCategoryMappingService.updateByBo(bo));
    }

    /**
     * 删除商品分类映射
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:categoryMapping:remove")
    @Log(title = "商品分类映射", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iProductCategoryMappingService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
