package com.sohu.admin.controller;

import com.sohu.admin.api.bo.SohuBanInterfaceBo;
import com.sohu.admin.api.vo.SohuBanInterfaceVo;
import com.sohu.admin.service.ISohuBanInterfaceService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 受控接口资源控制器
 * 前端访问路由地址为:/admin/banInterface
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/banInterface")
@Hidden
public class SohuBanInterfaceController extends BaseController {

    private final ISohuBanInterfaceService iSohuBanInterfaceService;

    /**
     * 查询受控接口资源列表
     */
    @GetMapping("/list")
    public TableDataInfo<SohuBanInterfaceVo> list(SohuBanInterfaceBo bo, PageQuery pageQuery) {
        return iSohuBanInterfaceService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出受控接口资源列表
     */
    @Log(title = "受控接口资源", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuBanInterfaceBo bo, HttpServletResponse response) {
        List<SohuBanInterfaceVo> list = iSohuBanInterfaceService.queryList(bo);
        ExcelUtil.exportExcel(list, "受控接口资源", SohuBanInterfaceVo.class, response);
    }

    /**
     * 获取受控接口资源详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<SohuBanInterfaceVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuBanInterfaceService.queryById(id));
    }

    /**
     * 新增受控接口资源
     */
    @Log(title = "受控接口资源", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuBanInterfaceBo bo) {
        return toAjax(iSohuBanInterfaceService.insertByBo(bo));
    }

    /**
     * 修改受控接口资源
     */
    @Log(title = "受控接口资源", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuBanInterfaceBo bo) {
        return toAjax(iSohuBanInterfaceService.updateByBo(bo));
    }

    /**
     * 删除受控接口资源
     *
     * @param ids 主键串
     */
    @Log(title = "受控接口资源", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuBanInterfaceService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
