package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.admin.api.bo.SohuBanPolicyBo;
import com.sohu.admin.api.vo.SohuBanPolicyVo;
import com.sohu.admin.service.ISohuBanPolicyService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 风控策略核心控制器
 * 前端访问路由地址为:/admin/banPolicy
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/banPolicy")
public class SohuBanPolicyController extends BaseController {

    private final ISohuBanPolicyService iSohuBanPolicyService;

    /**
     * 查询风控策略核心列表
     */
    @SaCheckPermission("admin:banPolicy:list")
    @GetMapping("/list")
    public TableDataInfo<SohuBanPolicyVo> list(SohuBanPolicyBo bo, PageQuery pageQuery) {
        return iSohuBanPolicyService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出风控策略核心列表
     */
    @SaCheckPermission("admin:banPolicy:export")
    @Log(title = "风控策略核心", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuBanPolicyBo bo, HttpServletResponse response) {
        List<SohuBanPolicyVo> list = iSohuBanPolicyService.queryList(bo);
        ExcelUtil.exportExcel(list, "风控策略核心", SohuBanPolicyVo.class, response);
    }

    /**
     * 获取风控策略核心详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("admin:banPolicy:query")
    @GetMapping("/{id}")
    public R<SohuBanPolicyVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuBanPolicyService.queryById(id));
    }

    /**
     * 新增风控策略核心
     */
    @SaCheckPermission("admin:banPolicy:add")
    @Log(title = "风控策略核心", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuBanPolicyBo bo) {
        return toAjax(iSohuBanPolicyService.insertByBo(bo));
    }

    /**
     * 修改风控策略核心
     */
    @SaCheckPermission("admin:banPolicy:edit")
    @Log(title = "风控策略核心", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuBanPolicyBo bo) {
        return toAjax(iSohuBanPolicyService.updateByBo(bo));
    }

    /**
     * 删除风控策略核心
     *
     * @param ids 主键串
     */
    @SaCheckPermission("admin:banPolicy:remove")
    @Log(title = "风控策略核心", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuBanPolicyService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
