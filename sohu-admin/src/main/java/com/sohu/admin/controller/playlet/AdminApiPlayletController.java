package com.sohu.admin.controller.playlet;


import cn.hutool.core.util.StrUtil;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.aspect.UserBehavior;
import com.sohu.middle.api.bo.SohuPlayletBo;
import com.sohu.middle.api.bo.playlet.PlayletDetailBo;
import com.sohu.middle.api.bo.playlet.PlayletDetailQueryBo;
import com.sohu.middle.api.bo.playlet.PlayletMaterialsBo;
import com.sohu.middle.api.bo.playlet.PlayletOperateBo;
import com.sohu.middle.api.enums.behavior.BehaviorBusinessTypeEnum;
import com.sohu.middle.api.enums.behavior.OperaTypeEnum;
import com.sohu.middle.api.service.RemoteMiddlePlayletService;
import com.sohu.middle.api.vo.SohuPlayletVo;
import com.sohu.middle.api.vo.SohuVideoVo;
import com.sohu.middle.api.vo.playlet.PlayletDetailListVo;
import com.sohu.middle.api.vo.playlet.PlayletDetailVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 海外短剧-剧集管理
 * 前端访问路由地址为:/admin/api/playlet
 *
 * <AUTHOR>
 * @date 2024-09-13
 */
@Tag(name = "海外短剧-剧集管理")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/playlet")
public class AdminApiPlayletController extends BaseController {

    @DubboReference
    private RemoteMiddlePlayletService remoteMiddlePlayletService;

    /**
     * 新增剧集
     */
    @Operation(summary = "新增剧集", description = "负责人：李君婕，剧集管理-新增剧集")
    @PostMapping("")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.SHORT_PLAY, operType = OperaTypeEnum.ADD)
    public R<Boolean> add(@RequestBody PlayletDetailBo bo) {
        SohuPlayletBo sohuPlayletBo = BeanCopyUtils.copy(bo, SohuPlayletBo.class);
        if (StrUtil.isBlank(sohuPlayletBo.getState())) {
            sohuPlayletBo.setState(CommonState.WaitShelf.getCode());
        }
        sohuPlayletBo.setOperateTime(new Date());
        sohuPlayletBo.setSysSource(Constants.SOHUGLOBAL);
        return R.ok(remoteMiddlePlayletService.addPlayLet(sohuPlayletBo));
    }

    /**
     * 新增花絮
     */
    @Operation(summary = "新增花絮", description = "负责人：李君婕，剧集管理-新增花絮")
    @PostMapping("/addMaterials")
    public R<Boolean> addMaterials(@RequestBody PlayletDetailBo bo) {
        SohuPlayletBo sohuPlayletBo = BeanCopyUtils.copy(bo, SohuPlayletBo.class);
        if (StrUtil.isBlank(sohuPlayletBo.getState())) {
            sohuPlayletBo.setState(CommonState.WaitShelf.getCode());
        }
        sohuPlayletBo.setOperateTime(new Date());
        sohuPlayletBo.setSysSource(Constants.SOHUGLOBAL);
        return R.ok(remoteMiddlePlayletService.addPlayLet(sohuPlayletBo));
    }

    /**
     * 查询花絮
     */
    @Operation(summary = "查询花絮", description = "负责人：李君婕，剧集管理-查询花絮")
    @GetMapping("/getMaterialsInfo/{playletId}")
    public R<List<SohuVideoVo>> getMaterialsInfo(@PathVariable Long playletId) {
        return R.ok(remoteMiddlePlayletService.getMaterialsInfo(playletId));
    }

    /**
     * 新增花絮
     */
    @Operation(summary = "修改花絮", description = "负责人：李君婕，剧集管理-修改花絮")
    @PostMapping("/updateMaterials")
    public R<Boolean> updateMaterials(@RequestBody PlayletMaterialsBo bo) {
        return R.ok(remoteMiddlePlayletService.updateMaterials(bo));
    }

    /**
     * 编辑剧集
     */
    @Operation(summary = "修改剧集", description = "负责人：李君婕，剧集管理-新增剧集")
    @PutMapping("")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.SHORT_PLAY, operType = OperaTypeEnum.UPDATE)
    public R<Boolean> edit(@RequestBody PlayletDetailBo bo) {
        SohuPlayletBo sohuPlayletBo = BeanCopyUtils.copy(bo, SohuPlayletBo.class);
        sohuPlayletBo.setOperateTime(new Date());
        sohuPlayletBo.setSysSource(Constants.SOHUGLOBAL);
        return R.ok(remoteMiddlePlayletService.updateByBo(sohuPlayletBo));
    }

    /**
     * 上下架剧集
     */
    @Operation(summary = "上下架剧集", description = "负责人：李君婕，剧集管理-上下架剧集")
    @PostMapping("/operate")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.SHORT_PLAY, operType = OperaTypeEnum.OFFSHELF)
    public R<Boolean> auditPlayLet(@RequestBody PlayletOperateBo bo) {
        SohuPlayletBo playletBo = new SohuPlayletBo();
        playletBo.setState(bo.getState());
        playletBo.setId(bo.getId());
        playletBo.setOperateTime(new Date());
        return R.ok(remoteMiddlePlayletService.updateState(playletBo));
    }

    /**
     * 删除剧集
     */
    @Operation(summary = "删除剧集", description = "负责人：李君婕，剧集管理-删除剧集")
    @DeleteMapping("/{id}")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.SHORT_PLAY, operType = OperaTypeEnum.DELETE)
    public R<Boolean> remove(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(remoteMiddlePlayletService.deletePlayLet(id));
    }

    /**
     * 获取剧集主体详细信息
     *
     * @param id 主键
     */
    @Operation(summary = "剧集详情", description = "负责人：李君婕，剧集管理-剧集详情")
    @GetMapping("/{id}")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.SHORT_PLAY, operType = OperaTypeEnum.INFO)
    public R<PlayletDetailVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        SohuPlayletVo sohuPlayletVo = remoteMiddlePlayletService.queryById(id);
        PlayletDetailVo detailVo = BeanCopyUtils.copy(sohuPlayletVo, PlayletDetailVo.class);
        return R.ok(detailVo);
    }

    /**
     * 获取剧集草稿箱
     */
    @Operation(summary = "获取剧集草稿箱", description = "负责人：李君婕，剧集管理-获取剧集草稿箱")
    @GetMapping("/getEditInfo")
    public R<PlayletDetailVo> getEditInfo() {
        SohuPlayletVo sohuPlayletVo = remoteMiddlePlayletService.getEditInfo();
        PlayletDetailVo detailVo = BeanCopyUtils.copy(sohuPlayletVo, PlayletDetailVo.class);
        return R.ok(detailVo);
    }


    /**
     * 查询短剧列表
     */
    @Operation(summary = "查询短剧列表", description = "负责人：李君婕，剧集管理-查询短剧列表")
    @GetMapping("/list")
    public TableDataInfo<PlayletDetailListVo> list(PlayletDetailQueryBo bo, PageQuery pageQuery) {
        bo.setSysSource(Constants.SOHUGLOBAL);
        return remoteMiddlePlayletService.queryAdminList(bo, pageQuery);
    }
}
