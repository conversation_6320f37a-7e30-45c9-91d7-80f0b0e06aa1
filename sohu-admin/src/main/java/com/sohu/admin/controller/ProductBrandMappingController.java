package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.admin.api.bo.ProductBrandMappingBo;
import com.sohu.admin.api.vo.ProductBrandMappingVo;
import com.sohu.admin.service.IProductBrandMappingService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * [多渠道]商品品牌映射控制器
 * 前端访问路由地址为:/system/brandMapping
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/brandMapping")
public class ProductBrandMappingController extends BaseController {

    private final IProductBrandMappingService iProductBrandMappingService;

    /**
     * 查询[多渠道]商品品牌映射列表
     */
    @SaCheckPermission("system:brandMapping:list")
    @GetMapping("/list")
    public TableDataInfo<ProductBrandMappingVo> list(ProductBrandMappingBo bo, PageQuery pageQuery) {
        return iProductBrandMappingService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出[多渠道]商品品牌映射列表
     */
    @SaCheckPermission("system:brandMapping:export")
    @Log(title = "[多渠道]商品品牌映射", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductBrandMappingBo bo, HttpServletResponse response) {
        List<ProductBrandMappingVo> list = iProductBrandMappingService.queryList(bo);
        ExcelUtil.exportExcel(list, "[多渠道]商品品牌映射", ProductBrandMappingVo.class, response);
    }

    /**
     * 获取[多渠道]商品品牌映射详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:brandMapping:query")
    @GetMapping("/{id}")
    public R<ProductBrandMappingVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iProductBrandMappingService.queryById(id));
    }

    /**
     * 新增[多渠道]商品品牌映射
     */
    @SaCheckPermission("system:brandMapping:add")
    @Log(title = "[多渠道]商品品牌映射", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody ProductBrandMappingBo bo) {
        return toAjax(iProductBrandMappingService.insertByBo(bo));
    }

    /**
     * 修改[多渠道]商品品牌映射
     */
    @SaCheckPermission("system:brandMapping:edit")
    @Log(title = "[多渠道]商品品牌映射", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody ProductBrandMappingBo bo) {
        return toAjax(iProductBrandMappingService.updateByBo(bo));
    }

    /**
     * 删除[多渠道]商品品牌映射
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:brandMapping:remove")
    @Log(title = "[多渠道]商品品牌映射", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iProductBrandMappingService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
