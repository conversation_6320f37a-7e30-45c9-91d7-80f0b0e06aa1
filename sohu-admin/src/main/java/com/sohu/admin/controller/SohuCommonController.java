package com.sohu.admin.controller;


import com.sohu.admin.api.bo.SohuAiScoreBo;
import com.sohu.admin.service.SohuCommonService;
import com.sohu.common.core.domain.R;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.RelationPageBO;
import com.sohu.middle.api.bo.SohuBusyBO;
import com.sohu.middle.api.vo.RelationRespVo;
import com.sohu.system.api.domain.UserVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 通用方法查询
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/common")
public class SohuCommonController {

    private final SohuCommonService sohuCommonService;

    @GetMapping("/relation/page")
    @Operation(summary = "关联项分页查询")
    public TableDataInfo<RelationRespVo> page(@Valid RelationPageBO relationPageVO) {
        return sohuCommonService.relationPage(relationPageVO);
    }

    @PostMapping("/commit")
    @Operation(summary = "草稿提交至审核")
    public R<Boolean> commit(@RequestBody SohuBusyBO sohuBusyBO) {
        sohuCommonService.commit(sohuBusyBO);
        return R.ok(Boolean.TRUE);
    }

    @Operation(summary = "用户列表")
    @GetMapping("/user/page")
    public TableDataInfo<UserVo> page(@RequestParam(value = "keyword", required = false) String keyword, PageQuery pageQuery) {
        return sohuCommonService.userPage(keyword, pageQuery);
    }

    @PostMapping("/ai/score")
    @Operation(summary = "AI评分")
    public R<Object> aiScore(@RequestBody SohuAiScoreBo bo) {
        return R.ok(sohuCommonService.aiScore(bo.getContent()));
    }

}
