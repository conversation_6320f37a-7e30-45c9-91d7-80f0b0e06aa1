package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuCategoryBo;
import com.sohu.middle.api.service.RemoteMiddleCategoryService;
import com.sohu.middle.api.vo.SohuCategoryVo;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 分类
 * 前端访问路由地址为:/admin/category
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/category")
public class SohuCategoryController extends BaseController {

    @DubboReference
    private RemoteMiddleCategoryService remoteMiddleCategoryService;

    /**
     * 查询分类列表
     */
    //@SaCheckPermission("admin:category:list")
    @GetMapping("/list")
    public TableDataInfo<SohuCategoryVo> list(SohuCategoryBo bo, PageQuery pageQuery) {
        return remoteMiddleCategoryService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出分类列表
     */
    @Hidden
    @SaCheckPermission(value = "admin:category:export",orRole = {"kefuchaoguan"})
    @Log(title = "分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuCategoryBo bo, HttpServletResponse response) {
        List<SohuCategoryVo> list = remoteMiddleCategoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "分类", SohuCategoryVo.class, response);
    }

    /**
     * 获取分类详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission(value = "admin:category:query",orRole = {"kefuchaoguan"})
    @GetMapping("/{id}")
    public R<SohuCategoryVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteMiddleCategoryService.queryById(id));
    }

    /**
     * 新增分类
     */
    @SaCheckPermission(value = "admin:category:add",orRole = {"kefuchaoguan"})
    @Log(title = "新增分类", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuCategoryBo bo) {
        return toAjax(remoteMiddleCategoryService.insertByBo(bo));
    }

    /**
     * 修改分类
     */
    @SaCheckPermission(value = "admin:category:edit",orRole = {"kefuchaoguan"})
    @Log(title = "修改分类", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuCategoryBo bo) {
        return toAjax(remoteMiddleCategoryService.updateByBo(bo));
    }

    /**
     * 删除分类
     *
     * @param ids 主键串
     */
    @SaCheckPermission(value = "admin:category:remove",orRole = {"kefuchaoguan"})
    @Log(title = "批量删除分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(remoteMiddleCategoryService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    @GetMapping("/tree")
    @Operation(summary = "获取当前站点分类列表(树形)")
    public R<List<SohuCategoryVo>> getCategoryTree(SohuCategoryBo bo) {
        return R.ok(remoteMiddleCategoryService.getCategoryTree(bo));
    }

    /**
     * 城市站隐藏或显示分类
     */
    @Log(title = "城市站隐藏或显示分类", businessType = BusinessType.INSERT)
    @PostMapping("/operate/black")
    public R<Boolean> hiddenCategory(@RequestBody SohuCategoryBo bo) {
        return toAjax(remoteMiddleCategoryService.hiddenCategory(bo));
    }
}
