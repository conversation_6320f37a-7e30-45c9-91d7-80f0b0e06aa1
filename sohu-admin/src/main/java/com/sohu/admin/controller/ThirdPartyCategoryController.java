package com.sohu.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.admin.api.bo.ThirdPartyCategoryBo;
import com.sohu.admin.api.vo.ThirdPartyCategoryVo;
import com.sohu.admin.service.IThirdPartyCategoryService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 三方平台商品分类控制器
 * 前端访问路由地址为:/system/partyCategory
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/partyCategory")
public class ThirdPartyCategoryController extends BaseController {

    private final IThirdPartyCategoryService iThirdPartyCategoryService;

    /**
     * 查询三方平台商品分类列表
     */
    @SaCheckPermission("system:partyCategory:list")
    @GetMapping("/list")
    public TableDataInfo<ThirdPartyCategoryVo> list(ThirdPartyCategoryBo bo, PageQuery pageQuery) {
        return iThirdPartyCategoryService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出三方平台商品分类列表
     */
    @SaCheckPermission("system:partyCategory:export")
    @Log(title = "三方平台商品分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ThirdPartyCategoryBo bo, HttpServletResponse response) {
        List<ThirdPartyCategoryVo> list = iThirdPartyCategoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "三方平台商品分类", ThirdPartyCategoryVo.class, response);
    }

    /**
     * 获取三方平台商品分类详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:partyCategory:query")
    @GetMapping("/{id}")
    public R<ThirdPartyCategoryVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iThirdPartyCategoryService.queryById(id));
    }

    /**
     * 新增三方平台商品分类
     */
    @SaCheckPermission("system:partyCategory:add")
    @Log(title = "三方平台商品分类", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody ThirdPartyCategoryBo bo) {
        return toAjax(iThirdPartyCategoryService.insertByBo(bo));
    }

    /**
     * 修改三方平台商品分类
     */
    @SaCheckPermission("system:partyCategory:edit")
    @Log(title = "三方平台商品分类", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody ThirdPartyCategoryBo bo) {
        return toAjax(iThirdPartyCategoryService.updateByBo(bo));
    }

    /**
     * 删除三方平台商品分类
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:partyCategory:remove")
    @Log(title = "三方平台商品分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iThirdPartyCategoryService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
