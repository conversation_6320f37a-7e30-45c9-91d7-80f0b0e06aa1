<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.shopgoods.mapper.SohuProductMapper">

    <resultMap type="com.sohu.shopgoods.domain.SohuProduct" id="SohuProductResult">
        <result property="id" column="id"/>
        <result property="siteId" column="site_id"/>
        <result property="merId" column="mer_id"/>
        <result property="image" column="image"/>
        <result property="flatPattern" column="flat_pattern"/>
        <result property="sliderImage" column="slider_image"/>
        <result property="storeName" column="store_name"/>
        <result property="storeInfo" column="store_info"/>
        <result property="keyword" column="keyword"/>
        <result property="cateId" column="cate_id"/>
        <result property="brandId" column="brand_id"/>
        <result property="categoryId" column="category_id"/>
        <result property="guaranteeIds" column="guarantee_ids"/>
        <result property="price" column="price"/>
        <result property="currency" column="currency"/>
        <result property="vipPrice" column="vip_price"/>
        <result property="otPrice" column="ot_price"/>
        <result property="postage" column="postage"/>
        <result property="unitName" column="unit_name"/>
        <result property="sales" column="sales"/>
        <result property="stock" column="stock"/>
        <result property="giveIntegral" column="give_integral"/>
        <result property="cost" column="cost"/>
        <result property="varSales" column="var_sales"/>
        <result property="browse" column="browse"/>
        <result property="sort" column="sort"/>
        <result property="ranks" column="ranks"/>
        <result property="specType" column="spec_type"/>
        <result property="productType" column="product_type"/>
        <result property="isRecycle" column="is_recycle"/>
        <result property="isForced" column="is_forced"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="isShow" column="is_show"/>
        <result property="independentIsShow" column="independent_is_show"/>
        <result property="independentPrice" column="independent_price"/>
        <result property="independentRatio" column="independent_ratio"/>
        <result property="independentType" column="independent_type"/>
        <result property="reason" column="reason"/>
        <result property="isDel" column="is_del"/>
        <result property="deliveryMode" column="delivery_mode"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getPlatformPageList" resultType="com.sohu.shopgoods.api.vo.SohuProductVo" parameterType="Map">
        SELECT p.id,p.image,p.store_name as storeName,p.keyword,p.category_id as
        categoryId,p.price,p.postage,p.sales,p.stock,p.var_sales as varSales, p.delivery_mode AS deliveryMode
        p.is_forced as isForced,p.audit_status as auditStatus,p.reason,p.ranks ,m.name as merchantName,m.is_self as
        isSelf,p.ranks,p.independent_is_show,p.independent_ratio,p.independent_price,p.first_category_id as
        firstCategoryId,p.update_time
        FROM sohu_product AS p
        right join sohu_merchant AS m on p.mer_id = m.id
        where p.is_del = 0 and p.is_recycle = 0
        <choose>
            <when test='map.type == "1"'>
                and p.is_forced = 0
                and p.audit_status = "PASS"
                and p.is_show = 1
            </when>
            <when test='map.type == "2"'>
                and p.is_show = 0
                and p.audit_status = "PASS"
            </when>
            <when test='map.type == "6"'>
                and (p.audit_status = "WAITAPPROVE" or p.audit_status = "WaitRobotApprove")
            </when>
            <when test='map.type == "7"'>
                and p.audit_status = "FALSE"
            </when>
        </choose>
        <if test="map.id != null and map.id != ''">
            AND p.id = #{map.id}
        </if>
        <if test="map.storeName != null and map.storeName !='' ">
            AND p.store_name like CONCAT('%', #{map.storeName}, '%')
        </if>
        <if test="map.merId != null and map.merId !='' ">
            and p.mer_id = #{map.merId}
        </if>
        <if test="map.sysSource != null and map.sysSource !='' ">
            and p.sys_source = #{map.sysSource}
        </if>
        <if test="map.self != null">
            and m.is_self = #{map.self}
        </if>
        <if test="map.productType != null">
            and p.product_type = #{map.productType}
        </if>
        <if test="map.deliveryMode != null">
            and p.delivery_mode = #{map.deliveryMode}
        </if>
        <if test="map.categoryIds != null and map.categoryIds !='' ">
            and find_in_set(p.category_id, #{map.categoryIds, jdbcType=VARCHAR})
        </if>
        <if test="map.keywords != null and map.keywords !='' ">
            and (p.store_name like CONCAT('%', #{map.keywords}, '%')
            or p.keyword like CONCAT('%', #{map.keywords}, '%'))
        </if>
        <if test="map.isBlack != null and map.isBlack == true and map.productIds != null and map.productIds !='' ">
            AND find_in_set(p.id, #{map.productIds, jdbcType=VARCHAR})
        </if>
        <if test="map.isBlack != null and map.isBlack == true and map.productIds != null and map.productIds =='' ">
            AND p.id = 0
        </if>
        <if test="map.isBlack != null and map.isBlack == false and map.productIds != null and map.productIds !='' ">
            AND !find_in_set(p.id, #{map.productIds, jdbcType=VARCHAR})
        </if>
        ORDER BY p.ranks desc, p.id desc
    </select>

    <update id="operationStock">
        UPDATE sohu_product
        <set>
            <if test="type.equals('add')">
                stock = stock + #{num},
                sales = sales - #{num},
            </if>
            <if test="type.equals('sub')">
                stock = stock - #{num},
                sales = sales + #{num},
                <if test="type.equals('sub') and num > 0">
                    quota = quota - #{num},
                </if>
                <!-- 扣减时加乐观锁保证库存不为负 -->
                <if test="type.equals('sub') and num > 0">
                    AND (quota >= #{num})
                </if>
                <!-- 扣减时加乐观锁保证库存不为负 -->
                <if test="type.equals('sub') and num &lt;= 0">
                    AND (stock >= #{num})
                </if>
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="getUserShopWindowPage" resultType="com.sohu.shopgoods.domain.SohuProduct" parameterType="Map">
        SELECT p.id,p.image,p.store_name as storeName,p.keyword,p.category_id as
        categoryId,p.price,p.postage,p.sales,p.stock,p.var_sales as varSales,
        p.is_forced as isForced,p.audit_status as auditStatus,p.reason,p.ranks, p.delivery_mode AS deliveryMode,
        p.ranks,p.independent_is_show,p.independent_ratio,p.independent_price, w.id as windowId ,
        w.product_top as productTop
        FROM sohu_product_window AS w
        INNER join sohu_product AS p on p.id = w.product_id and w.user_id = #{map.userId}
        where p.is_del = 0
        and p.is_recycle = 0
        and p.is_forced = 0
        and p.audit_status = "PASS"
        and p.is_show = 1
        <if test="map.productType != null">
            and p.product_type = #{map.productType}
        </if>
        <if test="map.independentIsShow != null ">
            and p.independent_is_show = #{map.independentIsShow}
        </if>
        <if test="map.siteId != null ">
            and p.site_id = #{map.siteId}
        </if>
        <if test="map.categoryIds != null and map.categoryIds !='' ">
            and find_in_set(p.category_id, #{map.categoryIds, jdbcType=VARCHAR})
        </if>
        <if test="map.productIds != null and map.productIds !='' ">
            and find_in_set(p.id, #{map.productIds, jdbcType=VARCHAR})
        </if>
        <if test="map.keywords != null and map.keywords !='' ">
            and (p.store_name like CONCAT('%', #{map.keywords}, '%')
            or p.keyword like CONCAT('%', #{map.keywords}, '%'))
        </if>
        <if test="map.sysSource != null and map.sysSource !='' ">
            and p.sys_source = #{map.sysSource}
        </if>
        ORDER BY
        <if test="map.salesOrder != null and map.salesOrder !='' and map.salesOrder == 'desc' ">
            (p.sales + p.var_sales) desc ,w.update_time desc ,productTop desc, p.ranks desc, p.sort desc, p.id desc
        </if>
        <if test="map.salesOrder != null and map.salesOrder !='' and map.salesOrder == 'asc' ">
            (p.sales + p.var_sales) asc ,w.update_time desc ,productTop desc, p.ranks desc, p.sort desc, p.id desc
        </if>
        <if test="map.price != null and map.price !='' and map.price == 'desc' ">
            p.price desc ,w.update_time desc ,productTop desc, p.ranks desc, p.sort desc, p.id desc
        </if>
        <if test="map.price != null and map.price !='' and map.price == 'asc' ">
            p.price asc ,w.update_time desc ,productTop desc, p.ranks desc, p.sort desc, p.id desc
        </if>
        <if test="map.sort != null and map.sort !='' ">
            productTop desc, w.update_time desc ,p.ranks desc, p.sort desc, p.id desc
        </if>

    </select>

    <select id="getUserShopWindowIndependentPage" resultType="com.sohu.shopgoods.domain.SohuProduct"
            parameterType="Map">
        SELECT p.id,p.image,p.store_name as storeName,p.keyword,p.category_id as
        categoryId,p.price,p.postage,p.sales,p.stock,p.var_sales as varSales,
        p.is_forced as isForced,p.audit_status as auditStatus,p.reason,p.ranks, p.delivery_mode AS deliveryMode,
        p.ranks,p.independent_is_show,p.independent_ratio,p.independent_price, w.id as windowId
        FROM sohu_product AS p
        <if test="map.mcnId != null">
            INNER JOIN sohu_product_window_mcn wm ON p.id=wm.product_id AND wm.mcn_id = #{map.mcnId}
        </if>
        LEFT join sohu_product_window AS w on p.id = w.product_id and w.user_id = #{map.userId}
        where p.is_del = 0
        and p.is_recycle = 0
        and p.is_forced = 0
        and p.audit_status = "PASS"
        and p.is_show = 1
        and w.id IS NULL
        <if test="map.mcnId != null">
            AND wm.expiration_time <![CDATA[ >= ]]> #{map.expirationTime}
            AND wm.is_del = 0
        </if>
        <if test="map.productType != null">
            and p.product_type = #{map.productType}
        </if>
        <if test="map.independentIsShow != null ">
            and p.independent_is_show = #{map.independentIsShow}
        </if>
        <if test="map.siteId != null ">
            and p.site_id = #{map.siteId}
        </if>
        <if test="map.categoryIds != null and map.categoryIds !='' ">
            and find_in_set(p.category_id, #{map.categoryIds, jdbcType=VARCHAR})
        </if>
        <if test="map.productIds != null and map.productIds !='' ">
            and find_in_set(p.id, #{map.productIds, jdbcType=VARCHAR})
        </if>
        <if test="map.keywords != null and map.keywords !='' ">
            and (p.store_name like CONCAT('%', #{map.keywords}, '%')
            or p.keyword like CONCAT('%', #{map.keywords}, '%'))
        </if>
        <if test="map.sysSource != null and map.sysSource !='' ">
            and p.sys_source = #{map.sysSource}
        </if>
        ORDER BY
        <if test="map.salesOrder != null and map.salesOrder !='' and map.salesOrder == 'desc' ">
            (p.sales + p.var_sales) desc
        </if>
        <if test="map.salesOrder != null and map.salesOrder !='' and map.salesOrder == 'asc' ">
            (p.sales + p.var_sales) asc
        </if>
        <if test="map.price != null and map.price !='' and map.price == 'desc' ">
            p.price desc
        </if>
        <if test="map.price != null and map.price !='' and map.price == 'asc' ">
            p.price asc
        </if>
        <if test="map.sort != null and map.sort !='' ">
            p.ranks desc, p.sort desc, p.id desc
        </if>
    </select>

    <select id="getMcnShopWindowIndependentPage" resultType="com.sohu.shopgoods.domain.SohuProduct"
            parameterType="Map">
        SELECT p.id,p.image,p.store_name as storeName,p.keyword,p.category_id as
        categoryId,p.price,p.postage,p.sales,p.stock,p.var_sales as varSales,
        p.is_forced as isForced,p.audit_status as auditStatus,p.reason,p.ranks, p.delivery_mode AS deliveryMode,
        p.ranks,p.independent_is_show,p.independent_ratio,p.independent_price,p.store_info,p.mer_id
        FROM sohu_product AS p
        LEFT JOIN sohu_product_window_mcn wm ON p.id=wm.product_id AND wm.mcn_id = #{map.mcnId} AND wm.is_del = 0
        WHERE p.is_del = 0
        AND p.is_recycle = 0
        AND p.is_forced = 0
        AND p.audit_status = "PASS"
        AND p.is_show = 1
        AND wm.id IS NULL
        <if test="map.categoryId != null">
            and p.category_id = #{map.categoryId}
        </if>
        <if test="map.productName != null and map.productName !=''">
            AND p.store_name like CONCAT('%', #{map.productName}, '%')
        </if>
        <if test="map.independentRatio != null">
            and p.independent_ratio = #{map.independentRatio}
        </if>
        <if test="map.maxRatio != null">
            and p.independent_ratio <![CDATA[ <= ]]> #{map.maxRatio}
        </if>
        <if test="map.minRatio != null">
            and p.independent_ratio <![CDATA[ > ]]> #{map.minRatio}
        </if>
        <if test="map.sysSource != null and map.sysSource !='' ">
            and p.sys_source = #{map.sysSource}
        </if>
        ORDER BY p.update_time DESC
    </select>

    <select id="getMcnShopWindowPage" resultType="com.sohu.shopgoods.domain.SohuProduct"
            parameterType="Map">
        SELECT p.id,p.image,p.store_name as storeName,p.keyword,p.category_id as
        categoryId,p.price,p.postage,p.sales,p.stock,p.var_sales as varSales,
        p.is_forced as isForced,p.audit_status as auditStatus,p.reason,p.ranks, p.delivery_mode AS deliveryMode,
        p.ranks,p.independent_is_show,p.independent_ratio,p.independent_price,p.store_info,p.mer_id
        ,wm.id as windowId,wm.expiration_time
        FROM sohu_product AS p
        INNER JOIN sohu_product_window_mcn wm ON p.id=wm.product_id AND wm.mcn_id = #{map.mcnId} AND wm.is_del = 0
        WHERE p.is_del = 0
        AND p.is_recycle = 0
        AND p.is_forced = 0
        AND p.audit_status = "PASS"
        AND p.is_show = 1
        <if test="map.categoryId != null">
            and p.category_id = #{map.categoryId}
        </if>
        <if test="map.productName != null and map.productName !=''">
            AND p.store_name like CONCAT('%', #{map.productName}, '%')
        </if>
        <if test="map.independentRatio != null">
            and p.independent_ratio = #{map.independentRatio}
        </if>
        <if test="map.maxRatio != null">
            and p.independent_ratio <![CDATA[ <= ]]> #{map.maxRatio}
        </if>
        <if test="map.minRatio != null">
            and p.independent_ratio <![CDATA[ > ]]> #{map.minRatio}
        </if>
        <if test="map.sysSource != null and map.sysSource !='' ">
            and p.sys_source = #{map.sysSource}
        </if>
        ORDER BY p.update_time DESC
    </select>

    <select id="selectProducPlayletList" resultType="com.sohu.shopgoods.api.vo.SohuProductPlayletListVo">
        SELECT
        p.id productId,
        m.`name` merName,
        p.store_name productName,
        p.price price,
        p.audit_status productState,
        p.image productImage,
        pc.state,
        pc.start_time startTime,
        pc.over_time overTime,
        pc.id id
        FROM
        sohu_product p
        INNER JOIN sohu_merchant m ON p.mer_id=m.id
        LEFT JOIN sohu_playlet_cart pc ON p.id = pc.relevance_id
        <if test="bo.isPlaylet != null ">
            AND pc.is_playlet=#{bo.isPlaylet}
        </if>
        <if test="bo.playletId != null ">
            AND pc.playlet_id=#{bo.playletId}
        </if>
        <if test="bo.videoId != null ">
            AND pc.video_id=#{bo.videoId}
        </if>
        WHERE p.audit_status='PASS' AND p.is_del=0 AND p.is_recycle=0 AND p.is_show=1 AND p.is_forced=0
        <if test="bo.productName != null and bo.productName != ''">
            AND p.store_name like concat('%',#{bo.productName},'%')
        </if>
        <if test="bo.merName != null and bo.merName != ''">
            AND m.`name` like concat('%',#{bo.merName},'%')
        </if>
        <if test="bo.state != null and bo.state != ''">
            AND pc.state =#{bo.state}
        </if>
        GROUP BY p.id
        ORDER BY
        <choose>
            <when test="bo.sortBy == 'startTimeAsc'">
                p.start_time ASC
            </when>
            <when test="bo.sortBy == 'startTimeDesc'">
                p.start_time DESC
            </when>
            <when test="bo.sortBy == 'overTimeAsc'">
                p.over_time ASC
            </when>
            <when test="bo.sortBy == 'overTimeDesc'">
                p.over_time DESC
            </when>
            <when test="bo.sortBy == 'stateAsc'">
                FIELD(p.state, 'OnShelf', 'WaitShelf', 'OffShelf'), p.update_time DESC
            </when>
            <when test="bo.sortBy == 'stateDesc'">
                FIELD(p.state, 'OffShelf', 'WaitShelf', 'OnShelf'), p.update_time DESC
            </when>
            <otherwise>
                p.id ASC
            </otherwise>
        </choose>
    </select>

    <select id="groupShowNum" resultType="com.sohu.shopgoods.api.vo.ProductShowVo">
        SELECT
        is_show,
        count( 1 ) cnt
        FROM
        sohu_product
        WHERE
        audit_status = 'PASS' and is_del = 0
        and create_time &lt;#{endTime}
        GROUP BY
        is_show
    </select>
</mapper>
