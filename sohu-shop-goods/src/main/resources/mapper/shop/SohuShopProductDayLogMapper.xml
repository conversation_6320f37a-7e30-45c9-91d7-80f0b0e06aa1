<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.shopgoods.mapper.SohuShopProductDayLogMapper">

    <resultMap type="com.sohu.shopgoods.domain.SohuShopProductDayLog" id="SohuShopProductDayLogResult">
    <result property="id" column="id"/>
    <result property="date" column="date"/>
    <result property="addProductNum" column="add_product_num"/>
    <result property="pageView" column="page_view"/>
    <result property="collectNum" column="collect_num"/>
    <result property="addCartNum" column="add_cart_num"/>
    <result property="orderProductNum" column="order_product_num"/>
    <result property="orderSuccessProductNum" column="order_success_product_num"/>
    </resultMap>


</mapper>
