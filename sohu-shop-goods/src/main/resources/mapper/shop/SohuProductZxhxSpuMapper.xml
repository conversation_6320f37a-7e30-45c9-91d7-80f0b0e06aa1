<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.shopgoods.mapper.SohuProductZxhxSpuMapper">

    <resultMap type="com.sohu.shopgoods.domain.SohuProductZxhxSpu" id="SohuProductZxhxSpuResult">
        <result property="id" column="id"/>
        <result property="thirdBrandId" column="third_brand_id"/>
        <result property="thirdCategoryId" column="third_category_id"/>
        <result property="thirdSpuId" column="third_spu_id"/>
        <result property="image" column="image"/>
        <result property="storeName" column="store_name"/>
        <result property="sliderImage" column="slider_image"/>
        <result property="productType" column="product_type"/>
        <result property="isShow" column="is_show"/>
        <result property="isOverseas" column="is_overseas"/>
        <result property="storeInfo" column="store_info"/>
        <result property="keyword" column="keyword"/>
        <result property="price" column="price"/>
        <result property="marketPrice" column="market_price"/>
        <result property="groupPrice" column="group_price"/>
        <result property="rate" column="rate"/>
        <result property="specType" column="spec_type"/>
        <result property="unitName" column="unit_name"/>
        <result property="syncTime" column="sync_time"/>
        <result property="listingTime" column="listing_time"/>
        <result property="offShelfReason" column="off_shelf_reason"/>
    </resultMap>


</mapper>
