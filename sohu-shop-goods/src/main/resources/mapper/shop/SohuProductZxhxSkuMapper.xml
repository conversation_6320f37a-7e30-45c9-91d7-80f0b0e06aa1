<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.shopgoods.mapper.SohuProductZxhxSkuMapper">

    <resultMap type="com.sohu.shopgoods.domain.SohuProductZxhxSku" id="SohuProductZxhxSkuResult">
        <result property="id" column="id"/>
        <result property="spuId" column="spu_id"/>
        <result property="thirdSpuId" column="third_spu_id"/>
        <result property="thirdSkuId" column="third_sku_id"/>
        <result property="sku" column="sku"/>
        <result property="stock" column="stock"/>
        <result property="image" column="image"/>
        <result property="price" column="price"/>
        <result property="barCode" column="bar_code"/>
        <result property="cost" column="cost"/>
        <result property="groupPrice" column="group_price"/>
        <result property="servicePrice" column="service_price"/>
        <result property="attrValue" column="attr_value"/>
        <result property="weight" column="weight"/>
        <result property="volume" column="volume"/>
        <result property="isShow" column="is_show"/>
        <result property="buyStartQty" column="buy_start_qty"/>
    </resultMap>


</mapper>
