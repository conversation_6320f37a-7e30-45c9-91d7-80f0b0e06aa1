package com.sohu.shopgoods.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.math.BigDecimal;

/**
 * 优选供应链商品
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_product_zxhx_spu")
public class SohuProductZxhxSpu extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 品牌 ID
     */
    private Long thirdBrandId;
    /**
     * 分类 ID
     */
    private Long thirdCategoryId;
    /**
     * spuId
     */
    private Long thirdSpuId;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 主图 URL
     */
    private String image;
    /**
     * 商品名称
     */
    private String storeName;
    /**
     * 轮播图
     */
    private String sliderImage;
    /**
     * 商品类型（1:普通商品 2: 虚拟商品）
     */
    private Integer productType;
    /**
     * 状态（0：未上架，1：上架）
     */
    private Integer isShow;
    /**
     * 是否海外商品
     */
    private Integer isOverseas;
    /**
     * 商品详情（HTML 富文本）
     */
    private String storeInfo;
    /**
     * 关键字
     */
    private String keyword;
    /**
     * 三方商品价格
     */
    private BigDecimal price;
    /**
     * 商品零售价
     */
    private BigDecimal marketPrice;
    /**
     * 商品最低控价
     */
    private BigDecimal groupPrice;
    /**
     * 商品利润
     */
    private BigDecimal rate;
    /**
     * 规格 0单 1多
     */
    private Integer specType;
    /**
     * 单位名
     */
    private String unitName;
    /**
     * 供应链同步时间
     */
    private Date syncTime;
    /**
     * 下架原因
     */
    private String offShelfReason;

}
