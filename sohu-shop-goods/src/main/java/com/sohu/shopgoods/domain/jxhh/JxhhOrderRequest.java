package com.sohu.shopgoods.domain.jxhh;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class JxhhOrderRequest {

    @JsonProperty("orderSn")
    private String orderSn;
    @JsonProperty("spu")
    private List<SpuDTO> spu;
    @JsonProperty("address")
    private AddressDTO address;
    @JsonProperty("strictMode")
    private Boolean strictMode;

    @NoArgsConstructor
    @Data
    public static class AddressDTO {
        @JsonProperty("consignee")
        private String consignee;
        @JsonProperty("phone")
        private String phone;
        @JsonProperty("province")
        private String province;
        @JsonProperty("city")
        private String city;
        @JsonProperty("area")
        private String area;
        @JsonProperty("street")
        private String street;
        @JsonProperty("description")
        private String description;
    }

    @NoArgsConstructor
    @Data
    public static class SpuDTO {
        @JsonProperty("sku")
        private Integer sku;
        @JsonProperty("number")
        private Integer number;
    }
}
