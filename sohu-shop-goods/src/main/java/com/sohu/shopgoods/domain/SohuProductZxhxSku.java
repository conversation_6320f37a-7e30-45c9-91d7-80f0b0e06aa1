package com.sohu.shopgoods.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 优选供应链商品规格
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@TableName("sohu_product_zxhx_sku")
public class SohuProductZxhxSku implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 关联spuId
     */
    private Long spuId;
    /**
     * 关联三方spuId
     */
    private Long thirdSpuId;
    /**
     * 关联三方skuId
     */
    private Long thirdSkuId;
    /**
     * 商品属性索引值 (attr_value|attr_value[|....])
     */
    private String sku;
    /**
     * 库存数量
     */
    private Long stock;
    /**
     * SKU 图片
     */
    private String image;
    /**
     * 售价
     */
    private BigDecimal price;
    /**
     * 条形码
     */
    private String barCode;
    /**
     * 成本价
     */
    private BigDecimal cost;
    /**
     * 最低控价
     */
    private BigDecimal groupPrice;
    /**
     * 服务费
     */
    private BigDecimal servicePrice;
    /**
     * SKU 描述
     */
    private String attrValue;
    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 体积
     */
    private BigDecimal volume;
    /**
     * 状态（0：未上架，1：上架）
     */
    private Integer isShow;
    /**
     * 起购数量
     */
    private Integer buyStartQty;

}
