package com.sohu.shopgoods.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品对象 sohu_product
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_product")
public class SohuProduct extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 站点id
     */
    private Long siteId;
    /**
     * 仓库id
     */
    private Long warehouseId;
    /**
     * 狐小店商户mer_id
     */
    private Long merId;
    /**
     * 商品图片
     */
    private String image;
    /**
     * 轮播图
     */
    private String flatPattern;
    /**
     * 轮播图
     */
    private String sliderImage;
    /**
     * 商品名称
     */
    private String storeName;
    /**
     * 商品简介
     */
    private String storeInfo;
    /**
     * 关键字
     */
    private String keyword;
    /**
     * 商户分类id(逗号拼接)
     */
    private String cateId;
    /**
     * 品牌id
     */
    private Long brandId;
    /**
     * 平台分类id
     */
    private Long categoryId;
    /**
     * 保障服务ids(英文逗号拼接)
     */
    private String guaranteeIds;
    /**
     * 商品价格
     */
    private BigDecimal price;
    /**
     * 币种，默认CNY人名币
     * 人民币（CNY）、美元（USD）、欧元（EUR）、日元（JPY）、英镑（GBP）、加拿大元（CAD）、
     * 澳大利亚元（AUD）、瑞士法郎（CHF）、瑞典克朗（SEK）、新西兰元（NZD）
     */
    private String currency;
    /**
     * 会员价格
     */
    private BigDecimal vipPrice;
    /**
     * 市场价
     */
    private BigDecimal otPrice;
    /**
     * 邮费
     */
    private BigDecimal postage;
    /**
     * 单位名
     */
    private String unitName;
    /**
     * 销量
     */
    private Long sales;
    /**
     * 库存
     */
    private Integer stock;
    /**
     * 获得积分
     */
    private Long giveIntegral;
    /**
     * 成本价
     */
    private BigDecimal cost;
    /**
     * 虚拟销量
     */
    private Long varSales;
    /**
     * 浏览量
     */
    private Long browse;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 总后台排序
     */
    private Integer ranks;
    /**
     * 规格 0单 1多
     */
    private Boolean specType;
    /**
     * 商品标识（0普通商品，1返哺商品，2抖音虚拟商品，3拓展商品）
     */
    private Long productType;
    /**
     * 是否回收站
     */
    private Boolean isRecycle;
    /**
     * 是否强制下架，0-否，1-是
     */
    private Boolean isForced;
    /**
     * 审核状态：WAITAPPROVE-待审核，PASS-审核成功，FALSE-审核拒绝
     */
    private String auditStatus;
    /**
     * 拒绝原因
     */
    private String reason;
    /**
     * 是否删除
     */
    private Boolean isDel;

    /**
     * 状态（0：未上架，1：上架）
     */
    private Boolean isShow;

    /**
     * 分佣开关状态（0：不启用分佣，1：启用分佣）
     */
    private Boolean independentIsShow;

    /**
     * 是否是分销商品：0不是 1是
     */
    private Boolean independentType;

    /**
     * 分佣比例、默认0.00
     */
    private BigDecimal independentRatio;

    /**
     * 分销金额、默认0.00
     */
    private BigDecimal independentPrice;

    /**
     * 商品详情
     */
    @TableField(exist = false)
    private String content;

    /**
     * 是否置顶 0不置顶 1置顶
     */
    @TableField(exist = false)
    private Boolean productTop;

    /**
     * 商品橱窗id
     */
    @TableField(exist = false)
    private Long windowId;

    /**
     * mcnId
     */
    @TableField(exist = false)
    private Long mcnId;

    /**
     * 销售量
     */
    @TableField(exist = false)
    private Integer saleCount;

    /**
     * 游览量
     */
    @TableField(exist = false)
    private Integer viewCount;

    /**
     * 曝光量
     */
    @TableField(exist = false)
    private Integer exposeCount;

    /**
     * 退货量
     */
    @TableField(exist = false)
    private Integer refundCount;

    /**
     * 带货有效期
     */
    @TableField(exist = false)
    private Date expirationTime;

    /**
     * 平台一级分类id
     */
    private Long firstCategoryId;

    /**
     * 运费模板id
     */
    private Long freightTemplateId;

    /**
     * 第三方商品唯一标识
     */
    private String thirdProductId;

    /**
     * 开放平台三方客户端ID
     */
    private Long openClientId;

    /**
     * 是否自营：1-自营，0-非自营
     */
    private Boolean isSelf;


    /**
     * 是否热门
     */
    private Boolean isHot;

    /**
     * 猜你喜欢
     */
    private Boolean isLike;

    /**
     * 系统来源(sohuglobal:狐少少,minglereels:海外短剧)
     */
    private String SysSource;

    /**
     * 上下架时间
     */
    private Date listingTime;

    /**
     * 商品发货模式 1 商家 2 蜂助手 3 优选供应链
     */
    private Integer deliveryMode;

    /**
     * 是否需要用户输入充值账号：0-否，1-是
     */
    @TableField(exist = false)
    private Integer isAccount;

    /**
     * 充值账号类型 1 手机号
     */
    @TableField(exist = false)
    private Integer accountType;

    /**
     * 发货方式：1-手动，2-自动
     */
    @TableField(exist = false)
    private Integer deliveryType;

    /**
     * 自动发货方式 1 蜂助手
     */
    @TableField(exist = false)
    private Integer autoDeliveryType;

}
