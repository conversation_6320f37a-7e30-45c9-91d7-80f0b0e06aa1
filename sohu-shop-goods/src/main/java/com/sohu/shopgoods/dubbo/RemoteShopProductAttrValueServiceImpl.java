package com.sohu.shopgoods.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sohu.shopgoods.api.RemoteProductAttrValueService;
import com.sohu.shopgoods.api.model.SohuProductAttrValueModel;
import com.sohu.shopgoods.api.vo.SohuProductAttrValueVo;
import com.sohu.shopgoods.domain.SohuProductAttrValue;
import com.sohu.shopgoods.mapper.SohuProductAttrValueMapper;
import com.sohu.shopgoods.service.ISohuProductAttrValueService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商品服务
 *
 * @author: zc
 * @date: 2023/7/26 15:15
 * @version: 1.0.0
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteShopProductAttrValueServiceImpl implements RemoteProductAttrValueService {

    @Resource
    private final SohuProductAttrValueMapper attrValueMapper;
    @Resource
    private final ISohuProductAttrValueService attrValueService;

    @Override
    public SohuProductAttrValueModel getByIdAndProductIdAndType(Long productAttrId, Long productId, String productActivityTypeNormals) {
        LambdaQueryWrapper<SohuProductAttrValue> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuProductAttrValue::getId, productAttrId);
        lqw.eq(SohuProductAttrValue::getProductId, productId);
        lqw.eq(SohuProductAttrValue::getType, productActivityTypeNormals);
        lqw.eq(SohuProductAttrValue::getIsDel, false);
        SohuProductAttrValueModel valueModel = new SohuProductAttrValueModel();
        BeanUtils.copyProperties(attrValueMapper.selectOne(lqw), valueModel);
        return valueModel;
    }

    @Override
    public Boolean operationStock(Long attrValueId, Integer num, String sub, String type, Integer attrValueVersion) {
        return attrValueService.operationStock(attrValueId, num, sub, type, attrValueVersion);
    }

    @Override
    public SohuProductAttrValueModel queryById(Long productAttrValueId) {
        SohuProductAttrValueModel valueModel = new SohuProductAttrValueModel();
        BeanUtils.copyProperties(attrValueMapper.selectById(productAttrValueId), valueModel);
        return valueModel;
    }

    @Override
    public List<SohuProductAttrValueVo> listByIds(List<Long> productAttrValueIds) {
        return attrValueMapper.selectVoBatchIds(productAttrValueIds);
    }

}
