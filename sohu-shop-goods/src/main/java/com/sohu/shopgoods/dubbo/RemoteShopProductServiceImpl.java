package com.sohu.shopgoods.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.utils.JsonUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.RemoteProductService;
import com.sohu.shopgoods.api.bo.*;
import com.sohu.shopgoods.api.domain.*;
import com.sohu.shopgoods.api.model.*;
import com.sohu.shopgoods.api.vo.*;
import com.sohu.shopgoods.domain.SohuProduct;
import com.sohu.shopgoods.domain.SohuProductWindow;
import com.sohu.shopgoods.domain.SohuProductWindowMcn;
import com.sohu.shopgoods.service.*;
import io.seata.common.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 商品服务
 *
 * @author: zc
 * @date: 2023/7/26 15:15
 * @version: 1.0.0
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteShopProductServiceImpl implements RemoteProductService {

    private final ISohuProductService productService;
    private final ISohuProductWindowService productWindowService;
    private final ISohuProductWindowMcnService productWindowMcnService;
    private final ISohuProductReplyService productReplyService;
    private final ISohuProductCategoryPcService productCategoryPcService;

    @Override
    public TableDataInfo<SohuIndexProductModel> getHomeList(ShopProductReqBo shopProductReqBo, PageQuery pageQuery) {
        return productService.homeIndexPage(shopProductReqBo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuIndexProductModel> userShopWindowPage(ShopProductReqBo shopProductReqBo, PageQuery pageQuery) {
        return productService.userShopWindowPage(shopProductReqBo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuIndexProductModel> mcnShopWindowPage(McnShopProductReqBo bo, PageQuery pageQuery) {
        return productService.mcnShopWindowPage(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuIndexProductModel> userShopWindowIndependentPage(ShopProductReqBo shopProductReqBo, PageQuery pageQuery) {
        return productService.userShopWindowIndependentPage(shopProductReqBo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuIndexProductModel> mcnShopWindowIndependentPage(McnShopProductReqBo bo, PageQuery pageQuery) {
        return productService.mcnShopWindowIndependentPage(bo, pageQuery);
    }

    @Override
    public Boolean insertByBo(SohuProductWindowModel bo) {
        SohuProductWindowBo sohuProductWindowBo = new SohuProductWindowBo();
        BeanUtils.copyProperties(bo, sohuProductWindowBo);
        return productWindowService.insertByBo(sohuProductWindowBo);
    }

    @Override
    public Boolean updateByBoTop(SohuProductWindowModel bo) {
        SohuProductWindowBo productWindowBo = new SohuProductWindowBo();
        BeanUtils.copyProperties(bo, productWindowBo);
        return productWindowService.updateByBo(productWindowBo);
    }

    @Override
    public boolean deleteWindow(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new RuntimeException("参数为空");
        }
        return productWindowService.deleteWithValidByIds(ids, true);
    }

    @Override
    public List<SohuProductWindowModel> selectByUserIdsAndProIds(List<Long> windowUserIds, List<Long> windowProductIds) {
        if (CollectionUtils.isEmpty(windowUserIds) && CollectionUtils.isEmpty(windowProductIds)) {
            //throw new RuntimeException("参数为空");
            return null;
        }
        List<SohuProductWindowVo> productWindowVos = productWindowService.selectByUserIdsAndProIds(windowUserIds, windowProductIds);
        List<SohuProductWindowModel> productWindowModels = Lists.newArrayList();
        for (SohuProductWindowVo productWindowVo : productWindowVos) {
            SohuProductWindowModel productWindowModel = new SohuProductWindowModel();
            BeanUtils.copyProperties(productWindowVo, productWindowModel);
            productWindowModels.add(productWindowModel);
        }
        return productWindowModels;
    }

    @Override
    public List<SohuProductWindowMcnModel> selectMcnByUserIdsAndProIds(List<Long> windowMcnUserIds, List<Long> windowMcnProductIds) {
        if (CollectionUtils.isEmpty(windowMcnUserIds) && CollectionUtils.isEmpty(windowMcnProductIds)) {
            //throw new RuntimeException("参数为空");
            return null;
        }
        List<SohuProductWindowMcnVo> productWindowMcnVos = productWindowMcnService.selectByUserIdsAndProIds(windowMcnUserIds, windowMcnProductIds);
        List<SohuProductWindowMcnModel> productWindowMcnModels = Lists.newArrayList();
        for (SohuProductWindowMcnVo productWindowMcnVo : productWindowMcnVos) {
            SohuProductWindowMcnModel productWindowModel = new SohuProductWindowMcnModel();
            BeanUtils.copyProperties(productWindowMcnVo, productWindowModel);
            productWindowMcnModels.add(productWindowModel);
        }
        return productWindowMcnModels;
    }

    @Override
    public Boolean updateBatchWindow(List<SohuProductWindowModel> productWindowModels) {
        if (CollectionUtils.isEmpty(productWindowModels)) {
            throw new RuntimeException("参数为空");
        }
        List<SohuProductWindow> productWindowList = Lists.newArrayList();
        for (SohuProductWindowModel productWindowModel : productWindowModels) {
            SohuProductWindow productWindow = new SohuProductWindow();
            BeanUtils.copyProperties(productWindowModel, productWindow);
            productWindowList.add(productWindow);
        }
        return productWindowService.updateBatch(productWindowList);
    }

    @Override
    public Boolean updateBatchWindowMcn(List<SohuProductWindowMcnModel> productWindowMcnModels) {
        if (CollectionUtils.isEmpty(productWindowMcnModels)) {
            throw new RuntimeException("参数为空");
        }
        List<SohuProductWindowMcn> productWindowMcnList = Lists.newArrayList();
        for (SohuProductWindowMcnModel productWindowMcnModel : productWindowMcnModels) {
            SohuProductWindowMcn productWindowMcn = new SohuProductWindowMcn();
            BeanUtils.copyProperties(productWindowMcnModel, productWindowMcn);
            productWindowMcnList.add(productWindowMcn);
        }
        return productWindowMcnService.updateBatch(productWindowMcnList);
    }

    @Override
    public Boolean insertByIds(SohuMcnShopProductBo bo) {
        return productWindowMcnService.insertByIds(bo);
    }

    @Override
    public Boolean productInsertByIds(List<Long> ids) {
        return productWindowService.insertByIds(ids);
    }

    @Override
    public Boolean mcnShopWindowDeleteByIds(List<Long> ids) {
        return productWindowMcnService.mcnShopWindowDeleteByIds(ids);
    }

    @Override
    public Boolean mcnWindowUpdate(SohuMcnShopProductBo bo) {
        return productWindowMcnService.mcnWindowUpdate(bo);
    }

    @Override
    public SohuIndexProductInfoModel getIndexInfo(Long id, Boolean isIndependent) {
        return productService.getInfo(id, isIndependent);
    }

    @Override
    public SohuIndexProductModel getMcnIndexInfo(Long id, Boolean isIndependent) {
        return productWindowMcnService.getMcnInfo(id, isIndependent);
    }

    @Override
    public Map<Long, List<SohuIndexProductModel>> getProductByUserIdAndMcnId(Collection<Long> userIds, Long mcnId) {
        Map<Long, List<SohuIndexProductModel>> resultMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(userIds)) {
            for (Long userId : userIds) {
                resultMap.put(userId, productWindowService.getProductByUserIdAndMcnId(userId, mcnId));
            }
        }
        return resultMap;
    }

//    @Override
//    public TableDataInfo<SohuIndexProductModel> getProductWindowTop(ShopProductWindowReqBo bo, PageQuery pageQuery) {
//        return this.productWindowService.getProductWindowTop(bo, pageQuery);
//    }

//    @Override
//    public SohuProductWindowStatModel getProductWindowStatByMcnId(Long mcnId) {
//        SohuProductWindowStatVo vo = this.productWindowMcnService.getProductWindowStatByMcnId(mcnId);
//        return BeanUtil.copyProperties(vo, SohuProductWindowStatModel.class);
//    }

    @Override
    public SohuProductReplyCountModel getReplyCount(Long id) {
        return productService.getReplyCount(id);
    }

    @Override
    public SohuProductReplyInfoModel getReplyInfo(Long id) {
        return productReplyService.getProductReply(id);
    }

    @Override
    public SohuProductInfoModel getSkuInfo(Long id) {
        return productService.getSkuInfo(id);
    }

    @Override
    public List<SohuProductModel> getLevel() {
        return productService.getLevel();
    }

    @Override
    public List<SohuProductModel> listByIds(List<Long> productIds) {
        List<SohuProduct> productList = productService.listByIds(productIds);
        return BeanCopyUtils.copyList(productList, SohuProductModel.class);
    }

    @Override
    public SohuProductModel queryById(Long productId) {
        SohuProductVo sohuProductVo = productService.queryById(productId);
        return BeanCopyUtils.copy(sohuProductVo, SohuProductModel.class);
    }

    @Override
    public Boolean operationStock(Long productId, Integer num, String type) {
        return productService.operationStock(productId, num, type);
    }

    @Override
    public TableDataInfo<SohuIndexProductModel> getMerchantProductList(ShopProductReqBo productReqBo, PageQuery pageQuery) {
        return productService.getMerchantProductList(productReqBo, pageQuery);
    }

    @Override
    public Boolean forcedRemovalAll(Long id, Long siteId) {
        return productService.forcedRemovalAll(id, siteId);
    }

    @Override
    public TableDataInfo<SohuAppProductMerchantModel> getMerchantIndependentProductList(ShopProductReqBo productReqBo, PageQuery pageQuery) {
        return productService.getMerchantIndependentProductList(productReqBo, pageQuery);
    }

    @Override
    public SohuIndexProductInfoModel getMcnShopWindowInfo(Long id, Boolean isIndependent) {
        return productService.getMcnShopWindowInfo(id, isIndependent);
    }

    @Override
    public Map<Long, SohuProductWindowStatModel> getProductWindowStatByUserIdsAndMcnId(Collection<Long> userIds, Long mcnId) {
        Map<Long, SohuProductWindowStatModel> resultMap = new HashMap<>();
        List<SohuProductWindowStatVo> list = this.productWindowService.getProductWindowStatByUserIdsAndMcnId(userIds, mcnId);
        if (CollectionUtil.isNotEmpty(list)) {
            for (SohuProductWindowStatVo vo : list) {
                resultMap.put(vo.getUserId(), BeanUtil.copyProperties(vo, SohuProductWindowStatModel.class));
            }
        }
        return resultMap;
    }

    @Override
    public Long productExposureStatistics(Long userId, Long mcnId) {
        return productWindowService.getProductExposureStatistics(userId, mcnId);
    }

    @Override
    public Long productViewStatistics(Long userId, Long mcnId) {
        return productWindowService.getProductViewStatistics(userId, mcnId);
    }

    @Override
    public Map<Integer, Long> productSexStatistics(Long userId, Long mcnId) {
        return productWindowService.getProductSexStatistics(userId, mcnId);
    }

    @Override
    public Map<String, Long> productAgeStatistics(Long userId, Long mcnId) {
        return productWindowService.getProductAgeStatistics(userId, mcnId);
    }

    @Override
    public Long productBuyStatistics(Long userId, Long mcnId) {
        return productWindowService.getProductBuyStatistics(userId, mcnId);
    }

    @Override
    public TableDataInfo<SohuProductModel> queryPageList(ShopProductReqBo bo, PageQuery pageQuery) {
        SohuProductBo sohuProductBo = BeanCopyUtils.copy(bo, SohuProductBo.class);
        TableDataInfo<SohuProductVo> dataInfo = productService.queryPageList(sohuProductBo, pageQuery);
        return TableDataInfoUtils.copyInfo(dataInfo, SohuProductModel.class);
    }

    @Override
    public TableDataInfo<SohuProductVo> queryPageListOfOnShelf(SohuProductAdBo bo, PageQuery pageQuery) {
        return productService.queryPageListOfOnShelf(bo,pageQuery);
    }

    @Override
    public List<SohuProductModel> queryList(ShopProductReqBo bo) {
        List<SohuProductVo> vos = productService.queryList(BeanCopyUtils.copy(bo, SohuProductBo.class));
        return BeanCopyUtils.copyList(vos, SohuProductModel.class);
    }

    @Override
    public Boolean insertByBo(SohuProductReqBo bo) {
        List<SohuProductAttrModel> attrList = bo.getAttrList();
        List<SohuProductAttrValueModel> attrValueList = bo.getAttrValueList();
        SohuProductBo sohuProductBo = BeanCopyUtils.copy(bo, SohuProductBo.class);
        // 临时处理浅拷贝问题
        sohuProductBo.setAttrList(BeanCopyUtils.copyList(attrList, SohuProductAttrBo.class));
        sohuProductBo.setAttrValueList(BeanCopyUtils.copyList(attrValueList, SohuProductAttrValueBo.class));
        return productService.insertByBo(sohuProductBo);
    }

    @Override
    public Boolean updateByBo(SohuProductReqBo bo) {
        List<SohuProductAttrModel> attrList = bo.getAttrList();
        List<SohuProductAttrValueModel> attrValueList = bo.getAttrValueList();
        SohuProductBo sohuProductBo = BeanCopyUtils.copy(bo, SohuProductBo.class);
        // 临时处理浅拷贝问题
        sohuProductBo.setAttrList(BeanCopyUtils.copyList(attrList, SohuProductAttrBo.class));
        sohuProductBo.setAttrValueList(BeanCopyUtils.copyList(attrValueList, SohuProductAttrValueBo.class));
        return productService.updateByBo(sohuProductBo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return productService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public Boolean deleteByIdAndType(Long id, String type) {
        return productService.deleteByIdAndType(id, type);
    }

    @Override
    public Boolean reStoreProduct(Long id) {
        return productService.reStoreProduct(id);
    }

    @Override
    public Boolean offOrPutShelf(SohuOffOrPutReqBo bo) {
        return productService.offOrPutShelf(BeanCopyUtils.copy(bo, SohuOffOrPutBo.class));
    }

    @Override
    public Boolean independentIsShow(ShopProductReqBo bo) {
        return productService.independentIsShow(BeanCopyUtils.copy(bo, SohuProductBo.class));
    }

    @Override
    public SohuProductModel queryProductById(Long id) {
        return productService.queryProductById(id);
    }

    @Override
    public List<SohuProductSyncVo> queryList() {
        return productService.queryList();
    }

    @Override
    public Boolean updateBatchById(List<SohuProductSyncBo> productList) {
        return productService.updateBatchById(productList);
    }

    @Override
    public int syncFirstCategoryToProduct() {
        return productService.syncFirstCategoryToProduct();
    }

    @Override
    public Boolean openProductSaveV1(SohuOpenProductSaveV1Bo bo) {
        return productService.openProductSaveV1(bo);
    }

    @Override
    public Boolean openProductSaveV2(SohuOpenProductSaveV2Bo bo) {
        return productService.openProductSaveV2(bo);
    }

    @Override
    public Boolean openUpdateProductShelfV1(SohuOpenProductShelfEditV1Bo bo) {
        return productService.openUpdateProductShelfV1(bo);
    }

    @Override
    public Boolean openUpdateProductShelfV2(SohuOpenProductShelfEditV2Bo bo) {
        return productService.openUpdateProductShelfV2(bo);
    }

    @Override
    public Boolean openUpdateProductStockV1(SohuOpenProductStockEditV1Bo bo) {
        return productService.openUpdateProductStockV1(bo);
    }

    @Override
    public Boolean openUpdateProductStockV2(SohuOpenProductStockEditV2Bo bo) {
        return productService.openUpdateProductStockV2(bo);
    }

    @Override
    public SohuOpenProductV1Vo getBythirdProductIdV1(SohuOpenProductQueryV1Bo bo) {
        return productService.getBythirdProductIdV1(bo);
    }

    @Override
    public SohuOpenProductV2Vo getBythirdProductIdV2(SohuOpenProductQueryV2Bo bo) {
        return productService.getBythirdProductIdV2(bo);
    }

    @Override
    public BigDecimal extractedGoodRation(SohuProductPmBo bo) {
        SohuProduct sohuProduct = BeanUtil.copyProperties(bo, SohuProduct.class);
        return productService.extractedGoodRation(sohuProduct);
    }

    @Override
    public List<SohuProductAttrVo> getListByProductIdAndType(Long id, String type) {
        return productService.getListByProductIdAndType(id, type);
    }

    @Override
    public TableDataInfo<SohuProductVo> shopCartList(Long videoId, PageQuery pageQuery) {
        return productService.shopCartList(videoId, pageQuery);
    }

    @Override
    public Boolean operateBatchPlaylet(ShopOperateBatchBo playletOperateBo) {
        return productService.operateBatchPlaylet(playletOperateBo);
    }

    @Override
    public TableDataInfo<SohuProductPlayletListVo> shopCartPlayletList(SohuPlayletShopCartQueryBo bo, PageQuery pageQuery) {
        return productService.shopCartPlayletList(bo, pageQuery);
    }

    @Override
    public Long countByAuditStatus(String auditStatus) {
        return productService.countByAuditStatus(auditStatus);
    }

    @Override
    public void productOverviewExecute(String day) {
        productService.productOverviewExecute(day);
    }

    @Override
    public List<SohuProductVo> queryGoodsByIds(List<Long> ids, String state) {
        return productService.queryGoodsByIds(ids, state);
    }

    @Override
    public SohuProductVo get(Long id) {
        return productService.get(id);
    }

    @Override
    public Long getCategoryIdByProductId(Long productId, Integer level) {
        SohuProductVo sohuProductVo = productService.get(productId);
        return this.handleCategory(sohuProductVo.getCategoryId(), level);
    }

    @Override
    public Long productCount(Long merId, String auditStatus) {
        return productService.productCount(merId,auditStatus);
    }

    @Override
    public SohuProductVo getInfoById(Long id) {
        return productService.queryById(id);
    }

    /**
     * 查询指定层级分类id
     *
     * @param categoryId
     * @param level
     * @return
     */
    private Long handleCategory(Long categoryId, Integer level) {
        log.error("categoryId:{}, level:{}", categoryId, level);
        SohuProductCategoryPcVo productCategoryPcVo = productCategoryPcService.queryById(categoryId);
        if (Objects.isNull(productCategoryPcVo)) {
            return null;
        }
        log.error("SohuProductCategoryPcVo:{}", JsonUtils.toJsonString(productCategoryPcVo));
        if (productCategoryPcVo.getLevel() == level.longValue()) {
            return productCategoryPcVo.getId();
        }
        return this.handleCategory(productCategoryPcVo.getPid(), level);
    }
}
