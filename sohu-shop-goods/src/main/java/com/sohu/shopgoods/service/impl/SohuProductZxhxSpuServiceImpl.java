package com.sohu.shopgoods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.sohu.common.core.utils.CollUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.bo.SohuProductZxhxSkuBo;
import com.sohu.shopgoods.domain.SohuProductVirtualSku;
import com.sohu.shopgoods.domain.SohuProductZxhxSku;
import com.sohu.shopgoods.mapper.SohuProductZxhxSkuMapper;
import com.sohu.third.zxhuixuan.supply.constant.ShelfStatusEnum;
import com.sohu.third.zxhuixuan.supply.constant.ZXHuiXuanConstant;
import com.sohu.third.zxhuixuan.supply.model.ZXHuiXuanConfig;
import com.sohu.third.zxhuixuan.supply.model.product.Sku;
import com.sohu.third.zxhuixuan.supply.model.product.SkuAttr;
import com.sohu.third.zxhuixuan.supply.model.product.SpuDetails;
import com.sohu.third.zxhuixuan.supply.request.product.ZXHuiXuanProductSpuDetailsRequest;
import com.sohu.third.zxhuixuan.supply.request.product.ZXHuiXuanProductSpuListRequest;
import com.sohu.third.zxhuixuan.supply.response.product.ZXHuiXuanProductSpuDetailsResponse;
import com.sohu.third.zxhuixuan.supply.response.product.ZXHuiXuanProductSpuListResponse;
import com.sohu.third.zxhuixuan.supply.service.ZXHuiXuanProductsService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.sohu.shopgoods.api.bo.SohuProductZxhxSpuBo;
import com.sohu.shopgoods.api.vo.SohuProductZxhxSpuVo;
import com.sohu.shopgoods.domain.SohuProductZxhxSpu;
import com.sohu.shopgoods.mapper.SohuProductZxhxSpuMapper;
import com.sohu.shopgoods.service.ISohuProductZxhxSpuService;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 优选供应链商品Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RequiredArgsConstructor
@Service
public class SohuProductZxhxSpuServiceImpl implements ISohuProductZxhxSpuService {

    private final SohuProductZxhxSpuMapper baseMapper;

    private final SohuProductZxhxSkuMapper productZxhxSkuMapper;

    @Value("${zxhx.secret}")
    private String secret;

    @Value("${zxhx.appId}")
    private String appId;


    /**
     * 查询优选供应链商品
     */
    @Override
    public SohuProductZxhxSpuVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询优选供应链商品列表
     */
    @Override
    public TableDataInfo<SohuProductZxhxSpuVo> queryPageList(SohuProductZxhxSpuBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuProductZxhxSpu> lqw = buildQueryWrapper(bo);
        Page<SohuProductZxhxSpuVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询优选供应链商品列表
     */
    @Override
    public List<SohuProductZxhxSpuVo> queryList(SohuProductZxhxSpuBo bo) {
        LambdaQueryWrapper<SohuProductZxhxSpu> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuProductZxhxSpu> buildQueryWrapper(SohuProductZxhxSpuBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuProductZxhxSpu> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getThirdBrandId() != null, SohuProductZxhxSpu::getThirdBrandId, bo.getThirdBrandId());
        lqw.eq(Objects.nonNull(bo.getThirdCategoryId()), SohuProductZxhxSpu::getThirdCategoryId, bo.getThirdCategoryId());
        lqw.eq(bo.getThirdSpuId() != null, SohuProductZxhxSpu::getThirdSpuId, bo.getThirdSpuId());
        lqw.eq(StringUtils.isNotBlank(bo.getImage()), SohuProductZxhxSpu::getImage, bo.getImage());
        lqw.like(StringUtils.isNotBlank(bo.getStoreName()), SohuProductZxhxSpu::getStoreName, bo.getStoreName());
        lqw.eq(StringUtils.isNotBlank(bo.getSliderImage()), SohuProductZxhxSpu::getSliderImage, bo.getSliderImage());
        lqw.eq(bo.getProductType() != null, SohuProductZxhxSpu::getProductType, bo.getProductType());
        lqw.eq(bo.getIsShow() != null, SohuProductZxhxSpu::getIsShow, bo.getIsShow());
        lqw.eq(bo.getIsOverseas() != null, SohuProductZxhxSpu::getIsOverseas, bo.getIsOverseas());
        lqw.eq(StringUtils.isNotBlank(bo.getStoreInfo()), SohuProductZxhxSpu::getStoreInfo, bo.getStoreInfo());
        lqw.eq(StringUtils.isNotBlank(bo.getKeyword()), SohuProductZxhxSpu::getKeyword, bo.getKeyword());
        lqw.eq(bo.getPrice() != null, SohuProductZxhxSpu::getPrice, bo.getPrice());
        lqw.eq(bo.getMarketPrice() != null, SohuProductZxhxSpu::getMarketPrice, bo.getMarketPrice());
        lqw.eq(bo.getGroupPrice() != null, SohuProductZxhxSpu::getGroupPrice, bo.getGroupPrice());
        lqw.eq(bo.getRate() != null, SohuProductZxhxSpu::getRate, bo.getRate());
        lqw.eq(bo.getSpecType() != null, SohuProductZxhxSpu::getSpecType, bo.getSpecType());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), SohuProductZxhxSpu::getUnitName, bo.getUnitName());
        lqw.eq(bo.getSyncTime() != null, SohuProductZxhxSpu::getSyncTime, bo.getSyncTime());
        lqw.eq(StringUtils.isNotBlank(bo.getOffShelfReason()), SohuProductZxhxSpu::getOffShelfReason, bo.getOffShelfReason());
        return lqw;
    }

    /**
     * 新增优选供应链商品
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SohuProductZxhxSpuBo bo) {
        SohuProductZxhxSpu add = BeanUtil.toBean(bo, SohuProductZxhxSpu.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        List<SohuProductZxhxSku> skuList = BeanUtil.copyToList(bo.getSkuList(), SohuProductZxhxSku.class);
        if (CollectionUtil.isNotEmpty(skuList)) {
            skuList.forEach(row -> row.setSpuId(bo.getId()));
            productZxhxSkuMapper.insertBatch(skuList);
        }
        return flag;
    }

    /**
     * 修改优选供应链商品
     */
    @Override
    public Boolean updateByBo(SohuProductZxhxSpuBo bo) {
        SohuProductZxhxSpu update = BeanUtil.toBean(bo, SohuProductZxhxSpu.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuProductZxhxSpu entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除优选供应链商品
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public void syncSpu() {
        ZXHuiXuanConfig config = new ZXHuiXuanConfig();
        config.setAppId(appId);
        config.setSecret(secret);
        ZXHuiXuanProductSpuListRequest request = new ZXHuiXuanProductSpuListRequest();
        request.setPerPage(100L);
        ZXHuiXuanProductSpuListResponse response = ZXHuiXuanProductsService.getSpuIdList(request, config);
        if (!response.isSuccess()) {
            return;
        }
        for (Long spuId : response.getSpuIdList()) {
            ZXHuiXuanProductSpuDetailsRequest detailsRequest = new ZXHuiXuanProductSpuDetailsRequest();
            detailsRequest.setSpuId(spuId);
            ZXHuiXuanProductSpuDetailsResponse detailsResponse = ZXHuiXuanProductsService.getSpuIdDetails(detailsRequest, config);
            if (!detailsResponse.isSuccess()) {
                continue;
            }
            SohuProductZxhxSpuBo spuBo = toSpuBo(detailsResponse.getDetails());
            this.insertByBo(spuBo);
        }
    }

    public static SohuProductZxhxSpuBo toSpuBo(SpuDetails details) {
        if (Objects.isNull(details)) {
            return null;
        }
        SohuProductZxhxSpuBo spu = new SohuProductZxhxSpuBo();
        spu.setThirdBrandId(details.getBrandId());
        spu.setThirdCategoryId(details.getClassId());
        spu.setThirdSpuId(details.getSpuId());
        spu.setImage(details.getImage());
        spu.setStoreName(details.getStoreName());
        if (CollectionUtil.isNotEmpty(details.getSliderImage())) {
            StringBuilder sliderImage = new StringBuilder();
            int index = 1;
            for (String item : details.getSliderImage()) {
                sliderImage.append(item);
                if (index < details.getSliderImage().size()) {
                    sliderImage.append(StrPool.COMMA);
                }
                index++;
            }
            spu.setSliderImage(sliderImage.toString());
        }
        spu.setProductType(1);
        spu.setIsShow(0);
        if (Objects.equals(details.getStatus(), ShelfStatusEnum.ON_SHELF.getCode())) {
            spu.setIsShow(1);
        }
        spu.setIsOverseas(details.getIsOverseas());
        spu.setStoreInfo(details.getStoreInfo());
        spu.setKeyword(details.getKeyword());
        spu.setPrice(details.getPrice());
        spu.setMarketPrice(details.getMarketPrice());
        spu.setGroupPrice(details.getGroupPrice());
        spu.setRate(details.getRate());
        spu.setSpecType(details.getSpecType());
        spu.setUnitName(details.getUnitName());
        spu.setSyncTime(new Date());
        if (CollectionUtil.isEmpty(details.getSku())) {
            return spu;
        }
        List<SohuProductZxhxSkuBo> skuList = details.getSku().stream().map(row -> {
            SohuProductZxhxSkuBo sku = new SohuProductZxhxSkuBo();
            sku.setThirdSpuId(row.getSpuId());
            sku.setThirdSkuId(row.getSkuId());
            sku.setStock(row.getStock());
            sku.setImage(row.getImage());
            sku.setPrice(row.getMarketPrice());
            sku.setBarCode(row.getBarCode());
            sku.setCost(row.getPrice());
            sku.setGroupPrice(row.getGroupPrice());
            sku.setServicePrice(row.getServicePrice());
            StringBuilder skuStr = new StringBuilder();
            Map<String, String> attrValueMap = new HashMap<>();
            if (CollectionUtil.isEmpty(row.getAttr())) {
                skuStr.append("默认");
                attrValueMap.put("规格", "默认");
            } else {
                int index = 1;
                for (SkuAttr attr : row.getAttr()) {
                    skuStr.append(attr.getValue());
                    attrValueMap.put(attr.getName(), attr.getValue());
                    if (index < row.getAttr().size()) {
                        skuStr.append(StrPool.COMMA);
                    }
                    index++;
                }
            }
            sku.setSku(skuStr.toString());
            sku.setAttrValue(JSONUtil.toJsonStr(attrValueMap));
            sku.setWeight(row.getWeight());
            sku.setVolume(row.getVolume());
            sku.setIsShow(row.getIsShow());
            sku.setBuyStartQty(row.getBuyStartQty());
            return sku;
        }).collect(Collectors.toList());
        spu.setSkuList(skuList);
        return spu;
    }
}
