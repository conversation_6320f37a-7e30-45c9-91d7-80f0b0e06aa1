package com.sohu.shopgoods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.RemoteAdminService;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.vo.SohuCategoryBrandBusinessSettingsVo;
import com.sohu.common.core.constant.CacheNames;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.bo.SohuCategoryReqBo;
import com.sohu.shopgoods.api.bo.SohuProducrBrandPageQueryBo;
import com.sohu.shopgoods.api.bo.SohuProductBrandBo;
import com.sohu.shopgoods.api.model.SohuProductBrandModel;
import com.sohu.shopgoods.api.vo.SohuProductBrandVo;
import com.sohu.shopgoods.api.vo.SohuProductCategoryPcVo;
import com.sohu.shopgoods.constant.Constants;
import com.sohu.shopgoods.domain.SohuProductBrand;
import com.sohu.shopgoods.domain.SohuProductBrandCategory;
import com.sohu.shopgoods.domain.SohuProductCategoryPc;
import com.sohu.shopgoods.mapper.SohuProductBrandMapper;
import com.sohu.shopgoods.mapper.SohuProductCategoryPcMapper;
import com.sohu.shopgoods.service.ISohuProductBrandCategoryService;
import com.sohu.shopgoods.service.ISohuProductBrandService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.core.convert.ConversionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品品牌Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuProductBrandServiceImpl implements ISohuProductBrandService {

    private final SohuProductBrandMapper baseMapper;
    private final SohuProductCategoryPcMapper sohuProductCategoryPcMapper;

    @Resource
    private ISohuProductBrandCategoryService productBrandCategoryService;
    @Resource
    private TransactionTemplate transactionTemplate;
    private ConversionService conversionService;

    @DubboReference
    private RemoteMerchantService remoteMerchantService;
    @DubboReference
    private RemoteAdminService remoteAdminService;

    /**
     * 查询商品品牌
     */
    @Override
    public SohuProductBrandVo queryById(Long id) {
        SohuProductBrandVo sohuProductBrandVo = baseMapper.selectVoById(id);
        if (ObjectUtil.isNull(sohuProductBrandVo)) {
            return null;
        }
        SohuCategoryBrandBusinessSettingsVo categoryBrandBusinessSettings = remoteAdminService.getCategoryBrandBusinessSettings(id, Constants.CATEGORY);
        if (ObjectUtil.isNull(categoryBrandBusinessSettings)) {
            return sohuProductBrandVo;
        }
        if (categoryBrandBusinessSettings.getIsOpenPersonal() == 1) {
            sohuProductBrandVo.setBrandType(Constants.PERSONAL);
        }
        if (categoryBrandBusinessSettings.getIsOpenBusiness() == 1) {
            sohuProductBrandVo.setBrandType(Constants.BUSINESS);
        }
        if (categoryBrandBusinessSettings.getIsOpenPersonal() == 1 && categoryBrandBusinessSettings.getIsOpenBusiness() == 1) {
            sohuProductBrandVo.setBrandType(Constants.ALL);
        }

        return sohuProductBrandVo;
    }

//    /**
//     * 查询商品品牌列表
//     *
//     * @param bo
//     * @param pageQuery
//     * @return
//     */
//    @Override
//    public TableDataInfo<SohuProductBrandVo> queryPageList(SohuProductBrandBo bo, PageQuery pageQuery) {
//        bo.setIsDel(Boolean.FALSE);
//        LambdaQueryWrapper<SohuProductBrand> lqw = buildQueryWrapper(bo);
//        lqw.orderByDesc(SohuProductBrand::getSort);
//        Page<SohuProductBrandVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
//        if (ObjectUtil.isNull(result) && CollectionUtils.isEmpty(result.getRecords())) {
//            return TableDataInfoUtils.build();
//        }
//        // 组装数据表品牌与分类
//        result.getRecords().forEach(e -> {
//            // 根据品牌id获取关联的分类id
//            List<SohuProductBrandCategory> brandCategoryList = productBrandCategoryService.getLists(e.getId());
//            if (CollUtil.isEmpty(brandCategoryList)) {
//                e.setCategoryIds("");
//            } else {
//                List<String> cidList = brandCategoryList.stream().map(bc -> bc.getCateId().toString()).collect(Collectors.toList());
//                e.setCategoryIds(String.join(",", cidList));
//            }
//        });
//        return TableDataInfoUtils.build(result);
//    }

    @Override
    public TableDataInfo<SohuProductBrandVo> queryPageList(SohuProducrBrandPageQueryBo bo, PageQuery pageQuery) {
        Page<SohuProductBrandVo> page = baseMapper.queryPageList(bo, PageQueryUtils.build(pageQuery));
        if (page == null || CollectionUtils.isEmpty(page.getRecords())) {
            return TableDataInfoUtils.build();
        }

        // 1. 批量查询二级分类并建立映射
        List<Long> secondCategoryIds = page.getRecords().stream()
                .filter(record -> record.getCateIds() != null && !record.getCateIds().isEmpty())
                .flatMap(record -> Arrays.stream(record.getCateIds().split(",")).map(Long::parseLong))
                .distinct()
                .collect(Collectors.toList());

        // 2.构建分类名称
        Map<Long, String> categoryNames = this.buildCategoryNames(secondCategoryIds);

        // 3. 组装分类名称
        page.getRecords().forEach(record -> {
            if (record.getCateIds() != null && !record.getCateIds().isEmpty()) {
                String[] cateIds = record.getCateIds().split(",");
                List<String> categoryNameList = Arrays.stream(cateIds)
                        .map(Long::parseLong)
                        .map(categoryNames::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                if (!categoryNameList.isEmpty()) {
                    record.setCateNames(String.join(",", categoryNameList));
                }
            }
        });

        return TableDataInfoUtils.build(page);
    }

    /**
     * 查询商品品牌列表
     */
    @Override
    public List<SohuProductBrandVo> queryList(SohuProductBrandBo bo) {
        LambdaQueryWrapper<SohuProductBrand> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuProductBrand> buildQueryWrapper(SohuProductBrandBo bo) {
//        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuProductBrand> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), SohuProductBrand::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getIcon()), SohuProductBrand::getIcon, bo.getIcon());
        lqw.eq(bo.getSort() != null, SohuProductBrand::getSort, bo.getSort());
        lqw.eq(bo.getIsShow() != null, SohuProductBrand::getIsShow, bo.getIsShow());
        lqw.eq(null != bo.getIsDel(), SohuProductBrand::getIsDel, bo.getIsDel());
        lqw.eq(StringUtils.isNotBlank(bo.getSysSource()), SohuProductBrand::getSysSource, bo.getSysSource());
        return lqw;
    }

    /**
     * 新增商品品牌
     */
    @CachePut(cacheNames = CacheNames.PRODUCT_BRAND)
    @Override
    public Boolean insertByBo(SohuProductBrandBo bo) {
        SohuProductBrand add = BeanUtil.toBean(bo, SohuProductBrand.class);
        validEntityBeforeSave(add);
        // 添加关联分类
        Boolean execute = transactionTemplate.execute(e -> {
            baseMapper.insert(add);
            bo.setId(add.getId());
            // 添加品牌与分类关联
            if (StrUtil.isNotEmpty(bo.getCateIds())) {
                List<SohuProductBrandCategory> initList = brandCategoryInit(add.getId(), bo.getCateIds());
                productBrandCategoryService.saveBatch(initList, 100);
            }
            return Boolean.TRUE;
        });
        return execute;
//        baseMapper.insert(add);
//        return true;
    }

    /**
     * 修改商品品牌
     */
    @CachePut(cacheNames = CacheNames.PRODUCT_BRAND)
    @Override
    public Boolean updateByBo(SohuProductBrandBo bo) {
        if (ObjectUtil.isNull(bo.getId())) {
            throw new RuntimeException("品牌id不能为空");
        }
        SohuProductBrand oldBrand = getByIdException(bo.getId());
//        // 根据categoryIds为空切传输对象的isShow与数据库的isShow不同就修改品牌状态
//        if (StringUtils.isBlank(bo.getCategoryIds()) && !oldBrand.getIsShow().equals(bo.getIsShow())) {
//            oldBrand.setIsShow(bo.getIsShow());
//            return baseMapper.updateById(oldBrand) > 0;
//        }
//        if (!oldBrand.getName().equals(bo.getName())) {
//            validateName(bo.getName(),bo.getSysSource());
//        }

        SohuProductBrand update = BeanUtil.toBean(bo, SohuProductBrand.class);
        validEntityBeforeSave(update);
        return transactionTemplate.execute(e -> {
            this.baseMapper.updateById(update);
            productBrandCategoryService.deleteWithValidByIds(Collections.singletonList(update.getId()), true);
            if (StrUtil.isNotEmpty(bo.getCateIds())) {
                List<SohuProductBrandCategory> initList = brandCategoryInit(update.getId(), bo.getCateIds());
                productBrandCategoryService.saveBatch(initList, 100);
            }
            baseMapper.updateById(update);
            return Boolean.TRUE;
        });
//        this.baseMapper.updateById(update);
//        return true;
    }

    /**
     * 获取对象，校验id
     *
     * @param id
     * @return
     */
    private SohuProductBrand getByIdException(Long id) {
        SohuProductBrand brand = this.baseMapper.selectById(id);
        if (ObjectUtil.isNull(brand) || brand.getIsDel()) {
            throw new RuntimeException("品牌不存在");
        }
        return brand;
    }

    /**
     * 校验品牌名是否重复
     *
     * @param name 品牌名
     */
    @Deprecated
    private void validateName(String name, String sysSource) {
        LambdaQueryWrapper<SohuProductBrand> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuProductBrand::getIsDel, false);
        lqw.eq(SohuProductBrand::getName, name);
        lqw.eq(SohuProductBrand::getSysSource, sysSource);
        lqw.last(" limit 1");
        SohuProductBrand brand = this.baseMapper.selectOne(lqw);
        if (ObjectUtil.isNotNull(brand)) {
            throw new RuntimeException("品牌已存在");
        }
    }

    /**
     * 初始化品牌分类关联
     *
     * @param categoryIds 分类ids
     * @return List<ProductBrandCategory>
     */
    private List<SohuProductBrandCategory> brandCategoryInit(Long bid, String categoryIds) {
        String[] split = categoryIds.split(",");
        return Arrays.stream(split).map(e -> {
            SohuProductBrandCategory brandCategory = new SohuProductBrandCategory();
            brandCategory.setBrandId(bid);
            brandCategory.setCateId(Long.valueOf(e));
            return brandCategory;
        }).collect(Collectors.toList());
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuProductBrand entity) {
        // TODO 做一些数据校验,如唯一约束
        LambdaQueryWrapper<SohuProductBrand> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuProductBrand::getIsDel, false);
        lqw.eq(SohuProductBrand::getName, entity.getName());
        lqw.last(" limit 1");
        SohuProductBrand brand = this.baseMapper.selectOne(lqw);
        if (Objects.nonNull(brand) && !Objects.equals(brand.getId(), entity.getId())) {
            throw new RuntimeException("品牌已存在");
        }

    }

    /**
     * 批量删除商品品牌
     */
    @CacheEvict(cacheNames = CacheNames.PRODUCT_BRAND)
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        if (isValid) {
//            // TODO 做一些业务上的校验,判断是否需要校验
//        }
//        // 删除品牌与分类关联
//        productBrandCategoryService.deleteWithValidByIds(ids, isValid);

        return baseMapper.deleteBatchIds(ids) > 0;
    }

    //    @Cacheable(cacheNames = CacheNames.PRODUCT_BRAND, key = "#pc")
    @Override
    public List<SohuProductBrand> queryCacheList(String sysSource) {
//        Object cacheObject = RedisUtils.getCacheObject(CacheNames.PRODUCT_BRAND);
//        if (ObjectUtil.isNotNull(cacheObject)) {
//            return JSONObject.parseArray(JSON.toJSONString(cacheObject), SohuProductBrand.class);
//        }
        // 查询所有品牌--自动放入缓存
        LambdaQueryWrapper<SohuProductBrand> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuProductBrand::getIsDel, false);
        lqw.eq(SohuProductBrand::getIsShow, 1);
        //.eq(SohuProductBrand::getSysSource, sysSource)
        lqw.orderByDesc(SohuProductBrand::getSort);
        List<SohuProductBrand> productBrandLists = this.baseMapper.selectList(lqw);
        if (CollectionUtils.isNotEmpty(productBrandLists)) {
//            RedisUtils.setCacheObject(CacheNames.PRODUCT_BRAND, productBrandLists, Duration.ofMinutes(10));
            return productBrandLists;
        }
        return Collections.emptyList();
    }

    @Override
    public TableDataInfo<SohuProductBrand> queryPageListByCate(SohuCategoryReqBo bo, PageQuery pageQuery) {
        Map<String, Object> map = new HashMap<>();
        map.put("cateId", bo.getCateId());
        if (StrUtil.isNotEmpty(bo.getBrandName())) {
            map.put("brandName", bo.getBrandName());
        }
        List<SohuProductBrand> brandList = this.baseMapper.getPageListByCategory(map);
        if (CollectionUtils.isEmpty(brandList)) {
            return TableDataInfoUtils.build();
        }
        return TableDataInfoUtils.build(brandList);
    }

    @Override
    public SohuProductBrand getByName(String name) {
        LambdaQueryWrapper<SohuProductBrand> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuProductBrand::getIsDel, false)
                .eq(SohuProductBrand::getIsShow, 1)
                .eq(SohuProductBrand::getName, name)
                .last("limit 1");
        return this.baseMapper.selectOne(lqw);
    }

    @Override
    public List<SohuProductBrandVo> list(Collection<Long> brandIds) {
        LambdaQueryWrapper<SohuProductBrand> lqw = new LambdaQueryWrapper<>();
        if (CollUtil.isNotEmpty(brandIds)) {
            lqw.in(SohuProductBrand::getId, brandIds);
        }
        return this.baseMapper.selectVoList(lqw);
    }

    /**
     * 根据二级分类 ID 列表，返回二级分类 ID 和对应拼接好的一二级分类名称的 Map。
     *
     * @param secondCategoryIds 二级分类 ID 列表
     * @return Key 为二级分类 ID，Value 为拼接好的一二级分类名称的 Map
     */
    @Override
    public Map<Long, String> buildCategoryNames(List<Long> secondCategoryIds) {
        if (CollectionUtils.isEmpty(secondCategoryIds)) {
            return null;
        }

        // 1. 批量查询二级分类并建立映射
        Map<Long, SohuProductCategoryPc> secondCategoryMap = sohuProductCategoryPcMapper.selectBatchIds(secondCategoryIds)
                .stream()
                .collect(Collectors.toMap(SohuProductCategoryPc::getId, Function.identity(), (v1, v2) -> v1));

        if (secondCategoryMap.isEmpty()) {
            log.warn("未找到二级分类信息，categoryIds: {}", secondCategoryIds);
            return null;
        }

        // 2. 提取并查询一级分类，建立映射
        List<Long> firstCategoryIds = secondCategoryMap.values().stream()
                .map(SohuProductCategoryPc::getPid)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, SohuProductCategoryPc> firstCategoryMap = CollectionUtils.isEmpty(firstCategoryIds) ?
                null :
                sohuProductCategoryPcMapper.selectBatchIds(firstCategoryIds)
                        .stream()
                        .collect(Collectors.toMap(SohuProductCategoryPc::getId, Function.identity(), (v1, v2) -> v1));

        // 3. 组装分类名称
        Map<Long, String> result = new LinkedHashMap<>();
        for (Long cateId : secondCategoryIds) {
            result.put(cateId, buildCateNames(cateId, secondCategoryMap, firstCategoryMap));
        }

        return result;
    }

    @Override
    public List<SohuProductBrandVo> merchantBrand(Long merchantId, Long cateId) {
        List<SohuProductBrandVo> brandList = new ArrayList<>();
        //查询商户类目下开启的品牌
        List<Long> brandIds = remoteMerchantService.getBrandIdByPass(merchantId, cateId);
        if (CollUtil.isEmpty(brandIds)) {
            return brandList;
        }
        brandList = this.baseMapper.selectVoList(new LambdaQueryWrapper<SohuProductBrand>().in(SohuProductBrand::getId, brandIds));
        return brandList;
    }

    /**
     * 组装分类名称
     *
     * @param cateId            二级分类id
     * @param secondCategoryMap 二级分类map
     * @param firstCategoryMap  一级分类map
     * @return 分类名称
     */

    private String buildCateNames(Long cateId,
                                  Map<Long, SohuProductCategoryPc> secondCategoryMap,
                                  Map<Long, SohuProductCategoryPc> firstCategoryMap) {
        SohuProductCategoryPc second = secondCategoryMap.get(cateId);
        if (second == null) {
            return "未知分类";
        }

        SohuProductCategoryPc first = firstCategoryMap.get(second.getPid());
        return first == null ? second.getName() : first.getName() + "-" + second.getName();
    }

}
