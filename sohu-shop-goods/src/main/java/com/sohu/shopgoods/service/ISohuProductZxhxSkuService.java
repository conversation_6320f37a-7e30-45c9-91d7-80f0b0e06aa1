package com.sohu.shopgoods.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.vo.SohuProductZxhxSkuVo;
import com.sohu.shopgoods.api.bo.SohuProductZxhxSkuBo;

import java.util.Collection;
import java.util.List;

/**
 * 优选供应链商品规格Service接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface ISohuProductZxhxSkuService {

    /**
     * 查询优选供应链商品规格
     */
    SohuProductZxhxSkuVo queryById(Long id);

    /**
     * 查询优选供应链商品规格列表
     */
    TableDataInfo<SohuProductZxhxSkuVo> queryPageList(SohuProductZxhxSkuBo bo, PageQuery pageQuery);

    /**
     * 查询优选供应链商品规格列表
     */
    List<SohuProductZxhxSkuVo> queryList(SohuProductZxhxSkuBo bo);

    /**
     * 修改优选供应链商品规格
     */
    Boolean insertByBo(SohuProductZxhxSkuBo bo);

    /**
     * 修改优选供应链商品规格
     */
    Boolean updateByBo(SohuProductZxhxSkuBo bo);

    /**
     * 校验并批量删除优选供应链商品规格信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
