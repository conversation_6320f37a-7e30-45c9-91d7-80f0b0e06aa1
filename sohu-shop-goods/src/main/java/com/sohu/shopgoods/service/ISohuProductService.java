package com.sohu.shopgoods.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.bo.*;
import com.sohu.shopgoods.api.domain.*;
import com.sohu.shopgoods.api.model.*;
import com.sohu.shopgoods.api.vo.*;
import com.sohu.shopgoods.domain.SohuProduct;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 商品Service接口
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
public interface ISohuProductService {

    /**
     * 查询商品-管理端
     */
    SohuProductVo queryById(Long id);

    SohuProductVo get(Long id);

    /**
     * 查询商品列表
     */
    TableDataInfo<SohuProductVo> queryPageList(SohuProductBo bo, PageQuery pageQuery);

    /**
     * 查询商品列表-上架
     */
    TableDataInfo<SohuProductVo> queryPageListOfOnShelf(SohuProductAdBo bo, PageQuery pageQuery);

    /**
     * 查询商品列表
     */
    List<SohuProductVo> queryList(SohuProductBo bo);

    /**
     * 修改商品
     */
    Boolean insertByBo(SohuProductBo bo);

    /**
     * 修改商品
     */
    Boolean updateByBo(SohuProductBo bo);

    /**
     * 校验并批量删除商品信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 判断商品是否使用平台分类
     *
     * @param categoryId 平台分类id
     * @return Boolean
     */
    boolean isUsePlatformCategory(Long categoryId);

    /**
     * 是否有商品使用对应的商户商品分类
     *
     * @param id 商户商品分类id
     * @return Boolean
     */
    boolean isExistStoreCategory(Long id);

    /**
     * pc商品列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuProductVo> queryPcPageList(SohuProductPcBo bo, PageQuery pageQuery);

    /**
     * 商城-商品列表
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuProductVo> queryPcPageListNew(SohuProductReqNewBo bo, PageQuery pageQuery);

    /**
     * 查询使用服务保障的商品列表
     *
     * @param id
     * @return
     */
    List<SohuProduct> findUseGuarantee(Long id);

    /**
     * 判断商品是否使用服务保障
     *
     * @param id 服务保障id
     * @return Boolean
     */
    Boolean isUseGuarantee(Long id);

    /**
     * 根据id集合获取商品简单信息
     *
     * @param productIds id集合
     * @return 商品信息
     */
    List<SimpleProductVo> getSimpleListInIds(List<Long> productIds);

    /**
     * 删除商品
     *
     * @param id   商品id
     * @param type 类型：recycle——回收站 delete——彻底删除
     * @return 删除结果
     */
    boolean deleteByIdAndType(Long id, String type);

    /**
     * 恢复已删除商品
     *
     * @param id 商品id
     * @return 恢复结果
     */
    boolean reStoreProduct(Long id);

    /**
     * 上下架商品
     *
     * @param bo 商品上下架操作对象
     */
    Boolean offOrPutShelf(SohuOffOrPutBo bo);

    /**
     * 平台端获取商品表头数量
     *
     * @return List
     */
    List<SohuProductTabsVo> getPlatformTabsHeader(String sysSource);

    /**
     * 强制下架商品
     *
     * @param bo
     */
    boolean forceDown(SohuOffOrPutBo bo);

    /**
     * 修改虚拟销量/排序
     *
     * @param bo
     */
    Boolean updatePcByBo(SohuPcUpdateBo bo);

    /**
     * 商品审核
     *
     * @param bo 审核参数
     * @return Boolean
     */
    Boolean pcAudit(SohuProductAuditBo bo);

    /**
     * 首页商品分页
     *
     * @param shopProductReqBo
     * @param pageQuery
     * @return 分页
     */
    TableDataInfo<SohuIndexProductModel> homeIndexPage(ShopProductReqBo shopProductReqBo, PageQuery pageQuery);

    /**
     * 个人商品橱窗
     *
     * @param shopProductReqBo
     * @param pageQuery
     * @return 商品橱窗
     */
    TableDataInfo<SohuIndexProductModel> userShopWindowPage(ShopProductReqBo shopProductReqBo, PageQuery pageQuery);

    /**
     * MNC带货库分页
     *
     * @param bo
     * @param pageQuery
     * @return 商品橱窗
     */
    TableDataInfo<SohuIndexProductModel> mcnShopWindowPage(McnShopProductReqBo bo, PageQuery pageQuery);

    /**
     * 个人添加分销商品分页
     *
     * @param shopProductReqBo
     * @param pageQuery
     * @return 个人添加分销商品分页
     */
    TableDataInfo<SohuIndexProductModel> userShopWindowIndependentPage(ShopProductReqBo shopProductReqBo, PageQuery pageQuery);

    /**
     * MCN选品商城商品分页
     *
     * @param bo
     * @param pageQuery
     * @return 商品分页
     */
    TableDataInfo<SohuIndexProductModel> mcnShopWindowIndependentPage(McnShopProductReqBo bo, PageQuery pageQuery);

    /**
     * app首页商品详情
     *
     * @param id
     * @return 详情
     */
    SohuIndexProductInfoModel getInfo(Long id, Boolean isIndependent);

    /**
     * 根据商品id查询商品数量和好评度
     *
     * @param id
     * @return SohuProductReplyCountModel
     */
    SohuProductReplyCountModel getReplyCount(Long id);

    /**
     * 商品规格详情
     *
     * @param id
     * @return SohuProductInfoModel
     */
    SohuProductInfoModel getSkuInfo(Long id);

    /**
     * 商品排行榜
     *
     * @return 商品排行榜
     */
    List<SohuProductModel> getLevel();

    /**
     * 根据ids查询商品集合
     *
     * @param productIds
     * @return List<SohuProduct>
     */
    List<SohuProduct> listByIds(List<Long> productIds);

    /**
     * 添加/扣减库存
     *
     * @param productId
     * @param num
     * @param type
     */
    Boolean operationStock(Long productId, Integer num, String type);

    /**
     * app商户商品列表
     *
     * @param productReqBo
     * @param pageQuery
     */
    TableDataInfo<SohuIndexProductModel> getMerchantProductList(ShopProductReqBo productReqBo, PageQuery pageQuery);

    /**
     * 根据店铺id下架店铺下所有商品
     */
    Boolean forcedRemovalAll(Long id, Long siteId);

    /**
     * 商品分佣开关
     *
     * @param bo
     */
    boolean independentIsShow(SohuProductBo bo);

    /**
     * app分销商户商品列表-sotre
     *
     * @param productReqBo
     * @param pageQuery
     */
    TableDataInfo<SohuAppProductMerchantModel> getMerchantIndependentProductList(ShopProductReqBo productReqBo, PageQuery pageQuery);

    /**
     * 查询商品--MCN选品商城商品详情
     */
    SohuIndexProductInfoModel getMcnShopWindowInfo(Long id, Boolean isIndependent);

    /**
     * 只查商品详情
     *
     * @param id 商品ID
     * @return {@link SohuProductModel}
     */
    SohuProductModel queryProductById(Long id);

    /**
     * 查询商品列表
     */
    List<SohuProductSyncVo> queryList();

    /**
     * 批量修改商品
     */
    Boolean updateBatchById(List<SohuProductSyncBo> productList);

    /**
     * 商品表同步一级分类
     *
     * @return 返回执行成功数量
     */
    int syncFirstCategoryToProduct();

    /**
     * 短剧小黄车编辑集商品列表
     */
    TableDataInfo<SohuProductPlayletListVo> shopCartPlayletList(SohuPlayletShopCartQueryBo bo, PageQuery pageQuery);

    /**
     * 短剧详情页小黄车商品列表
     */
    TableDataInfo<SohuProductVo> shopCartList(Long videoId, PageQuery pageQuery);

    /**
     * 上下架该剧的所有商品
     *
     * @param playletOperateBo
     * @return
     */
    Boolean operateBatchPlaylet(ShopOperateBatchBo playletOperateBo);

    /**
     * 判断商品是否使用品牌
     *
     * @param brandId 品牌id
     * @return Boolean
     */
    Boolean isUseBrand(Long brandId);

    /**
     * 开放平台商品保存
     * @param bo
     * @return
     */
    Boolean openProductSaveV1(SohuOpenProductSaveV1Bo bo);

    /**
     * 开放平台商品保存
     * @param bo
     * @return
     */
    Boolean openProductSaveV2(SohuOpenProductSaveV2Bo bo);

    /**
     * 商品上下架修改(开放平台)
     * @param bo
     * @return
     */
    Boolean openUpdateProductShelfV1(SohuOpenProductShelfEditV1Bo bo);

    /**
     * 商品上下架修改(开放平台)
     * @param bo
     * @return
     */
    Boolean openUpdateProductShelfV2(SohuOpenProductShelfEditV2Bo bo);

    /**
     * 修改商品库存（开放平台）
     * @param bo
     * @return
     */
    Boolean openUpdateProductStockV1(SohuOpenProductStockEditV1Bo bo);

    /**
     * 修改商品库存（开放平台）
     * @param bo
     * @return
     */
    Boolean openUpdateProductStockV2(SohuOpenProductStockEditV2Bo bo);

    /**
     * 根据第三方商品唯一标识获取商品信息
     *
     */
    SohuOpenProductV1Vo getBythirdProductIdV1(SohuOpenProductQueryV1Bo bo);

    /**
     * 根据第三方商品唯一标识获取商品信息
     *
     */
    SohuOpenProductV2Vo getBythirdProductIdV2(SohuOpenProductQueryV2Bo bo);

    /**
     * 平台分销商单挑选商品列表
     */
    TableDataInfo<SohuProductVo> queryPmList(SohuProductBo bo, PageQuery pageQuery);

    /**
     * 商品分佣费率计算
     */
    BigDecimal extractedGoodRation(SohuProduct bo);

    /**
     * 获取商品规格列表
     */
    List<SohuProductAttrVo> getListByProductIdAndType(Long id, String type);

    /**
     * 基于状态统计对应商品数量
     *
     * @param auditStatus
     * @return
     */
    Long countByAuditStatus(String auditStatus);

    /**
     * 商品总览统计定时任务
     * @param day
     */
    void productOverviewExecute(String day);


    /**
     * 通过id与状态查询商品
     * @param ids
     * @param state
     * @return
     */
    List<SohuProductVo> queryGoodsByIds(List<Long> ids, String state);

    /**
     * 基于状态统计对应商品数量
     *
     * @param merId       商家id
     * @param auditStatus 审核状态
     * @return 商品数量
     */
    Long productCount(Long merId, String auditStatus);

    /**
     * 处理审核结果
     * @param busyCode
     * @param isPass
     * @param reason
     */
    void handleRobot(String busyCode, Boolean isPass, String reason);
}

