package com.sohu.shopgoods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.CurrencyTypeEnum;
import com.sohu.common.core.utils.TreeNamesUtil;
import com.sohu.common.core.vo.NodeVo;
import com.sohu.common.excel.utils.PoiExcelUtil;
import com.sohu.shopgoods.api.bo.ProductImportBo;
import com.sohu.shopgoods.api.bo.SohuProductBo;
import com.sohu.shopgoods.api.bo.SohuProductBrandBo;
import com.sohu.shopgoods.api.vo.*;
import com.sohu.shopgoods.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/16 10:19
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ISohuProductExcelServiceImpl implements ISohuProductExcelService {
    private final ISohuProductGuaranteeService iSohuProductGuaranteeService;
    private final ISohuProductCategoryService iSohuProductCategoryService;
    private final ISohuProductCategoryPcService iSohuProductCategoryPcService;
    private final ISohuProductBrandService iSohuProductBrandService;
    @DubboReference
    private final RemoteMerchantService remoteMerchantService;
    private final ISohuFreightTemplateService sohuFreightTemplateService;
    private final ISohuOverseasFreightTemplateService sohuOverseasFreightTemplateService;
    private final ISohuProductService iSohuProductService;
    @Override
    public void downloadModel(String source, HttpServletResponse response) {
        //初始化表头
        ExcelWriter writer = getProdExcelWriter();
        try (Workbook workbook = writer.getWorkbook()) {
            Sheet sheet = writer.getSheet();
            //设置提示语
            setPrompt(sheet);
            dropDownList(sheet, workbook, source);
            PoiExcelUtil.writeExcel(response, writer,"product_template");
        } catch (Exception e) {
            log.error("Exception:", e);
        }
    }

    @Override
    public void importGoods(List<ProductImportBo> productList) {
        if (CollUtil.isNotEmpty(productList)){
            productList.forEach(productBo -> {
                SohuProductBo reqBo = new SohuProductBo();
                reqBo.setStoreName(productBo.getStoreName());
                reqBo.setCateId(productBo.getCateIds());
                reqBo.setCategoryId(productBo.getCategoryId());
                reqBo.setBrandId(productBo.getBrandId());
                reqBo.setGuaranteeIds(productBo.getGuaranteeIds());
                reqBo.setKeyword(productBo.getKeyword());
                reqBo.setUnitName(productBo.getUnitName());
                reqBo.setStoreInfo(productBo.getStoreInfo());
                reqBo.setMerId(productBo.getMerId());
                reqBo.setCurrency(CurrencyTypeEnum.getCode(productBo.getCurrency()));
                reqBo.setProductType(productBo.getProductType().equals("普通商品")?0L:1L);
                reqBo.setFreightTemplateId(productBo.getFreightTemplateId());
                reqBo.setImage(productBo.getCoverImage());
                reqBo.setSliderImage(productBo.getSliderImage());
                reqBo.setIndependentIsShow(productBo.getIndependentIsShow().equals("开启")?true:false);
                reqBo.setIndependentRatio(productBo.getIndependentRatio());
                reqBo.setSpecType(productBo.getSpecType().equals("多规格")?true:false);
                reqBo.setAttrList(productBo.getAttrList());
                reqBo.setAttrValueList(productBo.getAttrValueList());
                reqBo.setSysSource(productBo.getSysSource());
                reqBo.setSiteId(productBo.getSiteId());
                reqBo.setContent(productBo.getContent());
                iSohuProductService.insertByBo(reqBo);
            });
        }
    }


    public ExcelWriter getProdExcelWriter() {
        //通过工具类创建writer
        ExcelWriter writer = ExcelUtil.getBigWriter();
        // 商品导出or模板
        Collection<String> headerList = getHeaderList().values();
        getSheet(writer, headerList.size());
//        writer.merge(headerList.size() - 1, "商品信息整理");
        writer.writeRow(headerList);
        return writer;
    }
    private Map<Integer,String> getHeaderList() {
        Map<Integer,String> headerMap = new HashMap<>();
        headerMap.put(1,"商品编码");
        headerMap.put(2, "商品名称");
        headerMap.put(3, "商户商品分类");
        headerMap.put(4, "平台商品分类");
        headerMap.put(5, "品牌");
        headerMap.put(6, "保障服务");
        headerMap.put(7, "商品关键字");
        headerMap.put(8, "单位");
        headerMap.put(9, "商品简介");
        headerMap.put(10, "所属店铺");
        headerMap.put(11, "货币类型");
        headerMap.put(12, "商品类型");
        headerMap.put(13, "运费模板");
        headerMap.put(14, "商品封面图");
        headerMap.put(15, "商品轮播图");
        headerMap.put(16, "分销配置");
        headerMap.put(17, "分销比例");
        headerMap.put(18, "商品规格");
        headerMap.put(19, "商品规格组合字符串 格式是p1:v1;p2:v2");
        headerMap.put(20, "图片");
        headerMap.put(21, "售价");
        headerMap.put(22, "成本价");
        headerMap.put(23, "原价");
        headerMap.put(24, "库存");
        headerMap.put(25, "重量(KG)");
        headerMap.put(26, "体积(m³)");
        headerMap.put(27, "商品详情");
        return headerMap;
    }

    /**
     * 初始化Sheet，设置宽度
     * @param writer
     * @param size
     * @return
     */
    private Sheet getSheet(ExcelWriter writer,int size) {
        Sheet sheet = writer.getSheet();
        for (int i=0;i<size;i++){
            sheet.setColumnWidth(i, 40*256);
        }
        return sheet;
    }

    /**
     * 设置提示语
     * @param sheet
     * @param
     * @return
     */
    private Sheet setPrompt(Sheet sheet) {
        int rowNum = 0;
        int index = 0;
        //商品编码
        setComment(sheet.getRow(rowNum).getCell(index++),"商品编码必填,不同商品分类下相同商品的商品编码得保持一致",sheet);
        //商品名称
        setComment(sheet.getRow(rowNum).getCell(index++),"1、同编码商品第一条商品名称不能为空\n" +
                "2、最多可输入50个字",sheet);
        //商户商品分类
        setComment(sheet.getRow(rowNum).getCell(index++),"商户商品分类必填",sheet);
        //平台商品分类
        setComment(sheet.getRow(rowNum).getCell(index++),"平台商品分类必填",sheet);
        //品牌
        setComment(sheet.getRow(rowNum).getCell(index++),"品牌必填",sheet);
        //保障服务
        setComment(sheet.getRow(rowNum).getCell(index++),"保障服务必填",sheet);
        //商品关键字
        setComment(sheet.getRow(rowNum).getCell(index++),"商品关键字必填",sheet);
        //单位
        setComment(sheet.getRow(rowNum).getCell(index++),"单位必填",sheet);
        //商品简介
        setComment(sheet.getRow(rowNum).getCell(index++),"商品简介必填",sheet);
        //所属店铺
        setComment(sheet.getRow(rowNum).getCell(index++),"所属店铺必填",sheet);
        //货币类型
        setComment(sheet.getRow(rowNum).getCell(index++),"货币类型必填",sheet);
        //商品类型
        setComment(sheet.getRow(rowNum).getCell(index++),"商品类型必填",sheet);
        //运费模版
        setComment(sheet.getRow(rowNum).getCell(index++),"运费模版必填",sheet);
        setComment(sheet.getRow(rowNum).getCell(index++),"商品封面图必填,封面图只传一张",sheet);
        setComment(sheet.getRow(rowNum).getCell(index++),"商品轮播图必填,可上传多张(逗号分隔)",sheet);
        setComment(sheet.getRow(rowNum).getCell(index++),"分销配置必填",sheet);
        setComment(sheet.getRow(rowNum).getCell(index++),"分销配置开启时，分销比例必填",sheet);
        setComment(sheet.getRow(rowNum).getCell(index++),"商品规格为多规格时，商品规格组合必填",sheet);
        setComment(sheet.getRow(rowNum).getCell(index++),"图片地址必填",sheet);
        setComment(sheet.getRow(rowNum).getCell(index++),"售价必填",sheet);
        setComment(sheet.getRow(rowNum).getCell(index++),"成本价必填",sheet);
        setComment(sheet.getRow(rowNum).getCell(index++),"原价必填",sheet);
        setComment(sheet.getRow(rowNum).getCell(index++),"库存必填",sheet);
        setComment(sheet.getRow(rowNum).getCell(index++),"重量(KG)必填",sheet);
        setComment(sheet.getRow(rowNum).getCell(index++),"体积必填",sheet);
        setComment(sheet.getRow(rowNum).getCell(index),"商品详情必填",sheet);
        return sheet;
    }

    private void dropDownList(Sheet sheet, Workbook workbook,String sysSource){
        //商户商品分类
        Map<Long, String> storeCateNames = new HashMap<>();
        List<SohuProductCategoryVo> storeCateist = iSohuProductCategoryService.storeTree(null,sysSource);
        if (CollUtil.isNotEmpty(storeCateist)) {
            List<NodeVo> nodeList = JSONObject.parseArray(JSONObject.toJSONString(storeCateist), NodeVo.class);
            storeCateNames = TreeNamesUtil.buildTreeAndGetCombinedNames(nodeList);
            PoiExcelUtil.createDropDownList(sheet, storeCateNames.values().toArray(String[]::new), 1, 50000, 2, 2);
        }
        //平台商品分类
        Map<Long, String> platformCateNames = new HashMap<>();
        List<SohuProductCategoryPcVo> platformCateList = iSohuProductCategoryPcService.pcTree(sysSource);
        if (CollUtil.isNotEmpty(platformCateList)) {
            List<NodeVo> nodeList = JSONObject.parseArray(JSONObject.toJSONString(platformCateList), NodeVo.class);
            platformCateNames = TreeNamesUtil.buildTreeAndGetCombinedNames(nodeList);
            PoiExcelUtil.createDropDownList(sheet, platformCateNames.values().toArray(String[]::new), 1, 50000, 3, 3);
        }
        //品牌
        SohuProductBrandBo bo = new SohuProductBrandBo();
        bo.setSysSource(sysSource);
        List<SohuProductBrandVo> brandList = iSohuProductBrandService.queryList(bo);
        if (CollUtil.isNotEmpty(brandList)){
            String[] brandListStr = brandList.stream().map(SohuProductBrandVo::getName).toArray(String[]::new);
            PoiExcelUtil.createDropDownList(sheet, brandListStr, 1, 50000, 4, 4);
        }
        //保障服务
        List<SohuProductGuaranteeVo> guaranteeResult = iSohuProductGuaranteeService.queryStoreList(sysSource);
        if (CollectionUtil.isNotEmpty(guaranteeResult)) {
            String[] guaranteeList = guaranteeResult.stream().map(SohuProductGuaranteeVo::getName).toArray(String[]::new);
            PoiExcelUtil.createDropDownList(sheet, guaranteeList, 1, 50000, 5, 5);
        }
        //店铺
        List<SohuMerchantModel> merchantOpenList = remoteMerchantService.getProductMerchantList(sysSource);
        if (CollUtil.isNotEmpty(merchantOpenList)){
            String[] merchantList = merchantOpenList.stream().map(SohuMerchantModel::getName).toArray(String[]::new);
            PoiExcelUtil.createDropDownList(sheet, merchantList, 1, 50000, 9, 9);
        }
        //运费模版
        String[] templateArr = getTemplateNames(sysSource);
        if (templateArr.length>0){
            PoiExcelUtil.createDropDownList(sheet, templateArr, 1, 50000, 12, 12);
        }
        //商品类型下拉
        String[] proType = {"普通商品","虚拟商品"};
        PoiExcelUtil.createDropDownList(sheet, proType, 1, 50000, 11, 11);
        //分销配置
        String[] proConfig = {"开启","关闭"};
        PoiExcelUtil.createDropDownList(sheet, proConfig, 1, 50000, 15, 15);
        //商品规格
        String[] proSpec = {"单规格","多规格"};
        PoiExcelUtil.createDropDownList(sheet, proSpec, 1, 50000, 17, 17);
        //货币类型
        String[] currencyType = {"人民币","美元","欧元","日元","英镑","加拿大元","澳大利亚元","瑞士法郎","瑞典克朗","新西兰元"};
        PoiExcelUtil.createDropDownList(sheet, currencyType, 1, 50000, 10, 10);
    }

    private String[] getTemplateNames(String sysSource){
        String[] templateArr = {};
        if (sysSource.equals(Constants.SOHUGLOBAL)){
            List<SohuFreightTemplateVo> templateList = sohuFreightTemplateService.queryList();
            if (CollUtil.isNotEmpty(templateList)){
                templateArr = templateList.stream().map(SohuFreightTemplateVo::getName).toArray(String[]::new);
            }
        }
        if (sysSource.equals(Constants.MINGLEREELS)){
            List<SohuOverseasFreightTemplateVo> templateList = sohuOverseasFreightTemplateService.queryList();
            if (CollUtil.isNotEmpty(templateList)){
                templateArr = templateList.stream().map(SohuOverseasFreightTemplateVo::getName).toArray(String[]::new);
            }
        }
        return templateArr;
    }
    public static void setComment(Cell cell, String text, Sheet sheet){
        ClientAnchor anchor = new XSSFClientAnchor();
        anchor.setDx1(0);
        anchor.setDx2(0);
        anchor.setDy1(0);
        anchor.setDy2(0);
        anchor.setCol1(cell.getColumnIndex());
        anchor.setRow1(cell.getRowIndex());
        anchor.setCol2(cell.getColumnIndex() + 5);
        anchor.setRow2(cell.getRowIndex() + 6);
        Drawing drawing = sheet.createDrawingPatriarch();
        Comment comment = drawing.createCellComment(anchor);
        comment.setString(new XSSFRichTextString(text));
        cell.setCellComment(comment);
    }
}
