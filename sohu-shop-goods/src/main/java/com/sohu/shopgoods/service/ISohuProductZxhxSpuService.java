package com.sohu.shopgoods.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shopgoods.api.vo.SohuProductZxhxSpuVo;
import com.sohu.shopgoods.api.bo.SohuProductZxhxSpuBo;

import java.util.Collection;
import java.util.List;

/**
 * Service接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface ISohuProductZxhxSpuService {

    /**
     * 查询优选供应链商品
     */
    SohuProductZxhxSpuVo queryById(Long id);

    /**
     * 查询优选供应链商品列表
     */
    TableDataInfo<SohuProductZxhxSpuVo> queryPageList(SohuProductZxhxSpuBo bo, PageQuery pageQuery);

    /**
     * 查询优选供应链商品列表
     */
    List<SohuProductZxhxSpuVo> queryList(SohuProductZxhxSpuBo bo);

    /**
     * 修改优选供应链商品
     */
    Boolean insertByBo(SohuProductZxhxSpuBo bo);

    /**
     * 修改优选供应链商品
     */
    Boolean updateByBo(SohuProductZxhxSpuBo bo);

    /**
     * 校验并批量删除优选供应链商品信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 同步商品信息
     */
    void syncSpu();
}
