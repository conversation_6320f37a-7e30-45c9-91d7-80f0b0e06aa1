package com.sohu.shopgoods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.sohu.shopgoods.api.bo.SohuProductZxhxSkuBo;
import com.sohu.shopgoods.api.vo.SohuProductZxhxSkuVo;
import com.sohu.shopgoods.domain.SohuProductZxhxSku;
import com.sohu.shopgoods.mapper.SohuProductZxhxSkuMapper;
import com.sohu.shopgoods.service.ISohuProductZxhxSkuService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 优选供应链商品规格Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RequiredArgsConstructor
@Service
public class SohuProductZxhxSkuServiceImpl implements ISohuProductZxhxSkuService {

    private final SohuProductZxhxSkuMapper baseMapper;

    /**
     * 查询优选供应链商品规格
     */
    @Override
    public SohuProductZxhxSkuVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询优选供应链商品规格列表
     */
    @Override
    public TableDataInfo<SohuProductZxhxSkuVo> queryPageList(SohuProductZxhxSkuBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuProductZxhxSku> lqw = buildQueryWrapper(bo);
        Page<SohuProductZxhxSkuVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询优选供应链商品规格列表
     */
    @Override
    public List<SohuProductZxhxSkuVo> queryList(SohuProductZxhxSkuBo bo) {
        LambdaQueryWrapper<SohuProductZxhxSku> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuProductZxhxSku> buildQueryWrapper(SohuProductZxhxSkuBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuProductZxhxSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getSpuId() != null, SohuProductZxhxSku::getSpuId, bo.getSpuId());
        lqw.eq(bo.getThirdSpuId() != null, SohuProductZxhxSku::getThirdSpuId, bo.getThirdSpuId());
        lqw.eq(bo.getThirdSkuId() != null, SohuProductZxhxSku::getThirdSkuId, bo.getThirdSkuId());
        lqw.eq(StringUtils.isNotBlank(bo.getSku()), SohuProductZxhxSku::getSku, bo.getSku());
        lqw.eq(bo.getStock() != null, SohuProductZxhxSku::getStock, bo.getStock());
        lqw.eq(StringUtils.isNotBlank(bo.getImage()), SohuProductZxhxSku::getImage, bo.getImage());
        lqw.eq(bo.getPrice() != null, SohuProductZxhxSku::getPrice, bo.getPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getBarCode()), SohuProductZxhxSku::getBarCode, bo.getBarCode());
        lqw.eq(bo.getCost() != null, SohuProductZxhxSku::getCost, bo.getCost());
        lqw.eq(bo.getGroupPrice() != null, SohuProductZxhxSku::getGroupPrice, bo.getGroupPrice());
        lqw.eq(bo.getServicePrice() != null, SohuProductZxhxSku::getServicePrice, bo.getServicePrice());
        lqw.eq(StringUtils.isNotBlank(bo.getAttrValue()), SohuProductZxhxSku::getAttrValue, bo.getAttrValue());
        lqw.eq(bo.getWeight() != null, SohuProductZxhxSku::getWeight, bo.getWeight());
        lqw.eq(bo.getVolume() != null, SohuProductZxhxSku::getVolume, bo.getVolume());
        lqw.eq(bo.getIsShow() != null, SohuProductZxhxSku::getIsShow, bo.getIsShow());
        lqw.eq(bo.getBuyStartQty() != null, SohuProductZxhxSku::getBuyStartQty, bo.getBuyStartQty());
        return lqw;
    }

    /**
     * 新增优选供应链商品规格
     */
    @Override
    public Boolean insertByBo(SohuProductZxhxSkuBo bo) {
        SohuProductZxhxSku add = BeanUtil.toBean(bo, SohuProductZxhxSku.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改优选供应链商品规格
     */
    @Override
    public Boolean updateByBo(SohuProductZxhxSkuBo bo) {
        SohuProductZxhxSku update = BeanUtil.toBean(bo, SohuProductZxhxSku.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuProductZxhxSku entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除优选供应链商品规格
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
