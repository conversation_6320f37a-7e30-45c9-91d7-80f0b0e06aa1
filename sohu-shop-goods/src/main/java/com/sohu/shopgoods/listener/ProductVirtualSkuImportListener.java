package com.sohu.shopgoods.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.SpringUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.vo.ExcelErrVo;
import com.sohu.common.excel.core.ExcelListener;
import com.sohu.common.excel.core.ExcelResult;
import com.sohu.shopgoods.api.bo.SohuProductVirtualSkuBo;
import com.sohu.shopgoods.api.vo.SohuProductVirtualSkuImportVo;
import com.sohu.shopgoods.api.vo.SohuProductVirtualSkuVo;
import com.sohu.shopgoods.service.ISohuProductVirtualSkuService;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 虚拟商品自定义导入
 *
 * <AUTHOR>
 */
@Slf4j
public class ProductVirtualSkuImportListener extends AnalysisEventListener<SohuProductVirtualSkuImportVo> implements ExcelListener<SohuProductVirtualSkuImportVo> {

    private ISohuProductVirtualSkuService productVirtualSkuService;

    private int successNum = 0;
    private int failureNum = 0;

    private final StringBuilder failureMsg = new StringBuilder();

    List<SohuProductVirtualSkuImportVo> virtualSkuList = new ArrayList<>();

    public ProductVirtualSkuImportListener() {
        this.productVirtualSkuService = SpringUtils.getBean(ISohuProductVirtualSkuService.class);
    }

    @Override
    public void invoke(SohuProductVirtualSkuImportVo skuVo, AnalysisContext context) {
        try {
            // 校验字段
            String checkMsg = this.validateData(skuVo);
            if (StringUtils.isNotEmpty(checkMsg)) {
                failureNum++;
                failureMsg.append("<br/>").append(failureNum)
                        .append(checkMsg).append(" 参数校验失败");
                return;
            }
            // 校验虚拟商品是否存在
            SohuProductVirtualSkuVo virtualSkuVo = productVirtualSkuService.queryBySupplierCode(skuVo.getSupplierCode());
            if (Objects.nonNull(virtualSkuVo)) {
                failureNum++;
                failureMsg.append("<br/>").append(failureNum)
                        .append("、商品名称 ").append(skuVo.getGoodsName())
                        .append("、单品名称 ").append(skuVo.getSkuName())
                        .append(" 已存在");
                return;
            }
            virtualSkuList.add(skuVo);
        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、商品名称 " + skuVo.getGoodsName() + "、单品名称 " + skuVo.getSkuName() + " 导入失败：";
            failureMsg.append(msg).append(e.getMessage());
            log.error(msg, e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 1. 用 Code 分组
        Map<String, List<SohuProductVirtualSkuImportVo>> virtualSkuCodeMap = virtualSkuList.stream()
                .collect(Collectors.groupingBy(SohuProductVirtualSkuImportVo::getSupplierCode));
        // 2. 找出重复的 Code
        List<String> duplicateProductCodes = virtualSkuCodeMap.entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(duplicateProductCodes)) {
            duplicateProductCodes.forEach(code -> {
                List<SohuProductVirtualSkuImportVo> duplicates = virtualSkuCodeMap.get(code);
                failureNum++;
                failureMsg.append("<br/>").append(failureNum)
                        .append("、商品编码 ").append(code)
                        .append("、重复数量 ").append(duplicates.size())
                        .append(" 已存在");
            });
        }
        // 构建不包含重复 Code 的列表
        List<SohuProductVirtualSkuImportVo> filteredList = virtualSkuList.stream()
                .filter(vo -> !duplicateProductCodes.contains(vo.getSupplierCode()))
                .collect(Collectors.toList());
        // 3. 按 goodsName 分组
        Map<String, List<SohuProductVirtualSkuImportVo>> virtualSkuMap = filteredList.stream()
                .collect(Collectors.groupingBy(SohuProductVirtualSkuImportVo::getGoodsName));
        SohuProductVirtualSkuBo virtualSkuBo;
        for (String goodsName : virtualSkuMap.keySet()) {
            SohuProductVirtualSkuVo virtualSkuVo = productVirtualSkuService.queryByGoodsName(goodsName);
            List<SohuProductVirtualSkuBo> virtualSkuList = BeanUtil.copyToList(virtualSkuMap.get(goodsName), SohuProductVirtualSkuBo.class);
            virtualSkuList.forEach(row -> row.setSourceType(1L));
            if (Objects.isNull(virtualSkuVo)) {
                virtualSkuBo = new SohuProductVirtualSkuBo();
                virtualSkuBo.setVirtualId(0L);
                virtualSkuBo.setSourceType(1L);
                virtualSkuBo.setSkuName(goodsName);
                virtualSkuBo.setMarketPrice(new BigDecimal("0"));
                virtualSkuBo.setSupplyPrice(new BigDecimal("0"));
                virtualSkuBo.setChildList(virtualSkuList);
                productVirtualSkuService.insertByBo(virtualSkuBo);
            } else {
                virtualSkuBo = BeanUtil.toBean(virtualSkuVo, SohuProductVirtualSkuBo.class);
                virtualSkuBo.setChildList(virtualSkuList);
                productVirtualSkuService.updateByBo(virtualSkuBo);
            }
            successNum += virtualSkuList.size();
            successNum++;
        }
        log.info("所有数据解析完成！");
    }

    @Override
    public ExcelResult<SohuProductVirtualSkuImportVo> getExcelResult() {
        return new ExcelResult<SohuProductVirtualSkuImportVo>() {

            @Override
            public String getAnalysis() {
                StringBuilder sb = new StringBuilder();
                if (failureNum > 0) {
                    sb.append("很抱歉，导入失败！共 ").append(failureNum).append(" 条数据格式不正确，错误如下：").append(failureMsg);
                }
                if (successNum > 0) {
                    sb.append("恭喜您，数据已全部导入成功！共 ").append(successNum).append(" 条");
                }
                return sb.toString();
            }

            @Override
            public List<ExcelErrVo> getErrVoList() {
                return null;
            }

            @Override
            public List<SohuProductVirtualSkuImportVo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }

    /**
     * 校验excel数据
     */
    private String validateData(SohuProductVirtualSkuImportVo vo) {
        StringBuilder builder = new StringBuilder();
        if (StrUtil.isBlank(vo.getGoodsName())) {
            builder.append("商品名称不能为空");
            builder.append(";");
        }
        if (StrUtil.isNotEmpty(vo.getGoodsName()) && vo.getGoodsName().length() > 30) {
            builder.append("商品名称不能超过30");
            builder.append(";");
        }
        if (StrUtil.isBlank(vo.getSkuName())) {
            builder.append("单品名称不能为空");
            builder.append(";");
        }
        if (StrUtil.isNotEmpty(vo.getSkuName()) && vo.getSkuName().length() > 30) {
            builder.append("单品名称不能超过30");
            builder.append(";");
        }
        if (StrUtil.isBlank(vo.getSupplierCode())) {
            builder.append("商品编码不能为空");
            builder.append(";");
        }
        if (StrUtil.isNotEmpty(vo.getSupplierCode()) && vo.getSupplierCode().length() > 50) {
            builder.append("商品编码不能超过50");
            builder.append(";");
        }
        if (Objects.isNull(vo.getMarketPrice())) {
            builder.append("标准价不能为空");
            builder.append(";");
        }
        if (Objects.nonNull(vo.getMarketPrice()) && CalUtils.great(vo.getMarketPrice(), new BigDecimal("99999999.99"))) {
            builder.append("标准价不能大于99999999.99");
            builder.append(";");
        }
        if (Objects.isNull(vo.getSupplyPrice())) {
            builder.append("供货价不能为空");
            builder.append(";");
        }
        if (Objects.nonNull(vo.getSupplyPrice()) && CalUtils.great(vo.getSupplyPrice(), new BigDecimal("99999999.99"))) {
            builder.append("供货价不能大于99999999.99");
            builder.append(";");
        }
        return builder.toString();
    }
}
