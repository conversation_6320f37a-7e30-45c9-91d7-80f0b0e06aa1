package com.wangcaio2o.ipossa.sdk.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.wangcaio2o.ipossa.sdk.sign.StringUtils;

import lombok.Getter;
import lombok.Setter;

/**
 * 返回对象，后续返回对象都要继承这个类 { "alipay_trade_close_response": { "code": "20000", "msg": "Service Currently Unavailable",
 * "sub_code": "isp.unknown-error", "sub_msg": "系统繁忙" }, "sign": "ERITJKEIJKJHKKKKKKKHJEREEEEEEEEEEE" }
 *
 * <AUTHOR>
 */
@Setter
@Getter
public abstract class BaseResponse {

    @JSONField(name = "response_type")
    private String responseType;

    @JSONField(name = "isspid")
    private String isspid;

    @JSONField(name = "result")
    private Result result;

    @JSONField(name = "sign")
    private String sign;

    @JSONField(serialize = false)
    public boolean isSuccess() {
        return "0000".equals(this.result.getId());
    }

    @JSONField(serialize = false)
    public boolean isProcessing() {
        return "9998".equals(this.result.getId());
    }

    @Setter
    @Getter
    public static class Result {
        private String id;

        private String comment;
    }
}