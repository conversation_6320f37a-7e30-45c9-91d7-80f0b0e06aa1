package com.wangcaio2o.ipossa.sdk.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wangcaio2o.ipossa.sdk.model.ExtendParams;
import com.wangcaio2o.ipossa.sdk.model.GoodsDetail;
import com.wangcaio2o.ipossa.sdk.model.SplitInfo;
import com.wangcaio2o.ipossa.sdk.model.SplitList;
import com.wangcaio2o.ipossa.sdk.request.accountbalancequery.AccountBalanceQueryRequest;
import com.wangcaio2o.ipossa.sdk.request.barcodecancel.BarcodeCancel;
import com.wangcaio2o.ipossa.sdk.request.barcodecancel.BarcodeCancelRequest;
import com.wangcaio2o.ipossa.sdk.request.barcodequery.BarcodeQuery;
import com.wangcaio2o.ipossa.sdk.request.barcodequery.BarcodeQueryRequest;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverse;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverseRequest;
import com.wangcaio2o.ipossa.sdk.request.bindpos.BindPosRequest;
import com.wangcaio2o.ipossa.sdk.request.bindpos.StoreInfo;
import com.wangcaio2o.ipossa.sdk.request.delayconfirm.DelayConfirm;
import com.wangcaio2o.ipossa.sdk.request.delayconfirm.DelayConfirmRequest;
import com.wangcaio2o.ipossa.sdk.request.delayconfirmquery.DelayConfirmquery;
import com.wangcaio2o.ipossa.sdk.request.delayconfirmquery.DelayConfirmqueryRequest;
import com.wangcaio2o.ipossa.sdk.request.delayconfirmrefund.DelayConfirmrefund;
import com.wangcaio2o.ipossa.sdk.request.delayconfirmrefund.DelayConfirmrefundRequest;
import com.wangcaio2o.ipossa.sdk.request.merchantcreate.MerchantCreate;
import com.wangcaio2o.ipossa.sdk.request.merchantcreate.MerchantCreateRequest;
import com.wangcaio2o.ipossa.sdk.request.merchantdelete.MerchantDelete;
import com.wangcaio2o.ipossa.sdk.request.merchantdelete.MerchantDeleteRequest;
import com.wangcaio2o.ipossa.sdk.request.merchantquery.MerchantQuery;
import com.wangcaio2o.ipossa.sdk.request.merchantquery.MerchantQueryRequest;
import com.wangcaio2o.ipossa.sdk.request.merchantwithdrawquery.MerchantWithdrawquery;
import com.wangcaio2o.ipossa.sdk.request.merchantwithdrawquery.MerchantWithdrawqueryRequest;
import com.wangcaio2o.ipossa.sdk.request.scanpay.Scanpay;
import com.wangcaio2o.ipossa.sdk.request.scanpay.ScanpayRequest;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.Unifiedorder;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.UnifiedorderRequest;
import com.wangcaio2o.ipossa.sdk.response.accountbalancequery.AccountBalanceQueryResponse;
import com.wangcaio2o.ipossa.sdk.response.barcodecancel.BarcodeCancelResponse;
import com.wangcaio2o.ipossa.sdk.response.barcodequery.BarcodeQueryResponse;
import com.wangcaio2o.ipossa.sdk.response.barcodereverse.BarcodeReverseResponse;
import com.wangcaio2o.ipossa.sdk.response.bindpos.BindPosResponse;
import com.wangcaio2o.ipossa.sdk.response.delayconfirm.DelayConfirmResponse;
import com.wangcaio2o.ipossa.sdk.response.delayconfirmquery.DelayConfirmqueryResponse;
import com.wangcaio2o.ipossa.sdk.response.delayconfirmrefund.DelayConfirmrefundResponse;
import com.wangcaio2o.ipossa.sdk.response.merchantcreate.MerchantCreateResponse;
import com.wangcaio2o.ipossa.sdk.response.merchantdelete.MerchantDeleteResponse;
import com.wangcaio2o.ipossa.sdk.response.merchantquery.MerchantQueryResponse;
import com.wangcaio2o.ipossa.sdk.response.merchantwithdrawquery.MerchantWithdrawqueryResponse;
import com.wangcaio2o.ipossa.sdk.response.scanpay.ScanpayResponse;
import com.wangcaio2o.ipossa.sdk.response.unifiedorder.UnifiedorderResponse;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class TestPay {

    public static void main(String[] args) {
//        UnifiedorderRequest request = new UnifiedorderRequest();
//        BarcodeReverseRequest request = new BarcodeReverseRequest();
//        request.setPosId("***********");
//        request.setIsspid(Client.isspid);
//        request.setSystemId(Client.systemId);
//        request.setStoreId("********");
////        request.setPosSeq(System.nanoTime() + "");
//        request.setPosSeq("R" + System.nanoTime());

//        unifiedorder(request);
//        storeInfo(new BindPosRequest());
//        storeInfoEdit(new BindPosRequest());
//        refundOrder(request);
//        queryUser();
//        queryWithdraw();
//        queryUserMoney();
//        queryDelayPrice();
//        refundOrderDelay();
//        codePay();
//        queryPay();
//        List<YourObject> yourObjects = getYourObjects(); // 获取你的集合对象
//        BigDecimal totalAmount = calculateTotalAmount(yourObjects); // 计算集合对象的总金额
//        BigDecimal fee = new BigDecimal("10"); // 手续费
//
//        distributeFee(yourObjects, totalAmount, fee); // 分配手续费
//        System.out.println(JSONObject.toJSON(yourObjects));
        //支付金额
//        ScanpayRequest scanpayRequest = new ScanpayRequest();
//        scanpayRequest.setPosId("***********");
//        scanpayRequest.setIsspid(Client.isspid);
//        scanpayRequest.setSystemId(Client.systemId);
//        scanpayRequest.setStoreId("********");
//        scanpayRequest.setPosSeq("R" + System.nanoTime());
//        System.out.println("订单号="+scanpayRequest.getPosSeq());
//        Scanpay scanpay = new Scanpay();
//        scanpay.setPayType("503");
//        scanpay.setNotifyUrl("http://111.175.90.254:28080/pay/pay/callback/yima");
//        //支付金额
//        scanpay.setTxAmt(1000);
//        ExtendParams extendParams = new ExtendParams();
//        extendParams.setSplitFlag("D");
//        scanpay.setExtendParams(extendParams);
//        scanpayRequest.setScanpayRequest(scanpay);
//        System.out.println("扫码支付酬金请求request="+JSONObject.toJSONString(scanpayRequest));
//        ScanpayResponse response = Client.getClient().execute(scanpayRequest);
//        System.out.println("扫码支付酬金请求response返回="+JSONObject.toJSONString(response));

//        BarcodeCancelRequest cancelRequest = new BarcodeCancelRequest();
//        cancelRequest.setPosId("***********");
//        cancelRequest.setIsspid(Client.isspid);
//        cancelRequest.setSystemId(Client.systemId);
//        cancelRequest.setStoreId("********");
//        cancelRequest.setPosSeq("C" + System.nanoTime());
//        BarcodeCancel cancel = new BarcodeCancel();
//        cancel.setOrgPosSeq("R602860791808000");
//        cancelRequest.setBarcodeCancelRequest(cancel);
//        System.out.println("撤销交易request="+JSONObject.toJSONString(cancelRequest));
//        BarcodeCancelResponse barcodeCancelResponse = Client.getClient().execute(cancelRequest);
//        System.out.println("撤销交易请求response返回="+JSONObject.toJSONString(barcodeCancelResponse));

        // 部分退款
        BarcodeReverseRequest barcodeReverseRequest = new BarcodeReverseRequest();
        barcodeReverseRequest.setPosId("***********");
        barcodeReverseRequest.setIsspid(Client.isspid);
        barcodeReverseRequest.setSystemId(Client.systemId);
        barcodeReverseRequest.setStoreId("********");
        barcodeReverseRequest.setPosSeq("T" + System.nanoTime());
        System.out.println("订单号="+barcodeReverseRequest.getPosSeq());
        BarcodeReverse barcodeReverse = new BarcodeReverse();
//        barcodeReverse.setPayType("503");
        barcodeReverse.setTxAmt(1000);
        barcodeReverse.setOrgPosSeq("BTC607174946651712434062");
        barcodeReverseRequest.setBarcodeReverseRequest(barcodeReverse);
        System.out.println("退款请求request="+JSONObject.toJSONString(barcodeReverseRequest));
        BarcodeReverseResponse barcodeReverseResponse = Client.getClient().execute(barcodeReverseRequest);
        System.out.println("退款请求response返回="+JSONObject.toJSONString(barcodeReverseResponse));
        //确认分账
//        DelayConfirmRequest delayConfirmRequest = new DelayConfirmRequest();
//        delayConfirmRequest.setPosId("***********");
//        delayConfirmRequest.setIsspid(Client.isspid);
//        delayConfirmRequest.setSystemId(Client.systemId);
//        delayConfirmRequest.setStoreId("********");
//        delayConfirmRequest.setPosSeq("Q" + System.nanoTime());
//        delayConfirmRequest.setMemo("确认分账");
//        System.out.println("订单号="+delayConfirmRequest.getPosSeq());
//        DelayConfirm delayConfirm = new DelayConfirm();
//        delayConfirm.setOrgPosSeq("BTF788173760148564157309");
//        ExtendParams extendParams = new ExtendParams();
//        // 分账信息
//        SplitInfo splitInfo = new SplitInfo();
//        splitInfo.setKeepAmt("2");
//
//        List<SplitList> mergedList = new ArrayList<>();
//        SplitList newSplitList1 = new SplitList();
////        newSplitList.setDivAmt(amt.subtract(fee).intValue()+"");
//        newSplitList1.setDivAmt("18");
////        newSplitList.setMerchantId("2290136");
//        newSplitList1.setMerchantId("2290140");
//        mergedList.add(newSplitList1);
////
////        SplitList newSplitList2 = new SplitList();
//////        newSplitList.setDivAmt(amt.subtract(fee).intValue()+"");
////        newSplitList2.setDivAmt("493");
////        newSplitList2.setMerchantId("2290136");
//////        newSplitList2.setMerchantId("2290124");
////        mergedList.add(newSplitList2);
//
//        splitInfo.setSplitList(mergedList);
//        extendParams.setSplitInfo(splitInfo);
//        delayConfirm.setExtendParams(extendParams);
//        delayConfirmRequest.setDelayConfirmRequest(delayConfirm);
//        System.out.println("确认分账请求request="+ JSONObject.toJSONString(delayConfirmRequest));
//        DelayConfirmResponse delayConfirmResponse = Client.getClient().execute(delayConfirmRequest);
//        System.out.println("确认分账请求response返回="+JSONObject.toJSONString(delayConfirmResponse));

        //延时分账退回
//        DelayConfirmrefundRequest confirmrefundRequest = new DelayConfirmrefundRequest();
//        confirmrefundRequest.setPosId("***********");
//        confirmrefundRequest.setIsspid(Client.isspid);
//        confirmrefundRequest.setSystemId(Client.systemId);
//        confirmrefundRequest.setStoreId("********");
//        confirmrefundRequest.setPosSeq("TH" + System.nanoTime());
//        DelayConfirmrefund confirmrefund = new DelayConfirmrefund();
//        ExtendParams extendParams = new ExtendParams();
//        confirmrefund.setExtendParams(extendParams);
//        confirmrefund.setOrgPosSeq("BTF354174954799061193823");
//        confirmrefundRequest.setDelayConfirmrefundRequest(confirmrefund);
//        System.out.println("确认分账退回请求request="+ JSONObject.toJSONString(confirmrefundRequest));
//        DelayConfirmrefundResponse response = Client.getClient().execute(confirmrefundRequest);
//        System.out.println("确认分账退回请求response返回="+JSONObject.toJSONString(response));

        //查询延时交易确认
//        DelayConfirmqueryRequest delayConfirmqueryRequest = new DelayConfirmqueryRequest();
//        delayConfirmqueryRequest.setPosId("***********");
//        delayConfirmqueryRequest.setIsspid(Client.isspid);
//        delayConfirmqueryRequest.setSystemId(Client.systemId);
//        delayConfirmqueryRequest.setStoreId("********");
//        delayConfirmqueryRequest.setPosSeq("QR" + System.nanoTime());
//        DelayConfirmquery confirmquery = new DelayConfirmquery();
//        confirmquery.setOrgPosSeq("FZ77691183191300");
//        delayConfirmqueryRequest.setDelayConfirmqueryRequest(confirmquery);
//        System.out.println("延时交易确认查询request="+ JSONObject.toJSONString(delayConfirmqueryRequest));
//        DelayConfirmqueryResponse response = Client.getClient().execute(delayConfirmqueryRequest);
//        System.out.println("延时交易确认查询response="+ JSONObject.toJSONString(response));

        //查询分账账户
//        MerchantQueryRequest merchantQueryRequest = new MerchantQueryRequest();
////        merchantQueryRequest.setPosId("***********");
//        merchantQueryRequest.setIsspid(Client.isspid);
//        merchantQueryRequest.setSystemId(Client.systemId);
////        merchantQueryRequest.setStoreId("********");
//        merchantQueryRequest.setPosSeq("QR" + System.nanoTime());
//        MerchantQuery merchantQuery = new MerchantQuery();
//        merchantQuery.setMerchantId("2297308");
//        merchantQueryRequest.setMerchantQueryRequest(merchantQuery);
//        System.out.println("查询分账账户查询request="+ JSONObject.toJSONString(merchantQueryRequest));
//        MerchantQueryResponse response = Client.getClient().execute(merchantQueryRequest);
//        System.out.println("查询分账账户查询response="+ JSONObject.toJSONString(response));

//        MerchantQueryRequest merchantQueryRequest = new MerchantQueryRequest();
//        merchantQueryRequest.setIsspid(Client.isspid);
//        merchantQueryRequest.setSystemId(Client.systemId);
//        merchantQueryRequest.setPosSeq("TH" + System.nanoTime());
//        MerchantQuery merchantQuery = new MerchantQuery();
//        merchantQuery.setPhone("15210684621");
////        merchantQuery.setMerchantId();
//        merchantQueryRequest.setMerchantQueryRequest(merchantQuery);
//        System.out.println("查询分账账户查询request="+ JSONObject.toJSONString(merchantQueryRequest));
//        MerchantQueryResponse response = Client.getClient().execute(merchantQueryRequest);
//        System.out.println("查询分账账户查询response="+ JSONObject.toJSONString(response));


//        MerchantDeleteRequest merchantDeleteRequest = new MerchantDeleteRequest();
//        merchantDeleteRequest.setIsspid(Client.isspid);
//        merchantDeleteRequest.setSystemId(Client.systemId);
//        merchantDeleteRequest.setPosSeq("TH" + System.nanoTime());
//        MerchantDelete merchantDelete = new MerchantDelete();
//        merchantDelete.setMerchantId("2290916");
//        merchantDeleteRequest.setMerchantDeleteRequest(merchantDelete);
//        System.out.println("删除分账账户request="+ JSONObject.toJSONString(merchantDeleteRequest));
//        MerchantDeleteResponse response = Client.getClient().execute(merchantDeleteRequest);
//        System.out.println("删除分账账户response="+ JSONObject.toJSONString(response));

    }

    private static List<YourObject> getYourObjects() {
        // 返回你的集合对象，这里只是示例，你需要替换成实际的集合获取逻辑
        // 这里假设你的对象有一个 getId() 方法和一个 getPrice() 方法
        List<YourObject> yourObjects = new ArrayList<>();
        YourObject yourObject1 = new YourObject();
        yourObject1.setPrice(new BigDecimal("100"));
        YourObject yourObject2 = new YourObject();
        yourObject2.setPrice(new BigDecimal("200"));
        YourObject yourObject3 = new YourObject();
        yourObject3.setPrice(new BigDecimal("300"));
        yourObjects.add(yourObject1);
        yourObjects.add(yourObject2);
        yourObjects.add(yourObject3);
        // 添加你的对象到集合中...
        return yourObjects;
    }

    private static BigDecimal calculateTotalAmount(List<YourObject> yourObjects) {
        // 计算集合对象的总金额
        return yourObjects.stream()
                .map(YourObject::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private static void distributeFee(List<YourObject> yourObjects, BigDecimal totalAmount, BigDecimal fee) {
        // 计算每个对象的金额占比
        for (YourObject obj : yourObjects) {
            BigDecimal price = obj.getPrice();
            BigDecimal ratio = price.divide(totalAmount, 4, BigDecimal.ROUND_HALF_UP);
            BigDecimal allocatedFee = ratio.multiply(fee).setScale(2, BigDecimal.ROUND_HALF_UP);
            obj.setAllocatedFee(allocatedFee);
            obj.setPriceAllocatedFee(price.subtract(allocatedFee));
        }
    }

    // 假设你的对象类有一个 get/set Price 的方法，以及一个 get/set AllocatedFee 的方法
    static class YourObject {
        private BigDecimal price;
        private BigDecimal allocatedFee;

        private BigDecimal priceAllocatedFee;

        public BigDecimal getPrice() {
            return price;
        }

        public void setPrice(BigDecimal price) {
            this.price = price;
        }

        public void setPriceAllocatedFee(BigDecimal priceAllocatedFee) {
            this.priceAllocatedFee = priceAllocatedFee;
        }

        public BigDecimal getPriceAllocatedFee() {
            return priceAllocatedFee;
        }

        public BigDecimal getAllocatedFee() {
            return allocatedFee;
        }

        public void setAllocatedFee(BigDecimal allocatedFee) {
            this.allocatedFee = allocatedFee;
        }
    }
//    {
//        "posId":"***********",
//            "storeId":"********",
//            "posSeq":"M969169778468059197173",
//            "memo":"我的水果店",
//            "unifiedorderRequest":{
//        "payType":"515",
//                "tradeType":"JSAPI",
//                "txAmt":1000100,
//                "buyerId":"o0Dsn5IeExD278FTp7LgKb2Bsbqw",
//                "goodsDetail":[
//        {
//            "goodsId":"15",
//                "goodsName":"分销批量编辑",
//                "quantity":1,
//                "price":1000000,
//                "body":"分销批量编辑"
//        }
//        ],
//        "notifyUrl":"https://api.sohuglobal.com/pay/pay/callback/yima",
//                "extendParams":{
//            "splitFlag":"R",
//                    "planSplitDate":"2023-10-20"
//        }
//    },
//        "requestType":"unifiedorder_request",
//            "isspid":"********",
//            "systemId":"5441"
//    }

    /**
     * 统一下单
     */
    public static void unifiedorder(UnifiedorderRequest request) {
        Unifiedorder unifiedorder = new Unifiedorder();
        unifiedorder.setPayType("515");
        unifiedorder.setTradeType("JSAPI");
        unifiedorder.setTxAmt(1000100);
        unifiedorder.setBuyerId("o0Dsn5IeExD278FTp7LgKb2Bsbqw");
        // 商品信息
        List<GoodsDetail> goodsDetailList = new ArrayList<>();
        GoodsDetail goodsDetail = new GoodsDetail();
        goodsDetail.setGoodsId("15");
        goodsDetail.setGoodsName("分销批量编辑");
        goodsDetail.setQuantity(1);
        goodsDetail.setPrice(1000000);
        goodsDetail.setBody("分销批量编辑");
        goodsDetailList.add(goodsDetail);
        unifiedorder.setGoodsDetail(goodsDetailList);
        unifiedorder.setNotifyUrl("https://api.sohuglobal.com/pay/pay/callback/yima");
        ExtendParams extendParams = new ExtendParams();
        extendParams.setSplitFlag("D");
        extendParams.setPlanSplitDate("2023--10-21".replaceAll("-", ""));
        unifiedorder.setExtendParams(extendParams);
        request.setUnifiedorderRequest(unifiedorder);
        UnifiedorderResponse execute = Client.getClient().execute(request);
        System.out.println(JSON.toJSON(execute));
    }

    /**
     * 退款
     */
    public static void refundOrder(BarcodeReverseRequest request) {
        BarcodeReverse unifiedorder = new BarcodeReverse();
//        Unifiedorder unifiedorder = new Unifiedorder();
        unifiedorder.setPayType("515");
//        unifiedorder.setTradeType("JSAPI");
        unifiedorder.setTxAmt(1500);
        unifiedorder.setOrgPosSeq("M221169925544799559012");
        // 商品信息
        List<GoodsDetail> goodsDetailList = new ArrayList<>();
        GoodsDetail goodsDetail = new GoodsDetail();
        goodsDetail.setGoodsId("14");
        goodsDetail.setGoodsName("支付商单接单酬金-hss_633412660");
        goodsDetail.setQuantity(1);
        goodsDetail.setPrice(1500);
        goodsDetail.setBody("支付商单接单酬金-hss_633412660");
        goodsDetailList.add(goodsDetail);
        unifiedorder.setGoodsDetail(goodsDetailList);
//        unifiedorder.setNotifyUrl("https://api.sohuglobal.com/pay/pay/callback/yima");
//        ExtendParams extendParams = new ExtendParams();
//        extendParams.setSplitFlag("D");
//        extendParams.setPlanSplitDate("2023--10-21".replaceAll("-", ""));
//        unifiedorder.setExtendParams(extendParams);
        request.setBarcodeReverseRequest(unifiedorder);

//        ExtendParams params = new ExtendParams();
//        SplitInfo splitInfo = new SplitInfo();
//        splitInfo.setKeepAmt("0");
//        List<SplitList> splitLists = new ArrayList<>();
//        SplitList splitList1 = new SplitList();
//        splitList1.setDivAmt("237");
//        splitList1.setMerchantId("2289446");
//        SplitList splitList2 = new SplitList();
//        splitList2.setDivAmt("238");
//        splitList2.setMerchantId("2289447");
//
//        SplitList splitList3 = new SplitList();
//        splitList3.setDivAmt("25");
//        splitList3.setMerchantId("2289441");
////        SplitList splitList4 = new SplitList();
////        splitList4.setDivAmt("100");
////        splitList4.setMerchantId("2289458");
//        SplitList splitList5 = new SplitList();
//        splitList5.setDivAmt("525");
//        splitList5.setMerchantId("2289458");
////        splitLists.add(splitList1);
////        splitLists.add(splitList2);
//        splitLists.add(splitList3);
////        splitLists.add(splitList4);
////        splitLists.add(splitList5);
//        splitInfo.setSplitList(splitLists);
//        params.setSplitInfo(splitInfo);

        BarcodeReverseResponse execute = Client.getClient().execute(request);
        System.out.println(JSON.toJSON(execute));
    }

    /**
     * 终端绑定
     */
    public static void storeInfo(BindPosRequest request) {

        request.setPosId("***********");
        request.setRequestType("bind_pos_request");

        StoreInfo storeInfo = new StoreInfo();
        storeInfo.setStoreId("********");
        storeInfo.setStoreName("威科跨境贸易服务（湖北）有限公司");
        storeInfo.setStoreAddr("湖北省武汉市东湖新技术开发区光谷三路777号C1-2综合保税区一期2号标准厂房159（自贸区武汉片区");
        storeInfo.setCity("武汉");
        storeInfo.setProvince("湖北省");
        storeInfo.setTown("武昌区");

        request.setStoreInfo(storeInfo);
        BindPosResponse execute = Client.getClient().execute(request);
        System.out.println(execute.toString());
    }

    /**
     * 终端绑定
     */
    public static void storeInfoEdit(BindPosRequest request) {

        request.setPosId("***********");
        request.setRequestType("store_edit_request");

//        HashMap<String, Object> hashMap = new HashMap<>();
        request.setStoreId("********");
        StoreInfo storeInfo = new StoreInfo();
        storeInfo.setStoreId("********");
        storeInfo.setStoreName("威科跨境贸易服务（湖北）有限公司");
        storeInfo.setStoreAddr("湖北省武汉市东湖新技术开发区光谷三路777号C1-2综合保税区一期2号标准厂房159（自贸区武汉片区");
        storeInfo.setCity("武汉");
        storeInfo.setProvince("湖北省");
        storeInfo.setTown("武昌区");

        request.setStoreInfo(storeInfo);
        BindPosResponse execute = Client.getClient().execute(request);
        System.out.println(execute.toString());
    }

    /**
     * 创建用户
     * <p>
     * *********** 1000621
     */
    public static void createUser() {
        MerchantCreateRequest request = new MerchantCreateRequest();
        MerchantCreate create = new MerchantCreate();
        create.setMerchantShortName("1111");
        create.setMerchantName("222");
        create.setPhone("***********");
        create.setType("3");
        request.setMerchantCreateRequest(create);
        request.setPosSeq("12345678901234567890");
        MerchantCreateResponse response = Client.getClient().execute(request);
        System.out.println(response.toString());
    }

    /**
     * 删除用户
     * <p>
     * *********** 1000621
     */
    public static void deleteUser() {
        MerchantDeleteRequest request = new MerchantDeleteRequest();
        MerchantDelete delete = new MerchantDelete();
        delete.setMerchantId("2289284");
        request.setMerchantDeleteRequest(delete);
        request.setPosSeq("12345678901234567890");
        MerchantDeleteResponse response = Client.getClient().execute(request);
        System.out.println(response.toString());
    }

    /**
     * 用户查询
     * <p>
     * *********** 1000621
     */
    public static void queryUser() {
        MerchantQueryRequest request = new MerchantQueryRequest();
        MerchantQuery query = new MerchantQuery();
//        query.setPhone("***********");
        query.setMerchantId("2289448");
        request.setMerchantQueryRequest(query);
        request.setPosSeq("123456789012345678902");
        MerchantQueryResponse response = Client.getClient().execute(request);
        System.out.println(response.toString());
    }

    /**
     * 用户余额
     * <p>
     * *********** 1000621
     */
    public static void queryUserMoney() {
        AccountBalanceQueryRequest request = new AccountBalanceQueryRequest();
        request.setIsspid(Client.isspid);
        request.setSystemId(Client.systemId);
//        request.setMerchantId("2289387");
        request.setMerchantId("2289448");
        request.setPosSeq(System.nanoTime() + "");
        AccountBalanceQueryResponse execute = Client.getClient().execute(request);
        System.out.println(execute.toString());
    }

    /**
     * 查询延时分账金额
     */
    public static void queryDelayPrice() {
        DelayConfirmqueryRequest request = new DelayConfirmqueryRequest();
        request.setPosId("***********");
        request.setIsspid(Client.isspid);
        request.setSystemId(Client.systemId);
        request.setStoreId("********");
        request.setPosSeq("M2521699060111869987292");
        DelayConfirmquery confirmquery = new DelayConfirmquery();
        confirmquery.setOrgPosSeq("***********************");
        request.setDelayConfirmqueryRequest(confirmquery);
        DelayConfirmqueryResponse execute = Client.getClient().execute(request);
        System.out.println(execute.toString());
    }

    /**
     * 退款
     */
    public static void refundOrderDelay() {
        DelayConfirmrefundRequest request = new DelayConfirmrefundRequest();
        request.setPosId("***********");
        request.setIsspid(Client.isspid);
        request.setSystemId(Client.systemId);
        request.setStoreId("********");
//        request.setPosSeq(System.nanoTime() + "");
        request.setPosSeq("R" + "FZ2151699060757993768422217");
        DelayConfirmrefund confirmrefund = new DelayConfirmrefund();
        confirmrefund.setOrgPosSeq("FZ777169925608723433227");
        ExtendParams params = new ExtendParams();
        SplitInfo splitInfo = new SplitInfo();
        splitInfo.setKeepAmt("1279");
        List<SplitList> splitLists = new ArrayList<>();
        SplitList splitList1 = new SplitList();
        splitList1.setDivAmt("71");
        splitList1.setMerchantId("2289446");
        SplitList splitList2 = new SplitList();
        splitList2.setDivAmt("71");
        splitList2.setMerchantId("2289447");

//        SplitList splitList3 = new SplitList();
//        splitList3.setDivAmt("2");
//        splitList3.setMerchantId("2289465");
//        SplitList splitList4 = new SplitList();
//        splitList4.setDivAmt("100");
//        splitList4.setMerchantId("2289458");
        SplitList splitList5 = new SplitList();
        splitList5.setDivAmt("75");
        splitList5.setMerchantId("2289458");
        splitLists.add(splitList1);
        splitLists.add(splitList2);
//        splitLists.add(splitList3);
//        splitLists.add(splitList4);
        splitLists.add(splitList5);
        splitInfo.setSplitList(splitLists);
        params.setSplitInfo(splitInfo);
        confirmrefund.setExtendParams(params);
        request.setDelayConfirmrefundRequest(confirmrefund);
        DelayConfirmrefundResponse execute = Client.getClient().execute(request);
        System.out.println(JSON.toJSON(execute));
    }

    /**
     * 查询提现
     */
    private static void queryWithdraw() {
        MerchantWithdrawqueryRequest request = new MerchantWithdrawqueryRequest();
        request.setIsspid(Client.isspid);
        request.setSystemId(Client.systemId);
        request.setPosSeq("TX" + System.nanoTime());
        request.setIsspid(Client.isspid);
        MerchantWithdrawquery withdrawquery = new MerchantWithdrawquery();
        withdrawquery.setWithdrawSeq("12345678901234567890");
        request.setMerchantWithdrawqueryRequest(withdrawquery);
        MerchantWithdrawqueryResponse execute = Client.getClient().execute(request);
        System.out.println(JSON.toJSON(execute));
    }

    public static void codePay() {
        ScanpayRequest request = new ScanpayRequest();
        request.setPosSeq("CP" + System.nanoTime());
        request.setPosId("***********");
        request.setIsspid(Client.isspid);
        request.setSystemId(Client.systemId);
        request.setStoreId("********");

        Scanpay scanpay = new Scanpay();
        // 微信固定503
        scanpay.setPayType("503");
//        scanpay.setPayType("502");
//        unifiedorder.setPayType("515");
//        unifiedorder.setTradeType("JSAPI");
        scanpay.setTxAmt(100);
//        unifiedorder.setBuyerId("o0Dsn5IeExD278FTp7LgKb2Bsbqw");
        // 商品信息
        List<GoodsDetail> goodsDetailList = new ArrayList<>();
        GoodsDetail goodsDetail = new GoodsDetail();
        goodsDetail.setGoodsId("15");
        goodsDetail.setGoodsName("分销批量编辑");
        goodsDetail.setQuantity(1);
        goodsDetail.setPrice(100);
        goodsDetail.setBody("分销批量编辑");
        goodsDetailList.add(goodsDetail);
        scanpay.setGoodsDetail(goodsDetailList);
        scanpay.setNotifyUrl("https://api.sohuglobal.com/pay/pay/callback/yima");
//        ExtendParams extendParams = new ExtendParams();
//        extendParams.setSplitFlag("D");
//        extendParams.setPlanSplitDate("2023--10-21".replaceAll("-", ""));
//        unifiedorder.setExtendParams(extendParams);
        request.setScanpayRequest(scanpay);
        ScanpayResponse execute = Client.getClient().execute(request);
        System.out.println(JSON.toJSON(execute));
    }

    public static void queryPay() {
        BarcodeQueryRequest request = new BarcodeQueryRequest();
        request.setPosId("***********");
        request.setIsspid(Client.isspid);
        request.setSystemId(Client.systemId);
        request.setStoreId("********");
//        request.setPosSeq("BTP714170599783490425404");
        BarcodeQuery barcodeQuery = new BarcodeQuery();
        barcodeQuery.setOrgPosSeq("BTP714170599783490425404");
        barcodeQuery.setPayType("503");
        request.setBarcodeQueryRequest(barcodeQuery);
        BarcodeQueryResponse execute = Client.getClient().execute(request);
        System.out.println(JSON.toJSON(execute));
    }

}
