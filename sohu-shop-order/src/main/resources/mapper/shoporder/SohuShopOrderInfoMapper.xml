<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.shoporder.mapper.SohuShopOrderInfoMapper">

    <resultMap type="com.sohu.shoporder.domain.SohuShopOrderInfo" id="SohuShopOrderInfoResult">
        <result property="id" column="id"/>
        <result property="merOrderNo" column="mer_order_no"/>
        <result property="merId" column="mer_id"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="image" column="image"/>
        <result property="productAttrValueId" column="product_attr_value_id"/>
        <result property="sku" column="sku"/>
        <result property="skuId" column="sku_id"/>
        <result property="price" column="price"/>
        <result property="payNum" column="pay_num"/>
        <result property="independentUserId" column="independent_user_id"/>
        <result property="mcnId" column="mcn_id"/>
        <result property="originatorUserId" column="originator_user_id"/>
        <result property="inviteUserId" column="invite_user_id"/>
        <result property="distributorPrice" column="distributor_price"/>
        <result property="invitePrice" column="invite_price"/>
        <result property="cityPrice" column="city_price"/>
        <result property="countryPrice" column="country_price"/>
        <result property="adminPrice" column="admin_price"/>
        <result property="independentSuccess" column="independent_success"/>
        <result property="weight" column="weight"/>
        <result property="volume" column="volume"/>
        <result property="isReply" column="is_reply"/>
        <result property="isReceipt" column="is_receipt"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap id="VirtualOrderMap" type="com.sohu.shoporder.api.model.SohuVirtualOrderInfoModel">
        <result property="merOrderNo" column="mer_order_no"/>
        <result property="masterOrderNo" column="master_order_no"/>
        <result property="merId" column="mer_id"/>
        <result property="orderStatus" column="order_status"/>
        <result property="orderTime" column="order_time"/>
        <result property="totalPrice" column="total_price"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="virtualSkuName" column="virtual_sku_name"/>
        <result property="image" column="image"/>
        <result property="productAttrValueId" column="product_attr_value_id"/>
        <result property="sku" column="sku"/>
        <result property="skuId" column="sku_id"/>
        <result property="price" column="price"/>
        <result property="payNum" column="pay_num"/>
        <result property="independentUserId" column="independent_user_id"/>
        <result property="inviteUserId" column="invite_user_id"/>
        <result property="distributorPrice" column="distributor_price"/>
        <result property="invitePrice" column="invite_price"/>
        <result property="cityPrice" column="city_price"/>
        <result property="countryPrice" column="country_price"/>
        <result property="adminPrice" column="admin_price"/>
        <result property="independentSuccess" column="independent_success"/>
        <result property="weight" column="weight"/>
        <result property="volume" column="volume"/>
        <result property="isReply" column="is_reply"/>
        <result property="isReceipt" column="is_receipt"/>
        <result property="thirdProductId" column="third_product_id"/>
        <result property="virtualAccount" column="virtual_account"/>
        <result property="virtualAccountType" column="virtual_account_type"/>
        <result property="virtualSourceType" column="virtual_source_type"/>
        <result property="virtualCost" column="virtual_cost"/>
        <result property="virtualSkuId" column="virtual_sku_id"/>
        <result property="virtualLogisticsStatus" column="virtual_logistics_status"/>
        <result property="errorHandelStatus" column="error_handel_status"/>
        <result property="errorHandelRemarks" column="error_handel_remarks"/>
    </resultMap>

    <select id="selectByProductName" resultType="com.sohu.shoporder.domain.SohuShopOrderInfo">
        SELECT so.order_no,
        so.master_order_no,
        si.*
        FROM sohu_shop_order_info si
        INNER JOIN sohu_shop_order so ON si.mer_order_no = so.order_no
        WHERE so.user_id =
        AND si.product_name like concat('%', #{productName}, '%')
        GROUP BY so.master_order_no
    </select>


    <select id="getGmvByCategoryIdAndMerchantIdAndTime" resultType="java.math.BigDecimal">
        SELECT
        SUM(si.price * si.pay_num)
        FROM
        sohu_shop_order_info si INNER JOIN sohu_shop_order so
        ON si.mer_order_no = so.order_no AND si.mer_id = so.mer_id AND so.pay_time BETWEEN #{startTime} AND #{endTime}
        WHERE
        si.mer_id = #{merchantId}
        AND
        si.category_id = #{categoryId}
    </select>

    <select id="queryVirtualPageList" resultMap="VirtualOrderMap">
        SELECT
        so.master_order_no,
        so.`status` AS order_status,
        so.`create_time` AS order_time,
        so.total_price AS total_price,
        so.error_handel_status AS error_handel_status,
        so.error_handel_remarks AS error_handel_remarks,
        si.*,
        l.state AS virtual_logistics_status,
        pvs.sku_name AS virtual_sku_name
        FROM sohu_shop_order_info si
        LEFT JOIN sohu_shop_order so ON si.mer_order_no = so.order_no
        <choose>
            <when test="bo.virtualLogisticsStatus != null and bo.virtualLogisticsStatus != ''">
                INNER JOIN (
                SELECT t1.order_no, t1.state
                FROM sohu_shop_order_virtual_logistics t1
                INNER JOIN (
                SELECT order_no, MAX(id) AS max_id FROM sohu_shop_order_virtual_logistics GROUP BY order_no
                ) t2 ON t1.order_no = t2.order_no AND t1.id = t2.max_id
                ) l ON l.order_no = si.mer_order_no AND l.state = #{bo.virtualLogisticsStatus}
            </when>
            <otherwise>
                LEFT JOIN (
                SELECT t1.order_no, t1.state
                FROM sohu_shop_order_virtual_logistics t1
                INNER JOIN (
                SELECT order_no, MAX(id) AS max_id FROM sohu_shop_order_virtual_logistics GROUP BY order_no
                ) t2 ON t1.order_no = t2.order_no AND t1.id = t2.max_id
                ) l ON l.order_no = si.mer_order_no
            </otherwise>
        </choose>
        LEFT JOIN sohu_product_virtual_sku pvs ON pvs.id = si.virtual_sku_id
        <where>
            si.virtual_source_type = 1
            AND so.`status` != 'UNPAID'
            <if test="bo.merIds != null and bo.merIds.size() > 0">
                AND si.mer_id IN
                <foreach collection="bo.merIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="bo.merOrderNo != null and bo.merOrderNo != ''">
                AND si.mer_order_no = #{bo.merOrderNo}
            </if>
            <if test="bo.productId != null and bo.productId != ''">
                AND si.product_id = #{bo.productId}
            </if>
            <if test="bo.productName != null and bo.productName != ''">
                AND si.product_name = #{bo.productName}
            </if>
            <if test="bo.thirdProductId != null and bo.thirdProductId != ''">
                AND si.third_product_id = #{bo.thirdProductId}
            </if>
            <if test="bo.startTime != null and bo.startTime != ''">
                and so.create_time <![CDATA[ >= ]]> #{bo.startTime}
            </if>
            <if test="bo.endTime != null and bo.endTime != ''">
                and so.create_time <![CDATA[ <= ]]> #{bo.endTime}
            </if>
            <if test="bo.orderStatus != null and bo.orderStatus != ''">
                AND so.`status` = #{bo.orderStatus}
            </if>
            <if test="bo.errorHandelStatus != null">
                AND so.`error_handel_status` = #{bo.errorHandelStatus}
            </if>
        </where>
        ORDER BY so.create_time DESC
    </select>

    <select id="queryVirtualList" resultMap="VirtualOrderMap">
        SELECT
        so.master_order_no,
        so.`status` AS order_status,
        so.`create_time` AS order_time,
        so.total_price AS total_price,
        so.error_handel_status AS error_handel_status,
        so.error_handel_remarks AS error_handel_remarks,
        si.*,
        l.state AS virtual_logistics_status,
        pvs.sku_name AS virtual_sku_name
        FROM sohu_shop_order_info si
        LEFT JOIN sohu_shop_order so ON si.mer_order_no = so.order_no
        <choose>
            <when test="bo.virtualLogisticsStatus != null and bo.virtualLogisticsStatus != ''">
                INNER JOIN (
                SELECT t1.order_no, t1.state
                FROM sohu_shop_order_virtual_logistics t1
                INNER JOIN (
                SELECT order_no, MAX(id) AS max_id FROM sohu_shop_order_virtual_logistics GROUP BY order_no
                ) t2 ON t1.order_no = t2.order_no AND t1.id = t2.max_id
                ) l ON l.order_no = si.mer_order_no AND l.state = #{bo.virtualLogisticsStatus}
            </when>
            <otherwise>
                LEFT JOIN (
                SELECT t1.order_no, t1.state
                FROM sohu_shop_order_virtual_logistics t1
                INNER JOIN (
                SELECT order_no, MAX(id) AS max_id FROM sohu_shop_order_virtual_logistics GROUP BY order_no
                ) t2 ON t1.order_no = t2.order_no AND t1.id = t2.max_id
                ) l ON l.order_no = si.mer_order_no
            </otherwise>
        </choose>
        LEFT JOIN sohu_product_virtual_sku pvs ON pvs.id = si.virtual_sku_id
        <where>
            si.virtual_source_type = 1
            AND so.`status` != 'UNPAID'
            <if test="bo.merIds != null and bo.merIds.size() > 0">
                AND si.mer_id IN
                <foreach collection="bo.merIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="bo.merOrderNo != null and bo.merOrderNo != ''">
                AND si.mer_order_no = #{bo.merOrderNo}
            </if>
            <if test="bo.productId != null and bo.productId != ''">
                AND si.product_id = #{bo.productId}
            </if>
            <if test="bo.productName != null and bo.productName != ''">
                AND si.product_name = #{bo.productName}
            </if>
            <if test="bo.thirdProductId != null and bo.thirdProductId != ''">
                AND si.third_product_id = #{bo.thirdProductId}
            </if>
            <if test="bo.startTime != null and bo.startTime != ''">
                and so.create_time <![CDATA[ >= ]]> #{bo.startTime}
            </if>
            <if test="bo.endTime != null and bo.endTime != ''">
                and so.create_time <![CDATA[ <= ]]> #{bo.endTime}
            </if>
            <if test="bo.orderStatus != null and bo.orderStatus != ''">
                AND so.`status` = #{bo.orderStatus}
            </if>
            <if test="bo.errorHandelStatus != null">
                AND so.`error_handel_status` = #{bo.errorHandelStatus}
            </if>
        </where>
        ORDER BY so.create_time DESC
    </select>


</mapper>
