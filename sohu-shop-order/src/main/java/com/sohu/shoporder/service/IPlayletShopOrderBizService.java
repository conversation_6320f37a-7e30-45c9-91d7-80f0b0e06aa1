package com.sohu.shoporder.service;

import com.sohu.middle.api.bo.SohuOperateBo;
import com.sohu.shoporder.api.model.SohuShopOrderModel;

/**
 * 商城订单综合业务服务接口
 */
public interface IPlayletShopOrderBizService {
    /**
     * mq延迟队列消费。从MQ服务com.playlet.stream.mq.consumer.DelayConsumer#delayConfirm迁移至此，只为解耦，未改变原代码
     * @param shopOrder
     * @return
     */
    Boolean mqDelayConfirm(SohuShopOrderModel shopOrder);
    Boolean mqDelayConfirmV2(SohuShopOrderModel shopOrder);
    /**
     * mq延迟队列消费。从MQ服务com.playlet.stream.mq.consumer.updateBatchMerchants#delayConfirm迁移至此，只为解耦，未改变原代码
     * @return
     */
    void mqUpdateBatchMerchants(SohuOperateBo operateBo, Boolean isRefund);

    /**
     * 更新订单评论状态
     * @param orderNo 商户订单编号
     * @param orderInfoId 订单详情id
     * @param isReply 是否评价，0-未评价，1-已评价
     * @return
     */
    Boolean updateReplyStatus(String orderNo,Long orderInfoId,Integer isReply);
    /**
     * 订单检测
     * @param masterOrderNo
     * @return
     */
    Boolean orderCheck(String masterOrderNo);
}
