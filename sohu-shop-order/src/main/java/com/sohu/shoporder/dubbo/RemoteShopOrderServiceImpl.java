package com.sohu.shoporder.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.utils.JsonUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuOperateBo;
import com.sohu.shoporder.api.RemoteShopOrderService;
import com.sohu.shoporder.api.bo.*;
import com.sohu.shoporder.api.domain.*;
import com.sohu.shoporder.api.model.*;
import com.sohu.shoporder.api.vo.*;
import com.sohu.shoporder.domain.SohuShopOrder;
import com.sohu.shoporder.domain.SohuShopOrderInfo;
import com.sohu.shoporder.mapper.SohuShopMasterOrderMapper;
import com.sohu.shoporder.mapper.SohuShopOrderInfoMapper;
import com.sohu.shoporder.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单服务
 *
 * @author: zc
 * @date: 2023/7/26 15:15
 * @version: 1.0.0
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteShopOrderServiceImpl implements RemoteShopOrderService {

    private final IPlayletShopOrderService iPlayletShopOrderService;
    private final IPlayletShopRefundOrderService iPlayletShopRefundOrderService;
    private final IPlayletShopOrderInfoService iPlayletShopOrderInfoService;
    private final IPlayletShopOrderBizService iPlayletShopOrderBizService;
    private final IPlayletShopOrderLogisticsService iPlayletShopOrderLogisticsService;

    private final ISohuShopOrderService iSohuShopOrderService;
    private final ISohuShopRefundOrderService iSohuShopRefundOrderService;
    private final ISohuShopOrderInfoService iSohuShopOrderInfoService;
    private final ISohuShopOrderBizService iSohuShopOrderBizService;
    private final ISohuShopOrderLogisticsService iSohuShopOrderLogisticsService;

    private final SohuShopMasterOrderMapper sohuShopMasterOrderMapper;
    private final SohuShopOrderInfoMapper sohuShopOrderInfoMapper;

    @Override
    public TableDataInfo<SohuShopOrderModel> getOrderListByType(SohuOrderReqBo bo, PageQuery pageQuery) {
        if (StringUtils.equals(Constants.SOHUGLOBAL, bo.getSysSource())) {
            return iSohuShopOrderService.getOrderListByType(bo, pageQuery);
        }
        return iPlayletShopOrderService.getOrderListByType(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuRefundOrderModel> getRefundOrderList(PageQuery pageQuery, SohuOrderReqBo bo) {
        if (StringUtils.equals(Constants.SOHUGLOBAL, bo.getSysSource())) {
            return iPlayletShopRefundOrderService.getRefundOrderList(pageQuery, bo);
        }
        return iPlayletShopRefundOrderService.getRefundOrderList(pageQuery, bo);
    }

    @Override
    public TableDataInfo<SohuMasterOrderAwaitPayModel> getWaitOrderListByType(PageQuery pageQuery, SohuOrderReqBo bo) {
        if (StringUtils.equals(Constants.SOHUGLOBAL, bo.getSysSource())) {
            return iSohuShopOrderService.getWaitOrderListByType(pageQuery, bo);
        }
        return iPlayletShopOrderService.getWaitOrderListByType(pageQuery, bo);
    }


    @Override
    public List<SohuShopOrderModel> getListByMasterNo(String masterOrderNo) {
        List<SohuShopOrder> shopOrderList = iPlayletShopOrderService.getListByMasterNo(masterOrderNo);
        List<SohuShopOrderModel> orderInfoModelList = Lists.newArrayList();
        for (SohuShopOrder shopOrder : shopOrderList) {
            SohuShopOrderModel infoModel = new SohuShopOrderModel();
            BeanUtils.copyProperties(shopOrder, infoModel);
            orderInfoModelList.add(infoModel);
        }
        return orderInfoModelList;
    }

    @Override
    public Boolean updateBatchById(List<SohuShopOrderModel> storeOrderList) {
        return iPlayletShopOrderService.updateBatchById(storeOrderList);
    }

    @Override
    public Map<String, Object> createOrder(SohuCreateOrderReqBo createOrderReqBo) {
        log.info("SohuCreateOrderReqBo:{}", JsonUtils.toJsonString(createOrderReqBo));
        if (StringUtils.equals(Constants.SOHUGLOBAL, createOrderReqBo.getSysSource())) {
            return iSohuShopOrderService.createOrder(createOrderReqBo);
        }
        return iPlayletShopOrderService.createOrder(createOrderReqBo);
    }

    @Override
    public Map<String, Object> preOrder(SohuPreOrderReqBo preOrderReqBo) {
        if (StringUtils.equals(Constants.SOHUGLOBAL, preOrderReqBo.getSysSource())) {
            return iSohuShopOrderService.preorder(preOrderReqBo);
        }
        return iPlayletShopOrderService.preorder(preOrderReqBo);
    }

    @Override
    public SohuPreOrderModel loadPreOrder(String preOrderNo) {
        return iPlayletShopOrderService.loadPreOrder(preOrderNo);
    }

    @Override
    public Boolean cancelOrder(String orderNo) {
        return iPlayletShopOrderService.cancelOrder(orderNo);
    }

    @Override
    public Boolean cancelByMasterNo(String masterOrderNo, boolean isUser) {
        return iPlayletShopOrderService.cancelByMasterNo(masterOrderNo, isUser);
    }

    @Override
    public SohuApplyRefundOrderInfoModel refundApplyOrder(String orderNo) {
        return iPlayletShopOrderService.refundApplyOrder(orderNo);
    }

    @Override
    public Boolean refundApply(SohuOrderRefundApplyReqBo refundApplyReqBo) {
        if (StringUtils.equals(Constants.SOHUGLOBAL, refundApplyReqBo.getSysSource())) {
            return iSohuShopOrderService.refundApply(refundApplyReqBo);
        }
        return iPlayletShopOrderService.refundApply(refundApplyReqBo);
    }

    @Override
    public Boolean takeOrder(String orderNo) {
        Long userId = LoginHelper.getUserId();
        return iPlayletShopOrderService.taskOrder(orderNo, userId);
    }

    @Override
    public SohuShopOrderModel getByOrderNo(String shopOrderNo) {
        SohuShopOrder shopOrder = iPlayletShopOrderService.getByOrderNo(shopOrderNo);
        SohuShopOrderModel orderModel = new SohuShopOrderModel();
        BeanUtils.copyProperties(shopOrder, orderModel);
        return orderModel;
    }

    @Override
    public Boolean updateById(SohuShopOrderModel storeOrder) {
        SohuShopOrder shopOrder = new SohuShopOrder();
        BeanUtils.copyProperties(storeOrder, shopOrder);
        log.warn("shopOrder 订单对象：{}", JSONObject.toJSONString(shopOrder));
        return iPlayletShopOrderService.updateById(shopOrder);
    }

    @Override
    public SohuMasterOrderInfoModel getMasterOrderInfo(String orderNo) {
        return iPlayletShopOrderService.masterOrderInfo(orderNo);
    }

    @Override
    public SohuStoreOrderInfoResModel getShopOrderInfo(String orderNo) {
        return iPlayletShopOrderService.shopOrderInfo(orderNo);
    }

    @Override
    public SohuRefundOrderInfoModel refundOrderInfo(String refundOrderNo) {
        return iPlayletShopOrderService.refundOrderInfo(refundOrderNo);
    }

    @Override
    public Boolean deleteOrder(String orderNo) {
        return iPlayletShopOrderService.deleteOrder(orderNo);
    }

    @Override
    public KuaidiModel queryTrack(SohuQueryTrackBo param) {
        return iPlayletShopOrderService.queryTrack(param);
    }

    @Override
    public Map<String, SohuShopOrderInfoModel> queryMap(List<String> orderNos) {
        List<SohuShopOrderInfo> orderInfos = sohuShopOrderInfoMapper.selectList(SohuShopOrderInfo::getMerOrderNo, orderNos);
        Map<String, SohuShopOrderInfoModel> map = new HashMap<>();
        if (CollUtil.isNotEmpty(orderInfos)) {
            for (SohuShopOrderInfo orderInfo : orderInfos) {
                SohuShopOrderInfoModel infoModel = new SohuShopOrderInfoModel();
                BeanUtils.copyProperties(orderInfo, infoModel);
                map.put(orderInfo.getMerOrderNo(), infoModel);
            }
        }
        return map;
    }

    @Override
    public List<SohuShopOrderInfoModel> list(List<String> orderNos) {
        List<SohuShopOrderInfo> orderInfos = sohuShopOrderInfoMapper.selectList(SohuShopOrderInfo::getMerOrderNo, orderNos);
        if (CollUtil.isEmpty(orderNos)) {
            return null;
        }
        List<SohuShopOrderInfoModel> result = Lists.newArrayList();
        for (SohuShopOrderInfo info : orderInfos) {
            SohuShopOrderInfoModel infoModel = new SohuShopOrderInfoModel();
            BeanUtils.copyProperties(info, infoModel);
            result.add(infoModel);
        }
        return result;
    }

    @Override
    public List<SohuShopOrderInfoModel> getOrdersByProductId(List<Long> productIds) {
        return iPlayletShopOrderInfoService.getOrdersByProductId(productIds);
    }

    @Override
    public List<SohuShopOrderModel> getOrderListByOrderNo(List<String> orderNoList) {
        return iPlayletShopOrderService.getOrderListByOrderNo(orderNoList);
    }
//    @Override
//    public TableDataInfo<SohuShopOrderStatModel> queryShopOrderTop(Long mcnId, Long articleUserId, PageQuery pageQuery) {
//        return this.orderService.queryShopOrderTop(mcnId,articleUserId,pageQuery);
//    }

    @Override
    public TableDataInfo<SohuShopOrderListVo> queryStorePageList(SohuQueryOrderBo bo, PageQuery pageQuery) {
        SohuStatusOrderBo orderBo = BeanCopyUtils.copy(bo, SohuStatusOrderBo.class);
        TableDataInfo<SohuShopOrderVo> dataInfo = iPlayletShopOrderService.queryStorePageList(orderBo, pageQuery);
        return TableDataInfoUtils.copyInfo(dataInfo, SohuShopOrderListVo.class);
    }

    @Override
    public SohuShopOrderDetailVo queryByOrderNo(String orderNo) {
        SohuShopOrderVo sohuShopOrderVo = iPlayletShopOrderService.queryByOrderNo(orderNo);
        return BeanCopyUtils.copy(sohuShopOrderVo, SohuShopOrderDetailVo.class);
    }

    @Override
    public Boolean remark(SohuRemarkOrderReqBo bo) {
        SohuRemarkOrderBo remarkOrderBo = BeanCopyUtils.copy(bo, SohuRemarkOrderBo.class);
        return iPlayletShopOrderService.remark(remarkOrderBo);
    }

    @Override
    public TableDataInfo<SohuShopOrderListVo> queryPcPageList(SohuQueryOrderBo bo, PageQuery pageQuery) {
        SohuAdminQueryOrderBo sohuAdminQueryOrderBo = BeanCopyUtils.copy(bo, SohuAdminQueryOrderBo.class);
        TableDataInfo<SohuShopOrderVo> dataInfo = iPlayletShopOrderService.queryPcPageList(sohuAdminQueryOrderBo, pageQuery);
        return TableDataInfoUtils.copyInfo(dataInfo, SohuShopOrderListVo.class);
    }

    @Override
    public Boolean remarkPc(SohuRemarkOrderReqBo bo) {
        return iPlayletShopOrderService.remarkPc(BeanCopyUtils.copy(bo, SohuRemarkOrderBo.class));
    }

    @Override
    public Boolean send(SohuSendOrderReqBo bo) {
        return iPlayletShopOrderService.send(BeanCopyUtils.copy(bo, SohuSendOrderBo.class));
    }

    @Override
    public SohuShopOrderStatModel getTradeStat(Long mcnId) {
        return BeanCopyUtils.copy(iPlayletShopOrderService.getTradeStat(mcnId), SohuShopOrderStatModel.class);
    }

    @Override
    public SohuShopOrderStatModel getAfterSaleStat(Long mcnId) {
        return BeanCopyUtils.copy(iPlayletShopOrderService.getAfterSaleStat(mcnId), SohuShopOrderStatModel.class);
    }

    @Override
    public TableDataInfo<SohuShopOrderStatModel> queryShopOrderTop(SohuShopOrderMcnReqBo bo, PageQuery pageQuery) {
        SohuShopOrderMcnBo shopOrderMcnBo = BeanCopyUtils.copy(bo, SohuShopOrderMcnBo.class);
        TableDataInfo<SohuShopOrderStatVo> dataInfo = iPlayletShopOrderService.queryShopOrderTop(shopOrderMcnBo, pageQuery);
        return TableDataInfoUtils.copyInfo(dataInfo, SohuShopOrderStatModel.class);
    }

    @Override
    public void updateOrderStateSigned() {
        //处理订单
        iPlayletShopOrderService.updateOrderListWaitReceive();
    }

    @Override
    public Boolean mqDelayConfirm(SohuShopOrderModel shopOrder) {
        return this.iPlayletShopOrderBizService.mqDelayConfirm(shopOrder);
    }

    @Override
    public Boolean mqDelayConfirmV2(SohuShopOrderModel shopOrder) {
        return this.iPlayletShopOrderBizService.mqDelayConfirmV2(shopOrder);
    }

    @Override
    public void mqUpdateBatchMerchants(SohuOperateReqBo operateBo, Boolean isRefund) {
        this.iPlayletShopOrderBizService.mqUpdateBatchMerchants(BeanUtil.copyProperties(operateBo, SohuOperateBo.class), isRefund);
    }

    @Override
    public Boolean updateReplyStatus(String orderNo, Long orderInfoId, Integer isReply) {
        return iPlayletShopOrderBizService.updateReplyStatus(orderNo, orderInfoId, isReply);
    }

    @Override
    public TableDataInfo<SohuShopOrderVo> getStoreOrderList(String merId, PageQuery pageQuery) {
        return iPlayletShopOrderService.getStoreOrderList(merId, pageQuery);
    }

    @Override
    public Long getUserOrderByStore(Long merId, Long userId) {
        return iPlayletShopOrderService.getUserOrderByStore(merId, userId);
    }

    @Override
    public OrderDataVo orderData() {
        return iPlayletShopOrderService.orderData();
    }

    @Override
    public TableDataInfo<SohuShopOrderVo> getStoreOrderList(SohuStoreOrderBo bo, PageQuery pageQuery) {
        return iSohuShopOrderService.getStoreOrderList(bo, pageQuery);
    }

    @Override
    public Map<String, Object> getFreight(SohuShopOrderFreightBo bo) {
        return iSohuShopOrderService.getFreight(bo);
    }

    @Override
    public TableDataInfo<SohuOpenShopOrderVo> openList(SohuOpenShopOrderPageQueryBo bo, PageQuery pageQuery) {
        return iSohuShopOrderService.openList(bo, pageQuery);
    }

    @Override
    public SohuOpenShopOrderVo getByOrderNo(String orderNo, Long openClientId) {
        return iSohuShopOrderService.getByOrderNo(orderNo, openClientId);
    }

    @Override
    public KuaidiModel getLogisticsByExpNo(String expNo) {
        return iSohuShopOrderLogisticsService.getLogisticsByExpNo(expNo);
    }

    @Override
    public Boolean openSaveDeliverInfo(SohuOpenShopOrderDeliverBo bo) {
        return iSohuShopOrderService.openSaveDeliverInfo(bo);
    }

    @Override
    public TableDataInfo<SohuShopOrderVo> queryIndependentList(Long userId, Long pmSharePubId, PageQuery pageQuery) {
        return iSohuShopOrderService.queryIndependentList(userId, pmSharePubId, pageQuery);
    }

    @Override
    public Boolean updateOrderStatus(String orderNo, String sourceStaus, String targetStatus) {
        return iSohuShopOrderService.updateOrderStatus(orderNo, sourceStaus, targetStatus);
    }

    @Override
    public Boolean sendGoods(SohuSendOrderBo bo) {
        return iSohuShopOrderService.send(bo);
    }

    @Override
    public void merchantSalesExcute(String day) {
        iSohuShopOrderService.merchantSalesExcute(day);
    }

    @Override
    public Long inTransitOrder(List<Long> merId, List<String> states) {
        return iSohuShopOrderService.inTransitOrder(merId, states);
    }

    @Override
    public Boolean orderCheck(String masterOrderNo) {
        return this.iPlayletShopOrderBizService.orderCheck(masterOrderNo);
    }
}
