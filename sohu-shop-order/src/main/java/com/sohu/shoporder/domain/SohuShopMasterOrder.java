package com.sohu.shoporder.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 主订单对象 sohu_shop_master_order
 *
 * <AUTHOR>
 * @date 2023-06-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_shop_master_order")
public class SohuShopMasterOrder extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 站点id
     */
    private Long siteId;
    /**
     * 主订单号
     */
    private String orderNo;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 订单来源 1.微信小程序 2.app端  3.抖音小程序 4.支付宝小程序 5.PC网站
     */
    private Integer orderSource;
    /**
     * 支付来源-1、商城商品 2、商单-支付酬金 3、商户码扫码支付
     */
    private Integer paySource;
    /**
     * 用户邮箱
     */
    private String email;
    /**
     * 收货人姓名
     */
    private String receiverName;
    /**
     * 收货人电话
     */
    private String userPhone;
    /**
     * 详细地址
     */
    private String userDetailAddress;
    /**
     * 订单商品总数
     */
    private Integer totalNum;
    /**
     * 商品总价
     */
    private BigDecimal productTotalPrice;
    /**
     * 邮费
     */
    private BigDecimal totalPostage;
    /**
     * 订单总价
     */
    private BigDecimal totalPrice;
    /**
     * 总兑换积分
     */
    private Integer totalIntegral;
    /**
     * 支付邮费
     */
    private BigDecimal payPostage;
    /**
     * 实际支付金额
     */
    private BigDecimal payPrice;
    /**
     * 分销人分账金额-0.00
     */
    private BigDecimal distributorPrice;
    /**
     * 分销人id-分销时必传
     */
    private Long independentUserId;
    /**
     * 分销人的拉新人id-主动查询
     */
    private Long independentInviteUserId;
    /**
     * 拉新人id
     */
    private Long inviteUserId;
    /**
     * 第三方服务费-0.00
     */
    private BigDecimal chargePrice;
    /**
     * 拉新人分账金额-0.00
     */
    private BigDecimal invitePrice;
    /**
     * 分销人的拉新人金额-0.00
     */
    private BigDecimal distributorInvitePrice;
    /**
     * 代理人金额-0.00
     */
    private BigDecimal agencyAdminPrice;
    /**
     * 城市站长分账金额-0.00
     */
    private BigDecimal cityPrice;
    /**
     * 国家站长分账金额-0.00
     */
    private BigDecimal countryPrice;
    /**
     * 平台分账金额-0.00
     */
    private BigDecimal adminPrice;
    /**
     * 是否确认分账 0 没有 1确认
     */
    private Boolean independentSuccess;
    /**
     * 优惠券id
     */
    private Long couponId;
    /**
     * 优惠券金额
     */
    private BigDecimal couponPrice;
    /**
     * 支付状态
     */
    private Boolean paid;
    /**
     * 支付时间
     */
    private Date payTime;
    /**
     * 支付方式  wechat-jsapi-微信小程序支付、wechat-app-微信app支付、wechat-h5-微信h5支付、wechat-native-微信扫码支付、tiktok-抖音小程序支付、ali-pay-支付宝小程序支付、integral-积分支付、balance-余额支付、offline-pay-线下支付
     */
    private String payType;
    /**
     * 支付渠道：pc,mobile
     */
    private String payChannel;
    /**
     * 用户备注
     */
    private String mark;
    /**
     * 是否取消
     */
    private Boolean isCancel;
    /**
     * 主支付单号
     */
    private String payMasterOrderNo;
    /**
     * 第三方支付单号,32个字符内
     */
    private String outTradeNo;
    /**
     * 第三方延时分账单号,32个字符内
     */
    private String delayTradeNo;
    /**
     * 第三方交易流水号(唯一)
     */
    private String transactionId;
    /**
     * 支付重定向地址-国外支付用
     */
    private String redirect;

    /**
     * 用户性别
     */
    private Integer sex;

    /**
     * 是否是分销单-0不是 1是
     */
    private Boolean isIndependent;

    /**
     * 订单类型 1 实物商品订单 2 虚拟商品订单
     */
    private Integer orderType;

}
