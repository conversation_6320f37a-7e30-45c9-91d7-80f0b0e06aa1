<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sohu</groupId>
        <artifactId>sohu-dependency</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sohu-shop-order</artifactId>

    <description>
        sohu-shop-order商城订单模块
    </description>

    <dependencies>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-sentinel</artifactId>
        </dependency>

        <!-- sohu Common Log -->
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-dict</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-seata</artifactId>
        </dependency>

        <!-- sohu Api System -->
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-shop-order</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-resource</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-admin</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-shop-goods</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-pay</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-app</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-stream-rocketmq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wangcaio2o.ipossa</groupId>
            <artifactId>ym-sdk-java</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-middle</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-pm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zwztf.sdk</groupId>
            <artifactId>fzs-virtualgoods</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.zwztf.sdk</groupId>
            <artifactId>zxhuixuan-supply</artifactId>
            <version>1.0.1</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
