FROM anapsix/alpine-java:8_server-jre_unlimited

MAINTAINER sohu

RUN mkdir -p /sohu/shop-order/logs \
    /sohu/shop-order/temp \
    /sohu/skywalking/agent

WORKDIR /sohu/shop-order

ENV SERVER_PORT=9221

EXPOSE ${SERVER_PORT}

ADD ./target/sohu-shop-order.jar ./app.jar

ENTRYPOINT ["java", \
            "-Djava.security.egd=file:/dev/./urandom", \
            "-Dserver.port=${SERVER_PORT}", \
#            "-Dskywalking.agent.service_name=sohu-system", \
#            "-javaagent:/sohu/skywalking/agent/skywalking-agent.jar", \
            "-jar", "app.jar"]
