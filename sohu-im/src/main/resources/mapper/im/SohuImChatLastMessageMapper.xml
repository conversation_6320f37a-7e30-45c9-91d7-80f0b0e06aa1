<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.im.mapper.SohuImChatLastMessageMapper">

    <resultMap type="com.sohu.im.domain.SohuImChatLastMessage" id="SohuImChatLastMessageResult">
        <result property="id" column="id"/>
        <result property="content" column="content"/>
        <result property="senderId" column="sender_id"/>
        <result property="receiverId" column="receiver_id"/>
        <result property="sessionType" column="session_type"/>
        <result property="messageType" column="message_type"/>
        <result property="shareType" column="share_type"/>
        <result property="shareParam" column="share_param"/>
        <result property="readType" column="read_type"/>
        <result property="shareId" column="share_id"/>
        <result property="chatId" column="chat_id"/>
        <result property="msgId" column="msg_id"/>
        <result property="duration" column="duration"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileName" column="file_name"/>
        <result property="localId" column="local_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="atIds" column="at_ids"/>
        <result property="commandType" column="command_type"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="msgSource" column="msg_source"/>
    </resultMap>

    <delete id="deleteHistory">
        DELETE
        FROM sohu_im_chat_last_message
        WHERE session_type = #{sessionType}
          AND sender_id = #{userId}
          AND receiver_id = #{receiverId}
    </delete>


</mapper>
