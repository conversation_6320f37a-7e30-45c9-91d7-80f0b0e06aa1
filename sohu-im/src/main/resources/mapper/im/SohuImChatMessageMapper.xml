<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.im.mapper.SohuImChatMessageMapper">

    <resultMap type="com.sohu.im.domain.SohuImChatMessage" id="SohuImChatMessageResult">
        <result property="id" column="id"/>
        <result property="content" column="content"/>
        <result property="senderId" column="sender_id"/>
        <result property="receiverId" column="receiver_id"/>
        <result property="sessionType" column="session_type"/>
        <result property="messageType" column="message_type"/>
        <result property="shareType" column="share_type"/>
        <result property="shareParam" column="share_param"/>
        <result property="readType" column="read_type"/>
        <result property="hidden" column="hidden"/>
        <result property="shareId" column="share_id"/>
        <result property="chatId" column="chat_id"/>
        <result property="msgId" column="msg_id"/>
        <result property="duration" column="duration"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileName" column="file_name"/>
        <result property="fileDelete" column="file_delete"/>
        <result property="localId" column="local_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="mediaType" column="media_type"/>
        <result property="atIds" column="at_ids"/>
        <result property="commandType" column="command_type"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="msgSource" column="msg_source"/>
        <result property="nanoTime" column="nano_time"/>
    </resultMap>

    <delete id="deleteHistory">
        DELETE
        FROM sohu_im_chat_message
        WHERE session_type = #{sessionType}
          AND sender_id = #{userId}
          AND receiver_id = #{receiverId}
    </delete>

    <select id="queryPage" resultType="com.sohu.im.api.vo.SohuImChatMessageVo">
        SELECT * FROM sohu_im_chat_message WHERE ( (session_type ='single' or session_type='merchant') and (sender_id
        =#{loginId} or
        receiver_id=#{loginId})
        <if test="groupIds != null and groupIds.size > 0">
            OR (session_type != 'single' and receiver_id in
            <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
            )
        </if>
        )
        <if test="bo.beginDate!=null">
            AND create_time &gt;= #{bo.beginDate}
        </if>
        <if test="bo.endDate!=null">
            AND create_time &lt;= #{bo.endDate}
        </if>
    </select>

    <select id="countGroup" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM sohu_im_chat_message
        WHERE session_type = #{sessionType}
          AND receiver_id = #{receiverId}
    </select>

    <select id="queryPageListOfManage" resultType="com.sohu.im.api.vo.SohuImChatMessageVo">
        SELECT m.*
        ,u1.nick_name AS senderNickname
        ,u1.user_name AS senderUserName
        <if test="bo.sessionType =='single'">
            ,u2.nick_name AS receiverName
            ,u2.user_name AS receiverUserName
        </if>
        <if test="bo.sessionType =='group'">
            ,ig.`name` AS receiverName
            ,m.receiver_id AS receiverUserName
        </if>
        FROM sohu_im_chat_message m
        LEFT JOIN sys_user u1 ON u1.user_id=m.sender_id
        <if test="bo.sessionType =='single'">
            LEFT JOIN sys_user u2 ON u2.user_id=m.receiver_id
        </if>
        <if test="bo.sessionType =='group'">
            LEFT JOIN sohu_im_group ig ON ig.id=m.receiver_id
        </if>
        <where>
            <if test="bo.sessionType != null and bo.sessionType != ''">
                m.session_type = #{bo.sessionType}
            </if>
            <if test="bo.senderId != null">
                AND m.sender_id like concat('%',#{bo.senderId},'%')
            </if>
            <if test="bo.messageType != null and bo.messageType != ''">
                AND m.message_type = #{bo.messageType}
            </if>
            <if test="bo.messageType == null or bo.messageType == ''">
                AND m.message_type IN('text','photo','video','voice','share','file')
            </if>
            <if test="bo.receiverId != null">
                AND m.receiver_id like concat('%',#{bo.receiverId},'%')
            </if>
            <if test="bo.senderNickname != null and bo.senderNickname != ''">
                AND u1.nick_name like concat('%',#{bo.senderNickname},'%')
            </if>
            <if test="bo.senderUserName != null and bo.senderUserName != ''">
                AND u1.user_name like concat('%',#{bo.senderUserName},'%')
            </if>
            <if test="bo.content != null and bo.content != ''">
                AND m.content like concat('%',#{bo.content},'%')
            </if>
            <if test="bo.receiverName != null and bo.receiverName != ''">
                <if test="bo.sessionType =='single'">
                    AND u2.nick_name like concat('%',#{bo.receiverName},'%')
                </if>
                <if test="bo.sessionType =='group'">
                    AND ig.`name` like concat('%',#{bo.receiverName},'%')
                </if>
            </if>
            <if test="bo.startTime != null">
                and m.create_time <![CDATA[ >= ]]> #{bo.startTime}
            </if>
            <if test="bo.endTime != null">
                and m.create_time <![CDATA[ <= ]]> #{bo.endTime}
            </if>
            <if test="bo.currentUserId != null">
                AND (m.sender_id = #{bo.currentUserId} OR m.receiver_id = #{bo.currentUserId})
            </if>
        </where>
        ORDER BY m.create_time DESC

    </select>


</mapper>
