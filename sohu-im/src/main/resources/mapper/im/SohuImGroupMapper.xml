<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.im.mapper.SohuImGroupMapper">
    <resultMap type="com.sohu.im.domain.SohuImGroup" id="SohuImGroupResult">
        <result property="id" column="id"/>
        <result property="description" column="description"/>
        <result property="name" column="name"/>
        <result property="userId" column="user_id"/>
        <result property="logo" column="logo"/>
        <result property="groupGreet" column="group_greet"/>
        <result property="groupNotice" column="group_notice"/>
        <result property="needConfirm" column="need_confirm"/>
        <result property="groupUserNum" column="group_user_num"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="forbid" column="forbid"/>
        <result property="maxGroupUserNum" column="max_group_user_num"/>
        <result property="maxGroupAdminNum" column="max_group_admin_num"/>
        <result property="groupType" column="group_type"/>
        <result property="authorize" column="authorize"/>
        <result property="authorizeExt" column="authorize_ext"/>
        <result property="groupExt" column="group_ext"/>
        <result property="unq" column="unq"/>
        <result property="bindUser" column="bind_user"/>
        <result property="addFriend" column="add_friend"/>
        <result property="forbidTime" column="forbid_time"/>
        <result property="groupWord" column="group_word"/>
        <result property="state" column="state"/>
    </resultMap>

    <select id="queryMyImGroup" resultType="com.sohu.im.api.vo.SohuImGroupVo">
        SELECT g.* FROM sohu_im_group g
                            INNER JOIN sohu_im_group_user gu ON gu.group_id=g.id
        WHERE gu.user_id = #{bo.userId}
        <if test="bo.groupName!=null and bo.groupName!=''">
            AND g.`name` like concat('%',#{bo.groupName},'%')
        </if>

    </select>
</mapper>
