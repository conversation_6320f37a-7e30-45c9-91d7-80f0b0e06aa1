##http \u914D\u7F6E
http.port = 8050
http.page = classpath:page

http.404 = /showcase/404
http.500 = /showcase/500
# \u9875\u9762\u6587\u4EF6\u7F13\u5B58\u65F6\u95F4\uFF0C\u5F00\u53D1\u65F6\u8BBE\u7F6E\u62100\uFF0C\u751F\u4EA7\u73AF\u5883\u53EF\u4EE5\u8BBE\u7F6E\u62101\u5C0F\u65F6(3600)\uFF0C10\u5206\u949F(600)\u7B49\uFF0C\u5355\u4F4D\uFF1A\u79D2
http.maxLiveTimeOfStaticRes=0

start.http=1
ws.use.ssl=1


# file of keystore\uFF0C\u5982\u679C\u4EE5classpath:\u5F00\u5934\uFF0C\u5219\u4ECEclasspath\u4E2D\u67E5\u627E\uFF0C\u5426\u5219\u4ECE\u6587\u4EF6\u8DEF\u5F84\u4E2D\u67E5\u627E
#	--\u4F8B1\uFF1A classpath:config/ssl/keystore.jks
#	--\u4F8B2\uFF1A /ssl/ca/keystore.jks
ssl.keystore=classpath:config/ssl/server.jks
# file of truststore\uFF0C\u5982\u679C\u4EE5classpath:\u5F00\u5934\uFF0C\u5219\u4ECEclasspath\u4E2D\u67E5\u627E\uFF0C\u5426\u5219\u4ECE\u6587\u4EF6\u8DEF\u5F84\u4E2D\u67E5\u627E
#	--\u4F8B1\uFF1A classpath:config/ssl/keystore.jks
#	--\u4F8B2\uFF1A /ssl/ca/keystore.jks
ssl.truststore=classpath:config/ssl/server.jks
# password for keystore
ssl.pwd=Gf6)qyD#!h6t2brF


#type xxx.crt xxx.key > xxx.pem
#openssl pkcs12 -export -in scs1695877142692_chat.sohuglobal.com_server.pem -out api.sohuglobal.com.pkcs12
#keytool -importkeystore -srckeystore api.sohuglobal.com.pkcs12 -srcstoretype PKCS12 -destkeystore nginx.jks

# openssl pkcs12 -export -in www_sohuglobal.com_server.pem -out www.sohuglobal.com.pkcs12

