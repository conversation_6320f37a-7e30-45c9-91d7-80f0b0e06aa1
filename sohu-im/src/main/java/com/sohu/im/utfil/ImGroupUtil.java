package com.sohu.im.utfil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.ApplyStateEnum;
import com.sohu.common.mybatis.db.CacheMgr;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.im.api.constant.ImCacheConstants;
import com.sohu.im.api.enums.ImGroupPermissionType;
import com.sohu.im.api.enums.ImGroupTaskRole;
import com.sohu.im.api.enums.ImGroupType;
import com.sohu.im.api.enums.ImSessionTypeEnum;
import com.sohu.im.api.vo.*;
import com.sohu.im.domain.SohuImGroupForbidTime;
import com.sohu.im.domain.SohuImGroupUser;
import com.sohu.im.mapper.SohuImGroupForbidTimeMapper;
import com.sohu.im.mapper.SohuImGroupMapper;
import com.sohu.im.mapper.SohuImGroupUserMapper;
import com.sohu.im.server.WebsocketStarter;
import com.sohu.middle.api.service.RemoteMiddleFriendService;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.tio.core.Tio;
import org.tio.server.ServerTioConfig;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ImGroupUtil {

    private final SohuImGroupMapper sohuImGroupMapper;
    private final SohuImGroupUserMapper sohuImGroupUserMapper;
    private final SohuImGroupForbidTimeMapper sohuImGroupForbidTimeMapper;

    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteMiddleFriendService remoteMiddleFriendService;

    public void buildGroupUser(List<SohuImGroupUserVo> userVos, Long loginId, Long groupId) {
        Set<Long> userIds = new HashSet<>();
        SohuImGroupUserVo currentGroupUser = null;

        for (SohuImGroupUserVo record : userVos) {
            userIds.add(record.getUserId());
            if (Objects.equals(record.getUserId(), loginId)) {
                currentGroupUser = record;
            }
        }
        Map<Long, LoginUser> userMap = remoteUserService.selectMap(userIds);

        SohuImGroupVo imGroupVo = sohuImGroupMapper.selectVoById(groupId);

        for (SohuImGroupUserVo record : userVos) {
            LoginUser user = userMap.get(record.getUserId());
            if (Objects.isNull(user)) {
                sohuImGroupUserMapper.deleteById(record);
                continue;
            }

            record.setUserName(user.getUsername());
            record.setNickName(user.getNickname());
            // 用户在群的昵称兜底为 用户昵称
            record.setGroupUserNickName(StrUtil.isBlankIfStr(record.getGroupUserNickName()) ? record.getNickName() : record.getGroupUserNickName());
            record.setUserAvatar(StrUtil.isBlankIfStr(user.getAvatar()) ? Constants.DEFAULT_AVATAR : user.getAvatar());
            buildGroupUserSpecialRole(imGroupVo, record);
            // 当前是否禁言状态返回
            record.setForbid(handleForbid(imGroupVo, record));
            if (StrUtil.equalsAnyIgnoreCase(imGroupVo.getGroupType(), ImGroupType.groupTask.name(), ImGroupType.groupTaskCustom.name())) {
                record.setUserAvatar(Constants.DEFAULT_AVATAR);
            }
        }
        if (StrUtil.equalsAnyIgnoreCase(imGroupVo.getGroupType(), ImGroupType.group.name())) {
            // 构建备注
            buildAlias(userVos, loginId);
        }
        // 群成员需要打码的群
        /*if (Objects.nonNull(currentGroupUser) && ImGroupType.isSensitiveGroup(imGroupVo.getGroupType())) {
            if (StrUtil.equalsAnyIgnoreCase(imGroupVo.getGroupType(), ImGroupType.groupForm.name(), ImGroupType.groupFormCustom.name())) {
                for (SohuImGroupUserVo userVo : userVos) {
                    if (Objects.equals(userVo.getUserId(), currentGroupUser.getUserId())) {
                        continue;
                    }
                    JSONObject parseObj = JSONUtil.parseObj(userVo.getExt());
                    if (StrUtil.equalsAnyIgnoreCase(currentGroupUser.getPermissionType(), ImGroupPermissionType.group_leader.name())) {
                        if (StrUtil.equalsAnyIgnoreCase(userVo.getPermissionType(), ImGroupPermissionType.group_user.name())
                                && StrUtil.equalsAnyIgnoreCase(parseObj.getStr("groupTaskRole"), ImGroupTaskRole.taskRece.getCode())) {
                            userVo.setUserName(StringUtils.sensitive(1, userVo.getUserName()));
                            userVo.setNickName(StringUtils.sensitive(1, userVo.getNickName()));
                            userVo.setGroupUserNickName(StringUtils.sensitive(1, userVo.getGroupUserNickName()));
                        }
                    } else if (StrUtil.equalsAnyIgnoreCase(currentGroupUser.getPermissionType(), ImGroupPermissionType.group_user.name())) {
                        userVo.setUserName(StringUtils.sensitive(1, userVo.getUserName()));
                        userVo.setNickName(StringUtils.sensitive(1, userVo.getNickName()));
                        userVo.setGroupUserNickName(StringUtils.sensitive(1, userVo.getGroupUserNickName()));
                    }
                }
            }
        }*/
    }

    /**
     * 处理禁言
     *
     * @param sohuImGroup
     * @param record
     * @return 返回禁言结果
     */
    private Boolean handleForbid(SohuImGroupVo sohuImGroup, SohuImGroupUserVo record) {
        if (StrUtil.equals(record.getPermissionType(), ImGroupPermissionType.group_leader.name())) {
            // 群主永远不会禁言
            return Boolean.FALSE;
        }
        // 管理员禁言只受限于指定人员禁言
        if (StrUtil.equals(record.getPermissionType(), ImGroupPermissionType.group_admin.name())) {
            // 管理员返回用户是否被指定禁言
            return BooleanUtil.isTrue(record.isForbid());
        }
        if (BooleanUtil.isTrue(sohuImGroup.getForbid())) {
            // 开启了全员禁言 返回被禁言
            return Boolean.TRUE;
        }
        if (BooleanUtil.isTrue(sohuImGroup.getForbidTime())) {
            // 开启了时间段禁言,查询时间段
            List<SohuImGroupForbidTime> timeList = sohuImGroupForbidTimeMapper.selectList(Wrappers.<SohuImGroupForbidTime>lambdaQuery().eq(SohuImGroupForbidTime::getGroupId, sohuImGroup.getId()));
            if (CollectionUtil.isNotEmpty(timeList)) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
                LocalTime now = LocalTime.now();
                for (SohuImGroupForbidTime time : timeList) {
                    LocalTime startTime = LocalTime.parse(time.getStartTime() + ":00", formatter);
                    LocalTime endTime = LocalTime.parse(time.getEndTime() + ":59", formatter);
                    if (now.isAfter(startTime) && now.isBefore(endTime)) {
                        // 如果当前时间在禁言开始之后,禁言结束之前则需要进行禁言操作
                        return Boolean.TRUE;
                    }
                }
            }
        }
        // 直接返回用户是否被指定禁言
        return BooleanUtil.isTrue(record.isForbid());
    }

    /**
     * 设置备注
     *
     * @param list
     */
    private void buildAlias(List<SohuImGroupUserVo> list, Long loginId) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        Set<Long> userIds = new HashSet<>();
        userIds.add(loginId);
        for (SohuImGroupUserVo groupUserVo : list) {
            userIds.add(groupUserVo.getUserId());
        }
        Map<Long, String> friendMap = remoteMiddleFriendService.friendAliasMap(loginId, userIds, ApplyStateEnum.pass.name());
        for (SohuImGroupUserVo record : list) {
            String alias = friendMap.get(record.getUserId());
            //record.setUserName(StrUtil.isNotBlank(alias) ? alias : record.getUserName());
            record.setAlias(alias);
        }
    }

    /**
     * 处理特殊身份
     *
     * @param record
     */
    private void buildGroupUserSpecialRole(SohuImGroupVo imGroupVo, SohuImGroupUserVo record) {
        ImGroupTaskRole groupTaskRole = getGroupTaskRole(record.getUserId(), imGroupVo);
        if (groupTaskRole == null) {
            return;
        }
        record.setSpecialRole(groupTaskRole.getCode());
        if (StrUtil.equalsAnyIgnoreCase(imGroupVo.getGroupType(), ImGroupType.groupTask.name(), ImGroupType.groupTaskCustom.name())) {
            record.setUserName(groupTaskRole.getDesc());
            record.setNickName(groupTaskRole.getDesc());
            record.setGroupUserNickName(groupTaskRole.getDesc());
            record.setUserAvatar(groupTaskRole.getAvatar());
        }
    }

    public static SohuImGroupTaskExtVo convertTask(String groupExt) {
        if (StrUtil.isBlankIfStr(groupExt)) {
            return null;
        }
        return JSONUtil.toBean(groupExt, SohuImGroupTaskExtVo.class);
    }

    public static SohuImGroupFormExtVo convertForm(String groupExt) {
        if (StrUtil.isBlankIfStr(groupExt)) {
            return null;
        }
        return JSONUtil.toBean(groupExt, SohuImGroupFormExtVo.class);
    }

    public static SohuImGroupSubFormExtVo convertSubForm(String groupExt) {
        if (StrUtil.isBlankIfStr(groupExt)) {
            return null;
        }
        return JSONUtil.toBean(groupExt, SohuImGroupSubFormExtVo.class);
    }

    /**
     * 基于群id和用户id返回用户群角色
     *
     * @param groupId 群ID
     * @param userId  用户ID
     * @return {@link String}
     */
    public String exchangeUser(Long groupId, Long userId) {
        SohuImGroupUser groupUser = sohuImGroupUserMapper.selectOne(Wrappers.<SohuImGroupUser>lambdaQuery().eq(SohuImGroupUser::getGroupId, groupId).eq(SohuImGroupUser::getUserId, userId));
        if (StrUtil.equalsAnyIgnoreCase(groupUser.getPermissionType(), ImGroupPermissionType.group_leader.name())) {
            return "群主";
        } else if (StrUtil.equalsAnyIgnoreCase(groupUser.getPermissionType(), ImGroupPermissionType.group_admin.name())) {
            return "管理员";
        } else {
            return "成员";
        }
    }

    /**
     * 用户绑定到群组
     *
     * @param userId  进群用户ID
     * @param groupId 群id
     */
    public void bindGroup(Long userId, Long groupId) {
        ServerTioConfig serverTioConfig = WebsocketStarter.serverTioConfig;
        Tio.bindGroup(serverTioConfig, String.valueOf(userId), String.valueOf(groupId));
        log.info("bindGroup：userId:{},groupId:{}", userId, groupId);
    }

    /**
     * 获取群任务特殊角色
     *
     * @param loginId   登录人id
     * @param imGroupVo 群信息
     * @return {@link ImGroupTaskRole}
     */
    public static ImGroupTaskRole getGroupTaskRole(Long loginId, SohuImGroupVo imGroupVo) {
        String specialRole = getSpecialRole(loginId, imGroupVo);
        if (StrUtil.isBlankIfStr(specialRole)) {
            return null;
        }
        if (!ImGroupTaskRole.isExist(specialRole)) {
            return null;
        }
        return ImGroupTaskRole.valueOf(specialRole);
    }

    /**
     * 获取特殊角色身份
     *
     * @param loginId   登录人id
     * @param imGroupVo 群信息
     * @return {@link String}
     */
    public static String getSpecialRole(Long loginId, SohuImGroupVo imGroupVo) {
        if (loginId == null || Objects.isNull(imGroupVo) || StrUtil.isBlankIfStr(imGroupVo.getGroupExt())) {
            return null;
        }
        String groupExt = imGroupVo.getGroupExt();
        if (StrUtil.equalsAnyIgnoreCase(imGroupVo.getGroupType(), ImGroupType.groupTask.name())) {
            SohuImGroupTaskExtVo extVo = convertTask(groupExt);
            if (Objects.nonNull(extVo)) {
                if (Objects.equals(extVo.getTaskPublishUserId(), loginId)) {
                    return ImGroupTaskRole.taskPublish.name();
                } else if (Objects.equals(extVo.getTaskReceUserId(), loginId)) {
                    return ImGroupTaskRole.taskRece.name();
                }
            }
        } else if (StrUtil.equalsAnyIgnoreCase(imGroupVo.getGroupType(), ImGroupType.groupForm.name())) {
            SohuImGroupFormExtVo extVo = convertForm(groupExt);
            if (Objects.nonNull(extVo)) {
                if (Objects.equals(extVo.getTaskPublishUserId(), loginId)) {
                    return ImGroupTaskRole.taskPublish.name();
                } else if (extVo.getTaskReceUserIds().contains(loginId)) {
                    return ImGroupTaskRole.taskRece.name();
                }
            }
        } else if (StrUtil.equalsAnyIgnoreCase(imGroupVo.getGroupType(), ImGroupType.groupFormCustom.name())) {
            SohuImGroupSubFormExtVo extVo = convertSubForm(groupExt);
            if (Objects.nonNull(extVo)) {
                if (Objects.equals(extVo.getTaskPublishUserId(), loginId)) {
                    return ImGroupTaskRole.taskPublish.name();
                } else if (Objects.equals(loginId, extVo.getTaskReceUserId())) {
                    return ImGroupTaskRole.taskRece.name();
                }
            }
        }
        return null;
    }

    /**
     * 是否是群聊
     *
     * @param sessionType {@link ImSessionTypeEnum}
     * @return {@link Boolean}
     */
    public static Boolean isGroup(String sessionType) {
        return StrUtil.equalsAnyIgnoreCase(sessionType,
                ImSessionTypeEnum.group.name(),
                ImSessionTypeEnum.groupTask.name(),
                ImSessionTypeEnum.groupTaskCustom.name(),
                ImSessionTypeEnum.groupForm.name(),
                ImSessionTypeEnum.groupFormCustom.name(),
                ImSessionTypeEnum.groupFromGeneral.name()
        );
    }

    /**
     * 解绑socket群组
     *
     * @param userId 用户ID
     * @param groupId 群ID
     * @return {@link Boolean}
     */
    public Boolean unBindGroup(Long userId, Long groupId) {
        ServerTioConfig serverTioConfig = WebsocketStarter.serverTioConfig;
        Tio.unbindGroup(serverTioConfig, String.valueOf(userId), String.valueOf(groupId));
        return Boolean.TRUE;
    }
}
