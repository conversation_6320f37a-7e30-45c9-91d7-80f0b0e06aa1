package com.sohu.im.utfil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.ApplyStateEnum;
import com.sohu.common.core.utils.SpringUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.im.api.bo.ImChatRequestBo;
import com.sohu.im.api.bo.ImShareBo;
import com.sohu.im.api.enums.*;
import com.sohu.im.api.vo.ImChatResponseVo;
import com.sohu.im.api.vo.SohuImChatMessageVo;
import com.sohu.im.api.vo.SohuImGroupUserVo;
import com.sohu.im.api.vo.SohuImGroupVo;
import com.sohu.im.domain.SohuImChatLastMessage;
import com.sohu.im.domain.SohuImChatMessage;
import com.sohu.im.service.ISohuImGroupService;
import com.sohu.im.service.ISohuImGroupUserService;
import com.sohu.middle.api.service.RemoteMiddleFriendService;
import com.sohu.system.api.RemoteUserService;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class ImSocketResponseUtil {

    public static final int limitNum = 3;
    public transient static final String sendMsgAgainError = "你已经发送%s条消息，请耐心等待回复~";
    public transient static final String firstSendMsg = "由于对方未关注你，在收到回复前你只能发送一条消息；";
    public transient static final String firstReceMsg = "由于你未关注对方，TA 在收到你的回复前只能发送一条消息；";

    public transient static final String errKeyword = "拒绝发送违规信息，友好交流";
    public transient static final String GROUP_ALL_FORBID_OPEN = "全员禁言中";
    public transient static final String GROUP_USER_FORBID = "你已被禁言";
    public transient static final String GROUP_FORBID_TIME_OPEN = "当前时间段内禁言中";

    /**
     * 构建VO
     *
     * @param bo     socket消息入参
     * @param sender 发送者
     * @return {@link ImChatResponseVo}
     */
    public static ImChatResponseVo buildResponseVo(ImChatRequestBo bo, LoginUser sender) {
        // 接收者(群组或用户)唯一标识
        Long receiverId = bo.getReceiverId();
        String sessionType = bo.getSessionType();
        ImChatResponseVo responseVo = new ImChatResponseVo();
        responseVo.setNanoTime(bo.getNanoTime());
        responseVo.setSessionType(sessionType);
        // 构建发送者信息
        ImChatResponseVo.SenderDTO senderDTO = new ImChatResponseVo.SenderDTO();
        senderDTO.setId(sender.getUserId());
        senderDTO.setName(StrUtil.isNotBlank(sender.getNickname()) ? sender.getNickname() : sender.getUsername());
        senderDTO.setGroupUserNickName(sender.getGroupUserNickName());
        senderDTO.setAvatar(sender.getAvatar());
        if (StrUtil.isBlankIfStr(senderDTO.getAvatar())) {
            senderDTO.setAvatar(Constants.DEFAULT_USER_AVATAR);
        }
        String err = null;
        if (StrUtil.equalsAnyIgnoreCase(sessionType, ImSessionTypeEnum.single.getCode(), ImSessionTypeEnum.merchant.getCode())) {
            // 私聊
            // 构建接收者信息
            RemoteUserService remoteUserService = SpringUtils.getBean(RemoteUserService.class);
            LoginUser receiver = remoteUserService.queryById(receiverId);
            ImChatResponseVo.ReceiverDTO receiverDTO = new ImChatResponseVo.ReceiverDTO();
            receiverDTO.setId(receiver.getUserId());
            receiverDTO.setName(StrUtil.isNotBlank(receiver.getNickname()) ? receiver.getNickname() : receiver.getUsername());
            receiverDTO.setAvatar(StrUtil.isNotBlank(receiver.getAvatar()) ? receiver.getAvatar() : Constants.DEFAULT_USER_AVATAR);
            if (StrUtil.isBlankIfStr(receiverDTO.getAvatar())) {
                receiverDTO.setAvatar(Constants.DEFAULT_USER_AVATAR);
            }
            responseVo.setReceiver(receiverDTO);
        } else {
            // 群聊
            ISohuImGroupService imGroupService = SpringUtils.getBean(ISohuImGroupService.class);
            SohuImGroupVo groupVo = imGroupService.get(receiverId);
            ISohuImGroupUserService groupUserService = SpringUtils.getBean(ISohuImGroupUserService.class);
            SohuImGroupUserVo groupUser = groupUserService.selectOne(receiverId, sender.getUserId());
            ImChatResponseVo.ReceiverDTO receiverDTO = new ImChatResponseVo.ReceiverDTO();

            if (Objects.nonNull(groupUser)) {
                // 拓展字段
                String ext = groupUser.getExt();
                if (StrUtil.isNotBlank(ext)) {
                    JSONObject parsedObj = JSONUtil.parseObj(ext);
                    String groupTaskRole = parsedObj.getStr("groupTaskRole");
                    if (StrUtil.isNotBlank(groupTaskRole) && ImGroupType.isSensitiveGroup(sessionType)) {
                        if (StrUtil.equalsAnyIgnoreCase(groupTaskRole, ImGroupTaskRole.taskPublish.name(), ImGroupTaskRole.taskRece.name())
                                && StrUtil.equalsAnyIgnoreCase(groupVo.getGroupType(), ImGroupType.groupTask.name(), ImGroupType.groupTaskCustom.name())) {
                            senderDTO.setName(StrUtil.equalsAnyIgnoreCase(groupTaskRole, ImGroupTaskRole.taskPublish.name()) ? ImGroupTaskRole.taskPublish.getDesc() : ImGroupTaskRole.taskRece.getDesc());
                            senderDTO.setAvatar(StrUtil.equalsAnyIgnoreCase(groupTaskRole, ImGroupTaskRole.taskPublish.name()) ? ImGroupTaskRole.taskPublish.getAvatar() : ImGroupTaskRole.taskRece.getAvatar());
                        }
                        senderDTO.setSpecialRole(groupTaskRole);
                    }
                } else {
                    if (Objects.nonNull(groupVo)) {
                        ImGroupTaskRole groupTaskRole = ImGroupUtil.getGroupTaskRole(sender.getUserId(), groupVo);
                        if (groupTaskRole != null) {
                            if (StrUtil.equalsAnyIgnoreCase(groupVo.getGroupType(), ImGroupType.groupTask.name(), ImGroupType.groupTaskCustom.name())) {
                                senderDTO.setName(groupTaskRole.getDesc());
                                senderDTO.setAvatar(groupTaskRole.getAvatar());
                            }
                            senderDTO.setSpecialRole(groupTaskRole.getCode());
                        }
                    }
                }

                // 设置发送者的群用户身份
                senderDTO.setRole(groupUser.getPermissionType());

                if (StrUtil.equalsAnyIgnoreCase(ImGroupPermissionType.group_user.name(), senderDTO.getRole())) {
                    if (Objects.nonNull(groupVo) && BooleanUtil.isTrue(groupVo.getForbid())) {
                        // 全员禁言
                        err = ImSocketResponseUtil.GROUP_ALL_FORBID_OPEN;
                    } else if (groupUser.isForbid()) {
                        senderDTO.setForbid(true);
                        // 发送者被禁言
                        err = "你已被禁言";
                    }
                }

            }
            receiverDTO.setId(receiverId);
            receiverDTO.setName(Objects.nonNull(groupVo) ? groupVo.getName() : null);
            receiverDTO.setAvatar(Objects.nonNull(groupVo) ? groupVo.getLogo() : null);
            responseVo.setReceiver(receiverDTO);
        }
        ImChatResponseVo.BodyDTO bodyDTO = new ImChatResponseVo.BodyDTO();
        bodyDTO.setContent(bo.getContent());
        if (bo.getDuration() != null) {
            bodyDTO.setDuration(bo.getDuration());
        } else {
            if (StrUtil.equalsAnyIgnoreCase(bo.getMessageType(), ImMessageTypeEnum.Voice.getCode())) {
                if (bo.getDuration() == null || bo.getDuration() == 0) {
                    // 解析语音时长
                    bodyDTO.setDuration((int) MP3DurationCalculator.getMP3DurationInSeconds(bo.getContent()));
                    bo.setDuration(bodyDTO.getDuration());
                } else {
                    bodyDTO.setDuration(bo.getDuration());
                }
            }
        }
        bodyDTO.setErr(err);
        responseVo.setExt(bo.getExt());
        // 消息体
        responseVo.setBody(bodyDTO);
        responseVo.setSender(senderDTO);
        responseVo.setCommandType(bo.getCommandType());
        responseVo.setChatId((bo.getChatId() != null && bo.getChatId() > 0L) ? bo.getChatId() : System.nanoTime());
        responseVo.setCreateTime(System.currentTimeMillis());
        if (StrUtil.isBlankIfStr(bo.getLocalId())) {
            bo.setLocalId(RandomUtil.randomString(32));
        }
        responseVo.setLocalId(bo.getLocalId());
        responseVo.setAtIds(bo.getAtIds());
        String commandType = bo.getCommandType();
        if (StrUtil.isNotBlank(commandType) && StrUtil.equalsAnyIgnoreCase(commandType, ImCommandTypeEnum.recall.getCode())) {
            responseVo.setIsRecall(true);
        }
        responseVo.setMessageType(bo.getMessageType());
        responseVo.setMsgSource(bo.getMsgSource());
        responseVo.setHidden(bo.getHidden());

        // 构建mediaCall,仅 messageType 为 videoCall 或 voiceCall 时，才填充该对象
        buildMediaCall(bo.getMediaCall(), sender.getUserId(), responseVo);
        // 构建分享信息
        buildShareResponse(bo, responseVo);
        // 构建文件信息
        buildFile(bo, responseVo);
        return responseVo;
    }

    public static void buildMediaCall(ImChatRequestBo.MediaCall mediaCall, Long senderId, ImChatResponseVo responseVo) {
        if (mediaCall != null) {
            RemoteMiddleFriendService remoteMiddleFriendService = SpringUtils.getBean(RemoteMiddleFriendService.class);
            Collection<Long> userIds = new ArrayList<>();
            if (CollUtil.isNotEmpty(mediaCall.getMembers())) {
                userIds.addAll(mediaCall.getMembers());
            }
            if (CollUtil.isNotEmpty(mediaCall.getRealMembers())) {
                userIds.addAll(mediaCall.getRealMembers());
            }
            // 获取好友备注
            Map<Long, String> friendAliasMap = remoteMiddleFriendService.friendAliasMap(senderId, userIds, ApplyStateEnum.pass.name());
            ImChatResponseVo.MediaCall responseVoMediaCall = new ImChatResponseVo.MediaCall();
            responseVoMediaCall.setFront(mediaCall.isFront());
            responseVoMediaCall.setType(mediaCall.getType());
            responseVoMediaCall.setIceCandidate(mediaCall.getIceCandidate());
            responseVoMediaCall.setRoomId(mediaCall.getRoomId());
            responseVoMediaCall.setSessionDescription(mediaCall.getSessionDescription());
            responseVoMediaCall.setUserId(mediaCall.getUserId());
            responseVoMediaCall.setMembers(mediaCall.getMembers());
            responseVoMediaCall.setRealMembers(mediaCall.getRealMembers());
            responseVoMediaCall.setMemberList(buildMemberUser(mediaCall.getMembers(), friendAliasMap));
            responseVoMediaCall.setRealMemberList(buildMemberUser(mediaCall.getRealMembers(), friendAliasMap));
            responseVoMediaCall.setVideo(mediaCall.getVideo());
            responseVoMediaCall.setFromUserId(mediaCall.getFromUserId());
            responseVoMediaCall.setOriginUserId(mediaCall.getOriginUserId());
            responseVo.setMediaCall(responseVoMediaCall);
        }
    }

    /**
     * 构建文件信息
     *
     * @param bo
     * @param responseVo
     */
    public static void buildFile(ImChatRequestBo bo, ImChatResponseVo responseVo) {
        ImChatRequestBo.File boFile = bo.getFile();
        if (bo.getFile() == null) {
            return;
        }
        ImChatResponseVo.File file = new ImChatResponseVo.File();
        file.setFileName(boFile.getFileName());
        file.setFileSize(boFile.getFileSize());
        responseVo.setFile(file);
    }

    public static void buildFile(ImChatRequestBo bo, SohuImChatLastMessage message) {
        ImChatRequestBo.File boFile = bo.getFile();
        if (bo.getFile() == null) {
            return;
        }
        message.setFileSize(boFile.getFileSize());
        message.setFileName(boFile.getFileName());
    }

    public static void buildFile(ImChatRequestBo bo, SohuImChatMessage message) {
        ImChatRequestBo.File boFile = bo.getFile();
        if (bo.getFile() == null) {
            return;
        }
        message.setFileSize(boFile.getFileSize());
        message.setFileName(boFile.getFileName());
    }

    /**
     * 构建文件信息
     */
    public static void buildFile(ImChatResponseVo vo, SohuImChatMessageVo record) {
        if (!StrUtil.equalsAnyIgnoreCase(record.getMessageType(), ImMessageTypeEnum.File.getCode())) {
            return;
        }
        ImChatResponseVo.File file = new ImChatResponseVo.File();
        file.setFileName(record.getFileName());
        file.setFileSize(record.getFileSize());
        vo.setFile(file);
    }

    public static List<ImChatResponseVo.MemberUser> buildMemberUser(Collection<Long> memberIds, Map<Long, String> friendAliasMap) {
        List<ImChatResponseVo.MemberUser> memberUsers = new LinkedList<>();
        if (CollUtil.isNotEmpty(memberIds)) {
            RemoteUserService remoteUserService = SpringUtils.getBean(RemoteUserService.class);
            Map<Long, LoginUser> userMap = remoteUserService.selectMap(memberIds);
            for (Long memberId : memberIds) {
                ImChatResponseVo.MemberUser memberUser = new ImChatResponseVo.MemberUser();
                LoginUser user = userMap.get(memberId);
                if (Objects.isNull(user)) {
                    continue;
                }
                memberUser.setUserId(user.getUserId());
                memberUser.setNickName(user.getNickname());
                memberUser.setUserName(user.getUsername());
                memberUser.setUserAvatar(StrUtil.isBlankIfStr(user.getAvatar()) ? Constants.DEFAULT_USER_AVATAR : user.getAvatar());
                memberUser.setAlias(friendAliasMap.get(memberId));
                memberUsers.add(memberUser);
            }
        }
        return memberUsers;
    }

    /**
     * 构建分享信息
     *
     * @param bo
     * @param responseVo
     */
    public static void buildShareResponse(ImChatRequestBo bo, ImChatResponseVo responseVo) {
        if (Objects.isNull(bo) || Objects.isNull(bo.getShare()) || Objects.isNull(responseVo)) {
            return;
        }
        ImShareBo shareBo = bo.getShare();
        if (StrUtil.isBlankIfStr(shareBo.getType()) /*|| (shareBo.getId() == null || shareBo.getId() <= 0L)*/) {
            return;
        }
        RemoteUserService remoteUserService = SpringUtils.getBean(RemoteUserService.class);
        LoginUser shareUser = remoteUserService.selectById(shareBo.getShareUserId());
        if (Objects.nonNull(shareUser)) {
            shareBo.setShareUserName(StrUtil.isBlankIfStr(shareUser.getNickname()) ? shareUser.getUsername() : shareUser.getNickname());
            shareBo.setShareUserAvatar(StrUtil.isBlankIfStr(shareUser.getAvatar()) ? Constants.DEFAULT_USER_AVATAR : shareUser.getAvatar());
        }
        responseVo.setShare(shareBo);
    }

    public static void buildShareResponse(List<ImShareBo> shareBos, List<ImChatResponseVo> responseVoList) {
        if (CollUtil.isEmpty(shareBos) || CollUtil.isEmpty(responseVoList)) {
            return;
        }
        Set<Long> shareUserIds = new HashSet<>();
        shareBos.forEach(share -> {
            shareUserIds.add(share.getShareUserId());
        });
        RemoteUserService remoteUserService = SpringUtils.getBean(RemoteUserService.class);
        Map<Long, LoginUser> userMap = remoteUserService.selectMap(shareUserIds);
        for (ImChatResponseVo responseVo : responseVoList) {
            ImShareBo share = responseVo.getShare();
            if (Objects.isNull(share)) {
                continue;
            }
            if ((share.getShareUserId() == null || share.getShareUserId() <= 0L)) {
                continue;
            }
            LoginUser shareUser = userMap.get(share.getShareUserId());
            if (Objects.nonNull(shareUser)) {
                share.setShareUserName(StrUtil.isBlankIfStr(shareUser.getNickname()) ? shareUser.getUsername() : shareUser.getNickname());
                share.setShareUserAvatar(StrUtil.isBlankIfStr(shareUser.getAvatar()) ? Constants.DEFAULT_USER_AVATAR : shareUser.getAvatar());
            }
            responseVo.setShare(share);
        }
    }

    /**
     * 获取发送人名称优先级
     *
     * @param alias             接收方对发送方的 备注
     * @param groupUserNickName 发送方在群的昵称
     * @param userNickName      发送方的昵称
     * @param userName          发送方的名称
     * @return 接收方看到发送方的名称
     */
    public static String getSendName(String alias, String groupUserNickName, String userNickName, String userName) {
        return StringUtils.getValidString(alias, groupUserNickName, userNickName, userName);
    }

}
