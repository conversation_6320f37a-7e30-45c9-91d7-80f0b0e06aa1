package com.sohu.im.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CollUtils;
import com.sohu.common.core.utils.SpringUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.common.mybatis.aspect.LogicDeleteContext;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.mybatis.db.CacheMgr;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.bo.ImChatRequestBo;
import com.sohu.im.api.bo.SohuImChatMessageBo;
import com.sohu.im.api.bo.SohuImChatMessageQueryBo;
import com.sohu.im.api.constant.ImCacheConstants;
import com.sohu.im.api.enums.ImCommandTypeEnum;
import com.sohu.im.api.enums.ImMessageTypeEnum;
import com.sohu.im.api.enums.ImRiskTypeEnum;
import com.sohu.im.api.enums.ImSessionTypeEnum;
import com.sohu.im.api.vo.ImChatResponseVo;
import com.sohu.im.api.vo.SohuImChatMessageVo;
import com.sohu.im.api.vo.SohuImGroupVo;
import com.sohu.im.domain.SohuImChatMessage;
import com.sohu.im.domain.SohuImGroup;
import com.sohu.im.mapper.SohuImChatMessageMapper;
import com.sohu.im.mapper.SohuImGroupMapper;
import com.sohu.im.server.ImServerConfig;
import com.sohu.im.server.WebsocketStarter;
import com.sohu.im.service.ISohuImChatMessageService;
import com.sohu.im.service.ISohuImCommonService;
import com.sohu.im.utfil.ImGroupUtil;
import com.sohu.im.utfil.ImSendUtil;
import com.sohu.im.utfil.ImSocketResponseUtil;
import com.sohu.resource.api.RemoteFileService;
import com.sohu.system.api.RemoteUserService;
import com.sohu.system.api.bo.SysUserQueryBo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.tio.core.Tio;
import org.tio.server.ServerTioConfig;
import org.tio.websocket.common.WsResponse;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * im消息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuImChatMessageServiceImpl implements ISohuImChatMessageService {

    private final SohuImChatMessageMapper baseMapper;
    private final SohuImGroupMapper sohuImGroupMapper;
    private final ImSendUtil imSendUtil;

    private final ISohuImCommonService sohuImCommonService;

    @DubboReference
    private RemoteFileService remoteFileService;
    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 查询im消息
     */
    @Override
    public SohuImChatMessageVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询im消息列表
     */
    @Override
    public TableDataInfo<SohuImChatMessageVo> queryPageList(SohuImChatMessageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuImChatMessage> lqw = buildQueryWrapper(bo);
        Page<SohuImChatMessageVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询im消息列表
     */
    @Override
    public List<SohuImChatMessageVo> queryList(SohuImChatMessageBo bo) {
        LambdaQueryWrapper<SohuImChatMessage> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuImChatMessage> buildQueryWrapper(SohuImChatMessageBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuImChatMessage> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getContent()), SohuImChatMessage::getContent, bo.getContent());
        lqw.eq(bo.getSenderId() != null, SohuImChatMessage::getSenderId, bo.getSenderId());
        lqw.eq(bo.getReceiverId() != null, SohuImChatMessage::getReceiverId, bo.getReceiverId());
        lqw.eq(StringUtils.isNotBlank(bo.getSessionType()), SohuImChatMessage::getSessionType, bo.getSessionType());
        lqw.eq(StringUtils.isNotBlank(bo.getMessageType()), SohuImChatMessage::getMessageType, bo.getMessageType());
        lqw.eq(StringUtils.isNotBlank(bo.getShareType()), SohuImChatMessage::getShareType, bo.getShareType());
        lqw.eq(bo.getReadType() != null, SohuImChatMessage::getReadType, bo.getReadType());
        lqw.eq(bo.getHidden() != null, SohuImChatMessage::getHidden, bo.getHidden());
        lqw.eq(bo.getShareId() != null, SohuImChatMessage::getShareId, bo.getShareId());
        lqw.eq(bo.getChatId() != null, SohuImChatMessage::getChatId, bo.getChatId());
        lqw.eq(bo.getMsgId() != null, SohuImChatMessage::getMsgId, bo.getMsgId());
        lqw.in(CollUtil.isNotEmpty(bo.getMessageTypeList()), SohuImChatMessage::getMessageType, bo.getMessageTypeList());
        lqw.between(params.get("beginDuration") != null && params.get("endDuration") != null,
                SohuImChatMessage::getDuration, params.get("beginDuration"), params.get("endDuration"));
        lqw.between(params.get("beginFileSize") != null && params.get("endFileSize") != null,
                SohuImChatMessage::getFileSize, params.get("beginFileSize"), params.get("endFileSize"));
        return lqw;
    }

    /**
     * 新增im消息
     */
    @Override
    public Boolean insertByBo(SohuImChatMessageBo bo) {
        SohuImChatMessage add = BeanUtil.toBean(bo, SohuImChatMessage.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改im消息
     */
    @Override
    public Boolean updateByBo(SohuImChatMessageBo bo) {
        SohuImChatMessage update = BeanUtil.toBean(bo, SohuImChatMessage.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuImChatMessage entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除im消息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean deleteByChatId(Long chatId) {
        return baseMapper.delete(SohuImChatMessage::getChatId, chatId);
    }

    /**
     * IM上传文件超时7天处理
     *
     * @return {@link Boolean}
     */
    @Override
    public Boolean imFileOverTimeHandler() {
        LambdaQueryWrapper<SohuImChatMessage> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImChatMessage::getMessageType, ImMessageTypeEnum.File.getCode()); // 文件类型
        lqw.eq(SohuImChatMessage::isFileDelete, Boolean.FALSE); // 文件未删除
        lqw.le(SohuEntity::getCreateTime, LocalDateTime.now().minusDays(0)); // 7天以前
        List<SohuImChatMessage> overTimeFiles = baseMapper.selectList(lqw); // 查询文件列表
        if (CollUtil.isEmpty(overTimeFiles)) {
            log.info("imFileOverTimeHandler 不存在IM上传文件超时7天处理");
            return Boolean.FALSE;
        }
        for (SohuImChatMessage chatMessage : overTimeFiles) {
            chatMessage.setFileDelete(true);
        }
        baseMapper.updateBatchById(overTimeFiles);
        List<String> urlList = overTimeFiles.stream().filter(r -> Objects.nonNull(r) && StrUtil.isNotBlank(r.getContent())).map(SohuImChatMessage::getContent).collect(Collectors.toList());
        log.info("imFileOverTimeHandler 待删除文件：{} 个", urlList.size());
        // 分批处理，每批次最多 100 个
        int batchSize = 100;
        int totalSize = urlList.size();
        for (int i = 0; i < totalSize; i += batchSize) {
            // 获取当前批次
            List<String> batch = urlList.subList(i, Math.min(i + batchSize, totalSize));
            log.info("imFileOverTimeHandler 删除第 {} 批次，共 {} 个文件", (i / batchSize) + 1, batch.size());
            remoteFileService.ossDelete(batch);
        }
        log.info("imFileOverTimeHandler IM上传文件超时7天处理完成");
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateSessionType(Long groupId, String oldGroupType, String newGroupType) {
        LambdaUpdateWrapper<SohuImChatMessage> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuImChatMessage::getReceiverId, groupId).eq(SohuImChatMessage::getSessionType, oldGroupType);
        luw.set(SohuImChatMessage::getSessionType, newGroupType);
        return baseMapper.update(new SohuImChatMessage(), luw) > 0;
    }

    @Override
    public TableDataInfo<SohuImChatMessageVo> queryPageListOfManage(SohuImChatMessageQueryBo bo, PageQuery pageQuery) {
        Page<SohuImChatMessageVo> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        try {
            LogicDeleteContext.setIgnoreLogicDelete(true); // 禁用逻辑删除
            LambdaQueryWrapper<SohuImChatMessage> lqw = new LambdaQueryWrapper<>();
            lqw.eq(StrUtil.isNotBlank(bo.getSessionType()), SohuImChatMessage::getSessionType, bo.getSessionType());
            lqw.like(StrUtil.isNotBlank(bo.getSenderId()), SohuImChatMessage::getSenderId, bo.getSenderId());
            lqw.like(StrUtil.isNotBlank(bo.getReceiverId()), SohuImChatMessage::getReceiverId, bo.getReceiverId());
            lqw.like(StrUtil.isNotBlank(bo.getContent()), SohuImChatMessage::getContent, bo.getContent());
            lqw.eq(StrUtil.isNotBlank(bo.getMessageType()), SohuImChatMessage::getMessageType, bo.getMessageType());
            if (StrUtil.isBlankIfStr(bo.getMessageType())) {
                lqw.in(SohuImChatMessage::getMessageType, Arrays.asList(ImMessageTypeEnum.Text.getCode(),
                        ImMessageTypeEnum.Image.getCode(),
                        ImMessageTypeEnum.Video.getCode(),
                        ImMessageTypeEnum.Voice.getCode(),
                        ImMessageTypeEnum.Share.getCode(),
                        ImMessageTypeEnum.File.getCode()
                ));
            }
            if (bo.getStartTime() != null) {
                lqw.ge(SohuEntity::getCreateTime, bo.getStartTime());
            }
            if (bo.getEndTime() != null) {
                lqw.le(SohuEntity::getCreateTime, bo.getEndTime());
            }
            if (StrUtil.isNotBlank(bo.getSenderNickname()) || StrUtil.isNotBlank(bo.getSenderUserName())) {
                SysUserQueryBo queryBo = new SysUserQueryBo();
                queryBo.setNickName(bo.getSenderNickname());
                queryBo.setUserName(bo.getSenderUserName());
                List<Long> longs = remoteUserService.queryUserIdByBo(queryBo);
                lqw.in(CollUtil.isNotEmpty(longs), SohuImChatMessage::getSenderId, longs);
            }
            if (ImGroupUtil.isGroup(bo.getSessionType())) {
                if (StrUtil.isNotBlank(bo.getReceiverName())) {
                    LambdaQueryWrapper<SohuImGroup> lqwGroup = new LambdaQueryWrapper<>();
                    lqwGroup.like(SohuImGroup::getName, bo.getReceiverName());
                    List<SohuImGroup> sohuImGroups = sohuImGroupMapper.selectList(lqwGroup);
                    if (CollUtil.isNotEmpty(sohuImGroups)) {
                        lqw.in(SohuImChatMessage::getReceiverId, sohuImGroups.stream().map(SohuImGroup::getId).collect(Collectors.toList()));
                    }
                }
            }
            // 查询所有记录（包括已删除）
            lqw.in(SohuImChatMessage::isDelFlag, Arrays.asList("0", "1", "2"));
            lqw.orderByDesc(SohuImChatMessage::getId);

            page = this.baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        } finally {
            LogicDeleteContext.clear();
        }

        if (CollUtil.isEmpty(page.getRecords())) {
            return TableDataInfoUtils.build();
        }
        List<SohuImChatMessageVo> records = page.getRecords();
        Set<Long> userIds = new HashSet<>();
        if (StrUtil.equalsAnyIgnoreCase(bo.getSessionType(), ImSessionTypeEnum.single.name())) {
            for (SohuImChatMessageVo record : records) {
                userIds.add(record.getSenderId());
                userIds.add(record.getReceiverId());
            }
            Map<Long, LoginUser> userMap = remoteUserService.selectMap(userIds);
            for (SohuImChatMessageVo record : records) {
                LoginUser sender = userMap.get(record.getSenderId());
                LoginUser receiver = userMap.get(record.getReceiverId());
                if (Objects.nonNull(sender)) {
                    record.setSenderNickname(sender.getNickname());
                    record.setSenderUserName(sender.getUsername());
                }
                if (Objects.nonNull(receiver)) {
                    record.setReceiverName(receiver.getNickname());
                    record.setReceiverUserName(receiver.getUsername());
                }
            }
        } else if (ImGroupUtil.isGroup(bo.getSessionType())) {
            Set<Long> groupIds = new HashSet<>();
            for (SohuImChatMessageVo record : records) {
                userIds.add(record.getSenderId());
                groupIds.add(record.getReceiverId());
            }
            Map<Long, LoginUser> userMap = remoteUserService.selectMap(userIds);
            List<SohuImGroupVo> groupVos = sohuImCommonService.queryGroupList(groupIds);
            Map<Long, SohuImGroupVo> groupMap = groupVos.stream().collect(Collectors.toMap(SohuImGroupVo::getId, vo -> vo));
            for (SohuImChatMessageVo record : records) {
                LoginUser sender = userMap.get(record.getSenderId());
                if (Objects.nonNull(sender)) {
                    record.setSenderNickname(sender.getNickname());
                    record.setSenderUserName(sender.getUsername());
                }
                SohuImGroupVo groupVo = groupMap.get(record.getReceiverId());
                if (Objects.nonNull(groupVo)) {
                    record.setReceiverName(groupVo.getName());
                }
            }
        }
        page.setRecords(records);
        return TableDataInfoUtils.build(page);
    }

    @Override
    public Boolean recallMessage(Long id) {
        log.info("收到IM消息撤回，消息主键id：{}", id);
        Long loginId = LoginHelper.getUserId();
        if (!LoginHelper.checkLogin(loginId)) {
            log.error("recallMessage error,未登录！");
            return Boolean.FALSE;
        }
        SohuImChatMessage chatMessage = this.baseMapper.selectById(id);
        if (Objects.isNull(chatMessage)) {
            throw new ServiceException("消息不存在!");
        }
        if (!Objects.equals(chatMessage.getSenderId(), loginId)) {
            if (!LoginHelper.isAdmin()) {
                throw new ServiceException("非管理员不能撤回他人消息");
            }
        }
        String content = "系统撤回了一条消息";
        ImChatRequestBo requestBo = new ImChatRequestBo();
        //requestBo.setTenantId(chatMessage.getTenantId());
        requestBo.setSessionType(chatMessage.getSessionType());
        requestBo.setMessageType(ImMessageTypeEnum.Command.getCode());
        requestBo.setCommandType(ImCommandTypeEnum.systemRecall.getCode());
        requestBo.setContent(content);
        requestBo.setChatId(chatMessage.getChatId());
        requestBo.setLocalId(StrUtil.isBlankIfStr(chatMessage.getLocalId()) ? RandomUtil.randomString(16) : chatMessage.getLocalId());

        if (StrUtil.equalsAnyIgnoreCase(chatMessage.getSessionType(), ImSessionTypeEnum.group.getCode(), ImSessionTypeEnum.groupTask.getCode())) {
            requestBo.setReceiverId(chatMessage.getReceiverId());
            requestBo.setRealReceiverId(chatMessage.getReceiverId());
            requestBo.setRealSenderId(chatMessage.getSenderId());
        } else {
            requestBo.setReceiverId(chatMessage.getSenderId());
            requestBo.setRealReceiverId(chatMessage.getSenderId());
            requestBo.setRealSenderId(chatMessage.getReceiverId());
        }
        // 发送socket 消息
        imSendUtil.serverActiveSend(requestBo.getRealSenderId(), requestBo, true);
        // 删除IM消息
        this.deleteByChatId(chatMessage.getChatId());
        return Boolean.TRUE;
    }

    @Override
    public int setNanoTime() {
        LambdaQueryWrapper<SohuImChatMessage> lqw = new LambdaQueryWrapper<>();
        lqw.orderByAsc(SohuEntity::getCreateTime).orderByAsc(SohuImChatMessage::getId).
                and(wrapper -> wrapper.eq(SohuImChatMessage::isDelFlag, 0).or().eq(SohuImChatMessage::isDelFlag, 1));
        List<SohuImChatMessage> messageList = this.baseMapper.selectList();
        if (messageList == null || messageList.isEmpty()) {
            return 0;
        }

        // 为每一个毫秒时间戳单独维护一个计数器
        Map<Long, AtomicInteger> timestampCounterMap = new HashMap<>();

        List<SohuImChatMessage> toUpdateList = new ArrayList<>();

        for (SohuImChatMessage chatMessage : messageList) {
            Date createTime = chatMessage.getCreateTime();
            if (createTime != null) {
                long createTimestamp = createTime.getTime(); // 毫秒时间戳
                long newNanoTime = imSendUtil.generateNanoTime(createTimestamp, timestampCounterMap);

                chatMessage.setNanoTime(newNanoTime);
                toUpdateList.add(chatMessage);
            }
        }

        // 批量更新
        if (!toUpdateList.isEmpty()) {
            for (SohuImChatMessage message : toUpdateList) {
                this.baseMapper.updateById(message); // 逐条更新，或用批量更新
            }
        }
        return toUpdateList.size();
    }

    @Override
    public Boolean updateImRisk(String busyType, String busyCode, boolean checkPass) {
        if (!CollUtils.containsIgnoreCase(ImRiskTypeEnum.IM_CHAT_TYPE_LIST, busyType)) {
            return Boolean.FALSE;
        }
        if (StrUtil.isBlankIfStr(busyCode)) {
            log.error("updateImRisk busyCode is blank, busyType: {}, checkPass: {}", busyType, checkPass);
            return Boolean.FALSE;
        }
        ImChatRequestBo requestBo = (ImChatRequestBo) CacheMgr.get(ImCacheConstants.IM_RISK_CHECK, busyCode);
        if (Objects.isNull(requestBo)) {
            log.error("updateImRisk redis requestBo is null, busyType: {}, busyCode: {}, checkPass: {}", busyType, busyCode, checkPass);
            return Boolean.FALSE;
        }
        if (checkPass) {
            requestBo.setHidden(false);
            log.info("updateImRisk update success, busyType: {}, busyCode: {}, checkPass: {}", busyType, busyCode, checkPass);
            // 发送socket 消息 原接收方
            imSendUtil.serverActiveSend(requestBo.getRealSenderId(), requestBo, true);
        } else {
            log.info("updateImRisk update fail, busyType: {}, busyCode: {}, checkPass: {}", busyType, busyCode, checkPass);
            requestBo.setHidden(true);
            // 发送socket 消息 原给发送方
            ServerTioConfig serverTioConfig = WebsocketStarter.serverTioConfig;
            RemoteUserService remoteUserService = SpringUtils.getBean(RemoteUserService.class);
            LoginUser sendUser = remoteUserService.queryById(requestBo.getRealSenderId());
            requestBo.setNanoTime(imSendUtil.generateNanoTime(System.currentTimeMillis()));
            ImChatResponseVo responseVo = ImSocketResponseUtil.buildResponseVo(requestBo, sendUser);
            ImChatResponseVo.BodyDTO body = responseVo.getBody();
            body.setErr(ImSocketResponseUtil.errKeyword);
            WsResponse wsResponseForReceiver = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
            Tio.sendToUser(serverTioConfig, String.valueOf(requestBo.getRealSenderId()), wsResponseForReceiver);
            remoteFileService.ossDelete(Arrays.asList(requestBo.getContent()));
        }
        return Boolean.TRUE;
    }
}
