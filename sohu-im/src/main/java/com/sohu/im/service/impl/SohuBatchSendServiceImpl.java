package com.sohu.im.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.bo.SohuBatchSendBo;
import com.sohu.im.api.vo.SohuBatchSendListVo;
import com.sohu.im.api.vo.SohuBatchSendVo;
import com.sohu.im.domain.SohuBatchSend;
import com.sohu.im.mapper.SohuBatchSendMapper;
import com.sohu.im.service.ISohuBatchSendService;
import com.sohu.im.utfil.ImUnReadUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 群发内容记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
@RequiredArgsConstructor
@Service
public class SohuBatchSendServiceImpl implements ISohuBatchSendService {

    private final SohuBatchSendMapper baseMapper;

    /**
     * 查询群发内容记录
     */
    @Override
    public SohuBatchSendVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询群发内容记录列表
     */
    @Override
    public TableDataInfo<SohuBatchSendVo> queryPageList(SohuBatchSendBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuBatchSend> lqw = buildQueryWrapper(bo);
        Page<SohuBatchSendVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询群发内容记录列表
     */
    @Override
    public List<SohuBatchSendVo> queryList(SohuBatchSendBo bo) {
        LambdaQueryWrapper<SohuBatchSend> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuBatchSend> buildQueryWrapper(SohuBatchSendBo bo) {
        LambdaQueryWrapper<SohuBatchSend> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuBatchSend::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), SohuBatchSend::getContent, bo.getContent());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), SohuBatchSend::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiverId()), SohuBatchSend::getReceiverId, bo.getReceiverId());
        return lqw;
    }

    /**
     * 新增群发内容记录
     */
    @Override
    public Boolean insertByBo(SohuBatchSendBo bo) {
        SohuBatchSend add = BeanUtil.toBean(bo, SohuBatchSend.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改群发内容记录
     */
    @Override
    public Boolean updateByBo(SohuBatchSendBo bo) {
        SohuBatchSend update = BeanUtil.toBean(bo, SohuBatchSend.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuBatchSend entity) {
        entity.setUserId(LoginHelper.getUserId());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
    }

    /**
     * 批量删除群发内容记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<SohuBatchSendListVo> batchSendRecord() {
        LambdaQueryWrapper<SohuBatchSend> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuBatchSend::getUserId, LoginHelper.getUserId());
        lqw.orderByDesc(SohuEntity::getCreateTime);
        List<SohuBatchSendVo> sohuBatchSendVos = baseMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(sohuBatchSendVos)) {
            return null;
        }
        List<SohuBatchSendListVo> result = new LinkedList<>();
        for (SohuBatchSendVo sohuBatchSendVo : sohuBatchSendVos) {
            SohuBatchSendListVo vo = new SohuBatchSendListVo();
            vo.setContent(sohuBatchSendVo.getContent());
            vo.setType(sohuBatchSendVo.getType());
            vo.setCreateTime(sohuBatchSendVo.getCreateTime());
            vo.setCount(StrUtil.split(sohuBatchSendVo.getReceiverId(), StrUtil.COMMA).size());
            result.add(vo);
        }
        return result;
    }

    @Override
    @Async("asyncExecutor")
    public void asyncInrUnRead(List<Long> groupUserIds, Long groupId, String sessionType, Long senderId) {
        if (CollUtil.isEmpty(groupUserIds)) {
            return;
        }
        for (Long groupUserId : groupUserIds) {
            if (Objects.equals(groupUserId, senderId)) {
                continue;
            }
            // 未读消息缓存
            ImUnReadUtil.inrUnReadCount(sessionType, groupId, groupUserId);
        }
    }
}
