package com.sohu.im.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.im.api.bo.SohuBatchSendBo;
import com.sohu.im.api.vo.SohuBatchSendListVo;
import com.sohu.im.api.vo.SohuBatchSendVo;

import java.util.Collection;
import java.util.List;

/**
 * 群发内容记录Service接口
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
public interface ISohuBatchSendService {

    /**
     * 查询群发内容记录
     */
    SohuBatchSendVo queryById(Long id);

    /**
     * 查询群发内容记录列表
     */
    TableDataInfo<SohuBatchSendVo> queryPageList(SohuBatchSendBo bo, PageQuery pageQuery);

    /**
     * 查询群发内容记录列表
     */
    List<SohuBatchSendVo> queryList(SohuBatchSendBo bo);

    /**
     * 修改群发内容记录
     */
    Boolean insertByBo(SohuBatchSendBo bo);

    /**
     * 修改群发内容记录
     */
    Boolean updateByBo(SohuBatchSendBo bo);

    /**
     * 校验并批量删除群发内容记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 群发内容记录列表
     * @return {@link List}
     */
    List<SohuBatchSendListVo> batchSendRecord();

    /**
     * 异步增加未读消息数
     *
     * @param groupUserIds 群组用户ID
     * @param groupId      群组ID
     * @param sessionType  会话类型
     * @param senderId     发送者ID
     */
    void asyncInrUnRead(List<Long> groupUserIds, Long groupId, String sessionType, Long senderId);

}
