package com.sohu.im.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.im.api.bo.*;
import com.sohu.im.api.vo.SohuImGroupUserVo;
import com.sohu.im.api.vo.SohuImGroupVo;
import com.sohu.im.domain.SohuImGroup;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * im群组Service接口
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
public interface ISohuImGroupService {

    /**
     * 查询im群组
     */
    SohuImGroupVo get(Long id);

    /**
     * 查询im群组
     */
    SohuImGroupVo queryById(Long id);

    /**
     * 查询im群组列表
     */
    TableDataInfo<SohuImGroupVo> queryPageList(SohuImGroupBo bo, PageQuery pageQuery);

    /**
     * 查询im群组列表
     */
    List<SohuImGroupVo> queryList(SohuImGroupBo bo);

    /**
     * 查询im群组列表
     */
    List<SohuImGroupVo> selectBatchIds(Collection<Long> idList);

    /**
     * 创建im群组
     */
    Boolean insertByBo(SohuImGroupBo bo);

    /**
     * 创建im群组
     *
     * @param bo
     * @return {@link SohuImGroup}
     */
    SohuImGroup insert(SohuImGroupBo bo);

    /**
     * 修改im群组
     */
    Boolean updateByBo(SohuImGroupBo bo);

    /**
     * 校验并批量删除im群组信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 创建群聊
     *
     * @param userIds 用户id
     * @return {@link Boolean}
     */
    SohuImGroup create(Object userIds);

    /**
     * 创建子群
     *
     * @param subGroupBo 创建子群入参
     * @return {@link Boolean}
     */
    SohuImGroup subCreate(SohuImGroupInviteBo subGroupBo);

    /**
     * 建群通用方法
     *
     * @param groupHeaderId     群主ID，即创建人的用户ID
     * @param groupHeaderName   群主用户名称
     * @param pid               父群ID
     * @param groupName         群名称
     * @param adminIds          管理员ID列表
     * @param userIds           邀请的用户ID列表（不包含群主）-普通成员
     * @param sendFirstGroupMsg 是否发送创建群聊的第一条消息
     * @return {@link Boolean}
     */
    SohuImGroup createGroup(Long groupHeaderId, String groupHeaderName, Long pid, String groupName, Set<Long> adminIds, Set<Long> userIds, boolean sendFirstGroupMsg);

    /**
     * 建群通用方法
     *
     * @param groupHeaderId     群主ID，即创建人的用户ID
     * @param pid               父群ID
     * @param userIds           邀请的用户ID列表（不包含群主）
     * @param sendFirstGroupMsg 是否发送创建群聊的第一条消息
     * @return {@link Boolean}
     */
    SohuImGroup createGroup(Long groupHeaderId, Long pid, Set<Long> userIds, boolean sendFirstGroupMsg);

    /**
     * 进群审批，开启与关闭
     *
     * @param id 群ID
     * @return {@link Boolean}
     */
    Boolean switchGroup(Long id);

    /**
     * 生成群二维码
     *
     * @param id 群ID
     * @return {@link Boolean}
     */
    SohuImApplyGroupBo genQrCode(Long id);

    /**
     * 群关联列表
     *
     * @param groupId 群ID
     * @return {@link List}
     */
    List<SohuImGroupVo> subList(Long groupId);

    /**
     * 解散群
     *
     * @param groupId    群ID
     * @param canDismiss 是否可以强制解散群
     * @return {@link Boolean}
     */
    Boolean dismiss(Long groupId, Boolean canDismiss);

    /**
     * 查询群消息条数
     *
     * @param groupId 群ID
     */
    long groupCount(Long groupId);

    /**
     * 解散群
     *
     * @param groupIds 群ID集合
     * @return {@link Boolean}
     */
    Boolean batchDismiss(List<Long> groupIds);

    /**
     * 批量设置用户身份
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean batchSetRole(SohuImGroupUserListBo bo);

    /**
     * 批量设置用户禁言/解除禁言
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean batchSetForbid(SohuImGroupUserListBo bo);

    /**
     * 获取已经禁言用户列表
     *
     * @param groupId 群ID
     * @return {@link List}
     */
    List<SohuImGroupUserVo> forbidUsers(Long groupId);

    /**
     * 查询用户已加入的群列表,ID集合
     *
     * @return {@link List}
     */
    List<Long> joinGroupIdList();

    /**
     * 查询用户已加入的群列表
     *
     * @return {@link List}
     */
    List<SohuImGroupUserVo> joinGroupList();

    /**
     * 查询用户已加入的群列表
     *
     * @return {@link List}
     */
    List<SohuImGroupVo> joinList();

    /**
     * 获取用户管理的所有群
     *
     * @return {@link List}
     */
    List<Long> userManagedGroups();

    /**
     * 获取用户创建的所有群
     *
     * @return {@link List}
     */
    List<Long> userCreatedGroups();

    /**
     * 获取用户是否被禁言
     *
     * @param groupId 群ID
     * @return true 或false ,是否被禁言
     */
    Boolean forbid(Long groupId);

    /**
     * 构建群列表，按照最新聊天时间排序
     *
     * @param groupList 群列表数据
     * @return {@link List}
     */
    List<SohuImGroupVo> buildGroupListOrderByChatTime(List<SohuImGroupVo> groupList);

    /**
     * 建群
     *
     * @param bo
     * @return
     */
    Boolean createGroup(SohuImGroupCreateBo bo);

    /**
     * 通过接单号获取群信息
     *
     * @param taskNumber 接单号
     * @return {@link SohuImGroupVo}
     */
    SohuImGroupVo queryByTaskNumber(String taskNumber);

    /**
     * 通过接单号获取群信息
     *
     * @param groupType  群类型 {@link com.sohu.im.api.enums.ImGroupType}
     * @param taskNumber 接单号
     * @return {@link SohuImGroupVo}
     */
    SohuImGroupVo queryByTaskNumber(String groupType, String taskNumber);

    /**
     * 通过子群单号删除群
     *
     * @param publishUserId   发布者id
     * @param childTaskNumber 子群单号
     * @return {@link Boolean}
     */
    Boolean deleteGroupTask(Long publishUserId, String childTaskNumber);

    /**
     * 给没有群组唯一标识的群列表设置标识
     *
     * @return
     */
    int setGroupUnq();

    /**
     * 通过子群单号删除群-不需要登录才能调
     *
     * @param publishUserId   发单方用户ID
     * @param childTaskNumber 子群单号
     * @return {@link Boolean}
     */
    Boolean deleteGroupTaskNoLogin(Long publishUserId, String childTaskNumber);

    /**
     * 推荐群聊
     *
     * @return {@link List}
     */
    List<SohuImGroupVo> recommGroupList();

    /**
     * 禁止群成员互加好友 开启/关闭
     *
     * @param id
     * @return {@link Boolean}
     */
    Boolean addFriend(Long id);

    /**
     * 群禁言时间段设置开关
     *
     * @param id
     * @return {@link Boolean}
     */
    Boolean forbidTime(Long id);

    /**
     * 获取用户创建的群
     *
     * @return {@link List}
     */
    List<SohuImGroupVo> createdList();

    /**
     * 查询群信息
     *
     * @param groupIds 群ID集合
     * @return 返回map形式
     */
    Map<Long, SohuImGroupVo> queryMap(Collection<Long> groupIds);

    /**
     * 获取入群口令
     *
     * @param groupId 群id
     * @return {@link String} 入群口令
     */
    String getGroupWord(Long groupId);

    /**
     * 群禁用
     *
     * @return {@link Boolean}
     */
    Boolean groupDisable(SohuImGroupDisableBo bo);

    /**
     * 后台群启用
     *
     * @param groupId 群id
     * @return {@link Boolean}
     */
    Boolean groupEnable(Long groupId);

    /**
     * 群列表分页查询
     *
     * @param bo
     * @return {@link TableDataInfo}
     */
    TableDataInfo<SohuImGroupVo> groupPage(SohuImPageQueryBo bo);

    /**
     * 查询我的群聊
     *
     * @return
     */
    List<SohuImGroupVo> queryMyImGroup(SohuMyImGroupQueryBo bo);

    /**
     * 查询用户已加入的群数量
     *
     * @param loginId 用户id
     * @return 群数量
     */
    Long countJoinGroup(Long loginId);
}
