package com.sohu.im.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.busyorder.api.vo.SohuBusyTaskVo;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.bo.*;
import com.sohu.im.api.enums.*;
import com.sohu.im.api.vo.SohuImGroupFormExtVo;
import com.sohu.im.api.vo.SohuImGroupVo;
import com.sohu.im.domain.SohuImGroup;
import com.sohu.im.service.ISohuImCommonService;
import com.sohu.im.service.ISohuImGroupFromGeneralService;
import com.sohu.im.service.ISohuImGroupService;
import com.sohu.im.utfil.ImSendUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * IM表单Service接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ISohuImGroupFromGeneralServiceImpl implements ISohuImGroupFromGeneralService {

    private final ISohuImGroupService sohuImGroupService;
    private final ISohuImCommonService sohuImCommonService;

    @DubboReference
    private final RemoteBusyTaskService remoteBusyTaskService;

    private final ImSendUtil imSendUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createGroup(SohuImGroupFormGeneralCreateBo bo) {
        log.info("通用商单任务建群开始：{}", JSONUtil.toJsonStr(bo));
        // 接单方用户ID
        Long loginId = CalUtils.isNullOrZero(bo.getReceiveUserId()) ? LoginHelper.getUserId() : bo.getReceiveUserId();
        if (CalUtils.isNullOrZero(loginId)) {
            log.error("表单任务建群失败");
            return null;
        }
        SohuBusyTaskVo mainTask = remoteBusyTaskService.getByTaskNo(bo.getMasterTaskNumber());
        if (Objects.isNull(mainTask)) {
            log.info("IM通用商单不存在:{}", JSONUtil.toJsonStr(bo));
            return null;
        }

        log.info("IM通用商单建群:{}", JSONUtil.toJsonStr(bo));
        // 查询主群
        SohuImGroupVo mainGroup = sohuImGroupService.queryByTaskNumber(ImGroupType.groupForm.name(), bo.getMasterTaskNumber());
        if (Objects.nonNull(mainGroup)) {
            return mainGroup.getId();
        }
        SohuImGroupCreateBo mainGroupBo = new SohuImGroupCreateBo();
        log.info("IM通用商单建群开始");
        mainGroupBo.setUserId(mainTask.getUserId());
        mainGroupBo.setName(StringUtils.substring(mainTask.getTitle(), 0, 10));
        mainGroupBo.setLogo(SohuImGroupFormGeneralCreateBo.GROUP_FROM_LOGO);
        mainGroupBo.setGroupType(ImGroupType.groupFromGeneral.name());
        mainGroupBo.setNeedConfirm(true);
        SohuImGroupFormExtVo formExtVo = new SohuImGroupFormExtVo();
        formExtVo.setTaskPublishUserId(mainTask.getUserId());
        formExtVo.setMasterTaskId(mainTask.getId());
        formExtVo.setMasterTaskNumber(bo.getMasterTaskNumber());
        List<String> taskNumberList = new ArrayList<>();
        List<Long> taskReceUserIds = new ArrayList<>();
        taskNumberList.add(bo.getTaskNumber());
        taskReceUserIds.add(loginId);
        formExtVo.setTaskNumberList(taskNumberList);
        formExtVo.setTaskReceUserIds(taskReceUserIds);
        // 设置拓展字段
        mainGroupBo.setGroupExt(JSONUtil.toJsonStr(formExtVo));
        mainGroupBo.setGroupNotice(SohuImGroupFormGeneralCreateBo.ANONYMOUS_NOTICE);
        List<SohuImGroupUserBo> groupUsers = new ArrayList<>();
        for (Long taskUserId : taskReceUserIds) {
            // 接单人
            SohuImGroupUserBo groupUserRece = SohuImGroupUserBo.builder().
                    userId(taskUserId).sensitiveSwitch(true).
                    permissionType(ImGroupPermissionType.group_user.name()).build();
            Map<String, Object> extMap = new HashMap<>();
            extMap.put("groupTaskRole", ImGroupTaskRole.taskRece.getCode());
            groupUserRece.setExt(JSONUtil.toJsonStr(extMap));
            groupUsers.add(groupUserRece);
        }
        mainGroupBo.setGroupUsers(groupUsers);
        mainGroupBo.setSendFirstGroupMsg(true);
        sohuImGroupService.createGroup(mainGroupBo);
        log.info("IM通用商单建群结束");

        // 发送socket消息
        ImChatRequestBo requestBo = new ImChatRequestBo();
        requestBo.setReceiverId(mainGroupBo.getGroupId());
        requestBo.setSessionType(ImSessionTypeEnum.groupFromGeneral.name());
        requestBo.setMessageType(ImMessageTypeEnum.Command.getCode());
        requestBo.setCommandType(ImCommandTypeEnum.groupGreet.getCode());
        requestBo.setContent(SohuImGroupFormGeneralCreateBo.NOTICE_ONE);
        requestBo.setChatId(System.nanoTime());
        requestBo.setLocalId(RandomUtil.randomString(16));
        // 发送socket 消息
        imSendUtil.serverActiveSend(mainGroupBo.getUserId(), requestBo, true);

        // 发送socket消息
        ImChatRequestBo requestBo2 = new ImChatRequestBo();
        requestBo2.setReceiverId(mainGroupBo.getGroupId());
        requestBo2.setSessionType(ImSessionTypeEnum.groupFromGeneral.name());
        requestBo2.setMessageType(ImMessageTypeEnum.Text.getCode());
        requestBo2.setContent(SohuImGroupFormGeneralCreateBo.ANONYMOUS_NOTICE);
        requestBo2.setChatId(System.nanoTime());
        requestBo2.setLocalId(RandomUtil.randomString(16));
        // 发送socket 消息
        imSendUtil.serverActiveSend(mainGroupBo.getUserId(), requestBo2, true);
        return mainGroupBo.getGroupId();
    }

    @Override
    public Boolean handleOverMainGroup(SohuImGroupFormHandleBo bo) {
        String mainTaskNumber = bo.getMainTaskNumber();
        log.info("handleOverMainGroup,通用商单结束逻辑开始,{}", mainTaskNumber);
        SohuBusyTaskVo mainTask = remoteBusyTaskService.getByTaskNo(mainTaskNumber);
        if (Objects.isNull(mainTask)) {
            log.error("通用商单不存在:{}", mainTaskNumber);
            return Boolean.FALSE;
        }
        // 查询主群
        SohuImGroupVo mainGroup = sohuImGroupService.queryByTaskNumber(ImGroupType.groupFromGeneral.name(), mainTaskNumber);
        if (Objects.isNull(mainGroup)) {
            log.error("通用商单群不存在:{}", mainTaskNumber);
            return Boolean.FALSE;
        }
        // 群聊列表看不到此群 通用商单
        SohuImGroup sohuImGroup = sohuImCommonService.query(mainGroup.getId());
        sohuImGroup.setState(2);
        sohuImGroup.setLogo(SohuImGroupFormGeneralCreateBo.GROUP_FROM_LOGO_GRAY);
        sohuImCommonService.updateGroup(sohuImGroup);

        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {

        }

        // 发送socket消息
        String content = "愿望已结束";
        ImChatRequestBo requestBo = new ImChatRequestBo();
        requestBo.setReceiverId(sohuImGroup.getId());
        requestBo.setSessionType(sohuImGroup.getGroupType());
        requestBo.setMessageType(ImMessageTypeEnum.Command.getCode());
        requestBo.setCommandType(ImCommandTypeEnum.groupTaskFinish.getCode());
        requestBo.setContent(content);
        requestBo.setChatId(System.nanoTime());
        requestBo.setLocalId(RandomUtil.randomString(16));
        // 发送socket 消息
        imSendUtil.serverActiveSend(sohuImGroup.getUserId(), requestBo, true);
        return Boolean.TRUE;
    }

}
