package com.sohu.im.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.im.api.bo.SohuImGroupOrderUserBo;
import com.sohu.im.api.vo.SohuImGroupOrderUserVO;
import com.sohu.im.domain.SohuImGroupOrderUser;
import com.sohu.im.mapper.SohuImGroupOrderUserMapper;
import com.sohu.im.service.ISohuImGroupOrderUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: leibo
 * @Date: 2025/1/15 18:07
 **/
@RequiredArgsConstructor
@Service
public class SohuImGroupOrderUserServiceImpl implements ISohuImGroupOrderUserService {

    private final SohuImGroupOrderUserMapper sohuImGroupOrderUserMapper;

    @Override
    public Integer getPassPersonUserByMasterTaskNumber(String masterTaskNumber) {
        return Math.toIntExact(sohuImGroupOrderUserMapper.selectCount(Wrappers.<SohuImGroupOrderUser>lambdaQuery()
                .eq(SohuImGroupOrderUser::getRelateObjParentId, masterTaskNumber)));
    }

    @Override
    public Integer getPassPersonUserByTaskNumber(String taskNumber) {
        return Math.toIntExact(sohuImGroupOrderUserMapper.selectCount(Wrappers.<SohuImGroupOrderUser>lambdaQuery()
                .eq(SohuImGroupOrderUser::getRelateObjId, taskNumber)));
    }

    @Override
    public Integer getPassPersonUserByGroupIds(List<Long> groupIds) {
        return CollUtil.isEmpty(groupIds) ? 0 : Math.toIntExact(sohuImGroupOrderUserMapper.selectCount(Wrappers.<SohuImGroupOrderUser>lambdaQuery()
                .in(SohuImGroupOrderUser::getGroupId, groupIds)));
    }

    @Override
    public Set<Long> listPassPersonUserByGroupId(Long groupId) {
        LambdaQueryWrapper<SohuImGroupOrderUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImGroupOrderUser::getGroupId, groupId);
        List<SohuImGroupOrderUserVO> groupOrderUserVOS = sohuImGroupOrderUserMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(groupOrderUserVOS)) {
            return new HashSet<>();
        }
        Set<Long> userIds = new HashSet<>();
        for (SohuImGroupOrderUserVO userVO : groupOrderUserVOS) {
            userIds.add(userVO.getUserId());
        }
        return userIds;
    }

    @Override
    public Boolean insertBO(SohuImGroupOrderUserBo bo) {
        SohuImGroupOrderUser add = BeanCopyUtils.copy(bo, SohuImGroupOrderUser.class);
        return sohuImGroupOrderUserMapper.insert(add) > 0;
    }

    @Override
    public List<SohuImGroupOrderUserVO> queryList(List<String> taskNumbers) {
        if (CollUtil.isEmpty(taskNumbers)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SohuImGroupOrderUser> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuImGroupOrderUser::getRelateObjId, taskNumbers);
        return sohuImGroupOrderUserMapper.selectVoList(lqw);
    }

    @Override
    public List<Long> listPassUserIdsByGroupId(Long groupId) {
        List<SohuImGroupOrderUser> userList = sohuImGroupOrderUserMapper.selectList(
                Wrappers.<SohuImGroupOrderUser>lambdaQuery().eq(SohuImGroupOrderUser :: getGroupId, groupId));
        return userList.stream().map(SohuImGroupOrderUser :: getUserId).collect(Collectors.toList());
    }

    @Override
    public SohuImGroupOrderUserVO getOneByGroupIdAndUserId(Long groupId, Long userId) {
        return sohuImGroupOrderUserMapper.selectVoOne(Wrappers.<SohuImGroupOrderUser>lambdaQuery()
                .eq(SohuImGroupOrderUser :: getGroupId, groupId).eq(SohuImGroupOrderUser :: getUserId, userId));
    }

    @Override
    public SohuImGroupOrderUser queryByMasterTaskNum(String masterTaskNum) {
        LambdaQueryWrapper<SohuImGroupOrderUser> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuImGroupOrderUser::getRelateObjParentId, masterTaskNum);
        lqw.orderByDesc(SohuImGroupOrderUser::getUpdateTime);
        lqw.last("limit 1");
        return sohuImGroupOrderUserMapper.selectOne(lqw);
    }
}
