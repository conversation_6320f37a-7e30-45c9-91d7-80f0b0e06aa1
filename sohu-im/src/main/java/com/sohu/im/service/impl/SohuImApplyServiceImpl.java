package com.sohu.im.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.ApplyStateEnum;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.DictEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.SohuErrorUtil;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.bo.*;
import com.sohu.im.api.enums.ImGroupPermissionType;
import com.sohu.im.api.enums.ImSessionTypeEnum;
import com.sohu.im.api.vo.SohuImApplyVo;
import com.sohu.im.api.vo.SohuImGroupChannelVo;
import com.sohu.im.appevent.event.ImGroupAddUserEvent;
import com.sohu.im.domain.SohuImApply;
import com.sohu.im.domain.SohuImGroup;
import com.sohu.im.mapper.SohuImApplyMapper;
import com.sohu.im.service.*;
import com.sohu.im.utfil.ImErrorUtil;
import com.sohu.im.utfil.ImGroupUtil;
import com.sohu.middle.api.service.RemoteMiddleFriendService;
import com.sohu.system.api.RemoteDictService;
import com.sohu.system.api.RemoteUserService;
import com.sohu.system.api.domain.SysDictData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * im申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuImApplyServiceImpl implements ISohuImApplyService {

    private final SohuImApplyMapper baseMapper;
    private final ISohuImGroupUserService sohuImGroupUserService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final ISohuImGroupChannelUserService sohuImGroupChannelUserService;
    private final ISohuImGroupChannelService sohuImGroupChannelService;
    private final ISohuImCommonService sohuImCommonService;

    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteDictService remoteDictService;
    @DubboReference
    private RemoteMiddleFriendService remoteMiddleFriendService;

    /**
     * 查询im申请
     */
    @Override
    public SohuImApplyVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询im申请列表
     */
    @Override
    public TableDataInfo<SohuImApplyVo> queryPageList(SohuImApplyBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuImApply> lqw = buildQueryWrapper(bo);
        Page<SohuImApplyVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuImApplyVo> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfoUtils.build();
        }
        Set<Long> userIds = new HashSet<>();
        for (SohuImApplyVo record : records) {
            userIds.add(record.getApplyUserId());
        }
        Long loginId = LoginHelper.getUserId();
        Map<Long, String> friendAliasMap = new HashMap<>();
        if (StrUtil.isNotBlank(bo.getApplyType()) && StrUtil.equalsAnyIgnoreCase(bo.getApplyType(), ImSessionTypeEnum.group.getCode())) {
            friendAliasMap = remoteMiddleFriendService.friendAliasMap(loginId, userIds, ApplyStateEnum.pass.name());
        }
        Map<Long, LoginUser> userMap = remoteUserService.selectMap(userIds);
        for (SohuImApplyVo record : records) {
            LoginUser user = userMap.get(record.getApplyUserId());
            if (Objects.isNull(user)) {
                this.baseMapper.deleteById(record);
                continue;
            }
            record.setUserName(StringUtils.getValidString(user.getNickname(), user.getUsername()));
            record.setUserAvatar(user.getAvatar());
            if (ImGroupUtil.isGroup(record.getApplyType()) && StrUtil.isNotBlank(friendAliasMap.get(record.getApplyUserId()))) {
                record.setUserName(friendAliasMap.get(record.getApplyUserId()));
            }
        }
        result.setRecords(records);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询im申请列表
     */
    @Override
    public List<SohuImApplyVo> queryList(SohuImApplyBo bo) {
        LambdaQueryWrapper<SohuImApply> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuImApply> buildQueryWrapper(SohuImApplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuImApply> lqw = Wrappers.lambdaQuery();
        lqw.eq((bo.getApplyUserId() != null && bo.getApplyUserId() > 0L), SohuImApply::getApplyUserId, bo.getApplyUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyAttach()), SohuImApply::getApplyAttach, bo.getApplyAttach());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyContent()), SohuImApply::getApplyContent, bo.getApplyContent());
        lqw.eq(bo.getTargetId() != null, SohuImApply::getTargetId, bo.getTargetId());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyType()), SohuImApply::getApplyType, bo.getApplyType());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyState()), SohuImApply::getApplyState, bo.getApplyState());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), SohuImApply::getReason, bo.getReason());
        lqw.eq((bo.getInviteUser() != null && bo.getInviteUser() > 0L), SohuImApply::getInviteUser, bo.getInviteUser());
        lqw.orderByDesc(SohuEntity::getCreateTime);
        return lqw;
    }

    /**
     * 新增im申请
     */
    @Override
    public Boolean insertByBo(SohuImApplyBo bo) {
        SohuImApply add = BeanUtil.toBean(bo, SohuImApply.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改im申请
     */
    @Override
    public Boolean updateByBo(SohuImApplyBo bo) {
        SohuImApply update = BeanUtil.toBean(bo, SohuImApply.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuImApply entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除im申请
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addGroup(SohuImApplyGroupBo groupBo) {
        Long userId = LoginHelper.getUserId();
        SohuErrorUtil.checkLoginId(userId);
        long result = groupBo.getInviteUserId() ^ Constants.CALCULATE;
        if (result != groupBo.getInviteCalculate()) {
            throw new ServiceException(MessageUtils.message("illegal.request"));
        }
        SohuImGroup sohuImGroup = sohuImCommonService.query(groupBo.getGroupId());
        ImErrorUtil.checkGroupExist(sohuImGroup);
        if (sohuImGroup.getNeedConfirm() != null && !sohuImGroup.getNeedConfirm()) {
            // 未开启进群审核，直接进群
            SohuImGroupUserBo imGroupUserBo = SohuImGroupUserBo.builder().build();
            imGroupUserBo.setGroupId(groupBo.getGroupId());
            imGroupUserBo.setUserId(userId);
            imGroupUserBo.setPermissionType(ImGroupPermissionType.group_user.name());
            imGroupUserBo.setCreateTime(new Date());
            imGroupUserBo.setUpdateTime(new Date());
            imGroupUserBo.setInviteUser(groupBo.getInviteUserId());

            if (StrUtil.isNotBlank(groupBo.getChannelCode())) {
                sohuImGroupChannelUserService.invite(groupBo.getGroupId(), userId, groupBo.getChannelCode(), groupBo.getInviteUserId());
            }

            // 添加群成员
            sohuImGroupUserService.insertByBo(imGroupUserBo);

            // 绑定拉新关系是否开启，开启了则要保存拉新渠道关系关联
            bindUserInvite(sohuImGroup.getId(), groupBo.getInviteUserId(), userId);
            // 主动发送进群消息
            //ImSocketResponseUtil.sendJoinGroupCommandMessage(groupBo.getGroupId(), userId, groupBo.getChannelCode());

        } else {
            SohuImApply imApply = this.baseMapper.selectOne(SohuImApply::getApplyType, SohuImApply.GROUP, SohuImApply::getTargetId, groupBo.getGroupId(), SohuImApply::getApplyUserId, userId);
            if (Objects.isNull(imApply)) {
                SohuImApply sohuImApply = new SohuImApply(userId, groupBo.getApplyContent(), groupBo.getGroupId(), SohuImApply.GROUP);
                sohuImApply.setInviteUser(groupBo.getInviteUserId());
                sohuImApply.setChannelCode(groupBo.getChannelCode());
                baseMapper.insert(sohuImApply);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean activeGroup(SohuImApplyActiveGroupBo groupBo) {
        Long userId = LoginHelper.getUserId();
        SohuErrorUtil.checkLoginId(userId);
        SohuImGroup sohuImGroup = sohuImCommonService.query(groupBo.getGroupId());
        ImErrorUtil.checkGroupExist(sohuImGroup);
        if (sohuImGroup.getNeedConfirm() != null && !sohuImGroup.getNeedConfirm()) {
            // 未开启进群审核，直接进群
            SohuImGroupUserBo imGroupUserBo = SohuImGroupUserBo.builder().build();
            imGroupUserBo.setGroupId(groupBo.getGroupId());
            imGroupUserBo.setUserId(userId);
            imGroupUserBo.setPermissionType(ImGroupPermissionType.group_user.name());
            imGroupUserBo.setCreateTime(new Date());
            imGroupUserBo.setUpdateTime(new Date());
            // 添加群成员
            sohuImGroupUserService.insertByBo(imGroupUserBo);

            // 主动发送进群消息
            //ImSocketResponseUtil.sendJoinGroupCommandMessage(groupBo.getGroupId(), userId, groupBo.getChannelCode());
        } else {
            SohuImApply imApply = this.baseMapper.selectOne(SohuImApply::getApplyType, SohuImApply.GROUP, SohuImApply::getTargetId, groupBo.getGroupId(), SohuImApply::getApplyUserId, userId);
            if (Objects.isNull(imApply)) {
                SohuImApply sohuImApply = new SohuImApply(userId, groupBo.getApplyContent(), groupBo.getGroupId(), SohuImApply.GROUP);
                baseMapper.insert(sohuImApply);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean addUser(SohuImApplyUserBo bo) {
        Long userId = LoginHelper.getUserId();
        SohuErrorUtil.checkLoginId(userId);
        long result = bo.getUserId() ^ Constants.CALCULATE;
        if (result != bo.getUserCalculate()) {
            throw new ServiceException(MessageUtils.message("illegal.request"));
        }
        LoginUser user = remoteUserService.queryById(bo.getUserId());
        if (Objects.isNull(user)) {
            throw new ServiceException(MessageUtils.message("illegal.request"));
        }
        SohuImApply sohuImApply = new SohuImApply(userId, null, bo.getUserId(), SohuImApply.USER);
        baseMapper.insert(sohuImApply);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean groupAudit(SohuImApplyBo bo) {
        SohuImApply imApply = this.baseMapper.selectById(bo.getId());
        if (Objects.isNull(imApply)) {
            return Boolean.FALSE;
        }
        if (StrUtil.equalsAnyIgnoreCase(CommonState.Pass.getCode(), bo.getApplyState())) {
            SohuImGroupUserBo imGroupUserBo = SohuImGroupUserBo.builder().build();
            imGroupUserBo.setGroupId(imApply.getTargetId());
            imGroupUserBo.setUserId(imApply.getApplyUserId());
            imGroupUserBo.setPermissionType(ImGroupPermissionType.group_user.name());
            imGroupUserBo.setCreateTime(new Date());
            imGroupUserBo.setUpdateTime(new Date());
            imGroupUserBo.setInviteUser(imApply.getInviteUser());

            if (StrUtil.isNotBlank(imApply.getChannelCode())) {
                sohuImGroupChannelUserService.invite(imApply.getTargetId(), imApply.getApplyUserId(), imApply.getChannelCode(), imApply.getInviteUser());
            }

            List<SysDictData> dictDataList = remoteDictService.selectDictDataByType(DictEnum.CustomerService.getKey());
            Map<Long, String> dictMap = new HashMap<>();
            if (CollUtil.isNotEmpty(dictDataList)) {
                for (SysDictData dictData : dictDataList) {
                    if (StrUtil.isBlankIfStr(dictData.getDictValue()) || StrUtil.isBlankIfStr(dictData.getDictLabel())) {
                        continue;
                    }
                    dictMap.put(Long.parseLong(dictData.getDictValue()), dictData.getDictLabel());
                }
            }
            String diyLabel = dictMap.get(imGroupUserBo.getUserId());
            imGroupUserBo.setGroupUserNickName(diyLabel);
            // 添加群成员
            sohuImGroupUserService.insertByBo(imGroupUserBo);
            // 绑定拉新关系是否开启，开启了则要保存拉新渠道关系关联
            if (StrUtil.equalsAnyIgnoreCase(imApply.getApplyType(), "group")) {
                bindUserInvite(imApply.getTargetId(), imApply.getInviteUser(), imApply.getApplyUserId());
                // 主动发送进群消息
                //ImSocketResponseUtil.sendJoinGroupCommandMessage(imApply.getTargetId(), imApply.getApplyUserId(), imApply.getChannelCode());
            }

            ImGroupAddUserEvent groupAddUserEvent = new ImGroupAddUserEvent(this, imApply.getTargetId(), Lists.newArrayList(imApply.getApplyUserId()));
            applicationEventPublisher.publishEvent(groupAddUserEvent);
        }
        imApply.setApplyState(bo.getApplyState());
        this.baseMapper.updateById(imApply);
        return Boolean.TRUE;
    }

    /**
     * 绑定拉新关系
     *
     * @param groupId      群ID
     * @param inviteUserId 邀请人用户ID
     * @param userId       被邀请人用户ID
     */
    private void bindUserInvite(Long groupId, Long inviteUserId, Long userId) {
        SohuImGroup sohuImGroup = sohuImCommonService.query(groupId);
        SohuImGroupChannelVo channelVo = sohuImGroupChannelService.init(groupId, sohuImGroup.getUserId(), SohuImGroupChannelBo.channelType_bindUser);
        if (channelVo == null || !StringUtils.equalsAnyIgnoreCase(channelVo.getState(), CommonState.OnShelf.getCode())) {
            log.info("用户进群，绑定拉新关系开关已关闭，无需处理绑定，群ID：{},邀请人：{},进群用户ID：{}", groupId, inviteUserId, userId);
            return;
        }
        // 绑定拉新数据
        sohuImGroupChannelUserService.invite(groupId, userId, channelVo.getChannelCode(), inviteUserId);
    }

}
