package com.sohu.im.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.busyorder.api.model.SohuBusyTaskSiteModel;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.ApplyStateEnum;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.DictEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.SohuImUtil;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.vo.UserBaseVo;
import com.sohu.common.mybatis.annotation.J2CacheEvict;
import com.sohu.common.mybatis.annotation.J2Cacheable;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.mybatis.db.CacheMgr;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.bo.*;
import com.sohu.im.api.constant.ImCacheConstants;
import com.sohu.im.api.enums.*;
import com.sohu.im.api.vo.*;
import com.sohu.im.appevent.event.ImGroupAddUserEvent;
import com.sohu.im.domain.*;
import com.sohu.im.mapper.*;
import com.sohu.im.server.Const;
import com.sohu.im.service.*;
import com.sohu.im.utfil.ImErrorUtil;
import com.sohu.im.utfil.ImGroupUtil;
import com.sohu.im.utfil.ImSendUtil;
import com.sohu.im.utfil.ImUnReadUtil;
import com.sohu.middle.api.service.RemoteMiddleFriendService;
import com.sohu.middle.api.service.RemoteMiddleInviteService;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteDictService;
import com.sohu.system.api.RemoteUserService;
import com.sohu.system.api.domain.SysDictData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * im群用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuImGroupUserServiceImpl implements ISohuImGroupUserService {

    private final SohuImGroupUserMapper baseMapper;
    private final SohuImGroupMapper sohuImGroupMapper;
    private final SohuImApplyMapper sohuImApplyMapper;
    private final SohuImGroupTransferMapper sohuImGroupTransferMapper;
    private final SohuImChatMessageMapper sohuImChatMessageMapper;
    private final SohuImGroupChannelUserMapper sohuImGroupChannelUserMapper;
    private final SohuImGroupUserRecordMapper sohuImGroupUserRecordMapper;

    private final ApplicationEventPublisher applicationEventPublisher;

    private final SohuImGroupTaskService sohuImGroupTaskService;
    private final ISohuImGroupChannelService sohuImGroupChannelService;
    private final ISohuImGroupChannelUserService sohuImGroupChannelUserService;
    private final ISohuImCommonService sohuImCommonService;

    private final ImGroupUtil imGroupUtil;
    private final ImSendUtil imSendUtil;
    private final AsyncConfig asyncConfig;

    @DubboReference
    private RemoteMiddleFriendService remoteMiddleFriendService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteMiddleInviteService remoteMiddleInviteService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    @DubboReference
    private RemoteBusyTaskService remoteBusyTaskService;
    @DubboReference
    private RemoteDictService remoteDictService;

    /**
     * 查询im群用户
     */
    @Override
    public SohuImGroupUserVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询im群用户列表
     */
    @Override
    public TableDataInfo<SohuImGroupUserVo> queryPageList(SohuImGroupUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuImGroupUser> lqw = buildQueryWrapper(bo);
        Page<SohuImGroupUserVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuImGroupUserVo> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfoUtils.build();
        }
        imGroupUtil.buildGroupUser(records, LoginHelper.getUserId(), bo.getGroupId());
        result.setRecords(records);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询im群用户列表
     */
    @Override
    public List<SohuImGroupUserVo> queryList(SohuImGroupUserBo bo) {
        LambdaQueryWrapper<SohuImGroupUser> lqw = buildQueryWrapper(bo);
        List<SohuImGroupUserVo> records = baseMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(records)) {
            return CollUtil.newArrayList();
        }
        imGroupUtil.buildGroupUser(records, LoginHelper.getUserId(), bo.getGroupId());
        return records;
    }

    private LambdaQueryWrapper<SohuImGroupUser> buildQueryWrapper(SohuImGroupUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuImGroupUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getGroupId() != null, SohuImGroupUser::getGroupId, bo.getGroupId());
        lqw.eq(bo.getUserId() != null, SohuImGroupUser::getUserId, bo.getUserId());
        lqw.eq(bo.getForbid() != null, SohuImGroupUser::isForbid, bo.getForbid());
        lqw.eq(StringUtils.isNotBlank(bo.getNotifyLevel()), SohuImGroupUser::getNotifyLevel, bo.getNotifyLevel());
        lqw.eq(StringUtils.isNotBlank(bo.getPermissionType()), SohuImGroupUser::getPermissionType, bo.getPermissionType());
        lqw.eq(bo.getCardId() != null, SohuImGroupUser::getCardId, bo.getCardId());
        lqw.last("ORDER BY FIELD(permission_type, '" + ImGroupPermissionType.group_leader + "', '" + ImGroupPermissionType.group_admin + "', '" + ImGroupPermissionType.group_user + "'), create_time ASC");
        return lqw;
    }

    /**
     * 新增im群用户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SohuImGroupUserBo bo) {
        SohuImGroupVo sohuImGroup = sohuImCommonService.queryGroup(bo.getGroupId());
        // 检查群是否存在以及是否被封禁
        ImErrorUtil.checkGroupState(sohuImGroup);

        SohuImGroupUser exist = baseMapper.selectOne(SohuImGroupUser::getGroupId, bo.getGroupId(), SohuImGroupUser::getUserId, bo.getUserId());
        if (Objects.nonNull(exist)) {
            baseMapper.deleteById(exist);
        }

        if (StrUtil.equalsAnyIgnoreCase(sohuImGroup.getGroupType(), ImGroupType.groupFormCustom.name()) && (sohuImGroup.getPid() != null && sohuImGroup.getPid() > 0L)) {
            // 判断是否任务客户群
            List<SohuImGroup> sohuImGroups = sohuImGroupMapper.selectList(SohuImGroup::getPid, sohuImGroup.getPid());
            List<Long> groupIds = sohuImGroups.stream().map(SohuImGroup::getId).collect(Collectors.toList());
            List<SohuImGroupUserVo> subGroupUserVos = selectList(bo.getUserId(), groupIds);
            if (CollUtil.isNotEmpty(subGroupUserVos)) {
                throw new ServiceException("你已加入愿望群聊，加入失败");
            }
        }
        SohuImGroupUser add = BeanUtil.toBean(bo, SohuImGroupUser.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            // 更新群成员数量
            sohuImGroupMapper.addGroupUserNum(bo.getGroupId());

            if (bo.getInviteUser() != null && bo.getInviteUser() > 0L) {
                // 保存邀请数据
                remoteMiddleInviteService.saveOrQuery(bo.getInviteUser(), bo.getUserId());
            }

            // 绑定到群组
            imGroupUtil.bindGroup(add.getUserId(), sohuImGroup.getId());
            evict(sohuImGroup.getId(), add.getUserId());

            // 群未读消息增加
            Map<String, Object> userUnReadMap = ImUnReadUtil.getUserUnReadMap(bo.getUserId());
            long countGroup = sohuImChatMessageMapper.countGroup(ImSessionTypeEnum.group.name(), bo.getGroupId());
            userUnReadMap.put(ImSessionTypeEnum.group.getCode() + StrPool.DASHED + sohuImGroup.getId(), countGroup);
            ImUnReadUtil.setUserUnReadMap(bo.getUserId(), userUnReadMap);

            evictUserAllGroup(bo.getUserId());

            LoginUser joinGroupUser = remoteUserService.queryById(bo.getUserId());

            log.info("新增群用户，{}", JSONUtil.toJsonStr(bo));


            // 发送入群欢迎语
            String joinGroupUserName = StrUtil.isBlankIfStr(joinGroupUser.getNickname()) ? joinGroupUser.getUsername() : joinGroupUser.getNickname();

            if (StrUtil.equalsAnyIgnoreCase(sohuImGroup.getGroupType(), ImGroupType.groupForm.name(), ImGroupType.groupFormCustom.name())) {
                joinGroupUserName = StringUtils.sensitive(1, joinGroupUserName, 2, false);
            }
            if (!CalUtils.isNullOrZero(bo.getInviteUser())) {
                // 邀请人
                LoginUser inviteUser = remoteUserService.queryById(bo.getInviteUser());
                SohuImGroupUserVo operatorGroupUser = this.selectOne(bo.getGroupId(), bo.getInviteUser());

                String preContent = StringUtils.getValidString(operatorGroupUser.getGroupUserNickName(), inviteUser.getNickname(), inviteUser.getUsername());

                if (StrUtil.equalsAnyIgnoreCase(sohuImGroup.getGroupType(), ImSessionTypeEnum.groupForm.name(), ImSessionTypeEnum.groupFormCustom.name())) {
                    preContent = StringUtils.sensitive(1, preContent, 2, false);
                    joinGroupUserName = StringUtils.sensitive(1, joinGroupUserName, 2, false);
                }

                String content = preContent + " " + "邀请 " + joinGroupUserName + "进入群聊";
                ImChatRequestBo requestBo = new ImChatRequestBo();
                requestBo.setReceiverId(sohuImGroup.getId());
                requestBo.setSessionType(sohuImGroup.getGroupType());
                requestBo.setCommandType(ImCommandTypeEnum.joinGroup.getCode());
                requestBo.setContent(content);
                requestBo.setMessageType(ImMessageTypeEnum.Command.getCode());
                requestBo.setChatId(System.nanoTime());
                requestBo.setLocalId(RandomUtil.randomString(16));
                // 发送socket 命令消息
                //imSendUtil.sendCommandMessage(sohuImGroup.getUserId(), requestBo);
                imSendUtil.serverActiveSend(sohuImGroup.getUserId(), requestBo,true);
            }

            String content = "@" + joinGroupUserName + " " + (StrUtil.isBlankIfStr(sohuImGroup.getGroupGreet()) ? "欢迎你进群" : sohuImGroup.getGroupGreet());
            ImChatRequestBo requestBo = new ImChatRequestBo();
            requestBo.setReceiverId(sohuImGroup.getId());
            requestBo.setSessionType(sohuImGroup.getGroupType());
            requestBo.setCommandType(ImCommandTypeEnum.groupGreet.getCode());
            requestBo.setMessageType(ImMessageTypeEnum.Command.getCode());
            requestBo.setChatId(System.nanoTime());
            requestBo.setLocalId(RandomUtil.randomString(16));
            requestBo.setContent(content);
            // 发送socket 命令消息
            imSendUtil.serverActiveSend(sohuImGroup.getUserId(), requestBo,true);
            //imSendUtil.sendCommandMessage(sohuImGroup.getUserId(), requestBo);

            // 发送延时消息,并绑定用户标签及行业标签
            JSONObject parseObj = JSONUtil.parseObj(sohuImGroup.getGroupExt());
            String taskChildNumber = parseObj.getStr("taskNumber");
            Long deliveryDay = 0L;
            if (StrUtil.isNotEmpty(taskChildNumber)) {
                SohuBusyTaskSiteModel sohuBusyTaskSiteModel = remoteBusyTaskService.queryMasterTaskNumber(taskChildNumber);
                if (Objects.nonNull(sohuBusyTaskSiteModel)) {
                    deliveryDay = Long.valueOf(sohuBusyTaskSiteModel.getDeliveryDay());
                }
                log.info("群id:{},用户id:{}", bo.getGroupId(), bo.getUserId());
                JSONObject jsonObject = new JSONObject();
                jsonObject.set("groupId", bo.getGroupId());
                jsonObject.set("userId", bo.getUserId());
                MqMessaging mqMessaging = new MqMessaging(jsonObject.toString(), "join_group_user");
                Long deliveryTime = deliveryDay * 60 * 60L;
                log.info("MqMessaging:{},deliveryTime:{}", JSONUtil.toJsonStr(mqMessaging), deliveryTime);
                RedisUtils.delayQueue(JSONUtil.toJsonStr(mqMessaging), deliveryTime, TimeUnit.SECONDS);
                // 绑定用户标签及行业标签
                remoteBusyTaskService.insertUserLabelByTaskNumber(sohuBusyTaskSiteModel.getMasterTaskNumber(), bo.getUserId());
            }
            // 异步记录用户退群记录
            CompletableFuture.runAsync(() -> this.recordImGroupUser(bo.getUserId(), bo.getGroupId(), Constants.ONE), asyncConfig.getAsyncExecutor());
        }
        return flag;
    }

    /**
     * 发送延时消息
     *
     * @param bo          SohuImGroupUserBo
     * @param sohuImGroup SohuImGroup
     */
    private void sendDelayMsg(SohuImGroupUserBo bo, SohuImGroup sohuImGroup) {

    }

    @Override
    public Boolean insertBatch(List<SohuImGroupUser> sohuImGroupUsers) {
        if (CollUtil.isEmpty(sohuImGroupUsers)) {
            return Boolean.FALSE;
        }
        log.info("批量新增群用户，{}", sohuImGroupUsers.size());
        SohuImGroupUser groupUser = sohuImGroupUsers.get(0);
        SohuImGroup sohuImGroup = sohuImCommonService.query(groupUser.getGroupId());
        boolean flag = baseMapper.insertBatch(sohuImGroupUsers);
        if (flag) {
            // 更新群成员数量
            sohuImGroup.setGroupUserNum(sohuImGroup.getGroupUserNum() + sohuImGroupUsers.size());
            sohuImCommonService.updateGroup(sohuImGroup);
            for (SohuImGroupUser sohuImGroupUser : sohuImGroupUsers) {
                // 绑定到群组
                imGroupUtil.bindGroup(sohuImGroupUser.getUserId(), sohuImGroup.getId());
            }
        }
        return flag;
    }

    /**
     * 新增im群用户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SohuImGroupInviteBo inviteBo) {
        Long groupId = inviteBo.getGroupId();
        SohuImGroup sohuImGroup = sohuImCommonService.query(groupId);
        ImErrorUtil.checkGroupExist(sohuImGroup);
        Long loginId = LoginHelper.getUserId();
        SohuImGroupUser imGroupUser = this.baseMapper.selectOne(SohuImGroupUser::getGroupId, groupId, SohuImGroupUser::getUserId, loginId);
        if (Objects.isNull(imGroupUser)) {
            throw new RuntimeException("非该群用户不能拉人进群");
        }
        inviteBo.setInviteUserId(loginId);
        Set<Long> userIds = JSON.parseArray(inviteBo.getUserIds().toString(), Long.class).stream().collect(HashSet::new, Set::add, Set::addAll);

        LambdaQueryWrapper<SohuImApply> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImApply::getTargetId, groupId).eq(SohuImApply::getApplyType, SohuImApply.GROUP).in(SohuImApply::getApplyUserId, userIds);
        List<SohuImApply> applyList = sohuImApplyMapper.selectList(lqw);
        Map<Long, SohuImApply> existApplyMap = new HashMap<>();
        if (CollUtil.isNotEmpty(applyList)) {
            for (SohuImApply apply : applyList) {
                existApplyMap.put(apply.getApplyUserId(), apply);
            }
        }
        if (sohuImGroup.getNeedConfirm() != null && sohuImGroup.getNeedConfirm() && StrUtil.equalsAnyIgnoreCase(imGroupUser.getPermissionType(), ImGroupPermissionType.group_user.name())) {
            //进群需要确认
            List<SohuImApply> imApplyList = new ArrayList<>();
            for (Long userId : userIds) {
                if (Objects.nonNull(existApplyMap.get(userId))) {
                    continue;
                }
                SohuImApply imApply = new SohuImApply();
                imApply.setApplyUserId(userId);
                imApply.setTargetId(groupId);
                imApply.setApplyType(SohuImApply.GROUP);
                imApply.setApplyState(CommonState.WaitApprove.getCode());
                imApply.setCreateTime(new Date());
                imApply.setUpdateTime(new Date());
                imApply.setInviteUser(inviteBo.getInviteUserId());
                imApplyList.add(imApply);
            }
            if (CollUtil.isNotEmpty(imApplyList)) {
                sohuImApplyMapper.insertBatch(imApplyList);
            }
        } else {
            int count = 0;
            List<SohuImGroupUser> groupUserList = new ArrayList<>();

            List<SysDictData> dictDataList = remoteDictService.selectDictDataByType(DictEnum.CustomerService.getKey());
            Map<Long, String> dictMap = new HashMap<>();
            if (CollUtil.isNotEmpty(dictDataList)) {
                for (SysDictData dictData : dictDataList) {
                    if (StrUtil.isBlankIfStr(dictData.getDictValue()) || StrUtil.isBlankIfStr(dictData.getDictLabel())) {
                        continue;
                    }
                    dictMap.put(Long.parseLong(dictData.getDictValue()), dictData.getDictLabel());
                }
            }

            for (Long userId : userIds) {
                count++;
                if (Objects.nonNull(existApplyMap.get(userId))) {
                    SohuImApply imApply = existApplyMap.get(userId);
                    sohuImApplyMapper.deleteById(imApply);
                    continue;
                }
                SohuImGroupUser groupUser = new SohuImGroupUser(inviteBo.getGroupId(), userId, ImGroupPermissionType.group_user.name());
                SohuImGroupUserBo groupUserBo = SohuImGroupUserBo.builder().build();
                groupUserBo.setGroupId(inviteBo.getGroupId());
                groupUserBo.setUserId(userId);
                groupUserBo.setPermissionType(ImGroupPermissionType.group_user.name());
                String diyLabel = dictMap.get(userId);
                groupUserBo.setInviteUser(inviteBo.getInviteUserId());
                groupUserBo.setGroupUserNickName(diyLabel);
                try {
                    this.insertByBo(groupUserBo);
                    groupUserList.add(groupUser);
                } catch (Exception e) {
                    String message = e.getMessage();
                    throw new RuntimeException(StrUtil.isBlankIfStr(message) ? "邀请用户进群失败" : message);
                }
            }
            if (CollUtil.isNotEmpty(groupUserList)) {
                sohuImGroup.setGroupUserNum(sohuImGroup.getGroupUserNum() + groupUserList.size());
                sohuImCommonService.updateGroup(sohuImGroup);

                List<Long> addUserIds = groupUserList.stream().map(SohuImGroupUser::getUserId).distinct().collect(Collectors.toList());
                ImGroupAddUserEvent groupAddUserEvent = new ImGroupAddUserEvent(this, groupId, addUserIds);
                applicationEventPublisher.publishEvent(groupAddUserEvent);
            }
        }

        return Boolean.TRUE;
    }

    /**
     * 修改im群用户
     */
    @Override
    @J2CacheEvict(region = ImCacheConstants.GROUP_USER, keys = {"#bo.groupId + '-' + #bo.userId"}, allEntries = true)
    public Boolean updateByBo(SohuImGroupUserBo bo) {
        SohuImGroupUser update = BeanUtil.toBean(bo, SohuImGroupUser.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuImGroupUser entity) {
        // 因为updateByBo 方法的清除缓存注解不生效，只能手动清除下
        evict(entity.getGroupId(), entity.getUserId());
    }

    /**
     * 批量删除im群用户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        List<SohuImGroupUser> groupUsers = baseMapper.selectBatchIds(ids);
        if (CollUtil.isEmpty(groupUsers)) {
            return Boolean.FALSE;
        }
        Long loginId = LoginHelper.getUserId();
        Long groupId = groupUsers.get(0).getGroupId();

        // 查询操作人的权限
        SohuImGroupUser groupUserLogin = baseMapper.selectOne(SohuImGroupUser::getGroupId, groupId, SohuImGroupUser::getUserId, loginId);
        if (Objects.isNull(groupUserLogin)) {
            throw new ServiceException(MessageUtils.message("no.power"));
        }
        if (!StrUtil.equalsAnyIgnoreCase(groupUserLogin.getPermissionType(), ImGroupPermissionType.group_leader.name(), ImGroupPermissionType.group_admin.name())) {
            throw new RuntimeException("非群主或管理员不能删除群成员");
        }
        if (StrUtil.equalsAnyIgnoreCase(groupUserLogin.getPermissionType(), ImGroupPermissionType.group_leader.name())) {
            // 当前操作人群主
            if (groupUsers.stream().anyMatch(groupUser -> Objects.equals(loginId, groupUser.getUserId()))) {
                throw new RuntimeException("群主不能被删除");
            }
        }
        if (StrUtil.equalsAnyIgnoreCase(groupUserLogin.getPermissionType(), ImGroupPermissionType.group_admin.name())) {
            // 当前操作人群管理员
            if (groupUsers.stream().anyMatch(groupUser -> StrUtil.equalsAnyIgnoreCase(groupUser.getPermissionType(), ImGroupPermissionType.group_leader.name()))) {
                throw new ServiceException(MessageUtils.message("no.power"));
            }
            if (groupUsers.stream().anyMatch(groupUser -> StrUtil.equalsAnyIgnoreCase(groupUser.getPermissionType(), ImGroupPermissionType.group_admin.name()))) {
                throw new ServiceException(MessageUtils.message("no.power"));
            }
        }

        SohuImGroup sohuImGroup = sohuImCommonService.query(groupId);
        for (SohuImGroupUser groupUser : groupUsers) {
            SohuImRemoveGroupUserBo bo = SohuImRemoveGroupUserBo.builder().id(groupUser.getId()).build();
            bo.setRemoveActive(false);
            bo.setSendMsg(true);
            bo.setOperatorUserId(loginId);
            this.removeUser(bo);
        }
        sohuImCommonService.updateGroup(sohuImGroup);
        return Boolean.TRUE;
    }

    @Override
    @J2Cacheable(region = ImCacheConstants.USER_ALL_GROUP, key = "#userId")
    public List<Long> queryGroupIds(Long userId) {
        LambdaQueryWrapper<SohuImGroupUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SohuImGroupUser::getUserId, userId);
        List<SohuImGroupUser> list = this.baseMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().map(SohuImGroupUser::getGroupId).collect(Collectors.toList());
    }

    @Override
    public List<Long> queryGroupUserIds(Long groupId) {
        List<SohuImGroupUserVo> list = this.queryGroupUsers(groupId);
        return CollUtil.isEmpty(list) ? new ArrayList<>() : list.stream().map(SohuImGroupUserVo::getUserId).collect(Collectors.toList());
    }

    @Override
    public List<SohuImGroupUserVo> queryGroupUsers(Long groupId) {
        LambdaQueryWrapper<SohuImGroupUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SohuImGroupUser::getGroupId, groupId);
        return baseMapper.selectVoList(queryWrapper);
    }

    @Override
    public List<Long> queryGroupOnlineUserIds(Long groupId) {
        List<Long> userIds = this.queryGroupUserIds(groupId);
        if (CollUtil.isEmpty(userIds)) {
            return null;
        }
        List<Long> onlineUserIds = new ArrayList<>();
        for (Long userId : userIds) {
            Object cacheObject = RedisUtils.getCacheObject(Const.ICE_LIVE_USER_CACHE_KEY + userId);
            if (cacheObject != null && Long.parseLong(cacheObject.toString()) > 0L) {
                onlineUserIds.add(userId);
            }
        }
        return onlineUserIds;
    }

    @Override
    public List<Long> queryGroupOfflineUserIds(Long groupId) {
        List<Long> userIds = this.queryGroupUserIds(groupId);
        if (CollUtil.isEmpty(userIds)) {
            return null;
        }
        List<Long> offlineUserIds = new ArrayList<>();
        for (Long userId : userIds) {
            Object cacheObject = RedisUtils.getCacheObject(Const.ICE_LIVE_USER_CACHE_KEY + userId);
            if (!(cacheObject == null || Long.parseLong(cacheObject.toString()) <= 0L)) {
                offlineUserIds.add(userId);
            }
        }
        return offlineUserIds;
    }

    @Override
    public Boolean batchEdit(SohuImGroupUserBo bo) {
        Long groupId = bo.getGroupId();
        SohuImGroup sohuImGroup = sohuImCommonService.query(groupId);
        Long loginId = LoginHelper.getUserId();
        if (!Objects.equals(loginId, sohuImGroup.getUserId())) {
            SohuImGroupUser groupUser = this.baseMapper.selectOne(SohuImGroupUser::getGroupId, groupId, SohuImGroupUser::getUserId, loginId);
            if (!StrUtil.equalsAnyIgnoreCase(groupUser.getPermissionType(), ImGroupPermissionType.group_leader.name(), ImGroupPermissionType.group_admin.name())) {
                throw new ServiceException(MessageUtils.message("no.power"));
            }
        }
        Set<Long> ids = JSON.parseArray(bo.getIds().toString(), Long.class).stream().collect(HashSet::new, Set::add, Set::addAll);
        LambdaUpdateWrapper<SohuImGroupUser> lq = new LambdaUpdateWrapper<>();
        lq.in(SohuImGroupUser::getId, ids);
        if (StrUtil.isNotBlank(bo.getPermissionType())) {
            lq.set(SohuImGroupUser::getPermissionType, bo.getPermissionType());
        }
        if (bo.getForbid() != null) {
            lq.set(SohuImGroupUser::isForbid, bo.getForbid());
        }
        this.baseMapper.update(new SohuImGroupUser(), lq);
        List<SohuImGroupUser> groupUsers = this.baseMapper.selectBatchIds(ids);
        for (SohuImGroupUser groupUser : groupUsers) {
            evict(groupId, groupUser.getUserId());
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeUser(SohuImRemoveGroupUserBo bo) {
        SohuImGroupUser groupUser = null;
        if (CalUtils.isNullOrZero(bo.getId())) {
            groupUser = baseMapper.selectOne(SohuImGroupUser::getGroupId, bo.getGroupId(), SohuImGroupUser::getUserId, bo.getUserId());
        } else {
            groupUser = baseMapper.selectById(bo.getId());
        }
        if (Objects.isNull(groupUser)) {
            log.info("退群失败，该用户不存在群里面,{}", JSONUtil.toJsonStr(bo));
            return Boolean.TRUE;
        }
        if (StrUtil.equalsAnyIgnoreCase(groupUser.getPermissionType(), ImGroupPermissionType.group_leader.name())) {
            throw new ServiceException(MessageUtils.message("group.leader.can.not.remove"));
        }

        bo.setGroupId(groupUser.getGroupId());
        bo.setUserId(groupUser.getUserId());
        bo.setGroupUserNickName(groupUser.getGroupUserNickName());

        CacheMgr.evict(ImCacheConstants.IM_UN_READ, SohuImUtil.imRedisKey(ImSessionTypeEnum.group.getCode(), groupUser.getUserId(), groupUser.getGroupId()));
        Long groupId = groupUser.getGroupId();
        // 群信息
        SohuImGroup sohuImGroup = sohuImCommonService.query(groupId);
        ImErrorUtil.checkGroupExist(sohuImGroup);
        bo.setGroupHeadUserId(sohuImGroup.getUserId());
        // 检测是否可以移除群成员
        if (BooleanUtil.isFalse(bo.isIgnoreCheck())) {
            SohuImGroupUser operatorGroupUser = baseMapper.selectOne(SohuImGroupUser::getGroupId, groupId, SohuImGroupUser::getUserId, bo.getOperatorUserId());
            checkCanRemoveUser(sohuImGroup, operatorGroupUser, bo);
        }
        String sessionType = StrUtil.isNotBlank(bo.getSessionType()) ? bo.getSessionType() : sohuImGroup.getGroupType();
        bo.setSessionType(sessionType);
        // 删除申请记录
        sohuImApplyMapper.deleteGroupUser(ImSessionTypeEnum.group.getCode(), groupUser.getGroupId(), groupUser.getUserId());
        // 检查是否是普通群
        if (StrUtil.equalsAnyIgnoreCase(sessionType, ImGroupType.group.name())) {
            // 子群列表
            List<SohuImGroup> subImGroups = sohuImGroupMapper.selectList(SohuImGroup::getPid, sohuImGroup.getId());
            if (CollUtil.isNotEmpty(subImGroups)) {
                List<Long> subGroupIds = new ArrayList<>();
                Map<Long, SohuImGroup> map = new HashMap<>();
                for (SohuImGroup subImGroup : subImGroups) {
                    subGroupIds.add(subImGroup.getId());
                    map.put(subImGroup.getId(), subImGroup);
                }
                // 查询退群用户在所有子群信息
                List<SohuImGroupUserVo> subGroupUserVos = selectList(groupUser.getUserId(), subGroupIds);
                if (CollUtil.isNotEmpty(subGroupUserVos)) {
                    for (SohuImGroupUserVo subGroupUserVo : subGroupUserVos) {
                        this.baseMapper.deleteById(subGroupUserVo.getId());
                        // 清除与这个群的未读消息缓存
                        ImUnReadUtil.clearUnReadCount(ImSessionTypeEnum.group.getCode(), subGroupUserVo.getUserId(), subGroupUserVo.getGroupId());
                        SohuImGroup imGroup = map.get(subGroupUserVo.getGroupId());
                        SohuImRemoveGroupUserBo childBo = SohuImRemoveGroupUserBo.builder().build();
                        // 发送退群socket消息
                        childBo.setSessionType(imGroup.getGroupType());
                        childBo.setGroupHeadUserId(imGroup.getUserId());
                        childBo.setGroupUserNickName(subGroupUserVo.getGroupUserNickName());
                        childBo.setUserId(subGroupUserVo.getUserId());
                        childBo.setRemoveActive(bo.isRemoveActive());
                        // 发送子群退群socket消息
                        sendGroupUserRemoveMsg(childBo);
                    }
                    log.info("子群退群成功");
                }
            }
        }
        // 删除渠道邀请记录
        sohuImGroupChannelUserService.delete(groupUser.getGroupId(), groupUser.getUserId());
        int count = Math.max((sohuImGroup.getGroupUserNum() - 1), 0);
        sohuImGroup.setGroupUserNum(count);
        sohuImCommonService.updateGroup(sohuImGroup);
        // 异步记录用户退群记录
        SohuImGroupUser finalGroupUser = groupUser;
        CompletableFuture.runAsync(() -> this.recordImGroupUser(finalGroupUser.getUserId(), finalGroupUser.getGroupId(), Constants.ZERO), asyncConfig.getAsyncExecutor());
        // 清除与这个群的未读消息缓存
        ImUnReadUtil.clearUnReadCount(ImSessionTypeEnum.group.getCode(), groupUser.getUserId(), groupId);
        // 发送主群退群socket消息
        sendGroupUserRemoveMsg(bo);
        // 清除用户所有群缓存
        evictUserAllGroup(bo.getUserId());

        log.info("退群成功，群ID：{}，退群用户ID：{}", groupId, groupUser.getUserId());
        evict(groupId, groupUser.getUserId());
        return this.baseMapper.deleteById(groupUser.getId()) > 0;
    }

    /**
     * 记录用户进/退群记录
     */
    private void recordImGroupUser(Long userId, Long groupId, Integer isInGroup) {
        SohuImGroupUserRecord entity = new SohuImGroupUserRecord();
        entity.setGroupId(groupId);
        entity.setUserId(userId);
        entity.setJoinGroupTime(new Date());
        entity.setIsInGroup(isInGroup);
        sohuImGroupUserRecordMapper.insert(entity);
    }

    /**
     * 检测是否可以移除群成员
     *
     * @param sohuImGroup
     * @param operatorGroupUser 操作者
     * @param bo
     */
    private void checkCanRemoveUser(SohuImGroup sohuImGroup, SohuImGroupUser operatorGroupUser, SohuImRemoveGroupUserBo bo) {
        Long groupId = sohuImGroup.getId();
        Long userId = bo.getUserId();
        if (StrUtil.equalsAnyIgnoreCase(sohuImGroup.getGroupType(), ImGroupType.groupTask.name())) {
            bo.setIgnoreCheck(false);
        }
        if (bo.isIgnoreCheck()) {
            return;
        }
        if (StrUtil.equalsAnyIgnoreCase(sohuImGroup.getGroupType(), ImGroupType.group.name())) {
            if (BooleanUtil.isTrue(bo.isRemoveActive())) {
                return;
            }
            if (!StrUtil.equalsAnyIgnoreCase(operatorGroupUser.getPermissionType(), ImGroupPermissionType.group_leader.name(), ImGroupPermissionType.group_admin.name())) {
                throw new ServiceException(MessageUtils.message("no.power"));
            }
        } else if (StrUtil.equalsAnyIgnoreCase(sohuImGroup.getGroupType(), ImGroupType.groupTask.name())) {
            SohuImGroupTaskExtVo groupTaskExtVo = sohuImGroupTaskService.groupTaskOver(groupId);
            if (Objects.nonNull(groupTaskExtVo) && (Objects.equals(userId, groupTaskExtVo.getTaskReceUserId()) || Objects.equals(userId, groupTaskExtVo.getTaskPublishUserId()))) {
                // 当前任务正在进行中，不能退出任务群聊
                // todo
                throw new ServiceException(MessageUtils.message("group.task.rece.going"));
            }
        } else if (StrUtil.equalsAnyIgnoreCase(sohuImGroup.getGroupType(), ImGroupType.groupForm.name()) && StrUtil.isNotBlank(sohuImGroup.getGroupExt())) {
            SohuImGroupFormExtVo formExtVo = JSONUtil.toBean(sohuImGroup.getGroupExt(), SohuImGroupFormExtVo.class);
            Long taskPublishUserId = formExtVo.getTaskPublishUserId();
            List<Long> taskReceUserIds = formExtVo.getTaskReceUserIds();
            if (Objects.equals(taskPublishUserId, userId)) {
                throw new ServiceException("当前群聊不能移除发单方");
            }
            if (taskReceUserIds.contains(userId)) {
                throw new ServiceException("接单方不能踢出群聊");
            }
        } else if (StrUtil.equalsAnyIgnoreCase(sohuImGroup.getGroupType(), ImGroupType.groupFormCustom.name())) {
            SohuImGroupSubFormExtVo formExtVo = JSONUtil.toBean(sohuImGroup.getGroupExt(), SohuImGroupSubFormExtVo.class);
            Long taskPublishUserId = formExtVo.getTaskPublishUserId();
            Long taskReceUserId = formExtVo.getTaskReceUserId();
            if (Objects.equals(taskPublishUserId, userId)) {
                throw new ServiceException("当前群聊不能移除发单方");
            }
            if (Objects.equals(taskReceUserId, userId)) {
                throw new ServiceException("接单方不能踢出群聊");
            }
        }
    }

    @Override
    public Boolean userInGroup(Long groupId) {
        SohuImGroupUser imGroupUser = this.baseMapper.selectOne(SohuImGroupUser::getGroupId, groupId, SohuImGroupUser::getUserId, LoginHelper.getUserId());
        return Objects.nonNull(imGroupUser);
    }

    @Override
    public List<SohuImGroupUserVo> notInSubGroup(SohuImGroupSearchBo bo) {
        Long groupId = bo.getGroupId();
        // 子群
        SohuImGroup subImGroup = sohuImCommonService.query(groupId);
        if (Objects.isNull(subImGroup)) {
            return null;
        }
        // 大群ID
        Long pid = subImGroup.getPid();
        List<SohuImGroupUserVo> list = this.baseMapper.notInSubGroup(groupId, pid);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        Set<Long> userIds = new HashSet<>();
        for (SohuImGroupUserVo vo : list) {
            userIds.add(vo.getUserId());
        }
        Map<Long, String> friendMap = remoteMiddleFriendService.friendAliasMap(LoginHelper.getUserId(), userIds, ApplyStateEnum.pass.name());
        Map<Long, LoginUser> userMap = remoteUserService.selectMap(userIds);
        for (SohuImGroupUserVo vo : list) {
            LoginUser user = userMap.get(vo.getUserId());
            if (Objects.isNull(user)) {
                continue;
            }
            String alias = friendMap.get(vo.getUserId());
            vo.setUserName(user.getUsername());
            vo.setNickName(user.getNickname());
            vo.setAlias(alias);
            vo.setUserAvatar(StrUtil.isBlankIfStr(user.getAvatar()) ? Constants.DEFAULT_AVATAR : user.getAvatar());
        }
        if (StrUtil.isBlankIfStr(bo.getUserName())) {
            return list;
        }
        return list.stream().filter(v -> StrUtil.containsAnyIgnoreCase(v.getUserName(), bo.getUserName())).collect(Collectors.toList());
    }

    @Override
    public SohuImGroupUserVo selectOne(Long groupId, Long userId) {
        return sohuImCommonService.queryGroupUser(groupId,userId);
    }

    @Override
    public Boolean transferGroup(ImGroupTransferBo bo) {
        Long loginId = LoginHelper.getUserId();
        SohuImGroup imGroup = sohuImCommonService.query(bo.getGroupId());
        if (Objects.isNull(imGroup) || !Objects.equals(loginId, imGroup.getUserId())) {
            throw new ServiceException(MessageUtils.message("no.power"));
        }
        SohuImGroupTransfer exist = sohuImGroupTransferMapper.selectOne(SohuImGroupTransfer::getGroupId, bo.getGroupId(), SohuImGroupTransfer::getUserId, bo.getUserId());
        if (Objects.nonNull(exist)) {
            throw new ServiceException(MessageUtils.message("转让申请已提交"));
        }
        SohuImGroupTransfer transfer = new SohuImGroupTransfer();
        transfer.setGroupId(bo.getGroupId());
        transfer.setUserId(bo.getUserId());
        transfer.setState(CommonState.WaitApprove.getCode());
        sohuImGroupTransferMapper.insert(transfer);
        return Boolean.TRUE;
    }

    @Override
    public Map<Long, SohuImGroupUser> groupUserMap(Collection<Long> userIds, Long groupId) {
        if (CollUtil.isEmpty(userIds)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<SohuImGroupUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImGroupUser::getGroupId, groupId).in(SohuImGroupUser::getUserId, userIds);
        List<SohuImGroupUser> groupUsers = this.baseMapper.selectList(lqw);
        return CollUtil.isEmpty(groupUsers) ? new HashMap<>(0) : groupUsers.stream().collect(Collectors.toMap(SohuImGroupUser::getUserId, Function.identity()));
    }

    @Override
    public List<SohuImGroupUser> queryGroupUser(Long groupId, Object... permissionTypes) {
        LambdaQueryWrapper<SohuImGroupUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImGroupUser::getGroupId, groupId);
        if (permissionTypes != null) {
            lqw.in(SohuImGroupUser::getPermissionType, permissionTypes);
        }
        return this.baseMapper.selectList(lqw);
    }

    @Override
    public Map<Long, SohuImGroupUserCountVo> queryGroupUserCountMap(List<Long> groupIds) {
        List<SohuImGroupUserCountVo> list = this.baseMapper.queryGroupUserCountList(groupIds);
        return CollUtil.isEmpty(list) ? new HashMap<>() : list.stream().collect(Collectors.toMap(SohuImGroupUserCountVo::getGroupId, u -> u));
    }

    @Override
    public List<SohuImGroupUserInviteVo> inviteList(SohuImGroupUserInviteQueryBo bo) {
        Long groupId = bo.getGroupId();
        SohuImGroup sohuImGroup = sohuImCommonService.query(groupId);
        ImErrorUtil.checkGroupExist(sohuImGroup);
        log.info("inviteList request:{}", JSONUtil.toJsonStr(bo));
        if (StrUtil.equalsAnyIgnoreCase(bo.getChannelType(), ImGroupChannelEnum.BIND_USER.getCode())) {
            // 绑定拉新关系查询
            log.info("绑定拉新列表查询");
            SohuImGroupChannelVo channelVo = sohuImGroupChannelService.query(groupId, sohuImGroup.getUserId(), ImGroupChannelEnum.BIND_USER.getCode());
            if (Objects.isNull(channelVo)) {
                return new ArrayList<>();
            }
            LambdaQueryWrapper<SohuImGroupChannelUser> lqw = new LambdaQueryWrapper<>();
            lqw.eq(SohuImGroupChannelUser::getGroupId, groupId);
            lqw.eq(SohuImGroupChannelUser::getChannelCode, channelVo.getChannelCode());
            lqw.groupBy(SohuImGroupChannelUser::getInviteUser);
            List<SohuImGroupChannelUserVo> sohuImGroupChannelUserVos = sohuImGroupChannelUserMapper.selectVoList(lqw);
            if (CollUtil.isEmpty(sohuImGroupChannelUserVos)) {
                return new ArrayList<>();
            }
            List<SohuImGroupUserInviteVo> result = new ArrayList<>();
            for (SohuImGroupChannelUserVo channelUserVo : sohuImGroupChannelUserVos) {
                SohuImGroupUserInviteVo inviteVo = new SohuImGroupUserInviteVo();
                inviteVo.setChannelId(channelVo.getId());
                inviteVo.setChannelCode(channelVo.getChannelCode());
                inviteVo.setChannelType(channelVo.getChannelType());
                inviteVo.setChannelName(channelVo.getChannelName());
                inviteVo.setChannelUrl(channelVo.getChannelUrl());
                inviteVo.setChannelImage(channelVo.getChannelImage());
                inviteVo.setChannelNote(channelVo.getChannelNote());
                LoginUser user = remoteUserService.selectById(channelUserVo.getInviteUser());
                inviteVo.setUserName(StrUtil.isBlankIfStr(user.getNickname()) ? user.getUsername() : user.getNickname());
                inviteVo.setUserAvatar(StrUtil.isBlankIfStr(user.getAvatar()) ? Constants.DEFAULT_AVATAR : user.getAvatar());
                inviteVo.setUserId(channelVo.getUserId());

                SohuImGroupChannelUserBo channelUserBo = new SohuImGroupChannelUserBo();
                channelUserBo.setGroupId(groupId);
                channelUserBo.setInviteUser(channelUserVo.getInviteUser());
                channelUserBo.setChannelCode(channelVo.getChannelCode());
                log.info(JSONUtil.toJsonStr(channelUserVo));
                List<SohuImGroupChannelUserVo> userList = sohuImGroupChannelUserService.queryList(channelUserBo);
                if (CollUtil.isEmpty(userList)) {
                    inviteVo.setInviteCount(0);
                    inviteVo.setInviteList(new ArrayList<>());
                    result.add(inviteVo);
                    continue;
                }
                Set<Long> userIdList = userList.stream().map(SohuImGroupChannelUserVo::getUserId).collect(Collectors.toSet());
                Map<Long, LoginUser> inviteUserMap = remoteUserService.selectMap(userIdList);
                List<UserBaseVo> list = new LinkedList<>();
                for (SohuImGroupChannelUserVo groupChannelUserVo : userList) {
                    UserBaseVo userBaseVo = UserBaseVo.builder().build();
                    LoginUser user1 = inviteUserMap.get(groupChannelUserVo.getUserId());
                    userBaseVo.setUserName(StrUtil.isBlankIfStr(user1.getNickname()) ? user1.getUsername() : user1.getNickname());
                    userBaseVo.setUserAvatar(StrUtil.isBlankIfStr(user1.getAvatar()) ? Constants.DEFAULT_AVATAR : user1.getAvatar());
                    userBaseVo.setUserId(user1.getUserId());
                    userBaseVo.setCreateTime(groupChannelUserVo.getCreateTime());
                    list.add(userBaseVo);
                }
                inviteVo.setInviteCount(list.size());
                inviteVo.setInviteList(list);
                result.add(inviteVo);
            }
            result.sort((o1, o2) -> Integer.compare(o2.getInviteCount(), o1.getInviteCount()));
            return result;
        }

        log.info("非绑定拉新列表查询");

        SohuImGroupChannelBo channelBo = new SohuImGroupChannelBo();
        channelBo.setGroupId(groupId);
        //if (StrUtil.equalsAnyIgnoreCase(bo.getChannelType(), ImGroupChannelEnum.COMMON.getCode())) {
        channelBo.setChannelType(bo.getChannelType());
        channelBo.setChannelCode(bo.getChannelCode());
        //}
        List<SohuImGroupChannelVo> groupChannelVos = sohuImGroupChannelService.queryList(channelBo);
        if (CollUtil.isEmpty(groupChannelVos)) {
            return new ArrayList<>();
        }
        Set<Long> userIds = new HashSet<>();
        for (SohuImGroupChannelVo groupChannelVo : groupChannelVos) {
            userIds.add(groupChannelVo.getUserId());
        }
        Map<Long, LoginUser> userMap = remoteUserService.selectMap(userIds);

        List<SohuImGroupUserInviteVo> result = new LinkedList<>();
        for (SohuImGroupChannelVo channelVo : groupChannelVos) {
            SohuImGroupUserInviteVo inviteVo = new SohuImGroupUserInviteVo();
            inviteVo.setChannelId(channelVo.getId());
            inviteVo.setChannelCode(channelVo.getChannelCode());
            inviteVo.setChannelType(channelVo.getChannelType());
            inviteVo.setChannelName(channelVo.getChannelName());
            inviteVo.setChannelUrl(channelVo.getChannelUrl());
            inviteVo.setChannelImage(channelVo.getChannelImage());
            inviteVo.setChannelNote(channelVo.getChannelNote());
            LoginUser user = userMap.get(channelVo.getUserId());
            inviteVo.setUserName(StrUtil.isBlankIfStr(user.getNickname()) ? user.getUsername() : user.getNickname());
            inviteVo.setUserAvatar(StrUtil.isBlankIfStr(user.getAvatar()) ? Constants.DEFAULT_AVATAR : user.getAvatar());
            inviteVo.setUserId(channelVo.getUserId());

            SohuImGroupChannelUserBo channelUserBo = new SohuImGroupChannelUserBo();
            channelUserBo.setGroupId(groupId);
            channelUserBo.setInviteUser(channelVo.getUserId());
            if (StrUtil.equalsAnyIgnoreCase(bo.getChannelType(), ImGroupChannelEnum.COMMON.getCode())) {
                channelUserBo.setChannelCode(channelVo.getChannelCode());
            }
            List<SohuImGroupChannelUserVo> sohuImGroupChannelUserVos = sohuImGroupChannelUserService.queryList(channelUserBo);
            if (CollUtil.isNotEmpty(sohuImGroupChannelUserVos)) {
                Set<Long> userIdList = sohuImGroupChannelUserVos.stream().map(SohuImGroupChannelUserVo::getUserId).collect(Collectors.toSet());
                Map<Long, LoginUser> inviteUserMap = remoteUserService.selectMap(userIdList);
                List<UserBaseVo> list = new LinkedList<>();
                for (SohuImGroupChannelUserVo groupChannelUserVo : sohuImGroupChannelUserVos) {
                    UserBaseVo userBaseVo = UserBaseVo.builder().build();
                    LoginUser user1 = inviteUserMap.get(groupChannelUserVo.getUserId());
                    userBaseVo.setUserName(StrUtil.isBlankIfStr(user1.getNickname()) ? user1.getUsername() : user1.getNickname());
                    userBaseVo.setUserAvatar(StrUtil.isBlankIfStr(user1.getAvatar()) ? Constants.DEFAULT_AVATAR : user1.getAvatar());
                    userBaseVo.setUserId(user1.getUserId());
                    userBaseVo.setCreateTime(groupChannelUserVo.getCreateTime());
                    list.add(userBaseVo);
                }
                inviteVo.setInviteCount(list.size());
                inviteVo.setInviteList(list);
            }
            result.add(inviteVo);
        }
        return result;
    }

    @Override
    public Boolean updateNickName(SohuImGroupUserNickNameBo bo) {
        String nickName = bo.getGroupUserNickName();
        // 1. 去除字符串前后以及中间的空格
        nickName = nickName.replaceAll("\\s+", " ").trim();
        if (nickName.length() < 2 || nickName.length() > 24) {
            throw new ServiceException(MessageUtils.message("昵称请设置2~24个字符"));
        }
        // 定义允许的字符的正则表达式，只允许中文、英文、数字和下划线
        String pattern = "^[\\u4e00-\\u9fa5a-zA-Z0-9_]+$";
        // 2. 检查是否包含特殊字符
        if (!nickName.matches(pattern)) {
            throw new ServiceException(MessageUtils.message("昵称包含@<>/等字符,请修改后重试"));
        }
        SohuImGroupUserVo sohuImGroupUserVo = this.selectOne(bo.getGroupId(), bo.getUserId());
        if (Objects.isNull(sohuImGroupUserVo)) {
            throw new ServiceException(MessageUtils.message("该用户未在该群中,请确认后再试"));
        }
        SohuImGroupUserBo update = BeanUtil.toBean(bo, SohuImGroupUserBo.class);
        update.setId(sohuImGroupUserVo.getId());
        update.setGroupUserNickName(nickName);
        this.updateByBo(update);
        return Boolean.TRUE;
    }

    @Override
    @J2Cacheable(region = ImCacheConstants.GROUP_USER_COUNT, key = "#groupId")
    public Long groupUserCount(Long groupId) {
        return baseMapper.selectCount(SohuImGroupUser::getGroupId, groupId);
    }

    @Override
    public Map<Long, Long> groupUserCountMap(Collection<Long> groupIds) {
        if (CollUtil.isEmpty(groupIds)) {
            return new HashMap<>();
        }
        return fetchGroupUserCounts(groupIds);
    }

    @Override
    public List<SohuImGroupUserVo> selectList(Long userId, Collection<Long> groupIds) {
        LambdaQueryWrapper<SohuImGroupUser> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuImGroupUser::getGroupId, groupIds).eq(SohuImGroupUser::getUserId, userId);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询指定群组的用户人数，返回群ID与人数的映射
     *
     * @param groupIds 群组ID集合
     * @return 群ID与用户人数的映射
     */
    public Map<Long, Long> fetchGroupUserCounts(Collection<Long> groupIds) {
        if (CollUtil.isEmpty(groupIds)) {
            return new HashMap<>();
        }
        List<Map<String, Object>> maps = baseMapper.fetchGroupUserCounts(groupIds);
        if (CollUtil.isEmpty(maps)) {
            return new HashMap<>();
        }
        return maps.stream().collect(Collectors.toMap(row -> Long.valueOf(row.get("group_id").toString()), row -> Long.valueOf(row.get("group_user_count").toString())));
    }

    @Override
    public void evictUserAllGroup(Long userId) {
        CacheMgr.evict(ImCacheConstants.USER_ALL_GROUP, String.valueOf(userId));
    }

    @Override
    public void evict(Long groupId, Long userId) {
        CacheMgr.evict(ImCacheConstants.GROUP_USER, groupId + StrPool.DASHED + userId);
        CacheMgr.evict(ImCacheConstants.GROUP_USER_COUNT, String.valueOf(groupId));
    }

    @Override
    public List<SohuImGroupUser> groupUserList(Long groupId) {
        return baseMapper.selectList(SohuImGroupUser::getGroupId, groupId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchJoinGroup(SohuImJoinGroupBo bo) {
        if (CollUtil.isEmpty(bo.getUserIds())) {
            log.error("进群人员为空！");
            return Boolean.FALSE;
        }
        SohuImGroup sohuImGroup = sohuImCommonService.query(bo.getGroupId());
        ImErrorUtil.checkGroupState(sohuImGroup);
        if (CalUtils.isNullOrZero(bo.getInviteUser())) {
            // 如果邀请人ID为空，则为群主ID
            bo.setInviteUser(sohuImGroup.getUserId());
        }
        LambdaQueryWrapper<SohuImGroupUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImGroupUser::getGroupId, bo.getGroupId()).in(SohuImGroupUser::getUserId, bo.getUserIds());
        // 查询已经进群的
        List<SohuImGroupUser> existGroupUsers = this.baseMapper.selectList(lqw);

        // 过滤已存在的用户ID（使用Java8 Stream API）
        Set<Long> existUserIds = existGroupUsers.stream()
                .map(SohuImGroupUser::getUserId)
                .collect(Collectors.toSet());
        List<Long> newUserIds = bo.getUserIds().stream()
                .filter(userId -> !existUserIds.contains(userId))
                .collect(Collectors.toList());

        // 无新用户需要加入时直接返回
        if (CollUtil.isEmpty(newUserIds)) {
            log.info("所有用户已在群组中，无需重复加入");
            return Boolean.TRUE;
        }

        // 构建批量插入数据
        List<SohuImGroupUser> groupUsers = new ArrayList<>(newUserIds.size());
        for (Long userId : newUserIds) {
            SohuImGroupUser groupUser = new SohuImGroupUser();
            groupUser.setGroupId(sohuImGroup.getId());
            groupUser.setUserId(userId);
            groupUser.setSortIndex(3);
            groupUser.setPermissionType(ImGroupPermissionType.group_user.name());
            groupUsers.add(groupUser);
            imGroupUtil.bindGroup(userId, sohuImGroup.getId());
        }
        this.baseMapper.insertBatch(groupUsers);
        log.info("群ID：{}，{}，批量进群{}人", sohuImGroup.getId(), sohuImGroup.getName(), groupUsers.size());
        if (bo.getSendMsg() == null || BooleanUtil.isFalse(bo.getSendMsg())) {
            return Boolean.TRUE;
        }
        // 消息通知处理
        // 获取用户昵称信息（需要实现用户服务接口）
        Map<Long, String> userNicknames = remoteUserService.selectNameMap(newUserIds);

        // 构建消息内容
        String inviteName = getInviteName(bo.getInviteUser(), sohuImGroup.getUserId());
        String content = buildJoinMessage(inviteName, userNicknames.values());

        // 构建消息请求对象
        ImChatRequestBo requestBo = new ImChatRequestBo();
        requestBo.setReceiverId(bo.getGroupId());
        requestBo.setSessionType(sohuImGroup.getGroupType());
        requestBo.setMessageType(ImMessageTypeEnum.Command.getCode());
        requestBo.setCommandType(ImCommandTypeEnum.joinGroup.getCode());
        requestBo.setContent(content);
        requestBo.setChatId(System.nanoTime());
        requestBo.setLocalId(RandomUtil.randomString(16));
        // 发送系统消息
        imSendUtil.serverActiveSend(bo.getInviteUser(), requestBo, true);

        // 构建消息请求对象
        ImChatRequestBo requestBo2 = new ImChatRequestBo();
        requestBo2.setReceiverId(bo.getGroupId());
        requestBo2.setSessionType(sohuImGroup.getGroupType());
        requestBo2.setMessageType(ImMessageTypeEnum.Command.getCode());
        requestBo2.setCommandType(ImCommandTypeEnum.groupGreet.getCode());
        requestBo2.setContent("欢迎加入群聊");
        requestBo2.setChatId(System.nanoTime());
        requestBo2.setLocalId(RandomUtil.randomString(16));
        // 发送系统消息
        imSendUtil.serverActiveSend(bo.getInviteUser(), requestBo2, true);
        return Boolean.TRUE;
    }

    /**
     * 获取邀请人显示名称
     */
    private String getInviteName(Long inviteUserId, Long groupOwnerId) {
        if (Objects.equals(inviteUserId, groupOwnerId)) {
            return "群主";
        }
        LoginUser loginUser = remoteUserService.queryById(inviteUserId);
        return StringUtils.getValidString(loginUser.getNickname(), loginUser.getUsername()) + "邀请";
    }

    /**
     * 构建加入群聊消息内容
     */
    private String buildJoinMessage(String inviteName, Collection<String> nicknames) {
        StringBuilder sb = new StringBuilder(inviteName);

        List<String> nameList = new ArrayList<>(nicknames);
        int total = nameList.size();
        sb.append("邀请");
        if (total <= 3) {
            sb.append(String.join("、", nameList));
        } else {
            sb.append(String.join("、", nameList.subList(0, 3)))
                    .append("等").append(total).append("人");
        }
        sb.append("加入群聊");
        return sb.toString();
    }


    /**
     * 发送退群socket消息
     *
     * @param bo
     */
    @Async("asyncExecutor")
    public void sendGroupUserRemoveMsg(SohuImRemoveGroupUserBo bo) {
        if (BooleanUtil.isTrue(bo.isSendMsg())) {
            // 被踢出的用户
            LoginUser removeUser = remoteUserService.selectById(bo.getUserId());
            // 操作人ID
            Long operatorUserId = CalUtils.isNullOrZero(bo.getOperatorUserId()) ? bo.getGroupHeadUserId() : bo.getOperatorUserId();
            // 操作人
            LoginUser operatorUser = remoteUserService.selectById(operatorUserId);
            // 操作人在群里的信息
            SohuImGroupUserVo operatorGroupUser = selectOne(bo.getGroupId(), operatorUserId);

            String preContent = StringUtils.getValidString(operatorGroupUser.getGroupUserNickName(), operatorUser.getNickname(), operatorUser.getUsername());
            String removeContent = StringUtils.getValidString(bo.getGroupUserNickName(), removeUser.getNickname(), removeUser.getUsername());

            String content = "";
            if (BooleanUtil.isTrue(bo.isRemoveActive())) {
                content = removeContent + " 退出了群聊";
            } else {
                if (StrUtil.equalsAnyIgnoreCase(bo.getSessionType(), ImSessionTypeEnum.groupForm.name(), ImSessionTypeEnum.groupFormCustom.name())) {
                    preContent = StringUtils.sensitive(1, preContent, 2, false);
                    removeContent = StringUtils.sensitive(1, removeContent, 2, false);
                }
                // 发送socket消息
                content = preContent + " 将 " + removeContent + " 移出了群聊";
            }

            ImChatRequestBo requestBo = new ImChatRequestBo();
            requestBo.setReceiverId(bo.getGroupId());
            requestBo.setSessionType(bo.getSessionType());
            requestBo.setMessageType(ImMessageTypeEnum.Command.getCode());
            requestBo.setCommandType(ImCommandTypeEnum.kickGroup.getCode());
            requestBo.setContent(content);
            requestBo.setChatId(System.nanoTime());
            requestBo.setLocalId(RandomUtil.randomString(16));
            // 发送socket 消息
            imSendUtil.serverActiveSend(operatorUserId, requestBo, false);

//            requestBo.setSessionType(bo.getSessionType());
//            requestBo.setReceiverId(bo.getGroupId());
//            requestBo.setChatId(System.nanoTime());
//            requestBo.setLocalId(RandomUtil.randomString(16));
//            // 发送socket 消息 给被踢出的用户
//            ImChatResponseVo responseVoForSender = ImSocketResponseUtil.buildResponseVo(requestBo, operatorUser);
//            ServerTioConfig serverTioConfig = WebsocketStarter.serverTioConfig;
//            WsResponse wsResponseForReceiver = WsResponse.fromText(JSONUtil.toJsonStr(responseVoForSender), ImServerConfig.CHARSET);
//            Tio.sendToUser(serverTioConfig, String.valueOf(removeUser.getUserId()), wsResponseForReceiver);

            // 解绑用户id与群id
            imGroupUtil.unBindGroup(removeUser.getUserId(), bo.getGroupId());

        }
    }
}
