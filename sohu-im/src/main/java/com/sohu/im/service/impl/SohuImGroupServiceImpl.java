package com.sohu.im.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sohu.busyorder.api.RemoteBusyTaskReceiveService;
import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.busyorder.api.model.SohuBusyTaskSiteModel;
import com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskVo;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.ApplyStateEnum;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.DictEnum;
import com.sohu.common.core.enums.SohuBusyTaskState;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.common.mybatis.annotation.J2CacheEvict;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.mybatis.db.CacheMgr;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.bo.*;
import com.sohu.im.api.constant.ImCacheConstants;
import com.sohu.im.api.enums.*;
import com.sohu.im.api.vo.*;
import com.sohu.im.config.ImDynamicsProperties;
import com.sohu.im.domain.*;
import com.sohu.im.mapper.*;
import com.sohu.im.service.*;
import com.sohu.im.utfil.*;
import com.sohu.middle.api.service.RemoteMiddleAuditService;
import com.sohu.middle.api.service.RemoteMiddleFriendService;
import com.sohu.middle.api.vo.SohuAuditVo;
import com.sohu.system.api.RemoteDictService;
import com.sohu.system.api.RemoteUserService;
import com.sohu.system.api.bo.SysUserQueryBo;
import com.sohu.system.api.domain.SysDictData;
import com.sohu.third.aliyun.audit.constants.AliyunAuditLabelEnum;
import com.sohu.third.aliyun.audit.service.AliyunAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * im群组Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuImGroupServiceImpl implements ISohuImGroupService {

    private final SohuImGroupMapper baseMapper;
    private final SohuImGroupLogMapper sohuImGroupLogMapper;
    private final SohuImGroupUserMapper sohuImGroupUserMapper;
    private final SohuImApplyMapper sohuImApplyMapper;
    private final SohuImChatMessageMapper sohuImChatMessageMapper;
    private final SohuImChatLastMessageMapper sohuImChatLastMessageMapper;
    private final SohuImGroupDisturbMapper sohuImGroupDisturbMapper;

    private final ISohuImCommonService sohuImCommonService;
    private final SohuImGroupTaskService sohuImGroupTaskService;
    private final ISohuImGroupChannelService sohuImGroupChannelService;
    private final ISohuImGroupUserService sohuImGroupUserService;
    private final ISohuImGroupForbidTimeService sohuImGroupForbidTimeService;
    private final ISohuImGroupOrderUserService sohuImGroupOrderUserService;
    private final ImSendUtil imSendUtil;
    private final ImGroupUtil imGroupUtil;

    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteDictService remoteDictService;
    @DubboReference
    private RemoteMiddleFriendService remoteMiddleFriendService;
    @DubboReference
    private RemoteBusyTaskService remoteBusyTaskService;
    @DubboReference
    private RemoteMiddleAuditService remoteMiddleAuditService;
    @DubboReference
    private RemoteBusyTaskReceiveService remoteBusyTaskReceiveService;

    private final ImDynamicsProperties imDynamicsProperties;
    private final GroupInviteGenerator groupInviteGenerator;
    @Resource
    private AliyunAuditService aliyunAuditService;

    @Override
    public SohuImGroupVo get(Long id) {
        return sohuImCommonService.queryGroup(id);
    }

    /**
     * 查询im群组
     */
    @Override
    public SohuImGroupVo queryById(Long groupId) {
        SohuImGroupVo imGroupVo = this.get(groupId);
        ImErrorUtil.checkGroupExist(imGroupVo);
        Long loginId = LoginHelper.getUserId();
        Long count = sohuImCommonService.queryGroupUserCount(groupId);
        imGroupVo.setGroupUserNum(count <= 0L ? 0 : count.intValue());
        // 转让群不存在待审核，需求变动了
        //SohuImGroupTransfer groupTransfer = sohuImGroupTransferMapper.selectOne(SohuImGroupTransfer::getGroupId, groupId, SohuImGroupTransfer::getState, CommonState.WaitApprove.getCode());
        imGroupVo.setGroupTransfer(false);
        imGroupVo.setGroupType(StrUtil.isBlankIfStr(imGroupVo.getGroupType()) ? ImGroupType.group.name() : imGroupVo.getGroupType());
        if (!CalUtils.isNullOrZero(loginId)) {
            imGroupVo.setGroupLink(String.format(imDynamicsProperties.getGroupLinkDoMain(), groupId, loginId, loginId ^ Constants.CALCULATE));
            ImGroupTaskRole groupTaskRole = ImGroupUtil.getGroupTaskRole(loginId, imGroupVo);
            if (groupTaskRole != null) {
                imGroupVo.setSpecialRole(groupTaskRole.getCode());
            }
        }
        List<SohuImGroupForbidTimeVo> timeList = sohuImCommonService.queryGroupForbidTime(groupId);
        // 禁言时间段数组
        imGroupVo.setTimeList(timeList);
        SohuImGroupUserVo imGroupUser = sohuImCommonService.queryGroupUser(groupId, loginId);
        imGroupVo.setGroupUserNickName(Objects.nonNull(imGroupUser) ? imGroupUser.getGroupUserNickName() : null);
        if (StrUtil.isBlankIfStr(imGroupVo.getGroupUserNickName())) {
            LoginUser loginUser = remoteUserService.queryById(loginId);
            // 用户在本群的昵称
            imGroupVo.setGroupUserNickName(loginUser.getNickname());
        }
        String groupWord = getGroupWord(groupId);
        // 入群口令
        imGroupVo.setGroupWord(groupWord);
        // 表单任务详情
        buildPlanVo(imGroupVo);
        return imGroupVo;
    }

    /**
     * 查询im群组列表
     */
    @Override
    public TableDataInfo<SohuImGroupVo> queryPageList(SohuImGroupBo bo, PageQuery pageQuery) {
        return sohuImCommonService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询im群组列表
     */
    @Override
    public List<SohuImGroupVo> queryList(SohuImGroupBo bo) {
        return sohuImCommonService.queryGroupList(bo);
    }

    @Override
    public List<SohuImGroupVo> selectBatchIds(Collection<Long> idList) {
        return sohuImCommonService.queryGroupList(idList);
    }

    /**
     * 新增im群组
     */
    @Override
    public Boolean insertByBo(SohuImGroupBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        SohuImGroup add = BeanUtil.toBean(bo, SohuImGroup.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 新增im群组
     */
    @Override
    public SohuImGroup insert(SohuImGroupBo bo) {
        if (bo.getUserId() == null || bo.getUserId() <= 0L) {
            bo.setUserId(LoginHelper.getUserId());
        }
        SohuImGroup add = BeanUtil.toBean(bo, SohuImGroup.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return add;
    }

    /**
     * 修改im群组
     */
    @Override
    @J2CacheEvict(region = ImCacheConstants.GROUP, keys = {"#bo.id"})
    public Boolean updateByBo(SohuImGroupBo bo) {
        Long userId = LoginHelper.getUserId();
        SohuErrorUtil.checkLoginId(userId);
        SohuImGroupVo sohuImGroup = get(bo.getId());
        ImErrorUtil.checkGroupExist(sohuImGroup);
        LambdaQueryWrapper<SohuImGroupUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImGroupUser::getGroupId, sohuImGroup.getId());
        lqw.in(SohuImGroupUser::getPermissionType, Arrays.asList(ImGroupPermissionType.group_leader.name(), ImGroupPermissionType.group_admin.name()));
        // 查询该群的群主和管理员
        List<SohuImGroupUser> groupUsers = sohuImGroupUserMapper.selectList(lqw);
        // 判断登录者是否是该群的群主和管理员
        boolean containsUser = groupUsers.stream().anyMatch(user -> user.getUserId().equals(userId));
        if (!containsUser) {
            if (!Objects.equals(userId, sohuImGroup.getUserId())) {
                throw new ServiceException(MessageUtils.message("no.power"));
            } else {
                SohuImGroupUser groupUser = new SohuImGroupUser(bo.getId(), userId, ImGroupPermissionType.group_leader.name());
                sohuImGroupUserMapper.insert(groupUser);
                sohuImGroupUserService.evictUserAllGroup(groupUser.getUserId());
            }
        }
        SohuImGroup update = BeanUtil.toBean(bo, SohuImGroup.class);
        if (!Objects.isNull(update.getForbid()) && !update.getForbid().equals(sohuImGroup.getForbid())) {
            if (BooleanUtil.isTrue(update.getForbid())) {
                // 当开启全员禁言,需要将按时间段禁言关闭
                update.setForbidTime(false);
                imSendUtil.sendSystemMessage(sohuImGroup, imGroupUtil.exchangeUser(sohuImGroup.getId(), userId) + ImSystemMessageEnum.GROUP_ALL_FORBID_OPEN.getContent(), userId, ImCommandTypeEnum.groupForbid.getCode());
            } else {
                // 当关闭全员禁言
                imSendUtil.sendSystemMessage(sohuImGroup, imGroupUtil.exchangeUser(sohuImGroup.getId(), userId) + ImSystemMessageEnum.GROUP_ALL_FORBID_CLOSE.getContent(), userId, ImCommandTypeEnum.groupForbid.getCode());
            }
        }
        if (StrUtil.isNotBlank(update.getGroupNotice())) {
            // 校验公告是否合规
            String scanText = aliyunAuditService.scanText(HtmlUtil.cleanHtmlTag(update.getGroupNotice()), AliyunAuditLabelEnum.textCheck);
            if (StrUtil.isNotBlank(scanText)) {
                throw new ServiceException(MessageUtils.message("群公告内容涉及敏感信息，请修改后发布"));
            }
        }
        if (StrUtil.isNotBlank(update.getGroupGreet())) {
            // 校验入群欢迎语是否合规
            String scanText = aliyunAuditService.scanText(HtmlUtil.cleanHtmlTag(update.getGroupGreet()), AliyunAuditLabelEnum.textCheck);
            if (StrUtil.isNotBlank(scanText)) {
                throw new ServiceException(MessageUtils.message("内容涉及敏感信息，请修改后发布"));
            }
        }
        // 进群是否绑定拉新关系（false=否 true=是）
        if (bo.getBindUser() != null) {
            sohuImGroupChannelService.updateBindUserChannel(bo.getId(), sohuImGroup.getUserId(), bo.getBindUser());
        }
        return sohuImCommonService.updateGroup(update);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuImGroup entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除im群组
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SohuImGroup create(Object userIds) {
        if (userIds == null) {
            return null;
        }
        Set<Long> userIdsTemp = JSON.parseArray(userIds.toString(), Long.class).stream().collect(HashSet::new, Set::add, Set::addAll);
        if (userIdsTemp.isEmpty()) {
            return null;
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (Objects.isNull(loginUser)) {
            return null;
        }
        String name = StrUtil.isBlankIfStr(loginUser.getNickname()) ? loginUser.getUsername() : loginUser.getNickname();
        String groupName = name + "的群聊";
        return this.createGroup(loginUser.getUserId(), name, 0L, groupName, null, userIdsTemp, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SohuImGroup subCreate(SohuImGroupInviteBo subGroupBo) {
        Long groupId = subGroupBo.getGroupId();
        if (groupId == null || groupId <= 0L) {
            return null;
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (Objects.isNull(loginUser)) {
            return null;
        }
        SohuImGroup sohuImGroup = sohuImCommonService.query(groupId);
        ImErrorUtil.checkGroupExist(sohuImGroup);
        String name = StrUtil.isBlankIfStr(loginUser.getNickname()) ? loginUser.getUsername() : loginUser.getNickname();
        Set<Long> userIds = JSON.parseArray(subGroupBo.getUserIds().toString(), Long.class).stream().collect(HashSet::new, Set::add, Set::addAll);
        Set<Long> adminIds = new HashSet<>();
        adminIds.add(loginUser.getUserId());
        return this.createGroup(sohuImGroup.getUserId(), name, groupId, name + "的群聊", adminIds, userIds, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SohuImGroup createGroup(Long groupHeaderId, String groupHeaderName, Long pid, String groupName, Set<Long> adminIds, Set<Long> userIds, boolean sendFirstGroupMsg) {
        SohuImGroupCreateBo bo = new SohuImGroupCreateBo();
        bo.setUserId(groupHeaderId);
        bo.setGroupHeaderName(groupHeaderName);
        bo.setGroupPid(pid);
        bo.setName(groupName);
        List<SohuImGroupUserBo> groupUsers = new ArrayList<>();
        if (CollUtil.isNotEmpty(adminIds)) {
            adminIds.forEach(adminId -> {
                SohuImGroupUserBo groupUserBo = SohuImGroupUserBo.builder().userId(adminId).permissionType(ImGroupPermissionType.group_admin.name()).build();
                groupUsers.add(groupUserBo);
            });
        }
        if (CollUtil.isNotEmpty(userIds)) {
            for (Long userId : userIds) {
                if (CollUtil.isNotEmpty(adminIds) && adminIds.contains(userId)) {
                    continue;
                }
                SohuImGroupUserBo groupUserBo = SohuImGroupUserBo.builder().userId(userId).permissionType(ImGroupPermissionType.group_user.name()).build();
                groupUsers.add(groupUserBo);
            }
        }
        bo.setGroupType(ImGroupType.group.name());
        bo.setGroupUsers(groupUsers);
        bo.setSendFirstGroupMsg(sendFirstGroupMsg);
        return buildGroup(bo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SohuImGroup createGroup(Long groupHeaderId, Long pid, Set<Long> userIds, boolean sendFirstGroupMsg) {
        LoginUser loginUser = remoteUserService.queryById(groupHeaderId);
        if (Objects.isNull(loginUser)) {
            return null;
        }
        String name = StrUtil.isBlankIfStr(loginUser.getNickname()) ? loginUser.getUsername() : loginUser.getNickname();
        return this.createGroup(groupHeaderId, name, pid, name + "的群聊", null, userIds, sendFirstGroupMsg);
    }

    @Override
    public Boolean switchGroup(Long id) {
        Long userId = LoginHelper.getUserId();
        SohuImGroupVo sohuImGroupVo = this.get(id);
        if (Objects.isNull(sohuImGroupVo)) {
            return Boolean.FALSE;
        }
        SohuImGroup sohuImGroup = BeanUtil.toBean(sohuImGroupVo, SohuImGroup.class);
        if (sohuImGroup.getNeedConfirm() == null) {
            sohuImGroup.setNeedConfirm(false);
        } else {
            sohuImGroup.setNeedConfirm(!sohuImGroup.getNeedConfirm());
        }
        String content = "";
        if (sohuImGroup.getNeedConfirm()) {
            // 开启
            content = imGroupUtil.exchangeUser(sohuImGroup.getId(), userId) + ImSystemMessageEnum.GROUP_JSON_OPEN.getContent();
        } else {
            // 关闭
            content = imGroupUtil.exchangeUser(sohuImGroup.getId(), userId) + ImSystemMessageEnum.GROUP_JSON_CLOSE.getContent();
        }
        imSendUtil.sendSystemMessage(sohuImGroupVo, content, userId, ImCommandTypeEnum.groupApprove.getCode());
        return sohuImCommonService.updateGroup(sohuImGroup);
    }

    @Override
    public SohuImApplyGroupBo genQrCode(Long id) {
        Long userId = LoginHelper.getUserId();
        SohuErrorUtil.checkLoginId(userId);
        SohuImGroupVo sohuImGroup = this.get(id);
        if (Objects.isNull(sohuImGroup)) {
            return null;
        }
        SohuImApplyGroupBo applyGroupBo = new SohuImApplyGroupBo();
        applyGroupBo.setInviteUserId(userId);
        applyGroupBo.setInviteCalculate(userId ^ Constants.CALCULATE);
        applyGroupBo.setGroupId(id);
        return applyGroupBo;
    }

    @Override
    public List<SohuImGroupVo> subList(Long groupId) {
        if (groupId == null || groupId <= 0L) {
            return null;
        }
        SohuImGroupVo sohuImGroupVo = this.get(groupId);
        // 登录人id
        Long loginId = LoginHelper.getUserId();
        LambdaQueryWrapper<SohuImGroup> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImGroup::getRealPid, groupId);
        if (StrUtil.equalsAnyIgnoreCase(sohuImGroupVo.getGroupType(), ImGroupType.groupForm.name())) {
            lqw.in(SohuImGroup::getGroupType, Arrays.asList(ImGroupType.group.name(), ImGroupType.groupFormCustom.name()));
        } else if (loginId != null && loginId > 0L) {
            List<Long> groupIdList = joinGroupIdList();
            lqw.in(CollUtil.isNotEmpty(groupIdList), SohuImGroup::getId, groupIdList);
        }
        lqw.orderByDesc(SohuEntity::getCreateTime);
        List<SohuImGroupVo> sohuImGroupVos = baseMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(sohuImGroupVos)) {
            for (SohuImGroupVo imGroupVo : sohuImGroupVos) {
                imGroupVo.setCount(this.groupCount(imGroupVo.getId()));
                Long count = sohuImGroupUserMapper.selectCount(SohuImGroupUser::getGroupId, imGroupVo.getId());
                imGroupVo.setGroupUserNum((count == null || count <= 0L) ? 0 : count.intValue());
                if (StrUtil.equalsAnyIgnoreCase(sohuImGroupVo.getGroupType(), ImGroupType.groupForm.name())) {
                    imGroupVo.setGroupType(ImGroupType.groupFormCustom.name());
                }
                buildPlanVo(imGroupVo);
            }
        }
        return sohuImGroupVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean dismiss(Long groupId, Boolean canDismiss) {
        Long userId = LoginHelper.getUserId();
        SohuErrorUtil.checkLoginId(userId);
        SohuImGroupVo sohuImGroup = this.get(groupId);
        ImErrorUtil.checkGroupExist(sohuImGroup);
        if (canDismiss == null || !canDismiss) {
            if (!Objects.equals(sohuImGroup.getUserId(), userId)) {
                throw new ServiceException(MessageUtils.message("no.power"));
            }
            // 判断是否可以强制终止任务
            if (Objects.nonNull(sohuImGroupTaskService.groupTaskOver(sohuImGroup))) {
                throw new ServiceException(MessageUtils.message("group.task.rece.non.over"));
            }
        }

        deleteGroup(sohuImGroup);
        return Boolean.TRUE;
    }

    @Override
    public long groupCount(Long groupId) {
        return sohuImChatMessageMapper.countGroup(ImSessionTypeEnum.group.getCode(), groupId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDismiss(List<Long> groupIds) {
        if (CollUtil.isEmpty(groupIds)) {
            return Boolean.FALSE;
        }
        for (Long groupId : groupIds) {
            this.dismiss(groupId, false);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean batchSetRole(SohuImGroupUserListBo bo) {
        if (CollUtil.isEmpty(bo.getUserIds())) {
            return Boolean.FALSE;
        }
        SohuImGroupVo sohuImGroup = this.get(bo.getGroupId());
        ImErrorUtil.checkGroupExist(sohuImGroup);
        Long userId = LoginHelper.getUserId();
        if (!Objects.equals(sohuImGroup.getUserId(), userId)) {
            throw new ServiceException(MessageUtils.message("no.power"));
        }
        String power = bo.getPermissionType();
        if (bo.getPermissionType() == null || StrUtil.equalsAnyIgnoreCase(bo.getPermissionType(), ImGroupPermissionType.group_leader.name())) {
            power = ImGroupPermissionType.group_user.name();
        }
        String content = "";
        if (ImGroupPermissionType.group_user.name().equals(power)) {
            // 移除管理员
            content = imGroupUtil.exchangeUser(sohuImGroup.getId(), userId) + String.format(ImSystemMessageEnum.GROUP_RELIEVE_ADMIN.getContent(), this.buildUserName(bo.getUserIds(), bo.getGroupId()));
        } else {
            // 设置管理员
            content = imGroupUtil.exchangeUser(sohuImGroup.getId(), userId) + String.format(ImSystemMessageEnum.GROUP_SET_ADMIN.getContent(), this.buildUserName(bo.getUserIds(), bo.getGroupId()));
        }
        imSendUtil.sendSystemMessage(sohuImGroup, content, userId, ImCommandTypeEnum.groupAdministrator.getCode());
        LambdaUpdateWrapper<SohuImGroupUser> lq = new LambdaUpdateWrapper<>();
        lq.eq(SohuImGroupUser::getGroupId, bo.getGroupId());
        lq.set(SohuImGroupUser::getPermissionType, power);
        lq.in(SohuImGroupUser::getUserId, bo.getUserIds());
        sohuImGroupUserMapper.update(new SohuImGroupUser(), lq);
        for (Long boUserId : bo.getUserIds()) {
            sohuImGroupUserService.evict(sohuImGroup.getId(), boUserId);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean batchSetForbid(SohuImGroupUserListBo bo) {
        SohuImGroupVo sohuImGroup = this.get(bo.getGroupId());
        ImErrorUtil.checkGroupExist(sohuImGroup);
        Long userId = LoginHelper.getUserId();
        String content = "";
        if (bo.getForbid()) {
            // 批量设置群成员禁言
            content = imGroupUtil.exchangeUser(sohuImGroup.getId(), userId) + String.format(ImSystemMessageEnum.GROUP_USER_FORBID.getContent(), this.buildUserName(bo.getUserIds(), bo.getGroupId()));
        } else {
            // 批量解除群成员禁言
            content = imGroupUtil.exchangeUser(sohuImGroup.getId(), userId) + String.format(ImSystemMessageEnum.GROUP_RELIEVE_USER_FORBID.getContent(), this.buildUserName(bo.getUserIds(), bo.getGroupId()));
        }
        imSendUtil.sendSystemMessage(sohuImGroup, content, userId, ImCommandTypeEnum.groupMemberForbid.getCode());
        LambdaUpdateWrapper<SohuImGroupUser> lq = new LambdaUpdateWrapper<>();
        lq.eq(SohuImGroupUser::getGroupId, bo.getGroupId());
        lq.set(SohuImGroupUser::isForbid, bo.getForbid());
        lq.in(SohuImGroupUser::getUserId, bo.getUserIds());
        sohuImGroupUserMapper.update(new SohuImGroupUser(), lq);
        for (Long boUserId : bo.getUserIds()) {
            sohuImGroupUserService.evict(sohuImGroup.getId(), boUserId);
        }
        return Boolean.TRUE;
    }

    @Override
    public List<SohuImGroupUserVo> forbidUsers(Long groupId) {
        LambdaQueryWrapper<SohuImGroupUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImGroupUser::getGroupId, groupId).eq(SohuImGroupUser::isForbid, Boolean.TRUE);
        List<SohuImGroupUserVo> userVos = sohuImGroupUserMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(userVos)) {
            return null;
        }
        // 登录人id
        Long loginId = LoginHelper.getUserId();
        List<Long> userIds = userVos.stream().map(SohuImGroupUserVo::getUserId).collect(Collectors.toList());
        Map<Long, String> friendAliasMap = remoteMiddleFriendService.friendAliasMap(loginId, userIds, ApplyStateEnum.pass.name());
        Map<Long, LoginUser> userMap = remoteUserService.selectMap(userIds);
        userVos.forEach(userVo -> {
            LoginUser loginUser = userMap.get(userVo.getUserId());
            if (Objects.nonNull(loginUser)) {
                userVo.setUserName(loginUser.getUsername());
                userVo.setUserAvatar(loginUser.getAvatar());
            }
            userVo.setAlias(friendAliasMap.get(userVo.getUserId()));
        });
        return userVos;
    }

    @Override
    public List<Long> joinGroupIdList() {
        Long userId = LoginHelper.getUserId();
        return sohuImCommonService.queryUerJoinGroupIds(userId);
    }

    @Override
    public List<SohuImGroupUserVo> joinGroupList() {
        Long userId = LoginHelper.getUserId();
        LambdaQueryWrapper<SohuImGroupUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImGroupUser::getUserId, userId);
        return sohuImGroupUserMapper.selectVoList(lqw);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteGroup(SohuImGroupVo sohuImGroup) {
        if (Objects.isNull(sohuImGroup)) {
            return;
        }
        Long groupId = sohuImGroup.getId();
        Set<Long> userIds = new HashSet<>();

        // 查询群用户
        List<SohuImGroupUser> groupUsers = sohuImGroupUserMapper.selectList(SohuImGroupUser::getGroupId, groupId);
        if (CollUtil.isNotEmpty(groupUsers)) {
            for (SohuImGroupUser groupUser : groupUsers) {
                // 清除未读消息缓存
                CacheMgr.evict(ImCacheConstants.IM_UN_READ, SohuImUtil.imRedisKey(ImSessionTypeEnum.group.getCode(), groupUser.getUserId(), groupId));
                ImUnReadUtil.clearUnReadCount(ImSessionTypeEnum.group.getCode(), groupUser.getUserId(), groupId);
                userIds.add(groupUser.getUserId());
            }
            CacheMgr.set(ImCacheConstants.IM_UN_GROUP_USERS, String.valueOf(groupId), userIds);
        }
        // 子群列表
        List<SohuImGroup> subImGroups = baseMapper.selectList(SohuImGroup::getPid, sohuImGroup.getId());
        if (CollUtil.isNotEmpty(subImGroups)) {
            List<Long> subGroupIds = new ArrayList<>();
            for (SohuImGroup subImGroup : subImGroups) {
                subGroupIds.add(subImGroup.getId());
                List<SohuImGroupUser> subGroupUserVos = sohuImGroupUserMapper.selectList(SohuImGroupUser::getGroupId, subImGroup.getId());
                if (CollUtil.isNotEmpty(subGroupUserVos)) {
                    for (SohuImGroupUser sohuImGroupUser : subGroupUserVos) {
                        this.baseMapper.deleteById(sohuImGroupUser.getId());
                        // 清除与这个群的未读消息缓存
                        ImUnReadUtil.clearUnReadCount(ImSessionTypeEnum.group.getCode(), sohuImGroupUser.getUserId(), sohuImGroupUser.getGroupId());
                    }
                    log.info("子群退群成功");
                }
            }
        }
        // 删除群
        this.baseMapper.deleteById(sohuImGroup);
        // 删除群用户
        sohuImGroupUserMapper.deleteByGroupId(groupId);
        // 删除群消息记录
        //sohuImMessageLastService.deleteMsgByGroupId(groupId);
        sohuImChatLastMessageMapper.delete(SohuImChatLastMessage::getSessionType, ImSessionTypeEnum.group.getCode(), SohuImChatLastMessage::getReceiverId, groupId);
        sohuImChatMessageMapper.delete(SohuImChatMessage::getSessionType, ImSessionTypeEnum.group.getCode(), SohuImChatMessage::getReceiverId, groupId);
        // 删除群申请记录
        sohuImApplyMapper.deleteByGroupId(groupId);
        // 删除群禁言时间段
        sohuImGroupForbidTimeService.deleteByGroupId(groupId);
        // 删除缓存
        evictGroup(groupId);
    }


    @Override
    public List<SohuImGroupVo> joinList() {
        Long loginId = LoginHelper.getUserId();
        if (CalUtils.isNullOrZero(loginId)) {
            return new ArrayList<>();
        }
        List<SohuImGroupUserVo> groupUserVos = joinGroupList();
        if (CollUtil.isEmpty(groupUserVos)) {
            return new ArrayList<>();
        }
        Set<Long> groupIds = groupUserVos.stream().map(SohuImGroupUserVo::getGroupId).collect(Collectors.toSet());
        List<SohuImGroupVo> imGroupVos = sohuImCommonService.queryGroupList(groupIds);
        if (CollUtil.isEmpty(imGroupVos)) {
            return new ArrayList<>();
        }
        List<SohuImGroupVo> sohuImGroupVos = buildGroupListOrderByChatTime(imGroupVos);
        Map<Long, SohuImGroupUserVo> imGroupUserVoMap = groupUserVos.stream().collect(Collectors.toMap(SohuImGroupUserVo::getGroupId, u -> u));
        List<SohuImGroupVo> result = new LinkedList<>();
        for (SohuImGroupVo imGroupVo : sohuImGroupVos) {
            if (Objects.equals(imGroupVo.getState(), 2)) {
                continue;
            }
            SohuImGroupUserVo imGroupUserVo = imGroupUserVoMap.get(imGroupVo.getId());
            imGroupVo.setGroupUserPermissionType(imGroupUserVo.getPermissionType());
            result.add(imGroupVo);
        }
        return result;
    }

    @Override
    public List<Long> userManagedGroups() {
        List<Long> groupIds = joinGroupIdList();
        if (CollUtil.isEmpty(groupIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SohuImGroupUser> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuImGroupUser::getGroupId, groupIds).eq(SohuImGroupUser::getUserId, LoginHelper.getUserId());
        lqw.in(SohuImGroupUser::getPermissionType, ImGroupPermissionType.group_leader.name(), ImGroupPermissionType.group_admin.name());
        List<SohuImGroupUser> groupUsers = sohuImGroupUserMapper.selectList(lqw);
        return CollUtil.isEmpty(groupUsers) ? new ArrayList<>() : groupUsers.stream().map(SohuImGroupUser::getGroupId).collect(Collectors.toList());
    }

    @Override
    public List<Long> userCreatedGroups() {
        List<Long> groupIds = joinGroupIdList();
        if (CollUtil.isEmpty(groupIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SohuImGroupUser> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuImGroupUser::getGroupId, groupIds).eq(SohuImGroupUser::getUserId, LoginHelper.getUserId());
        lqw.in(SohuImGroupUser::getPermissionType, ImGroupPermissionType.group_leader.name());
        List<SohuImGroupUser> groupUsers = sohuImGroupUserMapper.selectList(lqw);
        return CollUtil.isEmpty(groupUsers) ? new ArrayList<>() : groupUsers.stream().map(SohuImGroupUser::getGroupId).collect(Collectors.toList());
    }

    @Override
    public Boolean forbid(Long groupId) {
        SohuImGroupVo sohuImGroup = this.get(groupId);
        ImErrorUtil.checkGroupExist(sohuImGroup);
        SohuImGroupUserVo imGroupUser = this.sohuImGroupUserService.selectOne(groupId, LoginHelper.getUserId());
        if (Objects.isNull(imGroupUser)) {
            return Boolean.TRUE;
        }
        if (StrUtil.equalsAnyIgnoreCase(imGroupUser.getPermissionType(), ImGroupPermissionType.group_leader.name()) ||
                StrUtil.equalsAnyIgnoreCase(imGroupUser.getPermissionType(), ImGroupPermissionType.group_admin.name())) {
            return Boolean.FALSE;
        }
        if (sohuImGroup.getForbid()) {
            return Boolean.TRUE;
        }
        // 判断是否在禁言时间段内
        if (sohuImGroup.getForbidTime()) {
            List<SohuImGroupForbidTimeVo> timeList = sohuImCommonService.queryGroupForbidTime(groupId);
            if (!CollectionUtils.isEmpty(timeList)) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
                LocalTime now = LocalTime.now();
                for (SohuImGroupForbidTimeVo timeVo : timeList) {
                    LocalTime startTime = LocalTime.parse(timeVo.getStartTime(), formatter);
                    LocalTime endTime = LocalTime.parse(timeVo.getEndTime(), formatter);
                    if (now.isAfter(startTime) && now.isBefore(endTime)) {
                        // 如果当前时间在禁言开始之后,禁言结束之前则需要进行禁言操作
                        return Boolean.TRUE;
                    }
                }
            }
        }
        return imGroupUser.isForbid();
    }

    @Override
    public List<SohuImGroupVo> buildGroupListOrderByChatTime(List<SohuImGroupVo> groupList) {
        if (CollUtil.isEmpty(groupList)) {
            return null;
        }
        Map<Long, SohuImGroupVo> sohuImGroupVoMap = new HashMap<>();
        List<Long> groupIds = groupList.stream().map(SohuImGroupVo::getId).collect(Collectors.toList());
        Map<Long, Long> groupedUserCountMap = sohuImGroupUserService.groupUserCountMap(groupIds);
        // 群禁言时间段组map
        Map<Long, List<SohuImGroupForbidTimeVo>> forbidTimeGroupMap = sohuImGroupForbidTimeService.selectMapByGroupIds(groupIds);
        for (SohuImGroupVo imGroupVo : groupList) {
            Long count = groupedUserCountMap.get(imGroupVo.getId());
            imGroupVo.setGroupUserNum((count == null || count <= 0L) ? 0 : count.intValue());
            sohuImGroupVoMap.put(imGroupVo.getId(), imGroupVo);
        }
        LambdaQueryWrapper<SohuImChatMessage> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImChatMessage::getSessionType, ImSessionTypeEnum.group.getCode()).in(SohuImChatMessage::getReceiverId, groupIds);
        lqw.orderByDesc(SohuEntity::getCreateTime);
        List<SohuImChatMessage> chatMessages = sohuImChatMessageMapper.selectList(lqw);
        if (CollUtil.isEmpty(chatMessages)) {
            return groupList;
        }
        // 创建一个Map来存储每个群最新的聊天消息时间
        Map<Long, Date> latestChatTimeMap = chatMessages.stream().collect(Collectors.toMap(SohuImChatMessage::getReceiverId, SohuImChatMessage::getUpdateTime, (existing, replacement) -> existing // 处理重复key的情况
        ));
        for (SohuImGroupVo imGroupVo : groupList) {
            latestChatTimeMap.computeIfAbsent(imGroupVo.getId(), k -> imGroupVo.getCreateTime());
            imGroupVo.setTimeList(forbidTimeGroupMap.get(imGroupVo.getId()));
        }
        // 按最新聊天时间排序群列表
        return groupList.stream().sorted((g1, g2) -> {
            Date time1 = latestChatTimeMap.get(g1.getId());
            Date time2 = latestChatTimeMap.get(g2.getId());
            return Comparator.nullsLast(Date::compareTo).reversed().compare(time1, time2);
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createGroup(SohuImGroupCreateBo bo) {
        return Objects.nonNull(this.buildGroup(bo));
    }

    @Override
    public SohuImGroupVo queryByTaskNumber(String taskNumber) {
        LambdaQueryWrapper<SohuImGroup> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuImGroup::getGroupType, ImGroupType.specialGroupTypes);
        lqw.like(SohuImGroup::getGroupExt, taskNumber);
        lqw.last(" limit 1");
        return this.baseMapper.selectVoOne(lqw);
    }

    @Override
    public SohuImGroupVo queryByTaskNumber(String groupType, String taskNumber) {
        LambdaQueryWrapper<SohuImGroup> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImGroup::getGroupType, groupType);
        lqw.like(SohuImGroup::getGroupExt, taskNumber);
        lqw.orderByDesc(SohuImGroup::getId);
        lqw.last(" limit 1");
        return this.baseMapper.selectVoOne(lqw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteGroupTask(Long publishUserId, String childTaskNumber) {
        LambdaQueryWrapper<SohuImGroup> luw = new LambdaQueryWrapper<>();
        luw.eq(SohuImGroup::getUserId, publishUserId);
        luw.eq(SohuImGroup::getGroupType, ImGroupType.groupTask.name());
        luw.like(SohuImGroup::getGroupExt, childTaskNumber);
        luw.last(" limit 1");
        SohuImGroup sohuImGroup = this.baseMapper.selectOne(luw);
        if (Objects.isNull(sohuImGroup)) {
            log.error("deleteGroupTask 删除群组失败，群组不存在");
            return Boolean.FALSE;
        }
        this.dismiss(sohuImGroup.getId(), true);
        return Boolean.TRUE;
    }

    @Override
    public int setGroupUnq() {
        LambdaQueryWrapper<SohuImGroup> lqw = new LambdaQueryWrapper<>();
        lqw.isNull(SohuImGroup::getUnq);
        List<SohuImGroup> sohuImGroups = this.baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(sohuImGroups)) {
            return 0;
        }
        List<SohuImGroup> updateList = new ArrayList<>();
        for (SohuImGroup imGroup : sohuImGroups) {
            if (imGroup.getUnq() != null && imGroup.getUnq() > 0L) {
                continue;
            }
            updateList.add(imGroup);
        }
        this.baseMapper.updateBatchById(updateList);
        return updateList.size();
    }

    @Override
    public Boolean deleteGroupTaskNoLogin(Long publishUserId, String childTaskNumber) {
        LambdaQueryWrapper<SohuImGroup> luw = new LambdaQueryWrapper<>();
        luw.eq(SohuImGroup::getUserId, publishUserId);
        luw.eq(SohuImGroup::getGroupType, ImGroupType.groupTask.name());
        luw.like(SohuImGroup::getGroupExt, childTaskNumber);
        luw.last(" limit 1");
        SohuImGroupVo sohuImGroup = this.baseMapper.selectVoOne(luw);
        if (Objects.isNull(sohuImGroup)) {
            log.error("deleteGroupTask 删除群组失败，群组不存在");
            return Boolean.FALSE;
        }
        deleteGroup(sohuImGroup);
        return Boolean.TRUE;
    }

    @Override
    public List<SohuImGroupVo> recommGroupList() {
        SysDictData dictData = remoteDictService.getDictData(DictEnum.SysRecomm.getKey(), "recomm_group");
        if (Objects.isNull(dictData)) {
            return null;
        }
        String dictValue = dictData.getDictValue();
        List<String> split = StrUtil.split(dictValue, StrPool.COMMA);
        List<Long> groupIds = new ArrayList<>();
        for (String string : split) {
            groupIds.add(Long.parseLong(string));
        }
        return baseMapper.selectVoBatchIds(groupIds);
    }

    @Override
    public Boolean addFriend(Long id) {
        SohuImGroupVo sohuImGroupVo = this.get(id);
        if (Objects.isNull(sohuImGroupVo)) {
            return Boolean.FALSE;
        }
        SohuImGroup sohuImGroup = BeanUtil.toBean(sohuImGroupVo, SohuImGroup.class);
        Long userId = LoginHelper.getUserId();
        if (sohuImGroup.getAddFriend() == null) {
            sohuImGroup.setAddFriend(false);
        } else {
            sohuImGroup.setAddFriend(!sohuImGroup.getAddFriend());
        }
        String content = "";
        if (sohuImGroup.getAddFriend()) {
            // 开启
            content = imGroupUtil.exchangeUser(sohuImGroup.getId(), userId) + ImSystemMessageEnum.GROUP_ADD_FRIEND_OPEN.getContent();
        } else {
            // 关闭
            content = imGroupUtil.exchangeUser(sohuImGroup.getId(), userId) + ImSystemMessageEnum.GROUP_ADD_FRIEND_CLOSE.getContent();
        }
        imSendUtil.sendSystemMessage(sohuImGroupVo, content, userId, ImCommandTypeEnum.groupAddFriend.getCode());
        return sohuImCommonService.updateGroup(sohuImGroup);
    }

    @Override
    public Boolean forbidTime(Long id) {
        Long userId = LoginHelper.getUserId();
        SohuImGroupVo sohuImGroupVo = this.get(id);
        if (Objects.isNull(sohuImGroupVo)) {
            return Boolean.FALSE;
        }
        SohuImGroup sohuImGroup = BeanUtil.toBean(sohuImGroupVo, SohuImGroup.class);
        if (sohuImGroup.getForbidTime() == null) {
            sohuImGroup.setForbidTime(false);
        } else {
            if (!sohuImGroup.getForbidTime()) {
                // 将关闭设置为开启,则需要把全员禁言开关给关闭
                sohuImGroup.setForbid(false);
            }
            sohuImGroup.setForbidTime(!sohuImGroup.getForbidTime());
        }
        String content = "";
        if (sohuImGroup.getForbidTime()) {
            // 开启,判断是否存在禁言时间段
            List<SohuImGroupForbidTimeVo> timeList = sohuImCommonService.queryGroupForbidTime(id);
            if (!CollectionUtils.isEmpty(timeList)) {
                StringBuffer timeStr = new StringBuffer();
                for (SohuImGroupForbidTimeVo timeVo : timeList) {
                    timeStr.append(timeVo.getStartTime()).append("-").append(timeVo.getEndTime()).append(" ");
                }
                content = imGroupUtil.exchangeUser(sohuImGroup.getId(), LoginHelper.getUserId()) + String.format(ImSystemMessageEnum.GROUP_FORBID_TIME_OPEN.getContent(), timeStr.toString());
            }
        } else {
            // 关闭
            content = imGroupUtil.exchangeUser(sohuImGroup.getId(), userId) + ImSystemMessageEnum.GROUP_FORBID_TIME_CLOSE.getContent();
        }
        if (StrUtil.isNotBlank(content)) {
            imSendUtil.sendSystemMessage(sohuImGroupVo, content, userId, ImCommandTypeEnum.groupForbidTime.getCode());
        }
        return sohuImCommonService.updateGroup(sohuImGroup);
    }

    @Override
    public List<SohuImGroupVo> createdList() {
        List<Long> groupIds = userCreatedGroups();
        if (CollUtil.isEmpty(groupIds)) {
            return new ArrayList<SohuImGroupVo>();
        }
        List<SohuImGroupVo> groupVos = sohuImCommonService.queryGroupList(groupIds);
        for (SohuImGroupVo groupVo : groupVos) {
            SohuImGroupDisturbVo groupDisturbVo = sohuImGroupDisturbMapper.selectByGroupId(groupVo.getId(), groupVo.getUserId());
            groupVo.setDisturb(Objects.nonNull(groupDisturbVo));
        }
        return groupVos;
    }

    @Override
    public Map<Long, SohuImGroupVo> queryMap(Collection<Long> groupIds) {
        if (CollUtil.isEmpty(groupIds)) {
            return new HashMap<Long, SohuImGroupVo>();
        }
        List<SohuImGroupVo> sohuImGroupVos = sohuImCommonService.queryGroupList(groupIds);
        Map<Long, SohuImGroupVo> result = new HashMap<>();
        for (SohuImGroupVo imGroupVo : sohuImGroupVos) {
            result.put(imGroupVo.getId(), imGroupVo);
        }
        return result;
    }

    @Override
    public String getGroupWord(Long groupId) {
        SohuImGroupVo sohuImGroup = this.get(groupId);
        if (Objects.isNull(sohuImGroup)) {
            return null;
        }
        String exist = CacheMgr.getStr(ImCacheConstants.GROUP_WORD, String.valueOf(groupId));
        if (StrUtil.isNotBlank(exist)) {
            return exist;
        }
        String groupWord = groupInviteGenerator.generateInviteMessage(groupId, sohuImGroup.getName());
        SohuImGroup update = new SohuImGroup();
        update.setId(groupId);
        update.setGroupWord(groupWord);
        sohuImCommonService.updateGroup(update);
        CacheMgr.set(ImCacheConstants.GROUP_WORD, String.valueOf(groupId), groupWord);
        return groupWord;
    }

    @Override
    public Boolean groupDisable(SohuImGroupDisableBo bo) {
        SohuImGroup sohuImGroup = sohuImCommonService.query(bo.getGroupId());
        ImErrorUtil.checkGroupExist(sohuImGroup);
        sohuImGroup.setState(ImGroupState.disable.getCode());
        sohuImCommonService.updateGroup(sohuImGroup);

        // 发送socket消息
        String content = "群已被禁用";
        ImChatRequestBo requestBo = new ImChatRequestBo();
        requestBo.setReceiverId(sohuImGroup.getId());
        requestBo.setSessionType(sohuImGroup.getGroupType());
        requestBo.setMessageType(ImMessageTypeEnum.Command.getCode());
        requestBo.setCommandType(ImCommandTypeEnum.groupDisable.getCode());
        requestBo.setContent(content);
        requestBo.setChatId(System.nanoTime());
        requestBo.setLocalId(RandomUtil.randomString(16));
        // 发送socket 消息
        imSendUtil.serverActiveSend(sohuImGroup.getUserId(), requestBo, true);
        this.createImGroupLog(bo, sohuImGroup);
        return Boolean.TRUE;
    }

    @Override
    public Boolean groupEnable(Long groupId) {
        SohuImGroup sohuImGroup = sohuImCommonService.query(groupId);
        ImErrorUtil.checkGroupExist(sohuImGroup);
        sohuImGroup.setState(ImGroupState.enable.getCode());
        sohuImCommonService.updateGroup(sohuImGroup);

        // 发送socket消息
        String content = "群聊已恢复解禁";
        ImChatRequestBo requestBo = new ImChatRequestBo();
        requestBo.setReceiverId(sohuImGroup.getId());
        requestBo.setSessionType(sohuImGroup.getGroupType());
        requestBo.setMessageType(ImMessageTypeEnum.Command.getCode());
        requestBo.setCommandType(ImCommandTypeEnum.groupEnable.getCode());
        requestBo.setContent(content);
        requestBo.setChatId(System.nanoTime());
        requestBo.setLocalId(RandomUtil.randomString(16));
        // 发送socket 消息
        imSendUtil.serverActiveSend(sohuImGroup.getUserId(), requestBo, true);
        this.createImGroupLog(null, sohuImGroup);
        return Boolean.TRUE;
    }

    /**
     * 记录群组日志
     *
     * @return
     */
    private Boolean createImGroupLog(SohuImGroupDisableBo bo, SohuImGroup sohuImGroup) {
        SohuImGroupLog imGroupLog = new SohuImGroupLog();
        imGroupLog.setGroupId(sohuImGroup.getId());
        LoginUser loginUser = LoginHelper.getLoginUser();
        imGroupLog.setOperUserId(loginUser.getUserId());
        imGroupLog.setOperNickName(loginUser.getNickname());
        if (Objects.equals(sohuImGroup.getState(), ImGroupState.enable.getCode())) {
            imGroupLog.setDescription(loginUser.getNickname() + "在" + DateUtils.getTime() + "解禁了群聊");
        } else if (Objects.equals(sohuImGroup.getState(), ImGroupState.disable.getCode())) {
            imGroupLog.setDescription(loginUser.getNickname() + "在" + DateUtils.getTime() + "封禁了群聊，原因：" + bo.getReason());
        } else {
            imGroupLog.setDescription("未知操作");
        }
        this.sohuImGroupLogMapper.insert(imGroupLog);
        return true;
    }

    @Override
    public TableDataInfo<SohuImGroupVo> groupPage(SohuImPageQueryBo bo) {
        List<Long> userIdList = null;
        if (StrUtil.isNotBlank(bo.getGroupOwnerUserName()) || StrUtil.isNotBlank(bo.getGroupOwnerNickName())) {
            SysUserQueryBo userQueryBo = new SysUserQueryBo();
            userQueryBo.setUserName(bo.getGroupOwnerUserName());
            userQueryBo.setNickName(bo.getGroupOwnerNickName());
            userIdList = this.remoteUserService.queryUserIdByBo(userQueryBo);
            if (userIdList.size() > Constants.DEFAULT_BATCH_SIZE) {
                userIdList = userIdList.subList(0, Constants.DEFAULT_BATCH_SIZE);
            }
        }
        LambdaQueryWrapper<SohuImGroup> lqw = new LambdaQueryWrapper<>();
        lqw.like(StrUtil.isNotBlank(bo.getGroupId()), SohuImGroup::getId, bo.getGroupId());
        lqw.like(StrUtil.isNotBlank(bo.getGroupName()), SohuImGroup::getName, bo.getGroupName());
        lqw.eq(StrUtil.isNotBlank(bo.getGroupType()), SohuImGroup::getGroupType, bo.getGroupType());
        lqw.eq(bo.getState() != null, SohuImGroup::getState, bo.getState());
        lqw.in(CollUtil.isNotEmpty(userIdList), SohuImGroup::getUserId, userIdList);
        lqw.ge(bo.getCreateStartTime() != null, SohuImGroup::getCreateTime, bo.getCreateStartTime());
        lqw.le(bo.getCreateEndTime() != null, SohuImGroup::getCreateTime, bo.getCreateEndTime());
        lqw.orderByDesc(SohuEntity::getCreateTime);
        IPage<SohuImGroupVo> result = this.baseMapper.selectVoPage(PageQueryUtils.build(new PageQuery(bo.getPageNum(), bo.getPageSize())), lqw);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            Set<Long> userIdSet = result.getRecords().stream().map(SohuImGroupVo::getUserId).collect(Collectors.toSet());
            Map<Long, LoginUser> userMap = this.remoteUserService.selectMap(userIdSet);
            for (SohuImGroupVo vo : result.getRecords()) {
                LoginUser loginUser = userMap.get(vo.getUserId());
                if (Objects.nonNull(loginUser)) {
                    vo.setGroupOwnerNickName(loginUser.getNickname());
                    vo.setGroupOwnerUserName(loginUser.getUsername());
                }
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public List<SohuImGroupVo> queryMyImGroup(SohuMyImGroupQueryBo bo) {
        return this.baseMapper.queryMyImGroup(bo);
    }

    @Override
    public Long countJoinGroup(Long loginId) {
        return sohuImGroupUserMapper.selectCount(SohuImGroupUser::getUserId, loginId);
    }

    @Transactional(rollbackFor = Exception.class)
    public SohuImGroup buildGroup(SohuImGroupCreateBo bo) {
        List<SohuImGroupUserBo> groupUsers = bo.getGroupUsers();

        SohuImGroupUserBo groupUserHeader = SohuImGroupUserBo.builder().userId(bo.getUserId()).permissionType(ImGroupPermissionType.group_leader.name()).build();

        if (StrUtil.equalsAnyIgnoreCase(bo.getGroupType(), ImGroupType.groupTask.name(), ImGroupType.groupTaskCustom.name())) {
            Map<String, Object> map = new HashMap<>();
            map.put("groupTaskRole", ImGroupTaskRole.taskPublish.getCode());
            // 标识群主是发单方
            groupUserHeader.setExt(JSONUtil.toJsonStr(map));
        }

        Long groupHeaderId = bo.getUserId();
        LoginUser groupHeader = remoteUserService.queryById(groupHeaderId);
        String groupName = bo.getName();

        SohuImGroupBo imGroupBo = new SohuImGroupBo();
        imGroupBo.setLogo(StrUtil.isBlankIfStr(bo.getLogo()) ? Constants.DEFAULT_GROUP_LOGO : bo.getLogo());
        imGroupBo.setName(groupName);
        imGroupBo.setPid((bo.getGroupPid() != null && bo.getGroupPid() > 0L) ? bo.getGroupPid() : 0L);
        imGroupBo.setCreateTime(new Date());
        imGroupBo.setUpdateTime(new Date());
        imGroupBo.setGroupUserNum(groupUsers.size());
        imGroupBo.setUserId(groupHeaderId);
        imGroupBo.setGroupType(StrUtil.isBlankIfStr(bo.getGroupType()) ? ImGroupType.group.name() : bo.getGroupType());
        imGroupBo.setGroupExt(bo.getGroupExt());
        imGroupBo.setUnq(bo.getUnq() == null ? System.nanoTime() : bo.getUnq());
        imGroupBo.setGroupNotice(bo.getGroupNotice());
        SohuImGroup imGroup = this.insert(imGroupBo);
        imGroupBo.setId(imGroup.getId());
        bo.setGroupId(imGroup.getId());
        List<SohuImGroupUser> imGroupUsers = new ArrayList<>();
        for (SohuImGroupUserBo groupUser : groupUsers) {
            if (Objects.equals(groupHeaderId, groupUser.getUserId())) {
                continue;
            }
            SohuImGroupUser imGroupUser = new SohuImGroupUser(imGroupBo.getId(), groupUser.getUserId(), groupUser.getPermissionType());
            imGroupUser.setExt(imGroupUser.getExt());
            if (groupUser.getForbid() != null) {
                imGroupUser.setForbid(groupUser.getForbid());
            }
            if (StrUtil.equalsAnyIgnoreCase(imGroupUser.getPermissionType(), ImGroupPermissionType.group_leader.name())) {
                imGroupUser.setSortIndex(1);
            } else if (StrUtil.equalsAnyIgnoreCase(imGroupUser.getPermissionType(), ImGroupPermissionType.group_admin.name())) {
                imGroupUser.setSortIndex(2);
            } else {
                imGroupUser.setSortIndex(3);
            }
            // 用户绑定到群组
            imGroupUtil.bindGroup(imGroupUser.getUserId(), imGroupUser.getGroupId());
            imGroupUsers.add(imGroupUser);
        }
        // 群主绑定到群组
        imGroupUtil.bindGroup(groupHeaderId, imGroup.getId());
        // 添加群主
        SohuImGroupUser imGroupUserHeader = new SohuImGroupUser(imGroupBo.getId(), groupUserHeader.getUserId(), groupUserHeader.getPermissionType());
        imGroupUsers.add(imGroupUserHeader);
        imGroup.setGroupUserNum(imGroupUsers.size());

        if (!CalUtils.isNullOrZero(imGroup.getPid())) {
            // 创建子群
            Set<Long> groupUserIds = imGroupUsers.stream().map(SohuImGroupUser::getUserId).collect(Collectors.toSet());
            Map<Long, SohuImGroupUser> groupedUserMap = sohuImGroupUserService.groupUserMap(groupUserIds, imGroup.getPid());
            for (SohuImGroupUser groupUser : imGroupUsers) {
                SohuImGroupUser imGroupUser = groupedUserMap.get(groupUser.getUserId());
                if (Objects.isNull(imGroupUser)) {
                    continue;
                }
                groupUser.setGroupUserNickName(imGroupUser.getGroupUserNickName());
            }
        }

        sohuImGroupUserMapper.insertBatch(imGroupUsers);
        sohuImCommonService.updateGroup(imGroup);
        if (BooleanUtil.isTrue(bo.getSendFirstGroupMsg())) {
            // 发送一个提示消息
            SohuImGroupVo sohuImGroupVo = BeanUtil.toBean(imGroup, SohuImGroupVo.class);
            String name = StringUtils.getValidString(groupHeader.getNickname(), groupHeader.getUsername());
            if (StrUtil.equalsAnyIgnoreCase(imGroupBo.getGroupType(), ImSessionTypeEnum.groupForm.getCode(), ImSessionTypeEnum.groupFormCustom.getCode(), ImSessionTypeEnum.groupFromGeneral.getCode())) {
                name = StringUtils.sensitive(1, name, 2, false);
            }
            imSendUtil.sendSystemMessage(sohuImGroupVo, String.format(ImSystemMessageEnum.GROUP_GREET.getContent(), name), groupHeaderId, ImCommandTypeEnum.groupGreet.getCode());
        }

        for (SohuImGroupUser groupUser : imGroupUsers) {
            sohuImGroupUserService.evictUserAllGroup(groupUser.getUserId());
        }
        log.info("建群成功:{}", JSONUtil.toJsonStr(imGroup));
        return imGroup;
    }

    /**
     * 构建用户信息
     *
     * @param userIds
     * @param groupId
     * @return
     */
    private String buildUserName(Collection<Long> userIds, Long groupId) {
        Map<Long, LoginUser> loginUserMap = remoteUserService.selectMap(userIds);
        Map<Long, SohuImGroupUser> groupUserMap = sohuImGroupUserService.groupUserMap(userIds, groupId);
        StringBuffer userName = new StringBuffer();
        for (Long id : userIds) {
            if (groupUserMap.containsKey(id) && StrUtil.isNotBlank(groupUserMap.get(id).getGroupUserNickName())) {
                userName.append(" " + groupUserMap.get(id).getGroupUserNickName() + " ");
                break;
            }
            if (loginUserMap.containsKey(id)) {
                if (StrUtil.isNotBlank(loginUserMap.get(id).getNickname())) {
                    userName.append(" " + loginUserMap.get(id).getNickname() + " ");
                } else {
                    userName.append(" " + loginUserMap.get(id).getUsername() + " ");
                }
            }
        }
        return userName.toString();
    }

    /**
     * 表单任务详情
     *
     * @param imGroupVo
     */
    private void buildPlanVo(SohuImGroupVo imGroupVo) {
        if (StrUtil.equalsAnyIgnoreCase(imGroupVo.getGroupType(), ImGroupType.groupForm.name()) && StrUtil.isNotBlank(imGroupVo.getGroupExt())) {
            SohuImGroupFormExtVo extVo = JSONUtil.toBean(imGroupVo.getGroupExt(), SohuImGroupFormExtVo.class);
            if (Objects.nonNull(extVo)) {
                SohuBusyTaskVo mainTask = remoteBusyTaskService.getByTaskNo(extVo.getMasterTaskNumber());
                // 汇总子群id集合
                // 所有的子单列表
                List<SohuBusyTaskSiteModel> childList = remoteBusyTaskService.getChildList(extVo.getMasterTaskNumber());
                List<String> taskNumberList = childList.stream()
                        .filter(item -> StrUtil.equalsAnyIgnoreCase(item.getState(), SohuBusyTaskState.OverSettle.name()))
                        .map(SohuBusyTaskSiteModel::getTaskNumber)
                        .collect(Collectors.toList());
                List<SohuImGroupOrderUserVO> groupOrderUserVOS = sohuImGroupOrderUserService.queryList(taskNumberList);
                List<Long> groupIdList = CollUtil.isEmpty(groupOrderUserVOS) ? new ArrayList<>() : groupOrderUserVOS.stream().map(SohuImGroupOrderUserVO::getGroupId).collect(Collectors.toList());
                if (Objects.nonNull(mainTask)) {
                    SohuImGroupFormPlanVo planVo = new SohuImGroupFormPlanVo();
                    planVo.setFullAmount(mainTask.getFullAmount());
                    planVo.setTaskTitle(mainTask.getTitle());
                    planVo.setReceiveNum(mainTask.getReceiveNum());
                    planVo.setDeliveryNum(sohuImGroupOrderUserService.getPassPersonUserByGroupIds(groupIdList));
                    planVo.setState(mainTask.getState());
                    planVo.setStandNum(sohuImGroupOrderUserService.getPassPersonUserByMasterTaskNumber(extVo.getMasterTaskNumber()));
                    planVo.setDeliveryStandard(mainTask.getDeliveryStandard());
                    planVo.setBusyTaskUpdateTime(new Date());
                    planVo.setGroupUserUpdateTime(new Date());
                    SohuImGroupOrderUser sohuImGroupOrderUser = sohuImGroupOrderUserService.queryByMasterTaskNum(mainTask.getTaskNumber());
                    if (Objects.nonNull(sohuImGroupOrderUser)) {
                        planVo.setBusyTaskUpdateTime(sohuImGroupOrderUser.getUpdateTime());
                        planVo.setGroupUserUpdateTime(sohuImGroupOrderUser.getUpdateTime());
                    }
                    imGroupVo.setPlanVo(planVo);
                }
            }
        } else if (StrUtil.equalsAnyIgnoreCase(imGroupVo.getGroupType(), ImGroupType.groupFormCustom.name()) && StrUtil.isNotBlank(imGroupVo.getGroupExt())) {
            SohuImGroupSubFormExtVo extVo = JSONUtil.toBean(imGroupVo.getGroupExt(), SohuImGroupSubFormExtVo.class);
            if (Objects.nonNull(extVo)) {
                SohuBusyTaskSiteModel subTask = remoteBusyTaskService.queryMasterTaskNumber(extVo.getTaskNumber());
                SohuBusyTaskVo mainTask = remoteBusyTaskService.getByTaskNo(extVo.getMasterTaskNumber());
                if (Objects.nonNull(subTask)) {

                    LoginUser subTaskUser = remoteUserService.queryById(extVo.getTaskReceUserId());
                    SohuImGroupFormPlanVo planVo = new SohuImGroupFormPlanVo();
                    planVo.setFullAmount(subTask.getFullAmount());
                    planVo.setTaskTitle(subTask.getTitle());
                    planVo.setState(subTask.getState());
                    Integer standNum = sohuImGroupOrderUserService.getPassPersonUserByTaskNumber(extVo.getTaskNumber());
                    planVo.setStandNum(standNum);
                    planVo.setStandAmount(CalUtils.multiply(new BigDecimal(planVo.getStandNum()), mainTask.getSingleAmount()));
                    planVo.setTaskReceUserName(StringUtils.sensitive(1, subTaskUser.getNickname(), 2, false));
                    planVo.setBusyTaskUpdateTime(new Date());
                    planVo.setGroupUserUpdateTime(new Date());
                    if (StrUtil.equalsAnyIgnoreCase(subTask.getState(), SohuBusyTaskState.WaitApproveSettle.name())) {
                        SohuBusyTaskReceiveVo sohuBusyTaskReceiveVo = remoteBusyTaskReceiveService.getReceiveDetailByTaskNumber(extVo.getTaskNumber());
                        if (Objects.nonNull(sohuBusyTaskReceiveVo)) {
                            // 获取申请审核记录
                            List<SohuAuditVo> auditVoList = remoteMiddleAuditService.selectListByObj(sohuBusyTaskReceiveVo.getId(), BusyType.SettleBusyTask.name());
                            if (CollUtil.isNotEmpty(auditVoList)) {
                                Date publishTime = auditVoList.get(0).getPublishTime();
                                planVo.setBusyTaskUpdateTime(publishTime);
                                planVo.setGroupUserUpdateTime(publishTime);
                            }
                        }
                    }
                    // 判断是否是最后一个达标用户
                    SohuImGroupOrderUser sohuImGroupOrderUser = sohuImGroupOrderUserService.queryByMasterTaskNum(subTask.getMasterTaskNumber());
                    if (Objects.nonNull(sohuImGroupOrderUser) && Objects.nonNull(mainTask) && Objects.equals(mainTask.getDeliveryStandard(), standNum)) {
                        planVo.setGroupUserUpdateTime(sohuImGroupOrderUser.getUpdateTime());
                    }
                    planVo.setTaskReceUserName(StringUtils.sensitive(1, subTaskUser.getNickname(), 2, false));
                    imGroupVo.setPlanVo(planVo);
                }
            }
        }
    }

    private void evictGroup(Long groupId) {
        CacheMgr.evict(ImCacheConstants.GROUP, String.valueOf(groupId));
    }

}
