package com.sohu.im.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.im.api.bo.SohuImChatMessageBo;
import com.sohu.im.api.bo.SohuImChatMessageQueryBo;
import com.sohu.im.api.enums.ImRiskTypeEnum;
import com.sohu.im.api.vo.SohuImChatMessageVo;

import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;

/**
 * im消息Service接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface ISohuImChatMessageService {

    /**
     * 查询im消息
     */
    SohuImChatMessageVo queryById(Long id);

    /**
     * 查询im消息列表
     */
    TableDataInfo<SohuImChatMessageVo> queryPageList(SohuImChatMessageBo bo, PageQuery pageQuery);

    /**
     * 查询im消息列表
     */
    List<SohuImChatMessageVo> queryList(SohuImChatMessageBo bo);

    /**
     * 修改im消息
     */
    Boolean insertByBo(SohuImChatMessageBo bo);

    /**
     * 修改im消息
     */
    Boolean updateByBo(SohuImChatMessageBo bo);

    /**
     * 校验并批量删除im消息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean deleteByChatId(Long chatId);

    /**
     * IM上传文件超时7天处理
     *
     * @return {@link Boolean}
     */
    Boolean imFileOverTimeHandler();

    /**
     * 聊天记录修改会话类型
     *
     * @param groupId      群id
     * @param oldGroupType 原会话类型 {@link com.sohu.im.api.enums.ImSessionTypeEnum}
     * @param newGroupType 新会话类型 {@link com.sohu.im.api.enums.ImSessionTypeEnum}
     * @return {@link Boolean}
     */
    Boolean updateSessionType(Long groupId, String oldGroupType, String newGroupType);

    /**
     * 查询im消息列表-管理端
     */
    TableDataInfo<SohuImChatMessageVo> queryPageListOfManage(SohuImChatMessageQueryBo bo, PageQuery pageQuery);

    /**
     * 消息撤回,管理员或者本人撤回IM消息
     *
     * @param id sohu_im_chat_message表的主键id
     * @return {@link Boolean}
     */
    Boolean recallMessage(@NotNull(message = "撤回消息id不能为空！") Long id);

    /**
     * 设置nano时间
     *
     * @return
     */
    int setNanoTime();

    /**
     * 更新IM风险信息
     *
     * @param busyType  检测类型 {@link ImRiskTypeEnum}
     * @param busyCode  检测对象，对应表主键ID或者其它唯一值
     * @param checkPass 是否通过检测 ,true=检测通过
     * @return
     */
    Boolean updateImRisk(String busyType, String busyCode, boolean checkPass);

}
