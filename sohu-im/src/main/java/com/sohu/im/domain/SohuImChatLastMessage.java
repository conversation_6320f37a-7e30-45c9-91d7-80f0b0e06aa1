package com.sohu.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * im外层消息对象 sohu_im_chat_last_message
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_im_chat_last_message")
public class SohuImChatLastMessage extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 内容
     */
    private String content;
    /**
     * 被拦截的原因，比如文字涉恐、反共、非好友、或其他规则
     */
    private String err;
    /**
     * 发送人ID
     */
    private Long senderId;
    /**
     * 接收人id，用户ID或群ID
     */
    private Long receiverId;
    /**
     * 会话类型:single - 单聊、group-群聊
     */
    private String sessionType;
    /**
     * 消息类型;(text - 文本、photo - 图片、video - 视频、voice - 语音、share - 分享、voiceCall - 语音通话、groupVoiceCall-群语音通话 ,
     * videoCall - 视频通话、groupCall - 群视频、notice - 公告、file - 文件、command - 命令)
     */
    private String messageType;
    /**
     * 分享类型，task-任务，good-商品，article-图文，video-视频，goodWindow-商品橱窗
     */
    private String shareType;
    /**
     * json，不做处理
     */
    private String shareParam;
    /**
     * 消息是否已读(1=已读 0=未读)
     */
    private Boolean readType;
    /**
     * 分享数据的id
     */
    private Long shareId;
    /**
     * 唯一值，纳秒
     */
    private Long chatId;
    /**
     * 会话id
     */
    private Long msgId;
    /**
     * 语音消息或视频消息的时长
     */
    private Integer duration;
    /**
     * 文件大小，单位kb
     */
    private long fileSize;
    /**
     * 文件名
     */
    private String fileName;

    /**
     * 这个是用来本地识别消息关联发送状态
     */
    private String localId;
    /**
     * at 群内成员的userId，英文逗号分隔，如果列表中有0即表示@所有人
     */
    private String atIds;
    /**
     * 命令类型
     * {@link com.sohu.im.api.enums.ImCommandTypeEnum}
     */
    private String commandType;
    /**
     * 商户店铺id
     */
    private Long merchantId;
    /**
     * 消息来源
     */
    private String msgSource;
}
