package com.sohu.im.server;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import com.sohu.common.mybatis.db.CacheMgr;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.im.api.bo.ImChatRequestBo;
import com.sohu.im.api.constant.ImCacheConstants;
import com.sohu.im.api.enums.ImMessageTypeEnum;
import com.sohu.im.api.enums.ImRiskTypeEnum;
import com.sohu.im.utfil.ImRiskCheckUtil;
import com.sohu.im.utfil.ImSocketResponseUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ImContentModerationProcessor {

    private final ImRiskCheckUtil imRiskCheckUtil;

    public boolean handlePreSend(ImChatRequestBo bo, LoginUser sender) {
        String messageType = bo.getMessageType();
        String content = bo.getContent();

        // 文本类消息,图片类消息，同步审核
        if (StrUtil.equalsAnyIgnoreCase(messageType, ImMessageTypeEnum.Text.getCode(), ImMessageTypeEnum.Image.getCode())) {
            String plainText = HtmlUtil.cleanHtmlTag(content);
            boolean risky = imRiskCheckUtil.syncCheck(plainText, bo.getLocalId(), ImRiskTypeEnum.getByImMessageType(bo.getSessionType(), bo.getMessageType()));
            if (!risky) {
                bo.setHidden(true);
                if (StrUtil.equalsAnyIgnoreCase(messageType, ImMessageTypeEnum.Image.getCode())) {
                    bo.setContent(ImRiskTypeEnum.RISK_DEFAULT_IMAGE);
                }
                bo.setErr(ImSocketResponseUtil.errKeyword); // 标记错误信息
                return false; // 阻断发送
            }
        }
        // 先标记为 hidden，异步处理
        if (StrUtil.equalsAnyIgnoreCase(messageType,
                ImMessageTypeEnum.Video.getCode(),
                ImMessageTypeEnum.Voice.getCode(),
                ImMessageTypeEnum.File.getCode())) {
            bo.setRealSenderId(sender.getUserId());
            bo.setRealReceiverId(bo.getReceiverId());
            bo.setHidden(true);
            imRiskCheckUtil.asyncCheck(bo.getContent(), bo.getLocalId(), ImRiskTypeEnum.getByImMessageType(bo.getSessionType(), bo.getMessageType()));
            CacheMgr.set(ImCacheConstants.IM_RISK_CHECK, bo.getLocalId(), bo);
            return false; // 阻断发送
        }
        return true;
    }
}
