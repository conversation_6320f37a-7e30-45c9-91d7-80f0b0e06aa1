package com.sohu.im.server.handle;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.common.core.utils.RandomUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.im.api.bo.ImChatRequestBo;
import com.sohu.im.api.enums.ImCommandTypeEnum;
import com.sohu.im.api.enums.ImGroupPermissionType;
import com.sohu.im.api.enums.ImMessageTypeEnum;
import com.sohu.im.api.vo.ImChatResponseVo;
import com.sohu.im.api.vo.SohuGroupKeywordVo;
import com.sohu.im.api.vo.SohuImGroupVo;
import com.sohu.im.domain.SohuImGroupUser;
import com.sohu.im.server.ImContentModerationProcessor;
import com.sohu.im.server.ImServerConfig;
import com.sohu.im.server.enums.ImChatHandleEnum;
import com.sohu.im.service.ISohuGroupKeywordService;
import com.sohu.im.service.ISohuImGroupService;
import com.sohu.im.service.ISohuImGroupUserService;
import com.sohu.im.utfil.ImSocketResponseUtil;
import com.sohu.im.utfil.ImUnReadUtil;
import com.sohu.middle.api.service.RemoteMiddleFriendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.tio.core.ChannelContext;
import org.tio.core.Tio;
import org.tio.websocket.common.WsResponse;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 任务客户群聊天
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class GroupTaskCustomChatHandler implements ImChatHandleEnum.ChatHandler {

    private final ISohuImGroupUserService sohuImGroupUserService;
    private final ISohuGroupKeywordService sohuGroupKeywordService;
    private final ISohuImGroupService sohuImGroupService;
    @DubboReference
    private RemoteMiddleFriendService remoteMiddleFriendService;
    private final ImContentModerationProcessor moderationProcessor;

    @Override
    public void handle(LoginUser sender, ImChatRequestBo bo, ChannelContext channelContext) {
        log.info("-----GroupTaskCustomChatHandler----: {} 给 {} 发送任务客户群IM消息:{}", sender.getUserId(), bo.getReceiverId(), bo.getContent());
        Long groupId = bo.getReceiverId();
        ImChatResponseVo responseVo = ImSocketResponseUtil.buildResponseVo(bo, sender);
        WsResponse wsResponse = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
        SohuImGroupVo imGroupVo = sohuImGroupService.get(groupId);

        // 封控检测
        boolean pass = moderationProcessor.handlePreSend(bo, sender);
        if (!pass) {
            responseVo.getBody().setErr(ImSocketResponseUtil.errKeyword);
            wsResponse = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
            Tio.sendToUser(channelContext.tioConfig, String.valueOf(sender.getUserId()), wsResponse);
            log.info("群聊：发送消息内容违规，,发送人ID:{},发送消息内容:{},发送失败！", sender.getUserId(), bo.getContent());
            return;
        }

        // 所有群用户
        List<SohuImGroupUser> groupUsers = sohuImGroupUserService.groupUserList(bo.getReceiverId());
        String role = responseVo.getSender().getRole();
        if (imGroupVo.getForbid()) {
            if (StrUtil.isNotBlank(role) && StrUtil.equalsAnyIgnoreCase(role, ImGroupPermissionType.group_leader.name(), ImGroupPermissionType.group_admin.name())) {
                Tio.sendToGroup(channelContext.tioConfig, bo.getReceiverId().toString(), wsResponse);
                log.info("此群开启了群全员禁言，只能群主或群管理员发言,群ID：{},发送人ID:{},发送人身份：{},发送消息内容:{}", groupId, sender.getUserId(), role, bo.getContent());
            } else {
                Tio.sendToUser(channelContext.tioConfig, String.valueOf(sender.getUserId()), wsResponse);
                log.info("此群开启了群全员禁言，只能群主或群管理员发言,群ID：{},发送人ID:{},发送人身份：{},发送消息内容:{},发送失败！", groupId, sender.getUserId(), role, bo.getContent());
                return;
            }
        } else if (responseVo.getSender().getForbid()) {
            Tio.sendToUser(channelContext.tioConfig, String.valueOf(sender.getUserId()), wsResponse);
            log.info("发送人被禁言，,发送人ID:{},发送人身份：{},发送消息内容:{},发送失败！", sender.getUserId(), role, bo.getContent());
            return;
        } else {
            SohuGroupKeywordVo keywordVo = sohuGroupKeywordService.getByGroupId(groupId);
            if (Objects.nonNull(keywordVo) && keywordVo.getKeywordOpen() && StrUtil.isNotBlank(keywordVo.getKeyword())) {
                String whiteList = keywordVo.getWhiteList();
                long[] userIds = StrUtil.splitToLong(whiteList, StrPool.C_COMMA);
                List<String> keywordList = StrUtil.split(keywordVo.getKeyword(), StrPool.C_COMMA);
                if (Arrays.stream(userIds).noneMatch(id -> id == sender.getUserId()) &&
                        (StrUtil.containsAnyIgnoreCase(keywordVo.getKeyword(), bo.getContent()) || keywordList.stream()
                                .anyMatch(keyword -> StrUtil.containsAnyIgnoreCase(bo.getContent(), keyword)))) {
                    bo.setContent(ImSocketResponseUtil.errKeyword);
                    ImChatResponseVo responseVoError = ImSocketResponseUtil.buildResponseVo(bo, sender);
                    responseVoError.setMessageType(ImMessageTypeEnum.Command.getCode());
                    responseVoError.setCommandType(ImCommandTypeEnum.chatLimitErrTip.getCode());
                    responseVoError.setChatId(System.nanoTime());
                    responseVoError.setLocalId(bo.getLocalId() + RandomUtils.randomString(3));
                    WsResponse wsResponseError = WsResponse.fromText(JSONUtil.toJsonStr(responseVoError), ImServerConfig.CHARSET);
                    Tio.sendToUser(channelContext.tioConfig, String.valueOf(sender.getUserId()), wsResponseError);
                    return;
                }
            }

            if (CollUtil.isNotEmpty(groupUsers)) {
                Long senderId = sender.getUserId();
                for (SohuImGroupUser groupUser : groupUsers) {
                    if (!Objects.equals(groupUser.getUserId(), senderId)) {
                        // 未读消息缓存
                        ImUnReadUtil.inrUnReadCount(bo.getSessionType(), groupUser.getGroupId(), groupUser.getUserId());
                    }
                    Tio.sendToUser(channelContext.tioConfig, String.valueOf(groupUser.getUserId()), wsResponse);
                }
            }
            //Tio.sendToGroup(channelContext.tioConfig, bo.getReceiverId().toString(), wsResponse);
            log.info("群消息发送成功,群ID：{},发送人ID:{},发送消息内容:{}", groupId, sender.getUserId(), bo.getContent());
        }

        ImChatHandleEnum.GROUP.sendMessage(sender.getUserId(), bo, (bo.getChatId() != null && bo.getChatId() > 0L) ? bo.getChatId() : responseVo.getChatId());
    }

    @Override
    public void sendMessage(Long senderId, ImChatRequestBo bo, Long chatId) {
        ImChatHandleEnum.GROUPTASKCUSTOM.sendMessage(senderId, bo, chatId);
    }

}
