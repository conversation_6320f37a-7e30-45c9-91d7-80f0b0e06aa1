package com.sohu.im.server.handle;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.common.core.enums.ApplyStateEnum;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.im.api.bo.ImChatRequestBo;
import com.sohu.im.api.bo.SohuImChatMessageBo;
import com.sohu.im.api.enums.ImCommandTypeEnum;
import com.sohu.im.api.enums.ImMessageTypeEnum;
import com.sohu.im.api.enums.ImSessionTypeEnum;
import com.sohu.im.api.vo.ImChatResponseVo;
import com.sohu.im.api.vo.SohuImChatMessageVo;
import com.sohu.im.server.ImContentModerationProcessor;
import com.sohu.im.server.ImServerConfig;
import com.sohu.im.server.enums.ImChatHandleEnum;
import com.sohu.im.service.ISohuImChatMessageService;
import com.sohu.im.utfil.ImSocketResponseUtil;
import com.sohu.im.utfil.ImUnReadUtil;
import com.sohu.middle.api.service.RemoteMiddleFriendService;
import com.sohu.middle.api.service.RemoteMiddleUserFollowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.tio.core.ChannelContext;
import org.tio.core.Tio;
import org.tio.websocket.common.WsResponse;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.sohu.im.utfil.ImSocketResponseUtil.sendMsgAgainError;

/**
 * 普通单人聊天
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SingleChatHandler implements ImChatHandleEnum.ChatHandler {

    @DubboReference
    private RemoteMiddleFriendService remoteMiddleFriendService;
    @DubboReference
    private RemoteMiddleUserFollowService remoteMiddleUserFollowService;
    private final ImContentModerationProcessor moderationProcessor;

    public void handle(LoginUser sender, ImChatRequestBo bo, ChannelContext channelContext) {
        Long senderId = sender.getUserId();
        Long receiverId = bo.getReceiverId();

        log.info("-----SingleChatHandler----: {} 给 {} 发送IM消息:{}", senderId, receiverId, bo.getContent());

        // 封控检测
        boolean pass = moderationProcessor.handlePreSend(bo, sender);
        if (!pass) {
            ImChatResponseVo responseVo = ImSocketResponseUtil.buildResponseVo(bo, sender);
            responseVo.getBody().setErr(ImSocketResponseUtil.errKeyword);
            WsResponse response = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
            Tio.sendToUser(channelContext.tioConfig, String.valueOf(sender.getUserId()), response);
            return; // 不再继续处理
        }

        // 2. 构建响应消息体
        ImChatResponseVo responseVoForSender = ImSocketResponseUtil.buildResponseVo(bo, sender);
        ImChatResponseVo responseVoForReceiver = cloneResponseVo(responseVoForSender);

        // 3. 判断是否需要限制（例如陌生人多次发图文）
        if (isMessageLimited(senderId, receiverId, bo)) {
            applyLimitError(responseVoForSender, bo);
        }

        // 4. 发给接收方（如果非 hidden）
        if (!Boolean.TRUE.equals(bo.getHidden())) {
            sendToReceiver(receiverId, responseVoForReceiver, channelContext);
        }

        // 5. 发给发送方
        sendToSender(senderId, receiverId, responseVoForSender, bo, channelContext);

        // 6. 消息存储 + 未读计数
        if (!Boolean.TRUE.equals(bo.getHidden())) {
            handleMessagePersistence(senderId, receiverId, bo, responseVoForReceiver.getChatId());
        }

        // 7. 如果被限制，且包含错误信息，提示
        if (Boolean.TRUE.equals(bo.getHidden()) && StrUtil.isNotBlank(responseVoForSender.getBody().getErr())) {
            sendLimitErrorCommand(senderId, responseVoForSender, channelContext);
        }
    }

    private void rejectMessage(LoginUser sender, ImChatRequestBo bo, ChannelContext ctx, String reason) {
        ImChatResponseVo responseVo = ImSocketResponseUtil.buildResponseVo(bo, sender);
        ImChatResponseVo.BodyDTO body = responseVo.getBody();
        body.setContent(bo.getContent());
        body.setErr(reason);
        responseVo.setBody(body);
        responseVo.setChatId(System.nanoTime());
        responseVo.setCreateTime(System.currentTimeMillis());
        responseVo.setLocalId(bo.getLocalId() + "1");

        WsResponse wsResponse = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
        Tio.sendToUser(ctx.tioConfig, String.valueOf(sender.getUserId()), wsResponse);
    }

    private ImChatResponseVo cloneResponseVo(ImChatResponseVo source) {
        ImChatResponseVo target = new ImChatResponseVo();
        BeanUtil.copyProperties(source, target);
        return target;
    }

    private boolean isMessageLimited(Long senderId, Long receiverId, ImChatRequestBo bo) {
        if (Objects.equals(senderId, receiverId)) return false;
        if (remoteMiddleFriendService.customService(receiverId)) return false;

        boolean focus = remoteMiddleUserFollowService.eachFocus(senderId, receiverId);
        boolean isFriend = Objects.nonNull(remoteMiddleFriendService.queryOne(senderId, receiverId, ApplyStateEnum.pass.name()));
        if (focus || isFriend) return false;

        ISohuImChatMessageService imMessageService = SpringUtil.getBean(ISohuImChatMessageService.class);

        // 查接收方发送的消息
        SohuImChatMessageBo boForReceiver = new SohuImChatMessageBo();
        boForReceiver.setSenderId(receiverId);
        boForReceiver.setReceiverId(senderId);
        boForReceiver.setHidden(false);
        boForReceiver.setMessageTypeList(Arrays.asList(ImMessageTypeEnum.Text.getCode()));
        bo.setHidden(false);
        List<SohuImChatMessageVo> receiverMessages = imMessageService.queryList(boForReceiver);

        if (CollUtil.isEmpty(receiverMessages)) {
            // 接收方未回复消息, 则查发送人消息条数
            SohuImChatMessageBo boForSender = new SohuImChatMessageBo();
            boForSender.setSenderId(senderId);
            boForSender.setReceiverId(receiverId);
            boForSender.setSessionType(ImSessionTypeEnum.single.getCode());
            boForSender.setHidden(false);

            List<SohuImChatMessageVo> senderMessages = imMessageService.queryList(boForSender);
            return CollUtil.isNotEmpty(senderMessages) && senderMessages.size() >= 3;
        }
        return false;
    }

    private void applyLimitError(ImChatResponseVo responseVo, ImChatRequestBo bo) {
        String errMsg = String.format(sendMsgAgainError, ImSocketResponseUtil.limitNum);
        responseVo.getBody().setErr(errMsg);
        bo.setErr(errMsg);
        bo.setHidden(true);
    }

    private void sendToReceiver(Long receiverId, ImChatResponseVo responseVo, ChannelContext ctx) {
        WsResponse wsResponse = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
        Tio.sendToUser(ctx.tioConfig, String.valueOf(receiverId), wsResponse);
    }

    private void sendToSender(Long senderId, Long receiverId, ImChatResponseVo responseVo, ImChatRequestBo bo, ChannelContext ctx) {
        WsResponse wsResponse = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
        Tio.sendToUser(ctx.tioConfig, String.valueOf(senderId), wsResponse);
        if (!Objects.equals(senderId, receiverId)) {
            ImChatHandleEnum.SINGLE.sendMessage(senderId, bo, responseVo.getChatId());
        }
    }

    private void handleMessagePersistence(Long senderId, Long receiverId, ImChatRequestBo bo, Long chatId) {
        bo.setReceiverId(senderId);
        bo.setAddUnreadMessage(false);
        bo.setSaveInnerMessage(Objects.equals(senderId, receiverId));

        ImChatHandleEnum.SINGLE.sendMessage(receiverId, bo,
                (bo.getChatId() != null && bo.getChatId() > 0L) ? bo.getChatId() : chatId);

        if (BooleanUtil.isFalse(bo.getHidden())) {
            ImUnReadUtil.inrUnReadCount(bo.getSessionType(), senderId, receiverId);
        }
    }

    private void sendLimitErrorCommand(Long senderId, ImChatResponseVo responseVo, ChannelContext ctx) {
        ImChatResponseVo.BodyDTO body = responseVo.getBody();
        body.setContent(body.getErr());

        responseVo.setBody(body);
        responseVo.setMessageType(ImMessageTypeEnum.Command.getCode());
        responseVo.setCommandType(ImCommandTypeEnum.chatLimitErrTip.getCode());
        responseVo.setChatId(System.nanoTime());
        responseVo.setCreateTime(System.currentTimeMillis());
        responseVo.setLocalId(responseVo.getLocalId() + "1");

        WsResponse wsResponse = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
        Tio.sendToUser(ctx.tioConfig, String.valueOf(senderId), wsResponse);
    }


    @Override
    public void sendMessage(Long senderId, ImChatRequestBo bo, Long chatId) {
        ImChatHandleEnum.SINGLE.sendMessage(senderId, bo, chatId);
    }

}
