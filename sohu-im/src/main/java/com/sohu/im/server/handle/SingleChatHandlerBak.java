package com.sohu.im.server.handle;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.common.core.enums.ApplyStateEnum;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.im.api.bo.ImChatRequestBo;
import com.sohu.im.api.bo.SohuImChatMessageBo;
import com.sohu.im.api.enums.ImCommandTypeEnum;
import com.sohu.im.api.enums.ImMessageTypeEnum;
import com.sohu.im.api.enums.ImSessionTypeEnum;
import com.sohu.im.api.vo.ImChatResponseVo;
import com.sohu.im.api.vo.SohuImChatMessageVo;
import com.sohu.im.server.ImServerConfig;
import com.sohu.im.server.enums.ImChatHandleEnum;
import com.sohu.im.service.ISohuImChatMessageService;
import com.sohu.im.utfil.ImSocketResponseUtil;
import com.sohu.im.utfil.ImUnReadUtil;
import com.sohu.middle.api.service.RemoteMiddleFriendService;
import com.sohu.middle.api.service.RemoteMiddleUserFollowService;
import com.sohu.third.aliyun.audit.constants.AliyunAuditLabelEnum;
import com.sohu.third.aliyun.audit.service.AliyunAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.tio.core.ChannelContext;
import org.tio.core.Tio;
import org.tio.websocket.common.WsResponse;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.sohu.im.utfil.ImSocketResponseUtil.sendMsgAgainError;

/**
 * 普通单人聊天
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SingleChatHandlerBak implements ImChatHandleEnum.ChatHandler {

    @DubboReference
    private RemoteMiddleFriendService remoteMiddleFriendService;
    @DubboReference
    private RemoteMiddleUserFollowService remoteMiddleUserFollowService;
    @Resource
    private AliyunAuditService aliyunAuditService;

    @Override
    public void handle(LoginUser sender, ImChatRequestBo bo, ChannelContext channelContext) {
        log.info("-----SingleChatHandler----: {} 给 {} 发送普通单人IM消息:{}", sender.getUserId(), bo.getReceiverId(), bo.getContent());
        Long senderId = sender.getUserId();
        Long receiverId = bo.getReceiverId();
        // 发送者消息体
        ImChatResponseVo responseVoForSender = ImSocketResponseUtil.buildResponseVo(bo, sender);

        if(StrUtil.equalsAnyIgnoreCase(ImMessageTypeEnum.Text.getCode(),bo.getMessageType())){
            String scanText = aliyunAuditService.scanText(HtmlUtil.cleanHtmlTag(bo.getContent()), AliyunAuditLabelEnum.textCheck);
            if (StrUtil.isNotBlank(scanText)) {
                responseVoForSender.getBody().setErr(ImSocketResponseUtil.errKeyword);
                WsResponse wsResponseError = WsResponse.fromText(JSONUtil.toJsonStr(responseVoForSender), ImServerConfig.CHARSET);
                Tio.sendToUser(channelContext.tioConfig, String.valueOf(sender.getUserId()), wsResponseError);
                log.info("单聊：发送消息内容违规，,发送人ID:{},发送消息内容:{},发送失败！", sender.getUserId(), bo.getContent());
                return;
            }
        }

        // 接收者消息体
        ImChatResponseVo responseVoForReceiver = new ImChatResponseVo();
        BeanUtil.copyProperties(responseVoForSender, responseVoForReceiver);

        WsResponse wsResponseForSender = null;
        WsResponse wsResponseForReceiver = null;

        if (!Objects.equals(senderId, receiverId)) {
            boolean customService = remoteMiddleFriendService.customService(receiverId);
            if (!customService) {
                // 是否互相关注
                boolean focus = remoteMiddleUserFollowService.eachFocus(senderId, receiverId);
                // 是否是好友
                boolean isFriend = Objects.nonNull(remoteMiddleFriendService.queryOne(senderId, receiverId, ApplyStateEnum.pass.name()));
                if (!focus && !isFriend) {
                    ISohuImChatMessageService imMessageService = SpringUtil.getBean(ISohuImChatMessageService.class);
                    // 查接收方发送的消息
                    SohuImChatMessageBo boForReceiver = new SohuImChatMessageBo();
                    boForReceiver.setSenderId(receiverId);
                    boForReceiver.setReceiverId(senderId);
                    boForReceiver.setHidden(false);
                    boForReceiver.setMessageTypeList(Arrays.asList(ImMessageTypeEnum.Text.getCode()));
                    bo.setHidden(false);
                    List<SohuImChatMessageVo> messageVoListForReceiver = imMessageService.queryList(boForReceiver);
                    if (CollUtil.isEmpty(messageVoListForReceiver)) {
                        // 接收方未回复消息, 则查发送人消息条数
                        SohuImChatMessageBo boForSender = new SohuImChatMessageBo();
                        boForSender.setSenderId(senderId);
                        boForSender.setReceiverId(receiverId);
                        boForSender.setSessionType(ImSessionTypeEnum.single.getCode());
                        boForSender.setHidden(false);
                        List<SohuImChatMessageVo> messageVoListForSender = imMessageService.queryList(boForSender);
                        if (CollUtil.isNotEmpty(messageVoListForSender) && messageVoListForSender.size() >= 3) {
                            responseVoForSender.getBody().setErr(String.format(sendMsgAgainError, ImSocketResponseUtil.limitNum));
                            bo.setErr(String.format(sendMsgAgainError, ImSocketResponseUtil.limitNum));
                            bo.setHidden(true);
                        }
                    }
                }
            }
        }


        if (bo.getHidden() != null && bo.getHidden()) {
            responseVoForSender.getBody().setErr(String.format(sendMsgAgainError, ImSocketResponseUtil.limitNum));
            bo.setErr(String.format(sendMsgAgainError, ImSocketResponseUtil.limitNum));
        } else {
            wsResponseForReceiver = WsResponse.fromText(JSONUtil.toJsonStr(responseVoForReceiver), ImServerConfig.CHARSET);
            // 给接收方发送socket消息
            Tio.sendToUser(channelContext.tioConfig, String.valueOf(receiverId), wsResponseForReceiver);
        }
        wsResponseForSender = WsResponse.fromText(JSONUtil.toJsonStr(responseVoForSender), ImServerConfig.CHARSET);
        // 给发送方发送socket消息
        if (!Objects.equals(senderId, receiverId)) {
            // 判断是否给自己发
            Tio.sendToUser(channelContext.tioConfig, String.valueOf(senderId), wsResponseForSender);
            ImChatHandleEnum.SINGLE.sendMessage(senderId, bo, responseVoForSender.getChatId());
        }
        if (bo.getHidden() != null && !bo.getHidden()) {
            bo.setReceiverId(senderId);
            bo.setSaveInnerMessage(false);
            bo.setAddUnreadMessage(false);
            if(Objects.equals(senderId, receiverId)) {
                bo.setSaveInnerMessage(true);
            }
            ImChatHandleEnum.SINGLE.sendMessage(receiverId, bo, (bo.getChatId() != null && bo.getChatId() > 0L) ? bo.getChatId() : responseVoForReceiver.getChatId());
            // 未读消息缓存
            ImUnReadUtil.inrUnReadCount(bo.getSessionType(), senderId, receiverId);
        }
        if (bo.getHidden() != null && bo.getHidden() && StrUtil.isNotBlank(responseVoForSender.getBody().getErr())) {
            String err = responseVoForSender.getBody().getErr();
            ImChatResponseVo.BodyDTO body = responseVoForSender.getBody();
            body.setContent(err);
            responseVoForSender.setChatId(System.nanoTime());
            responseVoForSender.setLocalId(responseVoForSender.getLocalId() + "1");
            responseVoForSender.setMessageType(ImMessageTypeEnum.Command.getCode());
            responseVoForSender.setCommandType(ImCommandTypeEnum.chatLimitErrTip.getCode());
            responseVoForSender.setCreateTime(System.currentTimeMillis());
            responseVoForSender.setBody(body);
            wsResponseForSender = WsResponse.fromText(JSONUtil.toJsonStr(responseVoForSender), ImServerConfig.CHARSET);
            // 给发送方发送socket消息
            Tio.sendToUser(channelContext.tioConfig, String.valueOf(senderId), wsResponseForSender);
        }
    }

    @Override
    public void sendMessage(Long senderId, ImChatRequestBo bo, Long chatId) {
        ImChatHandleEnum.SINGLE.sendMessage(senderId, bo, chatId);
    }

}
