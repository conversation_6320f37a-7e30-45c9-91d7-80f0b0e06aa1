package com.sohu.im.server.handle;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.im.api.bo.ImChatRequestBo;
import com.sohu.im.api.bo.SohuImGroupDisturbBo;
import com.sohu.im.api.bo.SohuImRemoveGroupUserBo;
import com.sohu.im.api.enums.ImCommandTypeEnum;
import com.sohu.im.api.enums.ImGroupPermissionType;
import com.sohu.im.api.enums.ImMessageTypeEnum;
import com.sohu.im.api.vo.ImChatResponseVo;
import com.sohu.im.api.vo.SohuImGroupDisturbVo;
import com.sohu.im.api.vo.SohuImGroupForbidTimeVo;
import com.sohu.im.api.vo.SohuImGroupVo;
import com.sohu.im.domain.SohuImGroupUser;
import com.sohu.im.server.ImContentModerationProcessor;
import com.sohu.im.server.ImServerConfig;
import com.sohu.im.server.enums.ImChatHandleEnum;
import com.sohu.im.service.ISohuBatchSendService;
import com.sohu.im.service.ISohuImGroupDisturbService;
import com.sohu.im.service.ISohuImGroupService;
import com.sohu.im.service.ISohuImGroupUserService;
import com.sohu.im.utfil.ImSocketResponseUtil;
import com.sohu.middle.api.service.RemoteMiddleFriendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.tio.core.ChannelContext;
import org.tio.core.Tio;
import org.tio.websocket.common.WsResponse;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.sohu.im.utfil.ImSocketResponseUtil.errKeyword;

/**
 * 普通群聊
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class GroupChatHandler implements ImChatHandleEnum.ChatHandler {

    private final ISohuImGroupUserService sohuImGroupUserService;
    private final ISohuImGroupDisturbService sohuImGroupDisturbService;
    private final ISohuImGroupService sohuImGroupService;
    private final ISohuBatchSendService sohuBatchSendService;
    @DubboReference
    private RemoteMiddleFriendService remoteMiddleFriendService;
    private final ImContentModerationProcessor moderationProcessor;

    @Override
    public void handle(LoginUser sender, ImChatRequestBo bo, ChannelContext channelContext) {
        log.info("-----GroupChatHandler----: {} 给 {} 发送普通群IM消息:{}", sender.getUserId(), bo.getReceiverId(), bo.getContent());
        Long groupId = bo.getReceiverId();
        Long senderId = sender.getUserId();
        // 所有群用户
        List<SohuImGroupUser> groupUsers = sohuImGroupUserService.groupUserList(bo.getReceiverId());

        // 发送方
        SohuImGroupUser groupUserSend = null;

        // 接收者集合
        List<Long> groupUserReceiveIds = new ArrayList<>();

        for (SohuImGroupUser groupUser : groupUsers) {
            if (!Objects.equals(groupUser.getUserId(), senderId)) {
                groupUserReceiveIds.add(groupUser.getUserId());
            } else {
                sender.setGroupUserNickName(StrUtil.isBlankIfStr(groupUser.getGroupUserNickName()) ? sender.getNickname() : groupUser.getGroupUserNickName());
                groupUserSend = groupUser;
            }
        }

        ImChatResponseVo responseVo = ImSocketResponseUtil.buildResponseVo(bo, sender);
        WsResponse wsResponse = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
        SohuImGroupVo imGroupVo = sohuImGroupService.get(groupId);

        String role = responseVo.getSender().getRole();
        // 是否是群主或者管理员
        boolean groupManager = StrUtil.isNotBlank(role) && StrUtil.equalsAnyIgnoreCase(role, ImGroupPermissionType.group_leader.name(), ImGroupPermissionType.group_admin.name());
        if (BooleanUtil.isTrue(imGroupVo.getForbid())) {
            if (groupManager) {
                Tio.sendToGroup(channelContext.tioConfig, bo.getReceiverId().toString(), wsResponse);
                log.info("此群开启了群全员禁言，只能群主或群管理员发言,群ID：{},发送人ID:{},发送人身份：{},发送消息内容:{}", groupId, sender.getUserId(), role, bo.getContent());
            } else {
                responseVo.getBody().setErr(ImSocketResponseUtil.GROUP_ALL_FORBID_OPEN);
                wsResponse = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
                Tio.sendToUser(channelContext.tioConfig, String.valueOf(sender.getUserId()), wsResponse);
                log.info("此群开启了群全员禁言，只能群主或群管理员发言,群ID：{},发送人ID:{},发送人身份：{},发送消息内容:{},发送失败！", groupId, sender.getUserId(), role, bo.getContent());
                return;
            }
        } else if (BooleanUtil.isTrue(responseVo.getSender().getForbid())) {
            responseVo.getBody().setErr(ImSocketResponseUtil.GROUP_USER_FORBID);
            wsResponse = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
            Tio.sendToUser(channelContext.tioConfig, String.valueOf(sender.getUserId()), wsResponse);
            log.info("发送人被禁言，,发送人ID:{},发送人身份：{},发送消息内容:{},发送失败！", sender.getUserId(), role, bo.getContent());
            return;
        } else if (BooleanUtil.isTrue(imGroupVo.getForbidTime())) {
            if (groupManager) {
                Tio.sendToGroup(channelContext.tioConfig, bo.getReceiverId().toString(), wsResponse);
                log.info("此群开启了按时间段禁言，只能群主或群管理员发言,群ID：{},发送人ID:{},发送人身份：{},发送消息内容:{}", groupId, sender.getUserId(), role, bo.getContent());
            } else {
                if (CollUtil.isNotEmpty(imGroupVo.getTimeList())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
                    LocalTime now = LocalTime.now();
                    for (SohuImGroupForbidTimeVo timeVo : imGroupVo.getTimeList()) {
                        LocalTime startTime = LocalTime.parse(timeVo.getStartTime() + ":00", formatter);
                        LocalTime endTime = LocalTime.parse(timeVo.getEndTime() + ":59", formatter);
                        if (now.isAfter(startTime) && now.isBefore(endTime)) {
                            // 如果当前时间在禁言开始之后,禁言结束之前则需要进行禁言操作
                            responseVo.getBody().setErr(ImSocketResponseUtil.GROUP_FORBID_TIME_OPEN);
                            wsResponse = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
                            Tio.sendToUser(channelContext.tioConfig, String.valueOf(sender.getUserId()), wsResponse);
                            log.info("此群开启了按时间段禁言且在禁言时段内，,发送人ID:{},发送人身份：{},发送消息内容:{},发送失败！", sender.getUserId(), role, bo.getContent());
                            return;
                        }
                    }
                }
                boolean handleDisturb = handleDisturb(imGroupVo, bo, sender, responseVo, wsResponse, channelContext);
                if (handleDisturb) {
                    return;
                }
            }
        } else {
            if (!groupManager) {
                boolean handleDisturb = handleDisturb(imGroupVo, bo, sender, responseVo, wsResponse, channelContext);
                if (handleDisturb) {
                    return;
                }
            }
        }

        // 封控检测
        boolean pass = moderationProcessor.handlePreSend(bo, sender);
        if (!pass) {
            responseVo.getBody().setErr(ImSocketResponseUtil.errKeyword);
            wsResponse = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
            Tio.sendToUser(channelContext.tioConfig, String.valueOf(sender.getUserId()), wsResponse);
            log.info("群聊：发送消息内容违规，,发送人ID:{},发送消息内容:{},发送失败！", sender.getUserId(), bo.getContent());
            return;
        }


        if (CollUtil.isNotEmpty(groupUsers)) {

            ImChatResponseVo.SenderDTO senderDTO = responseVo.getSender();
            sender.setGroupUserNickName(StringUtils.getValidString(groupUserSend.getGroupUserNickName(), sender.getNickname(), sender.getUsername()));
            responseVo.setSender(senderDTO);
            wsResponse = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
            Tio.sendToGroup(channelContext.tioConfig, bo.getReceiverId().toString(), wsResponse);
        }
        log.info("群消息发送成功,群ID：{},发送人ID:{},发送消息内容:{}", groupId, sender.getUserId(), bo.getContent());
        // 异步增加未读消息数
        sohuBatchSendService.asyncInrUnRead(groupUserReceiveIds, groupId, bo.getSessionType(), senderId);
        ImChatHandleEnum.GROUP.sendMessage(sender.getUserId(), bo, (bo.getChatId() != null && bo.getChatId() > 0L) ? bo.getChatId() : responseVo.getChatId());
    }

    @Override
    public void sendMessage(Long senderId, ImChatRequestBo bo, Long chatId) {
        ImChatHandleEnum.GROUP.sendMessage(senderId, bo, chatId);
    }


    /**
     * 是否触发了防骚扰
     *
     * @param imGroupVo
     * @param bo
     * @param sender
     * @param responseVo
     * @param wsResponse
     * @param channelContext
     * @return true，标识已触发
     */
    public boolean handleDisturb(SohuImGroupVo imGroupVo, ImChatRequestBo bo, LoginUser sender, ImChatResponseVo responseVo, WsResponse wsResponse, ChannelContext channelContext) {
        // 获取群的防骚扰设置详细信息
        SohuImGroupDisturbVo groupDisturbVo = sohuImGroupDisturbService.getByGroupId(imGroupVo.getId(), imGroupVo.getUserId());

        if (Objects.isNull(groupDisturbVo)) {
            return false;
        }
        if (!sohuImGroupDisturbService.checkDisturb(imGroupVo, bo.getContent())) {
            return false;
        }
        if (groupDisturbVo.getPunishType() == null) {
            return false;
        }
        // 触发规则后的处理，1=踢出群聊，2=不展示消息
        if (groupDisturbVo.getPunishType() == SohuImGroupDisturbBo.PUNISH_REMOVE_GROUP) {
            // 踢出群聊
            SohuImRemoveGroupUserBo removeGroupUserBo = SohuImRemoveGroupUserBo.builder().groupId(imGroupVo.getId()).userId(sender.getUserId()).removeActive(false).operatorUserId(imGroupVo.getUserId()).build();
            sohuImGroupUserService.removeUser(removeGroupUserBo);
            log.info("触发了防骚扰规则，发送者被移除群聊,发送人ID:{},发送消息内容:{}", sender.getUserId(), bo.getContent());
            responseVo.setMessageType(ImMessageTypeEnum.Command.getCode());
            responseVo.setCommandType(ImCommandTypeEnum.kickGroup.getCode());
            ImChatResponseVo.BodyDTO body = responseVo.getBody();
            body.setContent("由于您触发了防骚扰规则，您已被被移除群聊");
            responseVo.setBody(body);
            wsResponse = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
            Tio.sendToUser(channelContext.tioConfig, String.valueOf(sender.getUserId()), wsResponse);
            return true;
        }
        if (groupDisturbVo.getPunishType() == SohuImGroupDisturbBo.PUNISH_HIDDEN_CONTENT) {
            // 不展示消息
            responseVo.getBody().setErr(errKeyword);
            bo.setErr(errKeyword);
            bo.setHidden(true);
            wsResponse = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
            Tio.sendToUser(channelContext.tioConfig, String.valueOf(sender.getUserId()), wsResponse);
            log.info("触发了防骚扰规则，,发送人ID:{},发送消息内容:{},发送失败！", sender.getUserId(), bo.getContent());
            return true;
        }
        return false;
    }

}
