package com.sohu.im.server.enums;

import cn.hutool.extra.spring.SpringUtil;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.im.api.bo.ImChatRequestBo;
import com.sohu.im.server.handle.*;
import com.sohu.im.service.ISohuImSendService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.tio.core.ChannelContext;

/**
 * im 普通 聊天处理枚举
 */
@Getter
@AllArgsConstructor
@Slf4j
public enum ImChatHandleEnum {

    SINGLE("single", "单聊", SpringUtil.getBean(SingleChatHandler.class)),
    MERCHANT("merchant", "商城消息", SpringUtil.getBean(MerchantChatHandler.class)),
    GROUP("group", "群聊", SpringUtil.getBean(GroupChatHandler.class)),
    GROUPTASK("groupTask", "任务群聊", SpringUtil.getBean(GroupTaskChatHandler.class)),
    GROUPTASKCUSTOM("groupTaskCustom", "任务客户群聊", SpringUtil.getBean(GroupTaskCustomChatHandler.class)),
    GROUPFORM("groupForm", "表单任务主群", SpringUtil.getBean(GroupFormChatHandler.class)),
    GROUPFORMCUSTOM("groupFormCustom", "表单任务客户群", SpringUtil.getBean(GroupFormCustomChatHandler.class)),
    GROUPFROMGENERAL("groupFromGeneral", "普通商单群", SpringUtil.getBean(GroupFromGeneralChatHandler.class)),
    GROUPAGENT("groupAgent", "服务商专属群聊", SpringUtil.getBean(GroupAgentChatHandler.class)),
    ;

    private String code;
    private final String explain;
    private final ChatHandler chatHandler;

    public void send(LoginUser sender, ImChatRequestBo bo, ChannelContext channelContext) {
        chatHandler.handle(sender, bo, channelContext);
    }

    /**
     * 抽象聊天处理接口
     */
    public interface ChatHandler {
        void handle(LoginUser sender, ImChatRequestBo bo, ChannelContext channelContext);

        void sendMessage(Long senderId, ImChatRequestBo bo, Long chatId);
    }

    public void sendMessage(Long senderId, ImChatRequestBo bo, Long chatId) {
        ISohuImSendService sohuImSendService = SpringUtil.getBean(ISohuImSendService.class);
        bo.setChatId(chatId);
        // 保存发送者记录
        sohuImSendService.sendMessage(senderId, bo);
    }

}


