package com.sohu.im.server.handle;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.common.core.enums.ApplyStateEnum;
import com.sohu.common.core.enums.NoticeOuterEnum;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.im.api.bo.ImChatRequestBo;
import com.sohu.im.api.enums.ImCommandTypeEnum;
import com.sohu.im.api.enums.ImMessageTypeEnum;
import com.sohu.im.api.vo.ImChatResponseVo;
import com.sohu.im.server.ImContentModerationProcessor;
import com.sohu.im.server.ImServerConfig;
import com.sohu.im.server.enums.ImChatHandleEnum;
import com.sohu.im.utfil.ImSocketResponseUtil;
import com.sohu.middle.api.bo.notice.SohuOuterNoticeBo;
import com.sohu.middle.api.service.RemoteMiddleFriendService;
import com.sohu.middle.api.service.notice.RemoteMiddleOuterNoticeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.tio.core.ChannelContext;
import org.tio.core.Tio;
import org.tio.websocket.common.WsResponse;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

/**
 * 商家单人聊天
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantChatHandler implements ImChatHandleEnum.ChatHandler {

    @DubboReference
    private RemoteMiddleOuterNoticeService remoteMiddleOuterNoticeService;
    @DubboReference
    private RemoteMerchantService remoteMerchantService;
    @DubboReference
    private RemoteMiddleFriendService remoteMiddleFriendService;
    private final ImContentModerationProcessor moderationProcessor;

    @Override
    public void handle(LoginUser sender, ImChatRequestBo bo, ChannelContext channelContext) {
        log.info("-----MerchantChatHandler----: {} 给 {} 发送商家IM消息:{}", sender.getUserId(), bo.getReceiverId(), bo.getContent());
        Long senderId = sender.getUserId();
        Long receiverId = bo.getReceiverId();
        // 店铺ID
        Long merchantId = bo.getMerchantId();
        if (merchantId == null || merchantId <= 0L) {
            return;
        }
        SohuMerchantModel merchantModel = remoteMerchantService.selectById(merchantId);
        if (merchantModel == null) {
            return;
        }

        // 封控检测
        boolean pass = moderationProcessor.handlePreSend(bo, sender);
        if (!pass) {
            ImChatResponseVo responseVo = ImSocketResponseUtil.buildResponseVo(bo, sender);
            responseVo.getBody().setErr(ImSocketResponseUtil.errKeyword);
            WsResponse response = WsResponse.fromText(JSONUtil.toJsonStr(responseVo), ImServerConfig.CHARSET);
            Tio.sendToUser(channelContext.tioConfig, String.valueOf(sender.getUserId()), response);
            return; // 不再继续处理
        }

        // 店铺持有者
        Long userId = merchantModel.getUserId();

        bo.setAddUnreadMessage(false);

        // 商家欢迎语指令消息处理，变更类型，以方便入库处理
        if (StrUtil.equalsAnyIgnoreCase(bo.getMessageType(), ImMessageTypeEnum.Command.getCode()) &&
                StrUtil.equalsAnyIgnoreCase(bo.getCommandType(), ImCommandTypeEnum.merchantwelcome.getCode())) {
            bo.setMessageType(ImMessageTypeEnum.Text.getCode());
            bo.setCommandType(null);
        }

        // 发送者消息体
        ImChatResponseVo responseVoForSender = ImSocketResponseUtil.buildResponseVo(bo, sender);

        // 是否是商家给用户发消息
        boolean merchantToUser = StrUtil.equalsAnyIgnoreCase(String.valueOf(senderId), String.valueOf(userId));

        ImChatResponseVo.SenderDTO senderDTO = new ImChatResponseVo.SenderDTO();
        ImChatResponseVo.ReceiverDTO receiver = new ImChatResponseVo.ReceiverDTO();
        if (merchantToUser) {
            senderDTO = responseVoForSender.getSender();
            if (Objects.isNull(senderDTO)) {
                senderDTO = new ImChatResponseVo.SenderDTO();
            }
            senderDTO.setName(merchantModel.getName());
            senderDTO.setAvatar(merchantModel.getAvatar());
        } else {
            receiver = responseVoForSender.getReceiver();
            if (Objects.isNull(receiver)) {
                receiver = new ImChatResponseVo.ReceiverDTO();
            }
            receiver.setName(merchantModel.getName());
            receiver.setAvatar(merchantModel.getAvatar());
        }

        if (senderDTO.getId() == null || senderDTO.getId() <= 0L) {
            senderDTO = responseVoForSender.getSender();
        }

        if (receiver.getId() == null || receiver.getId() <= 0L) {
            receiver = responseVoForSender.getReceiver();
        }


        // 获取好友备注
        Map<Long, String> friendAliasMap = remoteMiddleFriendService.friendAliasMap(receiverId, Arrays.asList(sender.getUserId()), ApplyStateEnum.pass.name());
        // 获取接收方对发送方的备注
        String alias = friendAliasMap.get(sender.getUserId());
        if (StrUtil.isNotBlank(alias)) {
            senderDTO.setName(alias);
        }

        ImChatResponseVo.Merchant merchant = new ImChatResponseVo.Merchant();
        merchant.setId(merchantModel.getId());
        merchant.setUserId(merchantModel.getUserId());
        merchant.setName(merchantModel.getName());
        merchant.setAvatar(merchantModel.getAvatar());
        // 发送方是否是商户
        merchant.setSenderIsMerchant(Objects.equals(senderId, userId));

        responseVoForSender.setSender(senderDTO);
        responseVoForSender.setReceiver(receiver);
        responseVoForSender.setMerchant(merchant);

        // 接收者消息体
        ImChatResponseVo responseVoForReceiver = new ImChatResponseVo();
        BeanUtil.copyProperties(responseVoForSender, responseVoForReceiver);
        ImChatResponseVo.Merchant receiverMerchant = responseVoForReceiver.getMerchant();
        receiverMerchant.setSenderIsMerchant(!receiverMerchant.getSenderIsMerchant());
        responseVoForReceiver.setMerchant(receiverMerchant);

        WsResponse wsResponseForSender = WsResponse.fromText(JSONUtil.toJsonStr(responseVoForSender), ImServerConfig.CHARSET);
        WsResponse wsResponseForReceiver = WsResponse.fromText(JSONUtil.toJsonStr(responseVoForReceiver), ImServerConfig.CHARSET);


        // 给发送方发送socket消息
        if (Objects.equals(senderId, receiverId)) {
            // 判断是否给自己发
            Tio.sendToUser(channelContext.tioConfig, String.valueOf(senderId), wsResponseForSender);
        } else {
            // 给接收方发送socket消息
            Tio.sendToUser(channelContext.tioConfig, String.valueOf(receiverId), wsResponseForReceiver);
            // 给发送方发送socket消息
            Tio.sendToUser(channelContext.tioConfig, String.valueOf(senderId), wsResponseForSender);

            // 保存发送方消息到数据库
            ImChatHandleEnum.SINGLE.sendMessage(senderId, bo, responseVoForSender.getChatId());

            // 保存接收方消息到数据库，更新接收方外层聊天记录
            bo.setReceiverId(responseVoForSender.getSender().getId());
            bo.setSaveInnerMessage(false);
            ImChatHandleEnum.SINGLE.sendMessage(receiverId, bo, (bo.getChatId() != null && bo.getChatId() > 0L) ? bo.getChatId() : responseVoForReceiver.getChatId());

            // 更新接收方的外层商家IM通知消息
            updateMerchantNotice(merchantToUser, sender, receiverId, merchantModel, bo);
        }
    }

    @Async("asyncExecutor")
    public void updateMerchantNotice(boolean merchantToUser, LoginUser sender, Long receiverId, SohuMerchantModel merchantModel, ImChatRequestBo bo) {
        // 获取好友备注
        //Map<Long, String> friendAliasMap = remoteMiddleFriendService.friendAliasMap(receiverId, Arrays.asList(sender.getUserId()), ApplyStateEnum.pass.name());
        String userName = StrUtil.isBlankIfStr(sender.getNickname()) ? sender.getNickname() : sender.getUsername();
        //String name = merchantToUser ? merchantModel.getName() : (StrUtil.isBlankIfStr(friendAliasMap.get(sender.getUserId())) ? userName : friendAliasMap.get(sender.getUserId()));
        String name = merchantModel.getName();
        SohuOuterNoticeBo noticeBo = SohuOuterNoticeBo.builder().userId(receiverId).type(NoticeOuterEnum.OuterEnum.outerMerchant.name()).
                title(String.format("%s：%s", name, bo.getContent())).build();
        noticeBo.setLoginId(receiverId);
        // 更新外层消息
        remoteMiddleOuterNoticeService.updateLastContent(noticeBo);

        SohuOuterNoticeBo noticeBo1 = SohuOuterNoticeBo.builder().userId(sender.getUserId()).type(NoticeOuterEnum.OuterEnum.outerMerchant.name()).
                title(String.format("%s：%s", name, bo.getContent())).build();
        noticeBo1.setLoginId(sender.getUserId());
        // 更新外层消息
        remoteMiddleOuterNoticeService.updateLastContent(noticeBo1);
    }

    @Override
    public void sendMessage(Long senderId, ImChatRequestBo bo, Long chatId) {
        ImChatHandleEnum.MERCHANT.sendMessage(senderId, bo, chatId);
    }


}
