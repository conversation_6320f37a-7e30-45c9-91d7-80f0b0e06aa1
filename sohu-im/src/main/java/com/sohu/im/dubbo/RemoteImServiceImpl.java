package com.sohu.im.dubbo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.im.api.bo.*;
import com.sohu.im.api.enums.ImMessageTypeEnum;
import com.sohu.im.api.enums.ImSessionTypeEnum;
import com.sohu.im.api.service.RemoteImService;
import com.sohu.im.api.vo.SohuImGroupUserVo;
import com.sohu.im.api.vo.SohuImGroupVo;
import com.sohu.im.domain.SohuImChatLastMessage;
import com.sohu.im.domain.SohuImChatMessage;
import com.sohu.im.domain.SohuImGroup;
import com.sohu.im.domain.SohuImGroupUser;
import com.sohu.im.mapper.SohuImChatLastMessageMapper;
import com.sohu.im.mapper.SohuImChatMessageMapper;
import com.sohu.im.mapper.SohuImGroupMapper;
import com.sohu.im.mapper.SohuImGroupUserMapper;
import com.sohu.im.service.ISohuImChatMessageService;
import com.sohu.im.service.ISohuImGroupService;
import com.sohu.im.service.ISohuImSendService;
import com.sohu.resource.api.RemoteFileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteImServiceImpl implements RemoteImService {


    private final SohuImChatLastMessageMapper sohuImChatLastMessageMapper;
    private final SohuImChatMessageMapper sohuImChatMessageMapper;

    private final ISohuImGroupService sohuImGroupService;

    private final SohuImGroupUserMapper sohuImGroupUserMapper;
    private final ISohuImSendService sohuImSendService;
    private final SohuImGroupMapper sohuImGroupMapper;
    private final ISohuImChatMessageService sohuImChatMessageService;

    @DubboReference
    private RemoteFileService remoteFileService;


    @Override
    public void deleteHistory(Long userId, Long friendId) {
        sohuImChatMessageMapper.deleteHistory(ImSessionTypeEnum.single.getCode(), userId, friendId);
        sohuImChatMessageMapper.deleteHistory(ImSessionTypeEnum.single.getCode(), friendId, userId);
        sohuImChatLastMessageMapper.deleteHistory(ImSessionTypeEnum.single.getCode(), userId, friendId);
        sohuImChatLastMessageMapper.deleteHistory(ImSessionTypeEnum.single.getCode(), friendId, userId);
    }

    @Override
    public List<Long> getGroupUserIds(Long groupId) {
        List<SohuImGroupUser> groupUsers = sohuImGroupUserMapper.selectList(SohuImGroupUser::getGroupId, groupId);
        if (CollUtil.isEmpty(groupUsers)) {
            return null;
        }
        return groupUsers.stream().map(SohuImGroupUser::getUserId).collect(Collectors.toList());
    }

    @Override
    public List<SohuImGroupUserVo> groupUsers(Long groupId) {
        LambdaQueryWrapper<SohuImGroupUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SohuImGroupUser::getGroupId, groupId);
        return sohuImGroupUserMapper.selectVoList(queryWrapper);
    }

    @Override
    public void sendMessage(Long senderId, ImChatRequestBo bo) {
        sohuImSendService.sendMessage(senderId, bo);
    }

    @Override
    public void updateGroupCount(boolean firstUpdate) {
        List<SohuImGroup> sohuImGroups;

        if (firstUpdate) {
            sohuImGroups = sohuImGroupMapper.selectList();
        } else {
            LambdaQueryWrapper<SohuImChatLastMessage> lqw = new LambdaQueryWrapper<>();
            lqw.eq(SohuImChatLastMessage::getSessionType, ImSessionTypeEnum.group.getCode());
            lqw.ge(SohuEntity::getUpdateTime, DateUtil.offsetDay(new Date(), -1));
            List<SohuImChatLastMessage> lastMessageList = sohuImChatLastMessageMapper.selectList(lqw);
            if (CollUtil.isEmpty(lastMessageList)) {
                return;
            }
            List<Long> groupIds = lastMessageList.stream().map(SohuImChatLastMessage::getReceiverId).collect(Collectors.toList());
            sohuImGroups = sohuImGroupMapper.selectBatchIds(groupIds);
        }

        if (CollUtil.isEmpty(sohuImGroups)) {
            return;
        }

        sohuImGroups.forEach(sohuImGroup -> {
            Long count = sohuImGroupUserMapper.selectCount(SohuImGroupUser::getGroupId, sohuImGroup.getId());
            if (count != null && count > 0 && count.intValue() == sohuImGroup.getGroupUserNum()) {
                sohuImGroup.setGroupUserNum(count.intValue());
                sohuImGroupMapper.updateById(sohuImGroup);
                log.info("群ID：{}，更新群人数成功，原人数{}，现群人数更新为:{}", sohuImGroup.getId(), sohuImGroup.getGroupUserNum(), count);
            }
        });
    }

    @Override
    public int togetherGroupCount(Long loginId, Long userId) {
        return sohuImGroupUserMapper.togetherGroupCount(loginId, userId);
    }

    @Override
    public List<SohuImGroupVo> togetherGroupList(Long loginId, Long userId) {
        return sohuImGroupUserMapper.togetherGroupList(loginId, userId);
    }

    @Override
    public Boolean createGroup(SohuImGroupCreateBo bo) {
        return sohuImGroupService.createGroup(bo);
    }

    @Override
    public Boolean deleteGroup(Long groupId, Boolean canDismiss) {
        return sohuImGroupService.dismiss(groupId, canDismiss);
    }

    @Override
    public Boolean deleteGroupTask(Long publishUserId, String childTaskNumber) {
        return sohuImGroupService.deleteGroupTask(publishUserId, childTaskNumber);
    }

    @Override
    public Boolean deleteGroupTaskNoLogin(Long publishUserId, String childTaskNumber) {
        return sohuImGroupService.deleteGroupTaskNoLogin(publishUserId, childTaskNumber);
    }

    @Override
    public List<SohuImGroupVo> recommGroupList() {
        return sohuImGroupService.recommGroupList();
    }

    @Override
    public Boolean imFileOverTimeHandler() {
        return sohuImChatMessageService.imFileOverTimeHandler();
    }

    @Override
    public Boolean reportGroup(SohuImGroupReportBo bo) {
        log.info("reportGroup request:{}", JSONUtil.toJsonStr(bo));
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateGroup(SohuImGroupBo bo) {
        return sohuImGroupService.updateByBo(bo);
    }

    @Override
    public String getGroupWord(Long groupId) {
        return sohuImGroupService.getGroupWord(groupId);
    }

    @Override
    public Boolean groupDisable(SohuImGroupDisableBo bo) {
        return sohuImGroupService.groupDisable(bo);
    }

    @Override
    public Boolean groupEnable(Long groupId) {
        return sohuImGroupService.groupEnable(groupId);
    }

    @Override
    public TableDataInfo<SohuImGroupVo> groupPage(SohuImPageQueryBo bo) {
        return sohuImGroupService.groupPage(bo);
    }

    @Override
    public SohuImGroupVo queryById(Long groupId) {
        return sohuImGroupService.queryById(groupId);
    }

    @Override
    public List<SohuImGroupVo> queryMyImGroup(SohuMyImGroupQueryBo bo) {
        return sohuImGroupService.queryMyImGroup(bo);
    }

    @Override
    public SohuImGroupVo queryByTaskNumber(String groupType, String taskNumber) {
        return sohuImGroupService.queryByTaskNumber(groupType, taskNumber);
    }

    @Override
    public Boolean clearChatDataCache(Date beforeTime) {
        LambdaQueryWrapper<SohuImChatMessage> lqw = new LambdaQueryWrapper<>();
        lqw.le(SohuEntity::getCreateTime, beforeTime);
        List<SohuImChatMessage> overTimeList = this.sohuImChatMessageMapper.selectList(lqw);
        if (CollUtil.isEmpty(overTimeList)) {
            return false;
        }
        log.info("clearChatDataCache 共查到数据：{} 条", overTimeList.size());
        //先清理文件
        List<String> urlList = overTimeList.stream().filter(r -> Objects.nonNull(r)
                && Objects.equals(r.getMessageType(), ImMessageTypeEnum.File.getCode())
                && StrUtil.isNotBlank(r.getContent())).map(SohuImChatMessage::getContent).collect(Collectors.toList());
        log.info("clearChatDataCache 待删除文件：{} 个", urlList.size());
        // 分批处理，每批次最多 100 个
        int batchSize = 100;
        int totalSize = urlList.size();
        for (int i = 0; i < totalSize; i += batchSize) {
            // 获取当前批次
            List<String> batch = urlList.subList(i, Math.min(i + batchSize, totalSize));
            log.info("clearChatDataCache 删除第 {} 批次，共 {} 个文件", (i / batchSize) + 1, batch.size());
            remoteFileService.ossDelete(batch);
        }
        log.info("clearChatDataCache IM上传文件超时处理完成");
        List<Long> ids = overTimeList.stream().map(SohuImChatMessage::getId).collect(Collectors.toList());
        this.sohuImChatMessageMapper.deleteBatchIds(ids);
        log.info("clearChatDataCache IM聊天超时处理完成");
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateImRisk(String busyType, String busyCode, boolean checkPass) {
        return sohuImChatMessageService.updateImRisk(busyType, busyCode, checkPass);
    }
}
