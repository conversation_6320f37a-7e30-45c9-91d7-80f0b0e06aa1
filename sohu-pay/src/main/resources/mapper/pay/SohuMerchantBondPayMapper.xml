<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.pay.mapper.SohuMerchantBondPayMapper">

    <resultMap type="com.sohu.pay.domain.SohuMerchantBondPay" id="SohuMerchantBondPayResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="merchantBondId" column="merchant_bond_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="transactionId" column="transaction_id"/>
        <result property="payNumber" column="pay_number"/>
        <result property="payType" column="pay_type"/>
        <result property="payStatus" column="pay_status"/>
        <result property="payAmount" column="pay_amount"/>
        <result property="payTime" column="pay_time"/>
        <result property="voucherUrl" column="voucher_url"/>
        <result property="chargeAmount" column="charge_amount"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectAuditList" resultType="com.sohu.pay.api.vo.SohuMerchantBondAuditListVo">
        SELECT
            mbp.id,
            mbp.user_id AS userId,
            mbp.pay_amount AS amount,
            mbp.pay_type AS payType,
            mbp.pay_status AS payStatus,
            mbp.pay_time AS payTime,
            m.real_name AS userName,
            m.phone AS phone,
            m.name as merchantName,
            m.merchant_type AS merchantType,
            mb.cate_id AS categoryId
        FROM
            sohu_merchant_bond_pay mbp,
            sohu_merchant_bond mb,
            sohu_merchant m
            <if test="bo.areaCode != null and bo.areaCode != ''">
                INNER JOIN sohu_account sa ON m.user_id = sa.user_id AND sa.license_area_code = #{bo.areaCode}
            </if>
        WHERE
            mbp.merchant_bond_id = mb.id
        AND
            mb.mer_id = m.id
        <if test="bo.merchantName != null and bo.merchantName != ''" >
        AND m.name Like concat('%', #{bo.merchantName}, '%')
        </if>
        <if test="bo.merchantType != null and bo.merchantType != ''" >
        AND m.merchant_type = #{bo.merchantType}
        </if>
        <if test="bo.payStatus != null and bo.payStatus != ''" >
        AND mbp.pay_status = #{bo.payStatus}
        </if>
        ORDER BY mbp.id DESC
    </select>


</mapper>
