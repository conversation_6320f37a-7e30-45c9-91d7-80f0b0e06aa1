<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.pay.mapper.SohuMerchantBondRefundMapper">

    <resultMap type="com.sohu.pay.domain.SohuMerchantBondRefund" id="SohuMerchantBondRefundResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="merchantBondId" column="merchant_bond_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="refundOrderNo" column="refund_order_no"/>
        <result property="transactionId" column="transaction_id"/>
        <result property="payNumber" column="pay_number"/>
        <result property="type" column="type"/>
        <result property="refundType" column="refund_type"/>
        <result property="refundStatus" column="refund_status"/>
        <result property="payableAmount" column="payable_amount"/>
        <result property="refundAmount" column="refund_amount"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectAuditList" resultType="com.sohu.pay.api.vo.SohuMerchantBondRefundProcessListVo">
        SELECT
        mbr.id,
        mbr.user_id AS userId,
        mbr.payable_amount AS payableAmount,
        mbr.type,
        mbr.refund_type AS refundType,
        mbr.refund_status AS refundStatus,
        mbr.refund_time AS refundTime,
        m.real_name AS userName,
        m.phone AS phone,
        m.name as merchantName,
        m.merchant_type AS merchantType,
        mb.cate_id AS categoryId
        FROM
        sohu_merchant_bond_refund mbr,
        sohu_merchant_bond mb,
        sohu_merchant m
        <if test="bo.areaCode != null and bo.areaCode != ''">
            INNER JOIN sohu_account sa ON m.user_id = sa.user_id AND sa.license_area_code = #{bo.areaCode}
        </if>
        WHERE
        mbr.merchant_bond_id = mb.id
        AND
        mb.mer_id = m.id
        <if test="bo.merchantName != null and bo.merchantName != ''" >
            AND m.name Like concat('%', #{bo.merchantName}, '%')
        </if>
        <if test="bo.merchantType != null and bo.merchantType != ''" >
            AND m.merchant_type = #{bo.merchantType}
        </if>
        <if test="bo.refundStatus != null and bo.refundStatus != ''" >
            AND mbr.refund_status = #{bo.refundStatus}
        </if>
        <if test="bo.type != null and bo.type != ''" >
            AND mbr.type = #{type}
        </if>
        <if test="bo.refundType != null and bo.refundType != ''" >
            AND mbr.refund_type = #{refundType}
        </if>
        ORDER BY mbr.id DESC
    </select>

</mapper>
