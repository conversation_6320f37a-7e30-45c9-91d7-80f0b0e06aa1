package com.sohu.pay.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: leibo
 * @Date: 2025/5/10 14:42
 **/
@Data
public class SohuMerchantBondRefundProcessInfoVo implements Serializable {

    /**
     * 记录id
     */
    private Long id;
    /**
     * 缴纳金额
     */
    private BigDecimal bondAmount;
    /**
     * 扣款金额
     */
    private BigDecimal deductAmount;
    /**
     * 扣款原因
     */
    private String deductReason;
    /**
     * 待退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 已退款金额
     */
    private BigDecimal refundedAmount;
    /**
     * 支付记录
     */
    private List<payRecordVo> recordList;

    /**
     * 支付记录
     */
    @Data
    public static class payRecordVo {
        /**
         * 支付记录id
         */
        private Long payId;
        /**
         * 事项备注
         */
        private String remark;
        /**
         * 付款方式
         */
        private String payType;
        /**
         * 实付金额
         */
        private BigDecimal payAmount;
        /**
         * 支付时间（缴纳时间）
         */
        private Date payTime;
        /**
         * 退款状态
         */
        private String refundStatus;
        /**
         * 退款凭证
         */
        private String refundVoucherUrl;
        /**
         * 原因
         */
        private String reason;

    }




}
