package com.sohu.pay.service.strategy.yima;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Lists;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.constant.UserConstants;
import com.sohu.common.core.enums.PayStatus;
import com.sohu.common.core.enums.PayTypeEnum;
import com.sohu.common.core.enums.ShopNoticeEnum;
import com.sohu.common.core.enums.SohuTradeRecordEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.BigDecimalUtils;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.im.api.noticeContent.NoticeShopBuyContent;
import com.sohu.im.api.noticeContent.NoticeShopBuyContentDetail;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.service.notice.RemoteMiddleShopNoticeService;
import com.sohu.middle.api.vo.YiMaPayConfig;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.sohu.pay.api.vo.SohuIndependentOrderVo;
import com.sohu.pay.service.ISohuIndependentOrderService;
import com.sohu.pay.service.strategy.AbsTradeRecordStrategy;
import com.sohu.pay.service.strategy.PaymentStrategy;
import com.sohu.shopgoods.api.RemoteProductAttrValueService;
import com.sohu.shopgoods.api.RemoteProductService;
import com.sohu.shopgoods.api.model.SohuProductAttrValueModel;
import com.sohu.shopgoods.api.model.SohuProductModel;
import com.sohu.shoporder.api.*;
import com.sohu.shoporder.api.domain.SohuSendOrderReqBo;
import com.sohu.shoporder.api.model.SohuShopMasterOrderModel;
import com.sohu.shoporder.api.model.SohuShopOrderModel;
import com.sohu.shoporder.api.model.SohuShopRefundOrderModel;
import com.sohu.shoporder.api.vo.SohuShopOrderInfoVo;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.wangcaio2o.ipossa.sdk.model.ExtendParams;
import com.wangcaio2o.ipossa.sdk.model.GoodsDetail;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverse;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverseRequest;
import com.wangcaio2o.ipossa.sdk.request.scanpay.Scanpay;
import com.wangcaio2o.ipossa.sdk.request.scanpay.ScanpayRequest;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.Unifiedorder;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.UnifiedorderRequest;
import com.wangcaio2o.ipossa.sdk.response.barcodereverse.BarcodeReverseResponse;
import com.wangcaio2o.ipossa.sdk.response.callback.CallbackRequest;
import com.wangcaio2o.ipossa.sdk.response.scanpay.ScanpayResponse;
import com.wangcaio2o.ipossa.sdk.response.unifiedorder.UnifiedorderResponse;
import com.wangcaio2o.ipossa.sdk.test.Client;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品支付
 */
@Slf4j
@Component
public class YiMaShopOrderPayStrategy extends AbsTradeRecordStrategy implements YiMaPayStrategy {

    @DubboReference
    private RemoteMasterOrderService remoteMasterOrderService;
    @DubboReference
    private RemoteShopRefundOrderService remoteShopRefundOrderService;
    @DubboReference
    private RemoteMerchantService remoteMerchantService;
    @DubboReference
    private RemoteShopOrderService remoteShopOrderService;
    @DubboReference
    private RemoteMiddleShopNoticeService remoteMiddleShopNoticeService;
    @Resource
    private ISohuIndependentOrderService sohuIndependentOrderService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    @DubboReference
    private RemoteProductAttrValueService productAttrValueService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @DubboReference
    private RemoteShopOrderStatusService remoteShopOrderStatusService;
    @DubboReference
    private RemoteShopRefundOrderStatusService remoteShopRefundOrderStatusService;
    @DubboReference
    private RemoteShopOrderInfoService remoteShopOrderInfoService;
    @DubboReference
    private RemoteProductService remoteProductService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payment(SohuPrePayBo payBo) {
        log.info("翼码支付 - 商城商品支付 - 支付渠道：{}，-支付来源：{}", payBo.getPayChannel(), JSONUtil.toJsonStr(payBo));
        return getPay(payBo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean refund(SohuRefundPayBo refundPayBo) {
        log.info("翼码支付 - 商城商品退款 - 退款来源：{}", JSONUtil.toJsonStr(refundPayBo));
        SohuShopRefundOrderModel refundOrder = remoteShopRefundOrderService.getByRefundOrderNo(refundPayBo.getMasterOrderNo());
        if (refundPayBo.getUserId() != null && refundPayBo.getUserId() > 0L) {
            List<SohuMerchantModel> sohuMerchantModels = remoteMerchantService.selectByUserId(refundPayBo.getUserId());
            List<Long> merIds = sohuMerchantModels.stream().map(SohuMerchantModel::getId).collect(Collectors.toList());
            if (!merIds.contains(refundOrder.getMerId())) {
                throw new ServiceException("无法操作非自己商户的订单");
            }
        }
        SohuShopOrderModel storeOrder = remoteShopOrderService.getByOrderNo(refundOrder.getShopOrderNo());
        if (!storeOrder.getPaid()) {
            throw new ServiceException("未支付无法退款");
        }
        // 查询翼码支付配置
        YiMaPayConfig yiMaPayConfig = getPayConfig();
        // 组装微信小程序退款请求参数
        BarcodeReverseRequest request = getBarcodeReverseRequest();
        // 退款参数封装
        BarcodeReverse reverse = new BarcodeReverse();
        reverse.setPayType(yiMaPayConfig.getPayType());
        List<GoodsDetail> goodsDetailList = null;
        // 存在退单的则将商品信息传入
        if (StringUtils.isNotBlank(refundOrder.getMasterOrderNo())) {
            goodsDetailList = this.exchangeRefundGoodsDetailList(refundOrder.getShopOrderNo());
        }
        // 查询主订单信息
        SohuShopMasterOrderModel masterOrder = remoteMasterOrderService.queryMasterOrderByMasterOrderNo(refundOrder.getMasterOrderNo());
        request.setPosSeq(refundOrder.getRefundOrderNo());
        // 支付请求流水号
        reverse.setOrgPosSeq(masterOrder.getOrderNo());
        // 退款商品信息
        reverse.setGoodsDetail(goodsDetailList);
        // 所有退款单
        List<SohuIndependentOrderVo> sohuIndependentOrderVos = sohuIndependentOrderService.queryListByOrderNo(storeOrder.getOrderNo());
        // 退款金额
        if (StrUtil.equalsAnyIgnoreCase(storeOrder.getStatus(), OrderConstants.ORDER_STATUS_OVER) &&
                CollUtil.isNotEmpty(sohuIndependentOrderVos)) {
            if (null != refundPayBo.getIsDelayIndependent()) {
                reverse.setTxAmt(BigDecimalUtils.yuanToFen(refundPayBo.getIsDelayIndependent()));
            } else {
                // 总金额减去手续费
                reverse.setTxAmt(BigDecimalUtils.yuanToFen(storeOrder.getPayPrice()));
            }
        } else {
            // 直接退总金额
            reverse.setTxAmt(BigDecimalUtils.yuanToFen(storeOrder.getPayPrice()));
        }
        request.setBarcodeReverseRequest(reverse);
        // todo 平台分销退款，是否有分销人、拉新人。售后效期
        // 微信小程序发起退款接口
        BarcodeReverseResponse response = Client.getClient().execute(request);
        // 判断微信退款状态
        List<String> resultList = Lists.newArrayList("9998", "0000");
        if (ObjectUtils.isNull(response) || !resultList.contains(response.getResult().getId())) {
            log.error("微信退款异常");
            throw new RuntimeException(response.getResult().getComment());
        }
        // 修改订单退款状态
        storeOrder.setRefundStatus(OrderConstants.ORDER_REFUND_STATUS_REFUNDING);
        refundOrder.setRefundStatus(OrderConstants.MERCHANT_REFUND_ORDER_STATUS_REFUNDING);
        refundOrder.setRefundTime(new Date());
        Boolean execute = transactionTemplate.execute(e -> {
            remoteShopRefundOrderService.updateById(refundOrder);
            remoteShopOrderService.updateById(storeOrder);
            // 新增日志
            remoteShopOrderStatusService.saveRefund(storeOrder.getOrderNo(), refundOrder.getRefundPrice(), "退款中");
            remoteShopRefundOrderStatusService.createLog(refundOrder.getRefundOrderNo(), OrderConstants.REFUND_ORDER_LOG_TYPE_APPLY,
                    OrderConstants.ORDER_LOG_MESSAGE_REFUND_PRICE.replace("{amount}", refundOrder.getRefundPrice().toString()));
            return Boolean.TRUE;
        });
        if (Boolean.FALSE.equals(execute)) {
            remoteShopOrderStatusService.saveRefund(storeOrder.getOrderNo(), storeOrder.getPayPrice(), "失败");
            throw new ServiceException("订单更新失败");
        }
        // 2s后延迟 消息队列去做退款回调的事情-主动查询退款结果、订单状态、商品回滚
        MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(refundOrder.getRefundOrderNo()), MqKeyEnum.YI_MA_REFUND_PAY.getKey());
        remoteStreamMqService.sendDelayMsg(mqMessaging, 2L);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payCallback(String callbackResponse) {
        log.info("翼码支付 - 商城商品支付回调 ：{}", callbackResponse);
        CallbackRequest response = JSONObject.parseObject(callbackResponse, CallbackRequest.class);
        String outTradeNo = response.getPosSeq();
        // 校验支付状态
        checkPayStatus(response.getStatus());
        SohuShopMasterOrderModel masterOrder = remoteMasterOrderService.queryMasterOrderByMasterOrderNo(outTradeNo);
        masterOrder.setOutTradeNo(outTradeNo).setTransactionId(response.getTradeNo()).setPaid(true)
                .setChargePrice(CalUtils.centToYuan(new BigDecimal(response.getChargeAmount())));
        List<SohuShopOrderModel> storeOrderList = remoteShopOrderService.getListByMasterNo(masterOrder.getOrderNo());
        log.warn("storeOrderList pay now 计算每个子单的手续费之前: {}", JSONObject.toJSONString(storeOrderList));
        // 计算每个子单的手续费
        distributeFee(storeOrderList, masterOrder.getPayPrice(), masterOrder.getChargePrice());
        log.warn("storeOrderList pay update 计算每个子单的手续费: {}", JSONObject.toJSONString(storeOrderList));
        // 添加支付成功修改订单状态  --弃用redis队列
        Boolean execute = transactionTemplate.execute(e -> {
            for (SohuShopOrderModel storeOrder : storeOrderList) {
                storeOrder.setPaid(true);
                storeOrder.setPayTime(new Date());
                storeOrder.setPayType(masterOrder.getPayType());
                storeOrder.setStatus(OrderConstants.ORDER_STATUS_SHIPPING);
            }
            remoteShopOrderService.updateBatchById(storeOrderList);
            remoteMasterOrderService.updateById(masterOrder);
            return Boolean.TRUE;
        });
        if (Boolean.FALSE.equals(execute)) {
            log.error("商城商品支付回调 订单更新失败==》" + masterOrder.getOutTradeNo());
            throw new ServiceException("订单回调修改数据失败");
        }
        // 更新流水记录成功
        remoteMiddleTradeRecordService.updatePayStatus(outTradeNo, response.getTradeNo(), PayStatus.Paid, null);
        // 更新支付单
        updatePayOrder(outTradeNo, masterOrder.getChargePrice(), PayStatus.Paid.name(), response.getTradeNo());
        //订单检测
        MqMessaging mqCancelMessaging = new MqMessaging(outTradeNo, MqKeyEnum.SHOP_ORDER_CHECK.getKey());
        remoteStreamMqService.sendDelayMsg(mqCancelMessaging, 2L);
        return "success";
    }

    /**
     * 支付
     *
     * @param payBo 预支付请求对象
     * @return {@link String}
     */
    @Transactional(rollbackFor = Exception.class)
    public String getPay(SohuPrePayBo payBo) {
        String masterOrderNo = payBo.getMasterOrderNo();
        // 通过缓存获取预下单对象
        String key = CacheConstants.ORDER_PAY_TWO + PayTypeEnum.PAY_TYPE_YI_MA.getStatus() + StrPool.COLON + masterOrderNo;
        boolean exists = RedisUtils.isExistsObject(key);
        // 如果存在直接唤醒
        if (exists) {
            log.info("商品订单存在，直接唤醒支付，订单号：{}，缓存值：{}", masterOrderNo, RedisUtils.getCacheObject(key));
            return RedisUtils.getCacheObject(key);
        }
        SohuShopMasterOrderModel masterOrder = remoteMasterOrderService.queryMasterOrderByMasterOrderNo(masterOrderNo);
        // 状态判断
        if (ObjectUtil.isNull(masterOrder)) {
            throw new ServiceException("order does not exist");
        }
        if (masterOrder.getIsCancel()) {
            throw new ServiceException("order is cancelled");
        }
        if (masterOrder.getPaid()) {
            throw new ServiceException("order not paid");
        }
        // 剩余组装信息
        Long merId = remoteShopOrderService.getListByMasterNo(masterOrderNo).get(0).getMerId();
        SohuMerchantModel merchantModel = remoteMerchantService.selectById(merId);
        StringBuilder body = new StringBuilder();
        body.append(merchantModel.getName());
        if (StringUtils.isBlank(body)) {
            body.append("用户下单");
        }
        ScanpayRequest scanpayRequest = this.getScanpayRequest();
        UnifiedorderRequest unifiedorderRequest = getUnifiedorderRequest();
        // 备注
        scanpayRequest.setMemo(body.toString());
        unifiedorderRequest.setMemo(body.toString());
        // 唯一订单号
        scanpayRequest.setPosSeq(masterOrder.getOrderNo());
        unifiedorderRequest.setPosSeq(masterOrder.getOrderNo());
        Scanpay scanpay = this.getScanpay();
        Unifiedorder unifiedorder = getUnifiedorder(payBo.getUserId());
        // 总金额
        scanpay.setTxAmt(BigDecimalUtils.yuanToFen(masterOrder.getPayPrice()));
        unifiedorder.setTxAmt(BigDecimalUtils.yuanToFen(masterOrder.getPayPrice()));
        // 组装商品信息
        scanpay.setGoodsDetail(this.exchangeGoodsDetailList(masterOrder.getOrderNo()));
        unifiedorder.setGoodsDetail(scanpay.getGoodsDetail());
        // 是否分账配置
        ExtendParams extendParams = new ExtendParams();
        // 延时分账
        extendParams.setSplitFlag(ExtendParams.D);
        extendParams.setPlanSplitDate(DateUtils.getDate().replaceAll(StrPool.DASHED, ""));
        unifiedorder.setExtendParams(extendParams);
        scanpay.setExtendParams(extendParams);
        // 设置请求参数
        scanpayRequest.setScanpayRequest(scanpay);
        unifiedorderRequest.setUnifiedorderRequest(unifiedorder);

        payBo.setAmount(masterOrder.getPayPrice());

        SohuTradeRecordBo.SohuTradeRecordBoBuilder recordBo = buildRecord(payBo);
        recordBo.type(SohuTradeRecordEnum.Type.Good.getCode());
        recordBo.consumeType(SohuTradeRecordEnum.Type.Good.getCode());
        recordBo.consumeCode(masterOrderNo);
        recordBo.msg(SohuTradeRecordEnum.Type.Good.getMsg());
        recordBo.amountType(SohuTradeRecordEnum.AmountType.Expend.getCode());
        recordBo.payNumber(masterOrderNo);
        recordBo.accountType(SohuTradeRecordEnum.AccountType.Amount.name());
        // 保存流水记录 - 钱 - 支出
        saveTradeRecord(recordBo.build());

        // 子订单表
        List<SohuShopOrderModel> storeOrderList = remoteShopOrderService.getListByMasterNo(masterOrderNo);
        // 保存主单
        saveMasterPayOrder(payBo, masterOrderNo, PayStatus.WaitPay.name());
        for (SohuShopOrderModel sohuShopOrderModel : storeOrderList) {
            payBo.setAmount(sohuShopOrderModel.getPayPrice());
            // 保存子单
            savePayOrder(payBo, OrderConstants.ORDER_PREFIX_PLATFORM + masterOrderNo, sohuShopOrderModel.getOrderNo(), PayStatus.WaitPay.name());
            //发送消息通知
            sendMsgOfWaitSend(sohuShopOrderModel);
        }
        if (StrUtil.equalsAnyIgnoreCase(payBo.getPayChannel(), Constants.CHANNEL_PC)) {
            log.info("翼码支付 - PC-商城商品支付请求request：{}", JSONUtil.toJsonStr(scanpayRequest));
            ScanpayResponse response = Client.getClient().execute(scanpayRequest);
            log.info("翼码支付 - PC-商城商品支付请求response返回：{}", JSONUtil.toJsonStr(response));
            RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
            return JSONUtil.toJsonStr(response);
        }
        log.info("翼码支付 -MOBILE- 商城商品支付请求request：{}", JSONUtil.toJsonStr(unifiedorderRequest));
        UnifiedorderResponse response = Client.getClient().execute(unifiedorderRequest);
        log.info("翼码支付 -MOBILE- 商城商品支付请求response返回：{}", JSONUtil.toJsonStr(response));
        RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
        return JSONUtil.toJsonStr(response);
    }


    /**
     * 下单成功消息通知
     *
     * @param model
     */
    private void sendMsgOfWaitSend(SohuShopOrderModel model) {
        NoticeShopBuyContent content = new NoticeShopBuyContent();
        content.setTitle(ShopNoticeEnum.waitSendTitle);
        content.setNoticeTime(DateUtils.getTime());
        content.setDetailId(model.getId());
        List<SohuShopOrderInfoVo> shopOrderInfos = remoteShopOrderInfoService.getListByOrderNo(model.getOrderNo());
        SohuShopOrderInfoVo orderInfo = shopOrderInfos.get(0);
        content.setShopId(orderInfo.getProductId());
        content.setOrderNo(model.getMasterOrderNo());
        content.setState(OrderConstants.ORDER_STATUS_SHIPPING);
        NoticeShopBuyContentDetail detail = new NoticeShopBuyContentDetail();
        detail.setDesc(ShopNoticeEnum.waitSendDesc);
        detail.setShopTitle(orderInfo.getProductName());
        detail.setCoverImage(orderInfo.getImage());
        SohuProductAttrValueModel attrValue = productAttrValueService.queryById(orderInfo.getProductAttrValueId());
        detail.setAttrValue(attrValue.getAttrValue());
        detail.setPrice(orderInfo.getPrice());
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleShopNoticeService.sendNotice(UserConstants.ADMIN_ID, model.getUserId(), ShopNoticeEnum.waitSendTitle, contentJson);
    }

    /**
     * 计算每个子单的手续费
     */
    private static void distributeFee(List<SohuShopOrderModel> yourObjects, BigDecimal totalAmount, BigDecimal fee) {
        // 计算每个对象的金额占比
        for (SohuShopOrderModel obj : yourObjects) {
            BigDecimal price = obj.getPayPrice();
            BigDecimal ratio = price.divide(totalAmount, 4, RoundingMode.HALF_UP);
            BigDecimal allocatedFee = ratio.multiply(fee).setScale(2, RoundingMode.HALF_UP);
            obj.setChargePrice(allocatedFee);
            obj.setAdminPrice(CalUtils.sub(obj.getAdminPrice(), allocatedFee));
        }
    }

    /**
     * 根据主订单号查询所有商品详情并组装商品详细数据
     *
     * @param orderNo 主订单号
     * @return WechatPayUnifiedOrderRequest.DiscountDetail
     */
    private List<GoodsDetail> exchangeGoodsDetailList(String orderNo) {
        // 商户商品订单表
        // 微信支付请求需要的商品详情集合
        List<GoodsDetail> goodsDetailList = Lists.newArrayList();
        // 商户商品订单表
        List<SohuShopOrderModel> storeOrderList = remoteShopOrderService.getListByMasterNo(orderNo);
        if (CollUtil.isEmpty(storeOrderList)) {
            return goodsDetailList;
        }
        for (SohuShopOrderModel storeOrder : storeOrderList) {
            // 订单详情表
            List<SohuShopOrderInfoVo> storeOrderInfos = remoteShopOrderInfoService.getListByOrderNo(storeOrder.getOrderNo());
            List<Long> productIds = new ArrayList<>();
            for (SohuShopOrderInfoVo orderInfo : storeOrderInfos) {
                productIds.add(orderInfo.getProductId());
            }
            // 商品详情-当前订单所有购买的商品id
            List<SohuProductModel> storeProductList = remoteProductService.listByIds(productIds);
            if (CollUtil.isEmpty(storeOrderInfos)) {
                return goodsDetailList;
            }
            Map<Long, SohuProductModel> productModelMap = storeProductList.stream().collect(Collectors.toMap(SohuProductModel::getId, v -> v));
            for (SohuShopOrderInfoVo info : storeOrderInfos) {
                SohuProductModel storeProduct = productModelMap.get(info.getProductId());
                // 微信支付请商品详情
                GoodsDetail goodsDetail = new GoodsDetail();
                goodsDetail.setGoodsId(storeProduct.getId().toString());
                goodsDetail.setGoodsName(storeProduct.getStoreName());
                goodsDetail.setPrice(BigDecimalUtils.yuanToFen(storeProduct.getPrice()));
                // 根据商品id与订单详情表商品id匹配，设置商品购买数量
                goodsDetail.setQuantity(info.getPayNum());
                goodsDetail.setBody(storeProduct.getStoreName());
                goodsDetailList.add(goodsDetail);
            }
        }
        return goodsDetailList;
    }

    /**
     * 退款商品信息-YiMa
     *
     * @param merOrderNo
     */
    private List<GoodsDetail> exchangeRefundGoodsDetailList(String merOrderNo) {
        // 商户商品订单表
        List<SohuShopOrderModel> storeOrderList = remoteShopOrderService.getListByMasterNo(merOrderNo);
        // 微信支付请求需要的商品详情集合
        List<GoodsDetail> goodsDetailList = Lists.newArrayList();
        for (SohuShopOrderModel storeOrder : storeOrderList) {
            // 微信支付请商品详情
            GoodsDetail goodsDetail = new GoodsDetail();
            // 订单详情表
            List<SohuShopOrderInfoVo> storeOrderInfos = remoteShopOrderInfoService.getListByOrderNo(storeOrder.getOrderNo());
            // 获取商品数量+id
            Map<Long, Integer> productMap = storeOrderInfos.stream().collect(Collectors.toMap(
                    // 键是 getProductId
                    SohuShopOrderInfoVo::getProductId,
                    // 值是 getPayNum
                    SohuShopOrderInfoVo::getPayNum));
            // 商品详情-当前订单所有购买的商品id
            List<SohuProductModel> storeProductList = remoteProductService.listByIds(new ArrayList<>(productMap.keySet()));
            for (SohuProductModel storeProduct : storeProductList) {
                goodsDetail.setGoodsId(storeProduct.getId().toString());
                goodsDetail.setGoodsName(storeProduct.getStoreName());
                goodsDetail.setPrice(BigDecimalUtils.yuanToFen(storeProduct.getPrice()));
                // 根据商品id与订单详情表商品id匹配，设置商品购买数量
                goodsDetail.setQuantity(productMap.get(storeProduct.getId()));
                goodsDetail.setBody(storeProduct.getStoreName());
                goodsDetailList.add(goodsDetail);
            }
        }
        return goodsDetailList;
    }
}
