package com.sohu.pay.service.impl;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.SystemNoticeEnum;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.im.api.noticeContent.NoticeContentDetail;
import com.sohu.im.api.noticeContent.NoticeSystemContent;
import com.sohu.middle.api.service.notice.RemoteMiddleSystemNoticeService;
import com.sohu.pay.api.bo.SohuAccountBo;
import com.sohu.pay.api.enums.AccountEnum;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.pay.domain.SohuAccount;
import com.sohu.pay.domain.SohuAccountBank;
import com.sohu.pay.mapper.SohuAccountBankMapper;
import com.sohu.pay.mapper.SohuAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/25 10:52
 */
@Slf4j
@Service
public abstract class SohuAccountBaseServiceImpl {

    @Autowired
    protected SohuAccountMapper baseMapper;
    @Autowired
    protected SohuAccountBankMapper sohuAccountBankMapper;
    @DubboReference
    protected RemoteMiddleSystemNoticeService remoteMiddleSystemNoticeService;

    /**
     * 校验证件号码唯一性
     *
     * @param identityNo
     * @param license_code
     */
    protected void validateCertPass(String identityNo, String license_code) {
//        LambdaQueryWrapper<SohuAccount> lqw = new LambdaQueryWrapper();
//        if (StrUtil.isNotEmpty(identityNo)) {
//            lqw.eq(SohuAccount::getIdentityNo, identityNo);
//        }
//        if (StrUtil.isNotEmpty(license_code)) {
//            lqw.eq(SohuAccount::getLicenseCode, license_code);
//        }
//        lqw.eq(SohuAccount::getState, AccountEnum.AccountStatusEnum.Pass.name());
//        Long count = baseMapper.selectCount(lqw);
//        if (StrUtil.isNotEmpty(identityNo) && count > 0) {
//            throw new ServiceException("当前证件号已经被实名，请更换证件号进行实名");
//        }
//
//        if (StrUtil.isNotEmpty(license_code) && count > 0) {
//            throw new ServiceException("当前营业执照已经被实名，请更换营业执照进行实名");
//        }
    }

    /**
     * 构建实名认证信息（编辑）
     *
     * @param bo
     * @param entiey
     */
    protected void buildAccountOfEdit(SohuAccountBo bo, SohuAccount entiey) {
        if (Objects.equals(bo.getAccountType(), AccountEnum.AccountTypeEnum.BUSINESS.getCode())) {
            entiey.setLicenseType(bo.getLicenseType());
            entiey.setQualificationPhoto(bo.getQualificationPhoto());
            entiey.setLicenseCode(bo.getLicenseCode());
            entiey.setLicenseBeginDate(bo.getLicenseBeginDate());
            entiey.setLicenseEndDate(bo.getLicenseEndDate());
            entiey.setLicenseValidityType(bo.getLicenseValidityType());
            entiey.setLicenseAreaCode(bo.getLicenseAreaCode());
            entiey.setLicenseAreaName(bo.getLicenseAreaName());
            entiey.setLicenseProvinceCode(bo.getLicenseProvinceCode());
            entiey.setLicenseProvinceName(bo.getLicenseProvinceName());
            entiey.setLicenseCityCode(bo.getLicenseCityCode());
            entiey.setLicenseCityName(bo.getLicenseCityName());
            entiey.setContactName(bo.getContactName());
            entiey.setContactEmail(bo.getContactEmail());
            entiey.setContactPhone(bo.getContactPhone());
            entiey.setLicenseAddress(bo.getLicenseAddress());
        }
        entiey.setId(bo.getId());
        entiey.setAccountType(bo.getAccountType());
        entiey.setMerchantName(StrUtil.isEmpty(bo.getMerchantName()) ? bo.getLegalName() : bo.getMerchantName());
        entiey.setLegalName(bo.getLegalName());
        entiey.setIdentityType(bo.getIdentityType());
        entiey.setIdentityNo(bo.getIdentityNo());
        entiey.setIdentityBeginDate(bo.getIdentityBeginDate());
        entiey.setIdentityEndDate(bo.getIdentityEndDate());
        entiey.setIdentityValidityType(bo.getIdentityValidityType());
        entiey.setCertPhotoFront(bo.getCertPhotoFront());
        entiey.setCertPhotoBack(bo.getCertPhotoBack());
    }

    /**
     * 构建实名认证信息（已有翼码商户id）
     *
     * @param bo
     * @param vo
     * @param entiey
     */
    protected void buildAccountOfHasMerchantId(SohuAccountBo bo, SohuAccountVo vo, SohuAccount entiey) {
        if (Objects.equals(vo.getAccountType(), AccountEnum.AccountTypeEnum.BUSINESS.getCode())) {
            entiey.setLicenseType(bo.getLicenseType());
            entiey.setQualificationPhoto(bo.getQualificationPhoto());
            entiey.setLicenseCode(bo.getLicenseCode());
            entiey.setLicenseBeginDate(bo.getLicenseBeginDate());
            entiey.setLicenseEndDate(bo.getLicenseEndDate());
            entiey.setLicenseValidityType(bo.getLicenseValidityType());
            entiey.setLicenseAreaCode(bo.getLicenseAreaCode());
            entiey.setLicenseAreaName(bo.getLicenseAreaName());
            entiey.setLicenseProvinceCode(bo.getLicenseProvinceCode());
            entiey.setLicenseProvinceName(bo.getLicenseProvinceName());
            entiey.setLicenseCityCode(bo.getLicenseCityCode());
            entiey.setLicenseCityName(bo.getLicenseCityName());
            entiey.setContactName(bo.getContactName());
            entiey.setContactEmail(bo.getContactEmail());
            entiey.setContactPhone(bo.getContactPhone());
            entiey.setLicenseAddress(bo.getLicenseAddress());
        }
        entiey.setId(bo.getId());
        entiey.setMerchantName(StrUtil.isEmpty(bo.getMerchantName()) ? bo.getLegalName() : bo.getMerchantName());
        entiey.setLegalName(bo.getLegalName());
        entiey.setIdentityType(bo.getIdentityType());
        entiey.setIdentityNo(bo.getIdentityNo());
        entiey.setIdentityBeginDate(bo.getIdentityBeginDate());
        entiey.setIdentityEndDate(bo.getIdentityEndDate());
        entiey.setIdentityValidityType(bo.getIdentityValidityType());
        entiey.setCertPhotoFront(bo.getCertPhotoFront());
        entiey.setCertPhotoBack(bo.getCertPhotoBack());
    }

    /**
     * 构建实名认证信息（已有翼码分账账户）
     *
     * @param bo
     * @param vo
     * @param entiey
     */
    protected void buildAccountOfHasMerchant(SohuAccountBo bo, SohuAccount entiey) {
        if (Objects.equals(bo.getAccountType(), AccountEnum.AccountTypeEnum.BUSINESS.getCode())) {
            entiey.setQualificationPhoto(bo.getQualificationPhoto());
            entiey.setLicenseBeginDate(bo.getLicenseBeginDate());
            entiey.setLicenseEndDate(bo.getLicenseEndDate());
            entiey.setLicenseValidityType(bo.getLicenseValidityType());
            entiey.setLicenseAreaCode(bo.getLicenseAreaCode());
            entiey.setLicenseAreaName(bo.getLicenseAreaName());
            entiey.setLicenseProvinceCode(bo.getLicenseProvinceCode());
            entiey.setLicenseProvinceName(bo.getLicenseProvinceName());
            entiey.setLicenseCityCode(bo.getLicenseCityCode());
            entiey.setLicenseCityName(bo.getLicenseCityName());
            entiey.setContactName(bo.getContactName());
            entiey.setContactEmail(bo.getContactEmail());
            entiey.setContactPhone(bo.getContactPhone());
        }
        entiey.setId(bo.getId());
        entiey.setLegalName(bo.getLegalName());
        entiey.setIdentityNo(bo.getIdentityNo());
        entiey.setIdentityBeginDate(bo.getIdentityBeginDate());
        entiey.setIdentityEndDate(bo.getIdentityEndDate());
        entiey.setIdentityValidityType(bo.getIdentityValidityType());
        entiey.setCertPhotoFront(bo.getCertPhotoFront());
        entiey.setCertPhotoBack(bo.getCertPhotoBack());
    }

    /**
     * 实名认证提交发送消息通知
     *
     * @param id
     * @param userId
     */
    protected void sendMsgOfEntrySubmit(Long id, Long userId) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.personalAuthSubmit);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.personalAuthSubmit.name());
        content.setDetailId(id);
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setId(id);
        detail.setDesc(SystemNoticeEnum.personalAuthSubmitDesc);
        detail.setStatus(CommonState.WaitApprove.getCode());
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.personalAuthSubmit, contentJson, SystemNoticeEnum.Type.personalAuth);
    }

    /**
     * 实名认证审核通过发送消息通知
     *
     * @param userId 接收信息用户id
     */
    protected void sendMsgOfEntryPass(Long userId) {
        SohuAccount account = baseMapper.selectOne(new QueryWrapper<SohuAccount>().eq("user_id", userId));
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.personalAuthPass);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.personalAuthPass.name());
        content.setDetailId(account.getId());
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setId(account.getId());
        detail.setUserId(userId);
        String nickname = DesensitizedUtil.desensitized(account.getLegalName(), DesensitizedUtil.DesensitizedType.CHINESE_NAME);
        detail.setUserName(nickname);
        detail.setIdCard(DesensitizedUtil.idCardNum(account.getIdentityNo(), 2, 2));
        detail.setPhone(StrUtil.desensitized(account.getContactPhone(), DesensitizedUtil.DesensitizedType.MOBILE_PHONE));
        detail.setDesc(SystemNoticeEnum.personalAuthPassDesc);
        detail.setLinkTitle(SystemNoticeEnum.personalAuthPassLinkTitle);
        detail.setLinkUrl(SystemNoticeEnum.personalAuthPassLinkUrl);
        detail.setStatus(CommonState.Pass.getCode());
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.personalAuthPass, contentJson, SystemNoticeEnum.Type.personalAuth);
    }

    /**
     * 实名认证审核通过发送绑定银行卡消息通知
     *
     * @param userId 接收信息用户id
     */
    protected void sendMsgOfBankBind(Long userId) {
        LambdaQueryWrapper<SohuAccountBank> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuAccountBank::getUserId, userId);
        lqw.last("limit 1");
        SohuAccountBank sohuAccountBank = sohuAccountBankMapper.selectOne(lqw);
        if (Objects.nonNull(sohuAccountBank)) {
            return;
        }
        SohuAccount account = baseMapper.selectOne(new QueryWrapper<SohuAccount>().eq("user_id", userId));
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.BIND_BANK_CARD);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.bankBind.name());
        content.setDetailId(account.getId());
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setId(account.getId());
        detail.setUserId(userId);
        detail.setDesc(SystemNoticeEnum.BIND_BANK_CARD_DESC);
//        detail.setLinkUrl(SystemNoticeEnum.personalAuthPassLinkUrl);
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.BIND_BANK_CARD, contentJson, SystemNoticeEnum.Type.personalAuth);
    }

    /**
     * 实名认证审核未通过发送消息通知
     *
     * @param userId 接收信息用户id
     */
    protected void sendMsgOfEntryFail(Long userId, String rejectReason) {
        SohuAccount account = baseMapper.selectOne(new QueryWrapper<SohuAccount>().eq("user_id", userId));
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.personalAuthNotPass);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.personalAuthNotPass.name());
        content.setDetailId(account.getId());
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setId(account.getId());
        detail.setDesc(SystemNoticeEnum.personalAuthNotPassDesc);
        detail.setRejectReason(rejectReason);
        detail.setLinkTitle(SystemNoticeEnum.notPassLinkTitle);
        detail.setStatus(CommonState.Refuse.getCode());
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.personalAuthNotPass, contentJson, SystemNoticeEnum.Type.personalAuth);
    }

    /**
     * 保证金缴纳通过
     */
    protected void sendMsgOfBailPayPass(SohuAccount entity) {
        if (!Objects.equals(AccountEnum.BailStatusEnum.Paid.name(), entity.getBailState())) {
            return;
        }
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.bailPayPassTitle);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.bailPayPass.name());
        content.setDetailId(entity.getId());
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setId(entity.getId());
        detail.setUserId(entity.getUserId());
        detail.setDesc(SystemNoticeEnum.bailPayPassDesc);
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(entity.getUserId(), SystemNoticeEnum.bailPayPassTitle, contentJson, SystemNoticeEnum.Type.bail);
    }

}
