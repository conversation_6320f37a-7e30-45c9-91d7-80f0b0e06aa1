package com.sohu.pay.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sohu.busyorder.api.bo.SohuBusyTaskPayBo;
import com.sohu.busyorder.api.bo.SohuPayBusyBo;
import com.sohu.busyorder.api.bo.SohuRefundBusyBo;
import com.sohu.busyorder.api.enums.BusyTaskTypeEnum;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;
import com.sohu.busyorder.api.model.SohuBusyTaskModel;
import com.sohu.busyorder.api.vo.*;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.utils.BigDecimalUtils;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.NumberUtil;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.service.RemoteImGroupOrderUserService;
import com.sohu.middle.api.bo.SohuMaterialPromotionOrderCmdBo;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.bo.SohuUserBehaviorRecordPointBo;
import com.sohu.pay.api.bo.*;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.pay.api.model.SohuSplitInfoModel;
import com.sohu.pay.api.vo.SohuIndependentOrderVo;
import com.sohu.pay.service.AccountProcessService;
import com.sohu.pay.service.SohuTaskFlowSettlementService;
import com.sohu.shoporder.api.bo.SohuIndependentTempBo;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/4 12:32
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuTaskFlowSettlementServiceImpl extends SohuBaseSettlementServiceImpl implements SohuTaskFlowSettlementService {
    @DubboReference
    private RemoteImGroupOrderUserService remoteImGroupOrderUserService;

    private final AccountProcessService accountProcessService;
    @Resource
    private TransactionTemplate transactionTemplate;

    private final AsyncConfig asyncConfig;

    @Override
    public Boolean settle(SohuPaySettlementBo bo) {
        Long receiveId = bo.getReceiveId();
        String password = bo.getPassword();
        //避免重复提交
        if (!RedisUtils.setObjectIfAbsent(TASK_SETTLE_EXECUTE + receiveId, 1, Duration.ofSeconds(3))) {
            throw new RuntimeException("请稍后再试");
        }
        //校验支付密码
        checkPayPassword(LoginHelper.getUserId(), password);
        return handleSettle(receiveId);
    }

    @Override
    public Boolean settleV2(SohuPaySettlementBo bo) {
        Long receiveId = bo.getReceiveId();
        String password = bo.getPassword();
        //避免重复提交
        if (!RedisUtils.setObjectIfAbsent(TASK_SETTLE_EXECUTE + receiveId, 1, Duration.ofSeconds(3))) {
            throw new RuntimeException("请稍后再试");
        }
        //校验支付密码
        checkPayPasswordV2(LoginHelper.getUserId(), password);
        return handleSettle(receiveId);
    }

    @Override
    public Boolean batchSettle(SohuPayBatchSettlementBo bo) {
        String password = bo.getPassword();
        String taskNumber = bo.getTaskNumber();
        Long userId = LoginHelper.getUserId();
        // 非管理员结算需要校验支付密码
        if (!LoginHelper.isAdmin(userId)) {
            //校验支付密码
            checkPayPassword(LoginHelper.getUserId(), password);
        }
        return handleBatchSettle(taskNumber);
    }

    @Override
    public Boolean batchSettleV2(SohuPayBatchSettlementBo bo) {
        String password = bo.getPassword();
        String taskNumber = bo.getTaskNumber();
        Long userId = LoginHelper.getUserId();
        // 非管理员结算需要校验支付密码
        if (!LoginHelper.isAdmin(userId)) {
            //校验支付密码
            checkPayPasswordV2(LoginHelper.getUserId(), password);
        }
        return handleBatchSettle(taskNumber);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean delayConfirmquery(DelayConfirmqueryBo delayConfirmqueryBo) {
        return this.delayConfirmquery(delayConfirmqueryBo.getBaseSeq(), delayConfirmqueryBo.getPosSeq(), delayConfirmqueryBo.getTaskNumber(), delayConfirmqueryBo.getSuscess(), delayConfirmqueryBo.getBusyType());
    }

    @Override
    public TableDataInfo<SohuPayBusyVo> payList(SohuPayBusyBo bo, PageQuery pageQuery) {
        return remoteBusyTaskService.busyFlowList(bo, pageQuery);
    }

    @Override
    public Boolean refund(SohuRefundBusyBo bo) {
        return true;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean handleBatchSettle(String taskNumber) {
        //避免重复提交
        String lockKey = String.format("sohu:lock:pass_user_master_task_number:%s", taskNumber);
        if (!RedisUtils.setObjectIfAbsent(lockKey, 1, Duration.ofSeconds(10))) {
            throw new RuntimeException("该订单正在结算中，请稍后再试");
        }
        //查询未结算的接单任务
        List<String> validStatus = Arrays.asList(SohuBusyTaskState.WaitSettle.name(), SohuBusyTaskState.Execute.name(), SohuBusyTaskState.WaitApproveSettle.name());
        List<SohuBusyTaskReceiveVo> receiveList = remoteBusyTaskReceiveService.queryListByMasterTaskNumberAndStateList(taskNumber, validStatus);
        //查询主任务
        SohuBusyTaskVo sohuBusyTaskModel = remoteBusyTaskService.getByTaskNo(taskNumber);
        Objects.requireNonNull(sohuBusyTaskModel, "商单主任务不存在");
        //查询支付订单
        List<SohuBusyTaskPayVo> payList = remoteBusyTaskService.queryByTaskNumber(taskNumber, PayStatus.Paid.name());
        if (CollUtil.isEmpty(payList)) {
            throw new RuntimeException("支付订单不存在");
        }
        SohuBusyTaskPayVo sohuBusyTaskPayVo = payList.get(0);
        //接单剩余金额
        BigDecimal receiveBalance = sohuBusyTaskPayVo.getReceiveDistributorAmount();
        //分销剩余金额
        BigDecimal shareBalance = sohuBusyTaskPayVo.getShareDistributorAmount();
        // 流水号
        String posSeq = NumberUtil.getOrderNo(OrderConstants.YI_MA_INDEPENDENT_NO);
        try {
            BigDecimal balance = receiveBalance;
            //子单号集合
            List<String> childTaskNumbers = Lists.newArrayList();
            //封装分账对象信息
            List<SohuSplitInfoModel> splitInfoList = Lists.newArrayList();
            List<SohuSplitInfoModel> platfromSplitInfoList = Lists.newArrayList();
            for (SohuBusyTaskReceiveVo receiveModel : receiveList
            ) {
                SohuBusyTaskSiteVo taskSiteModel = remoteBusyTaskSiteService.queryByTaskNumber(receiveModel.getTaskNumber());
                Long receiveId = receiveModel.getId();
                Long receiveUserId = receiveModel.getUserId();
                Long shareUserId = receiveModel.getSharePerson();
                String childTaskNumber = receiveModel.getTaskNumber();
                childTaskNumbers.add(childTaskNumber);
                //查询达标人数
                Integer reachCount = remoteImGroupOrderUserService.getPassPersonUserByTaskNumber(childTaskNumber);
                //如果达标人数为0，则直接结算
                if (reachCount > Constants.ZERO) {
                    //结算金额
                    BigDecimal settleAmount = sohuBusyTaskModel.getSingleAmount().multiply(BigDecimal.valueOf(reachCount));
                    if (settleAmount.compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    //判断结算金额是否大于分账余额，如果大于分账余额，则结算分账余额且结束本次结算
                    Boolean isDistributor = false;
                    if (receiveBalance.compareTo(settleAmount) <= 0) {
                        settleAmount = receiveBalance;
                        isDistributor = true;
                    }
                    //此次平台手续费
                    BigDecimal chargeFee = CalUtils.multiply(sohuBusyTaskPayVo.getChargeAmount(), CalUtils.divide(settleAmount, sohuBusyTaskPayVo.getPayAmount())).setScale(2, RoundingMode.HALF_UP);
                    //完成百分比
                    BigDecimal rate = CalUtils.divide(BigDecimal.valueOf(reachCount), BigDecimal.valueOf(sohuBusyTaskModel.getDeliveryStandard()));
                    Boolean isShare = false;
                    //判断是否是分销商单
                    if (!sohuBusyTaskModel.getKickbackType().equals("none") && sohuBusyTaskModel.getReceiveNum() == Constants.ONE && !CalUtils.isNullOrZero(shareUserId)) {
                        isShare = true;
                    }
                    //平台分账各对象封装
                    SohuIndependentTemplateModel templateModel = templateService.queryTemplateInfo(receiveModel.getSiteType(), receiveModel.getSiteId(), 2);
                    AccountPlatformBo bo = new AccountPlatformBo();
                    bo.setBusyType(BusyType.BusyTask.getType());
                    bo.setBusyCode(sohuBusyTaskModel.getId());
                    bo.setUserId(receiveUserId);
                    bo.setSiteType(receiveModel.getSiteType());
                    bo.setEntranceSiteId(receiveModel.getSiteId());
                    bo.setCitySiteId(taskSiteModel.getSiteId());
                    bo.setIndustryType(taskSiteModel.getIndustryType());
                    bo.setPayPrice(settleAmount);
                    bo.setChargeFee(chargeFee);
                    bo.setTemplateModel(templateModel);
                    bo.setOrderTime(receiveModel.getCreateTime());
                    List<SohuIndependentTempBo> platformObjects = accountProcessService.accountPlatformObjects(bo);
                    SohuSplitInfoModel platformModel = accountPlatformSplitInfo(platformObjects, splitInfoList, receiveId, childTaskNumber, posSeq, true, chargeFee);
                    //接单人分账
                    BigDecimal allPlatfromAmount = platformObjects.stream().map(SohuIndependentTempBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal receiveDistributorAmount = CalUtils.sub(settleAmount, CalUtils.add(allPlatfromAmount, chargeFee));
                    //接单人分账
                    receiveSplitInfo(receiveDistributorAmount, receiveUserId, splitInfoList, receiveId, childTaskNumber, posSeq);
                    // 分销人分账
                    BigDecimal shareSettleAmount = BigDecimal.ZERO;
                    //分销平台服务费
                    BigDecimal platformInviteAmount = BigDecimal.ZERO;
                    //分销手续费
                    BigDecimal shareChareFee = BigDecimal.ZERO;
                    if (isShare) {
                        shareSettleAmount = CalUtils.multiply(sohuBusyTaskPayVo.getShareAmount(), rate).setScale(2, RoundingMode.HALF_UP);
                        if (shareSettleAmount.compareTo(BigDecimal.ZERO) > 0 && shareBalance.compareTo(shareSettleAmount) < 0) {
                            shareSettleAmount = shareBalance;
                        }
                        shareChareFee = CalUtils.multiply(sohuBusyTaskPayVo.getChargeAmount(), CalUtils.divide(shareSettleAmount, sohuBusyTaskPayVo.getShareAmount())).setScale(2, RoundingMode.HALF_UP);
//                    platformInviteAmount = CalUtils.multiply(shareSettleAmount, CalUtils.divide(sohuBusyTaskPayVo.getPlatformRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
                        AccountDistributionBo distributionBo = new AccountDistributionBo();
                        distributionBo.setShareUserId(shareUserId);
                        distributionBo.setShareAmount(shareSettleAmount);
                        distributionBo.setChargeFee(shareChareFee);
                        distributionBo.setTemplateModel(templateModel);
                        distributionBo.setOrderTime(receiveModel.getCreateTime());
                        distributionBo.setPlatfromDis(false);
                        List<SohuIndependentTempBo> distributionObjects = accountProcessService.accountDistributionObjects(distributionBo);
                        accountPlatformSplitInfo(distributionObjects, splitInfoList, receiveId, childTaskNumber, posSeq, false, shareChareFee);
                    }
//                    //分销人分账
//                    BigDecimal shareAmount = shareSplitInfo(isShare, shareUserId, shareInviteId, CalUtils.sub(shareSettleAmount, shareChareFee), templateModel.getDistributorRatio(), splitInfoList, receiveId, receiveUserId, childTaskNumber, posSeq, receiveModel.getCreateTime());
//                    //分销人拉新人分账
//                    shareInviteSplitInfo(shareInviteId, CalUtils.sub(shareSettleAmount, shareChareFee, shareAmount), splitInfoList, receiveId, childTaskNumber, posSeq, receiveModel.getCreateTime());
                    if (platformInviteAmount.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal indepeendentPrice = CalUtils.add(platformModel.getAmount(), CalUtils.sub(platformInviteAmount, shareChareFee));
                        platformModel.setAmount(indepeendentPrice);
                        platformModel.setChargeAmount(CalUtils.add(chargeFee, shareChareFee));
                    }
                    platfromSplitInfoList.add(platformModel);
                    receiveBalance = receiveBalance.subtract(settleAmount);
                    shareBalance = shareBalance.subtract(shareSettleAmount);
                    if (isDistributor) {
                        break;
                    }
                }
            }
            //判断分账对象是否为空
            if (CollUtil.isNotEmpty(splitInfoList)) {
                //封装翼码账户信息
                setMechantInfo(splitInfoList);
                //获取平台总金额
                BigDecimal allPlatformAmount = platfromSplitInfoList.stream().map(SohuSplitInfoModel::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                //分账操作
                Boolean success = distribution(posSeq, sohuBusyTaskPayVo.getPayNumber(), splitInfoList, String.valueOf(CalUtils.yuanToCent(allPlatformAmount).intValue()));
                List<SohuIndependentOrderBo> boList = Lists.newArrayList();
                List<SohuTradeRecordBo> tradeList = Lists.newArrayList();
                SohuMaterialPromotionOrderCmdBo cmdBo = new SohuMaterialPromotionOrderCmdBo();
                //封装平台对象
                SohuSplitInfoModel platformModel = platfromSplitInfoList.get(0);
                platformModel.setAmount(allPlatformAmount);
                splitInfoList.add(platformModel);
                //整合对象
                List<SohuSplitInfoModel> mergedList = new ArrayList<>();
                Map<String, List<SohuSplitInfoModel>> collect = splitInfoList.stream().collect(Collectors.groupingBy(e -> {
                    return e.getUserId() + "#" + e.getIndependentObject() + "#" + e.getSiteId() + "#" + e.getSiteType();
                }));
                for (Map.Entry<String, List<SohuSplitInfoModel>> entry : collect.entrySet()) {
                    List<SohuSplitInfoModel> list = entry.getValue();
                    SohuSplitInfoModel splitInfoModel = list.get(0);
                    splitInfoModel.setAmount(list.stream().map(SohuSplitInfoModel::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                    splitInfoModel.setChargeAmount(list.stream().map(SohuSplitInfoModel::getChargeAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                    mergedList.add(splitInfoModel);
                }
                //插入sohu_independent_order
                saveIndependentOrder(taskNumber, mergedList, success, boList, sohuBusyTaskModel.getTitle(), sohuBusyTaskModel.getFullAmount(), sohuBusyTaskModel.getUserId());
                //插入sohu_trade_record
                saveTradeRecord(taskNumber, sohuBusyTaskPayVo.getPayChannel(), mergedList, tradeList, SohuTradeRecordEnum.Type.BusyTaskFlow, sohuBusyTaskPayVo.getTransactionId(), true);
                //插入素材流水订单表
                SohuSplitInfoModel shareModel = mergedList.stream().filter(item -> item.getIndependentObject().equals(SohuIndependentObject.distribution.getKey())).findFirst().orElse(null);
                if (Objects.nonNull(shareModel)) {
                    saveMaterialOrder(shareModel.getUserId(), shareModel.getReceiveUserId(), taskNumber, sohuBusyTaskPayVo.getTransactionId(), shareModel.getAmount(), cmdBo, BusyTaskTypeEnum.FLOW_TASK.getCode());
                }

                if (CollUtil.isNotEmpty(boList)) {
                    sohuIndependentOrderService.insertByBoList(boList);
                }
                //修改商单状态 待结算->已完结
                boolean b = remoteBusyTaskService.settleBusyTask(taskNumber);
                log.info("修改商单状态 待结算->已完结 {}", b);
                if (CollUtil.isNotEmpty(tradeList)) {
                    remoteMiddleTradeRecordService.insertBatch(tradeList);
                }
                if (cmdBo != null && cmdBo.getShareUserId() != null) {
                    materialPromotionOrderService.save(cmdBo);
                }
                //更新支付表中余额
                SohuBusyTaskPayBo payBo = new SohuBusyTaskPayBo();
                payBo.setId(sohuBusyTaskPayVo.getId());
                payBo.setReceiveDistributorAmount(receiveBalance);
                payBo.setShareDistributorAmount(shareBalance);
                remoteBusyTaskService.updateBusyTaskPay(payBo);
                // 延时队列5s后查询分账是否成功-分账成功修改分账订单记录表状态,客户已绑卡，发起提现流程
                sendConfirmqueryMQ(posSeq, posSeq, String.join(",", childTaskNumbers), success, SohuTradeRecordEnum.Type.BusyTaskFlow.getCode(), 2L);
                //同步收入统计
                MqMessaging mqCancelMessaging = new MqMessaging(taskNumber, MqKeyEnum.INCOME_INFO.getKey());
                remoteStreamMqService.sendDelayMsg(mqCancelMessaging, 2L);
                //平台操作操作终止商单-发送通知
                if (LoginHelper.isAdmin(LoginHelper.getUserId())) {
                    CompletableFuture.supplyAsync(() -> {
                        sendNotice(sohuBusyTaskModel, receiveList);
                        return true;
                    }, asyncConfig.getAsyncExecutor());
                }
            } else {
                //修改商单状态 待结算->已完结
                boolean b = remoteBusyTaskService.settleBusyTask(taskNumber);
                log.info("修改商单状态 待结算->已完结 {}", b);
            }
            BigDecimal settleBalanceAll = CalUtils.sub(balance, receiveBalance);
            LoginUser loginUser = LoginHelper.getLoginUser();
            //异步新增埋点数据
            CompletableFuture.runAsync(() -> {
                SohuUserBehaviorRecordPointBo.EventAttribute eventAttribute = new SohuUserBehaviorRecordPointBo.EventAttribute();
                eventAttribute.setContentNo(taskNumber);
                eventAttribute.setContentName(sohuBusyTaskModel.getTitle());
                eventAttribute.setOrderNo(sohuBusyTaskPayVo.getOrderNo());
                eventAttribute.setPayAmount(settleBalanceAll);
                eventAttribute.setAmount(sohuBusyTaskModel.getFullAmount());
                eventAttribute.setContentType("流量");
                syncSettleUserBehavior(loginUser.getUserId(), loginUser.getNickname(), eventAttribute);
            }, asyncConfig.getAsyncExecutor());
        } catch (Exception e) {
            log.error("结算异常", e);
            //延时分账退回
            sendConfirmrefund(posSeq);
            throw new RuntimeException(e.getMessage());
        } finally {
            //释放锁
            RedisUtils.deleteObject(lockKey);
        }
        return true;
    }

    /**
     * 处理结算逻辑
     *
     * @param receiveId
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean handleSettle(Long receiveId) {
        //查询接单记录
        SohuBusyTaskReceiveVo receiveModel = remoteBusyTaskReceiveService.selectById(receiveId);
        Objects.requireNonNull(receiveModel, "接单记录不存在");
        List<String> validStatus = List.of(SohuBusyTaskState.WaitSettle.name(), SohuBusyTaskState.Execute.name(), SohuBusyTaskState.WaitApproveSettle.name());
        if (!validStatus.contains(receiveModel.getState())) {
            throw new RuntimeException("任务状态异常");
        }
        Long receiveUserId = receiveModel.getUserId();
        Long shareUserId = receiveModel.getSharePerson();
        //查询商单子任务
        String childTaskNumber = receiveModel.getTaskNumber();
        SohuBusyTaskSiteVo taskSiteModel = remoteBusyTaskSiteService.queryByTaskNumber(childTaskNumber);
        Objects.requireNonNull(taskSiteModel, "商单子任务不存在");
        //判断接单状态 待结算，执行中->结算
        if (!validStatus.contains(taskSiteModel.getState())) {
            throw new RuntimeException("任务状态异常");
        }
        //查询商单主任务
        String taskNumber = taskSiteModel.getMasterTaskNumber();
        SohuBusyTaskVo sohuBusyTaskModel = remoteBusyTaskService.getByTaskNo(taskNumber);
        Objects.requireNonNull(sohuBusyTaskModel, "商单主任务不存在");

        String lockKey = String.format("sohu:lock:pass_user_master_task_number:%s", taskNumber);
        if (!RedisUtils.setObjectIfAbsent(lockKey, 1, Duration.ofSeconds(10))) {
            throw new RuntimeException("该订单正在结算中，请稍后再试");
        }
        //查询达标人数
        Integer reachCount = remoteImGroupOrderUserService.getPassPersonUserByTaskNumber(childTaskNumber);
        //如果达标人数为0，则直接结算
        if (reachCount == Constants.ZERO) {
            //修改商单状态 待结算->已完结
            boolean b = remoteBusyTaskSiteService.overBusyTask(childTaskNumber);
            log.info("{}达标人数为0修改商单状态={}", childTaskNumber, b);
            //释放锁
            RedisUtils.deleteObject(lockKey);
            return true;
        }
        // 流水号
        String posSeq = NumberUtil.getOrderNo(OrderConstants.YI_MA_INDEPENDENT_NO);
        try {
            //查询支付订单
            List<SohuBusyTaskPayVo> payList = remoteBusyTaskService.queryByTaskNumber(taskNumber, PayStatus.Paid.name());
            if (CollUtil.isEmpty(payList)) {
                throw new RuntimeException("支付订单不存在");
            }
            SohuBusyTaskPayVo sohuBusyTaskPayVo = payList.get(0);
            //接单剩余金额
            BigDecimal receiveBalance = sohuBusyTaskPayVo.getReceiveDistributorAmount();
            //分销剩余金额
            BigDecimal shareBalance = sohuBusyTaskPayVo.getShareDistributorAmount();
            //此次结算金额
            BigDecimal settleAmount = sohuBusyTaskModel.getSingleAmount().multiply(BigDecimal.valueOf(reachCount));
            //校验剩余分销金额是否足够,如果不足，则使用剩余金额
            if (receiveBalance.compareTo(settleAmount) < 0) {
                settleAmount = receiveBalance;
            }
            //此次平台手续费
            BigDecimal chargeFee = CalUtils.multiply(sohuBusyTaskPayVo.getChargeAmount(), CalUtils.divide(settleAmount, sohuBusyTaskPayVo.getPayAmount())).setScale(2, RoundingMode.HALF_UP);
            //完成百分比
            BigDecimal rate = CalUtils.divide(BigDecimal.valueOf(reachCount), BigDecimal.valueOf(sohuBusyTaskModel.getDeliveryStandard()));
            boolean success = false;
            List<SohuIndependentOrderBo> boList = Lists.newArrayList();
            List<SohuTradeRecordBo> tradeList = Lists.newArrayList();
            SohuMaterialPromotionOrderCmdBo cmdBo = new SohuMaterialPromotionOrderCmdBo();
            //分销总金额
            BigDecimal shareSettleAmount = BigDecimal.ZERO;
            //分账金额大于0,进行分账
            if (settleAmount.compareTo(BigDecimal.ZERO) > 0) {
                //判断是否是分销商单
                Boolean isShare = false;
                if (!sohuBusyTaskModel.getKickbackType().equals("none") && sohuBusyTaskModel.getReceiveNum() == Constants.ONE && !CalUtils.isNullOrZero(shareUserId)) {
                    isShare = true;
                }
                //封装分账对象信息
                List<SohuSplitInfoModel> splitInfoList = new ArrayList<>();
                //平台分账各对象封装
                SohuIndependentTemplateModel templateModel = templateService.queryTemplateInfo(receiveModel.getSiteType(), receiveModel.getSiteId(), 2);
                AccountPlatformBo bo = new AccountPlatformBo();
                bo.setBusyType(BusyType.BusyTask.getType());
                bo.setBusyCode(sohuBusyTaskModel.getId());
                bo.setUserId(receiveUserId);
                bo.setSiteType(receiveModel.getSiteType());
                bo.setEntranceSiteId(receiveModel.getSiteId());
                bo.setCitySiteId(taskSiteModel.getSiteId());
                bo.setIndustryType(taskSiteModel.getIndustryType());
                bo.setPayPrice(settleAmount);
                bo.setChargeFee(chargeFee);
                bo.setTemplateModel(templateModel);
                bo.setOrderTime(receiveModel.getCreateTime());
                List<SohuIndependentTempBo> platformObjects = accountProcessService.accountPlatformObjects(bo);
                SohuSplitInfoModel platformModel = accountPlatformSplitInfo(platformObjects, splitInfoList, receiveId, childTaskNumber, posSeq, true, chargeFee);
                //接单人分账金额  = 结算金额 - 平台分账金额 - 平台手续费
                BigDecimal allPlatfromAmount = platformObjects.stream().map(SohuIndependentTempBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal receiveDistributorAmount = CalUtils.sub(settleAmount, CalUtils.add(allPlatfromAmount, chargeFee));
                receiveSplitInfo(receiveDistributorAmount, receiveUserId, splitInfoList, receiveId, childTaskNumber, posSeq);
                // 分销人分账
                BigDecimal platformInviteAmount = BigDecimal.ZERO;
                BigDecimal shareChareFee = BigDecimal.ZERO;
                SohuIndependentTempBo shareModel = null;
                if (isShare) {
                    shareSettleAmount = CalUtils.multiply(sohuBusyTaskPayVo.getShareAmount(), rate).setScale(2, RoundingMode.HALF_UP);
                    if (shareSettleAmount.compareTo(BigDecimal.ZERO) > 0 && shareBalance.compareTo(shareSettleAmount) < 0) {
                        shareSettleAmount = shareBalance;
                    }
                    shareChareFee = CalUtils.multiply(sohuBusyTaskPayVo.getChargeAmount(), CalUtils.divide(shareSettleAmount, sohuBusyTaskPayVo.getShareAmount())).setScale(2, RoundingMode.HALF_UP);
//                    platformInviteAmount = CalUtils.multiply(shareSettleAmount, CalUtils.divide(sohuBusyTaskPayVo.getPlatformRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
                    AccountDistributionBo distributionBo = new AccountDistributionBo();
                    distributionBo.setShareUserId(shareUserId);
                    distributionBo.setShareAmount(shareSettleAmount);
                    distributionBo.setChargeFee(shareChareFee);
                    distributionBo.setTemplateModel(templateModel);
                    distributionBo.setOrderTime(receiveModel.getCreateTime());
                    distributionBo.setPlatfromDis(Boolean.FALSE);
                    List<SohuIndependentTempBo> distributionObjects = accountProcessService.accountDistributionObjects(distributionBo);
                    accountPlatformSplitInfo(distributionObjects, splitInfoList, receiveId, childTaskNumber, posSeq, false, shareChareFee);
                    shareModel = distributionObjects.stream().filter(temp -> temp.getIndependentObject().equals(SohuIndependentObject.distribution.getKey())).findFirst().orElse(null);
                }
//                //分销人分账
//                BigDecimal shareAmount = shareSplitInfo(isShare, shareUserId, shareInviteId, CalUtils.sub(shareSettleAmount, shareChareFee), templateModel.getDistributorRatio(), splitInfoList, receiveId, receiveUserId, childTaskNumber, posSeq, receiveModel.getCreateTime());
//                //分销人拉新人分账
//                shareInviteSplitInfo(shareInviteId, CalUtils.sub(shareSettleAmount, shareChareFee, shareAmount), splitInfoList, receiveId, childTaskNumber, posSeq, receiveModel.getCreateTime());
                //封装翼码账户信息
                setMechantInfo(splitInfoList);
                if (platformInviteAmount.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal indepeendentPrice = CalUtils.add(platformModel.getAmount(), CalUtils.sub(platformInviteAmount, shareChareFee));
                    platformModel.setAmount(indepeendentPrice);
                    platformModel.setChargeAmount(CalUtils.add(chargeFee, shareChareFee));
                }
                //分账操作
                success = distribution(posSeq, sohuBusyTaskPayVo.getPayNumber(), splitInfoList, String.valueOf(CalUtils.yuanToCent(platformModel.getAmount()).intValue()));
                //封装平台记录
                splitInfoList.add(platformModel);
                //插入sohu_independent_order
                saveIndependentOrder(taskNumber, splitInfoList, success, boList, sohuBusyTaskModel.getTitle(), sohuBusyTaskModel.getFullAmount(), sohuBusyTaskModel.getUserId());
                //插入sohu_trade_record
                saveTradeRecord(taskNumber, sohuBusyTaskPayVo.getPayChannel(), splitInfoList, tradeList, SohuTradeRecordEnum.Type.BusyTaskFlow, sohuBusyTaskPayVo.getTransactionId(), true);
                //插入素材流水订单表
                if (Objects.nonNull(shareModel)) {
                    saveMaterialOrder(shareUserId, receiveUserId, taskNumber, sohuBusyTaskPayVo.getTransactionId(), shareModel.getIndependentPrice(), cmdBo, BusyTaskTypeEnum.FLOW_TASK.getCode());
                }
            }
            if (CollUtil.isNotEmpty(boList)) {
                sohuIndependentOrderService.insertByBoList(boList);
            }
            //修改商单状态 待结算->待提现
            boolean b = remoteBusyTaskSiteService.overBusyTask(childTaskNumber);
            log.info("{},待结算修改商单状态={}", childTaskNumber, b);
            if (CollUtil.isNotEmpty(tradeList)) {
                remoteMiddleTradeRecordService.insertBatch(tradeList);
            }
            if (cmdBo != null && cmdBo.getShareUserId() != null) {
                materialPromotionOrderService.save(cmdBo);
            }
            SohuBusyTaskPayBo payBo = new SohuBusyTaskPayBo();
            payBo.setId(sohuBusyTaskPayVo.getId());
            payBo.setReceiveDistributorAmount(CalUtils.sub(sohuBusyTaskPayVo.getReceiveDistributorAmount(), settleAmount));
            payBo.setShareDistributorAmount(CalUtils.sub(sohuBusyTaskPayVo.getShareAmount(), shareSettleAmount));
            remoteBusyTaskService.updateBusyTaskPay(payBo);
            //延时队列5s后查询分账是否成功-分账成功修改分账订单记录表状态,客户已绑卡，发起提现流程
            sendConfirmqueryMQ(posSeq, posSeq, childTaskNumber, success, SohuTradeRecordEnum.Type.BusyTaskFlow.getCode(), 2L);
            //同步收入统计
            MqMessaging mqCancelMessaging = new MqMessaging(taskNumber, MqKeyEnum.INCOME_INFO.getKey());
            remoteStreamMqService.sendDelayMsg(mqCancelMessaging, 2L);

            LoginUser loginUser = LoginHelper.getLoginUser();
            BigDecimal finalSettleAmount = settleAmount;
            //异步新增埋点数据
            CompletableFuture.runAsync(() -> {
                SohuUserBehaviorRecordPointBo.EventAttribute eventAttribute = new SohuUserBehaviorRecordPointBo.EventAttribute();
                eventAttribute.setContentNo(taskNumber);
                eventAttribute.setContentName(sohuBusyTaskModel.getTitle());
                eventAttribute.setOrderNo(sohuBusyTaskPayVo.getOrderNo());
                eventAttribute.setPayAmount(finalSettleAmount);
                eventAttribute.setAmount(sohuBusyTaskModel.getFullAmount());
                eventAttribute.setContentType("流量");
                syncSettleUserBehavior(loginUser.getUserId(), loginUser.getNickname(), eventAttribute);
            }, asyncConfig.getAsyncExecutor());
        } catch (Exception e) {
            log.error("结算异常", e);
            //延时分账退回
            sendConfirmrefund(posSeq);
            throw new RuntimeException(e);
        } finally {
            //释放锁
            RedisUtils.deleteObject(lockKey);
        }
        return true;
    }


    @Override
    public Boolean overSettle(String taskNumber) {
        log.info("overSettle:{}", taskNumber);
        //避免重复提交
        if (!RedisUtils.setObjectIfAbsent(TASK_SETTLE_EXECUTE + taskNumber, 1, Duration.ofSeconds(3))) {
            throw new RuntimeException("正在处理中，请稍后再试");
        }
        //商单完结后给平台分账
//        MqMessaging platformMessaging = new MqMessaging(taskNumber, "delay_confirm_platform");
//        remoteStreamMqService.sendDelayMsg(platformMessaging, 2L);
        //延时队列5s后退款
        MqMessaging mqMessaging = new MqMessaging(taskNumber, MqKeyEnum.DELAY_CONFIRM_REFUND.getKey());
        remoteStreamMqService.sendDelayMsg(mqMessaging, 3L);
        return true;
    }

    @Override
    public Boolean barcodeCancelPay(String taskNumber) {
        // 判断是否未支付记录，如果存在删除撤销之前的交易
        List<SohuBusyTaskPayVo> payList = remoteBusyTaskService.queryByTaskNumber(taskNumber, PayStatus.WaitPay.name());
        if (CollUtil.isEmpty(payList)) {
            return true;
        }
        SohuBusyTaskPayVo sohuBusyTaskPayVo = payList.get(0);
        //撤销交易
        this.sendBarcodeCancel(sohuBusyTaskPayVo.getPayNumber());
        //更新支付状态 待支付->取消支付
        SohuBusyTaskPayBo payBo = new SohuBusyTaskPayBo();
        payBo.setId(sohuBusyTaskPayVo.getId());
        payBo.setPayStatus(PayStatus.Cancel.name());
        remoteBusyTaskService.updateBusyTaskPay(payBo);
        return true;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean delayConfirmPlatform(String taskNumber) {
//        return platformDistribution(taskNumber);
        return true;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean delayConfirmRefund(String taskNumber) {
        //查询主任务
        SohuBusyTaskVo sohuBusyTaskVo = remoteBusyTaskService.getByTaskNo(taskNumber);
        Objects.requireNonNull(sohuBusyTaskVo, "商单主任务不存在");
        //查询支付订单
        List<SohuBusyTaskPayVo> payList = remoteBusyTaskService.queryByTaskNumber(taskNumber, PayStatus.Paid.name());
        if (CollUtil.isEmpty(payList)) {
            throw new RuntimeException("支付订单不存在");
        }
        SohuBusyTaskPayVo sohuBusyTaskPayVo = payList.get(0);
        //退款余额
        BigDecimal balance = CalUtils.add(sohuBusyTaskPayVo.getReceiveDistributorAmount(), sohuBusyTaskPayVo.getShareDistributorAmount());
        if (balance.compareTo(BigDecimal.ZERO) <= 0) {
            return true;
        }
        //剩余金额退款给发单人
        int totalPrice = CalUtils.yuanToCent(balance).intValue();
        //退款流水号
        String refundNumber = NumberUtil.getOrderNo(OrderConstants.ORDER_PREFIX_REFUND);
        sendRefund(refundNumber, totalPrice, sohuBusyTaskPayVo.getPayNumber());
        //记录退款流水
        saveRefundRecord(taskNumber, refundNumber, balance, sohuBusyTaskPayVo, SohuTradeRecordEnum.Type.BusyTaskFlow.getCode(), "流量商单分账余额退款", "");
        return true;
    }

    @Override
    public Boolean onDistributing(Long userId, String childTaskNumber) {
        Boolean flag = false;
        SohuBusyTaskSiteVo taskSiteVo = remoteBusyTaskSiteService.queryByTaskNumber(childTaskNumber);
        Objects.requireNonNull(taskSiteVo, "商单子任务不存在");
        //查询分账记录表
        SohuIndependentOrderVo independentOrderVo = sohuIndependentOrderService.queryByUserOrderNo(userId, taskSiteVo.getMasterTaskNumber()
                , IndependentStatusEnum.DISTRIBUTING.getCode());
        if (independentOrderVo != null) {
            flag = true;
        }
        return flag;
    }

    @Override
    public Boolean applySettle(String taskNumber, String reason) {
        String lockKey = String.format("sohu:lock:pass_user_master_task_number:%s", taskNumber);
        if (!RedisUtils.setObjectIfAbsent(lockKey, 1, Duration.ofSeconds(10))) {
            throw new RuntimeException("该订单正在结算中，请稍后再试");
        }
        try {
            //修改商单状态 待结算->已完结
            boolean b = remoteBusyTaskService.applySettleBusyTask(taskNumber, reason);
            log.info("审核结算,商单号:{}，状态:{}", taskNumber, b);
            handleBatchSettle(taskNumber);
        } catch (Exception e) {
            log.error("结算异常", e);
            throw new RuntimeException(e.getMessage());
        } finally {
            RedisUtils.deleteObject(lockKey);
        }
        return true;
    }


    private void sendNotice(SohuBusyTaskVo sohuBusyTaskModel, List<SohuBusyTaskReceiveVo> receiveList) {
        //发送终止通知 -> 发单方
        remoteBusyTaskNoticeService.sendTaskNotice(sohuBusyTaskModel.getId(), TaskNoticeEnum.TASK_STOP,
                sohuBusyTaskModel.getUserId(), null, null, Boolean.TRUE);
        //发送终止通知 -> 接单方
        receiveList.forEach(receive -> {
            remoteBusyTaskNoticeService.sendTaskNotice(sohuBusyTaskModel.getId(), TaskNoticeEnum.TASK_STOP_RECEIVER,
                    receive.getUserId(), null, null, Boolean.TRUE);
        });
    }
}
