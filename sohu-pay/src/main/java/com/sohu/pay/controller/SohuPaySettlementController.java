package com.sohu.pay.controller;

import cn.hutool.core.util.StrUtil;
import com.sohu.busyorder.api.bo.SohuPayBusyBo;
import com.sohu.busyorder.api.bo.SohuRefundBusyBo;
import com.sohu.busyorder.api.vo.SohuPayBusyVo;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.enums.SohuTradeRecordEnum;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.pay.api.bo.SohuPayBatchSettlementBo;
import com.sohu.pay.api.bo.SohuPayPasswordBo;
import com.sohu.pay.api.bo.SohuPaySettlementBo;
import com.sohu.pay.service.SohuPaySettlementService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 商单结算控制器
 * <AUTHOR>
 * @date 2025/1/14 17:10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/settlement")
public class SohuPaySettlementController {

    private final SohuPaySettlementService paySettlementService;

    @Operation(summary = "结算(密码明文)", description = "负责人：汪伟")
    @PostMapping
    public R<Boolean> settlement(@Validated @RequestBody SohuPaySettlementBo bo) {
        if (StrUtil.isEmpty(bo.getBusyTaskType())){
            bo.setBusyTaskType(SohuTradeRecordEnum.Type.BusyTaskFlow.getCode());
        }
        return R.ok(paySettlementService.settle(bo));
    }

    @Operation(summary = "结算(密码加密)", description = "负责人：汪伟")
    @PostMapping("/V2")
    public R<Boolean> settlementV2(@Validated @RequestBody SohuPaySettlementBo bo) {
        if (StrUtil.isEmpty(bo.getBusyTaskType())){
            bo.setBusyTaskType(SohuTradeRecordEnum.Type.BusyTaskFlow.getCode());
        }
        return R.ok(paySettlementService.settleV2(bo));
    }

    @Operation(summary = "批量结算(密码明文)", description = "负责人：汪伟 批量结算")
    @PostMapping("/batch")
    public R<Boolean> batchSettlement(@Validated @RequestBody SohuPayBatchSettlementBo bo) {
        if (StrUtil.isEmpty(bo.getBusyTaskType())){
            bo.setBusyTaskType(SohuTradeRecordEnum.Type.BusyTaskFlow.getCode());
        }
        return R.ok(paySettlementService.batchSettle(bo));
    }

    @Operation(summary = "批量结算(密码加密)", description = "负责人：汪伟 批量结算")
    @PostMapping("/batchV2")
    public R<Boolean> batchSettlement2(@Validated @RequestBody SohuPayBatchSettlementBo bo) {
        if (StrUtil.isEmpty(bo.getBusyTaskType())){
            bo.setBusyTaskType(SohuTradeRecordEnum.Type.BusyTaskFlow.getCode());
        }
        return R.ok(paySettlementService.batchSettleV2(bo));
    }
    @Operation(summary = "设置支付密码(密码明文)", description = "负责人：汪伟")
    @PostMapping("/setPassword")
    public R<Boolean> payPassword(@Validated @RequestBody SohuPayPasswordBo bo) {
        return R.ok(paySettlementService.payPassword(bo) );
    }

    @Operation(summary = "设置支付密码(密码加密)", description = "负责人：汪伟")
    @PostMapping("/setPasswordV2")
    public R<Boolean> payPassword2(@Validated @RequestBody SohuPayPasswordBo bo) {
        return R.ok(paySettlementService.payPasswordV2(bo) );
    }

    @Operation(summary = "支付记录", description = "负责人：汪伟 支付记录")
    @GetMapping("/pay/list")
    public TableDataInfo<SohuPayBusyVo> payList(SohuPayBusyBo bo, PageQuery pageQuery) {
        if (StrUtil.isEmpty(bo.getBusyTaskType())){
            bo.setBusyTaskType(SohuTradeRecordEnum.Type.BusyTaskFlow.getCode());
        }
        return paySettlementService.payList(bo,pageQuery);
    }

    @Operation(summary = "退款", description = "负责人：汪伟 退款")
    @PostMapping("/refund")
    public R<Boolean> refund(@RequestBody SohuRefundBusyBo bo) {
        if (StrUtil.isEmpty(bo.getBusyTaskType())){
            bo.setBusyTaskType(SohuTradeRecordEnum.Type.BusyTaskFlow.getCode());
        }
        return R.ok(paySettlementService.refund(bo));
    }

}
