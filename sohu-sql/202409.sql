# 公告 20240902
CREATE TABLE `sohu_notice` (
`id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
`user_id` bigint unsigned NOT NULL COMMENT '用户id',
`name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公告名称',
`image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公告封面',
`msg` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '公告描述',
`info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公告详情',
`type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '应用生效端(all:总后台 agent:代理商后台 stationAgent:站长后台 project:任务方后台)',
`state` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态（OnShelf：上架，OffShelf：下架）',
`on_shelf_time` datetime DEFAULT NULL COMMENT '上架时间',
`is_del` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
`create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
`update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='公告管理表';

# 互动通知表增加2个字段，最顶级业务类型和最顶级业务ID
ALTER TABLE `sohu_interact_notice`
    ADD COLUMN `top_type` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '最顶级业务类型:[Article:文章,House:房产,Video:视频,User:用户,Shop:店铺,Goods:商品,Qora:问答,Project:项目,Professional:专业服务,BusyModel:生意模式]' AFTER `read_state`,
ADD COLUMN `top_code` bigint UNSIGNED NULL COMMENT '业务id' AFTER `top_type`;

# 互动通知 20240910
ALTER TABLE sohu_interact_notice MODIFY COLUMN `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '通知标题';
ALTER TABLE sohu_outer_notice MODIFY COLUMN `title` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '通知标题';

# 角色入驻配置管理表 20240921
CREATE TABLE `sohu_platform_config` (
`id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
`platform_role_id` bigint DEFAULT NULL COMMENT '角色id',
`type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '身份认证类型(personal:个人 business:企业)',
`status` tinyint(1) DEFAULT NULL COMMENT '资质入驻(0停用 1启用)',
`entry_status` tinyint(1) DEFAULT '0' COMMENT '入驻状态(0关闭 1开启)',
`intro_page` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '介绍页',
`protocol_id` bigint DEFAULT NULL COMMENT '协议id',
`create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
`update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色入驻配置管理表';

CREATE TABLE `sohu_platform_config_aptitude` (
`id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
`platform_config_id` bigint NOT NULL COMMENT '角色入驻配置id',
`title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资质标题',
`type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资质类型(image:图片 text:文本)',
`is_required` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否必填(0:非必填 1:必填)',
`msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '资质描述',
`prompt_words` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '默认提示词',
`image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '示例照片',
`is_del` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
`create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
`update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色入驻配置资质表';

# 素材推广订单表 20240923 (汪伟)
CREATE TABLE `sohu_material_promotion_order` (
     `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
     `share_user_id` bigint unsigned NOT NULL COMMENT '分销人id',
     `buy_user_id` bigint unsigned NOT NULL COMMENT '被分销人id',
     `buy_user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '被分销人名称',
     `site_id` bigint unsigned DEFAULT NULL COMMENT '站点id',
     `material_id` bigint unsigned DEFAULT NULL COMMENT '素材id',
     `material_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '素材类型(短剧(Playlet) 商品(Goods) 商单(BuyTask))',
     `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '素材分享订单号-唯一',
     `trade_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '对应业务交易单号-唯一',
     `transaction_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方支付流水号',
     `pay_price` decimal(10,2) DEFAULT '0.00' COMMENT '实付金额-0.00',
     `independent_price` decimal(10,2) DEFAULT '0.00' COMMENT '分账金额-0.00',
     `independent_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '分账状态：0 未分账  1 已分账  2 分账处理中  3 分账异常  4 已退款',
     `trade_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '交易类型（按集付费(Episodes)，按剧付费(Drama)，充值付费(Recharge) 商品(Goods) 商单(BuyTask)）',
     `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (`id`) USING BTREE,
     KEY `order_no` (`order_no`) USING BTREE COMMENT '素材分享订单号',
     KEY `idx_user_mat` (`share_user_id`,`material_id`) COMMENT '素材分享人'
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材推广订单流水表';

# 第三方分账单表增加素材id字段 20240924 (汪伟)
ALTER TABLE sohu_independent_order ADD 	`material_id` bigint unsigned DEFAULT NULL COMMENT '素材id' AFTER site_id;

# 短剧观看或收藏记录表增加分销人id与素材id字段 20240925 (汪伟)
ALTER table sohu_playlet_user ADD `share_person` bigint DEFAULT NULL COMMENT '分销人ID或分享人ID' AFTER intro;
ALTER table sohu_playlet_user ADD `material_id` bigint  DEFAULT NULL COMMENT '素材id' AFTER share_person;

ALTER TABLE sohu_trade_record ADD `template_type` tinyint(1) NOT NULL COMMENT '分账模版类型：1、商品  2、商单 3、短剧分账(平台采购) 4、短剧分账(版权方上传)' AFTER unq;
ALTER TABLE sohu_trade_record ADD `user_type` tinyint(1) NOT NULL COMMENT '用户类型：1 个人  2 商户' AFTER template_type;
ALTER TABLE sohu_trade_record ADD `source_type` tinyint(1) NOT NULL COMMENT '收入来源：1 分佣  2 分享' AFTER user_type;

# im群表增加 进群是否绑定拉新关系 字段
ALTER TABLE `ry-im`.`sohu_im_group`
    ADD COLUMN `bind_user` tinyint NULL COMMENT '进群是否绑定拉新关系' AFTER `unq`;
ALTER TABLE `ry-im`.`sohu_im_group_user`
    MODIFY COLUMN `channel_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '进群渠道code码(授权手机号渠道code码)' AFTER `ext`,
    ADD COLUMN `channel_bind_user_code` varchar(50) NULL COMMENT '进群绑定拉新关系的渠道code码' AFTER `channel_code`;

#新增分销素材库
CREATE TABLE `sohu_independent_material`
(
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键素材id' NOT NULL COMMENT '主键素材id',
    `material_name`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '素材名称',
    `material_type`    varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '素材类型',
    `price`            decimal(10, 2)                                                NOT NULL COMMENT '商品金额',
    `material_user_id` bigint                                                        NOT NULL COMMENT '素材用户id',
    `site_id`          bigint unsigned                                                        DEFAULT NULL COMMENT '站点id',
    `material_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材关联唯一标识',
    `category_id`    bigint                                                        DEFAULT NULL COMMENT '短剧分类id(冗余字段)',
    `create_time`      datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='分销素材库';

#新增分销素材库子表
CREATE TABLE `sohu_independent_material_user`
(
    `material_id`              bigint NOT NULL COMMENT '主键素材id',
    `material_share_user_id`   bigint                                                        DEFAULT NULL COMMENT '素材分享人id',
    `material_share_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '素材分销人用户名',
    `material_share_url`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分享链接(预留)',
    PRIMARY KEY (`material_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='用户分销素材库';
# 群关联表增加字段，关联内容
ALTER TABLE `sohu_im_group_relate`
    ADD COLUMN `busy_content` varchar(500) NULL COMMENT '关联内容' AFTER `parent_code`;

# 添加商户可提现余额字段 ********(汪伟)
ALTER TABLE sohu_bill_record ADD `wallet_balance_merchant` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商户可提现余额' AFTER wallet_balance;

# 商品表 ********
ALTER TABLE sohu_product ADD `is_self` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否自营：1-自营，0-非自营';

# 分销商单表 ********
ALTER TABLE sohu_pm_share_pub ADD `user_id` bigint unsigned NOT NULL COMMENT '用户id';

# 入驻资质表 ******** (汪伟)
CREATE TABLE `sohu_account_enter_aptitude` (
       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
       `account_enter_id` bigint NOT NULL COMMENT '入驻id',
       `aptitude_config_id` bigint NOT NULL COMMENT '资质配置id',
       `content` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资质内容',
       `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='入驻资质表';