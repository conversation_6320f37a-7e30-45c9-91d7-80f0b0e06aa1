# 商品分类和品牌映射同步定时任务配置说明

## 任务概述

**任务名称**: ProductMappingSyncJobService  
**任务Handler**: `productMappingSyncJobHandler`  
**功能描述**: 自动同步三方平台的分类和品牌与我方系统的映射关系

## 功能特性

### 1. 分类映射同步
- 同步三方末级分类（level=3且isLeaf=1）与我方二级分类的映射关系
- 支持智能匹配算法：精确匹配 → 模糊匹配 → 关键词匹配
- 自动构建分类路径信息
- 支持多渠道分类映射

### 2. 品牌映射同步
- 同步三方品牌与我方品牌的映射关系
- 支持品牌名称清理和标准化
- 智能匹配算法：精确匹配 → 大小写忽略匹配 → 模糊匹配 → 关键词匹配
- 支持多渠道品牌映射

### 3. 数据处理特性
- 自动创建新的映射关系
- 智能更新已存在的映射关系
- 支持事务回滚保证数据一致性
- 详细的执行日志记录

## 涉及的数据表

### 源数据表
- `third_party_category` - 三方平台商品分类
- `third_party_brand` - 三方平台商品品牌
- `sohu_product_category` - 我方商品分类
- `sohu_product_brand` - 我方商品品牌

### 映射表
- `product_category_mapping` - 分类映射关系表
- `product_brand_mapping` - 品牌映射关系表

## XXL-JOB 配置

### 1. 任务基本信息
```
任务描述: 商品分类和品牌映射同步
负责人: 系统管理员
报警邮件: <EMAIL>
```

### 2. 调度配置
```
调度类型: CRON
Cron表达式: 0 0 2 * * ?  (每天凌晨2点执行)
运行模式: BEAN
JobHandler: productMappingSyncJobHandler
```

### 3. 高级配置
```
路由策略: 第一个
子任务: 无
任务超时时间: 1800秒 (30分钟)
失败重试次数: 2
```

## 匹配算法说明

### 分类匹配规则
1. **精确匹配**: 三方分类名称与我方分类名称完全相同
2. **模糊匹配**: 分类名称存在包含关系
3. **关键词匹配**: 基于三方分类路径中的关键词进行匹配
4. **默认匹配**: 匹配到"其他"、"默认"、"通用"等默认分类

### 品牌匹配规则
1. **精确匹配**: 品牌名称完全相同
2. **忽略大小写匹配**: 忽略大小写的精确匹配
3. **模糊匹配**: 品牌名称存在包含关系
4. **清理后匹配**: 去除常见前后缀后进行匹配
5. **默认匹配**: 匹配到"其他品牌"、"默认品牌"等默认品牌

### 品牌名称清理规则
**去除后缀**: 旗舰店、官方店、专卖店、有限公司、集团、品牌等  
**去除前缀**: 正品、官方、原装、authentic等

## 执行流程

### 1. 分类映射同步流程
```
1. 获取所有三方末级分类 (level=3, isLeaf=1)
2. 获取我方所有二级分类 (pid!=0, isDel=false)
3. 获取现有分类映射关系
4. 按渠道分组处理三方分类
5. 为每个三方分类智能匹配我方分类
6. 创建新映射或更新现有映射
7. 记录执行结果
```

### 2. 品牌映射同步流程
```
1. 获取所有三方品牌 (isDeleted=0)
2. 获取我方所有品牌 (isDel=false, isShow=1)
3. 获取现有品牌映射关系
4. 按渠道分组处理三方品牌
5. 为每个三方品牌智能匹配我方品牌
6. 创建新映射或更新现有映射
7. 记录执行结果
```

## 监控和日志

### 1. 执行日志
- 任务启动和结束时间
- 处理的分类和品牌数量
- 新增和更新的映射数量
- 错误信息和异常堆栈

### 2. 关键指标
- 分类映射成功率
- 品牌映射成功率
- 任务执行时长
- 数据处理量

### 3. 告警机制
- 任务执行失败告警
- 执行时间超时告警
- 数据异常告警

## 注意事项

### 1. 数据依赖
- 确保三方分类和品牌数据已正确同步
- 确保我方分类和品牌数据完整
- 确保相关的Dubbo服务正常运行

### 2. 性能考虑
- 大数据量时可能需要分批处理
- 建议在业务低峰期执行
- 监控数据库连接池使用情况

### 3. 数据一致性
- 使用分布式事务保证数据一致性
- 失败时自动回滚所有变更
- 支持手动重新执行

### 4. 扩展性
- 支持新增渠道的映射
- 支持自定义匹配规则
- 支持配置默认映射策略

## 依赖服务

定时任务依赖以下Dubbo远程服务：

1. **RemoteProductCategoryMappingService** - 商品分类映射服务
2. **RemoteProductBrandMappingService** - 商品品牌映射服务
3. **RemoteThirdPartyCategoryService** - 三方分类服务
4. **RemoteThirdPartyBrandService** - 三方品牌服务
5. **RemoteProductCategoryService** - 我方分类服务
6. **RemoteProductBrandService** - 我方品牌服务

**注意：** 已创建相应的远程服务接口和实现类：
- API接口位于：`sohu-api/sohu-api-admin/src/main/java/com/sohu/admin/api/`
- 实现类位于：`sohu-admin/src/main/java/com/sohu/admin/dubbo/`

确保这些服务已正确配置并可用。

## 故障排查

### 1. 常见问题
- **Dubbo服务调用失败**: 检查服务提供者状态
- **数据库连接超时**: 检查数据库连接池配置
- **匹配率低**: 检查分类/品牌名称规范性

### 2. 日志查看
```bash
# 查看任务执行日志
tail -f /logs/sohu-job/info.log | grep ProductMappingSyncJobService

# 查看XXL-JOB调度日志
# 登录XXL-JOB管理界面查看任务执行历史
```

### 3. 手动执行
```java
// 可以通过调用服务方法手动执行
@Autowired
private ProductMappingSyncJobService productMappingSyncJobService;

// 手动执行任务
productMappingSyncJobService.productMappingSyncJobHandler();
```

## 版本历史

- **v1.0** (2025-07-10): 初始版本，支持基本的分类和品牌映射同步
- 后续版本将根据业务需求进行功能扩展和优化
