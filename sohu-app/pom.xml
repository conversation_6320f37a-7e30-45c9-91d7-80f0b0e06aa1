<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sohu</groupId>
        <artifactId>sohu-dependency</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sohu-app</artifactId>

    <description>
        sohu-app模块，瀑布流、项目、个人中心，广告等
    </description>

    <dependencies>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.9.0</version>
        </dependency>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- sohu Common Log -->
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-seata</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-shop-goods</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-shop-order</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-pay</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zwztf.sdk</groupId>
            <artifactId>wechat-pay</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-app</artifactId>
        </dependency>

        <!-- 翼码 -->
        <dependency>
            <groupId>com.wangcaio2o.ipossa</groupId>
            <artifactId>ym-sdk-java</artifactId>
            <version>1.0</version>
        </dependency>

        <!-- 微博 -->
        <dependency>
            <groupId>com.github.scribejava</groupId>
            <artifactId>scribejava-apis</artifactId>
            <version>8.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.github.scribejava</groupId>
            <artifactId>scribejava-core</artifactId>
            <version>8.0.0</version>
        </dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>com.zwztf.sdk</groupId>
            <artifactId>aliyun-airec</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-middle</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-entry</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-resource</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-admin</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-busy-order</artifactId>
        </dependency>

        <!--火山视频-->
        <dependency>
            <groupId>com.volcengine</groupId>
            <artifactId>volc-sdk-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zwztf.sdk</groupId>
            <artifactId>aliyun-ocr</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.zwztf.sdk</groupId>
            <artifactId>aliyun-audit</artifactId>
            <version>1.0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-pm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-idempotent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zwztf.sdk</groupId>
            <artifactId>ai-sdk</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.zwztf.sdk</groupId>
            <artifactId>yidun-sdk</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-encrypt</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
