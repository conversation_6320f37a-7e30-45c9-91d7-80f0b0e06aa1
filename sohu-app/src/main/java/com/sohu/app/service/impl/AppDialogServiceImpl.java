package com.sohu.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.app.service.AppDialogService;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuArticleBo;
import com.sohu.middle.api.bo.SohuVideoBo;
import com.sohu.middle.api.bo.ai.SohuDialogBo;
import com.sohu.middle.api.bo.ai.SohuDialogQueryBo;
import com.sohu.middle.api.bo.ai.SohuDialogRecordBo;
import com.sohu.middle.api.enums.ai.DialogConfigEnum;
import com.sohu.middle.api.service.RemoteDialogConfigService;
import com.sohu.middle.api.service.RemoteDialogService;
import com.sohu.middle.api.service.RemoteMiddleArticleService;
import com.sohu.middle.api.service.RemoteMiddleVideoService;
import com.sohu.middle.api.vo.SohuArticleVo;
import com.sohu.middle.api.vo.SohuVideoVo;
import com.sohu.middle.api.vo.ai.*;
import com.sohu.third.ai.request.AiSiliconFlowChatTextRequest;
import com.sohu.third.ai.response.AiSiliconFlowChatStreamTextResponse;
import com.sohu.third.ai.service.AiSiliconFlowChatService;
import com.sohu.third.aliyun.airec.constants.AliyunAirecConstant;
import com.sohu.third.aliyun.airec.enums.AirecGenderEnum;
import com.sohu.third.aliyun.airec.enums.AirecItemStatusEnum;
import com.sohu.third.aliyun.airec.enums.AirecUserIdTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;

import static java.lang.Thread.sleep;

/**
 * @Author: leibo
 * @Date: 2025/3/11 17:21
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class AppDialogServiceImpl implements AppDialogService {

    @DubboReference
    private RemoteDialogService remoteDialogService;
    @DubboReference
    private RemoteDialogConfigService remoteDialogConfigService;
    @DubboReference
    private RemoteMiddleArticleService remoteMiddleArticleService;
    @DubboReference
    private RemoteMiddleVideoService remoteMiddleVideoService;

    @Resource
    private AiSiliconFlowChatService aiSiliconFlowChatService;

    @Override
    public void dialog(SohuDialogQueryBo sohuDialogQueryBo, HttpServletResponse response,
                       String busyCode, Long userId, String userName) {
        // 处理前置文案拼接
        if (StringUtils.isEmpty(busyCode)) {
            busyCode = DialogConfigEnum.HSS.name();
        }
        SohuDialogConfigVo dialogConfigVo = remoteDialogConfigService.getByBusyType(busyCode);
        // 3、判断是否存在当前会话框,存在则需要拼接内容
        List<AiMessageVo> messageList = new ArrayList<>();
        Long dialogId = sohuDialogQueryBo.getDialogId();
        if (Objects.nonNull(dialogId)) {
            List<SohuDialogRecordVo> recordVoList = remoteDialogService.listRecord(dialogId, userId);
            messageList.addAll(this.buildAiMessage(recordVoList, dialogId, new Date()));
        } else {
            // 存储会话
            SohuDialogBo sohuDialog = new SohuDialogBo();
            sohuDialog.setDialogName(sohuDialogQueryBo.getMessage());
            sohuDialog.setUserId(userId);
            sohuDialog.setCreateBy(userName);
            sohuDialog.setUpdateBy(userName);
            sohuDialog.setDialogExt(busyCode);
            remoteDialogService.insertBo(sohuDialog);
            SohuDialogVo sohuDialogVo = remoteDialogService.getNearDialog(userId);
            dialogId = sohuDialogVo.getId();
        }
        // 存储记录
        SohuDialogRecordBo record = new SohuDialogRecordBo();
        record.setDialogId(dialogId);
        record.setModel("Pro/deepseek-ai/DeepSeek-V3");
        record.setUserId(userId);
        record.setQuestion(sohuDialogQueryBo.getMessage());
        record.setCreateBy(userName);
        record.setUpdateBy(userName);
        remoteDialogService.insertRecord(record);
        SohuDialogRecordVo recordVo = remoteDialogService.getNearDialogRecord(dialogId, userId);
        AiMessageVo questionMessage = new AiMessageVo();
        questionMessage.setRole("user");
        questionMessage.setContent(dialogConfigVo.getExtMessage() + sohuDialogQueryBo.getMessage());
        messageList.add(questionMessage);
        StreamingResponseBody streamingBody = this.generateMsg(messageList, recordVo.getId());
        try {
            streamingBody.writeTo(response.getOutputStream());
        } catch (IOException e) {
            log.error("流式写入失败", e);
        }
    }

    @Override
    public void regenerate(Long dialogId, Long recordId, HttpServletResponse response, String busyCode, Long userId, String userName) {
        SohuDialogRecordVo recordVo = new SohuDialogRecordVo();
        if (Objects.isNull(recordId)) {
            // 基于会话id查询最近的一条会话记录
            recordVo = remoteDialogService.getNearDialogRecord(dialogId, userId);
        } else {
            recordVo = remoteDialogService.recordInfo(recordId);
        }
        if (StringUtils.isEmpty(busyCode)) {
            busyCode = DialogConfigEnum.HSS.name();
        }
        SohuDialogConfigVo dialogConfigVo = remoteDialogConfigService.getByBusyType(busyCode);
        // 3、判断是否存在当前会话框,存在则需要拼接内容
        List<AiMessageVo> messageList = new ArrayList<>();
        List<SohuDialogRecordVo> recordVoList = remoteDialogService.listRecord(recordVo.getDialogId(), userId);
        messageList.addAll(this.buildAiMessage(recordVoList, recordVo.getId(), recordVo.getCreateTime()));
        AiMessageVo questionMessage = new AiMessageVo();
        questionMessage.setRole("user");
        questionMessage.setContent(dialogConfigVo.getExtMessage() + recordVo.getQuestion());
        messageList.add(questionMessage);
        StreamingResponseBody streamingBody = this.generateMsg(messageList, recordVo.getId());
        try {
            streamingBody.writeTo(response.getOutputStream());
        } catch (IOException e) {
            log.error("流式写入失败", e);
        }
    }

    @Override
    public List<SohuDialogVo> listByDialogName(String dialogName, String busyType) {
        return remoteDialogService.listByDialogName(dialogName, busyType);
    }

    @Override
    public List<AiDialogRecordVo> listRecord(Long dialogId) {
        List<SohuDialogRecordVo> recordList = remoteDialogService.listRecord(dialogId);
        List<AiDialogRecordVo> dialogRecordList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(recordList)) {
            for (SohuDialogRecordVo sohuDialogRecordVo : recordList) {
                dialogRecordList.add(BeanUtil.toBean(sohuDialogRecordVo, AiDialogRecordVo.class));
            }
        }
        return dialogRecordList;
    }

    @Override
    public AiDialogRecordVo nearRecord(Long dialogId) {
        return BeanUtil.toBean(remoteDialogService.getNearDialogRecord(dialogId, LoginHelper.getUserId()), AiDialogRecordVo.class);
    }

    @Override
    public AiAnalysisVo analysisRecord(Long recordId, String busyCode) {
        return remoteDialogService.analysisRecord(recordId, busyCode);
    }

    @Override
    public Boolean deleteByIds(List<Long> ids) {
        return remoteDialogService.deleteByIds(ids);
    }

    @Override
    public SohuDialogInfoVo getDialogHistoryInfo(String busyType) {
        return remoteDialogService.getDialogInfo(busyType);
    }

    /**
     * 处理流式访问
     *
     * @param messageList 上下文内容
     * @param recordId    记录id
     */
    private StreamingResponseBody generateMsg(List<AiMessageVo> messageList,
                                              Long recordId) {
        return outputStream -> {
            try {
                handleSSEStreamAsync(messageList, recordId, outputStream);
            } catch (Exception e) {
                log.error("SSE流处理异常", e);
            } finally {
                try {
                    outputStream.flush();
                } catch (IOException e) {
                    log.error("刷新流失败", e);
                }
            }
        };
    }

    /**
     * SSE处理
     *
     * @param messageList
     * @param recordId
     * @param outputStream
     * @throws Exception
     */
    public void handleSSEStreamAsync(List<AiMessageVo> messageList,
                                     Long recordId, OutputStream outputStream) {
        // 构建请求对象
        AiSiliconFlowChatTextRequest request = buildChatRequest(messageList);
        Long startTime = System.currentTimeMillis();
        // 发送HTTP请求获取SSE流
        HttpResponse response = aiSiliconFlowChatService.chatStreamText(request);
        SohuDialogRecordVo record = remoteDialogService.recordInfo(recordId);
        try (InputStream inputStream = response.bodyStream();
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            // 处理流式数据
            String line;
            // 回复内容
            StringBuffer stringBuffer = new StringBuffer();
            // 思考内容
            StringBuffer thinkStringBuffer = new StringBuffer();
            // 上行token数量
            Integer promptToken = 0;
            // 下行token数量
            Integer completionToken = 0;
            while ((line = reader.readLine()) != null) {
                // 使用正则去掉 "data:" 前缀
                line = line.replaceFirst("^data:\\s*", "");
                // 如果是 "none" 或 "NONE"，跳过
                if (StrUtil.isBlankIfStr(line)) {
                    continue;
                }
                log.info("打印日志,获取信息:{}", line);
                try {
                    AiSiliconFlowChatStreamTextResponse streamTextResponse = JSONObject.parseObject(line, AiSiliconFlowChatStreamTextResponse.class);
                    AiSiliconFlowChatStreamTextResponse.UsageDTO usage = streamTextResponse.getUsage();
                    if (Objects.nonNull(usage)) {
                        promptToken = usage.getPromptTokens();
                        completionToken = usage.getCompletionTokens();
                    }
                    AiSiliconFlowChatStreamTextResponse.ChoicesDTO choicesDTO = streamTextResponse.getChoices().get(0);
                    AiSiliconFlowChatStreamTextResponse.ChoicesDTO.DeltaDTO delta = choicesDTO.getDelta();
                    if (Objects.nonNull(delta.getReasoningContent())) {
                        thinkStringBuffer = thinkStringBuffer.append(delta.getReasoningContent());
                    }
                    if (Objects.nonNull(delta.getContent())) {
                        Map<String, String> resultMap = new HashMap<>();
                        resultMap.put("dialogId", record.getDialogId().toString());
                        resultMap.put("dialogRecordId", record.getId().toString());
                        String content = delta.getContent().toString().replaceAll("^\\n{2}", "");
                        if (StringUtils.isNotEmpty(content)) {
                            // 返回客户端
                            resultMap.put("content", content);
                            String result = "data:" + JSONObject.toJSONString(resultMap) + "\n\n";
                            log.info("发送内容:{}", result);
                            outputStream.write(result.getBytes());
                            outputStream.flush();
                            stringBuffer = stringBuffer.append(delta.getContent());
                            // 防止前端接收流顺序异常
                            sleep(100);
                        }
                    }
                } catch (JSONException jsonException) {
                    log.error("已结束，需要关闭流");
                    break;
                } catch (IOException ioException) {
                    log.error("输出流关闭");
                    break;
                }
            }
            // 处理组装构建 record
            record.setThinkValue(thinkStringBuffer.toString());
            record.setAnswer(stringBuffer.toString());
            record.setPromptToken(promptToken);
            record.setCompletionToken(completionToken);
            record.setTotalToken(promptToken + completionToken);
        } catch (Exception e) {
            log.error("Error reading stream response", e);
        } finally {
            try {
                outputStream.flush();
            } catch (IOException e) {
                log.error("关闭流失败", e);
            }
            // 编辑数据库
            Long endTime = System.currentTimeMillis();
            record.setTotalDuration((int) (endTime - startTime) / 100);
            remoteDialogService.updateRecord(record);
        }
    }

    /**
     * 构建对象
     *
     * @param messageList
     * @return
     */
    private AiSiliconFlowChatTextRequest buildChatRequest(List<AiMessageVo> messageList) {
        // 构建请求逻辑（与原有代码相同）
        AiSiliconFlowChatTextRequest request = new AiSiliconFlowChatTextRequest();
        request.setModel("Pro/deepseek-ai/DeepSeek-V3");
        request.setMessages(this.buildMessageDTO(messageList));
        request.setStream(true);
        return request;
    }

    /**
     * 组装构建三方访问数据
     *
     * @param messageList
     * @return
     */
    private List<AiSiliconFlowChatTextRequest.MessagesDTO> buildMessageDTO(List<AiMessageVo> messageList) {
        List<AiSiliconFlowChatTextRequest.MessagesDTO> messagesDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(messageList)) {
            for (AiMessageVo aiMessageVo : messageList) {
                AiSiliconFlowChatTextRequest.MessagesDTO messagesDTO = new AiSiliconFlowChatTextRequest.MessagesDTO();
                messagesDTO.setRole(aiMessageVo.getRole());
                messagesDTO.setContent(aiMessageVo.getContent());
                messagesDTOList.add(messagesDTO);
            }
        }
        return messagesDTOList;
    }

    /**
     * 组装构建ai访问数据
     *
     * @param recordVoList
     * @param recordId
     * @param time
     * @return
     */
    private List<AiMessageVo> buildAiMessage(List<SohuDialogRecordVo> recordVoList, Long recordId, Date time) {
        List<AiMessageVo> messageList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(recordVoList)) {
            for (SohuDialogRecordVo dialogRecordVo : recordVoList) {
                if (!dialogRecordVo.getId().equals(recordId) && dialogRecordVo.getCreateTime().before(time)) {
                    AiMessageVo questionMessage = new AiMessageVo();
                    questionMessage.setRole("user");
                    questionMessage.setContent(dialogRecordVo.getQuestion());
                    messageList.add(questionMessage);
                    AiMessageVo answerMessage = new AiMessageVo();
                    answerMessage.setRole("assistant");
                    answerMessage.setContent(dialogRecordVo.getAnswer());
                    messageList.add(answerMessage);
                }
            }
        }
        return messageList;
    }

    @Override
    public List<SohuCommonContentVo> getCommonContentListByCategoryId(Long recordId) {
        AiAnalysisVo aiAnalysisVo = this.analysisRecord(recordId, DialogConfigEnum.TASK.name());
        List<SohuCommonContentVo> sohuCommonContentVos = new ArrayList<>();
        if (aiAnalysisVo == null || aiAnalysisVo.getBusyTaskVo() == null) {
            return sohuCommonContentVos;
        }
        Long categoryId = aiAnalysisVo.getBusyTaskVo().getCategoryId();
        if (categoryId == null) {
            return sohuCommonContentVos;
        }

        // 获取智能推荐图文
        SohuArticleBo bo = new SohuArticleBo();
        bo.setCategoryId(categoryId);
        bo.setAiRec(true);
        bo.setAiRecSceneId(AliyunAirecConstant.SCENE_ARTICLE_ALL);
        bo.setAiReturnCount(Constants.ONE);
        bo.setState(CommonState.OnShelf.getCode());
        TableDataInfo<SohuArticleVo> sohuArticleVoTableDataInfo = remoteMiddleArticleService.queryPageOfAirec(bo, new PageQuery(1, 1));
        if (CollectionUtils.isEmpty(sohuArticleVoTableDataInfo.getData())) {
            bo.setCategoryId(null);
            sohuArticleVoTableDataInfo = remoteMiddleArticleService.queryPageOfAirec(bo, new PageQuery(1, 1));
        }
        if (CollectionUtils.isNotEmpty(sohuArticleVoTableDataInfo.getData())) {
            SohuArticleVo articleVo = sohuArticleVoTableDataInfo.getData().get(0);
            SohuCommonContentVo sohuCommonContentVo = new SohuCommonContentVo();
            sohuCommonContentVo.setCommonTitle(articleVo.getTitle());
            sohuCommonContentVo.setCommonCover(articleVo.getCoverImage());
            sohuCommonContentVo.setCommonCode(articleVo.getId().toString());
            sohuCommonContentVo.setCommonType(BusyType.Article.getType());
            sohuCommonContentVos.add(sohuCommonContentVo);
        }

        // 获取智能推荐视频
        SohuVideoBo videoBo = new SohuVideoBo();
        videoBo.setCategoryId(categoryId);
        videoBo.setAiRec(true);
        videoBo.setAiRecSceneId(AliyunAirecConstant.SCENE_VIDEO_ALL);
        videoBo.setAiReturnCount(Constants.ONE);
        TableDataInfo<SohuVideoVo> sohuVideoVoTableDataInfo = remoteMiddleVideoService.queryPageOfAirec(videoBo, new PageQuery(1, 1));
        if (CollectionUtils.isEmpty(sohuVideoVoTableDataInfo.getData())) {
            videoBo.setCategoryId(null);
            sohuVideoVoTableDataInfo = remoteMiddleVideoService.queryPageOfAirec(videoBo, new PageQuery(1, 1));
        }
        if (CollectionUtils.isNotEmpty(sohuVideoVoTableDataInfo.getData())) {
            SohuVideoVo videoVo = sohuVideoVoTableDataInfo.getData().get(0);
            SohuCommonContentVo sohuCommonContentVo = new SohuCommonContentVo();
            sohuCommonContentVo.setCommonTitle(videoVo.getTitle());
            sohuCommonContentVo.setCommonCover(videoVo.getCoverImage());
            sohuCommonContentVo.setCommonCode(videoVo.getId().toString());
            sohuCommonContentVo.setCommonType(BusyType.Video.getType());
            sohuCommonContentVos.add(sohuCommonContentVo);
        }

        return sohuCommonContentVos;
    }
}
