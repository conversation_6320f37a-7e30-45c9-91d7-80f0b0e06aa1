package com.sohu.app.controller.api;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.utils.JsonUtils;
import com.sohu.common.core.utils.ValidatorUtil;
import com.sohu.common.encrypt.annotation.SkipApiEncrypt;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.common.security.handler.ExceptionMsgBot;
import com.sohu.middle.api.bo.SohuVideoBo;
import com.sohu.middle.api.bo.SohuVideoImportVo;
import com.sohu.middle.api.service.RemoteMiddleService;
import com.sohu.middle.api.service.RemoteMiddleTestService;
import com.sohu.middle.api.vo.SohuVideoVo;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 测试类
 */
@Slf4j
@Hidden
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/test")
public class AppApiTestController {
    @DubboReference
    private RemoteMiddleTestService remoteMiddleTestService;

    @Resource
    private ExceptionMsgBot exceptionMsgBot;
    @DubboReference
    private RemoteMiddleService<SohuVideoBo, SohuVideoVo> remoteMiddleService;

    /**
     * 查询数据
     */
    @GetMapping("/busyQuery")
    @SkipApiEncrypt
    public R busyQuery(@RequestParam("v") String v,
                       @RequestParam("busyType") String busyType,
                       @RequestParam("busyCode") Long busyCode) {
        ValidatorUtil.apiCheckPassword(v);
        return R.ok(remoteMiddleTestService.busyQuery(busyType, busyCode));
    }


    /**
     * 设置流水号的唯一标识（没有标识的就设置）
     */
    @GetMapping("/setTradeRecordUnq")
    @SkipApiEncrypt
    public R setTradeRecordUnq(@RequestParam("v") String v) {
        ValidatorUtil.apiCheckPassword(v);
        return R.ok(remoteMiddleTestService.setTradeRecordUnq());
    }

    /**
     * 设置虚拟币数值
     */
    @GetMapping("/setTradeRecordVirtualCoin")
    @SkipApiEncrypt
    public R setTradeRecordVirtualCoin(@RequestParam("v") String v, @RequestParam(name = "userId", required = false) Long userId) {
        ValidatorUtil.apiCheckPassword(v);
        return R.ok(remoteMiddleTestService.setTradeRecordVirtualCoin(userId));
    }

    /**
     * 同步视频至万能表数据
     */
    @GetMapping("/syncVideoToContent")
    @SkipApiEncrypt
    public R syncVideoToContent(@RequestParam("v") String v, @RequestParam(name = "userId", required = false) Long userId) {
        ValidatorUtil.apiCheckPassword(v);
        return R.ok(remoteMiddleTestService.syncVideoToContent(userId));
    }

    /**
     * 同步图文至万能表数据
     */
    @GetMapping("/syncArticleToContent")
    @SkipApiEncrypt
    public R syncArticleToContent(@RequestParam("v") String v, @RequestParam(name = "userId", required = false) Long userId) {
        ValidatorUtil.apiCheckPassword(v);
        return R.ok(remoteMiddleTestService.syncArticleToContent(userId));
    }

    /**
     * 设置文章的国家站点值
     */
    @GetMapping("/setArticleCountrySite")
    @SkipApiEncrypt
    public R setArticleCountrySite(@RequestParam("v") String v, @RequestParam(name = "userId", required = false) Long userId) {
        ValidatorUtil.apiCheckPassword(v);
        return R.ok(remoteMiddleTestService.setArticleCountrySite(userId));
    }

    /**
     * 设置视频的国家站点值
     */
    @GetMapping("/setVideoCountrySite")
    @SkipApiEncrypt
    public R setVideoCountrySite(@RequestParam("v") String v, @RequestParam(name = "userId", required = false) Long userId) {
        ValidatorUtil.apiCheckPassword(v);
        return R.ok(remoteMiddleTestService.setVideoCountrySite(userId));
    }

    /**
     * 设置问题的国家站点值
     */
    @GetMapping("/setQuestionCountrySite")
    @SkipApiEncrypt
    public R setQuestionCountrySite(@RequestParam("v") String v, @RequestParam(name = "userId", required = false) Long userId) {
        ValidatorUtil.apiCheckPassword(v);
        return R.ok(remoteMiddleTestService.setQuestionCountrySite(userId));
    }

    /**
     * 同步一级分类至商品表
     */
    @GetMapping("/syncFirstLevelToProduct")
    @SkipApiEncrypt
    public R syncFirstLevelToProduct(@RequestParam("v") String v) {
        ValidatorUtil.apiCheckPassword(v);
        return R.ok(remoteMiddleTestService.syncFirstLevelToProduct());
    }

    /**
     * 查询支付结果
     */
    @GetMapping("/queryPayOrder")
    @SkipApiEncrypt
    public R queryPayOrder(@RequestParam("v") String v, @RequestParam String masterOrderNo) {
        ValidatorUtil.apiCheckPassword(v);
        return R.ok(remoteMiddleTestService.queryPayOrder(masterOrderNo));
    }

    /**
     * 退款
     */
    @GetMapping("/refund")
    @SkipApiEncrypt
    public R refundPayOrder(@RequestParam("v") String v, @RequestParam String masterOrderNo) {
        ValidatorUtil.apiCheckPassword(v);
        return R.ok(remoteMiddleTestService.refundPayOrder(masterOrderNo));
    }

    /**
     * 建测试群
     */
    @GetMapping("/create/group")
    @SkipApiEncrypt
    public R createGroup(@RequestParam("v") String v, @RequestParam Long groupHeaderId) {
        ValidatorUtil.apiCheckPassword(v);
        return R.ok(remoteMiddleTestService.createGroup(groupHeaderId));
    }

    /**
     * 纠正评论数据
     */
    @GetMapping("/handle/comment")
    @SkipApiEncrypt
    public R handleComment(@RequestParam("v") String v, @RequestParam(required = false, defaultValue = "0") Long id) {
        ValidatorUtil.apiCheckPassword(v);
        return R.ok(remoteMiddleTestService.handleComment(id));
    }


    /**
     * 纠正评论数据
     */
    @GetMapping("/aliyun/scan")
    @SkipApiEncrypt
    public R aliyunScan(@RequestParam("v") String v, @RequestParam(required = false, defaultValue = "0") String text) {
        ValidatorUtil.apiCheckPassword(v);
        return R.ok(remoteMiddleTestService.aliyunScan(text));
    }

    /**
     * 主动抛异常
     */
    @GetMapping("/throw/error")
    @SkipApiEncrypt
    public R throwError(@RequestParam("v") String v, @RequestParam(required = false) String text, HttpServletRequest request) {
        ValidatorUtil.apiCheckPassword(v);
        return R.ok(remoteMiddleTestService.throwError(text));
    }

    /**
     * 刷新用户统计数据
     */
    @GetMapping("/refreshUserStat")
    @SkipApiEncrypt
    public R refreshUserStat() {
        StpUtil.checkLogin();
        if (LoginHelper.isAdmin()) {
            remoteMiddleTestService.refreshUserStat();
        }
        return R.ok();
    }


    /**
     * 刷新用户统计数据
     */
    @GetMapping("/clear/cache")
    @SkipApiEncrypt
    public R clearCache(@RequestParam("v") String v, @RequestParam(name = "region") String region,
                        @RequestParam(required = false, name = "key") String key) {
        ValidatorUtil.apiCheckPassword(v);
        remoteMiddleTestService.clearCache(region, key);
        return R.ok();
    }

    /**
     * OA打卡
     */
    @GetMapping("/oa/punch")
    @SkipApiEncrypt
    public R oaPunch(@RequestParam("v") String v, @RequestParam(name = "name", required = false) String name,
                     @RequestParam(name = "password", required = false) String password) {
        ValidatorUtil.apiCheckPassword(v);
        remoteMiddleTestService.oaPunch(name, password);
        return R.ok();
    }

    /**
     * 处理视频比例
     */
    @GetMapping("/video/aspect/ratio")
    @SkipApiEncrypt
    public R videoAspectRatio() {
        remoteMiddleTestService.handleVideoAspectRatio();
        return R.ok();
    }


    /**
     * 导入视频列表
     *
     * @param file 导入文件
     */
    @SkipApiEncrypt
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R importData(MultipartFile file) throws Exception {
        List<SohuVideoImportVo> sohuVideoBos = ExcelUtil.importExcel(file.getInputStream(), SohuVideoImportVo.class);
        if (CollectionUtils.isNotEmpty(sohuVideoBos)) {
            for (SohuVideoImportVo videoImportVo : sohuVideoBos) {
                SohuVideoBo videoBo = new SohuVideoBo();
                try {
                    videoBo.setSiteId(Long.valueOf(videoImportVo.getSiteId()));
                    videoBo.setCategoryId(Long.valueOf(videoImportVo.getCategoryId()));
                    videoBo.setType(videoImportVo.getType());
                    videoBo.setTitle(videoImportVo.getTitle());
                    videoBo.setCoverImage("https://sohugloba.oss-cn-beijing.aliyuncs.com/video/health%20/wyfm" + videoImportVo.getCategory() + videoImportVo.getCoverImage());
                    videoBo.setVideoUrl("https://sohugloba.oss-cn-beijing.aliyuncs.com/video/health%20/wysp" + videoImportVo.getCategory() + videoImportVo.getVideoUrl());
                    remoteMiddleService.add(videoBo);
                } catch (Exception e) {
                    log.error("视频信息导入异常,未成功导入的视频对象为 videoBo:{}", JsonUtils.toJsonString(videoBo));
                }
            }
        }
        return R.ok();
    }

}
