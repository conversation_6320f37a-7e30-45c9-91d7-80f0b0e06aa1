package com.sohu.app.controller.risk;

import com.alibaba.fastjson.JSONObject;
import com.sohu.common.core.domain.R;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.risk.RiskSyncCheckBo;
import com.sohu.middle.api.service.RemoteRiskService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 风险检测
 *
 * <AUTHOR>
 * @date 2025/6/17 9:31
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/risk")
public class RiskController {

    @DubboReference
    private RemoteRiskService remoteRiskService;

    @Operation(summary = "同步检测", description = "同步检测")
    @PostMapping("/syncCheck")
    public R<Boolean> syncCheck(@RequestBody RiskSyncCheckBo riskSyncCheckBo) {
//        if (riskSyncCheckBo.getContent().equals("admin")) {
//            return R.ok(Boolean.FALSE);
//        }
//        return R.ok(Boolean.TRUE);
        riskSyncCheckBo.setBusyCode(String.valueOf(LoginHelper.getUserId()));
        return R.ok(remoteRiskService.syncCheck(riskSyncCheckBo));
    }

    @Operation(summary = "异步检测", description = "异步检测")
    @PostMapping("/asyncCheck")
    public R asyncCheck(@RequestBody RiskSyncCheckBo riskSyncCheckBo) {
        riskSyncCheckBo.setBusyCode(String.valueOf(LoginHelper.getUserId()));
        remoteRiskService.asyncCheck(riskSyncCheckBo);
        return R.ok();
    }

    @Operation(summary = "检测回调", description = "检测回调")
    @PostMapping("/callback")
    public String callback(@RequestParam("callbackData") String callbackData) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", 200);
        jsonObject.put("msg", "接收成功");
        return JSONObject.toJSONString(jsonObject);
    }
}
