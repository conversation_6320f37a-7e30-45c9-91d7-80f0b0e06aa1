package com.sohu.app.controller;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuShopNoticeBo;
import com.sohu.middle.api.bo.notice.SohuInteractNoticeBo;
import com.sohu.middle.api.bo.notice.SohuSystemNoticeBo;
import com.sohu.middle.api.bo.notice.SohuTaskNoticeBo;
import com.sohu.middle.api.bo.notice.SohuWalletNoticeBo;
import com.sohu.middle.api.service.notice.*;
import com.sohu.middle.api.vo.SohuOuterTabListVo;
import com.sohu.middle.api.vo.SohuShopNoticeVo;
import com.sohu.middle.api.vo.notice.*;
import com.sohu.middle.api.service.notice.RemoteMiddleMcnNoticeService;
import com.sohu.middle.api.bo.SohuMcnNoticeBo;
import com.sohu.middle.api.vo.notice.SohuMcnNoticeVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * APP-通知
 * 前端访问路由地址为:/app/notice
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/notice")
public class AppNoticeController extends BaseController {

    @DubboReference
    private RemoteMiddleSystemNoticeService remoteMiddleSystemNoticeService;
    @DubboReference
    private RemoteMiddleInteractNoticeService remoteMiddleInteractNoticeService;
    @DubboReference
    private RemoteMiddleOuterNoticeService remoteMiddleOuterNoticeService;
    @DubboReference
    private RemoteMiddleShopNoticeService remoteMiddleShopNoticeService;
    @DubboReference
    private RemoteMiddleWalletNoticeService remoteMiddleWalletNoticeService;
    @DubboReference
    private RemoteMiddleTaskNoticeService remoteMiddleTaskNoticeService;
    @DubboReference
    private RemoteMiddleMcnNoticeService remoteMiddleMcnNoticeService;

    /**
     * 外层通知消息列表
     */
    @GetMapping("/outer/list")
    public R<List<SohuOuterNoticeVo>> list() {
        return R.ok(remoteMiddleOuterNoticeService.queryList());
    }

    /**
     * 通知栏tab列表,返回每个类型的消息未读数
     */
    @GetMapping("/tab/list")
    public R<List<SohuOuterTabListVo>> tabList() {
        return R.ok(remoteMiddleOuterNoticeService.tabList());
    }

    /**
     * 系统通知分页列表
     */
    @GetMapping("/system/page")
    public TableDataInfo<SohuSystemNoticeVo> noticePage(SohuSystemNoticeBo bo, PageQuery pageQuery) {
        bo.setReceiverId(LoginHelper.getUserId());
        return remoteMiddleSystemNoticeService.queryPageList(bo, pageQuery);
    }

    /**
     * 互动通知分页列表
     */
    @GetMapping("/interact/page")
    public TableDataInfo<SohuInteractNoticeVo> list(SohuInteractNoticeBo bo, PageQuery pageQuery) {
        return remoteMiddleInteractNoticeService.queryPageList(bo, pageQuery);
    }

    /**
     * 商城通知分页列表
     */
    @GetMapping("/shop/page")
    public TableDataInfo<SohuShopNoticeVo> shopPage(SohuShopNoticeBo bo, PageQuery pageQuery) {
        bo.setReceiverId(LoginHelper.getUserId());
        return remoteMiddleShopNoticeService.queryPageList(bo, pageQuery);
    }

    /**
     * 钱包通知分页列表
     */
    @GetMapping("/wallet/page")
    public TableDataInfo<SohuWalletNoticeVo> walletPage(SohuWalletNoticeBo bo, PageQuery pageQuery) {
        bo.setReceiverId(LoginHelper.getUserId());
        return remoteMiddleWalletNoticeService.queryPageList(bo, pageQuery);
    }

    /**
     * 任务通知
     */
    @GetMapping("/task/page")
    public TableDataInfo<SohuTaskNoticeVo> taskPage(SohuTaskNoticeBo bo, PageQuery pageQuery) {
        bo.setReceiverId(LoginHelper.getUserId());
        return remoteMiddleTaskNoticeService.queryPageList(bo, pageQuery);
    }

    /**
     * MCN通知
     */
    @GetMapping("/mcn/page")
    public TableDataInfo<SohuMcnNoticeVo> mcnPage(SohuMcnNoticeBo bo, PageQuery pageQuery) {
        bo.setReceiverId(LoginHelper.getUserId());
        return remoteMiddleMcnNoticeService.queryPageList(bo, pageQuery);
    }
}
