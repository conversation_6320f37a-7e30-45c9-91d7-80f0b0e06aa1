package com.sohu.app.controller.api;

import com.alibaba.fastjson.JSONObject;
import com.netease.yidun.sdk.antispam.audio.callback.v4.request.AudioV4ActiveCallbackRequest;
import com.netease.yidun.sdk.antispam.audio.callback.v4.response.AudioCallbackV4Result;
import com.netease.yidun.sdk.antispam.callback.ActiveCallbackResp;
import com.netease.yidun.sdk.antispam.crawler.v3.callback.request.CrawlerResourceActiveCallbackRequestV3;
import com.netease.yidun.sdk.antispam.crawler.v3.callback.response.CrawlerResourceCallbackV3Response;
import com.netease.yidun.sdk.antispam.file.v2.callback.request.FileActiveCallbackRequestV2;
import com.netease.yidun.sdk.antispam.file.v2.callback.response.FileCallbackV2Response;
import com.netease.yidun.sdk.antispam.image.v5.callback.request.ImageV5ActiveCallbackRequest;
import com.netease.yidun.sdk.antispam.image.v5.check.sync.response.ImageV5Result;
import com.netease.yidun.sdk.antispam.text.v5.callback.request.TextV5ActiveCallbackRequest;
import com.netease.yidun.sdk.antispam.text.v5.check.sync.single.TextCheckResult;
import com.netease.yidun.sdk.antispam.video.callback.v4.request.VideoV4ActiveCallbackRequest;
import com.netease.yidun.sdk.antispam.video.callback.v4.response.VideoCallbackV4Result;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.encrypt.annotation.SkipApiEncrypt;
import com.sohu.middle.api.service.RemoteRiskService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025/6/18 15:22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/risk/callback")
@Slf4j
public class RiskCallBackController {

    @DubboReference
    private RemoteRiskService remoteRiskService;
    private final AsyncConfig asyncConfig;

    @Operation(summary = "文本回调", description = "文本回调")
    @RequestMapping("/text")
    @SkipApiEncrypt
    public ActiveCallbackResp receiveCallback(HttpServletRequest request, TextV5ActiveCallbackRequest callbackResp) {
        log.info("文本回调={}", JSONObject.toJSONString(callbackResp));
        try {
            TextCheckResult textCheckResult = callbackResp.parseTextCallbackData();
            log.info("文本回调结果={}", JSONObject.toJSONString(textCheckResult));
            //异步处理业务逻辑
            CompletableFuture.supplyAsync(() -> {
                try {
                    Thread.sleep(100L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                String dataId = textCheckResult.getAntispam().getDataId();
                Integer status = textCheckResult.getAntispam().getSuggestion();
                String riskDescription = textCheckResult.getAntispam().getRiskDescription();
                remoteRiskService.handleCallback(dataId, status, riskDescription);
                return true;
            }, asyncConfig.getAsyncExecutor());
            return ActiveCallbackResp.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return ActiveCallbackResp.fail(ActiveCallbackResp.SERVER_ERROR);
        }
    }

    @Operation(summary = "图片回调", description = "图片回调")
    @RequestMapping("/image")
    @SkipApiEncrypt
    public ActiveCallbackResp receiveCallback(HttpServletRequest request, ImageV5ActiveCallbackRequest callbackResp) {
        log.info("图片回调={}", JSONObject.toJSONString(callbackResp));
        try {
            ImageV5Result imageV5Result = callbackResp.parseImageCallbackData();
            log.info("图片回调结果={}", JSONObject.toJSONString(imageV5Result));
            //异步处理业务逻辑
            CompletableFuture.supplyAsync(() -> {
                try {
                    Thread.sleep(100L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                String dataId = imageV5Result.getAntispam().getDataId();
                Integer status = Constants.TWO;
                if (imageV5Result.getAntispam().getStatus() == Constants.TWO){
                    status = imageV5Result.getAntispam().getSuggestion();
                }
                String riskDescription = imageV5Result.getAntispam().getRiskDescription();
                remoteRiskService.handleCallback(dataId, status, riskDescription);
                return true;
            }, asyncConfig.getAsyncExecutor());
            return ActiveCallbackResp.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return ActiveCallbackResp.fail(ActiveCallbackResp.SERVER_ERROR);
        }
    }

    @Operation(summary = "音频回调", description = "音频回调")
    @RequestMapping("/audio")
    @SkipApiEncrypt
    public ActiveCallbackResp receiveCallback(HttpServletRequest request, AudioV4ActiveCallbackRequest callbackResp) {
        log.info("音频回调={}", JSONObject.toJSONString(callbackResp));
        try {
            AudioCallbackV4Result audioCallbackV4Result = callbackResp.parseCallbackData();
            log.info("音频回调结果={}", JSONObject.toJSONString(audioCallbackV4Result));
            //异步处理业务逻辑
            CompletableFuture.supplyAsync(() -> {
                try {
                    Thread.sleep(100L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                String dataId = audioCallbackV4Result.getAntispam().getDataId();
                Integer status =  Constants.TWO;
                String riskDescription = audioCallbackV4Result.getAntispam().getRiskDescription();
                if (audioCallbackV4Result.getAntispam().getStatus() == Constants.TWO){
                    status = audioCallbackV4Result.getAntispam().getSuggestion();
                }
                if (audioCallbackV4Result.getAntispam().getStatus() == Constants.THRID){
                    riskDescription = "检测失败";
                }
                remoteRiskService.handleCallback(dataId, status, riskDescription);
                return true;
            }, asyncConfig.getAsyncExecutor());
            return ActiveCallbackResp.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return ActiveCallbackResp.fail(ActiveCallbackResp.SERVER_ERROR);
        }
    }

    @Operation(summary = "视频回调", description = "视频回调")
    @RequestMapping("/video")
    @SkipApiEncrypt
    public ActiveCallbackResp receiveCallback(HttpServletRequest request, VideoV4ActiveCallbackRequest callbackResp) {
        log.info("视频回调={}", JSONObject.toJSONString(callbackResp));
        try {
            VideoCallbackV4Result videoCallbackV4Result = callbackResp.parseCallbackData();
            log.info("视频回调结果={}", JSONObject.toJSONString(videoCallbackV4Result));
            //异步处理业务逻辑
            CompletableFuture.supplyAsync(() -> {
                try {
                    Thread.sleep(100L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                String dataId = videoCallbackV4Result.getAntispam().getDataId();
                Integer status = Constants.TWO;
                String riskDescription = videoCallbackV4Result.getAntispam().getRiskDescription();
                if (videoCallbackV4Result.getAntispam().getStatus() == Constants.TWO){
                    status = videoCallbackV4Result.getAntispam().getSuggestion();
                }
                if (videoCallbackV4Result.getAntispam().getStatus() == Constants.THRID){
                    riskDescription = "检测失败";
                }
                remoteRiskService.handleCallback(dataId, status, riskDescription);
                return true;
            }, asyncConfig.getAsyncExecutor());
            return ActiveCallbackResp.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return ActiveCallbackResp.fail(ActiveCallbackResp.SERVER_ERROR);
        }
    }

    @Operation(summary = "链接回调", description = "链接回调")
    @RequestMapping("/link")
    @SkipApiEncrypt
    public ActiveCallbackResp receiveCallback(HttpServletRequest request, CrawlerResourceActiveCallbackRequestV3 callbackResp) {
        log.info("链接回调={}", JSONObject.toJSONString(callbackResp));
        try {
            CrawlerResourceCallbackV3Response.CrawlerResourceResult activeCallbackResult = callbackResp.parseCrawlerResourceCallbackData();
            log.info("链接回调结果={}", JSONObject.toJSONString(activeCallbackResult));
            //异步处理业务逻辑
            CompletableFuture.supplyAsync(() -> {
                try {
                    Thread.sleep(100L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                String dataId = activeCallbackResult.getAntispam().getDataId();
                Integer checkStatus = activeCallbackResult.getAntispam().getCheckStatus();
                Integer status = Constants.TWO;
                String riskDescription = "";
                if (checkStatus == Constants.TWO){
                    status = activeCallbackResult.getAntispam().getSuggestion();
                }else {
                    riskDescription = "内容不合规";
                }
                remoteRiskService.handleCallback(dataId, status, riskDescription);
                return true;
            }, asyncConfig.getAsyncExecutor());
            return ActiveCallbackResp.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return ActiveCallbackResp.fail(ActiveCallbackResp.SERVER_ERROR);
        }
    }

    @Operation(summary = "文件回调", description = "文件回调")
    @RequestMapping("/file")
    @SkipApiEncrypt
    public ActiveCallbackResp receiveCallback(HttpServletRequest request, FileActiveCallbackRequestV2 callbackResp) {
        log.info("文件回调={}", JSONObject.toJSONString(callbackResp));
        try {
            FileCallbackV2Response.FileCallbackV2Resp activeCallbackResult = callbackResp.parseFileCallbackData();
            log.info("文件回调结果={}", JSONObject.toJSONString(activeCallbackResult));
            //异步处理业务逻辑
            CompletableFuture.supplyAsync(() -> {
                try {
                    Thread.sleep(100L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                String dataId = activeCallbackResult.getAntispam().getDataId();
                Integer status = activeCallbackResult.getAntispam().getSuggestion();
                String riskDescription = activeCallbackResult.getAntispam().getRiskDescription();
                remoteRiskService.handleCallback(dataId, status, riskDescription);
                return true;
            }, asyncConfig.getAsyncExecutor());
            return ActiveCallbackResp.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return ActiveCallbackResp.fail(ActiveCallbackResp.SERVER_ERROR);
        }
    }
}
