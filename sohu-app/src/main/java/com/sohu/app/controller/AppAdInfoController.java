package com.sohu.app.controller;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.middle.api.service.RemoteMiddleAdInfoService;
import com.sohu.middle.api.vo.SohuAdInfoVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 广告
 * 前端访问路由地址为:/app/advertise
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/advertise")
public class AppAdInfoController extends BaseController {

    @DubboReference
    private RemoteMiddleAdInfoService remoteMiddleAdInfoService;

    /**
     * 随机获取广告
     *
     * @param excludeIds 需要排除的广告ID列表
     * @return 随机广告信息
     */
    @GetMapping("/random")
    public R<SohuAdInfoVo> getRandomAd(@RequestParam(required = false) List<Long> excludeIds,
                                       @RequestParam(required = false)  String adPlaceCode) {
        return R.ok(remoteMiddleAdInfoService.getRandomAd(excludeIds, adPlaceCode));
    }
}
