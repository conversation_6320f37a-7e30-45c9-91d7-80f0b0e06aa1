package com.sohu.app.controller.api;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.sohu.app.service.AppArticleService;
import com.sohu.app.service.AppLiteratureService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.encrypt.annotation.SkipApiEncrypt;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.aspect.UserBehavior;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.enums.behavior.BehaviorBusinessTypeEnum;
import com.sohu.middle.api.enums.behavior.OperaTypeEnum;
import com.sohu.middle.api.service.*;
import com.sohu.middle.api.vo.*;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 万能接口，无需登录
 * 前端访问路由地址为:/app/api/article
 *
 * <AUTHOR>
 * @date 2023-06-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/content")
public class AppApiContentController extends BaseController {

    private final AppArticleService appArticleService;
    private final AppLiteratureService appLiteratureService;
    @DubboReference
    private RemoteMiddleService remoteMiddleService;
    @DubboReference
    private RemoteMiddleVideoService remoteMiddleVideoService;
    @DubboReference
    private RemoteMiddleArticleService remoteMiddleArticleService;
    @DubboReference
    private RemoteMiddleHistoryWordService remoteMiddleHistoryWordService;
    @DubboReference
    private RemoteMiddleLiteratureService remoteMiddleLiteratureService;

    /**
     * 瀑布流
     */
    @PostMapping("/waterfall")
    @SentinelResource("/app/api/content/waterfall")
    public TableDataInfo<SohuContentMainVo> waterfall(@RequestBody SohuContentMainBo bo) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(bo.getPageNum());
        pageQuery.setPageSize(bo.getPageSize());
        return appArticleService.waterfallOfAirec(bo, pageQuery);
    }

    /**
     * 获取万能详细信息-废弃
     *
     * @param id 主键
     */
    @Hidden
    @GetMapping("/{id}")
    public R<SohuContentMainVo> info(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(appArticleService.info(id));
    }

    /**
     * 草稿重发
     */
    @GetMapping("/retry")
    public R<Boolean> retry(SohuBusyBO busyBo) {
        return R.ok(appArticleService.retry(busyBo));
    }

    /**
     * 删除内容
     */
    @GetMapping("/delete")
    public R<Boolean> delete(SohuBusyBO busyBo) {
        return R.ok(appArticleService.delete(busyBo));
    }

    /**
     * 图文列表（精选 - 新）
     */
    @GetMapping("/article/list")
    @Operation(summary = "图文列表")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.ARTICLE, operType = OperaTypeEnum.LIST)
    public TableDataInfo<SohuArticleVo> list(SohuArticleBo bo, PageQuery pageQuery) {
        // 非游客状态记录搜索历史
        Long userId = LoginHelper.getUserId();
        if (StringUtils.isNotBlank(bo.getTitle()) && (userId != null && userId > 0L)) {
            remoteMiddleHistoryWordService.insertWord(userId, bo.getTitle());
        }
        return remoteMiddleArticleService.queryPageOfAirec(bo, pageQuery);
    }

    /**
     * 图文详情（新）
     */
    @GetMapping("/article/{id}")
    @Operation(summary = "图文详情")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.ARTICLE, operType = OperaTypeEnum.INFO)
    public R<SohuArticleVo> detail(@PathVariable("id") Long id,
                                   @RequestParam(value = "type", required = false, defaultValue = "false") Boolean type,
                                   @RequestParam(value = "isIndependent", required = false) Boolean isIndependent,
                                   @RequestParam(value = "taskNumber", required = false) String taskNumber) {
        return R.ok(appArticleService.queryById(id, type, isIndependent, taskNumber));
    }

    /**
     * 视频列表（新）
     */
    @GetMapping("/video/list")
    @Operation(summary = "视频列表")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.VIDEO, operType = OperaTypeEnum.LIST)
    public TableDataInfo<SohuVideoVo> videoList(SohuVideoBo bo, PageQuery pageQuery) {
        // 非游客状态记录搜索历史
        Long userId = LoginHelper.getUserId();
        if (StringUtils.isNotBlank(bo.getTitle()) && (userId != null && userId > 0L)) {
            remoteMiddleHistoryWordService.insertWord(userId, bo.getTitle());
        }
        bo.setState(CommonState.OnShelf.name());
        return remoteMiddleVideoService.queryPageOfAirec(bo, pageQuery);
    }

    /**
     * 视频详情（新）
     */
    @GetMapping("/video/{id}")
    @Operation(summary = "视频详情")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.VIDEO, operType = OperaTypeEnum.INFO)
    public R<SohuVideoVo> videoDetail(@PathVariable("id") Long id,
                                      @RequestParam(value = "isIndependent", required = false, defaultValue = "false") Boolean isIndependent,
                                      @RequestParam(value = "userId", required = false) Long userId) {
//        return R.ok((SohuVideoVo) remoteMiddleService.query(BusyType.Video, id));
        return R.ok(remoteMiddleVideoService.queryById(id, isIndependent, userId));
    }

    /**
     * 视频查看记录
     */
    @GetMapping("/video/view/{id}")
    @Operation(summary = "视频详情")
    public R<Boolean> videoView(@PathVariable("id") Long id) {
        return R.ok(remoteMiddleVideoService.videoView(id));
    }

    /**
     * 个人中心图文
     */
    @GetMapping("article/page/center")
    public TableDataInfo<SohuArticleVo> articlePageContentCenter(@RequestParam(required = false) Long userId, PageQuery pageQuery) {
        return remoteMiddleArticleService.articlePageCenter(userId, pageQuery);
    }

    /**
     * 个人中心视频
     */
    @GetMapping("/video/page/center")
    public TableDataInfo<SohuVideoVo> videoPageContentCenter(@RequestParam(required = false) Long userId, PageQuery pageQuery) {
        return remoteMiddleVideoService.videoPageCenter(userId, pageQuery);
    }

    /**
     * 数据转发
     */
    @PostMapping("/forward")
    @Operation(summary = "数据转发")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.COMMON, operType = OperaTypeEnum.FORWARD)
    public R<Boolean> forward(@RequestBody SohuBusyBO bo) {
        return R.ok(appArticleService.forward(bo));
    }

    /**
     * 关注视频
     */
    @GetMapping("/follow/video")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.VIDEO, operType = OperaTypeEnum.FOCUS)
    public TableDataInfo<SohuVideoVo> followVideoPage(PageQuery pageQuery) {
        return remoteMiddleVideoService.followPage(pageQuery);
    }

    /**
     * 关注图文
     */
    @GetMapping("/follow/article")
    public TableDataInfo<SohuArticleVo> followPage(PageQuery pageQuery) {
        return remoteMiddleArticleService.followPage(pageQuery);
    }

    /**
     * 返回每个标签下前五的图文
     * 双层数组结构返回
     */
    @GetMapping("/article/label/top/five")
    public R<List<SohuTopArticleVo>> articleLabelTopFive() {
        return R.ok(remoteMiddleArticleService.labelTopFive());
    }

    /**
     * 返回每个标签下前五的视频
     * 双层数组结构返回
     */
    @GetMapping("/video/label/top/five")
    public R<List<SohuTopVideoVo>> videoLabelTopFive() {
        return R.ok(remoteMiddleVideoService.labelTopFive());
    }

    /**
     * 个人中心诗歌
     */
    @GetMapping("/poetry/page/center")
    public TableDataInfo<SohuLiteratureVo> poetryPageContentCenter(@RequestParam(required = false) Long userId, PageQuery pageQuery) {
        return remoteMiddleLiteratureService.poetryPageContentCenter(userId, pageQuery);
    }

    /**
     * 个人中心散文
     */
    @GetMapping("/prose/page/center")
    public TableDataInfo<SohuLiteratureVo> prosePageContentCenter(@RequestParam(required = false) Long userId, PageQuery pageQuery) {
        return remoteMiddleLiteratureService.prosePageContentCenter(userId, pageQuery);
    }

    /**
     * 诗文列表
     */
    @GetMapping("/literature/list")
    @Operation(summary = "诗文列表")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.LITERATURE, operType = OperaTypeEnum.LIST)
    public TableDataInfo<SohuLiteratureVo> list(SohuLiteratureBo bo, PageQuery pageQuery) {
        // 非游客状态记录搜索历史
        Long userId = LoginHelper.getUserId();
        if (StringUtils.isNotBlank(bo.getTitle()) && (userId != null && userId > 0L)) {
            remoteMiddleHistoryWordService.insertWord(userId, bo.getTitle());
        }
        return remoteMiddleLiteratureService.queryPageList(bo, pageQuery);
    }

    /**
     * 诗文详情
     */
    @GetMapping("/literature/{id}")
    @Operation(summary = "诗文详情")
    @UserBehavior(busyType = BehaviorBusinessTypeEnum.LITERATURE, operType = OperaTypeEnum.INFO)
    public R<SohuLiteratureVo> literatureDetail(@PathVariable("id") Long id) {
        return R.ok(appLiteratureService.queryById(id));
    }

    /**
     * 返回许愿狐课堂下许愿的图文
     */
    @GetMapping("/article")
    @SkipApiEncrypt
    public R<List<SohuArticleVo>> article() {
        return R.ok(remoteMiddleArticleService.articleList());
    }

    /**
     * 内容列表（新）
     */
    @GetMapping("/content/list")
    @Operation(summary = "内容列表")
    public TableDataInfo<SohuContentVo> contentList(SohuContentListBo bo, PageQuery pageQuery) {
        return remoteMiddleService.queryPageOfAirec(bo, pageQuery);
    }

}
