package com.sohu.app.controller;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.middle.api.bo.SohuAgreeRecordBo;
import com.sohu.middle.api.service.RemoteMiddleAgreeRecordService;
import com.sohu.middle.api.service.RemoteMiddleUserAgreeService;
import com.sohu.middle.api.vo.SohuUserAgreeInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 用户协议控制器
 * 前端访问路由地址为:/app/userAgree
 *
 * <AUTHOR>
 * @date 2024-09-21
 */
@Tag(name = "协议管理-签署协议")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/userAgree")
public class AppUserAgreeController extends BaseController {

    @DubboReference
    private final RemoteMiddleUserAgreeService remoteMiddleUserAgreeService;

    @DubboReference
    private final RemoteMiddleAgreeRecordService remoteMiddleAgreeRecordService;


    /**
     * 获取用户协议详细信息
     *
     * @param id 主键
     */
    @Operation(summary = "获取用户协议详细信息", description = "负责人：李君婕，协议管理-获取用户协议详细信息")
    @Log(title = "获取用户协议详细信息")
    @GetMapping("/{id}")
    public R<SohuUserAgreeInfoVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteMiddleUserAgreeService.getInfoById(id));
    }

    /**
     * 签署用户协议
     */
    @Operation(summary = "签署用户协议", description = "负责人：李君婕，协议管理-新增用户协议")
    @Log(title = "签署用户协议", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@RequestBody SohuAgreeRecordBo bo) {
        return toAjax(remoteMiddleAgreeRecordService.insertByBo(bo));
    }
}
