package com.sohu.app.controller.api;

import com.sohu.common.core.enums.CommonState;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.service.RemoteMiddleAdInfoService;
import com.sohu.middle.api.service.RemoteMiddleArticleService;
import com.sohu.middle.api.service.RemoteMiddleQuestionService;
import com.sohu.middle.api.service.RemoteMiddleVideoService;
import com.sohu.middle.api.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * APP赚钱接口
 *
 * <AUTHOR>
 * @date 2024-03-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/business")
public class AppApiBusinessController {

    @DubboReference
    private RemoteMiddleVideoService remoteMiddleVideoService;
    @DubboReference
    private RemoteMiddleArticleService remoteMiddleArticleService;
    @DubboReference
    private RemoteMiddleQuestionService remoteMiddleQuestionService;
    @DubboReference
    private RemoteMiddleAdInfoService remoteMiddleAdInfoService;

    /**
     * 赚钱视频列表
     */
    @GetMapping("/video/list")
    @Operation(summary = "赚钱视频列表")
    public TableDataInfo<SohuVideoVo> businessVideoList(SohuBusinessVideoBo bo) {
        return remoteMiddleVideoService.businessVideoListOfAirec(bo);
    }

    /**
     * 视频列表-含广告
     */
    @GetMapping("/videoAdinfo/list")
    @Operation(summary = "视频列表-含广告")
    public TableDataInfo<SohuVideoAdInfoVo> businessVideoAdinfoList(SohuVideoAdInfoQueryBo bo, PageQuery pageQuery) {
        bo.setState(CommonState.OnShelf.name());
        return remoteMiddleAdInfoService.queryPageListOfVideo(bo,pageQuery);
    }

    /**
     * 赚钱图文列表
     */
    @GetMapping("/article/list")
    @Operation(summary = "赚钱图文列表")
    public TableDataInfo<SohuArticleVo> businessArticleList(SohuBusinessArticleBo bo) {
        return remoteMiddleArticleService.businessArticleListOfAirec(bo);
    }

    /**
     * 图文列表-含广告
     */
    @GetMapping("/articleAdinfo/list")
    @Operation(summary = "图文列表-含广告")
    public TableDataInfo<SohuArticleAdInfoVo> businessArticleAdInfoList(SohuArticleAdInfoQueryBo bo, PageQuery pageQuery) {
        return remoteMiddleAdInfoService.queryPageListOfArticle(bo,pageQuery);
    }

    /**
     * 赚钱问答列表
     */
    @GetMapping("/question/list")
    @Operation(summary = "赚钱问题列表")
    public TableDataInfo<SohuQuestionVo> businessQuestionList(SohuBusinessQuestionBo bo) {
        return remoteMiddleQuestionService.businessQuestionListOfAirec(bo);
    }
}
