package com.sohu.story.response.story;

import lombok.Data;

/**
 * @Author: leibo
 * @Date: 2025/7/3 09:39
 **/
@Data
public class BookInfoResponse {
    /**
     * 书籍ID
     */
    private Integer bookId;
    /**
     * 书籍标题
     */
    private String bookName;
    /**
     * 书籍封面
     */
    private String coverPic;
    /**
     * 书籍简介
     */
    private String bookIntro;
    /**
     * 书籍分类id（以英文逗号分割）
     */
    private String categoryIds;
    /**
     * 作者笔名
     */
    private String authorName;
    /**
     * 频道(1.男频  2.女频)
     */
    private Integer bookChannel;
    /**
     * 总章节数
     */
    private Long allChapter;
    /**
     * 付费章节数
     */
    private Long payChapter;
    /**
     * 是否为会员专享 0.否  1.是
     */
    private Integer isVip;
    /**
     * 是否完结（1.完结 2.连载）
     */
    private Integer isFinish;
    /**
     * 书籍字数
     */
    private Integer bookWordCount;
}
