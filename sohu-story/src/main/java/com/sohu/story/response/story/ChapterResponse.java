package com.sohu.story.response.story;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sohu.common.core.utils.DateUtils;
import lombok.Data;

import java.util.Date;

import static com.sohu.common.core.utils.DateUtils.TIME_ZONE_DEFAULT;

/**
 * 章节信息
 *
 * @Author: leibo
 * @Date: 2025/7/3 10:00
 **/
@Data
public class ChapterResponse {

    /**
     * 章节ID
     */
    private Long chapterId;
    /**
     * 章节标题
     */
    private String chapterName;
    /**
     * 章节序号
     */
    private Long order;
    /**
     * 章节字数
     */
    private Integer chapterWordCount;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS, timezone = TIME_ZONE_DEFAULT)
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS, timezone = TIME_ZONE_DEFAULT)
    private Date updateTime;
}
