package com.sohu.story.converter;

import com.google.common.base.Strings;
import com.sohu.story.api.vo.AnimeChapterVo;
import com.sohu.story.api.vo.AnimeVo;
import com.sohu.story.response.sevencats.*;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 七猫小说转换器
 *
 * @Author: leibo
 * @Date: 2025/5/6 09:35
 **/
public class SevenCatsConverter {

    private static final String URL = "https://api.sohuglobal.com/";

    /**
     * 定义分类映射关系（主分类 -> 子分类集合）
     */
    private static final Map<String, Set<String>> CATEGORY_MAPPINGS = new LinkedHashMap<>();

    static {
        CATEGORY_MAPPINGS.put("现代言情", Set.of("12", "2"));
        CATEGORY_MAPPINGS.put("古代言情", Set.of("17", "2"));
        CATEGORY_MAPPINGS.put("幻想言情", Set.of("20", "2"));
        CATEGORY_MAPPINGS.put("游戏竞技", Set.of("16"));
        CATEGORY_MAPPINGS.put("衍生言情", Set.of("32"));
        CATEGORY_MAPPINGS.put("纯爱", Set.of("30"));
    }

    /**
     * 定义二级分类映射关系（主分类 -> 子分类集合）
     */
    private static final Map<String, Set<String>> SUB_CATEGORY_MAPPINGS = new LinkedHashMap<>();

    static {
        SUB_CATEGORY_MAPPINGS.put("总裁豪门", Set.of("12", "2", "4"));
        SUB_CATEGORY_MAPPINGS.put("都市奇幻", Set.of("12", "2", "1", "20"));
        SUB_CATEGORY_MAPPINGS.put("年代重生", Set.of("12", "2", "22", "24"));
        SUB_CATEGORY_MAPPINGS.put("现实生活", Set.of("12", "2", "19"));
        SUB_CATEGORY_MAPPINGS.put("青春校园", Set.of("12", "2", "3"));
        SUB_CATEGORY_MAPPINGS.put("娱乐明星", Set.of("12", "2", "31"));
        SUB_CATEGORY_MAPPINGS.put("民国旧影", Set.of("12", "2", "37"));
        SUB_CATEGORY_MAPPINGS.put("职场情缘", Set.of("12", "2", "14"));
        SUB_CATEGORY_MAPPINGS.put("现代悬疑", Set.of("12", "2", "18"));
        SUB_CATEGORY_MAPPINGS.put("武侠江湖", Set.of("17", "2", "9"));
        SUB_CATEGORY_MAPPINGS.put("权谋天下", Set.of("17", "2", "34"));
        SUB_CATEGORY_MAPPINGS.put("古代情缘", Set.of("17", "2", "6"));
        SUB_CATEGORY_MAPPINGS.put("宫闱宅斗", Set.of("17", "2", "35", "36"));
        SUB_CATEGORY_MAPPINGS.put("古代悬疑", Set.of("17", "2", "18"));
        SUB_CATEGORY_MAPPINGS.put("异世幻想", Set.of("20", "2", "26", "7"));
        SUB_CATEGORY_MAPPINGS.put("末世求生", Set.of("20", "2", "21"));
        SUB_CATEGORY_MAPPINGS.put("无限快穿", Set.of("20", "2", "5"));
        SUB_CATEGORY_MAPPINGS.put("未来科幻", Set.of("20", "2", "10"));
        SUB_CATEGORY_MAPPINGS.put("玄幻仙侠", Set.of("20", "2", "8"));
        SUB_CATEGORY_MAPPINGS.put("网游电竞", Set.of("16"));
        SUB_CATEGORY_MAPPINGS.put("经典衍生", Set.of("32"));
        SUB_CATEGORY_MAPPINGS.put("幻想纯爱", Set.of("30", "20"));
        SUB_CATEGORY_MAPPINGS.put("古代纯爱", Set.of("30", "17"));
        SUB_CATEGORY_MAPPINGS.put("现代纯爱", Set.of("30", "12"));
    }

    /**
     * 根据书籍的分类标签映射主分类
     *
     * @param bookCategories 书籍的分类列表
     * @return 主分类或原分类
     */
    private static String mapCategories(List<String> bookCategories) {
        Set<String> bookSet = new HashSet<>(bookCategories);
        // 遍历映射，按顺序检查是否满足条件
        for (Map.Entry<String, Set<String>> entry : CATEGORY_MAPPINGS.entrySet()) {
            if (bookSet.containsAll(entry.getValue())) {
                return entry.getKey();
            }
        }
        return "";
    }

    /**
     * 根据书籍的分类标签映射二级分类
     *
     * @param bookCategories 书籍的分类列表
     * @return 主分类或原分类
     */
    private static String mapSubCategories(List<String> bookCategories) {
        Set<String> bookSet = new HashSet<>(bookCategories);
        // 遍历映射，按顺序检查是否满足条件
        for (Map.Entry<String, Set<String>> entry : SUB_CATEGORY_MAPPINGS.entrySet()) {
            if (bookSet.containsAll(entry.getValue())) {
                return entry.getKey();
            }
        }
        return "";
    }

    /**
     * 转换成明细
     *
     * @param animeVo
     * @param animeChapterVo
     * @param word
     * @param imageUrl
     * @return
     */
    public static BookInfoResponse toBookInfoResponse(AnimeVo animeVo, String imageUrl,
                                                      AnimeChapterVo animeChapterVo, Integer word) {
        if (Objects.isNull(animeVo)) {
            return null;
        }
        BookInfoResponse infoResponse = new BookInfoResponse();
        ItemBaseResponse itemResponse = toItemBaseResponse(animeVo);
        itemResponse.setWord(word);
        itemResponse.setImageLink(Strings.isNullOrEmpty(imageUrl) ? animeVo.getCoverpic() : imageUrl);
        itemResponse.setLatestChapter(animeChapterVo.getTitle());
        itemResponse.setLatestChapterId(animeChapterVo.getId().intValue());
        itemResponse.setLatestDate(animeChapterVo.getUpdatedAt().getTime() / 1000);
        itemResponse.setVipChapterNum(animeVo.getPaychapter().intValue());
        infoResponse.setItemResponse(itemResponse);
        return infoResponse;
    }

    /**
     * 章节转换
     *
     * @param chapterList
     * @return
     */
    public static NovelResponse toNovelResponse(List<AnimeChapterVo> chapterList, AnimeVo animeVo) {
        if (CollectionUtils.isEmpty(chapterList)) {
            return null;
        }
        NovelResponse novelResponse = new NovelResponse();
        NovelResponse.Volume volume = new NovelResponse.Volume();
        volume.setName("正文");
        List<NovelResponse.Chapter> chapters = new ArrayList<>();
        for (AnimeChapterVo chapterVo : chapterList) {
            chapters.add(toChapter(chapterVo, animeVo));
        }
        volume.setChapters(chapters);
        List<NovelResponse.Volume> volumeList = new ArrayList<>();
        volumeList.add(volume);
        novelResponse.setVolumes(volumeList);
        return novelResponse;
    }

    /**
     * 转换成章节对象
     *
     * @param animeChapterVo
     * @return
     */
    private static NovelResponse.Chapter toChapter(AnimeChapterVo animeChapterVo, AnimeVo animeVo) {
        NovelResponse.Chapter chapter = new NovelResponse.Chapter();
        chapter.setChapterId(animeChapterVo.getId().intValue());
        chapter.setIsVip(0);
        chapter.setChapterDate(animeChapterVo.getUpdatedAt().getTime() / 1000);
        chapter.setChapterUrl(URL + "story/seven/cats/chapter/info?book_id=" + animeChapterVo.getAnid() + "&chapter_id=" + animeChapterVo.getId());
        chapter.setChapterTitle(animeChapterVo.getTitle());
        chapter.setWords(animeChapterVo.getInfo().length());
        if (animeChapterVo.getChaps() >= animeVo.getPaychapter()) {
            chapter.setPrice(animeVo.getCoin().intValue());
        }
        return chapter;
    }

    /**
     * 转换成章节明细对象
     *
     * @param chapterList
     * @return
     */
    public static ChapterResponse toChapterResponse(List<AnimeChapterVo> chapterList) {
        if (CollectionUtils.isEmpty(chapterList)) {
            return null;
        }
        ChapterResponse chapterResponse = new ChapterResponse();
        List<ChapterResponse.Chapter> chapters = new ArrayList<>();
        for (AnimeChapterVo animeChapterVo : chapterList) {
            ChapterResponse.Chapter chapter = new ChapterResponse.Chapter();
            chapter.setBookId(animeChapterVo.getAnid().intValue());
            chapter.setChapterId(animeChapterVo.getId().intValue());
            chapter.setContent(replaceNewlinesWithPTags(animeChapterVo.getInfo()));
            chapters.add(chapter);
        }
        chapterResponse.setChapters(chapters);
        return chapterResponse;
    }

    /**
     * 转换成书对象
     *
     * @param animeVo
     * @return
     */
    public static ItemResponse toItemResponse(AnimeVo animeVo, Long id) {
        List<String> categoryIds = Arrays.asList(animeVo.getCateids().split(","));
        ItemResponse itemResponse = new ItemResponse();
        itemResponse.setId(id);
        itemResponse.setBookId(animeVo.getId());
        itemResponse.setOver(1);
        itemResponse.setTitle(animeVo.getTitle());
        itemResponse.setAuthor(animeVo.getAuthor());
        itemResponse.setVip(animeVo.getIsvip());
        itemResponse.setType(mapCategories(categoryIds));
        itemResponse.setSubCategory(mapSubCategories(categoryIds));
        itemResponse.setTag(animeVo.getTag());
        itemResponse.setComment(animeVo.getDesc());
        itemResponse.setChapter(URL + "story/seven/cats/chapter?book_id=" + animeVo.getId());
        return itemResponse;
    }

    /**
     * 转换成书对象
     *
     * @param animeVo
     * @return
     */
    public static ItemBaseResponse toItemBaseResponse(AnimeVo animeVo) {
        List<String> categoryIds = Arrays.asList(animeVo.getCateids().split(","));
        ItemBaseResponse itemResponse = new ItemBaseResponse();
        itemResponse.setBookId(animeVo.getId());
        itemResponse.setOver(1);
        itemResponse.setTitle(animeVo.getTitle());
        itemResponse.setAuthor(animeVo.getAuthor());
        itemResponse.setVip(animeVo.getIsvip());
        itemResponse.setType(mapCategories(categoryIds));
        itemResponse.setSubCategory(mapSubCategories(categoryIds));
        itemResponse.setTag(animeVo.getTag());
        itemResponse.setComment(animeVo.getDesc());
        itemResponse.setChapter(URL + "story/seven/cats/chapter?book_id=" + animeVo.getId());
        return itemResponse;
    }

    /**
     * 将换行替换成 p 标签
     *
     * @param input
     * @return
     */
    public static String replaceNewlinesWithPTags(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("\n", "</p><p>")
                .replace("\r", "") // 可选：处理 Windows 的 \r\n
                .replaceFirst("^", "<p>") // 开头加 <p>
                .replaceFirst("$", "</p>"); // 结尾加 </p>
    }
}
