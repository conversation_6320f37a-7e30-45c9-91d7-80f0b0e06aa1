package com.sohu.story.converter;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.story.api.vo.AnimeChapterVo;
import com.sohu.story.api.vo.AnimeVo;
import com.sohu.story.response.jadepool.JadePoolBookInfoResponse;
import com.sohu.story.response.jadepool.JadePoolChapterInfoResponse;
import com.sohu.story.response.jadepool.JadePoolChapterResponse;

import java.util.*;

/**
 * 瑶池小说转换器
 *
 * @Author: leibo
 * @Date: 2025/5/9 14:46
 **/
public class JadePoolConverter {

    /**
     * 定义分类映射关系（主分类 -> 子分类集合）
     */
    private static final Map<Integer, Set<String>> CATEGORY_MAPPINGS = new LinkedHashMap<>();

    static {
        CATEGORY_MAPPINGS.put(13, Set.of("58", "59", "60", "61", "62", "110", "111"));
        CATEGORY_MAPPINGS.put(15, Set.of("67", "68", "69", "70", "71", "72", "112", "113"));
        CATEGORY_MAPPINGS.put(16, Set.of("73", "74", "75", "76", "77", "114", "115", "116"));
        CATEGORY_MAPPINGS.put(17, Set.of("78", "79", "80", "81", "117", "118"));
        CATEGORY_MAPPINGS.put(18, Set.of("82", "83", "84", "85", "86", "119"));
        CATEGORY_MAPPINGS.put(19, Set.of("87", "88", "89", "90", "91", "92", "93", "94", "120"));
        CATEGORY_MAPPINGS.put(20, Set.of("95", "96", "97", "98", "99", "121", "122", "123", "124"));
        CATEGORY_MAPPINGS.put(21, Set.of("100", "101"));
    }

    /**
     * 定义二级分类映射关系（主分类 -> 子分类集合）
     */
    private static final Map<Integer, Set<String>> SUB_CATEGORY_MAPPINGS = new LinkedHashMap<>();

    static {
        SUB_CATEGORY_MAPPINGS.put(58, Set.of("18"));
        SUB_CATEGORY_MAPPINGS.put(59, Set.of("23"));
        SUB_CATEGORY_MAPPINGS.put(62, Set.of("33"));
        SUB_CATEGORY_MAPPINGS.put(110, Set.of("22"));
        SUB_CATEGORY_MAPPINGS.put(111, Set.of("11"));
        SUB_CATEGORY_MAPPINGS.put(67, Set.of("20"));
        SUB_CATEGORY_MAPPINGS.put(68, Set.of("7"));
        SUB_CATEGORY_MAPPINGS.put(69, Set.of("26"));
        SUB_CATEGORY_MAPPINGS.put(70, Set.of("9"));
        SUB_CATEGORY_MAPPINGS.put(71, Set.of("8"));
        SUB_CATEGORY_MAPPINGS.put(112, Set.of("27", "8"));
        SUB_CATEGORY_MAPPINGS.put(113, Set.of("29", "8"));
        SUB_CATEGORY_MAPPINGS.put(76, Set.of("21"));
        SUB_CATEGORY_MAPPINGS.put(114, Set.of("21", "27"));
        SUB_CATEGORY_MAPPINGS.put(80, Set.of("6"));
        SUB_CATEGORY_MAPPINGS.put(81, Set.of("37"));
        SUB_CATEGORY_MAPPINGS.put(117, Set.of("37", "29"));
        SUB_CATEGORY_MAPPINGS.put(118, Set.of("37", "28"));
        SUB_CATEGORY_MAPPINGS.put(82, Set.of("35", "36"));
        SUB_CATEGORY_MAPPINGS.put(83, Set.of("25"));
        SUB_CATEGORY_MAPPINGS.put(84, Set.of("34"));
        SUB_CATEGORY_MAPPINGS.put(85, Set.of("17", "29"));
        SUB_CATEGORY_MAPPINGS.put(86, Set.of("17", "28"));
        SUB_CATEGORY_MAPPINGS.put(119, Set.of("17", "27"));
        SUB_CATEGORY_MAPPINGS.put(87, Set.of("1"));
        SUB_CATEGORY_MAPPINGS.put(88, Set.of("3"));
        SUB_CATEGORY_MAPPINGS.put(89, Set.of("4"));
        SUB_CATEGORY_MAPPINGS.put(90, Set.of("14"));
        SUB_CATEGORY_MAPPINGS.put(91, Set.of("31"));
        SUB_CATEGORY_MAPPINGS.put(92, Set.of("24"));
        SUB_CATEGORY_MAPPINGS.put(93, Set.of("12", "29"));
        SUB_CATEGORY_MAPPINGS.put(94, Set.of("12", "28"));
        SUB_CATEGORY_MAPPINGS.put(120, Set.of("12", "27"));
        SUB_CATEGORY_MAPPINGS.put(95, Set.of("13"));
        SUB_CATEGORY_MAPPINGS.put(96, Set.of("2"));
        SUB_CATEGORY_MAPPINGS.put(121, Set.of("19", "27"));
        SUB_CATEGORY_MAPPINGS.put(122, Set.of("19", "29"));
        SUB_CATEGORY_MAPPINGS.put(98, Set.of("19"));
        SUB_CATEGORY_MAPPINGS.put(99, Set.of("12"));
        SUB_CATEGORY_MAPPINGS.put(123, Set.of("27"));
        SUB_CATEGORY_MAPPINGS.put(100, Set.of("30"));
    }

    /**
     * 根据书籍的分类标签映射主分类
     *
     * @param categoryId 分类id
     * @return 主分类或原分类
     */
    private static Integer mapCategories(String categoryId) {
        // 遍历映射，按顺序检查是否满足条件
        for (Map.Entry<Integer, Set<String>> entry : CATEGORY_MAPPINGS.entrySet()) {
            if (entry.getValue().contains(categoryId)) {
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * 根据书籍的分类标签映射二级分类
     *
     * @param bookCategories 书籍的分类列表
     * @return 主分类或原分类
     */
    private static Integer mapSubCategories(List<String> bookCategories) {
        Set<String> bookSet = new HashSet<>(bookCategories);
        // 遍历所有可能的子分类
        for (String category : bookSet) {
            for (Map.Entry<Integer, Set<String>> entry : SUB_CATEGORY_MAPPINGS.entrySet()) {
                if (entry.getValue().contains(category)) {
                    return entry.getKey(); // 匹配任意一个标签即返回
                }
            }
        }
        return null;
    }


    /**
     * 对象转换处理
     *
     * @param animeVo
     * @return
     */
    public static JadePoolBookInfoResponse toBookInfoResponse(AnimeVo animeVo, Integer word, Date time) {
        if (Objects.isNull(animeVo)) {
            return null;
        }
        List<String> categoryIds = Arrays.asList(animeVo.getCateids().split(","));
        JadePoolBookInfoResponse infoResponse = new JadePoolBookInfoResponse();
        infoResponse.setBookId(animeVo.getId().intValue());
        infoResponse.setBookName(animeVo.getTitle());
        infoResponse.setBookPhoto(animeVo.getPicture());
        infoResponse.setBookLabels("");
        infoResponse.setAuthorName(animeVo.getAuthor());
        infoResponse.setIsFinish(1);
        infoResponse.setBookChannel(animeVo.getIssex().intValue());
        // 待定
        Integer categoryId = mapSubCategories(categoryIds);
        if (Objects.nonNull(categoryId)) {
            infoResponse.setPCategoryId(mapCategories(categoryId.toString()));
            infoResponse.setCategoryId(categoryId);
        }
        infoResponse.setBookWordCount(word);
        infoResponse.setBookIntro(animeVo.getDesc());
        infoResponse.setIsVip(1);
        infoResponse.setLastUpdateChapterTime(time);
        return infoResponse;
    }

    /**
     * 章节对象转换处理
     *
     * @param chapterList
     * @return
     */
    public static List<JadePoolChapterResponse> toChapterList(List<AnimeChapterVo> chapterList, AnimeVo animeVo) {
        if (CollectionUtils.isEmpty(chapterList)) {
            return null;
        }
        List<JadePoolChapterResponse> chapterResponseList = new ArrayList<>();
        for (AnimeChapterVo animeChapterVo : chapterList) {
            JadePoolChapterResponse chapterResponse = new JadePoolChapterResponse();
            chapterResponse.setChapterId(animeChapterVo.getId().intValue());
            chapterResponse.setChapterName(animeChapterVo.getTitle());
            chapterResponse.setChapterWordCount(animeChapterVo.getInfo().length());
            if (animeChapterVo.getChaps() < animeVo.getPaychapter()) {
                chapterResponse.setIsVip(2);
            } else {
                chapterResponse.setIsVip(1);
            }
            chapterResponse.setOrder(animeChapterVo.getId().intValue());
            chapterResponse.setCreateTime(animeChapterVo.getCreatedAt());
            chapterResponseList.add(chapterResponse);
        }
        return chapterResponseList;
    }

    /**
     * 章节详情对象转换
     *
     * @param chapterList
     * @return
     */
    public static JadePoolChapterInfoResponse toChapterInfoResponse(List<AnimeChapterVo> chapterList) {
        if (CollectionUtils.isEmpty(chapterList)) {
            return null;
        }
        AnimeChapterVo animeChapterVo = chapterList.get(0);
        JadePoolChapterInfoResponse chapterInfoResponse = new JadePoolChapterInfoResponse();
        chapterInfoResponse.setChapterId(animeChapterVo.getId().intValue());
        chapterInfoResponse.setChapterName(animeChapterVo.getTitle());
        chapterInfoResponse.setChapterWordCount(animeChapterVo.getInfo().length());
        chapterInfoResponse.setChapterContent(animeChapterVo.getInfo());
        chapterInfoResponse.setOrder(animeChapterVo.getId().intValue());
        chapterInfoResponse.setCreateTime(animeChapterVo.getCreatedAt());
        return chapterInfoResponse;
    }
}
