package com.sohu.story.converter;

import com.sohu.story.api.vo.AnimeChapterVo;
import com.sohu.story.api.vo.AnimeVo;
import com.sohu.story.domain.Anime;
import com.sohu.story.domain.AnimeCates;
import com.sohu.story.response.jadepool.JadePoolChapterInfoResponse;
import com.sohu.story.response.jadepool.JadePoolChapterResponse;
import com.sohu.story.response.story.*;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 小说转换器
 *
 * @Author: leibo
 * @Date: 2025/7/3 09:35
 **/
public class StoryConverter {

    /**
     * 转换成分类
     *
     * @param animeCatesList
     * @return
     */
    public static List<CategoryResponse> toCategoryResponse(List<AnimeCates> animeCatesList) {
        if (CollectionUtils.isEmpty(animeCatesList)) {
            return new ArrayList<>();
        }
        List<CategoryResponse> categoryResponseList = new ArrayList<>();
        for (AnimeCates animeCates : animeCatesList) {
            CategoryResponse categoryResponse = new CategoryResponse();
            categoryResponse.setId(animeCates.getId());
            categoryResponse.setName(animeCates.getName());
            categoryResponseList.add(categoryResponse);
        }
        return categoryResponseList;
    }

    /**
     * 转换成书籍信息
     *
     * @param animeList
     * @return
     */
    public static List<BookResponse> toBookResponse(List<Anime> animeList) {
        if (CollectionUtils.isEmpty(animeList)) {
            return new ArrayList<>();
        }
        List<BookResponse> bookResponseList = new ArrayList<>();
        for (Anime anime : animeList) {
            BookResponse bookResponse = new BookResponse();
            bookResponse.setBookId(anime.getId());
            bookResponse.setBookName(anime.getTitle());
            bookResponseList.add(bookResponse);
        }
        return bookResponseList;
    }

    /**
     * 转换成书明细
     *
     * @param animeVo
     * @return
     */
    public static BookInfoResponse toBookInfo(AnimeVo animeVo, Integer word) {
        if (Objects.isNull(animeVo)) {
            return null;
        }
        BookInfoResponse infoResponse = new BookInfoResponse();
        infoResponse.setBookId(animeVo.getId().intValue());
        infoResponse.setBookName(animeVo.getTitle());
        infoResponse.setCoverPic(animeVo.getCoverpic());
        infoResponse.setBookIntro(animeVo.getDesc());
        infoResponse.setCategoryIds(animeVo.getCateids());
        infoResponse.setAuthorName(animeVo.getAuthor());
        infoResponse.setIsFinish(animeVo.getIswz() == 2L ? 1 : 0);
        infoResponse.setBookChannel(animeVo.getIssex().intValue());
        infoResponse.setAllChapter(animeVo.getAllchapter());
        infoResponse.setPayChapter(animeVo.getPaychapter());
        infoResponse.setIsVip(animeVo.getIsvip());
        infoResponse.setBookWordCount(word);
        return infoResponse;
    }

    /**
     * 转换成章节列表
     *
     * @param animeChapterList
     * @return
     */
    public static List<ChapterResponse> toChapterList(List<AnimeChapterVo> animeChapterList) {
        if (CollectionUtils.isEmpty(animeChapterList)) {
            return new ArrayList<>();
        }
        List<ChapterResponse> chapterResponseList = new ArrayList<>();
        for (AnimeChapterVo animeChapterVo : animeChapterList) {
            ChapterResponse chapterResponse = new ChapterResponse();
            chapterResponse.setChapterId(animeChapterVo.getId());
            chapterResponse.setChapterName(animeChapterVo.getTitle());
            chapterResponse.setOrder(animeChapterVo.getChaps());
            chapterResponse.setCreateTime(animeChapterVo.getCreatedAt());
            chapterResponse.setUpdateTime(animeChapterVo.getUpdatedAt());
            chapterResponse.setChapterWordCount(animeChapterVo.getInfo().length());
            chapterResponseList.add(chapterResponse);
        }
        return chapterResponseList;
    }

    /**
     * 转换成章节明细内容
     *
     * @param animeChapterVo
     * @return
     */
    public static ChapterInfoResponse toChapterInfo(AnimeChapterVo animeChapterVo) {
        if (Objects.isNull(animeChapterVo)) {
            return null;
        }
        ChapterInfoResponse chapterInfoResponse = new ChapterInfoResponse();
        chapterInfoResponse.setChapterId(animeChapterVo.getId());
        chapterInfoResponse.setChapterName(animeChapterVo.getTitle());
        chapterInfoResponse.setChapterContent(animeChapterVo.getInfo());
        chapterInfoResponse.setCreateTime(animeChapterVo.getCreatedAt());
        chapterInfoResponse.setUpdateTime(animeChapterVo.getUpdatedAt());
        return chapterInfoResponse;
    }
}
