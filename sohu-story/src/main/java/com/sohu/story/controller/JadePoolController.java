package com.sohu.story.controller;

import com.sohu.common.core.web.controller.BaseController;
import com.sohu.story.response.bookflag.*;
import com.sohu.story.response.jadepool.*;
import com.sohu.story.service.JadePoolService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 瑶池控制器
 * 前端访问路由地址为:/story/jade/pool
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/jade/pool")
@Slf4j
public class JadePoolController extends BaseController {

    private final JadePoolService jadePoolService;

    /**
     * 查询书籍列表
     *
     * @return
     */
    @GetMapping("/GetBookList")
    private JadePoolResult<List<JadePoolBookResponse>> list() {
        return JadePoolResult.ok(jadePoolService.list());
    }

    /**
     * 查询小说详情
     *
     * @param bookid
     * @return
     */
    @GetMapping("/GetBookInfo")
    private JadePoolResult<JadePoolBookInfoResponse> info(@RequestParam(name = "bookId") Long bookId) {
        return JadePoolResult.ok(jadePoolService.getBookInfo(bookId));
    }

    /**
     * 查询小说卷信息及章节信息
     *
     * @param bookid
     * @return
     */
    @GetMapping("/GetChapterList")
    private JadePoolResult<List<JadePoolChapterResponse>> getChapterList(@RequestParam(name = "bookId") Long bookId) {
        return JadePoolResult.ok(jadePoolService.getChapterList(bookId));
    }

    /**
     * 查询小说内容
     *
     * @param bookid
     * @return
     */
    @GetMapping("/GetChapterInfo")
    private JadePoolResult<JadePoolChapterInfoResponse> getChapterInfo(@RequestParam(name = "bookId") Long bookId,
                                                                       @RequestParam(name = "chapterId") Long chapterId) {
        return JadePoolResult.ok(jadePoolService.getChapterInfo(bookId, chapterId));
    }

}
