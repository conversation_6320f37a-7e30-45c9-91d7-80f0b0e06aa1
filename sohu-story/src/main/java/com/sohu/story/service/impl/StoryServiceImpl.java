package com.sohu.story.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.story.api.vo.AnimeChapterVo;
import com.sohu.story.api.vo.AnimeVo;
import com.sohu.story.converter.StoryConverter;
import com.sohu.story.domain.Anime;
import com.sohu.story.domain.AnimeCates;
import com.sohu.story.mapper.AnimeCatesMapper;
import com.sohu.story.mapper.AnimeMapper;
import com.sohu.story.response.story.*;
import com.sohu.story.service.IAnimeChapterService;
import com.sohu.story.service.IAnimeService;
import com.sohu.story.service.StoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: leibo
 * @Date: 2025/7/3 09:16
 **/
@RequiredArgsConstructor
@Service
public class StoryServiceImpl implements StoryService {

    private final AnimeCatesMapper animeCatesMapper;
    private final AnimeMapper animeMapper;
    private final IAnimeService animeService;
    private final IAnimeChapterService animeChapterService;

    @Override
    public List<CategoryResponse> listCategoryAll() {
        List<AnimeCates> animeCatesList = animeCatesMapper.selectList(Wrappers.<AnimeCates>lambdaQuery().eq(AnimeCates::getStus, 1));
        return StoryConverter.toCategoryResponse(animeCatesList);
    }

    @Override
    public List<BookResponse> listBook() {
        LambdaQueryWrapper<Anime> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(Anime :: getId, Anime :: getTitle);
        queryWrapper.eq(Anime::getBtype, 2)
                .eq(Anime::getStatus, 1)
                .eq(Anime::getReviewStatus, 1);
        List<Anime> animeList = animeMapper.selectList(queryWrapper);
        return StoryConverter.toBookResponse(animeList);
    }

    @Override
    public BookInfoResponse getBookInfo(Long bookId) {
        AnimeVo animeVo = animeService.queryById(bookId);
        Integer word = animeChapterService.getWordByBookId(bookId);
        return StoryConverter.toBookInfo(animeVo, word);
    }

    @Override
    public List<ChapterResponse> getChapterList(Long bookId) {
        List<AnimeChapterVo> animeChapterList = animeChapterService.listChapterByBookId(bookId);
        return StoryConverter.toChapterList(animeChapterList);
    }

    @Override
    public ChapterInfoResponse getChapterInfo(Long bookId, Long chapterId) {
        AnimeChapterVo animeChapterVo = animeChapterService.getChapterByBookIdAndChapterId(bookId, chapterId);
        return StoryConverter.toChapterInfo(animeChapterVo);
    }
}
