package com.sohu.story.service.impl;

import com.google.common.base.Strings;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.story.api.bo.AnimeBo;
import com.sohu.story.api.vo.AnimeChapterVo;
import com.sohu.story.api.vo.AnimeVo;
import com.sohu.story.converter.SevenCatsConverter;
import com.sohu.story.response.sevencats.*;
import com.sohu.story.service.IAnimeChapterService;
import com.sohu.story.service.IAnimeService;
import com.sohu.story.service.SevenCatsService;
import com.sohu.story.util.ImageUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 七猫实现层
 *
 * @Author: leibo
 * @Date: 2025/4/30 17:34
 **/
@RequiredArgsConstructor
@Service
public class SevenCatsServiceImpl implements SevenCatsService {

    private final IAnimeService animeService;
    private final IAnimeChapterService animeChapterService;
    private final ImageUtil imageUtil;

    @Override
    public BookResponse pageBook(String data, Integer page) {
        AnimeBo bo = new AnimeBo();
        bo.setBtype(2L);
        bo.setStatus(1L);
        bo.setReviewStatus(1L);
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(Objects.isNull(page) ? 1 : page);
        pageQuery.setPageSize(500);
        TableDataInfo<AnimeVo> result = animeService.queryPageList(bo, pageQuery);
        BookResponse response = new BookResponse();
        response.setTotalnum(result.getTotal());
        if (CollectionUtils.isEmpty(result.getData())) {
            return response;
        }
        List<ItemResponse> itemResponseList = new ArrayList<>();
        Long id = (pageQuery.getPageNum() - 1) * 500 + 1L;
        for (AnimeVo animeVo : result.getData()) {
            ItemResponse itemResponse = SevenCatsConverter.toItemResponse(animeVo, id);
            if (StringUtils.isEmpty(animeVo.getPicture())) {
                String imageUrl = imageUtil.processAndUploadImage(animeVo.getCoverpic());
                if (StringUtils.isNotEmpty(imageUrl)) {
                    animeService.updatePicture(animeVo.getId(), imageUrl);
                    animeVo.setPicture(imageUrl);
                }
            }
            AnimeChapterVo animeChapterVo = animeChapterService.getNearChapter(animeVo.getId());
            Integer word = animeChapterService.getWordByBookId(animeVo.getId());
            itemResponse.setWord(word);
            itemResponse.setImageLink(StringUtils.isEmpty(animeVo.getPicture()) ? animeVo.getCoverpic() : animeVo.getPicture());
            itemResponse.setLatestChapter(animeChapterVo.getTitle());
            itemResponse.setLatestChapterId(animeChapterVo.getId().intValue());
            itemResponse.setLatestDate(animeChapterVo.getUpdatedAt().getTime() /1000);
            itemResponse.setVipChapterNum(animeVo.getPaychapter().intValue());
            itemResponseList.add(itemResponse);
            id ++;
        }
        response.setItemResponseList(itemResponseList);
        return response;
    }

    @Override
    public NovelResponse listChapter(Long bookId) {
        AnimeVo animeVo = animeService.queryById(bookId);
        List<AnimeChapterVo> animeChapterList = animeChapterService.listChapterByBookId(bookId);
        return SevenCatsConverter.toNovelResponse(animeChapterList, animeVo);
    }

    @Override
    public ChapterResponse getChapterInfo(Long bookId, Long chapterId) {
        List<AnimeChapterVo> animeChapterList = animeChapterService.listChapterByBookIdAndChapterId(bookId, chapterId);
        return SevenCatsConverter.toChapterResponse(animeChapterList);
    }

    @Override
    public BookInfoResponse getBookInfo(Long bookId) {
        AnimeVo animeVo = animeService.queryById(bookId);
        String imageUrl = imageUtil.processAndUploadImage(animeVo.getCoverpic());
        AnimeChapterVo animeChapterVo = animeChapterService.getNearChapter(bookId);
        Integer word = animeChapterService.getWordByBookId(bookId);
        return SevenCatsConverter.toBookInfoResponse(animeVo, imageUrl, animeChapterVo, word);
    }

    @Override
    public List<ItemResponse> listAll() {
//        String[] data = {
//                "3627", "3630", "3631", "3632", "3634", "3635", "3637", "3638", "3639",
//                "3640", "3641", "3642", "3643", "3644", "3645", "3649", "3650", "3652",
//                "3654", "3655", "3656", "3657", "3658", "3661", "3662", "3664", "3665",
//                "3666", "3667", "3668", "3669", "3670", "3671", "3672", "3673", "3675",
//                "3677", "3679", "3680", "3681", "3682", "3683", "3684", "3685", "3686",
//                "3687", "3688", "3689", "3690", "3691", "3692", "3693", "3694", "3695",
//                "3696", "3699", "3700", "3702", "3703", "3704", "3705", "3706", "3707",
//                "3708", "3709", "3710", "3711", "3712", "3713", "3714", "3715", "3716",
//                "3720", "3721", "3722", "3723", "3724", "3725", "3726", "3727", "3728",
//                "3729", "3730", "3731", "3732", "3733", "3734", "3735", "3736", "3737",
//                "3738", "3740", "3741", "3742", "3743", "3744", "3745", "3746", "3747",
//                "3748"
//        };
//        // 转换为 List<Long>
//        List<Long> ids = Arrays.stream(data)
//                .map(Long::parseLong)
//                .collect(Collectors.toList());
//        List<AnimeVo> animeVos = animeService.listAnimeByIds(ids);
        List<AnimeVo> animeVos = animeService.listAnime();
        List<ItemResponse> itemResponseList = new ArrayList<>();
        for (AnimeVo animeVo : animeVos) {
            ItemResponse itemResponse = SevenCatsConverter.toItemResponse(animeVo, 1L);
            if (StringUtils.isEmpty(animeVo.getPicture())) {
                String imageUrl = imageUtil.processAndUploadImage(animeVo.getCoverpic());
                if (StringUtils.isNotEmpty(imageUrl)) {
                    animeService.updatePicture(animeVo.getId(), imageUrl);
                    animeVo.setPicture(imageUrl);
                }
            }
            AnimeChapterVo animeChapterVo = animeChapterService.getNearChapter(animeVo.getId());
            Integer word = animeChapterService.getWordByBookId(animeVo.getId());
            itemResponse.setWord(word);
            itemResponse.setImageLink(StringUtils.isEmpty(animeVo.getPicture()) ? animeVo.getCoverpic() : animeVo.getPicture());
            itemResponse.setLatestChapter(animeChapterVo.getTitle());
            itemResponse.setLatestChapterId(animeChapterVo.getId().intValue());
            itemResponse.setLatestDate(animeChapterVo.getUpdatedAt().getTime() /1000);
            itemResponse.setVipChapterNum(animeVo.getPaychapter().intValue());
            itemResponseList.add(itemResponse);
        }
        return itemResponseList;
    }
}
