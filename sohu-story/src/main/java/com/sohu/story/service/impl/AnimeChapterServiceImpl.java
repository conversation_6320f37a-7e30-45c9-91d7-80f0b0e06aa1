package com.sohu.story.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.story.api.vo.AnimeChapterVo;
import com.sohu.story.domain.AnimeChapter;
import com.sohu.story.mapper.AnimeChapterMapper;
import com.sohu.story.service.IAnimeChapterService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 章节Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@RequiredArgsConstructor
@Service
public class AnimeChapterServiceImpl implements IAnimeChapterService {

    private final AnimeChapterMapper baseMapper;


    @Override
    public List<AnimeChapterVo> listChapterByBookId(Long bookId) {
        return baseMapper.selectVoList(Wrappers.<AnimeChapter>lambdaQuery()
                .eq(AnimeChapter::getAnid, bookId)
                .eq(AnimeChapter::getReviewStatus, -1));
    }

    @Override
    public List<AnimeChapterVo> listChapterByBookIdAndChapterId(Long bookId, Long chapterId) {
        return baseMapper.selectVoList(Wrappers.<AnimeChapter>lambdaQuery()
                .eq(AnimeChapter::getAnid, bookId)
                .eq(AnimeChapter::getId, chapterId)
                .eq(AnimeChapter::getReviewStatus, -1));
    }

    @Override
    public AnimeChapterVo getNearChapter(Long bookId) {
        return baseMapper.selectVoOne(Wrappers.<AnimeChapter>lambdaQuery()
                .eq(AnimeChapter::getAnid, bookId)
                .eq(AnimeChapter::getReviewStatus, -1)
                .orderByDesc(AnimeChapter::getId).last(" limit 1"));
    }

    @Override
    public Integer getWordByBookId(Long bookId) {
        List<AnimeChapterVo> chapterVoList = this.listChapterByBookId(bookId);
        Integer word = 0;
        for (AnimeChapterVo animeChapterVo : chapterVoList) {
            word += animeChapterVo.getInfo().length();
        }
        return word;
    }
}
