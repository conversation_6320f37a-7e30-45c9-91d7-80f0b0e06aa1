package com.sohu.story.service;


import com.sohu.story.api.vo.AnimeChapterVo;

import java.util.List;

/**
 * 章节Service接口
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
public interface IAnimeChapterService {

    /**
     * 基于小说id查询相关章节列表
     *
     * @param bookId
     * @return
     */
    List<AnimeChapterVo> listChapterByBookId(Long bookId);

    /**
     * 基于书id及章节id查询章节
     *
     * @param bookId
     * @param chapterId
     * @return
     */
    List<AnimeChapterVo> listChapterByBookIdAndChapterId(Long bookId, Long chapterId);

    /**
     * 查询最新章节
     *
     * @param bookId
     * @return
     */
    AnimeChapterVo getNearChapter(Long bookId);

    /**
     * 查询书的总字数
     * @param bookId
     * @return
     */
    Integer getWordByBookId(Long bookId);

}
