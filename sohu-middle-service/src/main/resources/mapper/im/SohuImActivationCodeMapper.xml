<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.im.SohuImActivationCodeMapper">

    <select id="queryPageList"  parameterType="com.sohu.middle.api.bo.im.SohuImActivationCodeBo" resultType="com.sohu.middle.api.vo.im.SohuImActivationCodeVo">
        SELECT
            sac.id,
            sac.activation_code,
            sac.effective_day,
            sac.`status`,
            sat.user_id,
            sa.merchant_name,
            sat.valid_start_time,
            sat.valid_end_time,
            sac.create_time,
            sac.create_by
        FROM
            `sohu_im_activation_code` sac
            LEFT JOIN sohu_im_activation_tenant sat ON sac.id = sat.activation_code_id
            LEFT JOIN sohu_account sa ON sat.user_id = sa.user_id
        where sac.is_del = 0
        <if test="bo.activationCode!=null and bo.activationCode!='' ">
            and sac.activation_code = #{bo.activationCode}
        </if>
        <if test="bo.status!=null">
            and sac.`status` = #{bo.status}
        </if>
        <if test="bo.effectiveDay!=null">
            and sac.effective_day = #{bo.effectiveDay}
        </if>
        <if test="bo.userId!=null">
            and sat.user_id = #{bo.userId}
        </if>
        <if test="bo.merchantName!=null and bo.merchantName!='' ">
            and sa.merchant_name like concat('%',#{bo.merchantName},'%')
        </if>
        <if test="bo.activeStartTime!=null and bo.activeStartTime!='' ">
            and sat.create_time &gt; #{bo.activeStartTime}
        </if>
        <if test="bo.activeEndTime!=null and bo.activeEndTime!='' ">
            and sat.create_time &lt; #{bo.activeEndTime}
        </if>
        <if test="bo.validStartTime!=null and bo.validStartTime!='' ">
            and sat.valid_start_time &gt; #{bo.validStartTime}
        </if>
        <if test="bo.validEndTime!=null and bo.validEndTime!='' ">
            and sat.valid_end_time &lt; #{bo.validEndTime}
        </if>
         order by sac.create_time desc
    </select>
</mapper>