<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.notice.SohuTaskNoticeMapper">

    <resultMap type="com.sohu.middleService.domain.SohuTaskNotice" id="SohuTaskNoticeResult">
        <result property="id" column="id"/>
        <result property="relateId" column="relate_id"/>
        <result property="senderId" column="sender_id"/>
        <result property="receiverId" column="receiver_id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="noticeType" column="notice_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="relateTitle" column="relate_title"/>
        <result property="extValue" column="ext_value"/>
    </resultMap>


</mapper>
