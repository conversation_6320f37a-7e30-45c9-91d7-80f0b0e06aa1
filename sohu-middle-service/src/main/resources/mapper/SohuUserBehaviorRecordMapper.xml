<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuUserBehaviorRecordMapper">

    <resultMap type="com.sohu.middleService.domain.SohuUserBehaviorRecord" id="SohuUserBehaviorRecordResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="operaSource" column="opera_source"/>
        <result property="requestMethod" column="request_method"/>
        <result property="operUrl" column="oper_url"/>
        <result property="operParam" column="oper_param"/>
        <result property="operIp" column="oper_ip"/>
        <result property="operResult" column="oper_result"/>
        <result property="requestId" column="request_id"/>
        <result property="businessType" column="business_type"/>
        <result property="operaType" column="opera_type"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="list" resultType="com.sohu.middle.api.vo.behavior.UserBehaviorRecordVo">
        SELECT
           id,
           user_id,
           opera_source,
           business_type,
           opera_type,
           oper_result,
           create_time,
           source_type,
           event_sign,
           event_name,
           element_no,
           element_name,
           version,
           device_no,
           device_unit_no,
           event_time,
           user_name,
           oper_url,
           page_name
        FROM
            sohu_user_behavior_record
        WHERE
            user_id = #{bo.userId}
        <if test="bo.operaSource != null">
            AND opera_source = #{bo.operaSource}
        </if>
        <if test="bo.businessType != null and bo.businessType != ''">
            AND business_type = #{bo.businessType}
        </if>
        <if test="bo.operaType != null">
            AND opera_type = #{bo.operaType}
        </if>
        <if test="bo.startTime != null">
            AND create_time > #{bo.startTime}
        </if>
    </select>

</mapper>
