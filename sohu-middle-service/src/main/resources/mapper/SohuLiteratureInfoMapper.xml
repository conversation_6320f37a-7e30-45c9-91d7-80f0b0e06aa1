<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuLiteratureInfoMapper">

    <resultMap type="com.sohu.middleService.domain.SohuLiteratureInfo" id="SohuLiteratureInfoResult">
        <result property="id" column="id"/>
        <result property="siteId" column="site_id"/>
        <result property="literatureId" column="literature_id"/>
        <result property="content" column="content"/>
        <result property="viewCount" column="view_count"/>
        <result property="commentCount" column="comment_count"/>
        <result property="praiseCount" column="praise_count"/>
        <result property="collectCount" column="collect_count"/>
        <result property="forwardCount" column="forward_count"/>
    </resultMap>

    <update id="updateViewCount">
        UPDATE sohu_literature_info
        SET view_count = view_count + 1
        WHERE literature_id = #{id}
    </update>


</mapper>
