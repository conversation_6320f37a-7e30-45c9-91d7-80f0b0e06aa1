<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuArticleInfoMapper">

    <resultMap type="com.sohu.middleService.domain.SohuArticleInfo" id="SohuArticleInfoResult">
    <result property="id" column="id"/>
    <result property="siteId" column="site_id"/>
    <result property="articleId" column="article_id"/>
    <result property="content" column="content"/>
    <result property="viewCount" column="view_count"/>
    <result property="commentCount" column="comment_count"/>
    <result property="praiseCount" column="praise_count"/>
    <result property="collectCount" column="collect_count"/>
    <result property="forwardCount" column="forward_count"/>
        <result property="learnNum" column="learn_num"/>
    </resultMap>

    <update id="updateByArticleId">
        UPDATE sohu_article_info
        SET content  = #{content},
            learn_num=#{learnNum}
        WHERE article_id = #{articleId}
    </update>
    <update id="updateViewCount">
        UPDATE sohu_article_info
        SET view_count = view_count + 1,
            learn_num  = learn_num + 1
        WHERE article_id = #{objId}
    </update>

    <select id="selectByArticleId" resultType="com.sohu.middleService.domain.SohuArticleInfo">
        select * from sohu_article_info where article_id = #{articleId}
    </select>

    <select id="selectArticleForCount" resultType="com.sohu.middle.api.vo.SohuArticleInfoVo">
        select id,site_id,article_id,view_count,comment_count,praise_count,collect_count,forward_count,learn_num from sohu_article_info where article_id = #{articleId}
    </select>
</mapper>
