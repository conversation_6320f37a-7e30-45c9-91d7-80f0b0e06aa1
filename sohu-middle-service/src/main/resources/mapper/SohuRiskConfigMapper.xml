<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuRiskConfigMapper">

    <resultMap type="com.sohu.middleService.domain.SohuRiskConfig" id="SohuRiskConfigResult">
        <result property="id" column="id"/>
        <result property="busyType" column="busy_type"/>
        <result property="detectType" column="detect_type"/>
        <result property="fieldName" column="field_name"/>
        <result property="fieldCode" column="field_code"/>
        <result property="passProcess" column="pass_process"/>
        <result property="suspectProcess" column="suspect_process"/>
        <result property="noPassProcess" column="no_pass_process"/>
        <result property="platform" column="platform"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


</mapper>
