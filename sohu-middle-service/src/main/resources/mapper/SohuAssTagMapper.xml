<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuAssTagMapper">
    <resultMap type="com.sohu.middleService.domain.SohuAssTag" id="SohuAssTagResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="siteId" column="site_id"/>
        <result property="name" column="name"/>
        <result property="ident" column="ident"/>
        <result property="hot" column="hot"/>
        <result property="createTime" column="create_time"/>
        <result property="ext" column="ext"/>
        <result property="enName" column="en_name"/>
    </resultMap>
</mapper>
