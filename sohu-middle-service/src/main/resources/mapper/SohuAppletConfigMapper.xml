<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuAppletConfigMapper">
    <resultMap type="com.sohu.middleService.domain.SohuAppletConfig" id="SohuAppletConfigResult">
        <result property="id" column="id"/>
        <result property="siteId" column="site_id"/>
        <result property="name" column="name"/>
        <result property="categoryId" column="category_id"/>
        <result property="ident" column="ident"/>
        <result property="icon" column="icon"/>
        <result property="defaultIcon" column="default_icon"/>
        <result property="overIcon" column="over_icon"/>
        <result property="isDefault" column="is_default"/>
        <result property="enable" column="enable"/>
        <result property="pageUrl" column="page_url"/>
        <result property="json" column="json"/>
        <result property="sort" column="sort"/>
        <result property="deleted" column="deleted"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
</mapper>
