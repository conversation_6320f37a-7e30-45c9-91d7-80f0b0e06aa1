<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.notice.SohuMcnNoticeMapper">

    <resultMap type="com.sohu.pay.api.domain.SohuMcnNotice" id="SohuMcnNoticeResult">
        <result property="id" column="id"/>
        <result property="senderId" column="sender_id"/>
        <result property="receiverId" column="receiver_id"/>
        <result property="relateId" column="relate_id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="noticeType" column="notice_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="nickName" column="nick_name"/>
        <result property="userAvatar" column="user_avatar"/>
        <result property="noticeState" column="notice_state"/>
        <result property="institutionName" column="institution_name"/>
    </resultMap>


</mapper>
