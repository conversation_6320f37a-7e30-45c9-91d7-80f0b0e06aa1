<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuCommentMapper">
    <resultMap type="com.sohu.middleService.domain.SohuComment" id="SohuCommentResult">
        <result property="id" column="id"/>
        <result property="busyType" column="busy_type"/>
        <result property="busyCode" column="busy_code"/>
        <result property="busyUser" column="busy_user"/>
        <result property="commentUser" column="comment_user"/>
        <result property="replyId" column="reply_id"/>
        <result property="replyUser" column="reply_user"/>
        <result property="content" column="content"/>
        <result property="image" column="image"/>
        <result property="commentTime" column="comment_time"/>
        <result property="praiseCount" column="praise_count"/>
        <result property="commentCount" column="comment_count"/>
        <result property="pid" column="pid"/>
        <result property="ipAddress" column="ip_address"/>
        <result property="ip" column="ip"/>
        <result property="state" column="state"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="top" column="top"/>
    </resultMap>
</mapper>
