<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuUserAgreeMapper">

    <resultMap type="com.sohu.middleService.domain.SohuUserAgree" id="SohuUserAgreeResult">
        <result property="id" column="id"/>
        <result property="agreeNumber" column="agree_number"/>
        <result property="type" column="type"/>
        <result property="remark" column="remark"/>
        <result property="state" column="state"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="name" column="name"/>
    </resultMap>
</mapper>
