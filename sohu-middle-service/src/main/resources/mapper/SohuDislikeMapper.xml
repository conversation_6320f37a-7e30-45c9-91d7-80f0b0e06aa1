<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuDislikeMapper">

    <resultMap type="com.sohu.middleService.domain.SohuDislike" id="SohuDislikeResult">
    <result property="id" column="id"/>
    <result property="userId" column="user_id"/>
    <result property="dislikeType" column="dislike_type"/>
    <result property="dislikeId" column="dislike_id"/>
    <result property="count" column="count"/>
    <result property="busyCode" column="busy_code"/>
    <result property="busyType" column="busy_type"/>
    </resultMap>

</mapper>
