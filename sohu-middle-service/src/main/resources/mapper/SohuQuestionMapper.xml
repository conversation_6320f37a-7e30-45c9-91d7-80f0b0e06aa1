<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuQuestionMapper">

    <resultMap type="com.sohu.middleService.domain.SohuQuestion" id="SohuQuestionResult">
        <result property="id" column="id"/>
        <result property="siteId" column="site_id"/>
        <result property="categoryId" column="category_id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="state" column="state"/>
        <result property="type" column="type"/>
        <result property="sortIndex" column="sort_index"/>
        <result property="visibleType" column="visible_type"/>
        <result property="isShare" column="is_share"/>
        <result property="isDownload" column="is_download"/>
        <result property="viewCount" column="view_count"/>
    </resultMap>

    <select id="selectAnswerTop" resultType="com.sohu.middle.api.vo.SohuQuestionAnswerVo">
        SELECT
        a.*,
        q.praise_count AS praiseCount,
        q.comment_count AS commentCount,
        q.view_count AS viewCount,
        q.collect_count AS collectCount,
        q.forward_count AS forwardCount,
        u.avatar authorAvatar,
        u.nick_name authorName
        FROM
        sohu_question_answer a
        LEFT JOIN sys_user u ON a.user_id = u.user_id
        JOIN sohu_qora_info q ON a.id = q.qora_id
        WHERE
        a.question_id = #{bo.questionId}
        AND (
        a.state = 'OnShelf'
        <if test="bo.userId!=null and bo.userId!=''">
            OR (a.user_id = #{bo.userId} and a.state = 'WaitApprove')
        </if>
        )
        AND q.qora_type='Answer'
        GROUP BY a.id
        ORDER BY
        <if test="bo.topId!=null and bo.topId!=''">
            CASE WHEN a.id = #{bo.topId} THEN 0 ELSE 1 END,
        </if>
        q.praise_count DESC,
        q.view_count DESC,
        a.create_time DESC
    </select>

    <select id="questionCollectList" resultType="com.sohu.middle.api.vo.SohuQuestionAnswerVo">
        SELECT
        a.*,q.title AS title ,u.nick_name AS authorName,u.avatar AS authorAvatar,q.state questionState
        FROM
        sohu_user_collect sc
        INNER JOIN sohu_question_answer a ON sc.busy_code = a.id
        INNER JOIN sys_user u ON a.user_id = u.user_id
        INNER JOIN sohu_question q ON q.id = a.question_id
        WHERE
        sc.user_id = #{userId}
        AND sc.busy_type = 'Answer'
        <if test="title != null and title != ''">
            AND q.title LIKE concat( '%',#{title}, '%')
        </if>
        ORDER BY
        sc.create_time DESC
    </select>


    <select id="selectQuestionPages" resultType="com.sohu.middle.api.vo.SohuQuestionVo">
        SELECT
        v.*
        FROM (
        SELECT
        v.*,
        u.avatar authorAvatar,
        u.nick_name authorName,
        <choose>
            <when test="query.currentUserId != null and query.currentUserId > 0">
                <!--我好友的 + #我关注人的好友 -->
                IF(EXISTS(SELECT * FROM sohu_friends f WHERE ((f.user_id = #{query.currentUserId} or f.user_id in
                (SELECT focus_user_id FROM sohu_user_follow WHERE user_id = #{query.currentUserId} ) )
                and f.friend_id = v.user_id ) or ( (f.friend_id = #{query.currentUserId} or f.friend_id in
                (SELECT focus_user_id FROM sohu_user_follow WHERE user_id = #{query.currentUserId} )) and f.user_id =
                v.user_id ))
                <!--我关注的-->
                or EXISTS(SELECT * FROM sohu_user_follow uf WHERE uf.user_id = #{query.currentUserId} and
                uf.focus_user_id = v.user_id)
                <!--有关联作品的-->
                ,IF(EXISTS(SELECT * FROM sohu_qora_relate r WHERE v.id = r.qora_id and r.qora_type = 'Question'),2,1),0)
                as sort
            </when>
            <otherwise>
                0 as sort
            </otherwise>
        </choose>
        FROM sohu_question v LEFT JOIN sys_user u ON v.user_id = u.user_id
        ) v
        where 1=1
        <if test="query.siteId != null and query.type!='lesson'">
            and v.site_id = #{query.siteId}
        </if>
        <if test="query.visibleType != null">
            and v.visible_type = #{query.visibleType}
        </if>
        <if test="query.userId != null">
            and v.user_id = #{query.userId}
        </if>
        <if test="query.isRelate != null and query.isRelate ==false ">
            and v.id not in (select qora_id FROM sohu_qora_relate WHERE qora_type
            ='Question')
        </if>
        <if test="query.countrySiteId != null">
            and v.country_site_id = #{query.countrySiteId}
        </if>
        <if test="query.categoryId != null">
            and v.category_id = #{query.categoryId}
        </if>
        <if test="query.title != null and query.title != ''">
            and v.title like concat('%',#{query.title},'%')
        </if>
        <if test="query.content != null and query.content != ''">
            and v.content like concat('%',#{query.content},'%')
        </if>
        <if test="query.state != null and query.state != ''">
            and v.state = #{query.state}
        </if>
        <if test="query.type != null and query.type != ''">
            and v.type = #{query.type}
        </if>
        <if test="query.lessonLabelId != null ">
            and v.lesson_label_id = #{query.lessonLabelId}
        </if>
        <if test="query.stateList != null and query.stateList.size > 0">
            and v.state in
            <foreach item="item" index="index" collection="query.stateList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.notIds != null and query.notIds.size > 0">
            and v.id not in
            <foreach item="item" index="index" collection="query.notIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.notUserIds != null and query.notUserIds.size > 0">
            and v.user_id not in
            <foreach item="item" index="index" collection="query.notUserIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.ids != null and query.ids.size > 0">
            and v.id in
            <foreach item="item" index="index" collection="query.ids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.startDate != null ">
            and v.create_time >= #{query.startDate}
        </if>
        <if test="query.endDate != null ">
            and v.create_time &lt;= #{query.endDate}
        </if>
        ORDER BY v.sort_index ASC,v.create_time DESC
    </select>

    <select id="businessQuestionList" resultType="com.sohu.middle.api.vo.SohuQuestionVo">
        SELECT sq.*, sr.busy_code, sr.busy_title, sr.busy_type
        FROM sohu_question sq
        INNER JOIN sohu_qora_relate sr ON sq.id = sr.qora_id AND sr.qora_type = 'Question'
        WHERE sq.state = 'OnShelf'
        <if test="bo.siteId!=null">
            AND sq.site_id = #{bo.siteId}
        </if>
        <if test="bo.categoryId!=null">
            AND sq.category_id = #{bo.categoryId}
        </if>
        <if test="bo.searchKey != null and bo.searchKey != ''">
            and sq.title like concat('%',#{bo.searchKey},'%')
        </if>
        ORDER BY sq.id DESC
    </select>

    <select id="queryPageListStat" resultType="com.sohu.middle.api.vo.SohuConentListStatVo">
        SELECT
        SUM(CASE WHEN state ='OnShelf' THEN 1 ELSE 0 END) AS onShelfNum,
        SUM(CASE WHEN state IN ('OffShelf','CompelOff') THEN 1 ELSE 0 END) AS offShelfNum,
        SUM(CASE WHEN state ='WaitApprove' THEN 1 ELSE 0 END) AS waitApproveNum,
        SUM(CASE WHEN state ='Refuse' THEN 1 ELSE 0 END) AS refuseNum,
        SUM(CASE WHEN appeal_status =1 THEN 1 ELSE 0 END) AS appealNum
        FROM sohu_question
        ${ew.getCustomSqlSegment}
    </select>
    <select id="getTopicList" resultType="com.sohu.middle.api.vo.SohuQuestionVo">
        SELECT
        sq.*
        FROM sohu_topic_content_relation stcr
        INNER JOIN sohu_question sq ON sq.id = stcr.content_id
        INNER JOIN sohu_qora_info sqr ON sqr.qora_id = sq.id
        WHERE sq.state = 'OnShelf' AND sq.audit_state = 'Pass'
        AND stcr.del_flag = 0
        AND stcr.content_type = 'Question'
        AND sqr.qora_type = 'Question'
        <if test="bo.topicPid!=null and bo.topicPid!=''">
            AND stcr.topic_pid = #{bo.topicPid}
        </if>
        <if test="bo.topicId!=null and bo.topicId!=''">
            AND stcr.topic_id = #{bo.topicId}
        </if>
        GROUP BY sq.id
        <if test="bo.isSortByViewCount!=null and bo.isSortByViewCount!=''">
            ORDER BY sqr.view_count DESC
        </if>
    </select>
    <select id="queryPageListOfOnShelf" resultType="com.sohu.middle.api.vo.SohuQuestionVo">
        SELECT
        sq.*,
        su.user_id AS su_user_id,
        su.nick_name
        FROM
        sohu_question sq
        LEFT JOIN sys_user su ON sq.user_id = su.user_id
        <where>
            sq.del_flag = '0'
            <if test="bo.id != null">
                AND sq.id = #{bo.id}
            </if>
            <if test="bo.state != null">
                AND sq.state = #{bo.state}
            </if>
            <if test="bo.visibleType != null">
                AND sq.visible_type = #{bo.visibleType}
            </if>
            <if test="bo.categoryIds != null and bo.categoryIds.size() > 0">
                AND sq.category_id IN
                <foreach item="categoryId" collection="bo.categoryIds" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>
            <if test="bo.title != null and bo.title != ''">
                AND sq.title LIKE CONCAT('%', #{bo.title}, '%')
            </if>

            <if test="bo.nickName != null and bo.nickName != ''">
                AND su.nick_name LIKE CONCAT('%', #{bo.nickName}, '%')
            </if>
        </where>
    </select>
</mapper>
