package com.sohu.middleService.service.airec;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.airec.SohuAirecProductBo;
import com.sohu.middle.api.vo.airec.SohuAirecProductVo;
import com.sohu.middleService.domain.airec.SohuAirecProduct;
import com.sohu.middleService.service.ISohuBaseService;

import java.util.List;

/**
 * 智能推荐投流商品Service接口
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface ISohuAirecProductService extends ISohuBaseService<SohuAirecProduct,SohuAirecProductVo> {

//    /**
//     * 查询智能推荐投流商品
//     */
//    SohuAirecProductVo queryById(Long id);
//
    /**
     * 查询智能推荐投流商品列表
     */
    TableDataInfo<SohuAirecProductVo> queryPageList(SohuAirecProductBo bo, PageQuery pageQuery);

    /**
     * 查询智能推荐投流商品列表
     */
    List<SohuAirecProductVo> queryList(SohuAirecProductBo bo);

    /**
     * 修改智能推荐投流商品
     */
    Boolean insertByBo(SohuAirecProductBo bo);

    /**
     * 修改智能推荐投流商品
     */
    Boolean updateByBo(SohuAirecProductBo bo);
//
//    /**
//     * 校验并批量删除智能推荐投流商品信息
//     */
//    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
