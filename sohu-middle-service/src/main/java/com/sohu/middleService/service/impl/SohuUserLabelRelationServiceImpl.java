package com.sohu.middleService.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuBindUserLabelBo;
//import com.sohu.middle.api.bo.SohuSaveUserLabelRelationBo;
import com.sohu.middle.api.bo.SohuUserLabelRelationBo;
import com.sohu.middle.api.enums.LabelEnum;
import com.sohu.middle.api.service.RemoteIndustryCategoryService;
import com.sohu.middle.api.service.RemoteMiddleAdInfoService;
import com.sohu.middle.api.service.RemoteMiddleCommonLabelService;
import com.sohu.middle.api.vo.SohuAdInfoVo;
import com.sohu.middle.api.vo.SohuIndustryCategoryVo;
import com.sohu.middle.api.vo.SohuUserLabelRelationVo;
import com.sohu.middleService.domain.SohuCommonLabel;
import com.sohu.middleService.domain.SohuUserLabelRelation;
import com.sohu.middleService.mapper.SohuCommonLabelMapper;
import com.sohu.middleService.mapper.SohuUserLabelRelationMapper;
import com.sohu.middleService.service.ISohuUserLabelRelationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.formula.atp.Switch;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户标签关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuUserLabelRelationServiceImpl implements ISohuUserLabelRelationService {

    private final SohuUserLabelRelationMapper baseMapper;
    private final SohuCommonLabelMapper sohuCommonLabelMapper;

    @DubboReference
    private RemoteIndustryCategoryService remoteIndustryCategoryService;
    @DubboReference
    private RemoteMiddleCommonLabelService remoteMiddleCommonLabelService;
    @DubboReference
    private RemoteMiddleAdInfoService remoteMiddleAdInfoService;

    @Override
    public Map<Long, Long> queryRelationUserCount(List<Long> labelIds) {
        if (labelIds == null || labelIds.isEmpty()) {
            return new HashMap<>();
        }
        List<Map<String, Object>> maps = baseMapper.queryRelationUserCount(labelIds);

        return maps.stream().collect(Collectors.toMap(
                row -> Long.parseLong(row.get("labelId").toString()),
                row -> Long.parseLong(row.get("userCount").toString())
        ));
    }

    @Override
    public void batchInsert(List<SohuUserLabelRelationBo> boList) {
        if (boList == null || boList.isEmpty()) {
            return;
        }
        Long userId = boList.get(0).getUserId();
        List<SohuUserLabelRelationVo> userLabelRelationVoList = queryLabelsByUserId(userId);
        if (userLabelRelationVoList != null && !userLabelRelationVoList.isEmpty()) {
            // 按标签类型分组
            Map<String, List<SohuUserLabelRelationVo>> existingLabelMap = userLabelRelationVoList.stream()
                    .collect(Collectors.groupingBy(SohuUserLabelRelationVo::getLabelType));

            boList = boList.stream()
                    .filter(bo -> {
                        List<SohuUserLabelRelationVo> existingLabels = existingLabelMap.get(bo.getLabelType());
                        // 检查当前标签的ID是否已存在
                        return existingLabels == null || existingLabels.stream()
                                .noneMatch(existing -> existing.getLabelId().equals(bo.getLabelId()));
                    })
                    .collect(Collectors.toList());
        }
        baseMapper.insertBatch(BeanCopyUtils.copyList(boList, SohuUserLabelRelation.class));
    }

    @Override
    public List<SohuUserLabelRelationVo> queryLabelsByUserId(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        List<SohuUserLabelRelationVo> userLabelRelationVoList = baseMapper.selectVoList(Wrappers.<SohuUserLabelRelation>lambdaQuery().in(SohuUserLabelRelation::getUserId, userId), SohuUserLabelRelationVo.class);
        if (userLabelRelationVoList == null || userLabelRelationVoList.isEmpty()) {
            return new ArrayList<>();
        }
        ArrayList<Long> labelIds = new ArrayList<>();
        ArrayList<Long> idnustryLabelIds = new ArrayList<>();
        for (SohuUserLabelRelationVo userLabelRelationVo : userLabelRelationVoList) {
            if (userLabelRelationVo.getLabelType().equals(LabelEnum.COMMON.getCode())) {
                labelIds.add(userLabelRelationVo.getLabelId());
            }
            if (userLabelRelationVo.getLabelType().equals(LabelEnum.INDUSTRY.getCode())) {
                idnustryLabelIds.add(userLabelRelationVo.getLabelId());
            }
        }
        for (SohuUserLabelRelationVo userLabelRelationVo : userLabelRelationVoList) {
            if (userLabelRelationVo.getLabelType().equals(LabelEnum.COMMON.getCode())) {
                Map<Long, String> labelNamesByIds = remoteMiddleCommonLabelService.queryLabelNamesByIds(labelIds);
                if (CollUtil.isNotEmpty(labelNamesByIds)) {
                    userLabelRelationVo.setLabelName(labelNamesByIds.get(userLabelRelationVo.getLabelId()));
                }
            }
            if (userLabelRelationVo.getLabelType().equals(LabelEnum.INDUSTRY.getCode())) {
                Map<Long, SohuIndustryCategoryVo> industryCategoryVoMap = remoteIndustryCategoryService.queryMap(idnustryLabelIds);
                if (CollUtil.isNotEmpty(industryCategoryVoMap) && industryCategoryVoMap.containsKey(userLabelRelationVo.getLabelId())) {
                        userLabelRelationVo.setLabelName(industryCategoryVoMap.get(userLabelRelationVo.getLabelId()).getIndustryName());
                }
            }
        }

        return userLabelRelationVoList;
    }

    @Override
    public void deleteUserLabelByUserId(Long userId, String labelType) {
        // 根据用户id删除用户标签关联
        if (userId == null) {
            return;
        }
        List<SohuUserLabelRelation> sohuUserLabelRelationList = queryLabelsByUserIdAndLabelType(userId, labelType);
        if (CollUtil.isEmpty(sohuUserLabelRelationList)) {
            return;
        }
        baseMapper.deleteBatchIds(sohuUserLabelRelationList);
    }

    /**
     * 根据用户id以及标签类型获取标签id集合
     *
     * @param userId    用户id
     * @param labelType 标签类型
     * @return List<SohuUserLabelRelation>
     */
    private List<SohuUserLabelRelation> queryLabelsByUserIdAndLabelType(Long userId, String labelType) {
        LambdaQueryWrapper<SohuUserLabelRelation> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuUserLabelRelation::getUserId, userId);
        if (labelType != null) {
            lqw.eq(SohuUserLabelRelation::getLabelType, labelType);
        }
        List<SohuUserLabelRelation> sohuUserLabelRelations = baseMapper.selectList(lqw);
        return CollUtil.isEmpty(sohuUserLabelRelations) ? new ArrayList<>() : sohuUserLabelRelations;
    }

    @Override
    public Boolean bindUserLabelWithClick(SohuBindUserLabelBo bo) {
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        // 2. 根据业务类型处理标签绑定
        switch (bo.getBusyType()) {
            case "GameNovel":
                return handleGameNovelLabel(userId, "娱乐");
            case "AdInfo":
                return handleAdInfoLabel(userId, bo.getBusyCode());
            default:
                log.warn("Unsupported business type: {}", bo.getBusyType());
                return Boolean.FALSE;
        }
    }

    /**
     * 处理广告关联用户标签
     *
     * @param userId   用户id
     * @param busyCode 广告id
     * @return Boolean
     */
    private Boolean handleAdInfoLabel(Long userId, Long busyCode) {
        // 反查广告关联用户标签
        SohuAdInfoVo sohuAdInfoVo = remoteMiddleAdInfoService.queryById(busyCode);
        if (Objects.isNull(sohuAdInfoVo)) {
            throw new ServiceException(MessageUtils.message("广告不存在,请联系管理员"));
        }
        List<SohuUserLabelRelationBo> userLabelList = new ArrayList<>();
        if (CollUtil.isNotEmpty(sohuAdInfoVo.getLabelIdList())) {
            sohuAdInfoVo.getLabelIdList().forEach(labelId -> userLabelList.add(createUserLabelRelationBo(userId, labelId)));
            this.batchInsert(userLabelList);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 处理游戏小说关联用户标签
     *
     * @param userId    用户id
     * @param labelName 标签名称
     * @return Boolean
     */
    private Boolean handleGameNovelLabel(Long userId, String labelName) {
        // 通过标签名称匹配标签id并绑定给用户
        Long lableId = sohuCommonLabelMapper.findIdByLabelName(labelName);
        if (lableId == null) {
            return Boolean.FALSE;
        }
        List<SohuUserLabelRelationBo> userLabelList = new ArrayList<>();
        SohuUserLabelRelationBo userLabelRelationBo = createUserLabelRelationBo(userId, lableId);
        userLabelList.add(userLabelRelationBo);
        this.batchInsert(userLabelList);
        return Boolean.TRUE;
    }

    /**
     * 创建用户标签关联对象
     *
     * @param userId  用户id
     * @param labelId 标签id
     * @return 用户标签关联对象
     */
    private SohuUserLabelRelationBo createUserLabelRelationBo(Long userId, Long labelId) {
        SohuUserLabelRelationBo userLabel = new SohuUserLabelRelationBo();
        userLabel.setUserId(userId);
        userLabel.setLabelId(labelId);
        userLabel.setLabelType(LabelEnum.COMMON.getCode());
        return userLabel;
    }

}
