package com.sohu.middleService.service;

import com.sohu.common.core.domain.BaseCommonBo;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.vo.UserBaseVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.playlet.*;
import com.sohu.middle.api.vo.SohuPlayletPayVo;
import com.sohu.middle.api.vo.SohuPlayletVo;
import com.sohu.middle.api.vo.SohuTopPlayletVo;
import com.sohu.middle.api.vo.SohuVideoVo;
import com.sohu.middle.api.vo.playlet.*;
import com.sohu.middleService.domain.SohuPlaylet;

import java.util.Collection;
import java.util.List;

/**
 * 短剧首页分类Service接口
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
public interface ISohuPlayletService extends ISohuBaseService<SohuPlaylet, SohuPlayletVo> {

    /**
     * 查询短剧首页分类
     */
    SohuPlayletVo queryById(Long id);

    /**
     * 通过短剧标识查询短剧
     *
     * @param episodeRelevance 短剧标识
     * @return
     */
    SohuPlayletVo queryByEpisodeRelevance(String episodeRelevance);

    /**
     * 查询短剧首页分类列表
     */
    TableDataInfo<SohuPlayletVo> queryPageList(SohuPlayletBo bo, PageQuery pageQuery);

    /**
     * 查询短剧首页分类列表
     */
    List<SohuPlayletVo> queryList(SohuPlayletBo bo);

    /**
     * 修改短剧首页分类
     */
    Boolean insertByBo(SohuPlayletBo bo);

    /**
     * 修改短剧首页分类
     */
    Boolean updateByBo(SohuPlayletBo bo);

    /**
     * 校验并批量删除短剧首页分类信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

//    /**
//     * 更新内容短剧物料
//     */
//    void updateSohuAirecContentPlayletItem(SohuPlaylet playlet);

    /**
     * 短剧播放量最高列表，前三
     *
     * @param categoryId 短剧分类ID
     * @param sysSource
     * @return {@link List}
     */
    List<SohuPlayletVo> hotPlaylet(Long categoryId, String sysSource);

    /**
     * 短剧播放量最高列表，前三
     *
     * @param categoryId 短剧分类ID
     * @param limit
     * @return {@link List}
     */
    List<SohuPlayletVo> hotPlaylet(Long categoryId, int limit);

    /**
     * 短剧播放量最高列表，分页
     *
     * @param categoryId 短剧分类ID集合，英文逗号隔开
     * @param pageQuery PageQuery
     * @return TableDataInfo<SohuPlayletVo>
     */
//    TableDataInfo<SohuPlayletVo> hotPlaylet(Long categoryId, PageQuery pageQuery);

    /**
     * 最热门
     *
     * @param categoryIds 短剧分类ID集合，英文逗号隔开
     * @param limit
     * @return
     */
    List<SohuPlayletVo> hotPlaylet(String categoryIds, String sysSource, int limit);

    /**
     * 新增剧集
     */
    Boolean addPlayLet(SohuPlayletBo bo);

    /**
     * 剧集详情展开列表
     */
    TableDataInfo<SohuPlayletVo> sohuPlayletVoList(SohuPlayletBo bo, PageQuery pageQuery);

    /**
     * 剧集管理列表展开
     */
    TableDataInfo<SohuVideoVo> listVideos(String id, PageQuery pageQuery);

    /**
     * 删除剧集
     */
    Boolean deletePlayLet(Long id);

    /**
     * 审核剧集
     */
    Boolean auditPlayLet(SohuPlayletBo bo);

    /**
     * 剧集上下架
     */
    SohuPlaylet updateState(SohuPlayletBo bo);

    /**
     * 单分类列表播放量最高前10条
     *
     * @param sysSource
     * @param categoryId 分类ID
     * @return {@link List}
     */
    List<SohuPlayletVo> hotList(String sysSource, Long categoryId);

    /**
     * 所有分类列表播放量最高前10条
     *
     * @param sysSource
     * @param siteId    站点ID
     * @return {@link List}
     */
    List<SohuTopPlayletVo> categoryTopTen(String sysSource, Long siteId);

    /**
     * 单个短剧分类列表
     *
     * @param bo
     * @return {@link TableDataInfo}
     */
    TableDataInfo<SohuPlayletVo> pagePlay(SohuAppPlayletBo bo);

    /**
     * top10
     *
     * @param sysSource
     * @param siteId    站点ID
     * @return {@link List}
     */
    List<SohuPlayletVo> topTen(String sysSource, Long siteId);

    /**
     * 搜索剧
     *
     * @return {@link List}
     */
    TableDataInfo<SohuPlayletVo> searchPlaylet(SohuSearchPlayletBo bo);


    /**
     * 剧集详情
     */
    SohuPlayletVo getPlayletInfo(String episodeRelevance, Integer episodeNumber);

    /**
     * 初始化智能推荐物料
     */
    Boolean initAirecContentItems();

    /**
     * 查询需要付费的视频数量
     *
     * @param episodeRelevance 短剧标识
     * @return
     */
    Long needPayVideoCount(String episodeRelevance);

    /**
     * 查询用户需要付费的视频数量，要排除已经付费的视频
     *
     * @param episodeRelevance 短剧标识
     * @param userId           付款用户ID
     * @return
     */
    Long userNeedPayVideoCount(String episodeRelevance, Long userId);

    /**
     * 短剧收藏列表
     *
     * @param bo
     * @return {@link TableDataInfo}
     */
    TableDataInfo<SohuVideoVo> listCollect(SohuSearchPlayletBo bo);

    /**
     * 短剧购买记录
     *
     * @param bo
     * @return
     */
    TableDataInfo<SohuPlayletPayVo> purchaseList(SohuSearchPlayletBo bo);

    /**
     * 投流短剧
     */
    TableDataInfo<SohuPlayletVo> playletPageContentCenter(String title, PageQuery pageQuery);

    /**
     * 获取分销短剧列表
     */
    TableDataInfo<SohuPlayletVo> getDistributionPlayletList(SohuPlayletBo bo, PageQuery pageQuery);

    /**
     * 根据短剧用户id获取短剧列表
     *
     * @param playletUserId 短剧用户id
     * @param fansUserId    粉丝id
     * @return List<PlayletFollowListVo>
     */
    List<PlayletFollowListVo> getPublishPlayletListByPlayletUserId(Long playletUserId, Long fansUserId);

    /**
     * 短剧支付列表
     *
     * @param uuid
     * @param pageQuery
     * @return
     */
    TableDataInfo<PlayletPayedVo> playletPayedList(String uuid, PageQuery pageQuery);

    R<Boolean> replace(String episodeRelevance, String url);

    R<Boolean> replaceAll();

    TableDataInfo<PlayletSearchVo> recommendList(String sysSource, PageQuery query);

    /**
     * 短剧小黄车剧集列表
     */
    TableDataInfo<PlayletCartListVo> playletList(PlayletPlayletCartQueryBo bo, PageQuery pageQuery);

    /**
     * 短剧小黄车剧集列表(商品)
     */
    List<PlayletCartShopListVo> playletCartList(PlayletPlayletCartQueryBo bo);

    /**
     * 初始化一部短剧
     *
     * @param episodeRelevance 短剧标识
     * @param num              要添加的集数
     * @param preUrl
     * @return
     */
    Boolean initPlaylet(String episodeRelevance, int num, String preUrl);

    /**
     * 点赞短剧
     *
     * @return {@link Boolean}
     */
    Boolean like(SohuBusyBO bo);

    /**
     * 收藏短剧
     *
     * @return {@link Boolean}
     */
    Boolean collect(SohuBusyBO bo);

    /**
     * 评论短剧
     *
     * @return {@link Boolean}
     */
    Boolean comment(SohuCommentBo bo);

    /**
     * 获取剧集草稿箱
     */
    SohuPlayletVo getEditInfo();

    /**
     * 获取短剧管理列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<PlayletDetailListVo> queryAdminList(PlayletDetailQueryBo bo, PageQuery pageQuery);

    /**
     * 查询短剧，根据时间倒序
     *
     * @param limit
     * @return
     */
    List<SohuPlayletVo> listDesc(int limit);

    /**
     * 订阅短剧,true表示订阅成功
     *
     * @param episodeRelevance 短剧标识
     * @param commonBo
     */
    Boolean subscribe(String episodeRelevance, BaseCommonBo commonBo);

    /**
     * app ,分页查询更多短剧
     *
     * @param commonBo
     * @param bo
     * @return
     */
    TableDataInfo<PlayletHomeVo> appPlayletPageList(BaseCommonBo commonBo, PlayletMoreListBo bo);

    /**
     * 整部短剧的详情信息
     *
     * @param episodeRelevance 短剧唯一标识
     * @return PlayletDetailInfoVo
     */
    PlayletDetailInfoVo getPlayletDetailInfo(String episodeRelevance);

    List<SohuPlayletVo> ListPlaylet(Long categoryId, int limit);

    /**
     * 首选短剧
     *
     * @param sysSource
     * @param limit
     * @return
     */
    List<SohuPlayletVo> homePreferencePlaylet(String sysSource, int limit);

    /**
     * 随机获取短剧列表
     *
     * @return {@link List}
     */
    List<SohuPlayletVo> homeLatestPublishPlaylet(String sysSource);

    /**
     * 首页轮播图列表
     *
     * @return {@link List}
     */
    List<PlayletBannerVo> bannerList(String minglereels);

    /**
     * 短剧热门作者
     *
     * @return {@link List}
     */
    List<UserBaseVo> hotPlayletAuthorList();


    /**
     * 短剧搜索接口
     *
     * @param bo
     * @return
     */
    TableDataInfo<PlayletSearchVo> searchPlayletList(PlayletSearchBo bo);

    /**
     * 海外短剧转换
     *
     * @param playletVos
     * @return {@link List}
     */
    List<PlayletHomeVo> convertPlayletHomeVo(List<SohuPlayletVo> playletVos);

    /**
     * APP-短剧接口,返回二维数组结构
     *
     * @return {@link List}
     */
    List<AppPlayletHomeVo> appPlayletList(BaseCommonBo commonBo);

    /**
     * 免费短剧
     *
     * @param limit
     * @return
     */
    List<SohuPlayletVo> freePlaylet(int limit);

    /**
     * 查询目前剧已挂载小黄车数量
     *
     * @param playletId
     * @return
     */
    Integer selectCartCount(Long playletId);

    /**
     * 查询目前集已挂载小黄车数量
     *
     * @param videoId
     * @return
     */
    Integer selectCartVideoCount(Long videoId);

    /**
     * 查询目前集已挂载广告数量
     *
     * @param type
     * @return
     */
    Long selectAdsCount(String type, Long videoId);

    /**
     * 短剧订阅订单
     */
    TableDataInfo<PlayletOrderVo> queryOrderList(PlayletOrderBo bo, PageQuery pageQuery);

    /**
     * 导出短剧订阅订单
     */
    List<PlayletOrderVo> exportOrder(PlayletOrderBo bo);

    /**
     * 查询花絮
     */
    List<SohuVideoVo> getMaterialsInfo(Long playletId);

    /**
     * 修改花絮
     */
    Boolean updateMaterials(PlayletMaterialsBo bo);

    /**
     * 基于状态统计相关短剧数量
     *
     * @param state
     * @return
     */
    Long countByState(String state);

    /**
     * 基于时间统计相关数量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    Long countByTime(String startTime, String endTime);

    /**
     * 通过id与状态查询短剧
     * @param ids
     * @param state
     * @return
     */
    List<SohuPlayletVo> queryPlayletByIds(List<Long> ids,String state);

    /**
     * 机审审核结果处理
     *
     * @param busyCode
     * @param isPass
     * @param reason
     */
    void handleRobot(String busyCode, Boolean isPass, String reason);
}
