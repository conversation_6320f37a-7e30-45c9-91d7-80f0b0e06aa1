package com.sohu.middleService.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sohu.middle.api.bo.SohuTopicContentQueryBo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.vo.SohuConentListStatVo;
import com.sohu.middle.api.vo.SohuQuestionVo;
import com.sohu.middle.api.vo.SohuTopQuestionVo;
import com.sohu.middleService.domain.SohuQuestion;

import java.util.Collection;
import java.util.List;

/**
 * 问题主体Service接口
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
public interface ISohuQuestionService extends ISohuBaseService<SohuQuestion, SohuQuestionVo> {

    /**
     * 获取对象的作者id
     *
     * @param id 对象主键id
     * @return 返回对象的作者id，不存在就返回0
     */
    Long getAuthorId(Long id);

    /**
     * 查询问题主体
     */
    SohuQuestionVo get(Long id);

    /**
     * 查询问题主体
     */
    SohuQuestionVo queryById(Long id, Boolean type);

    /**
     * 查询问题主体
     */
    SohuQuestionVo selectVoById(Long id);

    /**
     * 查询问题主体列表
     */
    TableDataInfo<SohuQuestionVo> queryPageList(SohuQuestionBo bo, PageQuery pageQuery);

    /**
     * 查询问题主体列表-统计
     * @param bo
     * @return
     */
    SohuConentListStatVo queryPageListStat(SohuQuestionBo bo);

    /**
     * 查询问题主体列表
     */
    List<SohuQuestionVo> queryList(SohuQuestionBo bo);

    /**
     * 修改问题主体
     */
    Boolean insertByBo(SohuQuestionBo bo);

    /**
     * 修改问题主体
     */
    Boolean updateByBo(SohuQuestionBo bo);
//
//    /**
//     * 校验并批量删除问题主体信息
//     */
//    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 问题草稿提交至审核
     *
     * @param id 问题ID
     */
    @Deprecated
    void commit(Long id);

    /**
     * 問題列表
     */
    TableDataInfo<SohuQuestionVo> questionPageList(SohuQuestionBo bo, PageQuery pageQuery);

    /**
     * 問題列表-智能推荐
     */
    TableDataInfo<SohuQuestionVo> questionPageListOfAirec(SohuQuestionBo bo, PageQuery pageQuery);

    /**
     * 个人中心问题列表
     */
    TableDataInfo<SohuQuestionVo> questionPageContentCenter(Long userId, PageQuery pageQuery);

    /**
     * 问答收藏列表
     */
    TableDataInfo<SohuQuestionVo> questionCollectList(String busyTitle, PageQuery pageQuery);

    /**
     * 获取MCN问答点赞总数
     */
    Long getMcnQuestionPraiseStat(Long userId, Long mcnId);

    /**
     * 获取MCN问答评论总数
     */
    Long getMcnQuestionCommentStat(Long userId, Long mcnId);

    /**
     * 获取MCN问答浏览量
     */
    Long getMcnQuestionViewStat(Long userId, Long mcnId);

    /**
     * 赚钱问答列表
     *
     * @param bo
     * @return
     */
    IPage<SohuQuestionVo> businessQuestionList(SohuBusinessQuestionBo bo);

    /**
     * 赚钱问答列表-智能推荐
     *
     * @param bo
     * @return
     */
    TableDataInfo<SohuQuestionVo> businessQuestionListOfAirec(SohuBusinessQuestionBo bo);

    /**
     * 更新内容问答物料
     */
    void updateSohuAirecContentQuestionItem(SohuQuestion question);

//    /**
//     * 推荐问题列表
//     */
//    TableDataInfo<SohuQuestionVo> getSohuAirecContentQuestion(SohuQuestionBo sohuQuestionBo);

    /**
     * 初始化智能推荐物料
     */
    Boolean initAirecContentItems();

    /**
     * 返回每个标签下前五的问题
     * 双层数组结构返回
     */
    List<SohuTopQuestionVo> labelTopFive();

    /**
     * 投流问答
     */
    TableDataInfo<SohuQuestionVo> questionPageCenterByType(SohuQuestionBo bo, PageQuery pageQuery);

    /**
     * 草稿重发
     *
     * @param busyBO
     * @return {@link Boolean}
     */
    @Deprecated
    Boolean draftRetry(SohuBusyBO busyBO);

    /**
     * 转发问题
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean forward(SohuBusyBO bo);

    /**
     * 修改用户所有问题的状态，删除的话是假删除
     *
     * @param userId 用户ID
     * @param state  {@link com.sohu.common.core.enums.CommonState}
     * @return {@link Boolean}
     */
    @Deprecated
    Boolean updateUserQuestionState(Long userId, String state);

    /**
     * 修改问题状态
     *
     * @param bo
     * @return {@link Boolean}
     */
    @Deprecated
    Boolean updateState(SohuBusyUpdateStateBo bo);

    /**
     * 点赞
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean like(SohuBusyBO bo);

    /**
     * 收藏
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean collect(SohuBusyBO bo);

    /**
     * 站点内容数
     */
    Long queryQuestionNumBySite(Long siteId, Long userId);

    /**
     * 问答发送消息
     */
    void sendQuestionInteractNotice(Long answerId);

    /**
     * 更新问题浏览量
     */
    void updateViewCountJobHandler();

    /**
     * 批量修改作品状态
     *
     * @param bo SohuContentBatchBo
     * @return Boolean
     */
    @Deprecated
    Boolean updateBatchContentState(SohuContentBatchBo bo);

    /**
     * 提交草稿至审核
     * @param id
     * @return
     */
    Boolean submitAudit(Long id);

    /**
     * 图文下架-自主下架
     *
     * @param id
     * @return
     */
    boolean updateOffShelfById(Long id);

    /**
     * 图文下架-强制下架
     *
     * @param id
     * @return
     */
    boolean updateCompelOffById(SohuContentRefuseBo bo);

    /**
     * 从回收站恢复数据
     * @param id
     * @return
     */
    Boolean recoveryData(Long id);

    /**
     * 审核上架
     * @param id
     * @return
     */
    Boolean auditOnShelf(Long id);

    /**
     * 审核拒绝
     * @param id
     * @param rejectReason
     * @return
     */
    Boolean auditRefuse(Long id,String rejectReason);

    /**
     * 用户申述
     * @return
     */
    Boolean userAppeal(SohuUserContentAppealBo bo);

    /**
     * 隐藏数据
     * @param id
     * @return
     */
    Boolean hideData(Long id);

    /**
     * 更新图文只能推荐物料
     *
     * @param question
     */
    void updateSohuAirecContentArticleItem(SohuQuestion question);

    /**
     * 用户自主删除
     *
     * @param id
     * @return
     */
    Boolean logicDeleteById(Long id);

    /**
     * 图文逻辑删除-自主删除-批量
     *
     * @param ids
     * @return
     */
    boolean logicDeleteById(Collection<Long> ids);

    /**
     * 图文逻辑删除-强制删除
     *
     * @param id
     * @return
     */
    boolean logicForceDeleteById(Long id);

    /**
     * 图文逻辑删除-强制删除-批量
     *
     * @param ids
     * @return
     */
    boolean logicForceDeleteById(Collection<Long> ids);

    /**
     * 从回收站删除数据
     * @param id
     * @return
     */
    Boolean deleteDataById(Long id);

    /**
     * 清空回收站数据-过期
     * @return
     */
    Boolean clearRecycleDataOfTimeOut();

    /**
     * 隐藏数据
     * @param ids
     * @return
     */
    Boolean hideDataBatch(Collection<Long> ids);

    /**
     * 查询问答列表-上架
     */
    TableDataInfo<SohuQuestionVo> queryPageListOfOnShelf(SohuQuestionBo bo, PageQuery pageQuery);

    /**
     * 查询专题内容列表
     */
    TableDataInfo<SohuQuestionVo> getTopicList(SohuTopicContentQueryBo bo, PageQuery pageQuery);

    /**
     * 处理机审结果
     * @param busyCode
     * @param isPass
     * @param reason
     */
    void handleRobot(String busyCode, Boolean isPass, String reason);
}
