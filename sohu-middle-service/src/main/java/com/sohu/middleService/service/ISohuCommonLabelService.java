package com.sohu.middleService.service;


import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuCommonLabelBo;
import com.sohu.middle.api.bo.SohuCommonLabelListBo;
import com.sohu.middle.api.vo.SohuCommonLabelListVo;
import com.sohu.middle.api.vo.SohuCommonLabelVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 通用标签Service接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface ISohuCommonLabelService {

    /**
     * 查询通用标签列表
     */
    List<SohuCommonLabelListVo> queryPageList();

    /**
     * 查询通用标签列表
     */
    TableDataInfo<SohuCommonLabelVo> queryPageList(SohuCommonLabelListBo bo, PageQuery pageQuery);

    /**
     * 修改通用标签
     */
    Boolean insertByBo(SohuCommonLabelBo bo);

    /**
     * 修改通用标签
     */
    Boolean updateByBo(SohuCommonLabelBo bo);

    /**
     * 校验并批量删除通用标签信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据id查询标签名称
     *
     * @param ids 标签id列表
     * @return 标签名称Map
     */
    Map<Long, String> queryLabelNamesByIds(List<Long> ids);

    /**
     * 根据id查询标签名称列表
     *
     * @param ids 标签id列表
     * @return 标签名称列表
     */
    List<SohuCommonLabelListVo> queryLabelNameListByIds(List<Long> ids);
}
