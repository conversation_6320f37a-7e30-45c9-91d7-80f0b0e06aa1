package com.sohu.middleService.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.im.api.vo.SohuImGroupVo;
import com.sohu.middle.api.bo.notice.SohuSystemNoticeBo;
import com.sohu.middle.api.vo.notice.SohuSystemNoticeVo;

import java.util.Collection;
import java.util.List;

/**
 * 系统通知消息Service接口
 *
 * <AUTHOR>
 * @date 2024-04-10
 */
public interface ISohuSystemNoticeService {

    /**
     * 查询系统通知消息
     */
    SohuSystemNoticeVo queryById(Long id);

    /**
     * 查询系统通知消息列表
     */
    TableDataInfo<SohuSystemNoticeVo> queryPageList(SohuSystemNoticeBo bo, PageQuery pageQuery);

    /**
     * 查询系统通知消息列表
     */
    List<SohuSystemNoticeVo> queryList(SohuSystemNoticeBo bo);

    /**
     * 修改系统通知消息
     */
    Boolean insertByBo(SohuSystemNoticeBo bo);

    /**
     * 修改系统通知消息
     */
    Boolean updateByBo(SohuSystemNoticeBo bo);

    /**
     * 校验并批量删除系统通知消息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取系统通知(系统，任务，添加好友等通知)
     *
     * @return {@link List}
     */
    List<SohuSystemNoticeVo> noticeList();

    /**
     * 发送通知
     *
     * @param senderId   发送人ID，一般是超管ID
     * @param receiverId 接收人id，即用户ID
     * @param title      标题
     * @param content    内容，一般是json 格式
     */
    void sendNotice(Long senderId, Long receiverId, String title, String content, String type);

    /**
     * 查询服务商的专属IM群
     *
     * @param id 消息ID
     * @return
     */
    SohuImGroupVo agentDetail(Long id);

}
