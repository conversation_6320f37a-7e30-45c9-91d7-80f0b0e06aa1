package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.vo.SohuStatVo;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.bo.SohuTradeRecordIndependentBo;
import com.sohu.middle.api.vo.*;
import com.sohu.middle.api.vo.playlet.PlayletVirtualRecordVo;
import com.sohu.middleService.domain.*;
import com.sohu.middleService.mapper.*;
import com.sohu.middleService.service.*;
import com.sohu.novel.api.RemoteNovelService;
import com.sohu.novel.api.vo.CourseVo;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.pay.api.RemotePayOrderService;
import com.sohu.pay.api.model.SohuPayOrderModel;
import com.sohu.pay.api.vo.SohuAccountVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.sohu.common.core.utils.DateUtils.YYYY_MM;

/**
 * 用户流水明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-07
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuTradeRecordServiceImpl extends SohuBaseServiceImpl<SohuTradeRecordMapper, SohuTradeRecord, SohuTradeRecordVo> implements ISohuTradeRecordService {

    private final SohuRechargeListMapper sohuRechargeListMapper;
    private final ISohuUserVirtualService sohuUserVirtualService;
    private final ISohuInviteService sohuInviteService;
    private final SohuVideoMapper sohuVideoMapper;
    private final SohuPlayletMapper sohuPlayletMapper;
    private final ISohuUserService sohuUserService;
    private final SohuIndependentMaterialMapper sohuIndependentMaterialMapper;
    @DubboReference
    private RemoteAccountService remoteAccountService;
    @DubboReference
    private RemotePayOrderService remotePayOrderService;
    @DubboReference
    private RemoteNovelService remoteNovelService;
    private final ISohuLiteratureService literatureService;

    /**
     * 查询用户流水明细
     */
    @Override
    public SohuTradeRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public SohuTradeRecordVo queryByPayNumber(String payNumber) {
        LambdaQueryWrapper<SohuTradeRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuTradeRecord::getPayNumber, payNumber);
        lqw.last(" limit 1");
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public SohuTradeRecordVo queryByPayNumber(String payNumber, String amountType) {
        LambdaQueryWrapper<SohuTradeRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuTradeRecord::getPayNumber, payNumber).eq(SohuTradeRecord::getAmountType, amountType);
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 查询用户流水明细列表
     */
    @Override
    public TableDataInfo<SohuTradeRecordVo> queryPageList(SohuTradeRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuTradeRecord> lqw = buildQueryWrapper(bo);
        Page<SohuTradeRecordVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuTradeRecordVo> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfoUtils.build();
        }
        Page<SohuTradeRecordVo> built = buildMonthStat(result.getRecords(), result.getCurrent(), result.getSize(), result.getTotal(), bo);
        return TableDataInfoUtils.build(built);
    }

    @Override
    public TableDataInfo<SohuTradeRecordVo> getVirtualPayRecord(SohuTradeRecordBo bo, PageQuery pageQuery) {
        // TODO 提现需更改
        Page<SohuTradeRecordVo> result = baseMapper.getVirtualPayRecord(PageQueryUtils.build(pageQuery), bo);
        if (result == null || CollUtil.isEmpty(result.getRecords())) {
            return TableDataInfoUtils.build();
        }
        Page<SohuTradeRecordVo> built = buildMonthStat(result.getRecords(), result.getCurrent(), result.getSize(), result.getTotal(), bo);
        return TableDataInfoUtils.build(built);
    }

    private Page<SohuTradeRecordVo> buildMonthStat(List<SohuTradeRecordVo> records, long pageNum, long pageSize, long total, SohuTradeRecordBo bo) {
        // 过滤支付时间为空的数据
        List<SohuTradeRecordVo> filteredRecords = records.stream()
                .filter(record -> record.getPayTime() != null)
                .collect(Collectors.toList());

        Page<SohuTradeRecordVo> filteredResult = new Page<>(pageNum, pageSize, total);

        // 获取当前页所有月份
        Set<String> months = new HashSet<>();
        for (SohuTradeRecordVo record : filteredRecords) {
            String month = DateUtils.parseDateToStr(YYYY_MM, record.getCreateTime());
            months.add(month);
            record.setMonth(month);
        }
        // 每月不同类型的统计map
        Map<String, List<SohuStatAmountVo>> statMap = new HashMap<>();
        for (String month : months) {
            List<SohuStatAmountVo> stat = baseMapper.stat(bo.getUserId(), month);
            statMap.put(month, stat);
        }
        filteredRecords.forEach(record -> {
            if (statMap.containsKey(record.getMonth())) {
                List<SohuStatAmountVo> statList = statMap.get(record.getMonth());
                if (CollUtil.isNotEmpty(statList)) {
                    // map1形式：accountType-amountType-type:金额  ，如：Amount-Expend-BusyTaskPromise
                    Map<String, BigDecimal> mapAll = new HashMap<>();
                    // map2形式：accountType-amountType:金额  ，如：Amount-Expend
                    Map<String, BigDecimal> mapAmount = new HashMap<>();
                    // map3形式：accountType-amountType:虚拟币  ，如：Amount-Expend
                    Map<String, BigDecimal> mapVirtual = new HashMap<>();
                    for (SohuStatAmountVo statAmountVo : statList) {
                        if (statAmountVo.getAmount() == null) {
                            statAmountVo.setAmount(BigDecimal.ZERO);
                        }
                        if (statAmountVo.getVirtualCoin() == null) {
                            statAmountVo.setVirtualCoin(BigDecimal.ZERO);
                        }
                        mapAll.put(statAmountVo.getAccountType() + StrPool.DASHED + statAmountVo.getAmountType() + StrPool.DASHED + statAmountVo.getType(), statAmountVo.getAmount());
                        BigDecimal amount = mapAmount.get(statAmountVo.getAccountType() + StrPool.DASHED + statAmountVo.getAmountType());
                        BigDecimal coin = mapVirtual.get(statAmountVo.getAccountType() + StrPool.DASHED + statAmountVo.getAmountType());
                        if (amount == null) {
                            amount = BigDecimal.ZERO;
                            coin = BigDecimal.ZERO;
                        }
                        mapAmount.put(statAmountVo.getAccountType() + StrPool.DASHED + statAmountVo.getAmountType(), amount.add(statAmountVo.getAmount()));
                        mapVirtual.put(statAmountVo.getAccountType() + StrPool.DASHED + statAmountVo.getAmountType(), coin.add(statAmountVo.getVirtualCoin()));
                    }
                    if (StrUtil.equalsAnyIgnoreCase(bo.getAccountType(), SohuTradeRecordEnum.AccountType.Virtual.getCode())) {
                        BigDecimal income = mapVirtual.get(SohuTradeRecordEnum.AccountType.Virtual + StrPool.DASHED + SohuTradeRecordEnum.AmountType.InCome.getCode());
                        BigDecimal expend = mapVirtual.get(SohuTradeRecordEnum.AccountType.Virtual + StrPool.DASHED + SohuTradeRecordEnum.AmountType.Expend.getCode());
                        BigDecimal give = mapAll.get(SohuTradeRecordEnum.AccountType.Virtual + StrPool.DASHED + SohuTradeRecordEnum.Type.VirtualGive + StrPool.DASHED + record.getType());
                        record.setMonthVirtualRecharge(income == null ? BigDecimal.ZERO : income);
                        record.setMonthVirtualConsume(expend == null ? BigDecimal.ZERO : expend);
                        record.setMonthVirtualGive(give == null ? BigDecimal.ZERO : give);

                    } else if (StrUtil.equalsAnyIgnoreCase(bo.getAccountType(), SohuTradeRecordEnum.AccountType.Amount.getCode())) {
                        BigDecimal income = mapAmount.get(SohuTradeRecordEnum.AccountType.Amount + StrPool.DASHED + SohuTradeRecordEnum.AmountType.InCome.getCode());
                        BigDecimal expend = mapAmount.get(SohuTradeRecordEnum.AccountType.Amount + StrPool.DASHED + SohuTradeRecordEnum.AmountType.Expend.getCode());
                        record.setMonthVirtualRecharge(income == null ? BigDecimal.ZERO : income);
                        record.setMonthVirtualConsume(expend == null ? BigDecimal.ZERO : expend);
                        record.setMonthVirtualGive(BigDecimal.ZERO);
                    }

                }
            }
        });

        // 排序
        filteredRecords.sort((e1, e2) -> Long.compare(e2.getId(), e1.getId()));
        filteredResult.setRecords(filteredRecords);
        return filteredResult;
    }

    @Override
    public Boolean updatePayStatus(String payNumber, String transactionId, String oldPayStatus, String newPayStatus) {
        LambdaUpdateWrapper<SohuTradeRecord> lqw = Wrappers.lambdaUpdate();
        lqw.eq(SohuTradeRecord::getPayNumber, payNumber);
        lqw.eq(SohuTradeRecord::getPayStatus, oldPayStatus);
        if (StrUtil.isNotBlank(transactionId)) {
            lqw.set(SohuTradeRecord::getTransactionId, transactionId);
        }
        lqw.set(SohuTradeRecord::getPayStatus, newPayStatus);
        return this.baseMapper.update(SohuTradeRecord.builder().build(), lqw) > 0;
    }

    @Override
    public SohuTradeRecordVo queryFirstRecord(Long userId, String accountType) {
        return baseMapper.queryFirstRecord(userId, accountType);
    }

    @Override
    public SohuInComeVo income() {
        SohuInComeVo vo = new SohuInComeVo();
        LambdaQueryWrapper<SohuTradeRecord> lqw = buildIncomeQueryWrapper();

        // 代理商累计收益
        lqw.eq(SohuTradeRecord::getIndependentStatus, IndependentStatusEnum.DISTRIBUTED.getCode());
        List<SohuTradeRecord> totalList = baseMapper.selectList(lqw);
        BigDecimal totalIncome = CollUtil.isEmpty(totalList) ? BigDecimal.ZERO : totalList.stream().map(SohuTradeRecord::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTotalIncome(totalIncome);

        // 代理商今日收益
        LambdaQueryWrapper<SohuTradeRecord> todayWrapper = buildIncomeQueryWrapper();
        DateTime begin = DateUtil.beginOfDay(new Date());
        DateTime end = DateUtil.endOfDay(new Date());
        todayWrapper.between(SohuTradeRecord::getCreateTime, new Date(begin.getTime()), new Date(end.getTime()));

        List<SohuTradeRecord> todayList = baseMapper.selectList(todayWrapper);
        BigDecimal todayIncome = CollUtil.isEmpty(todayList) ? BigDecimal.ZERO : todayList.stream().map(SohuTradeRecord::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTodayIncome(todayIncome);
        return vo;
    }

    @Override
    public List<SohuInComeListVo> incomeList(Long days) {
        List<SohuInComeListVo> list = new ArrayList<>();
        LocalDate currentDate = LocalDate.now();
        for (int i = 0; i < days; i++) {
            SohuInComeListVo vo = new SohuInComeListVo();
            LocalDate date = currentDate.minusDays(i);

            LambdaQueryWrapper<SohuTradeRecord> lqw = buildIncomeQueryWrapper();
            lqw.ge(SohuTradeRecord::getCreateTime, DateUtil.beginOfDay(DateUtil.parseDate(date.toString())));
            lqw.le(SohuTradeRecord::getCreateTime, DateUtil.endOfDay(DateUtil.parseDate(date.toString())));
            List<SohuTradeRecord> todayList = baseMapper.selectList(lqw);
            BigDecimal income = CollUtil.isEmpty(todayList) ? BigDecimal.ZERO : todayList.stream().map(SohuTradeRecord::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setIncome(income);
            vo.setDate(date);
            list.add(vo);
        }
        return list.stream().sorted(Comparator.comparing(SohuInComeListVo::getDate)).collect(Collectors.toList());
    }

    @Override
    public List<SohuInComeListVo> incomeListByDate(String date) {
        List<SohuInComeListVo> list = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            SohuInComeListVo vo = new SohuInComeListVo();

            LocalTime localTime = LocalTime.of(i, 0);
            LocalTime startTime = LocalTime.of(i, 0, 0);
            LocalTime endTime = LocalTime.of(i, 59, 59);
            String queryStartTime = date.concat(" ").concat(String.valueOf(startTime));
            String queryEndTime = date.concat(" ").concat(String.valueOf(endTime));

            LambdaQueryWrapper<SohuTradeRecord> lqw = buildIncomeQueryWrapper();
            lqw.ge(SohuTradeRecord::getCreateTime, queryStartTime);
            lqw.le(SohuTradeRecord::getCreateTime, queryEndTime);
            List<SohuTradeRecord> todayList = baseMapper.selectList(lqw);
            BigDecimal income = CollUtil.isEmpty(todayList) ? BigDecimal.ZERO : todayList.stream().map(SohuTradeRecord::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setIncome(income);
            vo.setTime(String.valueOf(localTime));
            list.add(vo);
        }
        return list;
    }

    @Override
    public List<SohuSalesRankVo> salesRank() {
        List<SohuSalesRankVo> rankList = new ArrayList<>();
        LambdaQueryWrapper<SohuTradeRecord> lqw = buildIncomeQueryWrapper();
        List<SohuTradeRecord> list = baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(list)) {
            return rankList;
        }
        list.forEach(f -> {
            SohuSalesRankVo vo = new SohuSalesRankVo();
            SohuPayOrderModel sohuPayOrderModel = remotePayOrderService.queryByPayNumber(f.getPayNumber());
            if (sohuPayOrderModel != null) {
                vo.setUserId(sohuPayOrderModel.getUserId());
                vo.setSales(f.getAmount());
                rankList.add(vo);
            }
        });
        List<SohuSalesRankVo> voList = rankList.stream().collect(Collectors.groupingBy(SohuSalesRankVo::getUserId,
                Collectors.reducing(BigDecimal.ZERO, SohuSalesRankVo::getSales, BigDecimal::add))).entrySet().stream().map(entry -> {
            LoginUser loginUser = sohuUserService.selectById(entry.getKey());
            SohuSalesRankVo vo = new SohuSalesRankVo();
            vo.setUserId(entry.getKey());
            vo.setUserName(loginUser.getNickname());
            vo.setSales(entry.getValue());
            return vo;
        }).collect(Collectors.toList());
        return voList.stream().sorted(Comparator.comparing(SohuSalesRankVo::getSales).reversed()).collect(Collectors.toList());
    }

    @Override
    public List<SohuTradeRecordIndependentVo> getList(SohuTradeRecordIndependentBo bo) {
        List<SohuTradeRecordIndependentVo> result = new ArrayList<>();
//        Long userId = LoginHelper.getUserId();
//        if (!LoginHelper.isAdmin(userId)){
//            bo.setUserId(userId);
//        }
        List<String> personRoles = Arrays.asList(SohuIndependentObject.rece.getKey(), SohuIndependentObject.invite.getKey(), SohuIndependentObject.distribution.getKey(), SohuIndependentObject.distributionInvite.getKey());
        List<String> siteRoles = Arrays.asList(SohuIndependentObject.country.getKey(), SohuIndependentObject.city.getKey(), SohuIndependentObject.entrance.getKey(), SohuIndependentObject.industrysite.getKey(), SohuIndependentObject.invitecity.getKey());
        List<String> agencyRoles = Arrays.asList(SohuIndependentObject.agency.getKey());
        List<String> selectRoles = personRoles;
        if (UserRoleEnum.CITYSTATION.getType().equals(bo.getUserRole())) {
            selectRoles = siteRoles;
        } else if (UserRoleEnum.AGENCY.getType().equals(bo.getUserRole())) {
            selectRoles = agencyRoles;
        }
        bo.setIndependentObjectList(selectRoles);
        List<SohuTradeRecordVo> list = baseMapper.selectVoList(buildIndependent(bo));
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(record -> {
                SohuTradeRecordIndependentVo vo = new SohuTradeRecordIndependentVo();
                vo.setAmount(record.getAmount());
                vo.setIndependentPrice(record.getAmount());
                vo.setIndependentObject(SohuIndependentObject.MAP.get(record.getIndependentObject()));
                vo.setId(record.getId());
                vo.setPayNumber(record.getPayNumber());
                vo.setTradeNo(record.getTransactionId());
                vo.setSourceType(record.getSourceType());
                vo.setAmountType(record.getAmountType());
                if (StrUtil.containsAnyIgnoreCase(record.getType(), "Busy")) {
                    vo.setTradeType("商单");
                } else if (StrUtil.equalsAnyIgnoreCase(record.getType(), SohuTradeRecordEnum.Type.Good.getCode())) {
                    vo.setTradeType("商品");
                } else if (StrUtil.equalsAnyIgnoreCase(record.getType(), SohuTradeRecordEnum.Type.Playlet.getCode()) ||
                        StrUtil.equalsAnyIgnoreCase(record.getType(), SohuTradeRecordEnum.Type.Video.getCode())) {
                    vo.setTradeType("短剧");
                }
                vo.setUserId(record.getUserId());
                vo.setCreateTime(record.getCreateTime());
                vo.setIndependentStatusName(IndependentStatusEnum.getValue(record.getIndependentStatus()));
                vo.setAmountType(SohuTradeRecordEnum.AmountType.getValue(record.getAmountType()));
                result.add(vo);
            });
        }
        return result;
    }

    @Override
    public TableDataInfo<PlayletVirtualRecordVo> getPlayletFoxCoinRecord(String uuid, PageQuery pageQuery) {
        Long loginId = LoginHelper.getUserId();
        if ((loginId == null || loginId <= 0L) && StrUtil.isBlankIfStr(uuid)) {
            return TableDataInfoUtils.build();
        }

        LambdaQueryWrapper<SohuTradeRecord> lqw = new LambdaQueryWrapper<>();
        if (loginId != null && loginId > 0L) {
            lqw.eq(SohuTradeRecord::getUserId, loginId);
        } else {
            lqw.eq(SohuTradeRecord::getUuid, uuid);
        }
        lqw.eq(SohuTradeRecord::getAccountType, SohuTradeRecordEnum.AccountType.Virtual.getCode());
        lqw.eq(SohuTradeRecord::getPayStatus, PayStatus.Paid.name());
        lqw.orderByDesc(SohuTradeRecord::getId);
        Page<SohuTradeRecordVo> voPage = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        if (CollUtil.isEmpty(voPage.getRecords())) {
            return TableDataInfoUtils.build();
        }
        TableDataInfo<PlayletVirtualRecordVo> tableDataInfo = new TableDataInfo<>();
        List<PlayletVirtualRecordVo> list = new LinkedList<>();
        for (SohuTradeRecordVo record : voPage.getRecords()) {
            PlayletVirtualRecordVo vo = new PlayletVirtualRecordVo();
            vo.setAmount(record.getAmount());
            vo.setAmountType(record.getAmountType());
            vo.setVirtualCoin(record.getVirtualCoin());
            vo.setPayTime(record.getPayTime());
            list.add(vo);
        }
        tableDataInfo.setData(list);
        tableDataInfo.setTotal(voPage.getTotal());
        return tableDataInfo;
    }

    @Override
    public BigDecimal sumUserAmount(Long userId, String amountType, List<String> typeList) {
        return baseMapper.sumUserAmount(userId, amountType, PayStatus.Paid.name(), typeList);
    }

    @Override
    public BigDecimal sumGuestAmount(String uuid, String amountType) {
        return baseMapper.sumGuestAmount(uuid, amountType, PayStatus.Paid.name());
    }

    @Override
    public Boolean bindGuest(String uuid) {
        Long userId = LoginHelper.getUserId();
        LambdaUpdateWrapper<SohuTradeRecord> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuTradeRecord::getUuid, uuid);
        luw.set(SohuTradeRecord::getUserId, userId);
        return baseMapper.update(SohuTradeRecord.builder().build(), luw) > 0;
    }

    @Override
    public SohuTradeRecordVo queryByPayNumberOne(String payNumber, String payType) {
        LambdaQueryWrapper<SohuTradeRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuTradeRecord::getPayNumber, payNumber);
        lqw.eq(SohuTradeRecord::getPayType, payType);
        lqw.last("limit 1");
        return baseMapper.selectVoOne(lqw);
    }

    private LambdaQueryWrapper<SohuTradeRecord> buildIncomeQueryWrapper() {
        LambdaQueryWrapper<SohuTradeRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(SohuTradeRecord::getType, Arrays.asList(
                SohuTradeRecordEnum.Type.BusyTaskParty.getCode(),
                SohuTradeRecordEnum.Type.Good.getCode(),
                SohuTradeRecordEnum.Type.Playlet.getCode(),
                SohuTradeRecordEnum.Type.Video.getCode()));
        lqw.eq(SohuTradeRecord::getUserId, LoginHelper.getUserId());
        lqw.eq(SohuTradeRecord::getAmountType, SohuTradeRecordEnum.AmountType.InCome.getCode());
        lqw.eq(SohuTradeRecord::getAccountType, SohuTradeRecordEnum.AccountType.Amount.getCode());
        lqw.eq(SohuTradeRecord::getPayStatus, PayStatus.Paid.name());
        return lqw;
    }

    @Override
    public SohuTradeRecordVo queryConsume(String payType, String consumeType, String consumeCode) {
        LambdaQueryWrapper<SohuTradeRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuTradeRecord::getPayType, payType);
        lqw.eq(SohuTradeRecord::getConsumeType, consumeType);
        lqw.eq(SohuTradeRecord::getConsumeCode, consumeCode);
        lqw.last("limit 1");
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public TableDataInfo<SohuTradeRecordVo> queryNovelPageList(SohuTradeRecordBo bo, PageQuery pageQuery) {
        Page<SohuTradeRecordVo> result = baseMapper.queryNovelPageList(PageQueryUtils.build(pageQuery), bo);
        return TableDataInfoUtils.build(result);
    }

    @Override
    public SohuNovelTradeRecordVo queryNovelById(Long id) {
        SohuNovelTradeRecordVo novelTradeRecordVo = new SohuNovelTradeRecordVo();
        SohuTradeRecordVo tradeRecordVo = baseMapper.selectVoById(id);
        if (Objects.nonNull(tradeRecordVo)) {
            SohuPayOrderModel sohuPayOrderModel = remotePayOrderService.queryByPayNumber(tradeRecordVo.getPayNumber());
            tradeRecordVo.setPayStatus(sohuPayOrderModel.getPayStatus());
            LoginUser loginUser = sohuUserService.selectById(tradeRecordVo.getUserId());
            tradeRecordVo.setNickName(Objects.nonNull(loginUser) ? loginUser.getNickname() : "");
            // 小说
            novelTradeRecordVo = BeanUtil.toBean(tradeRecordVo, SohuNovelTradeRecordVo.class);
            CourseVo courseVo = remoteNovelService.queryNovelById(tradeRecordVo.getParentConsumeCode(), tradeRecordVo.getConsumeCode());
            if (Objects.nonNull(courseVo)) {
                novelTradeRecordVo.setNovelTitle(courseVo.getTitle());
                novelTradeRecordVo.setAuthor(courseVo.getAuthor());
                novelTradeRecordVo.setWholeIsPrice(courseVo.getIsPrice());
                novelTradeRecordVo.setWholePrice(courseVo.getPrice());
                novelTradeRecordVo.setSuraIsPrice(courseVo.getSuraIsPrice());
                novelTradeRecordVo.setSuraPrice(courseVo.getSuraPrice());
            }
        }
        return novelTradeRecordVo;
    }

    @Override
    public void cancelNovelOrder(SohuTradeRecordBo tradeRecordBo) {
        remotePayOrderService.cancelNovelOrder(tradeRecordBo.getPayNumber());
    }

    @Override
    public Boolean updateIndependentStatus(String orderNo, Integer sourceStatus, Integer targetStatus) {
        LambdaUpdateWrapper<SohuTradeRecord> luw = new LambdaUpdateWrapper();
        luw.eq(SohuTradeRecord::getPayNumber, orderNo);
        if (sourceStatus != null) {
            luw.eq(SohuTradeRecord::getIndependentStatus, sourceStatus);
        }
        luw.set(SohuTradeRecord::getIndependentStatus, targetStatus);
        return baseMapper.update(SohuTradeRecord.builder().build(), luw) > 0;
    }

    @Override
    public SohuLiteratureTradeRecordVo queryLiteratureById(Long id) {
        SohuLiteratureTradeRecordVo literatureTradeRecordVo = new SohuLiteratureTradeRecordVo();
        SohuTradeRecordVo tradeRecordVo = baseMapper.selectVoById(id);
        if (Objects.nonNull(tradeRecordVo)) {
            SohuPayOrderModel sohuPayOrderModel = remotePayOrderService.queryByPayNumber(tradeRecordVo.getPayNumber());
            tradeRecordVo.setPayStatus(sohuPayOrderModel.getPayStatus());
            LoginUser loginUser = sohuUserService.selectById(tradeRecordVo.getUserId());
            tradeRecordVo.setNickName(Objects.nonNull(loginUser) ? loginUser.getNickname() : "");
            // 小说
            literatureTradeRecordVo = BeanUtil.toBean(tradeRecordVo, SohuLiteratureTradeRecordVo.class);
            SohuLiteratureVo literatureVo = literatureService.queryById(Long.valueOf(tradeRecordVo.getParentConsumeCode()));
            if (Objects.nonNull(literatureVo)) {
                literatureTradeRecordVo.setTitle(literatureVo.getTitle());
                literatureTradeRecordVo.setAuthor(literatureVo.getUserName());
            }
        }
        return literatureTradeRecordVo;
    }

    /**
     * 查询用户流水明细列表
     */
    @Override
    public List<SohuTradeRecordVo> queryList(SohuTradeRecordBo bo) {
        LambdaQueryWrapper<SohuTradeRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuTradeRecord> buildQueryWrapper(SohuTradeRecordBo bo) {
        LambdaQueryWrapper<SohuTradeRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuTradeRecord::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), SohuTradeRecord::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getAccountType()), SohuTradeRecord::getAccountType, bo.getAccountType());
        lqw.eq(StringUtils.isNotBlank(bo.getConsumeType()), SohuTradeRecord::getConsumeType, bo.getConsumeType());
        lqw.eq(StringUtils.isNotBlank(bo.getConsumeCode()), SohuTradeRecord::getConsumeCode, bo.getConsumeCode());
        lqw.eq(StringUtils.isNotBlank(bo.getParentConsumeCode()), SohuTradeRecord::getParentConsumeCode, bo.getParentConsumeCode());
        lqw.eq(bo.getAmount() != null, SohuTradeRecord::getAmount, bo.getAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getMsg()), SohuTradeRecord::getMsg, bo.getMsg());
        lqw.eq(StringUtils.isNotBlank(bo.getAmountType()), SohuTradeRecord::getAmountType, bo.getAmountType());
        lqw.eq(StringUtils.isNotBlank(bo.getPayType()), SohuTradeRecord::getPayType, bo.getPayType());
        lqw.eq(bo.getEffectiveTime() != null, SohuTradeRecord::getEffectiveTime, bo.getEffectiveTime());
        lqw.eq(StringUtils.isNotBlank(bo.getOperateChannel()), SohuTradeRecord::getOperateChannel, bo.getOperateChannel());
        lqw.like(StringUtils.isNotBlank(bo.getPayNumber()), SohuTradeRecord::getPayNumber, bo.getPayNumber());
        lqw.eq(bo.getIndependent() != null, SohuTradeRecord::getIndependent, bo.getIndependent());
        lqw.eq(StringUtils.isNotBlank(bo.getIndependentObject()), SohuTradeRecord::getIndependentObject, bo.getIndependentObject());
        lqw.eq(StringUtils.isNotBlank(bo.getPayStatus()), SohuTradeRecord::getPayStatus, bo.getPayStatus());
        lqw.eq(StrUtil.isNotBlank(bo.getUuid()), SohuTradeRecord::getUuid, bo.getUuid());
        lqw.eq(StrUtil.isNotBlank(bo.getSysSource()), SohuTradeRecord::getSysSource, bo.getSysSource());
        return lqw;
    }

    /**
     * 新增用户流水明细
     */
    @Override
    public Boolean insertByBo(SohuTradeRecordBo bo) {
        log.info("购买AI流水Bo对象:{}", JSONUtil.toJsonStr(bo));
        SohuTradeRecord add = BeanUtil.toBean(bo, SohuTradeRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 批量插入流水明细
     *
     * @param entityList
     * @return
     */
    @Override
    public Boolean insertBatch(List<SohuTradeRecordBo> entityList) {
        List<SohuTradeRecord> list = new ArrayList<>();
        for (SohuTradeRecordBo bo : entityList) {
            SohuTradeRecord record = BeanCopyUtils.copy(bo, SohuTradeRecord.class);
            list.add(record);
        }
        this.baseMapper.insertBatch(list);
        return true;
    }

    /**
     * 修改用户流水明细
     */
    @Override
    public Boolean updateByBo(SohuTradeRecordBo bo) {
        SohuTradeRecord update = BeanUtil.toBean(bo, SohuTradeRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuTradeRecord entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除用户流水明细
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean deleteByByPayNumber(String payNumber) {
        return baseMapper.delete(SohuTradeRecord::getPayNumber, payNumber);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePayStatus(String payNumber, String transactionId, PayStatus payStatus, String amountType) {
        LambdaQueryWrapper<SohuTradeRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuTradeRecord::getPayNumber, payNumber);
        if (StringUtils.isNotBlank(amountType)) {
            lqw.eq(SohuTradeRecord::getAmountType, amountType);
        }
        lqw.last(" limit 1");
        SohuTradeRecord tradeRecord = this.baseMapper.selectOne(lqw);
        if (Objects.isNull(tradeRecord)) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<SohuTradeRecord> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuTradeRecord::getPayNumber, payNumber);
        if (StrUtil.isNotBlank(transactionId)) {
            luw.set(SohuTradeRecord::getTransactionId, transactionId);
            luw.set(SohuTradeRecord::getIndependentStatus, IndependentStatusEnum.DISTRIBUTED.getCode());
        }
        luw.set(SohuTradeRecord::getPayStatus, payStatus.name());

        if (StrUtil.equalsAnyIgnoreCase(tradeRecord.getType(), SohuTradeRecordEnum.Type.VirtualRecharge.getCode(), SohuTradeRecordEnum.Type.VirtualDiyRecharge.getCode())) {
            // 虚拟币充值
            if (StrUtil.equals(tradeRecord.getType(), SohuTradeRecordEnum.Type.VirtualRecharge.getCode()) && payStatus == PayStatus.Paid) {
                SohuRechargeList rechargeList = sohuRechargeListMapper.selectById(tradeRecord.getConsumeCode());
                if (Objects.isNull(rechargeList)) {
                    throw new ServiceException("充值列表不存在");
                }
                if (rechargeList.getGive() != null && rechargeList.getGive() > 0L) {
                    SohuTradeRecordBo recordGiveBo = SohuTradeRecordBo.builder().
                            userId(tradeRecord.getUserId()).
                            payType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus()).
                            type(SohuTradeRecordEnum.Type.VirtualGive.getCode()).
                            amount(new BigDecimal(rechargeList.getGive())).
                            msg(SohuTradeRecordEnum.Type.VirtualRecharge.getMsg()).
                            amountType(SohuTradeRecordEnum.AmountType.InCome.getCode()).
                            payStatus(PayStatus.Paid.name()).
                            operateChannel(tradeRecord.getOperateChannel()).
                            transactionId(transactionId).
                            payTime(new Date()).
                            payNumber(tradeRecord.getPayNumber() + RandomUtil.randomNumbers(2)).
                            build();
                    this.insertByBo(recordGiveBo);
                    // 保存充值赠送记录
                }
                luw.set(SohuTradeRecord::getVirtualCoin, rechargeList.getCoin());
                // 充值虚拟币
                if (tradeRecord.getUserId() == null || tradeRecord.getUserId() <= 0L) {
                    sohuUserVirtualService.rechargeCoin(tradeRecord.getUuid(), new BigDecimal(rechargeList.getCoin()),
                            rechargeList.getGive() != null ? new BigDecimal(rechargeList.getGive()) : BigDecimal.ZERO);
                } else {
                    sohuUserVirtualService.rechargeCoin(tradeRecord.getUserId(), new BigDecimal(rechargeList.getCoin()),
                            rechargeList.getGive() != null ? new BigDecimal(rechargeList.getGive()) : BigDecimal.ZERO);
                }
            }
            if (StrUtil.equals(tradeRecord.getType(), SohuTradeRecordEnum.Type.VirtualDiyRecharge.getCode()) && payStatus == PayStatus.Paid) {
                luw.set(SohuTradeRecord::getVirtualCoin, CalUtils.multiply(tradeRecord.getAmount(), Constants.VIRTUAL_AMOUNT_RATIO));
                // 充值虚拟币
                if (tradeRecord.getUserId() == null || tradeRecord.getUserId() <= 0L) {
                    sohuUserVirtualService.rechargeCoin(tradeRecord.getUuid(), CalUtils.multiply(tradeRecord.getAmount(), Constants.VIRTUAL_AMOUNT_RATIO), BigDecimal.ZERO);
                } else {
                    sohuUserVirtualService.rechargeCoin(tradeRecord.getUserId(), CalUtils.multiply(tradeRecord.getAmount(), Constants.VIRTUAL_AMOUNT_RATIO), BigDecimal.ZERO);
                }

            }
            SohuTradeRecord tradeRecordVirtual = SohuTradeRecord.builder().build();
            BeanUtil.copyProperties(tradeRecord, tradeRecordVirtual);
            tradeRecordVirtual.setId(null);
            tradeRecordVirtual.setUuid(tradeRecord.getUuid());
            tradeRecordVirtual.setAmountType(SohuTradeRecordEnum.AmountType.InCome.getCode());
            tradeRecordVirtual.setAccountType(SohuTradeRecordEnum.AccountType.Virtual.getCode());
            tradeRecordVirtual.setUnq(tradeRecord.getUnq() + "1");
            tradeRecordVirtual.setPayStatus(payStatus.name());
            this.baseMapper.insert(tradeRecordVirtual);
        }
//        else if (StrUtil.equalsAnyIgnoreCase(tradeRecord.getType(), SohuTradeRecordEnum.Type.Video.getCode(), SohuTradeRecordEnum.Type.Playlet.getCode())) {
//            SohuTradeRecord tradeRecordCopy = SohuTradeRecord.builder().build();
//            BeanUtil.copyProperties(tradeRecord, tradeRecordCopy);
//            tradeRecordCopy.setId(null);
//            if (StrUtil.equalsAnyIgnoreCase(tradeRecord.getType(), SohuTradeRecordEnum.Type.Video.getCode())) {
//                // 视频ID
//                String consumeCode = tradeRecord.getConsumeCode();
//                SohuVideo sohuVideo = sohuVideoMapper.selectById(Long.valueOf(consumeCode));
//                tradeRecordCopy.setUserId(sohuVideo.getUserId());
//            } else if (StrUtil.equalsAnyIgnoreCase(tradeRecord.getType(), SohuTradeRecordEnum.Type.Playlet.getCode())) {
//                // 短剧ID
//                String consumeCode = tradeRecord.getConsumeCode();
//                SohuPlaylet sohuPlaylet = sohuPlayletMapper.selectById(Long.valueOf(consumeCode));
//                tradeRecordCopy.setUserId(sohuPlaylet.getUserId());
//            }
//            tradeRecordCopy.setUuid(tradeRecord.getUuid());
//            tradeRecordCopy.setPayStatus(payStatus.name());
//            tradeRecordCopy.setAmountType(SohuTradeRecordEnum.AmountType.InCome.getCode());
//            tradeRecordCopy.setAccountType(tradeRecord.getAccountType());
//            tradeRecordCopy.setUnq(tradeRecord.getUnq() + "1");
//            this.baseMapper.insert(tradeRecordCopy);
//        }

        if (payStatus == PayStatus.Paid) {
            luw.set(SohuTradeRecord::getPayTime, DateUtil.date());
        }
        String key = CacheConstants.ORDER_PAY_TWO + PayTypeEnum.PAY_TYPE_YI_MA.getStatus() + StrPool.COLON +
                tradeRecord.getConsumeCode() + StrPool.COLON + tradeRecord.getOperateChannel() + StrPool.COLON + tradeRecord.getUserId();
        RedisUtils.deleteObject(key);
        return this.baseMapper.update(SohuTradeRecord.builder().build(), luw) > 0;
    }

    @Override
    public BigDecimal todayIncome() {
        return baseMapper.getTodayIncomeStat(LoginHelper.getUserId());
    }

    @Override
    public SohuTradeRecordVo queryByPayNumber(String payNumber, String type, Long userId) {
        LambdaQueryWrapper<SohuTradeRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuTradeRecord::getPayNumber, payNumber)
                .eq(SohuTradeRecord::getType, type)
                .eq(SohuTradeRecord::getUserId, userId);
        lqw.last("limit 1");
        return this.baseMapper.selectVoOne(lqw);
    }

    @Override
    public SohuTradeRecordVo queryOne(Long userId, String type, String consumeType, String consumeCode, String payStatus) {
        LambdaQueryWrapper<SohuTradeRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuTradeRecord::getUserId, userId)
                .eq(SohuTradeRecord::getType, type)
                .eq(SohuTradeRecord::getConsumeType, consumeType)
                .eq(SohuTradeRecord::getConsumeCode, consumeCode)
                .eq(SohuTradeRecord::getPayStatus, payStatus);
        lqw.last("limit 1");
        return this.baseMapper.selectVoOne(lqw);
    }

    @Override
    public SohuTradeRecordVo queryOne(SohuTradeRecordBo bo) {
        LambdaQueryWrapper<SohuTradeRecord> lqw = buildQueryWrapper(bo);
        lqw.last("limit 1");
        return this.baseMapper.selectVoOne(lqw);
    }

    @Override
    public Map<String, SohuTradeRecordVo> queryMap(Long userId, String type, String consumeType, List<String> consumeCodeList, String payStatus) {
        log.info("流水记录查询,queryMap:userId:{},type:{},consumeCodeList:{},payStatus:{}", userId, type, consumeCodeList, payStatus);
        LambdaQueryWrapper<SohuTradeRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuTradeRecord::getUserId, userId)
                .eq(SohuTradeRecord::getType, type)
                .eq(SohuTradeRecord::getConsumeType, consumeType)
                .in(SohuTradeRecord::getConsumeCode, consumeCodeList)
                .eq(SohuTradeRecord::getPayStatus, payStatus);
        List<SohuTradeRecordVo> tradeRecords = this.baseMapper.selectVoList(lqw);
        return CollUtil.isEmpty(tradeRecords) ? new HashMap<>() : tradeRecords.stream().collect(Collectors.toMap(SohuTradeRecordVo::getConsumeCode, u -> u));
    }

    @Override
    public Map<String, SohuTradeRecordVo> queryMap(String uuid, String type, String consumeType, List<String> consumeCodeList, String payStatus) {
        log.info("流水记录查询,queryMap:uuid:{},type:{},consumeCodeList:{},payStatus:{}", uuid, type, consumeCodeList, payStatus);
        LambdaQueryWrapper<SohuTradeRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuTradeRecord::getUuid, uuid)
                .eq(SohuTradeRecord::getType, type)
                .eq(SohuTradeRecord::getConsumeType, consumeType)
                .in(SohuTradeRecord::getConsumeCode, consumeCodeList)
                .eq(SohuTradeRecord::getPayStatus, payStatus);
        List<SohuTradeRecordVo> tradeRecords = this.baseMapper.selectVoList(lqw);
        return CollUtil.isEmpty(tradeRecords) ? new HashMap<>() : tradeRecords.stream().collect(Collectors.toMap(SohuTradeRecordVo::getConsumeCode, u -> u));
    }

    @Override
    public TableDataInfo<SohuTradeRecordIndependentVo> queryIndependentList(SohuTradeRecordIndependentBo bo) {
        PageQuery pageQuery = new PageQuery(bo.getPageNum(), bo.getPageSize());
        Long userId = LoginHelper.getUserId();
        if (!LoginHelper.isAdmin(userId)) {
            bo.setUserId(userId);
            List<String> personRoles = Arrays.asList(SohuIndependentObject.rece.getKey(), SohuIndependentObject.invite.getKey(), SohuIndependentObject.distribution.getKey(), SohuIndependentObject.distributionInvite.getKey());
            List<String> siteRoles = Arrays.asList(SohuIndependentObject.country.getKey(), SohuIndependentObject.city.getKey(), SohuIndependentObject.entrance.getKey(), SohuIndependentObject.industrysite.getKey(), SohuIndependentObject.invitecity.getKey());
            List<String> agencyRoles = Arrays.asList(SohuIndependentObject.agency.getKey());
            List<String> selectRoles = personRoles;
            if (UserRoleEnum.CITYSTATION.getType().equals(bo.getUserRole())) {
                selectRoles = siteRoles;
            } else if (UserRoleEnum.AGENCY.getType().equals(bo.getUserRole())) {
                selectRoles = agencyRoles;
            }
            bo.setIndependentObjectList(selectRoles);
        }
        Page<SohuTradeRecordVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), buildIndependent(bo));
        if (CollUtil.isEmpty(result.getRecords())) {
            return TableDataInfoUtils.build();
        }
        List<SohuTradeRecordVo> records = result.getRecords();
        List<SohuTradeRecordIndependentVo> list = new LinkedList<>();
        Set<Long> userIds = new HashSet<>();
        for (SohuTradeRecordVo record : records) {
            userIds.add(record.getUserId());
        }
        Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
        Map<Long, SohuAccountVo> sohuAccountMap = remoteAccountService.selectAccountMapByUserIdsOfPass(userIds);

        for (SohuTradeRecordVo record : records) {
            SohuTradeRecordIndependentVo vo = new SohuTradeRecordIndependentVo();
            LoginUser user = userMap.get(record.getUserId());
            if (Objects.nonNull(user)) {
                vo.setUserName(user.getNickname());
                vo.setUserAvatar(user.getAvatar());
            }
            SohuAccountVo sohuAccount = sohuAccountMap.get(record.getUserId());
            if (Objects.nonNull(sohuAccount)) {
                vo.setMerchantName(sohuAccount.getMerchantName());
            }
            vo.setAmount(record.getAmount());
            vo.setIndependentPrice(record.getAmount());
            vo.setIndependentObject(SohuIndependentObject.MAP.get(record.getIndependentObject()));
            vo.setId(record.getId());
            vo.setPayNumber(record.getPayNumber());
            vo.setTradeNo(record.getTransactionId());
            vo.setSourceType(record.getSourceType());
            vo.setAmountType(record.getAmountType());
            if (StrUtil.containsAnyIgnoreCase(record.getType(), "Busy")) {
                vo.setTradeType("商单");
            } else if (StrUtil.equalsAnyIgnoreCase(record.getType(), SohuTradeRecordEnum.Type.Good.getCode())) {
                vo.setTradeType("商品");
            } else if (StrUtil.equalsAnyIgnoreCase(record.getType(), SohuTradeRecordEnum.Type.Playlet.getCode()) ||
                    StrUtil.equalsAnyIgnoreCase(record.getType(), SohuTradeRecordEnum.Type.Video.getCode())) {
                vo.setTradeType("短剧");
            } else if (StrUtil.equalsAnyIgnoreCase(record.getType(), SohuTradeRecordEnum.Type.VirtualRecharge.getCode())) {
                vo.setTradeType("虚拟币充值");
            }
            vo.setUserId(record.getUserId());
            vo.setCreateTime(record.getCreateTime());
            vo.setIndependentStatus(record.getIndependentStatus() == null ? 0 : record.getIndependentStatus());
            list.add(vo);
        }
        IPage<SohuTradeRecordIndependentVo> ipage = new Page<>();
        ipage.setRecords(list);
        ipage.setTotal(result.getTotal());
        return TableDataInfoUtils.build(ipage);
    }

    @Override
    public WalletIncomeStatVo getWalletStat() {
        WalletIncomeStatVo walletIncomeStatVo = new WalletIncomeStatVo();
        walletIncomeStatVo.setTotalIncomeStat(this.countTotalIncome());
        walletIncomeStatVo.setTodayIncomeStat(this.todayIncome());
        walletIncomeStatVo.setPlayletIncomeStat(this.countPlayletIncome());
        walletIncomeStatVo.setTaskIncomeStat(this.countTaskIncome());
        walletIncomeStatVo.setShareAndInviteIncomeStat(this.countShareAndInviteIncome());
        walletIncomeStatVo.setShareIncomeStat(this.countShareIncome());
        walletIncomeStatVo.setInviteIncomeStat(this.countInviteIncome());

        return walletIncomeStatVo;
    }

    @Override
    public BigDecimal countTotalIncome() {
        // 短剧收入
        BigDecimal playletIncomeStat = this.countPlayletIncome();
        // 任务收入
        BigDecimal taskIncomeStat = this.countTaskIncome();
        // 总佣金收入
        BigDecimal shareAndInviteIncomeStat = this.countShareAndInviteIncome();

        return playletIncomeStat.add(taskIncomeStat).add(shareAndInviteIncomeStat);
    }

    @Override
    public BigDecimal countTodayIncome() {
        return baseMapper.getTodayIncomeStat(LoginHelper.getUserId());
    }

    @Override
    public BigDecimal countPlayletIncome() {
        return baseMapper.getPlayletIncomeStat(LoginHelper.getUserId());
    }

    @Override
    public BigDecimal countTaskIncome() {
        return baseMapper.getTaskIncomeStat(LoginHelper.getUserId());
    }

    @Override
    public BigDecimal countShareAndInviteIncome() {
        // 拉新佣金
        BigDecimal inviteIncome = this.countInviteIncome();
        // 推广佣金
        BigDecimal shareIncome = this.countShareIncome();
        return inviteIncome.add(shareIncome);
    }

    @Override
    public BigDecimal countShareIncome() {
        return baseMapper.getShareIncomeStat(LoginHelper.getUserId());
    }

    @Override
    public BigDecimal countInviteIncome() {
        // TODO 二级佣金是什么???
        Long regUserId = sohuInviteService.selectByInviteCount(LoginHelper.getUserId(), Constants.TWO);
        if (regUserId == null) {
            return BigDecimal.ZERO;
        }
        return baseMapper.getInviteIncomeStat(LoginHelper.getUserId());
    }

    @Override
    public SohuTradeRecordDetailIndependentVo taskDetail(Long id, String payNumber, String independentObject,Integer independentStatus) {
        SohuTradeRecord tradeRecord = null;
        if (id != null) {
            tradeRecord = this.baseMapper.selectById(id);
        }
        if (StrUtil.isNotEmpty(payNumber)) {
            tradeRecord = this.baseMapper.selectOne(new LambdaQueryWrapper<SohuTradeRecord>()
                    .eq(SohuTradeRecord::getPayNumber, payNumber)
                    .eq(SohuTradeRecord::getIndependentObject, independentObject)
                    .eq(SohuTradeRecord::getIndependentStatus, independentStatus)
                    .last(" limit 1"));
        }
        if (Objects.isNull(tradeRecord)) {
            return null;
        }
        LoginUser user = sohuUserService.selectById(tradeRecord.getUserId());
        SohuAccountVo sohuAccountModel = remoteAccountService.queryByUserIdOfPass(tradeRecord.getUserId());
        PayTypeEnum payTypeEnum = PayTypeEnum.LOOKUP.get(tradeRecord.getPayType());
        SohuTradeRecordDetailIndependentVo result = new SohuTradeRecordDetailIndependentVo();

        result.setId(tradeRecord.getId());
        if (StrUtil.equalsAnyIgnoreCase(tradeRecord.getType(), SohuTradeRecordEnum.Type.Good.getCode())) {
            result.setTradeType(SohuTradeRecordEnum.Type.Good.getCode());
        } else if (StrUtil.equalsAnyIgnoreCase(tradeRecord.getType(), SohuTradeRecordEnum.Type.Playlet.getCode())) {
            result.setTradeType(SohuTradeRecordEnum.Type.Playlet.getCode());
        } else {
            result.setTradeType("BusyType");
        }
        result.setPayNumber(tradeRecord.getPayNumber());
        result.setUserId(tradeRecord.getUserId());
        result.setUserName(StrUtil.isNotBlank(user.getNickname()) ? user.getNickname() : user.getUsername());
        result.setUserAvatar(user.getAvatar());
        result.setMerchantName(Objects.nonNull(sohuAccountModel) ? sohuAccountModel.getMerchantName() : null);
        result.setPayType(payTypeEnum.getDescription());
        result.setPayStatus(PayStatus.getMsg(tradeRecord.getPayStatus()));
        result.setPayAmount(tradeRecord.getAmount());
        result.setPayableAmount(tradeRecord.getAmount());
        result.setPayIntegral(BigDecimal.ZERO);
        result.setIndependentObject(SohuIndependentObject.MAP.get(tradeRecord.getIndependentObject()));
        result.setIndependentPrice(tradeRecord.getAmount());
        result.setIndependentStatus(tradeRecord.getIndependentStatus() == null ? 0 : tradeRecord.getIndependentStatus());
        result.setCreateTime(tradeRecord.getCreateTime());
        result.setTradePid(tradeRecord.getConsumeCode());
        result.setTradeId(tradeRecord.getPayNumber());

        String type = tradeRecord.getType();
        if (StrUtil.equalsAnyIgnoreCase(SohuTradeRecordEnum.Type.BusyTaskParty.getCode(), type)) {

        } else if (StrUtil.equalsAnyIgnoreCase(SohuTradeRecordEnum.Type.Good.getCode(), type)) {

        } else if (StrUtil.equalsAnyIgnoreCase(SohuTradeRecordEnum.Type.Video.getCode(), type)) {

        } else if (StrUtil.equalsAnyIgnoreCase(SohuTradeRecordEnum.Type.Playlet.getCode(), type)) {
            // 补充短剧扩展信息
            SohuPlaylet sohuPlaylet = sohuPlayletMapper.selectById(tradeRecord.getConsumeCode());
            result.setTradeTitle(sohuPlaylet.getTitle());
            Long count = sohuVideoMapper.selectCount(new LambdaQueryWrapper<SohuVideo>()
                    .eq(SohuVideo::getEpisodeRelevance, sohuPlaylet.getEpisodeRelevance())
                    .eq(SohuVideo::getIsPay, 1));
            result.setTradeAmount(sohuPlaylet.getSinglePrice().multiply(new BigDecimal(count)));
            SohuIndependentMaterial sohuIndependentMaterial = sohuIndependentMaterialMapper.selectOne(new LambdaQueryWrapper<SohuIndependentMaterial>()
                    .eq(SohuIndependentMaterial::getMaterialCode, sohuPlaylet.getEpisodeRelevance()).last("limit 1"));
            result.setTradeIndependentAmount(sohuIndependentMaterial == null ? null : sohuIndependentMaterial.getIndependentPrice());
            result.setTradeState(PayStatus.getMsg(tradeRecord.getPayStatus()));
        }
        return result;
    }

    @Override
    public BigDecimal independentTotal(SohuTradeRecordIndependentBo bo) {
        Long userId = LoginHelper.getUserId();
        if (!LoginHelper.isAdmin(userId)) {
            bo.setUserId(userId);
            List<String> personRoles = Arrays.asList(SohuIndependentObject.rece.getKey(), SohuIndependentObject.invite.getKey(), SohuIndependentObject.distribution.getKey(), SohuIndependentObject.distributionInvite.getKey());
            List<String> siteRoles = Arrays.asList(SohuIndependentObject.country.getKey(), SohuIndependentObject.city.getKey(), SohuIndependentObject.entrance.getKey(), SohuIndependentObject.industrysite.getKey(), SohuIndependentObject.invitecity.getKey());
            List<String> agencyRoles = Arrays.asList(SohuIndependentObject.agency.getKey());
            List<String> selectRoles = personRoles;
            if (UserRoleEnum.CITYSTATION.getType().equals(bo.getUserRole())) {
                selectRoles = siteRoles;
            } else if (UserRoleEnum.AGENCY.getType().equals(bo.getUserRole())) {
                selectRoles = agencyRoles;
            }
            bo.setIndependentObjectList(selectRoles);
        }
        List<SohuTradeRecord> tradeRecords = this.baseMapper.selectList(buildIndependent(bo));
        return CollUtil.isEmpty(tradeRecords) ? BigDecimal.ZERO : tradeRecords.stream().map(SohuTradeRecord::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private LambdaQueryWrapper<SohuTradeRecord> buildIndependent(SohuTradeRecordIndependentBo bo) {
        LambdaQueryWrapper<SohuTradeRecord> lqw = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(bo.getTradeType())) {
            if (StrUtil.equalsAnyIgnoreCase(bo.getTradeType(), SohuTradeRecordEnum.Type.Good.getCode())) {
                lqw.eq(SohuTradeRecord::getType, SohuTradeRecordEnum.Type.Good.getCode());
            } else if (StrUtil.equalsAnyIgnoreCase(bo.getTradeType(), SohuTradeRecordEnum.Type.PlayletPlatform.getCode())) {
                lqw.in(SohuTradeRecord::getType, Arrays.asList(
                        SohuTradeRecordEnum.Type.Playlet.getCode(),
                        SohuTradeRecordEnum.Type.Video.getCode()));
                lqw.eq(SohuTradeRecord::getTemplateType, Constants.THRID);
            } else if (StrUtil.equalsAnyIgnoreCase(bo.getTradeType(), SohuTradeRecordEnum.Type.PlayletCopyright.getCode())) {
                lqw.in(SohuTradeRecord::getType, Arrays.asList(
                        SohuTradeRecordEnum.Type.Playlet.getCode(),
                        SohuTradeRecordEnum.Type.Video.getCode()));
                lqw.eq(SohuTradeRecord::getTemplateType, Constants.FOUR);
            } else if (StrUtil.equalsAnyIgnoreCase(bo.getTradeType(), SohuTradeRecordEnum.Type.Video.getCode())) {
                lqw.in(SohuTradeRecord::getType, Arrays.asList(
                        SohuTradeRecordEnum.Type.Playlet.getCode(),
                        SohuTradeRecordEnum.Type.Video.getCode()));
                lqw.eq(SohuTradeRecord::getTemplateType, Constants.FOUR);
            } else {
                lqw.in(SohuTradeRecord::getType, Arrays.asList(
                        SohuTradeRecordEnum.Type.BusyTaskParty.getCode(), SohuTradeRecordEnum.Type.BusyTaskFlow.getCode(), SohuTradeRecordEnum.Type.BusyTaskCommon.getCode()));
            }
        } else {
            lqw.in(SohuTradeRecord::getType, Arrays.asList(
                    SohuTradeRecordEnum.Type.BusyTaskParty.getCode(),
                    SohuTradeRecordEnum.Type.BusyTaskFlow.getCode(),
                    SohuTradeRecordEnum.Type.BusyTaskCommon.getCode(),
                    SohuTradeRecordEnum.Type.Good.getCode(),
                    SohuTradeRecordEnum.Type.Playlet.getCode(),
                    SohuTradeRecordEnum.Type.VirtualRecharge.getCode(),
                    SohuTradeRecordEnum.Type.Video.getCode()));
        }
        if (bo.getIsPerson() != null && bo.getIsPerson()) {
            lqw.ne(SohuTradeRecord::getIndependentObject, SohuIndependentObject.shop.getKey());
        }
        lqw.eq(bo.getIndependentStatus() != null, SohuTradeRecord::getIndependentStatus, bo.getIndependentStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getPayNumber()), SohuTradeRecord::getPayNumber, bo.getPayNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getTradeNo()), SohuTradeRecord::getTransactionId, bo.getTradeNo());
        lqw.eq(StringUtils.isNotBlank(bo.getAmountType()), SohuTradeRecord::getAmountType, bo.getAmountType());
        lqw.eq(SohuTradeRecord::getIndependent, true);
        lqw.eq(StringUtils.isNotBlank(bo.getIndependentObject()), SohuTradeRecord::getIndependentObject, bo.getIndependentObject());
        // 时间转换
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            DateTime dateTime = DateUtil.beginOfDay(DateUtil.parseDate(bo.getStartTime()));
            lqw.ge(SohuTradeRecord::getCreateTime, dateTime);
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            DateTime dateTime = DateUtil.endOfDay(DateUtil.parseDate(bo.getEndTime()));
            lqw.le(SohuTradeRecord::getCreateTime, dateTime);
        }
        if (bo.getMinAmount() != null) {
            lqw.ge(SohuTradeRecord::getAmount, bo.getMinAmount());
        }
        if (bo.getMaxAmount() != null) {
            lqw.le(SohuTradeRecord::getAmount, bo.getMaxAmount());
        }
//        if (bo.getUserType()!=null){
//            if (bo.getUserType() == Constants.TWO){
//                lqw.in(SohuTradeRecord::getUserType, Arrays.asList(Constants.ONE, Constants.TWO));
//            }else {
//                lqw.eq(SohuTradeRecord::getUserType,bo.getUserType());
//            }
//        }
        lqw.eq(bo.getUserType() != null, SohuTradeRecord::getUserType, bo.getUserType());
        lqw.eq(bo.getSourceType() != null, SohuTradeRecord::getSourceType, bo.getSourceType());
        lqw.eq(bo.getUserId() != null, SohuTradeRecord::getUserId, bo.getUserId());
        lqw.eq(bo.getSiteType() != null, SohuTradeRecord::getSiteType, bo.getSiteType());
        lqw.eq(bo.getSiteId() != null, SohuTradeRecord::getSiteId, bo.getSiteId());
        lqw.in(CollUtil.isNotEmpty(bo.getIndependentObjectList()), SohuTradeRecord::getIndependentObject, bo.getIndependentObjectList());
        lqw.orderByDesc(SohuTradeRecord::getCreateTime);
        return lqw;
    }

    @Override
    public TableDataInfo<SohuTradeRecordVo> queryLiteraturePageList(SohuTradeRecordBo bo, PageQuery pageQuery) {
        Page<SohuTradeRecordVo> result = baseMapper.queryLiteraturePageList(PageQueryUtils.build(pageQuery), bo);
        return TableDataInfoUtils.build(result);
    }

    @Override
    public Long getNewPayUserNumByCreateTime(Date startTime, Date endTime) {
        LambdaQueryWrapper<SohuTradeRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuTradeRecord::getAmountType, SohuTradeRecordEnum.AmountType.Expend.getCode());
        lqw.eq(SohuTradeRecord::getPayStatus, PayStatus.Paid.name());
        lqw.between(SohuTradeRecord::getPayTime, startTime, endTime);
        return this.baseMapper.getNewPayUserNum(lqw);
    }

    @Override
    public SohuStatVo getStat(Date endTime) {
        return this.baseMapper.getStat(endTime);
    }

    @Override
    public Boolean updatePayNumber(String tradeNo, String newTradeNo) {
        SohuTradeRecord tradeRecord = this.baseMapper.selectOne(new LambdaQueryWrapper<SohuTradeRecord>().eq(SohuTradeRecord::getPayNumber, tradeNo).last(" limit 1"));
        if (Objects.nonNull(tradeRecord)) {
            tradeRecord.setPayNumber(newTradeNo);
            this.baseMapper.updateById(tradeRecord);
        }
        return true;
    }
}
