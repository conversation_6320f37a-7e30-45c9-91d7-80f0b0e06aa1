package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.RemoteBanRecordService;
import com.sohu.admin.api.bo.SohuBanRecordsBo;
import com.sohu.admin.api.vo.SohuBanRecordsVo;
import com.sohu.busyorder.api.RemoteBusyTaskSiteService;
import com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo;
import com.sohu.middle.api.bo.SohuTopicContentQueryBo;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.UserConstants;
import com.sohu.common.core.domain.BaseCommonBo;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.entry.api.RemoteEntryService;
import com.sohu.im.api.noticeContent.NoticeContentDetail;
import com.sohu.im.api.noticeContent.NoticeSystemContent;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.airec.SohuAirecContentItemBo;
import com.sohu.middle.api.bo.mcn.SohuMcnVideoReqBo;
import com.sohu.middle.api.bo.notice.SohuInteractNoticeBo;
import com.sohu.middle.api.bo.playlet.PlayletAdsQueryBo;
import com.sohu.middle.api.bo.risk.SohuRiskMqBo;
import com.sohu.middle.api.enums.*;
import com.sohu.middle.api.enums.report.ActionTypeEnum;
import com.sohu.middle.api.enums.report.RecreationReportEnum;
import com.sohu.middle.api.service.RemoteMiddleEventReportService;
import com.sohu.middle.api.service.RemoteMiddleIndependentMaterialService;
import com.sohu.middle.api.service.RemoteMiddleUserBusyTaskExecuteRecordService;
import com.sohu.middle.api.service.notice.RemoteMiddleSystemNoticeService;
import com.sohu.middle.api.vo.*;
import com.sohu.middle.api.vo.airec.SohuAirecContentItemVo;
import com.sohu.middle.api.vo.mcn.SohuMcnUserVo;
import com.sohu.middle.api.vo.playlet.PlayletListVo;
import com.sohu.middle.api.vo.playlet.SohuPlayetEpisodeListVo;
import com.sohu.middleService.domain.*;
import com.sohu.middleService.domain.bo.SohuAirecArticleQueryBo;
import com.sohu.middleService.domain.bo.SohuAirecVideoQueryBo;
import com.sohu.middleService.domain.mcn.SohuMcnUser;
import com.sohu.middleService.mapper.*;
import com.sohu.middleService.mapper.mcn.SohuMcnUserMapper;
import com.sohu.middleService.service.*;
import com.sohu.middleService.service.airec.ISohuAirecContentItemService;
import com.sohu.middleService.service.airec.ISohuAirecTagRelationService;
import com.sohu.middleService.service.mcn.ISohuArticleDetailService;
import com.sohu.resource.api.RemoteFileService;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.third.aliyun.airec.constants.AliyunAirecConstant;
import com.sohu.third.aliyun.airec.constants.AliyunAirecTagsConstant;
import com.sohu.third.aliyun.airec.domain.AliyunAirecJoinFilterRule;
import com.sohu.third.aliyun.airec.domain.AliyunAirecSingleFilterRule;
import com.sohu.third.aliyun.airec.enums.*;
import com.sohu.third.aliyun.airec.util.AliyunAirecUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static cn.hutool.core.text.StrPool.COMMA;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 视频Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuVideoServiceImpl extends SohuBaseServiceImpl<SohuVideoMapper, SohuVideo, SohuVideoVo> implements ISohuVideoService {

    private final ISohuContentMainService sohuContentMainService;
    private final ISohuVideoInfoService sohuVideoInfoService;
    private final ISohuAuditService sohuAuditService;
    private final ISohuAuditInitService sohuAuditInitService;
    private final ISohuVideoRelateService sohuVideoRelateService;
    private final ISohuSyncContentService sohuSyncContentService;
    private final ISohuArticleDetailService sohuArticleDetailService;
    private final ISohuUserLikeService sohuUserLikeService;
    private final ISohuUserCollectService sohuUserCollectService;
    private final ISohuUserFollowService sohuUserFollowService;
    private final ISohuDislikeService iSohuDislikeService;
    private final SohuVideoInfoMapper sohuVideoInfoMapper;
    private final SohuVideoRelateMapper sohuVideoRelateMapper;
    private final SohuContentMainMapper sohuContentMainMapper;
    private final SohuMcnUserMapper sohuMcnUserMapper;
    private final ISohuPlayletService playLetService;
    private final ISohuLessonLabelService sohuLessonLabelService;
    private final ISohuCategoryService categoryService;
    private final ISohuTradeRecordService sohuTradeRecordService;
    private final ISohuAirecTagRelationService iSohuAirecTagRelationService;
    private final ISohuAirecContentItemService iSohuAirecContentItemService;
    private final ISohuInteractNoticeService sohuInteractNoticeService;
    private final ISohuSiteService siteService;
    private final ISohuPlayletUserService sohuPlayletUserService;
    private final ISohuUserService sohuUserService;
    private final SohuCommentMapper sohuCommentMapper;
    private final ISohuContentLifecycleService sohuContentLifecycleService;
    private final ISohuPlatformIndustryRelationService relationService;
    private final ISohuBusyBlackService busyBlackService;

    @DubboReference
    private RemoteFileService remoteFileService;
    @DubboReference
    private RemoteEntryService remoteEntryService;
    @DubboReference
    private RemoteMiddleSystemNoticeService remoteMiddleSystemNoticeService;
    @DubboReference
    private RemoteMiddleUserBusyTaskExecuteRecordService remoteMiddleUserBusyTaskExecuteRecordService;
    @DubboReference
    private RemoteMiddleEventReportService remoteMiddleEventReportService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    @DubboReference
    private RemoteBanRecordService remoteBanRecordService;
    @DubboReference
    private RemoteMiddleIndependentMaterialService remoteMiddleIndependentMaterialService;
    @DubboReference
    private RemoteBusyTaskSiteService remoteBusyTaskSiteService;

    private final AsyncConfig asyncConfig;

    /**
     * 最大可提交次数，TODO 后期改为可配置参数
     */
    private static final Integer SUBMIT_NUM_MAX_VALUE = 3;
    /**
     * 回收站超时时间，单位天
     */
    private final static Integer CON_RECYCLE_DATA_TIME_OUT = 14;


//    @DubboReference
//    private RemoteSohuAirecContentItemService remoteSohuAirecContentItemService;

    @Override
    public Boolean videoView(Long id) {
        SohuVideoVo videoVo = baseMapper.selectVoById(id);
        if (Objects.isNull(videoVo)) {
            return false;
        }
        Long userId = LoginHelper.getUserId();
        if (userId != null && userId > 0L) {
            // 记录用户数据浏览量
            String videoKey = CacheConstants.VIDEO + userId;
            Set<Long> cacheListIds = RedisUtils.getCacheObject(videoKey);
            if (CollUtil.isEmpty(cacheListIds)) {
                cacheListIds = new LinkedHashSet<>();
            }
            cacheListIds.add(id);
            RedisUtils.setCacheObject(videoKey, cacheListIds);
        }
        sohuContentMainMapper.updateViewCount(id, BusyType.Video.name());
        return true;
    }

    @Override
    public SohuVideoVo selectVoById(Long id) {
        SohuVideoVo sohuVideoVo = baseMapper.selectVoById(id);
        SohuVideoInfoVo infoVo = sohuVideoInfoService.queryByVideoId(id);
        if (Objects.nonNull(infoVo)) {
            sohuVideoVo.setIntro(infoVo.getIntro());
        }
        SohuCategoryVo categoryVo = categoryService.queryById(sohuVideoVo.getCategoryId());
        if (Objects.nonNull(categoryVo)) {
            String categoryName = categoryVo.getName();
            if (StrUtil.isNotBlank(categoryName)) {
                sohuVideoVo.setCategoryName(categoryName);
            }
        }
        return sohuVideoVo;
    }

    @Override
    public Long getAuthorId(Long id) {
        if (CalUtils.isNullOrZero(id)) {
            return 0L;
        }
        SohuVideoVo videoVo = this.get(id);
        return Objects.isNull(videoVo) ? 0L : videoVo.getUserId();
    }

    @Override
    public SohuVideoVo get(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询视频
     */
    @Override
    public SohuVideoVo queryById(Long id) {
        SohuVideoVo videoVo = baseMapper.selectVoById(id);
        if (Objects.isNull(videoVo)) {
            return null;
        }
        // 设置封面
        if (NumberUtil.isNumber(videoVo.getCoverImage())) {
            String url = remoteFileService.getUrl(Long.valueOf(videoVo.getCoverImage()));
            videoVo.setCoverUrl(url);
            videoVo.setCoverImage(url);
        }
        SohuVideoInfoVo infoVo = sohuVideoInfoService.queryByVideoId(id);
        // 设置内容
        if (Objects.nonNull(infoVo)) {
            videoVo.setCollectCount(infoVo.getCollectCount());
            videoVo.setViewCount(infoVo.getViewCount());
            videoVo.setCommentCount(infoVo.getCommentCount());
            videoVo.setPraiseCount(infoVo.getPraiseCount());
            videoVo.setForwardCount(infoVo.getForwardCount());
            videoVo.setLearnNum(infoVo.getLearnNum());
        }
        // 设置作者信息
        LoginUser authorUser = sohuUserService.selectById(videoVo.getUserId());
        if (Objects.nonNull(authorUser)) {
            videoVo.setUserAvatar(StrUtil.isNotBlank(authorUser.getAvatar()) ? authorUser.getAvatar() : Constants.DEFAULT_AVATAR);
            videoVo.setUserName(StrUtil.isNotBlank(authorUser.getNickname()) ? authorUser.getNickname() : authorUser.getUsername());
        } else {
            videoVo.setUserAvatar(Constants.DEFAULT_USER_NICKNAME);
            videoVo.setUserName(Constants.DEFAULT_AVATAR);
        }

        // 关联项
        videoVo.setRelation(this.getRelationRespVo(id));
        //构建智能推荐
        AliyunAirecUtil.buildAiRecommend(videoVo, AliyunAirecContentItemTypeEnum.VIDEO.getCode());

        Long loginId = LoginHelper.getUserId();
        if (Objects.isNull(loginId) || loginId <= 0L) {
            return videoVo;
        }
        String busyType;
        if (VideoEnum.Type.playlet.getCode().equals(videoVo.getType())) {
            busyType = BusyType.PlayletVideo.name();
        } else {
            busyType = BusyType.Video.name();
        }

        // 设置作者关注状态
        Map<Long, SohuUserFollowVo> userFollowMap = sohuUserFollowService.mapUserFollows(loginId, Collections.singletonList(videoVo.getUserId()));
        Map<Long, SohuUserLikeVo> likeVoMap = sohuUserLikeService.queryMap(loginId, busyType, Collections.singletonList(videoVo.getId()));
        Map<Long, SohuUserCollectVo> collectVoMap = sohuUserCollectService.queryMap(loginId, busyType, Collections.singletonList(videoVo.getId()));

        // 浏览量和学习人数+1 只有审核通过的才+1
        if (StrUtil.equalsAnyIgnoreCase(videoVo.getState(), CommonState.OnShelf.getCode())) {
            sohuVideoInfoMapper.updateViewCount(id);
        }
        if (StrUtil.isNotBlank(videoVo.getEpisodeRelevance())) {
            // 如果视频是短剧
            sohuPlayletUserService.saveOrUpdate(videoVo, loginId, null, null);
        }
        // 我关注状态，true = 关注
        videoVo.setFollowObj(Objects.nonNull(userFollowMap.get(videoVo.getUserId())));
        // 我点赞状态，true = 点赞
        videoVo.setPraiseObj(Objects.nonNull(likeVoMap.get(videoVo.getId())));
        // 我收藏状态，true = 收藏
        videoVo.setCollectObj(Objects.nonNull(collectVoMap.get(videoVo.getId())));
        // 视频详情埋点
        videoVo.setEventId(buildVideoEventRecord(RecreationReportEnum.CKSP, loginId));
        return videoVo;
    }

    /**
     * 视频埋点
     *
     * @param reportEnum
     * @param userId
     * @return
     */
    public String buildVideoEventRecord(RecreationReportEnum reportEnum, Long userId) {
        SohuEventReportBo bo = new SohuEventReportBo(
                RecreationReportEnum.getCode(reportEnum.getType()),
                reportEnum.getDesc(),
                ActionTypeEnum.Recreation.name(),
                reportEnum.getType(),
                userId);

        return remoteMiddleEventReportService.getEventId(bo);
    }

    @Override
    public SohuVideoVo queryById(Long id, Boolean isIndependent, Long userId) {
        SohuVideoVo videoVo = baseMapper.selectVoById(id);
        if (Objects.isNull(videoVo)) {
            return null;
        }
        // 设置封面
        if (NumberUtil.isNumber(videoVo.getCoverImage())) {
            String url = remoteFileService.getUrl(Long.valueOf(videoVo.getCoverImage()));
            videoVo.setCoverUrl(url);
            videoVo.setCoverImage(url);
        }
        SohuVideoInfoVo infoVo = sohuVideoInfoService.queryByVideoId(id);
        // 设置内容
        if (Objects.nonNull(infoVo)) {
            videoVo.setCollectCount(infoVo.getCollectCount());
            videoVo.setViewCount(infoVo.getViewCount());
            videoVo.setCommentCount(infoVo.getCommentCount());
            videoVo.setPraiseCount(infoVo.getPraiseCount());
            videoVo.setForwardCount(infoVo.getForwardCount());
            videoVo.setLearnNum(infoVo.getLearnNum());
        }
        // 设置作者信息
        LoginUser authorUser = sohuUserService.selectById(videoVo.getUserId());
        if (Objects.nonNull(authorUser)) {
            videoVo.setUserAvatar(StrUtil.isNotBlank(authorUser.getAvatar()) ? authorUser.getAvatar() : Constants.DEFAULT_AVATAR);
            videoVo.setUserName(StrUtil.isNotBlank(authorUser.getNickname()) ? authorUser.getNickname() : authorUser.getUsername());
        } else {
            videoVo.setUserAvatar(Constants.DEFAULT_USER_NICKNAME);
            videoVo.setUserName(Constants.DEFAULT_AVATAR);
        }

        // 关联项
        videoVo.setRelation(this.getRelationRespVo(id));
        //构建智能推荐
        AliyunAirecUtil.buildAiRecommend(videoVo, AliyunAirecContentItemTypeEnum.VIDEO.getCode());
        if (Objects.isNull(userId) || userId <= 0L) {
            return videoVo;
        }
        String busyType;
        if (VideoEnum.Type.playlet.getCode().equals(videoVo.getType())) {
            busyType = BusyType.PlayletVideo.name();
        } else {
            busyType = BusyType.Video.name();
        }

        // 设置作者关注状态
        Map<Long, SohuUserFollowVo> userFollowMap = sohuUserFollowService.mapUserFollows(userId, Collections.singletonList(videoVo.getUserId()));
        Map<Long, SohuUserLikeVo> likeVoMap = sohuUserLikeService.queryMap(userId, busyType, Collections.singletonList(videoVo.getId()));
        Map<Long, SohuUserCollectVo> collectVoMap = sohuUserCollectService.queryMap(userId, busyType, Collections.singletonList(videoVo.getId()));

        // 浏览量和学习人数+1 只有审核通过的才+1
        if (StrUtil.equalsAnyIgnoreCase(videoVo.getState(), CommonState.OnShelf.getCode())) {
            sohuVideoInfoMapper.updateViewCount(id);
        }
        if (StrUtil.isNotBlank(videoVo.getEpisodeRelevance())) {
            // 如果视频是短剧
            sohuPlayletUserService.saveOrUpdate(videoVo, userId, null, null);
        }
        // 我关注状态，true = 关注
        videoVo.setFollowObj(Objects.nonNull(userFollowMap.get(videoVo.getUserId())));
        // 我点赞状态，true = 点赞
        videoVo.setPraiseObj(Objects.nonNull(likeVoMap.get(videoVo.getId())));
        // 我收藏状态，true = 收藏
        videoVo.setCollectObj(Objects.nonNull(collectVoMap.get(videoVo.getId())));
        if (isIndependent) {
            SohuUserBusyTaskExecuteRecordBo bo = new SohuUserBusyTaskExecuteRecordBo();
            bo.setUserId(LoginHelper.getUserId());
            bo.setBusyCode(String.valueOf(videoVo.getId()));
            bo.setBusyType(BusyType.Video.getType());
            bo.setBusyBelonged(videoVo.getUserId());
            bo.setOperationTime(new Date());
            remoteMiddleUserBusyTaskExecuteRecordService.insertByBo(bo);
        }
        // 视频详情埋点
        videoVo.setEventId(buildVideoEventRecord(RecreationReportEnum.CKSP, userId));
        return videoVo;
    }

    @Override
    public List<SohuVideoVo> queryVideoByIds(List<Long> ids, String state) {

        List<SohuVideoVo> sohuVideoVoList = baseMapper.selectVoList(new LambdaQueryWrapper<SohuVideo>().in(SohuVideo::getId, ids)
                .eq(SohuVideo::getState, state)
                .eq(SohuVideo::getAuditState, AuditState.Pass));
        if (CollectionUtils.isNotEmpty(sohuVideoVoList)) {
            this.getRecord(sohuVideoVoList, LoginHelper.getUserId());
        }
        return sohuVideoVoList;
    }

    private void setListRelation(List<SohuVideoVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> videoIds = list.stream().map(SohuVideoVo::getId).collect(Collectors.toList());
        Map<Long, SohuVideoRelateVo> relateMap = sohuVideoRelateService.queryMapByVideoIds(videoIds);
        for (SohuVideoVo videoVo : list) {
            SohuVideoRelateVo relate = relateMap.get(videoVo.getId());
            if (Objects.isNull(relate)) {
                continue;
            }
            videoVo.setRelation(this.getRelationRespVo(relate));
        }
    }

    /**
     * 根据id获取内容关联项
     *
     * @param relate
     * @return
     */
    private RelationRespVo getRelationRespVo(SohuVideoRelateVo relate) {
        RelationRespVo relation = new RelationRespVo();
        if (Objects.isNull(relate)) {
            return null;
        }
        if (StrUtil.equalsAnyIgnoreCase(relate.getBusyType(), BusyType.GoodsWindow.name(), BusyType.Window.name(),
                BusyType.BusyTask.name(), BusyType.SohuLesson.name(), BusyType.EntryRole.name())) {
            //橱窗商品
            buildRelationInfo(relation, relate);
        }
        return relation;
    }

    /**
     * 根据id获取内容关联项
     *
     * @param id
     * @return
     */
    private RelationRespVo getRelationRespVo(Long id) {
        SohuVideoRelateVo relate = sohuVideoRelateService.getByVideoId(id);
        return Objects.isNull(relate) ? null : this.getRelationRespVo(relate);
    }

    private static void buildRelationInfo(RelationRespVo relation, SohuVideoRelateVo relate) {
        relation.setBusyCode(relate.getBusyCode());
        relation.setBusyType(relate.getBusyType());
        relation.setBusyTitle(relate.getBusyTitle());
        relation.setBusyInfo(relate.getBusyInfo());
        relation.setChildTaskNumber(relate.getBusyInfo());
    }

    /**
     * 查询视频列表
     */
    @Override
    public TableDataInfo<SohuVideoVo> queryPageList(SohuVideoBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        // 超级管理员
        boolean isAdmin = LoginHelper.hasRole(loginUser, RoleCodeEnum.ADMIN);
        // 国家站长
        boolean isCountry = LoginHelper.hasRole(loginUser, RoleCodeEnum.CountryStationAgent);
        // 城市站长
        boolean isCity = LoginHelper.hasRole(loginUser, RoleCodeEnum.CityStationAgent);
        if (isAdmin || isCountry || isCity) {

        } else {
            bo.setUserId(LoginHelper.getUserId());
        }
        LambdaQueryWrapper<SohuVideo> lqw = buildQueryWrapper(bo);
        lqw.eq(SohuVideo::getDelFlag, false);
        lqw.orderByAsc(SohuVideo::getSortIndex);
        lqw.orderByDesc(SohuVideo::getCreateTime);
        Page<SohuVideoVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            Set<Long> ossIds = new HashSet<>();
            List<Long> videoIds = new ArrayList<>();
            Set<Long> siteIds = new HashSet<>();
            for (SohuVideoVo record : result.getRecords()) {
                if (NumberUtil.isNumber(record.getCoverImage())) {
                    ossIds.add(Long.valueOf(record.getCoverImage()));
                }
                videoIds.add(record.getId());
                siteIds.add(record.getSiteId());
            }
            Map<Long, SohuVideoInfoVo> infoVoMap = sohuVideoInfoService.selectMap(videoIds);
            Map<Long, String> map = remoteFileService.map(ossIds);
            Map<Long, SohuSiteVo> siteModelMap = siteService.queryMap(siteIds);
            //if (map != null) {
            for (SohuVideoVo record : result.getRecords()) {
                record.setInfo(infoVoMap.get(record.getId()));
                if (NumberUtil.isNumber(record.getCoverImage()) && map != null) {
                    record.setCoverUrl(map.get(Long.valueOf(record.getCoverImage())));
                } else {
                    record.setCoverUrl(record.getCoverImage());
                }
                record.setRelation(this.getRelationRespVo(record.getId()));
                //设置分类名
                SohuCategoryVo sohuCategoryVo = categoryService.queryById(record.getCategoryId());
                record.setCategoryName(Objects.nonNull(sohuCategoryVo) ? sohuCategoryVo.getName() : null);
                // 设置审核状态以及站点名称
                setAuditStatus(record, siteModelMap);
                record.setContentState(record.getState());
                // 设置用户头像以及昵称
                record.setBusyType(BusyType.Video.getType());
                if (Objects.nonNull(loginUser)) {
                    record.setUserName(loginUser.getNickname());
                    record.setUserAvatar(loginUser.getAvatar());
                } else {
                    record.setUserName(Constants.DEFAULT_USER_NICKNAME);
                    record.setUserAvatar(Constants.DEFAULT_AVATAR);
                }
                // 设置移出时间
                if (bo.getIsBlack()) {
                    SohuBusyBlackVo sohuBusyBlackVo = null;
                    if (Objects.nonNull(bo.getIndustryId())) {
                        sohuBusyBlackVo = busyBlackService.queryByParam(record.getId(), 2, BusyType.Video.getType());
                    } else {
                        sohuBusyBlackVo = busyBlackService.queryByParam(record.getId(), 1, BusyType.Video.getType());
                    }
                    record.setRemoveTime(sohuBusyBlackVo.getCreateTime());
                }
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public SohuConentListStatVo queryPageListStat(SohuVideoBo bo) {
        List<Long> handleIds = new ArrayList<>();
        if (Objects.nonNull(bo.getIndustryId())) {
            // 行业id不为空,则查询相关行业对应的分类
            SohuPlatformIndustryRelationBo relationBo = new SohuPlatformIndustryRelationBo();
            relationBo.setPlatformIndustryId(bo.getIndustryId());
            relationBo.setBusyType(BusyType.Content.getType());
            List<SohuPlatformIndustryRelationVo> relationList = relationService.queryList(relationBo);
            List<Long> categoryIds = relationList.stream().map(SohuPlatformIndustryRelationVo::getBusyCategoryId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(bo.getCategoryIds())) {
                bo.setCategoryIds(categoryIds);
            } else {
                // 取交集
                categoryIds.retainAll(bo.getCategoryIds());
                bo.setCategoryIds(categoryIds);
            }
            // 查询行业黑名单
            handleIds = busyBlackService.listBusyIds(bo.getIndustryId(), 2, BusyType.Video.getType());
        } else if (Objects.nonNull(bo.getSiteId()) && LoginHelper.hasRole(LoginHelper.getLoginUser(), RoleCodeEnum.CityStationAgent)) {
            // 站点id不为空,则查询行业黑名单
            handleIds = busyBlackService.listBusyIds(bo.getSiteId(), 1, BusyType.Video.getType());
        }
        if (bo.getIsBlack() && CollectionUtils.isEmpty(handleIds)) {
            handleIds.add(0L);
        }
        bo.setHandleIds(handleIds);
        bo.setUserId(LoginHelper.getUserId());
        LambdaQueryWrapper<SohuVideo> lqw = buildQueryWrapper(bo);
        if (StrUtil.isNotBlank(bo.getState()) && (!Constants.ALL.equals(bo.getState()))) {
            lqw.eq(SohuVideo::getState, bo.getState());
        }
        lqw.eq(SohuVideo::getDelFlag, 0);
        SohuConentListStatVo vo = this.baseMapper.queryPageListStat(lqw);
        if (Objects.isNull(vo)) {
            vo = new SohuConentListStatVo();
        }
        Long allNum = vo.getOnShelfNum() + vo.getOffShelfNum() + vo.getWaitApproveNum() + vo.getRefuseNum();
        vo.setAllNum(allNum);
        return vo;
    }

    @Override
    public TableDataInfo<SohuVideoVo> queryPageListOfOnShelf(SohuVideoBo bo, PageQuery pageQuery) {
//        LambdaQueryWrapper<SohuVideo> lqw = buildQueryWrapper(bo);
//        lqw.eq(SohuVideo::getState, CommonState.OnShelf.name());
//        lqw.eq(bo.getId() != null, SohuVideo::getId, bo.getId());
//        lqw.isNull(SohuVideo::getEpisodeRelevance);
//        lqw.in(CollUtil.isNotEmpty(bo.getCategoryIds()), SohuVideo::getCategoryId, bo.getCategoryIds());
//        lqw.eq(SohuVideo::getVisibleType, VisibleTypeEnum.open.getCode());
//        lqw.like(StringUtils.isNotBlank(bo.getTitle()), SohuVideo::getTitle, bo.getTitle());
//        lqw.orderByAsc(SohuVideo::getSortIndex);
//        lqw.orderByDesc(SohuVideo::getCreateTime);
//        Page<SohuVideoVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        Page<SohuVideoVo> result = baseMapper.queryPageListOfOnShelf(PageQueryUtils.build(pageQuery), bo);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            Set<Long> ossIds = new HashSet<>();
            Set<Long> userIds = new HashSet<>();
            Set<Long> categoryIds = new HashSet<>();
            for (SohuVideoVo record : result.getRecords()) {
                if (NumberUtil.isNumber(record.getCoverImage())) {
                    ossIds.add(Long.valueOf(record.getCoverImage()));
                }
                userIds.add(record.getUserId());
                categoryIds.add(record.getCategoryId());
            }
            Map<Long, String> map = remoteFileService.map(ossIds);
            Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
            Map<Long, SohuCategoryVo> sohuCategoryVoMap = categoryService.queryMap(categoryIds);
            for (SohuVideoVo record : result.getRecords()) {
                if (StrUtil.isNotBlank(record.getCoverImage())) {
                    if (NumberUtil.isNumber(record.getCoverImage())) {
                        record.setCoverUrl(map.get(record.getCoverImage()));
                    } else {
                        record.setCoverUrl(record.getCoverImage());
                    }
                }
                // 设置用户头像以及昵称
                LoginUser loginUser = userMap.get(record.getUserId());
                if (Objects.nonNull(loginUser)) {
                    record.setUserName(loginUser.getUsername());
                    record.setNickName(loginUser.getNickname());
                    record.setUserAvatar(loginUser.getAvatar());
                } else {
                    record.setUserName(Constants.DEFAULT_USER_NICKNAME);
                    record.setNickName(Constants.DEFAULT_USER_NICKNAME);
                    record.setUserAvatar(Constants.DEFAULT_AVATAR);
                }
                SohuCategoryVo sohuCategoryVo = sohuCategoryVoMap.get(record.getCategoryId());
                if (Objects.nonNull(sohuCategoryVo)) {
                    record.setCategoryName(sohuCategoryVo.getName());
                }
            }
        }
        return TableDataInfoUtils.build(result);
    }

    /**
     * 设置审核状态
     */
    private static void setAuditStatus(SohuVideoVo record, Map<Long, SohuSiteVo> siteModelMap) {
        SohuSiteVo sohuSiteModel = siteModelMap.get(record.getSiteId());
        if (Objects.isNull(sohuSiteModel)) {
            return;
        }
        record.setSiteName(sohuSiteModel.getName());
        Set<String> allowedStates = new HashSet<>(Arrays.asList(CommonState.WaitApprove.getCode(), CommonState.OnShelf.getCode(), CommonState.Refuse.getCode()));
        // 获取当前状态
        String currentState = record.getState();
        // 判断当前状态是否在允许的状态列表中
        if (allowedStates.contains(currentState)) {
            record.setContentState(currentState);
        }
    }

    @Override
    public TableDataInfo<SohuVideoVo> videoPageCenter(Long userId, PageQuery pageQuery) {
        Long loginUserId = LoginHelper.getUserId();
        if (userId == null) {
            userId = loginUserId;
        }
        SohuVideoBo dto = new SohuVideoBo();
        dto.setUserId(userId);
        List<String> states = new ArrayList<>();
        states.add(BusyOrderStatus.OnShelf.name());
        if (Objects.equals(loginUserId, userId)) {
            // 查看自己的
            states.add(BusyOrderStatus.WaitApprove.name());
            states.add(BusyOrderStatus.CompelOff.name());
            states.add(BusyOrderStatus.Hide.name());
        }
        dto.setStateList(states);
        if (dto.getUserId() == null) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        return queryPage(dto, pageQuery);
    }

    /**
     * 关注视频列表  暂定规则 展示我关注的用户数据
     */
    @Override
    public TableDataInfo<SohuVideoVo> followPage(PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            throw new RuntimeException(MessageUtils.message("user.not.login"));
        }
        //准备播放内容的作者id集合
        List<Long> authorIds = new ArrayList<>();
        //我自己
        authorIds.add(userId);
        // 我关注的用户
        SohuUserFollowBo followBo = new SohuUserFollowBo();
        followBo.setUserId(userId);
        List<SohuUserFollowVo> followVos = sohuUserFollowService.queryList(followBo);
        if (CollectionUtils.isNotEmpty(followVos)) {
            List<Long> focusUser = followVos.stream().map(SohuUserFollowVo::getFocusUserId).collect(Collectors.toList());
            authorIds.addAll(focusUser);
            //关注的用户的好友
           /* LambdaQueryWrapper<SohuFriends> friendsWrapper = new LambdaQueryWrapper<>();
            friendsWrapper.in(SohuFriends::getUserId, focusUser).eq(SohuFriends::getApplyState, ApplyStateEnum.pass.name());
            List<SohuFriends> friendsList = this.sohuFriendsMapper.selectList(friendsWrapper);
            if (CollectionUtils.isNotEmpty(friendsList)) {
                List<Long> friendIds = friendsList.stream().map(SohuFriends::getFriendId).collect(Collectors.toList());
                authorIds.addAll(friendIds);
            }*/
        }
        //去重
        authorIds = authorIds.stream().distinct().collect(Collectors.toList());
        SohuVideoBo bo = new SohuVideoBo();
        bo.setUserIds(authorIds);
        bo.setState(CommonState.OnShelf.getCode());
        return queryPage(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuVideoVo> queryMCNVideoList(SohuMcnVideoReqBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuVideo> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuVideo::getMcnUserId, bo.getMcnUserId()).eq(Objects.nonNull(bo.getUserId()), SohuVideo::getUserId, bo.getUserId()).like(StrUtil.isNotBlank(bo.getTitle()), SohuVideo::getTitle, bo.getTitle());
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            lqw.ge(StrUtil.isNotBlank(bo.getStartTime()), SohuVideo::getCreateTime, DateUtil.beginOfDay(DateUtil.parseDate(bo.getStartTime())));
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            lqw.le(StrUtil.isNotBlank(bo.getEndTime()), SohuVideo::getCreateTime, DateUtil.endOfDay(DateUtil.parseDate(bo.getEndTime())));
        }
        Page<SohuVideoVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            Set<Long> userIds = result.getRecords().stream().filter(p -> Objects.nonNull(p.getUserId())).map(p -> p.getUserId()).collect(Collectors.toSet());
            Set<Long> videoIds = result.getRecords().stream().map(p -> p.getId()).collect(Collectors.toSet());
            Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
            List<SohuVideoInfo> infoList = this.sohuVideoInfoMapper.selectList(SohuVideoInfo::getVideoId, videoIds);
            Map<Long, SohuVideoInfo> infoMap = infoList.stream().collect(Collectors.toMap(SohuVideoInfo::getVideoId, u -> u));
            for (SohuVideoVo vo : result.getRecords()) {
                LoginUser loginUser = userMap.get(vo.getUserId());
                if (Objects.nonNull(loginUser)) {
                    vo.setUserAvatar(loginUser.getAvatar());
                    vo.setNickName(loginUser.getNickname());
                }
                SohuVideoInfo info = infoMap.get(vo.getId());
                if (Objects.nonNull(info)) {
                    vo.setViewCount(info.getViewCount());
                    vo.setCommentCount(info.getCommentCount());
                    vo.setPraiseCount(info.getPraiseCount());
                }
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public Boolean logicDeleteById(Long id) {
        SohuVideo sohuVideo = baseMapper.selectById(id);
        if (Objects.isNull(sohuVideo)) {
            return false;
        }
        SohuContentLifecycleBo lifecycleBo = new SohuContentLifecycleBo(id, BusyType.Video.name(), sohuVideo.getState(), CommonState.Delete.name(), "自主删除");
        if (!Objects.equals(LoginHelper.getUserId(), sohuVideo.getUserId())) {
            throw new RuntimeException("非法操作，这不是您的数据");
        }
        sohuVideo.setState(CommonState.Delete.name());
        sohuVideo.setDelTime(DateUtils.getNowDate());
        baseMapper.updateById(sohuVideo);
        this.createLifecycleLog(lifecycleBo);
        iSohuAirecContentItemService.updateStatusToOffShelf(id.toString(), AliyunAirecContentItemTypeEnum.VIDEO.getCode());
        return true;
    }

    @Override
    public boolean logicDeleteById(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        for (Long id : ids) {
            this.logicDeleteById(id);
        }
        return true;
    }

    @Override
    public boolean logicForceDeleteById(Long id) {
        if (!LoginHelper.anyMatchRole(RoleCodeEnum.ADMIN.getCode(), RoleCodeEnum.CALL_ADMIN.getCode(), RoleCodeEnum.OPERATION_ADMIN.getCode())) {
            throw new RuntimeException("非法操作，您无权操作");
        }
        SohuVideo sohuVideo = baseMapper.selectById(id);
        if (Objects.isNull(sohuVideo)) {
            return false;
        }
        SohuContentLifecycleBo lifecycleBo = new SohuContentLifecycleBo(id, BusyType.Video.name(), sohuVideo.getState(), CommonState.ForceDelete.name(), "强制删除");
        sohuVideo.setState(CommonState.ForceDelete.name());
        sohuVideo.setDelTime(DateUtils.getNowDate());
        baseMapper.updateById(sohuVideo);
        this.createLifecycleLog(lifecycleBo);
        iSohuAirecContentItemService.updateStatusToOffShelf(id.toString(), AliyunAirecContentItemTypeEnum.VIDEO.getCode());
        return true;
    }

    @Override
    public boolean logicForceDeleteById(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        for (Long id : ids) {
            this.logicForceDeleteById(id);
        }
        return true;
    }

    /**
     * 记录生命周期
     *
     * @param lifecycleBo
     * @return
     */
    private Boolean createLifecycleLog(SohuContentLifecycleBo lifecycleBo) {
        if (Objects.equals(lifecycleBo.getLastState(), CommonState.OnShelf.name())
                || Objects.equals(lifecycleBo.getLastState(), CommonState.WaitApprove.name())) {
            if (Objects.equals(lifecycleBo.getCurrentState(), CommonState.ForceDelete.name())) {
                lifecycleBo.setCurrentState(CommonState.CompelOff.name());
                this.sohuContentLifecycleService.insertByBo(lifecycleBo);
                lifecycleBo.setId(null);
                lifecycleBo.setLastState(CommonState.CompelOff.name());
                lifecycleBo.setCurrentState(CommonState.ForceDelete.name());
            } else if (Objects.equals(lifecycleBo.getCurrentState(), CommonState.Delete.name())) {
                lifecycleBo.setCurrentState(CommonState.OffShelf.name());
                this.sohuContentLifecycleService.insertByBo(lifecycleBo);
                lifecycleBo.setId(null);
                lifecycleBo.setLastState(CommonState.OffShelf.name());
                lifecycleBo.setCurrentState(CommonState.Delete.name());
            }
        }
        this.sohuContentLifecycleService.insertByBo(lifecycleBo);
        return true;
    }

    @Override
    public Long getMcnVideoPraiseStat(Long userId, Long mcnId) {
        // 获取视频集合
        List<SohuVideoInfo> videoInfoList = this.getVideoInfoList(userId, mcnId);
        // 汇总视频点赞数

        return videoInfoList.stream().mapToLong(SohuVideoInfo::getPraiseCount).sum();
    }

    @Override
    public Long getMcnVideoCommentStat(Long userId, Long mcnId) {
        // 获取视频集合
        List<SohuVideoInfo> videoInfoList = this.getVideoInfoList(userId, mcnId);
        // 汇总视频评论数

        return videoInfoList.stream().mapToLong(SohuVideoInfo::getCommentCount).sum();
    }

    @Override
    public Long getMcnVideoViewStat(Long userId, Long mcnId) {
        // 获取视频集合
        List<SohuVideoInfo> videoInfoList = this.getVideoInfoList(userId, mcnId);
        // 汇总视频评论数

        return videoInfoList.stream().mapToLong(SohuVideoInfo::getViewCount).sum();
    }

    @Override
    public IPage<SohuVideoVo> businessVideoList(SohuBusinessVideoBo bo) {
        Page page = new Page(bo.getPageNum(), bo.getPageSize());
        IPage<SohuVideoVo> result = baseMapper.businessVideoList(page, bo);
//        List<SohuVideoVo> records = result.getRecords();
//        if (CollUtil.isNotEmpty(records)) {
//            Set<Long> userIds = new HashSet<>();
//            Set<Long> videoIds = new HashSet<>();
//            records.forEach(p -> {
//                userIds.add(p.getUserId());
//                videoIds.add(p.getId());
//            });
//            List<SohuVideoInfo> infoList = this.sohuVideoInfoMapper.selectList(SohuVideoInfo::getVideoId, videoIds);
//            Map<Long, SohuVideoInfo> infoMap = infoList.stream().collect(Collectors.toMap(SohuVideoInfo::getVideoId, u -> u));
//            Map<Long, LoginUser> userMap = this.sohuUserService.selectMap(userIds);
//            for (SohuVideoVo vo : result.getRecords()) {
//                LoginUser user = userMap.get(vo.getUserId());
//                if (Objects.nonNull(user)) {
//                    vo.setUserAvatar(user.getAvatar());
//                    vo.setUserName(user.getUsername());
//                    vo.setNickName(user.getNickname());
//                }
//                SohuVideoInfo info = infoMap.get(vo.getId());
//                if (Objects.isNull(info)) {
//                    continue;
//                }
//                vo.setViewCount(info.getViewCount());
//                vo.setCommentCount(info.getCommentCount());
//                vo.setPraiseCount(info.getPraiseCount());
//                // 关联项目，TODO 待优化
//                vo.setRelation(this.getRelationRespVo(vo.getId()));
//            }
//            result.setRecords(records);
//        }
        this.buildVideoVo(result.getRecords());
        return result;
    }

    @Override
    public TableDataInfo<SohuVideoVo> businessVideoListOfAirec(SohuBusinessVideoBo bo) {
        List<SohuVideoVo> sohuVideoVoList = new ArrayList<>();
        if (ObjectUtils.isNotNull(bo.getVideoId())) {
            SohuVideoVo sohuVideoVo = baseMapper.selectBusinessVideoById(bo.getVideoId());
            sohuVideoVoList.add(sohuVideoVo);
        }
        if (bo.getAiRec()) {
            if (StrUtil.isBlank(bo.getAiRecImei()) && Objects.isNull(LoginHelper.getUserId())) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (!(AliyunAirecConstant.SCENE_VIDEO_ALL.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_VIDEO_HOMEPAGE.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_VIDEO_MY_MAY_LIKE.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_VIDEO_MONEYMAKING.equals(bo.getAiRecSceneId()))) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (Objects.nonNull(LoginHelper.getUserId())) {
                bo.setAiUserId(LoginHelper.getUserId().toString());
            }
            bo.setAiReturnCount(bo.getPageSize());
            //构造过滤的内容
            SohuAirecVideoQueryBo airecQueryBo = new SohuAirecVideoQueryBo();
            if (Objects.nonNull(bo.getSiteId())) {
                SohuSiteVo sohuSiteVo = siteService.queryById(bo.getSiteId());
                if (Objects.nonNull(sohuSiteVo)) {
                    if (Objects.equals(sohuSiteVo.getType(), SiteType.City.name())) {
                        airecQueryBo.setCity(String.valueOf(sohuSiteVo.getId()));
                    } else if (Objects.equals(sohuSiteVo.getType(), SiteType.Country.name())) {
                        airecQueryBo.setCountry(sohuSiteVo.getCountryCode());
                    }
                }
            }
            AliyunAirecJoinFilterRule rootRule = airecQueryBo.buildAirecFilterRule();
            //this.buildAirecJoinFilterRule(bo.getSiteId(), rootRule);
            //获取阿里云智能推荐结果
            List<SohuVideoVo> resultList = AliyunAirecUtil.aiRecommendSingleType(rootRule, bo, itemIds -> baseMapper.selectVoBatchIds(itemIds));
            if (CollUtil.isNotEmpty(resultList)) {
                getRecord(resultList, LoginHelper.getUserId());
                sohuVideoVoList.addAll(resultList);
                return TableDataInfoUtils.build(sohuVideoVoList);
            }
        }
        IPage<SohuVideoVo> iPage = this.businessVideoList(bo);
        sohuVideoVoList.addAll(iPage.getRecords());
        TableDataInfo<SohuVideoVo> tableDataInfo = TableDataInfoUtils.build(sohuVideoVoList);
        AliyunAirecUtil.buildAiRecommendSingleType(tableDataInfo.getData(), AliyunAirecContentItemTypeEnum.VIDEO.getCode());
        return tableDataInfo;
    }

    /**
     * 构建视频返回参数，此类里面有相似代码，请重新封装
     *
     * @param records
     */
    @Override
    public void buildVideoVo(List<SohuVideoVo> records) {
        if (CollUtil.isNotEmpty(records)) {
            Set<Long> userIds = new HashSet<>();
            Set<Long> videoIds = new HashSet<>();
            records.forEach(p -> {
                userIds.add(p.getUserId());
                videoIds.add(p.getId());
            });
            List<SohuVideoInfo> infoList = this.sohuVideoInfoMapper.selectList(SohuVideoInfo::getVideoId, videoIds);
            Map<Long, SohuVideoInfo> infoMap = infoList.stream().collect(Collectors.toMap(SohuVideoInfo::getVideoId, u -> u));
            Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
            // 设置作者关注状态
            Map<Long, SohuUserFollowVo> userFollowMap = sohuUserFollowService.mapUserFollows(LoginHelper.getUserId(), new ArrayList<>(userIds));
            for (SohuVideoVo vo : records) {
                LoginUser user = userMap.get(vo.getUserId());
                if (Objects.nonNull(user)) {
                    vo.setUserAvatar(user.getAvatar());
                    vo.setUserName(StrUtil.isNotBlank(user.getNickname()) ? user.getNickname() : user.getUsername());
                    vo.setNickName(StrUtil.isNotBlank(user.getNickname()) ? user.getNickname() : user.getUsername());
                }
                SohuVideoInfo info = infoMap.get(vo.getId());
                if (Objects.isNull(info)) {
                    continue;
                }
                vo.setFollowObj(Objects.nonNull(userFollowMap.get(vo.getUserId())));
                vo.setViewCount(info.getViewCount());
                vo.setCommentCount(info.getCommentCount());
                vo.setPraiseCount(info.getPraiseCount());
                vo.setForwardCount(info.getForwardCount());
                // 关联项目，TODO 待优化
                vo.setRelation(this.getRelationRespVo(vo.getId()));
            }
        }
    }

    @Override
    public void updateRation(Long videoId, Integer ratioEnum) {
        SohuVideo video = new SohuVideo();
        video.setId(videoId);
        video.setAspectRatio(ratioEnum);
        baseMapper.updateById(video);
    }

    @Override
    public void handleRobot(String busyCode, Boolean isPass, String reason) {
        SohuAuditBo auditBo = new SohuAuditBo();
        auditBo.setBusyCode(Long.valueOf(busyCode));
        auditBo.setBusyType(BusyType.Video.getType());
        List<SohuAuditVo> auditList = sohuAuditService.queryList(auditBo);
        if (CollectionUtils.isEmpty(auditList)) {
            return;
        }
        SohuAuditVo auditVo = auditList.stream()
                .filter(record -> record != null && CommonState.WaitApprove.getCode().equals(record.getSysAuditState()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(auditVo)) {
            return;
        }
        SohuAuditBo newAuditBo = new SohuAuditBo();
        newAuditBo.setId(auditVo.getId());
        newAuditBo.setBusyType(BusyType.Video.getType());
        newAuditBo.setState(isPass ? CommonState.OnShelf.getCode() : CommonState.Refuse.getCode());
        newAuditBo.setRejectReason(reason);
        sohuAuditService.process(newAuditBo);
    }


    /**
     * 获取视频实体集合
     *
     * @param userId 当前用户id
     * @param mcnId  MCN机构id
     * @return List
     */
    private List<SohuVideoInfo> getVideoInfoList(Long userId, Long mcnId) {
        LambdaQueryWrapper<SohuVideo> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuVideo::getUserId, userId).eq(SohuVideo::getMcnUserId, mcnId);
        // 获取视频id集合
        List<SohuVideo> articleList = baseMapper.selectList(lqw);
        if (CollectionUtils.isEmpty(articleList)) {
            return new ArrayList<>();
        }
        List<Long> videoIds = articleList.stream().map(SohuVideo::getId).collect(Collectors.toList());

        LambdaQueryWrapper<SohuVideoInfo> infoWrapper = Wrappers.lambdaQuery();
        infoWrapper.in(SohuVideoInfo::getVideoId, videoIds);
        // 根据视频id获取视频集合
        return sohuVideoInfoMapper.selectList(infoWrapper);
    }

    /**
     * 查询视频列表
     */
    @Override
    public TableDataInfo<SohuVideoVo> queryPage(SohuVideoBo bo, PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        //当前用户idset进去为了排序用
        bo.setCurrentUserId(userId);
        // todo 暂时删除看一个少一个
        if (!VideoEnum.Type.lesson.getCode().equals(bo.getType())) {
            if (StrUtil.isBlank(bo.getTitle()) && bo.getUserId() == null && userId != null && CollUtil.isEmpty(bo.getUserIds()) && bo.getIsEnable()) {
                // 看一个少一个 过滤游客，关注，个人中心,以及搜索
                String videoKey = CacheConstants.VIDEO + userId;
                Set<Long> cacheListIds = RedisUtils.getCacheObject(videoKey);
                bo.addToNotIds(cacheListIds);
            }
            if (StrUtil.isBlank(bo.getTitle()) && bo.getUserId() == null && userId != null) {
                // 不感兴趣 过滤游客，个人中心,以及搜索
                List<SohuDislikeVo> sohuDislikeVos = iSohuDislikeService.selectByUserId(userId);
                if (CollUtil.isNotEmpty(sohuDislikeVos)) {
                    Set<Long> dislikeCategory = sohuDislikeVos.stream().filter(a -> DislikeEnum.DislikeCategory.name().equals(a.getDislikeType()) && UserConstants.DISLIKE_CATEGORY_COUNT <= a.getCount()).map(SohuDislikeVo::getDislikeId).collect(Collectors.toSet());
                    bo.setNotCategoryIds(dislikeCategory);
                    Set<Long> dislikeAuthor = sohuDislikeVos.stream().filter(a -> DislikeEnum.DislikeAuthor.name().equals(a.getDislikeType()) && UserConstants.DISLIKE_AUTHOR_COUNT <= a.getCount()).map(SohuDislikeVo::getDislikeId).collect(Collectors.toSet());
                    if (CollUtil.isNotEmpty(bo.getNotUserIds())) {
                        bo.setNotUserIds(Stream.concat(bo.getNotUserIds().stream(), dislikeAuthor.stream()).collect(Collectors.toSet()));
                    } else {
                        bo.setNotUserIds(dislikeAuthor);
                    }
                    // 不感兴趣缓存
                    String videoDislikeKey = CacheConstants.VIDEO_DISLIKE + userId;
                    Set<Long> cacheListIds = RedisUtils.getCacheObject(videoDislikeKey);
                    bo.addToNotIds(cacheListIds);
                }
            }
        }
        LambdaQueryWrapper<SohuVideo> lqw = buildQueryWrapper(bo);
        lqw.orderByAsc(SohuVideo::getSortIndex);
        lqw.orderByDesc(SohuVideo::getCreateTime);


        Page<SohuVideoVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        // 获取不包含不感兴趣的视频列表
//        Page<SohuVideoVo> result = baseMapper.queryVideoListWithoutDislike(PageQueryUtils.build(pageQuery), bo);
        //2023-12-28 修改排序  hezhen
        List<SohuVideoVo> videoList = result.getRecords();
        if (CollUtil.isEmpty(videoList)) {
            return TableDataInfoUtils.build(result);
        }
//        Page<SohuVideoVo> result = baseMapper.selectVideoPages(PageQueryUtils.build(pageQuery),bo);
        getRecord(videoList, userId);
        result.setRecords(videoList);
        return TableDataInfoUtils.build(result);
    }

    @Override
    public TableDataInfo<SohuVideoVo> queryPageOfAirec(SohuVideoBo bo, PageQuery pageQuery) {
        // 过滤封禁用户作品
        filterBanUserContent(bo);
        SohuAirecVideoQueryBo airecQueryBo = new SohuAirecVideoQueryBo();
        if (Objects.nonNull(bo.getSiteId())) {
            List<Long> handleIds = busyBlackService.listBusyIds(bo.getSiteId(), 1, BusyType.Article.getType());
            if (CollUtil.isNotEmpty(bo.getHandleIds())) {
                bo.setHandleIds(Stream.concat(bo.getHandleIds().stream(), handleIds.stream()).distinct().collect(Collectors.toList()));
                airecQueryBo.setBlackIds(Stream.concat(bo.getHandleIds().stream(), handleIds.stream()).distinct().collect(Collectors.toList()));
            } else {
                bo.setHandleIds(handleIds);
                airecQueryBo.setBlackIds(handleIds);
            }
        }
        buildSiteIdRule(bo, airecQueryBo);
        if (ObjectUtils.isNotNull(bo.getTaskNumber())) {
            SohuUserBusyTaskExecuteRecordBo recordBo = new SohuUserBusyTaskExecuteRecordBo();
            recordBo.setUserId(LoginHelper.getUserId());
            recordBo.setBusyCode(String.valueOf(0));
            recordBo.setBusyType(BusyType.Video.getType());
            recordBo.setBusyBelonged(LoginHelper.getUserId());
            recordBo.setOperationTime(new Date());
            recordBo.setTaskNumber(bo.getTaskNumber());
            remoteMiddleUserBusyTaskExecuteRecordService.insertByBo(recordBo);
        }
        List<SohuVideoVo> sohuVideoVoList = new ArrayList<>();
        if (ObjectUtils.isNotNull(bo.getId())) {
            SohuVideoVo sohuVideoVo = baseMapper.selectBusinessVideoById(bo.getId());
            if (Objects.nonNull(sohuVideoVo)) {
                sohuVideoVoList.add(sohuVideoVo);
                this.getRecord(sohuVideoVoList, LoginHelper.getUserId());
            }
            bo.setId(null);
        }
        if (bo.getAiRec()) {
            if (StrUtil.isBlank(bo.getAiRecImei()) && Objects.isNull(LoginHelper.getUserId())) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (!(AliyunAirecConstant.SCENE_VIDEO_ALL.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_VIDEO_HOMEPAGE.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_VIDEO_MY_MAY_LIKE.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_VIDEO_MONEYMAKING.equals(bo.getAiRecSceneId()))) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (Objects.nonNull(LoginHelper.getUserId())) {
                bo.setAiUserId(LoginHelper.getUserId().toString());
            }
            bo.setAiReturnCount(pageQuery.getPageSize());
            //构造过滤的内容
            SohuAirecContentItemVo airecCategoryInfo = categoryService.getAirecCategoryInfoById(bo.getCategoryId());
            airecQueryBo.setCategoryPath(airecCategoryInfo.getCategoryPath());
            airecQueryBo.setType(bo.getType());
            AliyunAirecJoinFilterRule rootRule = airecQueryBo.buildAirecFilterRule();
            this.buildAirecJoinFilterRule(bo.getSiteId(), rootRule);
            //获取阿里云智能推荐结果
            List<SohuVideoVo> resultList = AliyunAirecUtil.aiRecommendSingleType(rootRule, bo, itemIds -> baseMapper.selectVoBatchIds(itemIds));
            if (CollUtil.isNotEmpty(resultList)) {
                getRecord(resultList, LoginHelper.getUserId());
                sohuVideoVoList.addAll(resultList);
                return TableDataInfoUtils.build(sohuVideoVoList);
            }
        }
        //首页不显示关联项  APP-1.0临时需求
//        if (bo.getIsRelate() == null && StringUtils.isBlank(bo.getTitle())) {
//            bo.setIsRelate(false);
//        }
        bo.setIsPay(false);
        bo.setEpisodePay(false);
        bo.setVisibleType(VisibleTypeEnum.open.getCode());
        TableDataInfo<SohuVideoVo> tableDataInfo = this.queryPage(bo, pageQuery);
        tableDataInfo.getData().addAll(sohuVideoVoList);
        AliyunAirecUtil.buildAiRecommendSingleType(tableDataInfo.getData(), AliyunAirecContentItemTypeEnum.VIDEO.getCode());
        return tableDataInfo;
    }

    /**
     * 过滤用户作品内容
     *
     * @param bo SohuArticleBo
     */
    private void filterBanUserContent(SohuVideoBo bo) {
        SohuBanRecordsBo sohuBanRecordsBo = new SohuBanRecordsBo();
        sohuBanRecordsBo.setStatus("active");
        List<SohuBanRecordsVo> sohuBanRecordsVoList = remoteBanRecordService.queryList(sohuBanRecordsBo);
        // 聚合用户id集合
        Set<Long> banUserIds = sohuBanRecordsVoList.stream().map(SohuBanRecordsVo::getUserId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(banUserIds)) {
            bo.setNotUserIds(banUserIds);
        }
    }

    /**
     * 处理站点字段
     */
    private void buildSiteIdRule(SohuVideoBo bo, SohuAirecVideoQueryBo airecQueryBo) {
        if (Objects.isNull(bo.getSiteId())) {
            SohuSiteVo siteVo = siteService.getSiteByIp(ServletUtils.getClientIP());
            if (Objects.nonNull(siteVo)) {
                bo.setSiteId(siteVo.getId());
                if (Objects.equals(siteVo.getType(), SiteType.City.name())) {
                    airecQueryBo.setCity(String.valueOf(siteVo.getId()));
                } else if (Objects.equals(siteVo.getType(), SiteType.Country.name())) {
                    airecQueryBo.setCountry(siteVo.getCountryCode());
                }
            }
        }
        //首页的话 不查站点 只分国内国外
        if (Objects.nonNull(bo.getRecommend()) && bo.getRecommend()) {
            //推荐页就不分站点 只分国家
            if (Objects.nonNull(bo.getSiteId())) {
//                SohuSiteVo sohuSiteVo = siteService.selectSiteByPid(bo.getSiteId());
//                if (Objects.nonNull(sohuSiteVo)) {
//                    bo.setCountrySiteId(sohuSiteVo.getId());
//                    bo.setSiteId(null);
//                    airecQueryBo.setCountry(sohuSiteVo.getCountryCode());
//                }
                SohuSiteVo sohuSiteVo = siteService.getCountrySiteById(bo.getSiteId());
                if (Objects.nonNull(sohuSiteVo)) {
                    bo.setCountrySiteId(sohuSiteVo.getId());
                    bo.setSiteId(null);
                    airecQueryBo.setCountry(sohuSiteVo.getCountryCode());
                    airecQueryBo.setCity(null);
                }
            }
        }
    }

    /**
     * 构建智能推荐过滤参数
     *
     * @param siteId   站点ID
     * @param rootRule
     */
    @Deprecated
    private void buildAirecJoinFilterRule(Long siteId, AliyunAirecJoinFilterRule rootRule) {
        rootRule.setJoin(AliyunAirecQueryJoinEnum.AND.getCode());
        {
            //内容的类型
            AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
            rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
            rule.setField(AliyunAirecQueryFieldContentEnum.ITEM_TYPE.getCode());
            rule.setValue(AliyunAirecContentItemTypeEnum.VIDEO.getCode());
            rootRule.getFilters().add(rule);
        }
        {
            //站点
            if (Objects.nonNull(siteId)) {
                AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
                rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
                rule.setField(AliyunAirecQueryFieldContentEnum.CITY.getCode());
                rule.setValue(siteId.toString());
                rootRule.getFilters().add(rule);
            }
        }
        {
            //TODO 进行判断
            AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
            rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
            rule.setField(AliyunAirecQueryFieldContentEnum.COUNTRY.getCode());
            SohuSiteVo sohuSiteVo = siteService.selectSiteByPid(siteId);
            if (Objects.nonNull(sohuSiteVo)) {
                String code = siteService.selectCountryCodeById(sohuSiteVo.getId());
                if (StrUtil.isNotBlank(code)) {
                    //设置国家编码
                    rule.setValue(code);
                }
            }
            rootRule.getFilters().add(rule);
        }
    }

    /**
     * 查询视频列表
     */
    @Override
    public List<SohuVideoVo> queryList(SohuVideoBo bo) {
        LambdaQueryWrapper<SohuVideo> lqw = buildQueryWrapper(bo);
        lqw.orderByAsc(SohuVideo::getSortIndex);
        lqw.orderByDesc(SohuVideo::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuVideo> buildQueryWrapper(SohuVideoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuVideo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getSiteId() != null, SohuVideo::getSiteId, bo.getSiteId());
        lqw.eq(bo.getCountrySiteId() != null, SohuVideo::getCountrySiteId, bo.getCountrySiteId());
        List<SohuVideoRelate> sohuVideoRelates = sohuVideoRelateMapper.selectList();
        List<Long> ids = sohuVideoRelates.stream().map(SohuVideoRelate::getVideoId).collect(Collectors.toList());
        //狐少少课堂不过滤有关联项的视频
        if (!VideoEnum.Type.lesson.getCode().equals(bo.getType())) {
            if (bo.getIsRelate() != null && !bo.getIsRelate()) {
                if (CollUtil.isNotEmpty(ids)) {
                    lqw.notIn(SohuVideo::getId, ids);
                }
            }
        }
        lqw.eq(bo.getUserId() != null, SohuVideo::getUserId, bo.getUserId());
        // todo 短剧
        // 是否需要付费
        lqw.eq(bo.getIsPay() != null, SohuVideo::getIsPay, bo.getIsPay());
        // 当前集是否需要付费
        lqw.eq(bo.getEpisodePay() != null, SohuVideo::getEpisodePay, bo.getEpisodePay());
        // 是否是短剧 0不是短剧、大于0是短剧
        lqw.ge(SohuVideo::getEpisodeCount, 1);
        // 剧集关联
        lqw.eq(StringUtils.isNotBlank(bo.getEpisodeRelevance()), SohuVideo::getEpisodeRelevance, bo.getEpisodeRelevance());
        //查询剧集的推荐视频
        lqw.ne(SohuVideo::getType, VideoEnum.Type.playlet.getCode());
        lqw.eq(bo.getCategoryId() != null, SohuVideo::getCategoryId, bo.getCategoryId());
        lqw.eq(bo.getLessonLabelId() != null, SohuVideo::getLessonLabelId, bo.getLessonLabelId());
        lqw.like(StrUtil.isNotBlank(bo.getTitle()), SohuVideo::getTitle, bo.getTitle());
        if (StrUtil.isEmpty(bo.getState())) {
            lqw.notIn(SohuVideo::getState, CommonState.Edit.getCode(), CommonState.Delete.getCode(), CommonState.ForceDelete.getCode());
        } else if (StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.OffShelf.getCode())) {
            lqw.in(SohuVideo::getState, CommonState.OffShelf.getCode(), CommonState.CompelOff.getCode());
        } else {
            lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuVideo::getState, bo.getState());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getType()), SohuVideo::getType, bo.getType());
        lqw.in(CollUtil.isNotEmpty(bo.getStateList()), SohuVideo::getState, bo.getStateList());
        lqw.notIn(CollUtil.isNotEmpty(bo.getNotIds()), SohuVideo::getId, bo.getNotIds());
        lqw.notIn(CollUtil.isNotEmpty(bo.getNotCategoryIds()), SohuVideo::getCategoryId, bo.getNotCategoryIds());
        lqw.notIn(CollUtil.isNotEmpty(bo.getNotUserIds()), SohuVideo::getUserId, bo.getNotUserIds());
        lqw.in(CollUtil.isNotEmpty(bo.getIds()), SohuVideo::getId, bo.getIds());
        lqw.in(CollUtil.isNotEmpty(bo.getUserIds()), SohuVideo::getUserId, bo.getUserIds());
        lqw.eq(StrUtil.isNotBlank(bo.getVisibleType()), SohuVideo::getVisibleType, bo.getVisibleType());
        lqw.in(CollUtil.isNotEmpty(bo.getCategoryIds()), SohuVideo::getCategoryId, bo.getCategoryIds());
        lqw.in(CollUtil.isNotEmpty(bo.getSiteIds()), SohuVideo::getSiteId, bo.getSiteIds());
        lqw.eq(StringUtils.isNotBlank(bo.getContentState()), SohuVideo::getState, bo.getContentState());
        lqw.eq(bo.getAppealStatus() != null, SohuVideo::getAppealStatus, bo.getAppealStatus());
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            lqw.ge(StrUtil.isNotBlank(bo.getStartTime()), SohuVideo::getCreateTime, DateUtil.beginOfDay(DateUtil.parseDate(bo.getStartTime())));
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            lqw.le(StrUtil.isNotBlank(bo.getEndTime()), SohuVideo::getCreateTime, DateUtil.endOfDay(DateUtil.parseDate(bo.getEndTime())));
        }
        if (CollUtil.isNotEmpty(bo.getHandleIds())) {
            if (bo.getIsBlack()) {
                lqw.in(SohuVideo::getId, bo.getHandleIds());
            } else {
                lqw.notIn(SohuVideo::getId, bo.getHandleIds());
            }
        }
        return lqw;
    }

    /**
     * 新增视频
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SohuVideoBo bo) {
        SohuVideo add = BeanUtil.toBean(bo, SohuVideo.class);
        String state = StrUtil.isNotBlank(bo.getState()) && StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.Edit.name()) ? CommonState.Edit.name() : CommonState.WaitApprove.name();
        if (!(Objects.equals(CommonState.WaitApprove.name(), state) || Objects.equals(CommonState.Edit.name(), state))) {
            throw new RuntimeException("状态参数异常");
        }
        if (StrUtil.isBlank(bo.getType())) {
            add.setType(VideoEnum.Type.general.getCode());
        }
        if (StrUtil.isBlank(bo.getVisibleType())) {
            add.setVisibleType(VisibleTypeEnum.open.getCode());
        }
        add.setState(state);
        add.setUserId(LoginHelper.getUserId());
        validEntityBeforeSave(add);
        setMCNParams(add, bo);
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
            add.setSubmitTime(new Date());
            add.setSubmitScene("自主发布");
            add.setSubmitNum(Constants.ONE);
            add.setAuditState(CommonState.WaitApprove.name());
        }
        // 宽高比处理
        add.setAspectRatio(remoteFileService.determineAspectRatio(add.getVideoUrl()));
        //处理站点信息
        //如果站点没传 需要做一下兜底
        if (Objects.isNull(bo.getSiteId())) {
            SohuSiteVo siteVo = siteService.getSiteByIp(ServletUtils.getClientIP());
            add.setSiteId(siteVo.getId());
            if (Objects.equals(siteVo.getType(), SiteType.City.name())) {
                Long pid = siteVo.getPid();
                add.setCountrySiteId(pid);
            }
        } else {
            SohuSiteVo sohuSiteVo = siteService.selectSiteByPid(bo.getSiteId());
            if (Objects.nonNull(sohuSiteVo)) {
                add.setCountrySiteId(sohuSiteVo.getId());
            }
        }
        // 短剧名
        String djNo = com.sohu.common.core.utils.NumberUtil.getOrderNo("DJ");
        SohuPlayletBo sohuPlayletBo = null;
        // 保存剧集相关
        if (ObjectUtils.isNotNull(bo.getPlayLetBo()) && StringUtils.isBlank(bo.getEpisodeRelevance()) && StrUtil.isNotEmpty(bo.getPlayLetBo().getTitle())) {
            sohuPlayletBo = BeanUtil.toBean(bo.getPlayLetBo(), SohuPlayletBo.class);
            sohuPlayletBo.setUserId(LoginHelper.getUserId());
            if (StringUtils.isBlank(bo.getEpisodeRelevance())) {
                sohuPlayletBo.setEpisodeRelevance(djNo);
            }
            if (null != bo.getEpisodeCount() && bo.getEpisodeCount() > 0) {
                sohuPlayletBo.setEpisodeCount(bo.getEpisodeCount());
            }
            sohuPlayletBo.setCategoryId(bo.getCategoryId());
            sohuPlayletBo.setIsPay(bo.getIsPay());
            sohuPlayletBo.setSortIndex(1);
            sohuPlayletBo.setState(CommonState.OnShelf.name());
            playLetService.insertByBo(sohuPlayletBo);
            add.setEpisodeRelevance(djNo);
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            SohuVideoInfoBo info = new SohuVideoInfoBo();
            info.setVideoId(add.getId());
            info.setSiteId(bo.getSiteId());
            info.setChildTaskNum(bo.getBusyInfo());
            if (ObjectUtils.isNotNull(sohuPlayletBo) && StringUtils.isNotBlank(sohuPlayletBo.getIntro())) {
                info.setIntro(sohuPlayletBo.getIntro());
            }
            info.setLearnNum(bo.getLearnNum());
            // 保存内容详情
            sohuVideoInfoService.insertByBo(info);
            // 保存内容
            bo.setId(add.getId());
            // 保存关联项
            addVideoRelate(bo);
            bo.setId(add.getId());
            // 保存审核记录
            if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
                this.initCreateAudited(add);
                // 发布异步机审消息
                SohuRiskMqBo riskMqBo = new SohuRiskMqBo();
                riskMqBo.setBusyType(BusyType.Video);
                riskMqBo.setBusyCode(add.getId().toString());
                MqMessaging mqMessaging = new MqMessaging(JSONObject.toJSONString(riskMqBo), MqKeyEnum.HANDLE_RISK_MANAGEMENT.getKey());
                remoteStreamMqService.sendDelayMsg(mqMessaging, 2L);
            }
            sohuSyncContentService.sync(add);
            //新增发布详情（蚁小二）
            this.sohuArticleDetailService.insertDetailByBo(add.getId(), McnHandlerTypeEnum.Video, bo.getDetails());
        }
        return flag;
    }

    /**
     * 保存关联项
     *
     * @param bo
     */
    public void addVideoRelate(SohuVideoBo bo) {
        //删除关联项
        sohuVideoRelateService.deleteByVideoIds(Arrays.asList(bo.getId()));
        if (((StrUtil.equalsAnyIgnoreCase(bo.getRelateType(), BusyType.Window.name()) || bo.getBusyCode() != null || StrUtil.isNotBlank(bo.getBusyTitle())) || StrUtil.isNotBlank(bo.getBusyInfo())) || StrUtil.isNotBlank(bo.getRelateType())) {
            SohuVideoRelateBo videoRelateBo = new SohuVideoRelateBo();
            videoRelateBo.setVideoId(bo.getId());
            videoRelateBo.setBusyType(bo.getRelateType());
            videoRelateBo.setBusyInfo(bo.getBusyInfo());
            Long busyCode = bo.getBusyCode();
            String busyTitle = bo.getBusyTitle();
            if (StrUtil.equalsAnyIgnoreCase(bo.getRelateType(), BusyType.Window.name())) {
                LoginUser loginUser = LoginHelper.getLoginUser();
                if (loginUser != null) {
                    busyTitle = loginUser.getNickname();
                    busyCode = loginUser.getUserId();
                }
            } else if (StrUtil.equalsAnyIgnoreCase(bo.getRelateType(), BusyType.GoodsWindow.name())) {
                LoginUser loginUser = LoginHelper.getLoginUser();
                if (loginUser != null) {
                    busyCode = loginUser.getUserId();
                }
            } else if (StrUtil.equalsAnyIgnoreCase(bo.getRelateType(), BusyType.BusyTask.name())) {
                LoginUser loginUser = LoginHelper.getLoginUser();
                if (loginUser != null) {
                    busyCode = loginUser.getUserId();
                }
            }
            videoRelateBo.setBusyCode(busyCode);
            videoRelateBo.setBusyTitle(busyTitle);
            sohuVideoRelateService.insertByBo(videoRelateBo);
        }
    }

    /**
     * 设置MCN参数（蚁小二）
     *
     * @param entity
     * @param bo
     */
    private void setMCNParams(SohuVideo entity, SohuVideoBo bo) {
        if (CollectionUtils.isNotEmpty(bo.getDetails())) {
            //同步账号数（蚁小二）
            entity.setPlatformNum(bo.getDetails().size());
        } else {
            entity.setPlatformNum(0);
            entity.setPublishStatus(McnPublishMainStatusEnum.NoThirdParty.getCode());
        }
        infomationByLoginId(entity);
    }

    /**
     * 通过登录id赋予mcn信息
     *
     * @param entity
     */
    private void infomationByLoginId(SohuVideo entity) {
        LambdaQueryWrapper<SohuMcnUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuMcnUser::getUserId, LoginHelper.getUserId()).in(SohuMcnUser::getState, McnUserStateEnum.NORMAL.getCode(), McnUserStateEnum.DISABLED.getCode());
        SohuMcnUserVo sohuMcnUserVo = sohuMcnUserMapper.selectVoOne(wrapper);
        if (Objects.nonNull(sohuMcnUserVo)) {
            entity.setMcnUserId(sohuMcnUserVo.getMcnUserId());
        }
    }

    /**
     * 修改视频
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(SohuVideoBo bo) {
        SohuVideoVo videoVo = baseMapper.selectVoById(bo.getId());
        validateArticleExists(videoVo);
        SohuVideo update = BeanUtil.toBean(bo, SohuVideo.class);
//        String state = StrUtil.isNotBlank(bo.getState()) && StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.Edit.name()) ? CommonState.Edit.name() : CommonState.WaitApprove.name();
        String state = StrUtil.isBlankIfStr(bo.getState())
                ? CommonState.WaitApprove.name()
                : (StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.Edit.name())
                ? CommonState.Edit.name()
                : bo.getState());

        if (!(Objects.equals(CommonState.Refuse.name(), videoVo.getState())
                || Objects.equals(CommonState.Edit.name(), videoVo.getState())
                || Objects.equals(CommonState.CompelOff.name(), videoVo.getState())
                || Objects.equals(CommonState.OffShelf.name(), videoVo.getState()))) {
            throw new RuntimeException("此状态不支持修改");
        }
        //String state = bo.getState();
        bo.setState(state);
        update.setState(state);
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
            if (videoVo.getSubmitNum().compareTo(SUBMIT_NUM_MAX_VALUE) > -1) {
                throw new RuntimeException("您已提交" + videoVo.getSubmitNum() + "次，最大提交次数为" + SUBMIT_NUM_MAX_VALUE);
            }
            fieldChangeChecker(bo, videoVo);
            update.setSubmitTime(new Date());
            if (videoVo.getSubmitNum() > 0) {
                update.setSubmitScene("下架整改");
            } else {
                update.setSubmitScene("自主发布");
            }
            update.setSubmitNum(videoVo.getSubmitNum() + 1);
            update.setAppealStatus(false);
            update.setAuditState(CommonState.WaitApprove.getCode());
        } else if (StrUtil.equalsAnyIgnoreCase(state, CommonState.Edit.name())) {
            update.setAuditState(null);
        }
        update.setUserId(videoVo.getUserId());
        validEntityBeforeSave(update);
        // 保存关联项
        addVideoRelate(bo);
        // TODO 关联话题
        // 保存审核记录
//        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
//            this.initCreateAudited(update);
//        }
        sohuSyncContentService.sync(update);
        //return baseMapper.updateById(update) > 0;
        LambdaUpdateWrapper<SohuVideo> updateWrapper = new LambdaUpdateWrapper<>();
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name()) || StrUtil.equalsAnyIgnoreCase(state, CommonState.Edit.name())) {
            updateWrapper.set(SohuVideo::getCoverImage, update.getCoverImage()).eq(SohuVideo::getId, update.getId());
            updateWrapper.set(SohuVideo::getAppealReason, null);
            updateWrapper.set(SohuVideo::getRejectReason, null);
            updateWrapper.set(SohuVideo::getAuditState, update.getAuditState());
            update.setAppealReason(null);
            update.setRejectReason(null);
            baseMapper.updateByIdThenEviction(update, updateWrapper);
        } else {
//            update.setState(null);
//            update.setAuditState(null);
            baseMapper.updateById(update);
        }

        SohuVideoInfo sohuVideoInfo = new SohuVideoInfo();
        sohuVideoInfo.setVideoId(bo.getId());
        sohuVideoInfo.setLearnNum(bo.getLearnNum());
        //修改初始学习人数
        LambdaUpdateWrapper<SohuVideoInfo> updateInfoWrapper = new LambdaUpdateWrapper<>();
        updateInfoWrapper.set(SohuVideoInfo::getLearnNum, sohuVideoInfo.getLearnNum()).eq(SohuVideoInfo::getVideoId, sohuVideoInfo.getVideoId());
        sohuVideoInfoMapper.update(new SohuVideoInfo(), updateInfoWrapper);
        SohuContentLifecycleBo lifecycleBo = new SohuContentLifecycleBo(videoVo.getId(), BusyType.Video.name(), videoVo.getState(), update.getState(), "提交审核");
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
            this.initCreateAudited(update);
            // 发布异步机审消息
            SohuRiskMqBo riskMqBo = new SohuRiskMqBo();
            riskMqBo.setBusyType(BusyType.Video);
            riskMqBo.setBusyCode(update.getId().toString());
            MqMessaging mqMessaging = new MqMessaging(JSONObject.toJSONString(riskMqBo), MqKeyEnum.HANDLE_RISK_MANAGEMENT.getKey());
            remoteStreamMqService.sendDelayMsg(mqMessaging, 2L);
        } else {
            lifecycleBo.setRemark("编辑");
        }
        this.createLifecycleLog(lifecycleBo);
        return true;
    }

    private void fieldChangeChecker(SohuVideoBo bo, SohuVideoVo entityVo) {
        if (StrUtil.equalsAnyIgnoreCase(entityVo.getState(), CommonState.Edit.name())) {
            return;
        }
        SohuContentLifecycleVo lifecycleVo = this.sohuContentLifecycleService.selectOfLast(entityVo.getId(), BusyType.Video.name());
        if (Objects.nonNull(lifecycleVo) && Objects.equals(entityVo.getState(), lifecycleVo.getLastState()) && Objects.equals(entityVo.getState(), lifecycleVo.getCurrentState())) {
            return;
        }
        SohuVideoBo oldValue = BeanUtil.copyProperties(entityVo, SohuVideoBo.class);
        if (!SohuFieldChangeUtil.hasFieldChanged(oldValue, bo)) {
            throw new RuntimeException("请修改后再提交");
        }
    }

    @Override
    public List<SohuVideoVo> listByEp(String episodeRelevance, BaseCommonBo commonBo) {
        LambdaQueryWrapper<SohuVideo> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuVideo::getEpisodeRelevance, episodeRelevance);
        lqw.eq(SohuVideo::getState, CommonState.OnShelf.getCode());
        lqw.orderByAsc(SohuVideo::getEpisodeNumber);
        SohuPlayletVo playletInfo = playLetService.getPlayletInfo(episodeRelevance, 1);
        List<SohuVideoVo> sohuVideoVos = this.baseMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(sohuVideoVos)) {
            return null;
        }
        Long userId = LoginHelper.getUserId();
        if (userId == null) {
            userId = 0L;
        }
        log.info("listByEp:episodeRelevance:{},commonBo:{}", episodeRelevance, JSONUtil.toJsonStr(commonBo));
        List<String> videoIds = new ArrayList<>();
        List<Long> videoIdList = new ArrayList<>();
        for (SohuVideoVo videoVo : sohuVideoVos) {
            if (StrUtil.isBlankIfStr(videoVo.getVideoUrl())) {
                continue;
            }
            videoVo.setVideoKey(SecretUtil.generateUkStr(userId, SecretUtil.KEY_LENGTH));
            videoVo.setVideoUrl(SecretUtil.encrypt(StrUtil.trim(videoVo.getVideoUrl()), videoVo.getVideoKey()));
            videoVo.setPlayetId(playletInfo.getId());
            videoIds.add(String.valueOf(videoVo.getId()));
            videoIdList.add(videoVo.getId());
        }
        // 是否登录
        boolean login = userId > 0L;
//        if (login) {
        getRecord(sohuVideoVos, userId);
//        }
        String uuid = commonBo.getUuid();
        SohuTradeRecordBo tradeRecordBo = SohuTradeRecordBo.builder().userId(userId)
                .type(SohuTradeRecordEnum.Type.Playlet.getCode())
                .consumeType(SohuTradeRecordEnum.Type.Playlet.getCode())
                .consumeCode(String.valueOf(playletInfo.getId()))
                .payStatus(PayStatus.Paid.name())
                .build();
        SohuTradeRecordVo playletPayTrade = null;
        if (!login && StrUtil.isNotBlank(uuid)) {
            tradeRecordBo.setUuid(uuid);
        }
        playletPayTrade = sohuTradeRecordService.queryOne(tradeRecordBo);
        boolean allPay = Objects.nonNull(playletPayTrade);
        Map<Long, SohuVideoInfoVo> infoVoMap = sohuVideoInfoService.selectMap(videoIdList);
        log.info("listByEp response allPay:{}", allPay);
        if (allPay) {
            sohuVideoVos.forEach(sohuVideoVo -> {
                sohuVideoVo.setPaid(true);
                sohuVideoVo.setIsPay(false);
                sohuVideoVo.setPlayletTitle(playletInfo.getTitle());
                sohuVideoVo.setEpisodeCount(playletInfo.getEpisodeCount());
                SohuVideoInfoVo sohuVideoInfo = infoVoMap.get(sohuVideoVo.getId());
                if (ObjectUtil.isNotNull(sohuVideoInfo)) {
                    if (StringUtils.isNotBlank(sohuVideoInfo.getIntro())) {
                        sohuVideoVo.setIntro(sohuVideoInfo.getIntro());
                    }
                }
            });
            log.info("listByEp response sohuVideoVos :{}", sohuVideoVos);
            return sohuVideoVos;
        }
        Map<String, SohuTradeRecordVo> videoPayMap = login ? sohuTradeRecordService.queryMap(userId, SohuTradeRecordEnum.Type.Video.getCode(),
                SohuTradeRecordEnum.Type.Video.getCode(), videoIds, PayStatus.Paid.name()) : sohuTradeRecordService.queryMap(uuid, SohuTradeRecordEnum.Type.Video.getCode(),
                SohuTradeRecordEnum.Type.Video.getCode(), videoIds, PayStatus.Paid.name());
        sohuVideoVos.forEach(sohuVideoVo -> {
            if (!sohuVideoVo.getEpisodePay()) {
                sohuVideoVo.setPaid(true);
                sohuVideoVo.setIsPay(false);
            } else {
                sohuVideoVo.setPaid(Objects.nonNull(videoPayMap.get(String.valueOf(sohuVideoVo.getId()))));
                sohuVideoVo.setIsPay(!Objects.nonNull(videoPayMap.get(String.valueOf(sohuVideoVo.getId()))));
            }

            sohuVideoVo.setPlayletTitle(playletInfo.getTitle());
            sohuVideoVo.setEpisodeCount(playletInfo.getEpisodeCount());
            SohuVideoInfoVo sohuVideoInfo = infoVoMap.get(sohuVideoVo.getId());
            if (ObjectUtil.isNotNull(sohuVideoInfo)) {
                if (StringUtils.isNotBlank(sohuVideoInfo.getIntro())) {
                    sohuVideoVo.setIntro(sohuVideoInfo.getIntro());
                }
            }
        });
        return sohuVideoVos;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuVideo entity) {
        // TODO 做一些数据校验,如唯一约束
        entity.setUpdateTime(new Date());
        if (entity.getId() == null || entity.getId() < 1L) {
            entity.setCreateTime(new Date());
        }
        //校验当前操作id是否是作者
        if (!entity.getUserId().equals(LoginHelper.getUserId())) {
            throw new ServiceException(MessageUtils.message("no.power"));
        }
        infomationByLoginId(entity);
    }

    /**
     * 批量删除视频
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        // TODO 做一些业务上的校验,判断是否需要校验
//        if (isValid) {
//        }
        for (Long id : ids) {
            // 删除万能表数据
            sohuContentMainService.deleteByObj(id, BusyType.Video.name());
            // 删除审核数据
            sohuAuditService.deleteByObj(id, BusyType.Video.name());
            iSohuAirecContentItemService.updateStatusToOffShelf(id.toString(), AliyunAirecContentItemTypeEnum.VIDEO.getCode());
        }
        // 删除关联项数据
        sohuVideoRelateService.deleteByVideoIds(ids);
        // 删除视频拓展数据
        sohuVideoInfoService.deleteVideoIds(ids);
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 视频草稿提交至审核
     *
     * @param videoId 视频ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void commit(Long videoId) {
        SohuVideo sohuVideo = baseMapper.selectById(videoId);
        validateArticleExists(sohuVideo);
        sohuVideo.setState(CommonState.WaitApprove.name());
        this.baseMapper.updateById(sohuVideo);
        // 同步视频至万能表
        sohuSyncContentService.sync(sohuVideo);
        // 同步至审核
        this.initCreateAudited(sohuVideo);
    }

    @Override
    public Boolean submitAudit(Long id) {
        SohuVideo sohuVideo = baseMapper.selectById(id);
        validateArticleExists(sohuVideo);
        SohuVideoBo bo = BeanUtil.copyProperties(sohuVideo, SohuVideoBo.class);
        bo.setState(CommonState.WaitApprove.name());
        SohuVideoInfoVo infoVo = sohuVideoInfoService.queryByVideoId(id);
        if (Objects.nonNull(infoVo)) {
            bo.setLearnNum(infoVo.getLearnNum());
        }
        return this.updateByBo(bo);
    }

    private void validateArticleExists(SohuVideoVo articleVo) {
        if (Objects.isNull(articleVo)) {
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
    }

    private void validateArticleExists(SohuVideo articleVo) {
        if (Objects.isNull(articleVo)) {
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
    }

    /**
     * 视频同步至审核
     */
    private void initCreateAudited(SohuVideo sohuVideo) {
        SohuVideoBo bo = BeanUtil.toBean(sohuVideo, SohuVideoBo.class);
        sohuAuditInitService.initCreateAudited(bo);
    }

    /**
     * 阿里云智能推荐视频物料
     *
     * @param sohuVideo
     */
    @Override
    public void updateSohuAirecContentVideoItem(SohuVideo sohuVideo) {
//        if (sohuVideo.getIsPay() || (!Objects.equals(VisibleTypeEnum.open.getCode(), sohuVideo.getVisibleType()))) {
//            return;
//        }
        //上架
        if (StringUtils.equalsIgnoreCase(CommonState.OnShelf.getCode(), sohuVideo.getState())) {
            // 关联项
            SohuVideoRelate relate = sohuVideoRelateMapper.selectOne(SohuVideoRelate::getVideoId, sohuVideo.getId());
            //创建model
            SohuAirecContentItemBo model = buildAirecContentItemModel(sohuVideo, Objects.nonNull(relate));
            model.setTags(iSohuAirecTagRelationService.saveTagStr(sohuVideo.getId(), AiRecTag.BizTypeEnum.VIDEO.getCode(), model.getTags()));
            iSohuAirecContentItemService.saveAirecContentItem(model);
        } else {
            //下架
            iSohuAirecContentItemService.updateStatusToOffShelf(sohuVideo.getId().toString(), AliyunAirecContentItemTypeEnum.VIDEO.getCode());

        }
    }

    @Override
    public List<SohuPlayetEpisodeListVo> listEpisodes(PlayletAdsQueryBo bo) {
        if (bo.getType().equals(AdsType.PATCHES.getCode())) {
            bo.setType(AdsRelateType.Video.getCode());
        }
        LambdaQueryWrapper<SohuVideo> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuVideo::getEpisodeRelevance, bo.getEpisodeRelevance());
        if (bo.getEpisodeNumber() != null) {
            lqw.eq(SohuVideo::getEpisodeNumber, bo.getEpisodeNumber());
        }
        lqw.eq(SohuVideo::getState, CommonState.OnShelf.getCode());
        lqw.orderByAsc(SohuVideo::getEpisodeNumber);
        List<SohuVideoVo> sohuVideos = baseMapper.selectVoList(lqw);

        if (CollUtil.isEmpty(sohuVideos)) {
            return null;
        }
        List<SohuPlayetEpisodeListVo> data = new LinkedList<>();
        for (SohuVideoVo videoVo : sohuVideos) {
            SohuPlayetEpisodeListVo vo = new SohuPlayetEpisodeListVo();
            Long videoVoId = videoVo.getId();
            vo.setVideoId(videoVoId);
            vo.setEpisodeNumber(videoVo.getEpisodeNumber());
            Long count = playLetService.selectAdsCount(bo.getType(), videoVoId);
            if (count > 0L) {
                vo.setIsExistAds(true);
            } else {
                vo.setIsExistAds(false);
            }
            data.add(vo);
        }
        return data;
    }

    /**
     * 创建智能推荐物料数据
     *
     * @param sohuVideo
     * @param hasRelate 存在关联
     * @return
     */
    public SohuAirecContentItemBo buildAirecContentItemModel(SohuVideo sohuVideo, Boolean hasRelate) {
        SohuAirecContentItemBo model = new SohuAirecContentItemBo();
        model.setItemId(String.valueOf(sohuVideo.getId()));
        model.setItemType(AliyunAirecContentItemTypeEnum.VIDEO.getCode());
//        String sceneId = String.join(AliyunAirecConstant.CONNECTED_COMMA, AliyunAirecConstant.SCENE_VIDEO_HOMEPAGE, AliyunAirecConstant.SCENE_VIDEO_MY_MAY_LIKE);
//        model.setSceneId(sceneId);
        model.setDuration(Constants.DEFAULT_VIDEO_TIME);
        Date createTime = sohuVideo.getCreateTime();
        long time = DateUtils.getTimeOfSecond(createTime);
        model.setPubTime(String.valueOf(time));
        model.setTitle(sohuVideo.getTitle());
        model.setWeight(AliyunAirecItemWeightEnum.NOT_WEIGHT.getCode());
        model.setAuthor(sohuVideo.getUserId().toString());
        model.setContent(StringUtils.isEmpty(sohuVideo.getContent()) ? sohuVideo.getTitle() : StringUtils.substring(sohuVideo.getContent(), AliyunAirecConstant.CONTENT_SUBSTRING));
        model.setCity(String.valueOf(sohuVideo.getSiteId()));
//        model.setStatus(AirecItemStatusEnum.CONTENT_YES.getCode());
        if (StringUtils.equalsAnyIgnoreCase(CommonState.OnShelf.getCode(), sohuVideo.getState())
                && Objects.equals(VisibleTypeEnum.open.getCode(), sohuVideo.getVisibleType()) && !sohuVideo.getIsPay()) {
            model.setStatus(AirecItemStatusEnum.CONTENT_YES.getCode());
        } else {
            model.setStatus(AirecItemStatusEnum.CONTENT_NO.getCode());
        }
        SohuAirecContentItemVo airecCategoryInfo = categoryService.getAirecCategoryInfoById(sohuVideo.getCategoryId());
        model.setCategoryPath(airecCategoryInfo.getCategoryPath());
        model.setCategoryLevel(airecCategoryInfo.getCategoryLevel());
        String code = siteService.selectCountryCodeById(sohuVideo.getCountrySiteId());
        if (StrUtil.isNotBlank(code)) {
            model.setCountry(code);
        }
        //贴标签
        this.buildTagsAndScene(sohuVideo, hasRelate, model);
        return model;
    }

    /**
     * 贴标签和场景
     *
     * @param sohuVideo
     * @param hasRelate 存在关联
     * @param model
     */
    private void buildTagsAndScene(SohuVideo sohuVideo, Boolean hasRelate, SohuAirecContentItemBo model) {
        //标签
        Set<String> tagSet = new HashSet<>();
        //场景
        Set<String> sceneIdSet = new HashSet<>();
        //作品类型标签
        if (Objects.equals(VideoEnum.Type.general.getCode(), sohuVideo.getType())) {
            tagSet.add(AliyunAirecTagsConstant.MEDIA_TYPE_GENERAL);
            sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_HOMEPAGE);
            sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_MY_MAY_LIKE);
        } else if (Objects.equals(VideoEnum.Type.lesson.getCode(), sohuVideo.getType())) {
            tagSet.add(AliyunAirecTagsConstant.MEDIA_TYPE_LESSON);
            sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_HOMEPAGE);
            sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_MY_MAY_LIKE);
        } else if (Objects.equals(VideoEnum.Type.playlet.getCode(), sohuVideo.getType())) {
            tagSet.add(AliyunAirecTagsConstant.MEDIA_TYPE_PLAYLET);
            sceneIdSet.add(AliyunAirecConstant.SCENE_SHORT_VIDEO_LIFE);
        } else if (Objects.equals(VideoEnum.Type.recommendPlaylet.getCode(), sohuVideo.getType())) {
            tagSet.add(AliyunAirecTagsConstant.MEDIA_TYPE_PLAYLET);
            sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_HOMEPAGE);
            sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_MY_MAY_LIKE);
        }
        sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_ALL);
        //场景和物料类型
        //如果不是剧集&&存在关联
        if (StrUtil.isBlank(sohuVideo.getEpisodeRelevance()) && hasRelate) {
            if (hasRelate) {
                sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_MONEYMAKING);
            }
        }
        //关联标签
        if (hasRelate) {
            tagSet.add(AliyunAirecTagsConstant.MEDIA_HAS_RELATE);
        } else {
            sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_HOMEPAGE);
        }
        model.setTags(String.join(AliyunAirecConstant.CONNECTED_COMMA, tagSet));
        model.setSceneId(String.join(AliyunAirecConstant.CONNECTED_COMMA, sceneIdSet));
    }

//    /**
//     * 获取视频推荐
//     *
//     * @param sohuVideoBo
//     * @return TableDataInfo
//     */
//    @Override
//    public TableDataInfo<SohuVideoVo> getSohuAirecContentVideo(SohuVideoBo sohuVideoBo) {
//        RecommendRequest request = new RecommendRequest();
//        request.setSceneId(AliyunAirecConstant.SCENE_VIDEO_HOMEPAGE);
//        request.setUserId(sohuVideoBo.getCurrentUserId() == null ? null : String.valueOf(sohuVideoBo.getCurrentUserId()));
//        request.setReturnCount(AliyunAirecConstant.AIREC_MAX);
//        if (StrUtil.isNotBlank(sohuVideoBo.getImei())) {
//            request.setImei(DigestUtil.md5Hex(sohuVideoBo.getImei()));
//        }
//
//        //构造过滤的内容
//        AliyunAirecJoinFilterRule rootRule = new AliyunAirecJoinFilterRule();
//        rootRule.setJoin(AliyunAirecQueryJoinEnum.AND.getCode());
//
//        {
//            AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
//            rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
//            rule.setField(AliyunAirecQueryFieldContentEnum.ITEM_TYPE.getCode());
//            rule.setValue(AliyunAirecContentItemTypeEnum.VIDEO.getCode());
//            rootRule.getFilters().add(rule);
//        }
//
//        //调用获取推荐的工具方法
//        AliyunAirecRecommendResponse recommend = AliyunAirecUtil.aliyunAirecRecommendResponse(request, rootRule);
//        Map<String, List<String>> idGroup = recommend.getIds();
//        List<String> ids = idGroup.get(AliyunAirecContentItemTypeEnum.VIDEO.getCode());
//        List<SohuVideoVo> sohuVideoVos = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(ids)) {
//            sohuVideoVos = baseMapper.selectVoBatchIds(ids);
//            //存在极端返回时视频被删的情况
//            if (CollectionUtils.isNotEmpty(sohuVideoVos)) {
//                //获取sohuArticleVos中对应的智能推荐返回的ResultItem
//                sohuVideoVos.stream().forEach(vos -> {
//                    RecommendResponse.ResultItem resultItem = recommend.getResult().stream().filter(r -> StringUtils.equalsIgnoreCase(String.valueOf(vos.getId()), r.getItemId())).findFirst().orElse(null);
//                    vos.setResultItem(resultItem);
//                });
//            }
//        }
//        return TableDataInfoUtils.build(sohuVideoVos);
//    }

//    /**
//     * 获取短剧推荐
//     *
//     * @param sohuPlayletVideoBo
//     * @return TableDataInfo
//     */
//    @Override
//    public TableDataInfo<SohuVideoVo> getSohuAirecContentShortVideo(SohuPlayletVideoBo sohuPlayletVideoBo, Long userId) {
//        RecommendRequest request = new RecommendRequest();
//        request.setSceneId(AliyunAirecConstant.SCENE_SHORT_VIDEO_LIFE);
//        request.setUserId(userId == null ? null : String.valueOf(userId));
//        request.setReturnCount(AliyunAirecConstant.AIREC_MAX);
//        if (StrUtil.isNotBlank(sohuPlayletVideoBo.getImei())) {
//            request.setImei(DigestUtil.md5Hex(sohuPlayletVideoBo.getImei()));
//        }
//
//        //构造过滤的内容
//        AliyunAirecJoinFilterRule rootRule = new AliyunAirecJoinFilterRule();
//        rootRule.setJoin(AliyunAirecQueryJoinEnum.AND.getCode());
//
//        {
//            AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
//            rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
//            rule.setField(AliyunAirecQueryFieldContentEnum.ITEM_TYPE.getCode());
//            rule.setValue(AliyunAirecContentItemTypeEnum.SHORTVIDEO.getCode());
//            rootRule.getFilters().add(rule);
//        }
//
//        //调用获取推荐的工具方法
//        AliyunAirecRecommendResponse recommend = AliyunAirecUtil.aliyunAirecRecommendResponse(request, rootRule);
//        Map<String, List<String>> idGroup = recommend.getIds();
//        List<String> ids = idGroup.get(AliyunAirecContentItemTypeEnum.SHORTVIDEO.getCode());
//        List<SohuVideoVo> sohuVideoVos = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(ids)) {
//            sohuVideoVos = baseMapper.selectVoBatchIds(ids);
//            //存在极端返回时视频被删的情况
//            if (CollectionUtils.isNotEmpty(sohuVideoVos)) {
//                //获取sohuArticleVos中对应的智能推荐返回的ResultItem
//                sohuVideoVos.stream().forEach(vos -> {
//                    RecommendResponse.ResultItem resultItem = recommend.getResult().stream().filter(r -> StringUtils.equalsIgnoreCase(String.valueOf(vos.getId()), r.getItemId())).findFirst().orElse(null);
//                    vos.setResultItem(resultItem);
//                });
//            }
//        }
//        return TableDataInfoUtils.build(sohuVideoVos);
//    }

    /**
     * 返回每个标签下前五的视频
     * 双层数组结构返回
     */
    @Override
    public List<SohuTopVideoVo> labelTopFive() {
        SohuLessonLabelBo sohuLessonLabelBo = new SohuLessonLabelBo();
        sohuLessonLabelBo.setType(BusyType.Video.name());
        List<SohuLessonLabelVo> sohuLessonLabelVos = sohuLessonLabelService.queryList(sohuLessonLabelBo);
        if (CollUtil.isEmpty(sohuLessonLabelVos)) {
            return null;
        }
        List<SohuTopVideoVo> data = new ArrayList<>();
        for (SohuLessonLabelVo label : sohuLessonLabelVos) {
            SohuTopVideoVo vo = new SohuTopVideoVo();
            List<SohuVideoVo> sohuVideoVos = hotList(label.getId());
            if (CollUtil.isNotEmpty(sohuVideoVos)) {
                vo.setLessonLabelId(label.getId());
                vo.setLabelName(label.getName());
                vo.setList(sohuVideoVos);
                data.add(vo);
            }
        }
        return data;
    }

    @Override
    public TableDataInfo<SohuVideoVo> videoPageCenterByType(SohuVideoBo bo, PageQuery pageQuery) {
        Long loginUserId = LoginHelper.getUserId();
        SohuVideoBo dto = new SohuVideoBo();
        dto.setUserId(loginUserId);
        List<String> states = new ArrayList<>();
        states.add(BusyOrderStatus.OnShelf.name());
        dto.setStateList(states);
        dto.setType(bo.getType());
        if (StrUtil.isNotBlank(bo.getTitle())) {
            dto.setTitle(bo.getTitle());
        }
        if (dto.getUserId() == null) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        return queryPage(dto, pageQuery);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean draftRetry(SohuBusyBO busyBO) {
        SohuVideo video = baseMapper.selectById(busyBO.getBusyCode());
        if (Objects.isNull(video)) {
            return Boolean.FALSE;
        }
        video.setState(CommonState.WaitApprove.getCode());
        baseMapper.updateById(video);
        this.initCreateAudited(video);
        SohuContentMain contentMain = sohuContentMainService.getEntityByObj(busyBO.getBusyCode(), BusyType.Video.name());
        if (Objects.nonNull(contentMain)) {
            contentMain.setState(CommonState.WaitApprove.getCode());
            sohuContentMainService.updateById(contentMain);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean forward(SohuBusyBO bo) {
        SohuVideoInfoVo infoVo = sohuVideoInfoService.queryByVideoId(bo.getBusyCode());
        if (Objects.isNull(infoVo) || infoVo.getId() == null) {
            return Boolean.FALSE;
        }
        int count = infoVo.getForwardCount() + 1;
        SohuVideoInfoBo infoBo = new SohuVideoInfoBo();
        infoBo.setId(infoVo.getId());
        infoBo.setForwardCount(count);
        sohuVideoInfoService.updateByBo(infoBo);
        SohuContentMainBo mainBo = new SohuContentMainBo();
        mainBo.setObjId(bo.getBusyCode());
        mainBo.setObjType(BusyType.Video.name());
        mainBo.setForwardCount(count);
        sohuContentMainService.updateByBo(mainBo);
        return Boolean.TRUE;
    }

    @Override
    public Boolean comment(SohuCommentBo bo, Boolean commentCountAdd) {
        SohuVideoInfo info = sohuVideoInfoService.queryEntityByVideoId(bo.getBusyCode());
        if (Objects.isNull(info) || info.getId() == null) {
            return false;
        }
        SohuVideoVo videoVo = this.baseMapper.selectVoById(bo.getBusyCode());
        if (BooleanUtil.isTrue(commentCountAdd)) {
            info.setCommentCount(info.getCommentCount() + 1);
        }
        sohuVideoInfoService.updateById(info);
        SohuContentMain sohuContentMain = sohuContentMainService.getEntityByObj(bo.getBusyCode(), BusyType.Video.name());
        if (Objects.nonNull(sohuContentMain)) {
            sohuContentMain.setCommentCount(info.getCommentCount());
            sohuContentMainService.updateById(sohuContentMain);
        }
        bo.setTopType(BusyType.Video.name());
        bo.setTopCode(info.getVideoId());
        bo.setBusyCoverImage(videoVo.getCoverImage());
        bo.setBusyType(BusyType.Video.name());
        bo.setBusyUser(videoVo.getUserId());

        if (bo.getTopReplyUser() != null && bo.getTopReplyUser() > 0L) {
            SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildComment(bo, bo.getReplyUser(), NoticeInteractEnum.commentReceive.name());
            sohuInteractNoticeService.insertByBo(interactNoticeReceive);
        } else {
            if (!Objects.equals(bo.getReplyUser(), bo.getOperateUser())) {
                // 判断是不是自己回复自己的评论
                SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildComment(bo, bo.getReplyUser(), NoticeInteractEnum.commentReceive.name());
                sohuInteractNoticeService.insertByBo(interactNoticeReceive);
            }

            // 操作者收到
            SohuInteractNoticeBo interactNoticeSend = SohuInteractNoticeBo.buildComment(bo, bo.getOperateUser(), NoticeInteractEnum.commentSend.name());
            sohuInteractNoticeService.insertByBo(interactNoticeSend);
        }

        // 清除视频缓存
        sohuVideoInfoService.evictByVideoId(bo.getBusyCode());
        return Boolean.TRUE;
    }

    @Override
    public Boolean like(SohuBusyBO bo) {
        SohuVideoInfo info = sohuVideoInfoService.queryEntityByVideoId(bo.getBusyCode());
        if (Objects.isNull(info) || info.getId() == null) {
            return Boolean.FALSE;
        }
        log.info("点赞视频：{}", JSONUtil.toJsonStr(bo));
        Integer oldCount = info.getPraiseCount();
        int count = bo.getIsAdd() != null && bo.getIsAdd() ? oldCount + 1 : Math.max((oldCount - 1), 0);
        info.setPraiseCount(count);
        sohuVideoInfoService.updateById(info);
        // 更新万能表的点赞数量
        sohuContentMainMapper.setPraiseCount(bo.getBusyCode(), BusyType.Video.name(), count);

        bo.setTopCode(info.getVideoId());
        bo.setTopType(BusyType.Video.name());
        if (bo.getIsAdd() != null && bo.getIsAdd()) {
            SohuVideoVo videoVo = baseMapper.selectVoById(bo.getBusyCode());

            bo.setSourceUser(videoVo.getUserId());
            bo.setOperateUser(bo.getOperateUser());
            bo.setBusyCoverImage(videoVo.getCoverImage());
            bo.setBusyUser(videoVo.getUserId());
            SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildBusy(bo, videoVo.getUserId(), NoticeInteractEnum.like.name());
            interactNoticeReceive.setContent(String.format(NoticeInteractEnum.likeContent, "视频"));
            sohuInteractNoticeService.insertByBo(interactNoticeReceive);
            // 点赞埋点
            buildVideoEventRecord(RecreationReportEnum.SPDZ, bo.getOperateUser());
        }
        // 清除视频缓存
        sohuVideoInfoService.evictByVideoId(bo.getBusyCode());

        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean collect(SohuBusyBO bo) {
        SohuVideoInfo info = sohuVideoInfoService.queryEntityByVideoId(bo.getBusyCode());
        if (Objects.isNull(info) || info.getId() == null) {
            return false;
        }
        log.info("收藏视频：{}", JSONUtil.toJsonStr(bo));
        Integer oldCount = info.getCollectCount();
        int count = bo.getIsAdd() ? oldCount + 1 : Math.max((oldCount - 1), 0);
        info.setCollectCount(count);
        boolean result = sohuVideoInfoService.updateById(info);
        // 更新万能表的收藏数量
        sohuContentMainMapper.setCollectCount(bo.getBusyCode(), BusyType.Video.name(), count);
        if (bo.getIsAdd() != null && bo.getIsAdd()) {
            SohuVideoVo videoVo = baseMapper.selectVoById(bo.getBusyCode());

            bo.setSourceUser(videoVo.getUserId());
            bo.setOperateUser(bo.getOperateUser());
            bo.setBusyCoverImage(videoVo.getCoverImage());
            bo.setBusyUser(videoVo.getUserId());
            SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildBusy(bo, videoVo.getUserId(), NoticeInteractEnum.collect.name());
            interactNoticeReceive.setContent(String.format(NoticeInteractEnum.collectContent, "视频"));
            sohuInteractNoticeService.insertByBo(interactNoticeReceive);
        }
        // 清除视频缓存
        sohuVideoInfoService.evictByVideoId(bo.getBusyCode());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateUserVideoState(Long userId, String state) {
        if (userId == null || userId <= 0L) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<SohuVideo> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuVideo::getUserId, userId).set(SohuVideo::getState, state);
        // 修改万能表的状态
        sohuContentMainService.updateUserObjState(userId, BusyType.Video.name(), state);
        return baseMapper.update(new SohuVideo(), luw) > 0;
    }

    @Override
    public Boolean updateState(SohuBusyUpdateStateBo bo) {
        String rejectReason = bo.getRejectReason();
        Long busyCode = bo.getBusyCode();
        SohuVideo video = baseMapper.selectById(busyCode);
        if (Objects.isNull(video)) {
            return Boolean.FALSE;
        }
        video.setState(StrUtil.isBlankIfStr(bo.getState()) ? CommonState.CompelOff.getCode() : bo.getState());
        video.setRejectReason(rejectReason);
        video.setUpdateTime(new Date());
        // 更新视频
        baseMapper.updateById(video);
        // 更新万能表
        sohuContentMainMapper.updateState(video.getState(), busyCode, BusyType.Video.name());
        // 智能推送表
        updateSohuAirecContentVideoItem(video);
        return Boolean.TRUE;
    }

    @Override
    public Long queryVideoNumBySite(Long siteId, Long userId) {
        LambdaQueryWrapper<SohuVideo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SohuVideo::getUserId, userId);
        wrapper.eq(SohuVideo::getSiteId, siteId);
        wrapper.notIn(SohuVideo::getState, CommonState.Delete.name());
        return baseMapper.selectCount(wrapper);
    }

    @Override
    public CompletableFuture<Boolean> playletForward(Long playletId) {
        return CompletableFuture.supplyAsync(() -> {
            if (playletId == null || playletId <= 0L) {
                return Boolean.FALSE;
            }
            SohuVideoInfoVo infoVo = sohuVideoInfoService.queryByVideoId(playletId);
            if (Objects.isNull(infoVo) || infoVo.getId() == null) {
                return Boolean.FALSE;
            }
            int count = infoVo.getForwardCount() + 1;
            SohuVideoInfoBo infoBo = new SohuVideoInfoBo();
            infoBo.setId(infoVo.getId());
            infoBo.setForwardCount(count);

            return sohuVideoInfoService.updateByBo(infoBo);
        }, asyncConfig.getAsyncExecutor());
    }

    @Override
    public List<PlayletListVo> getPlayletList(String episodeRelevance, BaseCommonBo commonBo) {
        // 方法复用
        List<SohuVideoVo> sohuVideoVoList = this.listByEp(episodeRelevance, commonBo);
        if (CollUtil.isEmpty(sohuVideoVoList)) {
            return CollUtil.newArrayList();
        }
        List<PlayletListVo> playletListVoList = new ArrayList<>();
        for (SohuVideoVo sohuVideoVo : sohuVideoVoList) {
            PlayletListVo playletVo = new PlayletListVo();
            playletVo.setPlayletVideoUrl(sohuVideoVo.getVideoUrl());
            playletVo.setPlayletSortIndex(sohuVideoVo.getEpisodeNumber());
            playletVo.setPaid(sohuVideoVo.getPaid());
            playletVo.setPlayletEpisodePay(sohuVideoVo.getEpisodePay());
            playletVo.setPlayetId(sohuVideoVo.getPlayetId());
            playletVo.setPlayetVideoKey(sohuVideoVo.getVideoKey());
            playletVo.setVideoId(sohuVideoVo.getId());
            playletListVoList.add(playletVo);
        }
        return playletListVoList;
    }

    @Override
    public Map<Long, SohuVideoVo> map(Set<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new HashMap<>();
        }
        List<SohuVideoVo> articles = baseMapper.selectVoBatchIds(ids);
        return CollUtil.isEmpty(articles) ? new HashMap<>() : articles.stream().collect(Collectors.toMap(SohuVideoVo::getId, u -> u));
    }

    /**
     * 根据标签查排序前五的视频
     */
    private List<SohuVideoVo> hotList(Long labelId) {
        LambdaQueryWrapper<SohuVideo> lqw = Wrappers.lambdaQuery();
        if (labelId != null && labelId > 0L) {
            lqw.eq(SohuVideo::getLessonLabelId, labelId);
        }
        lqw.eq(SohuVideo::getState, CommonState.OnShelf.getCode());
        lqw.orderByAsc(SohuVideo::getSortIndex);
        lqw.orderByDesc(SohuVideo::getCreateTime);
        lqw.last("limit 5");
        List<SohuVideoVo> sohuVideoVos = baseMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(sohuVideoVos)) {
            Set<Long> userIds = sohuVideoVos.stream().filter(p -> Objects.nonNull(p.getUserId())).map(p -> p.getUserId()).collect(Collectors.toSet());
            Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
            for (SohuVideoVo videoVo : sohuVideoVos) {
                SohuVideoInfoVo infoVo = sohuVideoInfoService.queryByVideoId(videoVo.getId());
                // 设置内容
                if (Objects.nonNull(infoVo)) {
                    videoVo.setCollectCount(infoVo.getCollectCount());
                    videoVo.setViewCount(infoVo.getViewCount());
                    videoVo.setCommentCount(infoVo.getCommentCount());
                    videoVo.setPraiseCount(infoVo.getPraiseCount());
                    videoVo.setForwardCount(infoVo.getForwardCount());
                    videoVo.setLearnNum(infoVo.getLearnNum());
                    LoginUser loginUser = userMap.get(videoVo.getUserId());
                    if (Objects.nonNull(loginUser)) {
                        videoVo.setUserAvatar(loginUser.getAvatar());
                        videoVo.setNickName(loginUser.getNickname());
                    }
                }
            }
        }
        return sohuVideoVos;
    }

    /**
     * 初始化视频物料
     */
    @Override
    public Boolean initAirecContentItems() {
//        LambdaQueryWrapper<SohuVideo> lqw = new LambdaQueryWrapper<>();
//        lqw.eq(SohuVideo::getState, CommonState.OnShelf.name())
//                .eq(SohuVideo::getVisibleType, VisibleTypeEnum.open.getCode())
//                .eq(SohuVideo::getIsPay, false);
        //设置分页参数
        // 查询总数
//        long total = baseMapper.selectCount(lqw);
        long total = baseMapper.selectCount();
        final int PAGE_SIZE = AliyunAirecConstant.BATCH_SIZE;
        // 总页数
        long totalPages = (total + PAGE_SIZE - 1) / PAGE_SIZE;
        for (int i = 1; i <= totalPages; i++) {
            // 分页查询
            Page<SohuVideo> page = new Page<>(i, PAGE_SIZE);
//            IPage<SohuVideo> pageResult = baseMapper.selectPage(page, lqw);
            IPage<SohuVideo> pageResult = baseMapper.selectPage(page, null);
            List<SohuVideo> list = pageResult.getRecords();
            // 处理查询结果
            if (CollUtil.isNotEmpty(list)) {
                //关联项
                List<Long> idList = list.stream().map(p -> p.getId()).collect(Collectors.toList());
                List<SohuVideoRelate> relateList = this.sohuVideoRelateMapper.selectList(SohuVideoRelate::getVideoId, idList);
                //存在关联项的id集合
                Set<Long> idSet = new HashSet<>();
                if (CollUtil.isNotEmpty(relateList)) {
                    idSet = relateList.stream().map(p -> p.getVideoId()).collect(Collectors.toSet());
                }
                //物料信息记录
                List<SohuAirecContentItemBo> modelList = new ArrayList<>();
                for (SohuVideo entity : list) {
                    SohuAirecContentItemBo model = buildAirecContentItemModel(entity, idSet.contains(entity.getId()));
                    model.setTags(iSohuAirecTagRelationService.saveTagStr(entity.getId(), AiRecTag.BizTypeEnum.VIDEO.getCode(), model.getTags()));
                    modelList.add(model);
                }
                //保存物料信息
                iSohuAirecContentItemService.initAirecContentItems(modelList);
            }
        }
        return true;
    }

    @Override
    public TableDataInfo<SohuVideoVo> playletVideoList(SohuPlayletVideoBo bo) {
        Long userId = LoginHelper.getUserId();
//        //如果需要智能推荐
//        if (bo.getAiRecommend()) {
//            TableDataInfo<SohuVideoVo> sohuAirecContentVideo = getSohuAirecContentShortVideo(bo, userId);
//            if (CollUtil.isNotEmpty(sohuAirecContentVideo.getData())) {
//                if (userId != null && StringUtils.isEmpty(bo.getImei())) {
//                    getRecord(sohuAirecContentVideo.getData(), userId);
//                }
//                //如果推荐内容不为空返回推荐
//                return sohuAirecContentVideo;
//            }
//        }
        LambdaQueryWrapper<SohuVideo> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuVideo::getState, CommonState.OnShelf.name());
        lqw.eq(SohuVideo::getIsPay, false);
        lqw.eq(SohuVideo::getType, VideoEnum.Type.playlet);
        lqw.isNotNull(SohuVideo::getEpisodeRelevance);
        if (StrUtil.isNotBlank(bo.getEpisodeRelevance())) {
            lqw.eq(SohuVideo::getEpisodeRelevance, bo.getEpisodeRelevance());
        }
        lqw.orderByDesc(BaseEntity::getCreateTime);
        PageQuery pageQuery = new PageQuery(bo.getPageNum(), bo.getPageSize());
        Page<SohuVideoVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            getRecord(result.getRecords(), LoginHelper.getUserId());
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public TableDataInfo<SohuVideoVo> playletVideoListOfAirec(SohuPlayletVideoBo bo) {
        if (bo.getAiRec()) {
            if (StrUtil.isBlank(bo.getAiRecImei()) && Objects.isNull(LoginHelper.getUserId())) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (!(AliyunAirecConstant.SCENE_VIDEO_ALL.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_VIDEO_HOMEPAGE.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_VIDEO_MY_MAY_LIKE.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_VIDEO_MONEYMAKING.equals(bo.getAiRecSceneId()))) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (Objects.nonNull(LoginHelper.getUserId())) {
                bo.setAiUserId(LoginHelper.getUserId().toString());
            }
            bo.setAiReturnCount(bo.getPageSize());
            //构造过滤的内容
            SohuAirecVideoQueryBo airecQueryBo = new SohuAirecVideoQueryBo();
            AliyunAirecJoinFilterRule rootRule = airecQueryBo.buildAirecFilterRule();
            //this.buildAirecJoinFilterRule(null, rootRule);
            //获取阿里云智能推荐结果
            List<SohuVideoVo> resultList = AliyunAirecUtil.aiRecommendSingleType(rootRule, bo, itemIds -> baseMapper.selectVoBatchIds(itemIds));
            if (CollUtil.isNotEmpty(resultList)) {
                getRecord(resultList, LoginHelper.getUserId());
                return TableDataInfoUtils.build(resultList);
            }
        }
        TableDataInfo<SohuVideoVo> tableDataInfo = this.playletVideoList(bo);
        AliyunAirecUtil.buildAiRecommendSingleType(tableDataInfo.getData(), AliyunAirecContentItemTypeEnum.VIDEO.getCode());
        return tableDataInfo;
    }

    /**
     * TODO 此类里面有相似代码请重新封装一下
     *
     * @param records
     * @param userId
     */
    public void getRecord(List<SohuVideoVo> records, Long userId) {
        Set<Long> ossIds = new HashSet<>();
        List<Long> videoIds = new ArrayList<>();
        List<Long> userIds = new ArrayList<>();

        for (SohuVideoVo record : records) {
            videoIds.add(record.getId());
            userIds.add(record.getUserId());
            if (NumberUtil.isNumber(record.getCoverImage())) {
                ossIds.add(Long.valueOf(record.getCoverImage()));
            }
            if (StrUtil.isNotBlank(record.getEpisodeRelevance())) {
                SohuPlayletVo playletInfo = playLetService.getPlayletInfo(record.getEpisodeRelevance(), 1);
                record.setPlayletTitle(playletInfo.getTitle());
                record.setEpisodeCount(playletInfo.getEpisodeCount());
            }
        }

        Map<Long, SohuVideoInfoVo> infoVoMap = sohuVideoInfoService.selectMap(videoIds);
        Map<Long, String> map = remoteFileService.map(ossIds);
        Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
        Map<Long, SohuUserLikeVo> likeMap;
        Map<Long, SohuUserCollectVo> collectMap;
        Map<Long, SohuUserFollowVo> userFollowMap;
        if (userId != null && userId > 0L) {
            userFollowMap = sohuUserFollowService.mapUserFollows(userId, userIds);
            likeMap = sohuUserLikeService.queryVideoMap(userId, videoIds);
            collectMap = sohuUserCollectService.queryVideoMap(userId, videoIds);
        } else {
            userFollowMap = new HashMap<>();
            likeMap = new HashMap<>();
            collectMap = new HashMap<>();
        }
        for (SohuVideoVo record : records) {
            SohuVideoInfoVo infoVo = infoVoMap.get(record.getId());
            if (Objects.nonNull(infoVo)) {
                record.setPraiseCount(infoVo.getPraiseCount());
                record.setCommentCount(infoVo.getCommentCount());
                record.setCollectCount(infoVo.getCollectCount());
                record.setLearnNum(infoVo.getLearnNum());
                record.setForwardCount(infoVo.getForwardCount());
            }
            LoginUser user = userMap.get(record.getUserId());
            if (Objects.nonNull(user)) {
                record.setUserName(StrUtil.isNotBlank(user.getNickname()) ? user.getNickname() : user.getUsername());
                record.setNickName(StrUtil.isNotBlank(user.getNickname()) ? user.getNickname() : user.getUsername());
                record.setUserAvatar(StrUtil.isNotBlank(user.getAvatar()) ? user.getAvatar() : Constants.DEFAULT_AVATAR);
            }
            if (userId != null && userId > 0L) {
                record.setPraiseObj(Objects.nonNull(likeMap.get(record.getId())));
                record.setCollectObj(Objects.nonNull(collectMap.get(record.getId())));
                // 我关注状态，true = 关注
                record.setFollowObj(Objects.nonNull(userFollowMap.get(record.getUserId())));
            }
            if (NumberUtil.isNumber(record.getCoverImage()) && map != null) {
                record.setCoverUrl(map.get(Long.valueOf(record.getCoverImage())));
                record.setCoverImage(record.getCoverUrl());
            } else {
                record.setCoverUrl(record.getCoverImage());
            }
        }
        this.setListRelation(records);
    }

    @Override
    public Boolean updateBatchContentState(SohuContentBatchBo bo) {
        // 获取需要修改的主键id
        List<String> idList = StrUtil.split(bo.getIds(), COMMA);

        List<SohuVideo> sohuVideoList = baseMapper.selectBatchIds(idList);
        // 如果查询结果为空，或者数量与传入的 id 数量不一致，返回失败
        if (sohuVideoList == null || sohuVideoList.size() != idList.size()) {
            return Boolean.FALSE;
        }
        // 校验所有作品的作者 ID 是否一致
        Long currentUserId = LoginHelper.getUserId();
        boolean isAllAuthorMatched = sohuVideoList.stream()
                .allMatch(article -> article.getUserId().equals(currentUserId));

        if (!isAllAuthorMatched) {
            return Boolean.FALSE;
        }
        // 构建需要更新的内容
        List<SohuVideo> contentList = sohuVideoList.stream()
                .map(article -> {
                    SohuVideo content = new SohuVideo();
                    content.setId(article.getId());
                    content.setState(bo.getState());
                    return content;
                })
                .collect(Collectors.toList());

        // 执行批量更新
        return baseMapper.updateBatchById(contentList);
    }

    @Override
    public Long countByTime(String startTime, String endTime) {
        LambdaQueryWrapper<SohuVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(SohuVideo::getCreateTime, startTime, endTime);
        return this.baseMapper.selectCount(wrapper);
    }
//    @Override
//    public Boolean submitAudit(Long id) {
//        SohuVideo entity = this.baseMapper.selectById(id);
//        if (Objects.isNull(entity)) {
//            throw new RuntimeException("数据不存在");
//        }
//        if (!Objects.equals(LoginHelper.getUserId(), entity.getUserId())) {
//            throw new RuntimeException("非法操作,这不是您的数据");
//        }
//        if (!(Objects.equals(entity.getState(), CommonState.Delete.name())
//                || Objects.equals(entity.getState(), CommonState.ForceDelete.name()))) {
//            throw new RuntimeException("非审核拒绝状态，不支持申诉");
//        }
//        SohuVideo updateEntity = new SohuVideo();
//        updateEntity.setId(id);
//        LambdaUpdateWrapper<SohuVideo> luw = new LambdaUpdateWrapper<>();
//        luw.eq(SohuVideo::getState, entity.getState());
//        luw.eq(SohuVideo::getId, id);
//        SohuContentLifecycleVo lifecycleVo = this.sohuContentLifecycleService.selectOfLast(id, BusyType.Video.getType());
//        if (Objects.isNull(lifecycleVo)) {
//            //兼容历史数据
//            updateEntity.setState(CommonState.OffShelf.name());
//        } else {
//            updateEntity.setState(lifecycleVo.getLastState());
//        }
//        return this.baseMapper.update(updateEntity, luw) > 0;
//    }

    @Override
    public boolean updateOffShelfById(Long id) {
        SohuVideo sohuVideo = baseMapper.selectById(id);
        if (Objects.isNull(sohuVideo)) {
            return false;
        }
        if (!Objects.equals(LoginHelper.getUserId(), sohuVideo.getUserId())) {
            throw new RuntimeException("非法操作，这不是您的数据");
        }
        sohuVideo.setState(CommonState.OffShelf.name());
        baseMapper.updateById(sohuVideo);
        iSohuAirecContentItemService.updateStatusToOffShelf(id.toString(), AliyunAirecContentItemTypeEnum.VIDEO.getCode());
        // 同步素材库
        syncIndependentMaterial(sohuVideo);
        return true;
    }

    /**
     * 同步分销素材库
     *
     * @param sohuVideo
     */
    private void syncIndependentMaterial(SohuVideo sohuVideo) {
        SohuVideoRelateVo relate = sohuVideoRelateService.getByVideoId(sohuVideo.getId());
        if (Objects.isNull(relate) || StrUtil.equalsAnyIgnoreCase(relate.getBusyType(), BusyType.BusyTask.getType())) {
            return;
        }
        SohuBusyTaskSiteVo taskSiteVo = remoteBusyTaskSiteService.queryByTaskNumber(relate.getBusyInfo());
        if (Objects.isNull(taskSiteVo)) {
            return;
        }
        remoteMiddleIndependentMaterialService.deleteByCodeAndType(taskSiteVo.getMasterTaskNumber(), taskSiteVo.getConstMark(), sohuVideo.getUserId());
    }

    @Override
    public boolean updateCompelOffById(SohuContentRefuseBo bo) {
//        if (!LoginHelper.anyMatchRole(RoleCodeEnum.ADMIN.getCode(), RoleCodeEnum.CALL_ADMIN.getCode(), RoleCodeEnum.OPERATION_ADMIN.getCode())) {
//            throw new RuntimeException("非法操作，您无权操作");
//        }
        SohuVideo sohuVideo = baseMapper.selectById(bo.getId());
        if (Objects.isNull(sohuVideo)) {
            return false;
        }
        sohuVideo.setState(CommonState.CompelOff.name());
        sohuVideo.setRejectReason(bo.getReason());
        baseMapper.updateById(sohuVideo);
        iSohuAirecContentItemService.updateStatusToOffShelf(bo.getId().toString(), AliyunAirecContentItemTypeEnum.VIDEO.getCode());
        // 同步素材库
        syncIndependentMaterial(sohuVideo);
        CompletableFuture.runAsync(() -> sendMsgOfCompelOff(sohuVideo), asyncConfig.getAsyncExecutor());
        return true;
    }

    /**
     * 发送系统消息通知-强制下架
     */
    private void sendMsgOfCompelOff(SohuVideo sohuVideo) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.CONTENT_COMPEL_OFF_TITLE);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.videoCompelOff.name());
        content.setDetailId(sohuVideo.getId());
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setId(sohuVideo.getId());
        detail.setDesc(String.format(SystemNoticeEnum.CONTENT_COMPEL_OFF_DESC, sohuVideo.getTitle(), sohuVideo.getRejectReason()));
        detail.setStatus(CommonState.CompelOff.name());
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(sohuVideo.getUserId(), SystemNoticeEnum.CONTENT_COMPEL_OFF_TITLE, contentJson, SystemNoticeEnum.Type.contentCompelOff);
    }

    @Override
    public Boolean recoveryData(Long id) {
        SohuVideo entity = this.baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new RuntimeException("数据不存在");
        }
        if (!Objects.equals(LoginHelper.getUserId(), entity.getUserId())) {
            throw new RuntimeException("非法操作,这不是您的数据");
        }
        if (!(Objects.equals(entity.getState(), CommonState.Delete.name())
                || Objects.equals(entity.getState(), CommonState.ForceDelete.name()))) {
            throw new RuntimeException("非审核拒绝状态，不支持申诉");
        }
        SohuVideo updateEntity = new SohuVideo();
        updateEntity.setId(id);
        LambdaUpdateWrapper<SohuVideo> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuVideo::getState, entity.getState());
        luw.eq(SohuVideo::getId, id);
        SohuContentLifecycleVo lifecycleVo = this.sohuContentLifecycleService.selectOfLast(id, BusyType.Video.name());
        if (Objects.isNull(lifecycleVo)) {
            //兼容历史数据
            updateEntity.setState(CommonState.OffShelf.name());
        } else {
            updateEntity.setState(lifecycleVo.getLastState());
        }
        return this.baseMapper.update(updateEntity, luw) > 0;
    }

    @Override
    public Boolean auditOnShelf(Long id) {
        SohuVideo video = baseMapper.selectById(id);
        if (Objects.isNull(video)) {
            throw new RuntimeException("内容不存在");
        }
        if (!(Objects.equals(video.getState(), CommonState.WaitApprove.name())
                || Objects.equals(video.getState(), CommonState.Refuse.name()))) {
            throw new RuntimeException("此状态不支持上架，请检查数据");
        }
        video.setState(CommonState.OnShelf.name());
        video.setAuditState(CommonState.Pass.name());
        video.setAuditTime(new Date());
        video.setAppealStatus(false);
        video.setAppealReason(null);
        LambdaUpdateWrapper<SohuVideo> luw = new LambdaUpdateWrapper<>();
        luw.set(SohuVideo::getAppealReason, null);
        luw.set(SohuVideo::getRejectReason, null);
        luw.eq(SohuVideo::getId, id);
        this.baseMapper.update(video, luw);
        // 更新万能表
        sohuContentMainMapper.updateState(video.getState(), id, BusyType.Video.name());
        // 智能推送表
        updateSohuAirecContentArticleItem(video);
        return true;
    }

    @Override
    public Boolean auditRefuse(Long id, String rejectReason) {
        SohuVideo video = baseMapper.selectById(id);
        if (Objects.isNull(video)) {
            throw new RuntimeException("内容不存在");
        }
        if (!(Objects.equals(video.getState(), CommonState.WaitApprove.name())
                || Objects.equals(video.getState(), CommonState.Refuse.name()))) {
            throw new RuntimeException("此状态不支持上架，请检查数据");
        }
        video.setState(CommonState.Refuse.name());
        video.setAuditState(CommonState.Refuse.name());
        video.setAuditTime(new Date());
        video.setRejectReason(rejectReason);
        //video.setSubmitNum(video.getSubmitNum() + 1);
        this.baseMapper.updateById(video);
        // 更新万能表
        sohuContentMainMapper.updateState(video.getState(), id, BusyType.Video.name());
        // 智能推送表
        updateSohuAirecContentArticleItem(video);
        return true;
    }

    @Override
    public Boolean userAppeal(SohuUserContentAppealBo bo) {
        SohuVideo video = baseMapper.selectById(bo.getId());
        if (Objects.isNull(video)) {
            throw new RuntimeException("内容不存在");
        }
        if (!Objects.equals(video.getState(), CommonState.Refuse.name())) {
            throw new RuntimeException("非审核拒绝状态，不支持申诉");
        }
        if (BooleanUtil.isTrue(video.getAppealStatus())) {
            throw new RuntimeException("您已申诉过，只有一次申诉机会");
        }
        SohuVideo updateEntity = new SohuVideo();
        //updateEntity.setState(CommonState.WaitApprove.name());
        updateEntity.setAppealStatus(true);
        updateEntity.setAppealReason(bo.getAppealReason());
        updateEntity.setAuditState(CommonState.WaitApprove.name());
        updateEntity.setSubmitScene("申诉复审");
        LambdaUpdateWrapper<SohuVideo> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuVideo::getId, bo.getId());
        luw.eq(SohuVideo::getState, CommonState.Refuse.name());
        this.baseMapper.update(updateEntity, luw);
        //初始化审核
        video.setAppealStatus(updateEntity.getAppealStatus());
        video.setAppealReason(updateEntity.getAppealReason());
        video.setSubmitScene(updateEntity.getSubmitScene());
        video.setState(CommonState.WaitApprove.name());
        this.initCreateAudited(video);
        return true;
    }

    @Override
    public Boolean hideDataBatch(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        for (Long id : ids) {
            this.hideData(id);
        }
        return true;
    }

    @Override
    public Boolean hideData(Long id) {
        SohuVideo entity = this.baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new RuntimeException("数据不存在");
        }
        if (!Objects.equals(LoginHelper.getUserId(), entity.getUserId())) {
            throw new RuntimeException("非法操作,这不是您的数据");
        }
        if (!Objects.equals(entity.getState(), CommonState.OnShelf.name())) {
            throw new RuntimeException("未上架，不支持隐藏");
        }
        SohuVideo updateEntity = new SohuVideo();
        updateEntity.setId(id);
        updateEntity.setState(CommonState.Hide.name());
        LambdaUpdateWrapper<SohuVideo> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuVideo::getState, entity.getState());
        luw.eq(SohuVideo::getId, id);
        if (this.baseMapper.update(updateEntity, luw) > 0) {
            this.iSohuAirecContentItemService.updateStatusToOffShelf(id.toString(), AliyunAirecContentItemTypeEnum.VIDEO.getCode());
        }
        return true;
    }

    @Override
    public void updateSohuAirecContentArticleItem(SohuVideo video) {
//        if (!Objects.equals(VisibleTypeEnum.open.getCode(), article.getVisibleType())) {
//            return;
//        }
        //上架
        if (StringUtils.equalsAnyIgnoreCase(CommonState.OnShelf.getCode(), video.getState())) {
            // 关联项
            SohuVideoRelate relate = sohuVideoRelateMapper.selectOne(SohuVideoRelate::getVideoId, video.getId());
            SohuAirecContentItemBo model = buildAirecContentItemModel(video, Objects.nonNull(relate));
            model.setTags(iSohuAirecTagRelationService.saveTagStr(video.getId(), AiRecTag.BizTypeEnum.VIDEO.getCode(), model.getTags()));
            iSohuAirecContentItemService.saveAirecContentItem(model);
        } else {
            //下架
            iSohuAirecContentItemService.updateStatusToOffShelf(video.getId().toString(), AliyunAirecContentItemTypeEnum.VIDEO.getCode());
        }

    }

    @Override
    public Boolean deleteDataById(Long id) {
        SohuVideo sohuVideo = baseMapper.selectById(id);
        if (Objects.isNull(sohuVideo)) {
            return false;
        }
        if (!Objects.equals(LoginHelper.getUserId(), sohuVideo.getUserId())) {
            throw new RuntimeException("非法操作，这不是您的数据");
        }

        return sohuContentMainMapper.flushRecycleVideo(LoginHelper.getUserId(), Collections.singleton(id));
    }

    @Override
    public Boolean clearRecycleDataOfTimeOut() {
        LambdaUpdateWrapper<SohuVideo> luw = new LambdaUpdateWrapper<>();
        luw.set(SohuVideo::getDelFlag, Constants.TWO);
        luw.in(SohuVideo::getState, CommonState.Delete.name(), CommonState.ForceDelete.name());
        luw.lt(SohuVideo::getDelTime, DateUtil.offsetDay(new Date(), -CON_RECYCLE_DATA_TIME_OUT.intValue()));
        this.baseMapper.update(new SohuVideo(), luw);
        return true;
    }

    @Override
    public TableDataInfo<SohuVideoVo> getTopicList(SohuTopicContentQueryBo bo, PageQuery pageQuery) {
        Page<SohuVideoVo> sohuVideoVoPage = baseMapper.getTopicList(bo, PageQueryUtils.build(pageQuery));
        if (CollUtil.isNotEmpty(sohuVideoVoPage.getRecords())) {
            getRecord(sohuVideoVoPage.getRecords(), LoginHelper.getUserId());
        }
        return TableDataInfoUtils.build(sohuVideoVoPage);
    }
}
