package com.sohu.middleService.service.impl;

import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuModelConfigBo;
import com.sohu.middle.api.vo.SohuModelConfigVo;
import com.sohu.middleService.domain.SohuModelConfig;
import com.sohu.middleService.mapper.SohuModelConfigMapper;
import com.sohu.middleService.service.ISohuModelConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 移动端设备机型配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@RequiredArgsConstructor
@Service
public class SohuModelConfigServiceImpl implements ISohuModelConfigService {

    private final SohuModelConfigMapper baseMapper;

    /**
     * 查询移动端设备机型配置
     */
    @Override
    public SohuModelConfigVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询移动端设备机型配置列表
     */
    @Override
    public TableDataInfo<SohuModelConfigVo> queryPageList(SohuModelConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuModelConfig> lqw = buildQueryWrapper(bo);
        Page<SohuModelConfigVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询移动端设备机型配置列表
     */
    @Override
    public List<SohuModelConfigVo> queryList(SohuModelConfigBo bo) {
        LambdaQueryWrapper<SohuModelConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuModelConfig> buildQueryWrapper(SohuModelConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuModelConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceUnitNo()), SohuModelConfig::getDeviceUnitNo, bo.getDeviceUnitNo());
        lqw.eq(StringUtils.isNotBlank(bo.getModelNo()), SohuModelConfig::getModelNo, bo.getModelNo());
        lqw.eq(bo.getType() != null, SohuModelConfig::getType, bo.getType());
        lqw.eq(bo.getEnable() != null, SohuModelConfig::getEnable, bo.getEnable());
        return lqw;
    }

}
