package com.sohu.middleService.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sohu.middle.api.bo.SohuTopicContentQueryBo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.mcn.SohuMcnArticleReqBo;
import com.sohu.middle.api.vo.SohuArticleVo;
import com.sohu.middle.api.vo.SohuConentListStatVo;
import com.sohu.middle.api.vo.SohuTopArticleVo;
import com.sohu.middleService.domain.SohuArticle;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 图文主体Service接口
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
public interface ISohuArticleService extends ISohuBaseService<SohuArticle, SohuArticleVo> {

    /**
     * 获取对象的作者id
     *
     * @param id 对象主键id
     * @return 返回对象的作者id，不存在就返回0
     */
    Long getAuthorId(Long id);

    /**
     * 查询图文主体-返回图文主体
     *
     * @param id 主键ID
     */
    SohuArticleVo get(Long id);

    /**
     * 查询图文主体-返回图文主体
     *
     * @param id 主键ID
     */
    @Override
    SohuArticleVo queryById(Long id);

    /**
     * 查询图文主体-返回更多信息
     *
     * @param id 主键ID
     */
    SohuArticleVo selectVoById(Long id);

    /**
     * 查询图文主体列表
     */
    TableDataInfo<SohuArticleVo> queryPageList(SohuArticleBo bo, PageQuery pageQuery);

    /**
     * 查询图文主体列表-OnShelf
     */
    TableDataInfo<SohuArticleVo> queryPageListOfOnShelf(SohuArticleBo bo, PageQuery pageQuery);

    /**
     * 查询图文主体列表-统计
     * @param bo
     * @return
     */
    SohuConentListStatVo queryPageListStat(SohuArticleBo bo);

    /**
     * 图文列表（单表-新）
     */
    TableDataInfo<SohuArticleVo> queryPage(SohuArticleBo bo, PageQuery pageQuery);

    /**
     * 图文列表（单表-新）-智能推荐
     */
    TableDataInfo<SohuArticleVo> queryPageOfAirec(SohuArticleBo bo, PageQuery pageQuery);

    /**
     * 个人中心图文列表
     */
    TableDataInfo<SohuArticleVo> articlePageCenter(Long userId, PageQuery pageQuery);

    /**
     * 查询图文主体列表
     */
    List<SohuArticleVo> queryList(SohuArticleBo bo);

    /**
     * 修改图文主体
     */
    Boolean insertByBo(SohuArticleBo bo);

    /**
     * 修改图文主体
     */
    Boolean updateByBo(SohuArticleBo bo);
//
//    /**
//     * 校验并批量删除图文主体信息
//     */
//    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 图文草稿提交至审核
     *
     * @param articleId 图文ID
     */
    @Deprecated
    void commit(Long articleId);

    /**
     * 提交草稿至审核
     * @param id
     * @return
     */
    Boolean submitAudit(Long id);

    /**
     * 关注图文
     */
    TableDataInfo<SohuArticleVo> followPage(PageQuery pageQuery);

    /**
     * 图文分页查询
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuArticleVo> queryMCNArticleList(SohuMcnArticleReqBo bo, PageQuery pageQuery);

    /**
     * 图文逻辑删除-自主删除
     *
     * @param id
     * @return
     */
    boolean logicDeleteById(Long id);

    /**
     * 图文逻辑删除-自主删除-批量
     *
     * @param ids
     * @return
     */
    boolean logicDeleteById(Collection<Long> ids);

    /**
     * 图文逻辑删除-强制删除
     *
     * @param id
     * @return
     */
    boolean logicForceDeleteById(Long id);

    /**
     * 图文逻辑删除-强制删除-批量
     *
     * @param ids
     * @return
     */
    boolean logicForceDeleteById(Collection<Long> ids);

    /**
     * 图文下架-自主下架
     *
     * @param id
     * @return
     */
    boolean updateOffShelfById(Long id);

    /**
     * 图文下架-强制下架
     *
     * @param bo
     * @return
     */
    boolean updateCompelOffById(SohuContentRefuseBo bo);

    /**
     * 获取MCN图文点赞总数
     */
    Long getMcnArticlePraiseStat(Long userId, Long mcnId);

    /**
     * 获取MCN图文评论总数
     */
    Long getMcnArticleCommentStat(Long userId, Long mcnId);

    /**
     * 获取MCN图文浏览量
     */
    Long getMcnArticleViewStat(Long userId, Long mcnId);

    /**
     * 赚钱图文列表
     *
     * @param bo
     * @return
     */
    IPage<SohuArticleVo> businessArticleList(SohuBusinessArticleBo bo);

    /**
     * 赚钱图文列表-智能推荐
     *
     * @param bo
     * @return
     */
    TableDataInfo<SohuArticleVo> businessArticleListOfAirec(SohuBusinessArticleBo bo);

    void buildRelate(SohuArticleVo articleVo);

    /**
     * 更新图文只能推荐物料
     *
     * @param article
     */
    void updateSohuAirecContentArticleItem(SohuArticle article);

//    /**
//     * 推荐图文列表
//     *
//     * @return
//     */
//    TableDataInfo<SohuArticleVo> getSohuAirecContentArticle(SohuArticleBo sohuArticleBo);

    /**
     * 初始化智能推荐物料
     */
    Boolean initAirecContentItems();

    /**
     * 根据发布前的唯一表示查询发布者id
     */
    Long getUserIdByPublishMediaId(String publishMediaId);

    /**
     * 返回每个标签下前五的图文
     * 双层数组结构返回
     */
    List<SohuTopArticleVo> labelTopFive();

    /**
     * 投流图文
     */
    TableDataInfo<SohuArticleVo> articlePageCenterByType(SohuArticleBo bo, PageQuery pageQuery);

    /**
     * 评论，增加评论数量
     *
     * @param bo
     * @return
     */
    Boolean comment(SohuCommentBo bo, Boolean commentCountAdd);

    /**
     * 点赞
     *
     * @param bo
     * @return
     */
    Boolean like(SohuBusyBO bo);

    /**
     * 收藏
     *
     * @param bo
     * @return
     */
    Boolean collect(SohuBusyBO bo);

    /**
     * 修改图文的状态，上下架，强制下架，通过，驳回等
     *
     * @param bo
     * @return {@link Boolean}
     */
    @Deprecated
    Boolean updateState(SohuBusyUpdateStateBo bo);

    /**
     * 审核上架
     * @param id
     * @return
     */
    Boolean auditOnShelf(Long id);

    /**
     * 审核拒绝
     * @param id
     * @param rejectReason
     * @return
     */
    Boolean auditRefuse(Long id,String rejectReason);

    /**
     * 用户申述
     * @return
     */
    Boolean userAppeal(SohuUserContentAppealBo bo);

    /**
     * 隐藏数据
     * @param ids
     * @return
     */
    Boolean hideDataBatch(Collection<Long> ids);

    /**
     * 草稿重发
     *
     * @param busyBO
     * @return {@link Boolean}
     */
    @Deprecated
    Boolean draftRetry(SohuBusyBO busyBO);

    /**
     * 转发图文
     *
     * @param bo
     * @return
     */
    Boolean forward(SohuBusyBO bo);

    /**
     * 修改用户所有图文的状态，删除的话是假删除
     *
     * @param userId 用户ID
     * @param state  {@link com.sohu.common.core.enums.CommonState}
     * @return {@link Boolean}
     */
    @Deprecated
    Boolean updateUserArticleState(Long userId, String state);

    /**
     * 站点内容数
     */
    Long queryArticleNumBySite(Long siteId, Long userId);

    /**
     * 返回map
     *
     * @param ids 主键集合
     * @return {@link Map}
     */
    Map<Long, SohuArticleVo> map(Collection<Long> ids);

    /**
     * 批量修改作品状态
     *
     * @param bo SohuContentBatchBo
     * @return Boolean
     */
    @Deprecated
    Boolean updateBatchContentState(SohuContentBatchBo bo);


    /**
     * 从回收站恢复数据
     * @param id
     * @return
     */
    Boolean recoveryData(Long id);

    /**
     * 从回收站删除数据
     * @param id
     * @return
     */
    Boolean deleteDataById(Long id);

    /**
     * 清空回收站数据-过期
     * @return
     */
    Boolean clearRecycleDataOfTimeOut();

    /**
     * 通过id与状态查询图文
     * @param ids
     * @param state
     * @return
     */
    List<SohuArticleVo> queryArticleByIds(List<Long> ids, String state);

    /**
     * 查询专题内容列表
     */
    TableDataInfo<SohuArticleVo> getTopicList(SohuTopicContentQueryBo bo, PageQuery pageQuery);

    /**
     * 查询课堂内容
     *
     * @return
     */
    List<SohuArticleVo> getLessonList();

    /**
     * 处理机审结果
     *
     * @param busyCode
     * @param isPass
     * @param reason
     */
    void handleRobot(String busyCode, Boolean isPass, String reason);

}
