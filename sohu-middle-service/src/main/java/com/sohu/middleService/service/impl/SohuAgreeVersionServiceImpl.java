package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuAgreeVersionBo;
import com.sohu.middle.api.bo.SohuUseBo;
import com.sohu.middle.api.vo.SohuAgreeVersionListVo;
import com.sohu.middle.api.vo.SohuAgreeVersionVo;
import com.sohu.middleService.domain.SohuAgreeVersion;
import com.sohu.middleService.domain.SohuUserAgree;
import com.sohu.middleService.mapper.SohuAgreeVersionMapper;
import com.sohu.middleService.mapper.SohuUserAgreeMapper;
import com.sohu.middleService.service.ISohuAgreeVersionService;
import com.sohu.middleService.service.ISohuUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 协议版本管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-28
 */
@RequiredArgsConstructor
@Service
public class SohuAgreeVersionServiceImpl implements ISohuAgreeVersionService {

    private final ISohuUserService userService;
    private final SohuAgreeVersionMapper baseMapper;
    private final SohuUserAgreeMapper userAgreeMapper;

    /**
     * 查询协议版本管理
     */
    @Override
    public SohuAgreeVersionVo queryById(Long id) {
        SohuAgreeVersionVo sohuAgreeVersionVo = baseMapper.selectVoById(id);
        SohuUserAgree sohuUserAgree = userAgreeMapper.selectById(sohuAgreeVersionVo.getAgreeId());
        if (Objects.nonNull(sohuUserAgree)) {
            sohuAgreeVersionVo.setName(sohuUserAgree.getName());
        }
        return sohuAgreeVersionVo;
    }

    /**
     * 查询协议版本管理列表
     */
    @Override
    public TableDataInfo<SohuAgreeVersionListVo> queryPageList(SohuAgreeVersionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuAgreeVersion> lqw = buildQueryWrapper(bo);
        Page<SohuAgreeVersion> result = baseMapper.selectPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuAgreeVersion> records = result.getRecords();
        List<SohuAgreeVersionListVo> agreeVersionListVos = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            List<Long> userIds = records.stream().map(SohuAgreeVersion::getUserId).collect(Collectors.toList());
            Map<Long, LoginUser> userMap = userService.selectMap(userIds);
            for (SohuAgreeVersion record : records) {
                SohuAgreeVersionListVo agreeVersionListVo = BeanCopyUtils.copy(record, SohuAgreeVersionListVo.class);
                LoginUser loginUser = userMap.get(record.getUserId());
                if (Objects.isNull(loginUser)) {
                    continue;
                }
                agreeVersionListVo.setUserName(loginUser.getNickname());
                SohuUserAgree sohuUserAgree = userAgreeMapper.selectById(record.getAgreeId());
                if (Objects.nonNull(sohuUserAgree)) {
                    agreeVersionListVo.setName(sohuUserAgree.getName());
                }
                agreeVersionListVos.add(agreeVersionListVo);
            }

        }
        TableDataInfo<SohuAgreeVersionListVo> dataInfo = TableDataInfoUtils.build(agreeVersionListVos);
        dataInfo.setTotal(result.getTotal());
        return dataInfo;
    }

    /**
     * 查询协议版本管理列表
     */
    @Override
    public List<SohuAgreeVersionVo> queryList(SohuAgreeVersionBo bo) {
        LambdaQueryWrapper<SohuAgreeVersion> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuAgreeVersion> buildQueryWrapper(SohuAgreeVersionBo bo) {
        LambdaQueryWrapper<SohuAgreeVersion> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAgreeId() != null, SohuAgreeVersion::getAgreeId, bo.getAgreeId());
        lqw.like(StringUtils.isNotBlank(bo.getVersionNum()), SohuAgreeVersion::getVersionNum, bo.getVersionNum());
        lqw.like(StringUtils.isNotBlank(bo.getDescription()), SohuAgreeVersion::getDescription, bo.getDescription());
        lqw.like(StringUtils.isNotBlank(bo.getContent()), SohuAgreeVersion::getContent, bo.getContent());
        return lqw;
    }

    /**
     * 新增协议版本管理
     */
    @Override
    public Boolean insertByBo(SohuAgreeVersionBo bo) {
        SohuAgreeVersion add = BeanUtil.toBean(bo, SohuAgreeVersion.class);
        add.setState(CommonState.Edit.getCode());
        add.setCreateTime(new Date());
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改协议版本管理
     */
    @Override
    public Boolean updateByBo(SohuAgreeVersionBo bo) {
        SohuAgreeVersion update = BeanUtil.toBean(bo, SohuAgreeVersion.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 批量删除协议版本管理
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean updateState(SohuUseBo bo) {
        SohuAgreeVersion agreeVersion = baseMapper.selectById(bo.getId());
        if (Objects.nonNull(agreeVersion)) {
            validEntityBeforeSave(agreeVersion);
            agreeVersion.setState(bo.getState());
            if (CommonState.OnShelf.getCode().equals(bo.getState())) {
                agreeVersion.setEnableTime(new Date());
                //有一个启用后，该协议底下的其他版本启用版本将改为禁用
                updateStateByAgreeId(agreeVersion.getAgreeId());
            } else {
                agreeVersion.setDisableTime(new Date());
            }
            baseMapper.updateById(agreeVersion);
        }
        return true;
    }

    @Override
    public SohuAgreeVersionVo queryByAgreeId(Long agreeId) {
        LambdaQueryWrapper<SohuAgreeVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuAgreeVersion::getState, CommonState.OnShelf.getCode());
        wrapper.eq(SohuAgreeVersion::getAgreeId, agreeId);
        return baseMapper.selectVoOne(wrapper);
    }

    private Boolean updateStateByAgreeId(Long agreeId) {
        LambdaQueryWrapper<SohuAgreeVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuAgreeVersion::getState, CommonState.OnShelf.getCode());
        wrapper.eq(SohuAgreeVersion::getAgreeId, agreeId);
        SohuAgreeVersion sohuAgreeVersion = baseMapper.selectOne(wrapper);
        if (Objects.nonNull(sohuAgreeVersion)) {
            sohuAgreeVersion.setState(CommonState.OffShelf.getCode());
            sohuAgreeVersion.setDisableTime(new Date());
            return baseMapper.updateById(sohuAgreeVersion) > 0;
        }
        return false;
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuAgreeVersion entity) {
        //TODO 做一些数据校验,如唯一约束
        entity.setUpdateTime(new Date());
        entity.setUserId(LoginHelper.getUserId());
    }

}
