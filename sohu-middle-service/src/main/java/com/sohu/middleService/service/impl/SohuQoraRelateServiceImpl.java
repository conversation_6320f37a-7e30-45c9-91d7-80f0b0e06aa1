package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.mybatis.db.CacheMgr;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuQoraRelateBo;
import com.sohu.middle.api.vo.SohuQoraRelateVo;
import com.sohu.middleService.domain.SohuQoraRelate;
import com.sohu.middleService.mapper.SohuQoraRelateMapper;
import com.sohu.middleService.service.ISohuQoraRelateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 问题关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@RequiredArgsConstructor
@Service
public class SohuQoraRelateServiceImpl extends SohuBaseServiceImpl<SohuQoraRelateMapper, SohuQoraRelate, SohuQoraRelateVo> implements ISohuQoraRelateService {

//    /**
//     * 查询问题关联
//     */
//    @Override
//    public SohuQoraRelateVo queryById(Long id) {
//        return baseMapper.selectVoById(id);
//    }

    /**
     * 查询问题关联列表
     */
    @Override
    public TableDataInfo<SohuQoraRelateVo> queryPageList(SohuQoraRelateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuQoraRelate> lqw = buildQueryWrapper(bo);
        Page<SohuQoraRelateVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询问题关联列表
     */
    @Override
    public List<SohuQoraRelateVo> queryList(SohuQoraRelateBo bo) {
        LambdaQueryWrapper<SohuQoraRelate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuQoraRelate> buildQueryWrapper(SohuQoraRelateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuQoraRelate> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getQoraId() != null, SohuQoraRelate::getQoraId, bo.getQoraId());
        lqw.eq(StringUtils.isNotBlank(bo.getQoraType()), SohuQoraRelate::getQoraType, bo.getQoraType());
        return lqw;
    }

    /**
     * 新增问题关联
     */
    @Override
    public Boolean insertByBo(SohuQoraRelateBo bo) {
        SohuQoraRelate add = BeanUtil.toBean(bo, SohuQoraRelate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改问题关联
     */
    @Override
    public Boolean updateByBo(SohuQoraRelateBo bo) {
        SohuQoraRelate update = BeanUtil.toBean(bo, SohuQoraRelate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuQoraRelate entity) {
        // TODO 做一些数据校验,如唯一约束
    }

//    /**
//     * 批量删除问题关联
//     */
//    @Override
//    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        if (isValid) {
//            // TODO 做一些业务上的校验,判断是否需要校验
//        }
//        return baseMapper.deleteBatchIds(ids) > 0;
//    }

    @Override
    public SohuQoraRelate getByQora(Long qoraId, String type) {
        /*if (Constants.cacheOff) {
            LambdaQueryWrapper<SohuQoraRelate> lqw = new LambdaQueryWrapper<>();
            lqw.eq(SohuQoraRelate::getQoraType, type).eq(SohuQoraRelate::getQoraId, qoraId);
            return this.baseMapper.selectOne(lqw);
        }
        BaseEntity.Cache cache = SohuQoraRelate.class.getAnnotation(BaseEntity.Cache.class);
        String key = type + ":" + qoraId;
        if (CacheMgr.exists(cache.region(), key)) {
            return (SohuQoraRelate) CacheMgr.get(cache.region(), key);
        }*/
        LambdaQueryWrapper<SohuQoraRelate> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuQoraRelate::getQoraType, type).eq(SohuQoraRelate::getQoraId, qoraId);
        SohuQoraRelate relate = this.baseMapper.selectOne(lqw);
        //CacheMgr.set(cache.region(), key, relate);
        return relate;
    }

    @Override
    public void deleteByQuestionIds(Collection<Long> questionIds) {
        this.baseMapper.delete(SohuQoraRelate::getQoraType, BusyType.Question.name(), SohuQoraRelate::getQoraId, questionIds);
    }
}
