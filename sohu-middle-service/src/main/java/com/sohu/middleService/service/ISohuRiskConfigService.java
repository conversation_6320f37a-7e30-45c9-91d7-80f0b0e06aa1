package com.sohu.middleService.service;


import com.sohu.middle.api.bo.risk.SohuRiskConfigBo;
import com.sohu.middle.api.vo.risk.SohuRiskConfigVo;

/**
 * 风控检测配置Service接口
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface ISohuRiskConfigService {

    /**
     * 查询风控检测配置
     * @param bo
     * @return
     */
    SohuRiskConfigVo queryByBo(SohuRiskConfigBo  bo);

    /**
     * 基于主键id查询检测配置
     *
     * @param id
     * @return
     */
    SohuRiskConfigVo queryById(Long id);
}
