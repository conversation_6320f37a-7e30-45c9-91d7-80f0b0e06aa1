package com.sohu.middleService.service.risk.impl;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.middle.api.bo.risk.SohuRiskRecordRelateBo;
import com.sohu.middle.api.vo.risk.SohuRiskRecordRelateVo;
import com.sohu.middleService.domain.risk.SohuRiskRecordRelate;
import com.sohu.middleService.mapper.risk.SohuRiskRecordRelateMapper;
import com.sohu.middleService.service.risk.ISohuRiskRecordRelateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 风控记录检测Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@RequiredArgsConstructor
@Service
public class SohuRiskRecordRelateServiceImpl implements ISohuRiskRecordRelateService {

    private final SohuRiskRecordRelateMapper baseMapper;

    @Override
    public Boolean insertByBo(SohuRiskRecordRelateBo bo) {
        SohuRiskRecordRelate add = BeanUtil.toBean(bo, SohuRiskRecordRelate.class);
        boolean flag = baseMapper.insert(add)>0;
        if (flag){
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(SohuRiskRecordRelateBo sohuRiskRecordRelateBo) {
        SohuRiskRecordRelate update = BeanUtil.toBean(sohuRiskRecordRelateBo, SohuRiskRecordRelate.class);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean insertBatch(List<SohuRiskRecordRelateBo> sohuRiskRecordRelateBoList) {
        List<SohuRiskRecordRelate> sohuRiskRecordRelateList = BeanUtil.copyToList(sohuRiskRecordRelateBoList, SohuRiskRecordRelate.class);
        return baseMapper.insertBatch(sohuRiskRecordRelateList);
    }

    @Override
    public List<SohuRiskRecordRelateVo> queryListByRecordId(Long recordId) {
        return baseMapper.selectVoList(Wrappers.<SohuRiskRecordRelate>lambdaQuery()
                .eq(SohuRiskRecordRelate::getRiskRecordId, recordId));
    }

    @Override
    public SohuRiskRecordRelateVo queryByDataId(String dataId) {
        return baseMapper.selectVoOne(Wrappers.<SohuRiskRecordRelate>lambdaQuery()
                .eq(SohuRiskRecordRelate::getDataId, dataId));
    }
}
