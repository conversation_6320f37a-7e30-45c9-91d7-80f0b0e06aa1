package com.sohu.middleService.service;


import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuUserBehaviorRecordBo;
import com.sohu.middle.api.bo.SohuUserBehaviorRecordPointBo;
import com.sohu.middle.api.bo.UserBehaviorRecordBo;
import com.sohu.middle.api.vo.SohuUserBehaviorRecordVo;
import com.sohu.middle.api.vo.UserBehaviorGroupInfoVo;
import com.sohu.middle.api.vo.behavior.UserBehaviorInfoVo;
import com.sohu.middle.api.vo.behavior.UserBehaviorRecordVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 用户行为记录Service接口
 *
 * <AUTHOR>
 * @date 2025-02-25
 */
public interface ISohuUserBehaviorRecordService {

    /**
     * 查询用户行为记录
     */
    SohuUserBehaviorRecordVo queryById(Long id);

    /**
     * 查询用户行为记录列表
     */
    TableDataInfo<SohuUserBehaviorRecordVo> queryPageList(SohuUserBehaviorRecordBo bo, PageQuery pageQuery);

    /**
     * 查询用户行为记录列表
     */
    List<SohuUserBehaviorRecordVo> queryList(SohuUserBehaviorRecordBo bo);

    /**
     * 修改用户行为记录
     */
    Boolean insertByBo(SohuUserBehaviorRecordBo bo);

    /**
     * 修改用户行为记录
     */
    Boolean updateByBo(SohuUserBehaviorRecordBo bo);

    /**
     * 校验并批量删除用户行为记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询用户行为列表
     *
     * @param bo
     * @return
     */
    List<UserBehaviorRecordVo> list(UserBehaviorRecordBo bo);

    /**
     * 查询用户行为明细
     *
     * @param id
     * @return
     */
    UserBehaviorInfoVo getInfoById(Long id);

    /**
     * 批量插入用户行为记录
     *
     * @param list
     * @return
     */
    Boolean addList(List<SohuUserBehaviorRecordPointBo> list);

    /**
     * 更新苹果设备的型号信息
     */
    void updateAppleDeviceModelInfo();

    /**
     * 用户行为分组
     *
     * @param sourceType
     * @param eventSigns
     * @return
     */
    List<UserBehaviorGroupInfoVo> groupList(String sourceType, List<String> eventSigns, Date startTime, Date endTime);
}
