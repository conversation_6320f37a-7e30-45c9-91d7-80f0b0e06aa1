package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuUserBehaviorRecordBo;
import com.sohu.middle.api.bo.SohuUserBehaviorRecordPointBo;
import com.sohu.middle.api.enums.behavior.BehaviorArticleEnum;
import com.sohu.middle.api.enums.behavior.BehaviorBusinessTypeEnum;
import com.sohu.middle.api.enums.behavior.BehaviorQuestionEnum;
import com.sohu.middle.api.enums.behavior.BehaviorVideoEnum;
import com.sohu.middle.api.bo.UserBehaviorRecordBo;
import com.sohu.middle.api.service.RemoteMiddleCategoryService;
import com.sohu.middle.api.vo.SohuCategoryVo;
import com.sohu.middle.api.vo.SohuUserBehaviorRecordVo;
import com.sohu.middle.api.vo.behavior.UserBehaviorInfoVo;
import com.sohu.middle.api.vo.behavior.UserBehaviorRecordVo;
import com.sohu.middleService.domain.SohuIndustryCategory;
import com.sohu.middleService.domain.SohuModelConfig;
import com.sohu.middleService.domain.SohuUserBehaviorRecord;
import com.sohu.middleService.mapper.SohuIndustryCategoryMapper;
import com.sohu.middleService.mapper.SohuModelConfigMapper;
import com.sohu.middleService.mapper.SohuUserBehaviorRecordMapper;
import com.sohu.middleService.service.ISohuCategoryService;
import com.sohu.middleService.service.ISohuUserBehaviorRecordService;
import com.sohu.middleService.utils.BehaviorUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户行为记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-25
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuUserBehaviorRecordServiceImpl implements ISohuUserBehaviorRecordService {

    private final SohuUserBehaviorRecordMapper baseMapper;
    private final SohuModelConfigMapper sohuModelConfigMapper;
    
    private final SohuIndustryCategoryMapper sohuIndustryCategoryMapper;
    @Autowired
    private BehaviorUtils behaviorUtils;

    /**
     * 查询用户行为记录
     */
    @Override
    public SohuUserBehaviorRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询用户行为记录列表
     */
    @Override
    public TableDataInfo<SohuUserBehaviorRecordVo> queryPageList(SohuUserBehaviorRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuUserBehaviorRecord> lqw = buildQueryWrapper(bo);
        Page<SohuUserBehaviorRecordVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询用户行为记录列表
     */
    @Override
    public List<SohuUserBehaviorRecordVo> queryList(SohuUserBehaviorRecordBo bo) {
        LambdaQueryWrapper<SohuUserBehaviorRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuUserBehaviorRecord> buildQueryWrapper(SohuUserBehaviorRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuUserBehaviorRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuUserBehaviorRecord::getUserId, bo.getUserId());
        lqw.eq(bo.getOperaSource() != null, SohuUserBehaviorRecord::getOperaSource, bo.getOperaSource());
        lqw.eq(StringUtils.isNotBlank(bo.getRequestMethod()), SohuUserBehaviorRecord::getRequestMethod, bo.getRequestMethod());
        lqw.eq(StringUtils.isNotBlank(bo.getOperUrl()), SohuUserBehaviorRecord::getOperUrl, bo.getOperUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getOperParam()), SohuUserBehaviorRecord::getOperParam, bo.getOperParam());
        lqw.eq(StringUtils.isNotBlank(bo.getOperIp()), SohuUserBehaviorRecord::getOperIp, bo.getOperIp());
        lqw.eq(StringUtils.isNotBlank(bo.getOperResult()), SohuUserBehaviorRecord::getOperResult, bo.getOperResult());
        lqw.eq(StringUtils.isNotBlank(bo.getRequestId()), SohuUserBehaviorRecord::getRequestId, bo.getRequestId());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessType()), SohuUserBehaviorRecord::getBusinessType, bo.getBusinessType());
        lqw.eq(bo.getOperaType() != null, SohuUserBehaviorRecord::getOperaType, bo.getOperaType());
        return lqw;
    }

    /**
     * 新增用户行为记录
     */
    @Override
    public Boolean insertByBo(SohuUserBehaviorRecordBo bo) {
//        behaviorUtils.setRecordBo(bo);
        if (bo.getOperaType() == null) {
            return true;
        }
        SohuUserBehaviorRecord add = BeanUtil.toBean(bo, SohuUserBehaviorRecord.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户行为记录
     */
    @Override
    public Boolean updateByBo(SohuUserBehaviorRecordBo bo) {
        SohuUserBehaviorRecord update = BeanUtil.toBean(bo, SohuUserBehaviorRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuUserBehaviorRecord entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除用户行为记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<UserBehaviorRecordVo> list(UserBehaviorRecordBo bo) {
        return baseMapper.list(bo);
    }

    @Override
    public UserBehaviorInfoVo getInfoById(Long id) {
        SohuUserBehaviorRecord behaviorRecord = baseMapper.selectById(id);
        if (Objects.isNull(behaviorRecord)) {
            throw new ServiceException("行为数据不存在");
        }
        UserBehaviorInfoVo info = BeanCopyUtils.copy(behaviorRecord, UserBehaviorInfoVo.class);
        // 基于类型查询处理明细
        String result = behaviorUtils.getEnumResultByInfo(behaviorRecord);
        info.setOperResult(result);
        return info;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addList(List<SohuUserBehaviorRecordPointBo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Boolean.TRUE;
        }
        List<SohuUserBehaviorRecord> records = new ArrayList<>();
        for (SohuUserBehaviorRecordPointBo bo : list
        ) {
            if (getInfoByRequestId(bo.getRequestId())) {
                continue;
            }
            // sd_select_trade事件需封装一级行业名称
            if ("sd_select_trade".equals(bo.getElementNo()) && StrUtil.isNotEmpty(bo.getEventAttribute().getContentRelationNo())){
                SohuIndustryCategory sohuCategoryVo = sohuIndustryCategoryMapper.selectById(Long.parseLong(bo.getEventAttribute().getContentRelationNo()));
                bo.getEventAttribute().setContentRelationName(Objects.nonNull(sohuCategoryVo)?sohuCategoryVo.getIndustryName():"");
            }
            SohuUserBehaviorRecord record = BeanCopyUtils.copy(bo, SohuUserBehaviorRecord.class);
            if (StrUtil.isEmpty(record.getOperResult())) {
                record.setOperResult(JSONUtil.toJsonStr(bo.getEventAttribute()));
            }
            records.add(record);
        }
        return baseMapper.insertBatch(records);
    }

    /**
     * 判断是否存在
     *
     * @param requestId
     * @return
     */
    private Boolean getInfoByRequestId(String requestId) {
        return baseMapper.selectCount(Wrappers.lambdaQuery(SohuUserBehaviorRecord.class)
                .eq(SohuUserBehaviorRecord::getRequestId, requestId)) > 0;
    }

    /**
     * 更新苹果设备的型号信息
     */
    public void updateAppleDeviceModelInfo() {
        // 获取苹果所有机型配置信息
        List<SohuModelConfig> sohuModelConfigList = sohuModelConfigMapper.selectList(Wrappers.lambdaQuery(SohuModelConfig.class)
                .eq(SohuModelConfig::getDelFlag, Constants.ZERO)
                .eq(SohuModelConfig::getType, Constants.ONE));

        if (sohuModelConfigList.isEmpty()) {
            log.info("没有找到苹果设备配置信息，跳过更新");
            return;
        }

        // 转换成map - 使用流操作优化
        Map<String, String> modelMap = sohuModelConfigList.stream()
                .collect(Collectors.toMap(
                        SohuModelConfig::getDeviceUnitNo,
                        SohuModelConfig::getModelNo,
                        // 处理可能的键冲突
                        (existing, replacement) -> existing
                ));

        // 批量获取需要更新的记录
        List<SohuUserBehaviorRecord> recordsToUpdate = baseMapper.selectList(Wrappers.lambdaQuery(SohuUserBehaviorRecord.class)
                .eq(SohuUserBehaviorRecord::getModelNo, "")
                .eq(SohuUserBehaviorRecord::getOperaSource, Constants.THRID));

        if (recordsToUpdate.isEmpty()) {
            log.info("没有需要更新的记录");
            return;
        }

        log.info("找到{}条需要更新modelNo的记录", recordsToUpdate.size());

        // 批量更新
        List<SohuUserBehaviorRecord> batchList = new ArrayList<>();

        for (SohuUserBehaviorRecord record : recordsToUpdate) {
            String modelNo = modelMap.get(record.getDeviceUnitNo());
            if (StringUtils.isNotBlank(modelNo)) {
                record.setModelNo(modelNo);
                batchList.add(record);
            }
        }

        log.info("共有{}条记录的modelNo要被更新", batchList.size());

        // 循环结束后，统一执行批量更新
        int updateCount = 0;
        if (!batchList.isEmpty()) {
            List<List<SohuUserBehaviorRecord>> partitionList = Lists.partition(batchList, 500);
            for (List<SohuUserBehaviorRecord> updateList : partitionList) {
                updateBatchRecords(updateList);
                updateCount += updateList.size();
            }
        }

        log.info("成功更新了{}条记录的modelNo", updateCount);

    }

    /**
     * 批量更新记录
     *
     * @param recordList 需要更新的记录列表
     */
    private void updateBatchRecords(List<SohuUserBehaviorRecord> recordList) {
        if (recordList.isEmpty()) {
            return;
        }

        try {
            // 批量更新
            baseMapper.updateBatchById(recordList);
        } catch (Exception e) {
            log.error("批量更新modelNo失败:{}", e.getMessage());
        }
    }
}
