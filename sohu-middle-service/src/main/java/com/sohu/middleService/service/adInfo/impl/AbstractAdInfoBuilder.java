package com.sohu.middleService.service.adInfo.impl;

import com.sohu.common.core.enums.BusyType;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.vo.SohuAdInfoVo;
import com.sohu.middle.api.vo.SohuVideoVo;
import com.sohu.middleService.service.ISohuUserLikeService;
import com.sohu.middleService.service.ISohuUserService;
import com.sohu.middleService.service.ISohuVideoService;
import com.sohu.middleService.service.adInfo.AdInfoBuilder;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

/**
 * 广告信息构建公共方法
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public abstract class AbstractAdInfoBuilder implements AdInfoBuilder {

    protected final ISohuVideoService iSohuVideoService;
    protected final ISohuUserService iSohuUserService;
    protected final ISohuUserLikeService iSohuUserLikeService;

    @Override
    public void extendAdInfo(SohuAdInfoVo adInfoVo) {
        SohuVideoVo videoVo = iSohuVideoService.queryById(adInfoVo.getVideoId());

        if (Objects.nonNull(videoVo)) {
            Long videoLikeCount = iSohuUserLikeService.countStatById(BusyType.Video.name(), videoVo.getId());
            LoginUser author = iSohuUserService.selectById(videoVo.getUserId());
            adInfoVo.setVideoNickName(author.getNickname());
            adInfoVo.setVideoAvatar(author.getAvatar());
            adInfoVo.setVideoPraiseCount(videoLikeCount == null ? 0 : videoLikeCount.intValue());
            if (Objects.nonNull(LoginHelper.getUserId())) {
                adInfoVo.setVideoPraiseObj(iSohuUserLikeService.queryByUserId(LoginHelper.getUserId(), BusyType.Video.name(), videoVo.getId()));
            }

            adInfoVo.setVideoCoverImage(videoVo.getCoverImage());
            adInfoVo.setVideoUrl(videoVo.getVideoUrl());
            adInfoVo.setVideoUserId(videoVo.getUserId());
            adInfoVo.setVideoForwardCount(videoVo.getForwardCount());
            adInfoVo.setVideoCollectCount(videoVo.getCollectCount());
            adInfoVo.setVideoCollectObj(videoVo.getCollectObj());
            adInfoVo.setVideoFollow0bj(videoVo.getFollowObj());
        }
    }
}