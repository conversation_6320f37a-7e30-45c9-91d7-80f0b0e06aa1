package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.lang.tree.parser.NodeParser;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.UserConstants;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.utils.TreeBuildUtils;
import com.sohu.common.core.utils.reflect.ReflectUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.entry.api.RemoteEntryService;
import com.sohu.middle.api.bo.SohuIndustryCategoryBo;
import com.sohu.middle.api.vo.SohuIndustryCategoryVo;
import com.sohu.middleService.domain.SohuIndustryCategory;
import com.sohu.middleService.mapper.SohuCategoryMapper;
import com.sohu.middleService.mapper.SohuIndustryCategoryMapper;
import com.sohu.middleService.mapper.SohuSiteMapper;
import com.sohu.middleService.service.ISohuIndustryCategoryService;
import com.sohu.middleService.service.ISohuPlatformIndustryRelationService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 行业分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-29
 */
@RequiredArgsConstructor
@Service
public class SohuIndustryCategoryServiceImpl extends SohuBaseServiceImpl<SohuIndustryCategoryMapper, SohuIndustryCategory, SohuIndustryCategoryVo> implements ISohuIndustryCategoryService {

    //private final SohuIndustryCategoryMapper baseMapper;
    private final SohuSiteMapper sohuSiteMapper;
    private final SohuCategoryMapper sohuCategoryMapper;
    private final ISohuPlatformIndustryRelationService relationService;

    @DubboReference
    private RemoteEntryService remoteEntryService;

    /**
     * 查询行业分类
     */
    @Override
    public SohuIndustryCategoryVo queryById(Long id) {
        SohuIndustryCategoryVo categoryVo = baseMapper.selectVoById(id);
        if (Objects.isNull(categoryVo)) {
            return null;
        }
        categoryVo.setEntryProfile(remoteEntryService.getExtEntryProfile(id));
        return categoryVo;
    }

    /**
     * 查询行业分类列表
     */
    @Override
    public TableDataInfo<SohuIndustryCategoryVo> queryPageList(SohuIndustryCategoryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuIndustryCategory> lqw = buildQueryWrapper(bo);
        Page<SohuIndustryCategoryVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询行业分类列表
     */
    @Override
    public List<SohuIndustryCategoryVo> queryList(SohuIndustryCategoryBo bo) {
        LambdaQueryWrapper<SohuIndustryCategory> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuIndustryCategory> buildQueryWrapper(SohuIndustryCategoryBo bo) {
        //Map<String, Object> params = bo.getParams();
        //Long siteId = bo.getSiteId();
        LambdaQueryWrapper<SohuIndustryCategory> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPid() != null, SohuIndustryCategory::getPid, bo.getPid());
        //SohuSite site = sohuSiteMapper.selectById(siteId);
//        if (Objects.nonNull(site)) {
//            if (StrUtil.equalsAnyIgnoreCase(site.getType(), SiteType.City.name())) {
//                lqw.eq(true, SohuIndustryCategory::getSiteId, site.getPid());
//            } else {
//                lqw.eq(true, SohuIndustryCategory::getSiteId, siteId);
//            }
//        }
        lqw.like(StringUtils.isNotBlank(bo.getIndustryName()), SohuIndustryCategory::getIndustryName, bo.getIndustryName());
        return lqw;
    }

    /**
     * 新增行业分类
     */
    @Override
    public Boolean insertByBo(SohuIndustryCategoryBo bo) {
        SohuIndustryCategory add = BeanUtil.toBean(bo, SohuIndustryCategory.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            remoteEntryService.saveEntryProfile(add.getId(), bo.getEntryProfile());
//            允许入驻资质为空
//            if (StrUtil.isNotBlank(bo.getEntryProfile())) {
//            }
        }
        return flag;
    }

    /**
     * 修改行业分类
     */
    @Override
    public Boolean updateByBo(SohuIndustryCategoryBo bo) {
        SohuIndustryCategory update = BeanUtil.toBean(bo, SohuIndustryCategory.class);
        validEntityBeforeSave(update);
//        Long categoryId = bo.getCategoryId();
//        if (categoryId != null && categoryId > 0L) {
//            SohuCategory sohuCategory = sohuCategoryMapper.selectById(categoryId);
//            if (Objects.isNull(sohuCategory)) {
//                throw new RuntimeException(MessageUtils.message("category.not.exist"));
//            }
        // 无需校验
//            SohuIndustryCategory industryCategory = this.baseMapper.selectOne(SohuIndustryCategory::getCategoryId, categoryId);
//            if (Objects.nonNull(industryCategory) && !Objects.equals(industryCategory.getId(), bo.getId())) {
//                throw new RuntimeException(MessageUtils.message("category.already.already"));
//            }
//        }
//        允许入驻资质为空
//        if (StrUtil.isNotBlank(bo.getEntryProfile())) {
//        }
        remoteEntryService.updateEntryProfile(update.getId(), bo.getEntryProfile());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuIndustryCategory entity) {
        //TODO 做一些数据校验,如唯一约束
        if (StringUtils.isNotBlank(entity.getRoleKeysStr())) {
            String[] roleList = entity.getRoleKeysStr().split(",");
            if (Arrays.stream(roleList).anyMatch(p -> p.equals(UserConstants.ADMIN_ROLE_KEY))) {
                throw new ServiceException("管理员角色不能认证");
            }
        }

        LambdaQueryWrapper<SohuIndustryCategory> lqw = Wrappers.lambdaQuery();
        lqw.select(SohuIndustryCategory::getId);
        lqw.eq(SohuIndustryCategory::getPid, entity.getPid());
        lqw.eq(SohuIndustryCategory::getIndustryName, entity.getIndustryName());
        lqw.last(" limit 1");
        SohuIndustryCategory industryCategory = this.baseMapper.selectOne(lqw);

        if (ObjectUtil.isNotNull(industryCategory) && (!industryCategory.getId().equals(entity.getId()))) {
            throw new RuntimeException("分类名称已存在");
        }
//        Long categoryId = entity.getCategoryId();
//        if (categoryId != null) {
//            SohuCategory sohuCategory = sohuCategoryMapper.selectById(categoryId);
//            if (Objects.isNull(sohuCategory)) {
//                throw new RuntimeException(MessageUtils.message("category.not.exist"));
//            }
//        }
    }

    /**
     * 批量删除行业分类
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid && CollUtil.isNotEmpty(ids)) {
            for (Long id : ids) {
                SohuIndustryCategory industryCategory = this.baseMapper.selectById(id);
                if (ObjectUtil.isNull(industryCategory)) {
                    throw new RuntimeException("分类不存在");
                }
                validEntityBeforeDelete(industryCategory);
                // 删除入驻行业资料
                remoteEntryService.deleteByIndustryId(id);
            }
        }

        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 删除前的数据校验
     *
     * @param industryCategory
     */
    private void validEntityBeforeDelete(SohuIndustryCategory industryCategory) {
        // TODO 做一些业务上的校验
        // 判断是否存在子行业
        LambdaQueryWrapper<SohuIndustryCategory> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuIndustryCategory::getPid, industryCategory.getId());
        if (this.baseMapper.selectCount(lqw) > 0) {
            throw new RuntimeException("请先删除子行业分类");
        }

        // 判断行业分类是否被引用
        if (remoteEntryService.queryByIndustryId(industryCategory.getId()) > 0) {
            throw new RuntimeException("有行业已被认证引用,无法删除");
        }
    }

    @Override
    public List<Tree<Long>> getAllTrees(Long platformIndustryId) {
        List<SohuIndustryCategory> all = this.baseMapper.selectList();
        if (CollUtil.isEmpty(all)) {
            return new ArrayList<>();
        }
        List<Long> categoryIds = new ArrayList<>();
        if (Objects.nonNull(platformIndustryId)) {
            // 查询行业对应的分类
            categoryIds = relationService.queryByIndustryIdAndBusyType(platformIndustryId, BusyType.BusyTask.getType());
        }
        // 2. 如果没有 categoryIds，直接构建完整树
        if (CollUtil.isEmpty(categoryIds)) {
            List treeNodeList = all.stream().map(category ->
                    new TreeNode(category.getId(), category.getPid(), category.getIndustryName(), null)).collect(Collectors.toList());
            return TreeBuildUtils.build(treeNodeList, 0L);
        }
        // 3. 从 all 中筛选出 categoryIds 及其所有父节点
        List<SohuIndustryCategory> filteredCategories = filterCategoriesWithParents(all, categoryIds);
        List treeNodeList = filteredCategories.stream().map(category ->
                new TreeNode(category.getId(), category.getPid(), category.getIndustryName(), null)).collect(Collectors.toList());
        return TreeBuildUtils.build(treeNodeList, 0L);
    }

    /**
     * 筛选出目标节点及其所有父节点
     */
    private List<SohuIndustryCategory> filterCategoriesWithParents(List<SohuIndustryCategory> all, List<Long> targetIds) {
        Set<Long> processedIds = new HashSet<>(); // 去重
        Queue<Long> queue = new LinkedList<>(targetIds); // 待处理的节点ID
        List<SohuIndustryCategory> result = new ArrayList<>();
        while (!queue.isEmpty()) {
            Long currentId = queue.poll();
            if (processedIds.contains(currentId)) {
                continue; // 已处理过，跳过
            }
            // 查找当前ID对应的节点
            all.stream()
                    .filter(c -> currentId.equals(c.getId()))
                    .findFirst()
                    .ifPresent(c -> {
                        result.add(c); // 添加到结果
                        processedIds.add(currentId); // 标记为已处理

                        // 如果父节点未处理，加入队列
                        if (c.getPid() != null && c.getPid() != 0L && !processedIds.contains(c.getPid())) {
                            queue.add(c.getPid());
                        }
                    });
        }
        return result;
    }


    @Override
    public List<SohuIndustryCategoryVo> tree(SohuIndustryCategoryBo bo) {
        List<SohuIndustryCategoryVo> list = this.queryList(bo);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        if (bo.getPid() != null && bo.getPid() > 0L) {
            return list;
        }
        return this.buildTree(list, 0L, 1);
    }

    @Override
    public List<SohuIndustryCategoryVo> tree() {
        List<SohuIndustryCategoryVo> voList = this.baseMapper.selectVoList(new QueryWrapper<>());
        return this.buildTree(voList, 0L, 1);
    }

    public List<SohuIndustryCategoryVo> buildTree(List<SohuIndustryCategoryVo> list, Long parentId, int level) {
        List<SohuIndustryCategoryVo> tree = new ArrayList<>();
        for (SohuIndustryCategoryVo category : list) {
            if (Objects.equals(category.getPid(), parentId)) {
                category.setChildren(buildTree(list, category.getId(), level + 1));
                tree.add(category);
            }
        }
        return tree;
    }

    @Override
    public Map<Long, SohuIndustryCategoryVo> queryMap(Collection<Long> ids) {
        LambdaQueryWrapper<SohuIndustryCategory> lqw = Wrappers.lambdaQuery();
        lqw.in(SohuIndustryCategory::getId, ids);
        List<SohuIndustryCategoryVo> list = baseMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(SohuIndustryCategoryVo::getId, u -> u));
    }

    @Override
    public List<SohuIndustryCategoryVo> getEntryTreeOfInclude(Collection<Long> industryIds) {
        LambdaQueryWrapper<SohuIndustryCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.in(SohuIndustryCategory::getId, industryIds);
        List<SohuIndustryCategoryVo> sohuIndustryCategoryVoList = baseMapper.selectVoList(wrapper);
        return this.buildTree(sohuIndustryCategoryVoList, 0L, 1);
    }

    @Override
    public List<SohuIndustryCategoryVo> getEntryTreeOfExclude(Collection<Long> industryIds) {
        List<SohuIndustryCategoryVo> voList = this.baseMapper.selectVoList(new QueryWrapper<>());
        if (CollUtil.isNotEmpty(industryIds)) {
            Set<Long> idSet = industryIds.stream().collect(Collectors.toSet());
            voList = voList.stream().filter(p -> !idSet.contains(p.getId())).collect(Collectors.toList());
        }
        return this.buildTree(voList, 0L, 1);
    }

    @Override
    public List<String> getUserEntryAuth(List<Long> industryIds) {
        LambdaQueryWrapper<SohuIndustryCategory> wrapper = Wrappers.lambdaQuery();
        wrapper.select(SohuIndustryCategory::getId, SohuIndustryCategory::getIndustryName);
        wrapper.in(SohuIndustryCategory::getId, industryIds);
        String ids = industryIds.stream()
                .map(Object::toString)
                .collect(Collectors.joining(", "));
        wrapper.last("ORDER BY FIELD(ID," + ids + ")");
        List<SohuIndustryCategoryVo> list = baseMapper.selectVoList(wrapper);
        return list.stream().map(SohuIndustryCategoryVo::getIndustryName).collect(Collectors.toList());
    }

    @Override
    public SohuIndustryCategoryVo queryRootById(Long childId) {
        SohuIndustryCategoryVo vo = this.baseMapper.selectVoById(childId);
        if (Objects.isNull(vo)) {
            return null;
        }
        if (Objects.equals(vo.getPid(), 0L)) {
            return vo;
        }
        return this.queryRootById(vo.getPid());
    }

    @Override
    public SohuIndustryCategoryVo queryByName(String industryName) {
        SohuIndustryCategoryVo vo = this.baseMapper.selectVoOne(Wrappers.<SohuIndustryCategory>lambdaQuery()
                .eq(SohuIndustryCategory::getIndustryName, industryName));
        return vo;
    }

    @Override
    public List<SohuIndustryCategoryVo> queryListByIds(List<Long> ids) {
        return baseMapper.selectVoList(Wrappers.<SohuIndustryCategory>lambdaQuery()
                .in(SohuIndustryCategory::getId, ids));
    }

}
