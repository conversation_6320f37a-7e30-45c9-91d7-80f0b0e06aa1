package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuVideoRelateBo;
import com.sohu.middle.api.vo.RelationRespVo;
import com.sohu.middle.api.vo.SohuVideoRelateVo;
import com.sohu.middleService.domain.SohuVideoRelate;
import com.sohu.middleService.mapper.SohuVideoRelateMapper;
import com.sohu.middleService.service.ISohuVideoRelateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 视频与关联项的关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@RequiredArgsConstructor
@Service
public class SohuVideoRelateServiceImpl extends SohuBaseServiceImpl<SohuVideoRelateMapper, SohuVideoRelate, SohuVideoRelateVo> implements ISohuVideoRelateService {

//    /**
//     * 查询视频与关联项的关联
//     */
//    @Override
//    public SohuVideoRelateVo queryById(Long id) {
//        return baseMapper.selectVoById(id);
//    }

    /**
     * 查询视频与关联项的关联列表
     */
    @Override
    public TableDataInfo<SohuVideoRelateVo> queryPageList(SohuVideoRelateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuVideoRelate> lqw = buildQueryWrapper(bo);
        Page<SohuVideoRelateVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询视频与关联项的关联列表
     */
    @Override
    public List<SohuVideoRelateVo> queryList(SohuVideoRelateBo bo) {
        LambdaQueryWrapper<SohuVideoRelate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuVideoRelate> buildQueryWrapper(SohuVideoRelateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuVideoRelate> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getVideoId() != null, SohuVideoRelate::getVideoId, bo.getVideoId());
        lqw.eq(StringUtils.isNotBlank(bo.getBusyType()), SohuVideoRelate::getBusyType, bo.getBusyType());
        lqw.eq(bo.getBusyCode() != null, SohuVideoRelate::getBusyCode, bo.getBusyCode());
        return lqw;
    }

    /**
     * 新增视频与关联项的关联
     */
    @Override
    public Boolean insertByBo(SohuVideoRelateBo bo) {
        SohuVideoRelate add = BeanUtil.toBean(bo, SohuVideoRelate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改视频与关联项的关联
     */
    @Override
    public Boolean updateByBo(SohuVideoRelateBo bo) {
        SohuVideoRelate update = BeanUtil.toBean(bo, SohuVideoRelate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuVideoRelate entity) {
        // TODO 做一些数据校验,如唯一约束
    }

//    /**
//     * 批量删除视频与关联项的关联
//     */
//    @Override
//    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        if (isValid) {
//            // TODO 做一些业务上的校验,判断是否需要校验
//        }
//        return baseMapper.deleteBatchIds(ids) > 0;
//    }

    @Override
    public SohuVideoRelateVo getByVideoId(Long videoId) {
        /*if (Constants.cacheOff) {
            LambdaQueryWrapper<SohuVideoRelate> lqw = new LambdaQueryWrapper<>();
            lqw.eq(SohuVideoRelate::getVideoId, videoId);
            return this.baseMapper.selectOne(lqw);
        }
        BaseEntity.Cache cache = SohuVideoRelate.class.getAnnotation(BaseEntity.Cache.class);
        if (CacheMgr.exists(cache.region(), String.valueOf(videoId))) {
            return (SohuVideoRelate) CacheMgr.get(cache.region(), String.valueOf(videoId));
        }*/
        LambdaQueryWrapper<SohuVideoRelate> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuVideoRelate::getVideoId, videoId);
        SohuVideoRelateVo relate = this.baseMapper.selectVoOne(lqw);
        if (Objects.isNull(relate)) {
            return null;
        }
        //CacheMgr.set(cache.region(), String.valueOf(videoId), relate);
        return relate;
    }

    @Override
    public void deleteByVideoIds(Collection<Long> ids) {
        this.baseMapper.delete(SohuVideoRelate::getVideoId, ids);
    }

    @Override
    public List<SohuVideoRelateVo> queryListByVideoIds(List<Long> videoIds) {
        if (CollUtil.isEmpty(videoIds)) {
            return null;
        }
        LambdaQueryWrapper<SohuVideoRelate> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuVideoRelate::getVideoId, videoIds);
        return this.baseMapper.selectVoList(lqw);
    }

    @Override
    public Map<Long, SohuVideoRelateVo> queryMapByVideoIds(List<Long> videoIds) {
        List<SohuVideoRelateVo> list = this.queryListByVideoIds(videoIds);
        return CollUtil.isEmpty(list) ? new HashMap<>() : list.stream().collect(Collectors.toMap(SohuVideoRelateVo::getVideoId, item -> item));
    }

    @Override
    public RelationRespVo buildRelationInfo(SohuVideoRelateVo relate) {
        RelationRespVo relationRespVO = new RelationRespVo();
        relationRespVO.setBusyCode(relate.getBusyCode());
        relationRespVO.setBusyType(relate.getBusyType());
        relationRespVO.setBusyTitle(relate.getBusyTitle());
        relationRespVO.setBusyInfo(relate.getBusyInfo());
        return relationRespVO;
    }

}
