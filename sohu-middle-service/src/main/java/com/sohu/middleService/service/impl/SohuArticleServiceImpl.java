package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.RemoteBanRecordService;
import com.sohu.admin.api.bo.SohuBanRecordsBo;
import com.sohu.admin.api.vo.SohuBanRecordsVo;
import com.sohu.middle.api.bo.SohuTopicContentQueryBo;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.UserConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.entry.api.RemoteEntryService;
import com.sohu.im.api.noticeContent.NoticeContentDetail;
import com.sohu.im.api.noticeContent.NoticeSystemContent;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.airec.SohuAirecContentItemBo;
import com.sohu.middle.api.bo.mcn.SohuMcnArticleReqBo;
import com.sohu.middle.api.bo.notice.SohuInteractNoticeBo;
import com.sohu.middle.api.bo.risk.SohuRiskMqBo;
import com.sohu.middle.api.enums.*;
import com.sohu.middle.api.enums.report.ActionTypeEnum;
import com.sohu.middle.api.enums.report.RecreationReportEnum;
import com.sohu.middle.api.service.RemoteMiddleEventReportService;
import com.sohu.middle.api.service.notice.RemoteMiddleSystemNoticeService;
import com.sohu.middle.api.vo.*;
import com.sohu.middle.api.vo.airec.SohuAirecContentItemVo;
import com.sohu.middle.api.vo.mcn.SohuMcnUserVo;
import com.sohu.middleService.domain.*;
import com.sohu.middleService.domain.bo.SohuAirecArticleQueryBo;
import com.sohu.middleService.domain.mcn.SohuMcnUser;
import com.sohu.middleService.mapper.*;
import com.sohu.middleService.mapper.mcn.SohuMcnUserMapper;
import com.sohu.middleService.service.*;
import com.sohu.middleService.service.airec.ISohuAirecContentItemService;
import com.sohu.middleService.service.airec.ISohuAirecTagRelationService;
import com.sohu.middleService.service.mcn.ISohuArticleDetailService;
import com.sohu.resource.api.RemoteFileService;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.third.aliyun.airec.constants.AliyunAirecConstant;
import com.sohu.third.aliyun.airec.constants.AliyunAirecTagsConstant;
import com.sohu.third.aliyun.airec.domain.AliyunAirecJoinFilterRule;
import com.sohu.third.aliyun.airec.domain.AliyunAirecSingleFilterRule;
import com.sohu.third.aliyun.airec.enums.*;
import com.sohu.third.aliyun.airec.util.AliyunAirecUtil;
import com.sohu.third.aliyun.audit.constants.AliyunAuditLabelEnum;
import com.sohu.third.aliyun.audit.service.AliyunAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.text.StrPool.COMMA;

/**
 * 图文主体Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuArticleServiceImpl extends SohuBaseServiceImpl<SohuArticleMapper, SohuArticle, SohuArticleVo> implements ISohuArticleService {

    private final SohuArticleInfoMapper sohuArticleInfoMapper;
    private final SohuFriendsMapper sohuFriendsMapper;
    private final SohuMcnUserMapper sohuMcnUserMapper;
    private final SohuArticleRelateMapper sohuArticleRelateMapper;
    private final SohuContentMainMapper sohuContentMainMapper;

    private final ISohuContentMainService iSohuContentMainService;
    private final ISohuArticleInfoService iSohuArticleInfoService;
    private final ISohuAuditService iSohuAuditService;
    private final ISohuAuditRecordService iSohuAuditRecordService;
    private final ISohuAuditInitService sohuAuditInitService;
    private final ISohuArticleRelateService iSohuArticleRelateService;
    private final ISohuSyncContentService sohuSyncContentService;
    private final ISohuArticleDetailService sohuArticleDetailService;
    private final ISohuUserFollowService sohuUserFollowService;
    private final ISohuUserLikeService sohuUserLikeService;
    private final ISohuDislikeService iSohuDislikeService;
    private final ISohuLessonLabelService sohuLessonLabelService;
    private final ISohuCategoryService categoryService;
    private final ISohuSiteService siteService;
    private final ISohuAirecTagRelationService iSohuAirecTagRelationService;
    private final ISohuAirecContentItemService iSohuAirecContentItemService;
    private final ISohuInteractNoticeService sohuInteractNoticeService;
    private final ISohuUserService sohuUserService;
    private final ISohuVideoService sohuVideoService;
    private final ISohuContentLifecycleService sohuContentLifecycleService;
    private final ISohuBusyBlackService busyBlackService;
    private final AsyncConfig asyncConfig;

    @DubboReference
    private RemoteFileService remoteFileService;
    @DubboReference
    private RemoteEntryService remoteEntryService;
    @DubboReference
    private RemoteMiddleSystemNoticeService remoteMiddleSystemNoticeService;
    @DubboReference
    private RemoteMiddleEventReportService remoteMiddleEventReportService;
    private final ISohuPlatformIndustryRelationService relationService;
    @Resource
    private AliyunAuditService aliyunAuditService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    @DubboReference
    private RemoteBanRecordService remoteBanRecordService;

    private static final Long DEFAULT_STAT_VALUE = 0L;
    /**
     * 最大可提交次数，TODO 后期改为可配置参数
     */
    private static final Integer SUBMIT_NUM_MAX_VALUE = 3;
    /**
     * 回收站超时时间，单位天
     */
    private final static Integer CON_RECYCLE_DATA_TIME_OUT = 14;


    /**
     * 查询图文主体
     */
    @Override
    public SohuArticleVo selectVoById(Long id) {
        SohuArticleVo sohuArticleVo = baseMapper.selectVoById(id);
        validateArticleExists(sohuArticleVo);
        SohuArticleInfoVo infoVo = iSohuArticleInfoService.queryByArticleId(id);
        // 设置内容
        sohuArticleVo.setContent(Objects.nonNull(infoVo) ? infoVo.getContent() : null);
        return sohuArticleVo;
    }

    @Override
    public Long getAuthorId(Long id) {
        if (CalUtils.isNullOrZero(id)) {
            return 0L;
        }
        SohuArticleVo articleVo = this.get(id);
        return Objects.isNull(articleVo) ? 0L : articleVo.getUserId();
    }

    /**
     * 查询图文主体
     */
    @Override
    public SohuArticleVo queryById(Long id) {
        SohuArticleVo articleVo = baseMapper.selectVoById(id);
        validateArticleExists(articleVo);
        SohuArticleInfoVo infoVo = iSohuArticleInfoService.queryByArticleId(id);
        // 设置内容
        if (Objects.nonNull(infoVo)) {
            articleVo.setContent(infoVo.getContent());
            articleVo.setCollectCount(infoVo.getCollectCount());
            articleVo.setViewCount(infoVo.getViewCount());
            articleVo.setCommentCount(infoVo.getCommentCount());
            articleVo.setLearnNum(infoVo.getLearnNum());
            articleVo.setPraiseCount(infoVo.getPraiseCount());
            articleVo.setForwardCount(infoVo.getForwardCount());
            articleVo.setInfo(infoVo);
        }
        // 设置封面
        if (StrUtil.isNotBlank(articleVo.getCoverImage()) && NumberUtil.isNumber(articleVo.getCoverImage())) {
            String url = remoteFileService.getUrl(Long.valueOf(articleVo.getCoverImage()));
            articleVo.setCoverUrl(url);
            articleVo.setCoverImage(url);
        }
        // 浏览量和学习人数+1 只有审核通过的才+1
        if (StrUtil.equalsAnyIgnoreCase(articleVo.getState(), CommonState.OnShelf.getCode())) {
            sohuArticleInfoMapper.updateViewCount(id);
        }
        // 关联项设置
        buildRelate(articleVo);
        // 图文详情埋点,移动端已埋点
//        Long userId = LoginHelper.getUserId();
//        if (Objects.nonNull(userId)) {
//            articleVo.setEventId(buildArticleEventRecord(RecreationReportEnum.CKTW, userId));
//        }
        return articleVo;
    }

    /**
     * 图文埋点
     *
     * @param reportEnum 埋点枚举
     * @param userId     用户id
     * @return 事件id
     */
    public String buildArticleEventRecord(RecreationReportEnum reportEnum, Long userId) {
        SohuEventReportBo bo = new SohuEventReportBo(
                RecreationReportEnum.getCode(reportEnum.getType()),
                reportEnum.getDesc(),
                ActionTypeEnum.Recreation.name(),
                reportEnum.getType(),
                userId);

        return remoteMiddleEventReportService.getEventId(bo);
    }

    @Override
    public SohuArticleVo get(Long id) {
        return baseMapper.selectVoById(id);
    }

    @NotNull
    private static RelationRespVo buildRelationRespVo(SohuArticleRelateVo relate) {
        RelationRespVo relationRespVO = new RelationRespVo();
        relationRespVO.setBusyCode(relate.getBusyCode());
        relationRespVO.setBusyType(relate.getBusyType());
        relationRespVO.setBusyTitle(relate.getBusyTitle());
        relationRespVO.setBusyInfo(relate.getBusyInfo());
        relationRespVO.setChildTaskNumber(relate.getBusyInfo());
        return relationRespVO;
    }

    /**
     * 查询图文主体列表
     */
    @Override
    public TableDataInfo<SohuArticleVo> queryPageList(SohuArticleBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        // 超级管理员
        boolean isAdmin = LoginHelper.hasRole(loginUser, RoleCodeEnum.ADMIN);
        // 国家站长
        boolean isCountry = LoginHelper.hasRole(loginUser, RoleCodeEnum.CountryStationAgent);
        // 城市站长
        boolean isCity = LoginHelper.hasRole(loginUser, RoleCodeEnum.CityStationAgent);
        if (isAdmin || isCountry || isCity) {

        } else {
            bo.setUserId(LoginHelper.getUserId());
        }
        LambdaQueryWrapper<SohuArticle> lqw = buildQueryWrapper(bo);
        lqw.eq(SohuArticle::getDelFlag, false);
        lqw.orderByAsc(SohuArticle::getSortIndex);
        lqw.orderByDesc(SohuArticle::getCreateTime);
        Page<SohuArticleVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            Set<Long> ossIds = new HashSet<>();
            List<Long> articleIds = new ArrayList<>();
            Set<Long> siteIds = new HashSet<>();
            for (SohuArticleVo record : result.getRecords()) {
                if (NumberUtil.isNumber(record.getCoverImage())) {
                    ossIds.add(Long.valueOf(record.getCoverImage()));
                }
                articleIds.add(record.getId());
                siteIds.add(record.getSiteId());
            }
            SohuArticleInfoBo infoBo = new SohuArticleInfoBo();
            infoBo.setArticleIds(articleIds);
            // 设置拓展数据
            List<SohuArticleInfoVo> articleInfoVos = iSohuArticleInfoService.queryList(infoBo);
            Map<Long, SohuArticleInfoVo> infoVoMap = articleInfoVos.stream().collect(Collectors.toMap(SohuArticleInfoVo::getArticleId, u -> u));
            Map<Long, String> map = remoteFileService.map(ossIds);
            Map<Long, SohuSiteVo> siteModelMap = siteService.queryMap(siteIds);
            for (SohuArticleVo record : result.getRecords()) {
                if (StrUtil.isNotBlank(record.getCoverImage())) {
                    if (NumberUtil.isNumber(record.getCoverImage())) {
                        record.setCoverUrl(map.get(record.getCoverImage()));
                    } else {
                        record.setCoverUrl(record.getCoverImage());
                    }
                }
                // 设置分类名
                SohuCategoryVo sohuCategoryVo = categoryService.queryById(record.getCategoryId());
                record.setCategoryName(Objects.nonNull(sohuCategoryVo) ? sohuCategoryVo.getName() : null);
                // 设置审核状态以及站点名称
                setAuditStatus(record, siteModelMap);
                record.setContentState(record.getState());
                record.setBusyType(BusyType.Article.getType());
                // 设置用户头像以及昵称

                if (Objects.nonNull(loginUser)) {
                    record.setUserName(loginUser.getNickname());
                    record.setUserAvatar(loginUser.getAvatar());
                } else {
                    record.setUserName(Constants.DEFAULT_USER_NICKNAME);
                    record.setUserAvatar(Constants.DEFAULT_AVATAR);
                }
                SohuArticleInfoVo infoVo = infoVoMap.get(record.getId());
                if (Objects.nonNull(infoVo)) {
                    //infoVo.setContent(null);
                    record.setContent(infoVo.getContent());
                    record.setInfo(infoVo);
                } else {
                    record.setInfo(new SohuArticleInfoVo());
                }
                // 设置关联数据
                buildRelate(record);
                // 设置移出时间
                if (bo.getIsBlack()) {
                    SohuBusyBlackVo sohuBusyBlackVo = null;
                    if (Objects.nonNull(bo.getIndustryId())) {
                        sohuBusyBlackVo = busyBlackService.queryByParam(record.getId(), 2, BusyType.Article.getType());
                    } else {
                        sohuBusyBlackVo = busyBlackService.queryByParam(record.getId(), 1, BusyType.Article.getType());
                    }
                    record.setRemoveTime(sohuBusyBlackVo.getCreateTime());
                }
            }
        }

        return TableDataInfoUtils.build(result);
    }

    @Override
    public TableDataInfo<SohuArticleVo> queryPageListOfOnShelf(SohuArticleBo bo, PageQuery pageQuery) {
//        LambdaQueryWrapper<SohuArticle> lqw = new LambdaQueryWrapper<>();
//        lqw.eq(SohuArticle::getState,CommonState.OnShelf.name());
//        lqw.in(CollUtil.isNotEmpty(bo.getCategoryIds()), SohuArticle::getCategoryId, bo.getCategoryIds());
//        lqw.like(StringUtils.isNotBlank(bo.getTitle()), SohuArticle::getTitle, bo.getTitle());
//        lqw.orderByAsc(SohuArticle::getSortIndex);
//        lqw.orderByDesc(SohuArticle::getCreateTime);
//        Page<SohuArticleVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        Page<SohuArticleVo> result = baseMapper.queryPageListOfOnShelf(PageQueryUtils.build(pageQuery), bo);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            Set<Long> ossIds = new HashSet<>();
            Set<Long> userIds = new HashSet<>();
            List<Long> articleIds = new ArrayList<>();
            Set<Long> siteIds = new HashSet<>();
            for (SohuArticleVo record : result.getRecords()) {
                if (NumberUtil.isNumber(record.getCoverImage())) {
                    ossIds.add(Long.valueOf(record.getCoverImage()));
                }
                articleIds.add(record.getId());
                userIds.add(record.getUserId());
                siteIds.add(record.getSiteId());
            }
            Map<Long, String> map = remoteFileService.map(ossIds);
            Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
            SohuArticleInfoBo infoBo = new SohuArticleInfoBo();
            infoBo.setArticleIds(articleIds);
            // 设置拓展数据
            List<SohuArticleInfoVo> articleInfoVos = iSohuArticleInfoService.queryList(infoBo);
            Map<Long, SohuArticleInfoVo> infoVoMap = articleInfoVos.stream().collect(Collectors.toMap(SohuArticleInfoVo::getArticleId, u -> u));
            Map<Long, SohuSiteVo> siteModelMap = siteService.queryMap(siteIds);
            for (SohuArticleVo record : result.getRecords()) {
                if (StrUtil.isNotBlank(record.getCoverImage())) {
                    if (NumberUtil.isNumber(record.getCoverImage())) {
                        record.setCoverUrl(map.get(record.getCoverImage()));
                    } else {
                        record.setCoverUrl(record.getCoverImage());
                    }
                }
                // 设置分类名
                SohuCategoryVo sohuCategoryVo = categoryService.queryById(record.getCategoryId());
                record.setCategoryName(Objects.nonNull(sohuCategoryVo) ? sohuCategoryVo.getName() : null);
                // 设置审核状态以及站点名称
                setAuditStatus(record, siteModelMap);
                record.setContentState(record.getState());
                record.setBusyType(BusyType.Article.getType());
                // 设置用户头像以及昵称
                LoginUser loginUser = userMap.get(record.getUserId());
                if (Objects.nonNull(loginUser)) {
                    record.setUserName(loginUser.getUsername());
                    record.setNickName(loginUser.getNickname());
                    record.setUserAvatar(loginUser.getAvatar());
                } else {
                    record.setUserName(Constants.DEFAULT_USER_NICKNAME);
                    record.setNickName(Constants.DEFAULT_USER_NICKNAME);
                    record.setUserAvatar(Constants.DEFAULT_AVATAR);
                }
                SohuArticleInfoVo infoVo = infoVoMap.get(record.getId());
                if (Objects.nonNull(infoVo)) {
                    //infoVo.setContent(null);
                    record.setContent(infoVo.getContent());
                    record.setInfo(infoVo);
                } else {
                    record.setInfo(new SohuArticleInfoVo());
                }
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public SohuConentListStatVo queryPageListStat(SohuArticleBo bo) {
        // 判断站点和行业，分别查询黑名单
        List<Long> handleIds = new ArrayList<>();
        if (Objects.nonNull(bo.getIndustryId())) {
            // 行业id不为空,则查询相关行业对应的分类
            SohuPlatformIndustryRelationBo relationBo = new SohuPlatformIndustryRelationBo();
            relationBo.setPlatformIndustryId(bo.getIndustryId());
            relationBo.setBusyType(BusyType.Content.getType());
            List<SohuPlatformIndustryRelationVo> relationList = relationService.queryList(relationBo);
            List<Long> categoryIds = relationList.stream().map(SohuPlatformIndustryRelationVo::getBusyCategoryId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(bo.getCategoryIds())) {
                bo.setCategoryIds(categoryIds);
            } else {
                // 取交集
                categoryIds.retainAll(bo.getCategoryIds());
                bo.setCategoryIds(categoryIds);
            }
            // 查询行业黑名单
            handleIds = busyBlackService.listBusyIds(bo.getIndustryId(), 2, BusyType.Article.getType());
        } else if (Objects.nonNull(bo.getSiteId()) && LoginHelper.hasRole(LoginHelper.getLoginUser(), RoleCodeEnum.CityStationAgent)) {
            // 站点id不为空,则查询行业黑名单
            handleIds = busyBlackService.listBusyIds(bo.getSiteId(), 1, BusyType.Article.getType());
        }
        if (bo.getIsBlack() && CollectionUtils.isEmpty(handleIds)) {
            handleIds.add(0L);
        }
        bo.setHandleIds(handleIds);
        bo.setUserId(LoginHelper.getUserId());
        LambdaQueryWrapper<SohuArticle> lqw = buildQueryWrapper(bo);
        if (StrUtil.isNotBlank(bo.getState()) && (!Constants.ALL.equals(bo.getState()))) {
            lqw.eq(SohuArticle::getState, bo.getState());
        }
        lqw.eq(SohuArticle::getDelFlag, 0);
        SohuConentListStatVo vo = this.baseMapper.queryPageListStat(lqw);
        if (Objects.isNull(vo)) {
            vo = new SohuConentListStatVo();
        }
        Long allNum = vo.getOnShelfNum() + vo.getOffShelfNum() + vo.getWaitApproveNum() + vo.getRefuseNum();
        vo.setAllNum(allNum);
        return vo;
    }

    /**
     * 设置审核状态
     */
    private static void setAuditStatus(SohuArticleVo record, Map<Long, SohuSiteVo> siteModelMap) {
        SohuSiteVo sohuSiteModel = siteModelMap.get(record.getSiteId());
        if (Objects.isNull(sohuSiteModel)) {
            return;
        }
        record.setSiteName(sohuSiteModel.getName());
        Set<String> allowedStates = new HashSet<>(Arrays.asList(CommonState.WaitApprove.getCode(), CommonState.OnShelf.getCode(), CommonState.Refuse.getCode()));
        // 获取当前状态
        String currentState = record.getState();
        // 判断当前状态是否在允许的状态列表中
        if (allowedStates.contains(currentState)) {
            record.setContentState(currentState);
        }
    }

    @Override
    public TableDataInfo<SohuArticleVo> articlePageCenter(Long userId, PageQuery pageQuery) {
        Long loginUserId = LoginHelper.getUserId();
        if (userId == null) {
            userId = loginUserId;
        }
        SohuArticleBo dto = new SohuArticleBo();
        dto.setUserId(userId);
        List<String> states = new ArrayList<>();
        states.add(BusyOrderStatus.OnShelf.name());
        if (Objects.equals(loginUserId, userId)) {
            // 查看自己的
            states.add(BusyOrderStatus.WaitApprove.name());
            states.add(BusyOrderStatus.CompelOff.name());
            states.add(BusyOrderStatus.Hide.name());
        }
        dto.setStateList(states);
        if (dto.getUserId() == null) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        return queryPage(dto, pageQuery);
    }

    @Override
    public TableDataInfo<SohuArticleVo> followPage(PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            throw new RuntimeException(MessageUtils.message("user.not.login"));
        }
        //准备播放内容的作者id集合
        List<Long> authorIds = new ArrayList<>();
        //我自己
        authorIds.add(userId);
        // 我关注的用户
        SohuUserFollowBo followBo = new SohuUserFollowBo();
        followBo.setUserId(userId);
        List<SohuUserFollowVo> followVos = sohuUserFollowService.queryList(followBo);
        if (CollectionUtils.isNotEmpty(followVos)) {
            List<Long> focusUser = followVos.stream().map(SohuUserFollowVo::getFocusUserId).collect(Collectors.toList());
            authorIds.addAll(focusUser);
            //关注的用户的好友
            LambdaQueryWrapper<SohuFriends> friendsWrapper = new LambdaQueryWrapper<>();
            friendsWrapper.in(SohuFriends::getUserId, userId).eq(SohuFriends::getApplyState, ApplyStateEnum.pass.name());
            List<SohuFriends> friendsList = this.sohuFriendsMapper.selectList(friendsWrapper);
            if (CollectionUtils.isNotEmpty(friendsList)) {
                List<Long> friendIds = friendsList.stream().map(SohuFriends::getFriendId).collect(Collectors.toList());
                authorIds.addAll(friendIds);
            }
        }
        //去重
        authorIds = authorIds.stream().distinct().collect(Collectors.toList());
        SohuArticleBo bo = new SohuArticleBo();
        bo.setUserIds(authorIds);
        bo.setState(CommonState.OnShelf.getCode());
        return queryPage(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuArticleVo> queryMCNArticleList(SohuMcnArticleReqBo bo, PageQuery pageQuery) {
        Page<SohuArticleVo> result = baseMapper.selectMCNArticleList(PageQueryUtils.build(pageQuery), bo);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            Set<Long> userIds = result.getRecords().stream().filter(p -> Objects.nonNull(p.getUserId())).map(p -> p.getUserId()).collect(Collectors.toSet());
            Set<Long> articleIds = result.getRecords().stream().map(p -> p.getId()).collect(Collectors.toSet());
            Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
            List<SohuArticleInfo> articleInfoList = this.sohuArticleInfoMapper.selectList(SohuArticleInfo::getArticleId, articleIds);
            Map<Long, SohuArticleInfo> articleInfoMap = articleInfoList.stream().collect(Collectors.toMap(SohuArticleInfo::getArticleId, u -> u));
            for (SohuArticleVo vo : result.getRecords()) {
                LoginUser loginUser = userMap.get(vo.getUserId());
                if (Objects.nonNull(loginUser)) {
                    vo.setUserAvatar(loginUser.getAvatar());
                    vo.setNickName(loginUser.getNickname());
                }
                SohuArticleInfo articleInfo = articleInfoMap.get(vo.getId());
                if (Objects.nonNull(articleInfo)) {
                    vo.setViewCount(articleInfo.getViewCount());
                    vo.setCommentCount(articleInfo.getCommentCount());
                    vo.setPraiseCount(articleInfo.getPraiseCount());
                }
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public boolean logicDeleteById(Long id) {
        SohuArticle sohuArticle = baseMapper.selectById(id);
        if (Objects.isNull(sohuArticle)) {
            return false;
        }
        SohuContentLifecycleBo lifecycleBo = new SohuContentLifecycleBo(id, BusyType.Article.name(), sohuArticle.getState(), CommonState.Delete.name(), "自主删除");
        if (!Objects.equals(LoginHelper.getUserId(), sohuArticle.getUserId())) {
            throw new RuntimeException("非法操作，这不是您的数据");
        }
        sohuArticle.setState(CommonState.Delete.name());
        sohuArticle.setDelTime(DateUtils.getNowDate());
        baseMapper.updateById(sohuArticle);
        this.createLifecycleLog(lifecycleBo);
        iSohuAirecContentItemService.updateStatusToOffShelf(id.toString(), AliyunAirecContentItemTypeEnum.ARTICLE.getCode());
        return true;
    }

    @Override
    public boolean logicDeleteById(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        for (Long id : ids) {
            this.logicDeleteById(id);
        }
        return true;
    }

    @Override
    public boolean logicForceDeleteById(Long id) {
//        if (!LoginHelper.anyMatchRole(RoleCodeEnum.ADMIN.getCode(), RoleCodeEnum.CALL_ADMIN.getCode(), RoleCodeEnum.OPERATION_ADMIN.getCode())) {
//            throw new RuntimeException("非法操作，您无权操作");
//        }
        SohuArticle sohuArticle = baseMapper.selectById(id);
        if (Objects.isNull(sohuArticle)) {
            return false;
        }
        SohuContentLifecycleBo lifecycleBo = new SohuContentLifecycleBo(id, BusyType.Article.name(), sohuArticle.getState(), CommonState.ForceDelete.name(), "强制删除");
        sohuArticle.setState(CommonState.ForceDelete.name());
        sohuArticle.setDelTime(DateUtils.getNowDate());
        baseMapper.updateById(sohuArticle);
        this.createLifecycleLog(lifecycleBo);
        iSohuAirecContentItemService.updateStatusToOffShelf(id.toString(), AliyunAirecContentItemTypeEnum.ARTICLE.getCode());
        return true;
    }

    @Override
    public boolean logicForceDeleteById(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        for (Long id : ids) {
            this.logicForceDeleteById(id);
        }
        return true;
    }

    @Override
    public boolean updateOffShelfById(Long id) {
        SohuArticle sohuArticle = baseMapper.selectById(id);
        if (Objects.isNull(sohuArticle)) {
            return false;
        }
        if (!Objects.equals(LoginHelper.getUserId(), sohuArticle.getUserId())) {
            throw new RuntimeException("非法操作，这不是您的数据");
        }
        sohuArticle.setState(CommonState.OffShelf.name());
        baseMapper.updateById(sohuArticle);
        iSohuAirecContentItemService.updateStatusToOffShelf(id.toString(), AliyunAirecContentItemTypeEnum.ARTICLE.getCode());
        return true;
    }

    @Override
    public boolean updateCompelOffById(SohuContentRefuseBo bo) {
//        if (!LoginHelper.anyMatchRole(RoleCodeEnum.ADMIN.getCode(), RoleCodeEnum.CALL_ADMIN.getCode(), RoleCodeEnum.OPERATION_ADMIN.getCode())) {
//            throw new RuntimeException("非法操作，您无权操作");
//        }
        SohuArticle sohuArticle = baseMapper.selectById(bo.getId());
        if (Objects.isNull(sohuArticle)) {
            return false;
        }
        sohuArticle.setState(CommonState.CompelOff.name());
        sohuArticle.setRejectReason(bo.getReason());
        baseMapper.updateById(sohuArticle);
        iSohuAirecContentItemService.updateStatusToOffShelf(bo.getId().toString(), AliyunAirecContentItemTypeEnum.ARTICLE.getCode());
        CompletableFuture.runAsync(() -> sendMsgOfCompelOff(sohuArticle), asyncConfig.getAsyncExecutor());
        return true;
    }

    /**
     * 发送系统消息通知-强制下架
     */
    private void sendMsgOfCompelOff(SohuArticle sohuArticle) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.CONTENT_COMPEL_OFF_TITLE);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.articleCompelOff.name());
        content.setDetailId(sohuArticle.getId());
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setId(sohuArticle.getId());
        detail.setDesc(String.format(SystemNoticeEnum.CONTENT_COMPEL_OFF_DESC, sohuArticle.getTitle(), sohuArticle.getRejectReason()));
        detail.setStatus(CommonState.CompelOff.name());
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(sohuArticle.getUserId(), SystemNoticeEnum.CONTENT_COMPEL_OFF_TITLE, contentJson, SystemNoticeEnum.Type.contentCompelOff);
    }

    /**
     * 记录生命周期
     *
     * @param lifecycleBo
     * @return
     */
    private Boolean createLifecycleLog(SohuContentLifecycleBo lifecycleBo) {
        if (Objects.equals(lifecycleBo.getLastState(), CommonState.OnShelf.name())
                || Objects.equals(lifecycleBo.getLastState(), CommonState.WaitApprove.name())) {
            if (Objects.equals(lifecycleBo.getCurrentState(), CommonState.ForceDelete.name())) {
                lifecycleBo.setCurrentState(CommonState.CompelOff.name());
                this.sohuContentLifecycleService.insertByBo(lifecycleBo);
                lifecycleBo.setId(null);
                lifecycleBo.setLastState(CommonState.CompelOff.name());
                lifecycleBo.setCurrentState(CommonState.ForceDelete.name());
            } else if (Objects.equals(lifecycleBo.getCurrentState(), CommonState.Delete.name())) {
                lifecycleBo.setCurrentState(CommonState.OffShelf.name());
                this.sohuContentLifecycleService.insertByBo(lifecycleBo);
                lifecycleBo.setId(null);
                lifecycleBo.setLastState(CommonState.OffShelf.name());
                lifecycleBo.setCurrentState(CommonState.Delete.name());
            }
        }
        this.sohuContentLifecycleService.insertByBo(lifecycleBo);
        return true;
    }

    @Override
    public TableDataInfo<SohuArticleVo> queryPage(SohuArticleBo bo, PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        //当前用户idset进去为了排序用
        bo.setCurrentUserId(userId);
//        //如果需要智能推荐
//        if (bo.getAiRecommend()) {
//            TableDataInfo<SohuArticleVo> sohuAirecContentArticle = getSohuAirecContentArticle(bo);
//            if (CollUtil.isNotEmpty(sohuAirecContentArticle.getData())) {
//                if (bo.getCurrentUserId() != null && StringUtils.isEmpty(bo.getImei())) {
//                    getRecord(sohuAirecContentArticle.getData(), userId);
//                }
//                //如果推荐内容不为空返回推荐
//                return sohuAirecContentArticle;
//            }
//        }
        //狐少少课堂不需要过滤数据
        if (!VideoEnum.Type.lesson.getCode().equals(bo.getType())) {
            if (StrUtil.isBlank(bo.getTitle()) && bo.getUserId() == null && userId != null && CollUtil.isEmpty(bo.getUserIds())) {
                // 看一个少一个 过滤游客,关注列表,个人中心以及搜索列表
                String articleKey = CacheConstants.ARTICLE + userId;
                Set<Long> cacheListIds = RedisUtils.getCacheObject(articleKey);
                bo.addToNotIds(cacheListIds);
            }
            if (StrUtil.isBlank(bo.getTitle()) && bo.getUserId() == null && userId != null) {
                // 不感兴趣 过滤游客，个人中心,以及搜索
                List<SohuDislikeVo> sohuDislikeVos = iSohuDislikeService.selectByUserId(userId);
                if (CollUtil.isNotEmpty(sohuDislikeVos)) {
                    Set<Long> dislikeCategory = sohuDislikeVos.stream().filter(a -> DislikeEnum.DislikeCategory.name().equals(a.getDislikeType()) && UserConstants.DISLIKE_CATEGORY_COUNT <= a.getCount()).map(SohuDislikeVo::getDislikeId).collect(Collectors.toSet());
                    bo.setNotCategoryIds(dislikeCategory);
                    Set<Long> dislikeAuthor = sohuDislikeVos.stream().filter(a -> DislikeEnum.DislikeAuthor.name().equals(a.getDislikeType()) && UserConstants.DISLIKE_AUTHOR_COUNT <= a.getCount()).map(SohuDislikeVo::getDislikeId).collect(Collectors.toSet());
                    if (CollUtil.isNotEmpty(bo.getNotUserIds())) {
                        bo.setNotUserIds(Stream.concat(bo.getNotUserIds().stream(), dislikeAuthor.stream()).collect(Collectors.toSet()));
                    } else {
                        bo.setNotUserIds(dislikeAuthor);
                    }
                    // 不感兴趣缓存
                    String articleDislikeKey = CacheConstants.ARTICLE_DISLIKE + userId;
                    Set<Long> cacheListIds = RedisUtils.getCacheObject(articleDislikeKey);
                    bo.addToNotIds(cacheListIds);
                }
            }
        }
        /*LambdaQueryWrapper<SohuArticle> lqw = buildQueryWrapper(bo);
        Page<SohuArticleVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);*/
        //2023-12-28 修改排序  hezhen
        bo.setVisibleType(VisibleTypeEnum.open.getCode());

        Page<SohuArticleVo> result = baseMapper.selectArticlePages(PageQueryUtils.build(pageQuery), bo);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            List<SohuArticleVo> records = result.getRecords();
            //getRecord(records, userId);
            result.setRecords(getRecord(records, userId));
        }
        return TableDataInfoUtils.build(result);
    }

    /**
     * 过滤用户作品内容
     *
     * @param bo SohuArticleBo
     */
    private void filterBanUserContent(SohuArticleBo bo) {
        SohuBanRecordsBo sohuBanRecordsBo = new SohuBanRecordsBo();
        sohuBanRecordsBo.setStatus("active");
        List<SohuBanRecordsVo> sohuBanRecordsVoList = remoteBanRecordService.queryList(sohuBanRecordsBo);
        // 聚合用户id集合
        Set<Long> banUserIds = sohuBanRecordsVoList.stream().map(SohuBanRecordsVo::getUserId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(banUserIds)) {
            bo.setNotUserIds(banUserIds);
        }
        // 查询用户id作品集合
        List<Long> articleIds = this.getArticleIdsByUserId(banUserIds);
        if (CollUtil.isNotEmpty(articleIds)) {
            bo.setHandleIds(articleIds);
        }
    }

    private List<Long> getArticleIdsByUserId(Set<Long> banUserIds) {
        List<SohuArticleVo> sohuArticleVoList = baseMapper.selectVoList(new LambdaQueryWrapper<SohuArticle>().in(CollUtil.isNotEmpty(banUserIds), SohuArticle::getUserId, banUserIds));
        if (CollUtil.isNotEmpty(sohuArticleVoList)) {
            return sohuArticleVoList.stream().map(SohuArticleVo::getId).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public TableDataInfo<SohuArticleVo> queryPageOfAirec(SohuArticleBo bo, PageQuery pageQuery) {
        // 过滤封禁用户作品
        filterBanUserContent(bo);
        SohuAirecArticleQueryBo airecQueryBo = new SohuAirecArticleQueryBo();
        if (Objects.nonNull(bo.getSiteId())) {
            List<Long> handleIds = busyBlackService.listBusyIds(bo.getSiteId(), 1, BusyType.Article.getType());
            if (CollUtil.isNotEmpty(bo.getHandleIds())) {
                bo.setHandleIds(Stream.concat(bo.getHandleIds().stream(), handleIds.stream()).distinct().collect(Collectors.toList()));
                airecQueryBo.setBlackIds(Stream.concat(bo.getHandleIds().stream(), handleIds.stream()).distinct().collect(Collectors.toList()));
            } else {
                bo.setHandleIds(handleIds);
                airecQueryBo.setBlackIds(handleIds);
            }
        }
        buildSiteIdRule(bo, airecQueryBo);
        if (bo.getAiRec()) {
            if (StrUtil.isBlank(bo.getAiRecImei()) && Objects.isNull(LoginHelper.getUserId())) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (!(AliyunAirecConstant.SCENE_ARTICLE_ALL.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_ARTICLE_HOMEPAGE.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_ARTICLE_MONEYMAKING.equals(bo.getAiRecSceneId()))) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (Objects.nonNull(LoginHelper.getUserId())) {
                bo.setAiUserId(LoginHelper.getUserId().toString());
            }
            bo.setAiReturnCount(pageQuery.getPageSize());
            //构造过滤的内容
            SohuAirecContentItemVo airecCategoryInfo = categoryService.getAirecCategoryInfoById(bo.getCategoryId());
            airecQueryBo.setCategoryPath(airecCategoryInfo.getCategoryPath());
            airecQueryBo.setType(bo.getType());
            AliyunAirecJoinFilterRule rootRule = airecQueryBo.buildAirecFilterRule();
//            this.buildAirecJoinFilterRule(bo.getSiteId(), bo.getCategoryId(), rootRule);
            //获取阿里云智能推荐结果
            List<SohuArticleVo> resultList = AliyunAirecUtil.aiRecommendSingleType(rootRule, bo, itemIds -> baseMapper.selectVoBatchIds(itemIds));
            if (CollUtil.isNotEmpty(resultList)) {
                //getRecord(resultList, LoginHelper.getUserId());
                resultList = getRecord(resultList, LoginHelper.getUserId());
                return TableDataInfoUtils.build(resultList);
            }
        }
        //首页不显示有关联的--APP-1.0临时需求
//        if (bo.getIsRelate() == null && !VideoEnum.Type.lesson.getCode().equals(bo.getType()) && StringUtils.isBlank(bo.getTitle())) {
//            bo.setIsRelate(false);
//        }
        TableDataInfo<SohuArticleVo> tableDataInfo = this.queryPage(bo, pageQuery);
        AliyunAirecUtil.buildAiRecommendSingleType(tableDataInfo.getData(), AliyunAirecContentItemTypeEnum.ARTICLE.getCode());
        return tableDataInfo;
    }

    /**
     * 处理站点字段
     */
    private void buildSiteIdRule(SohuArticleBo bo, SohuAirecArticleQueryBo airecQueryBo) {
        if (Objects.isNull(bo.getSiteId())) {
            SohuSiteVo siteVo = siteService.getSiteByIp(ServletUtils.getClientIP());
            if (Objects.nonNull(siteVo)) {
                bo.setSiteId(siteVo.getId());
                if (Objects.equals(siteVo.getType(), SiteType.City.name())) {
                    airecQueryBo.setCity(String.valueOf(siteVo.getId()));
                } else if (Objects.equals(siteVo.getType(), SiteType.Country.name())) {
                    airecQueryBo.setCountry(siteVo.getCountryCode());
                }
            }
        }
        //首页的话 不查站点 只分国内国外
        if (Objects.nonNull(bo.getRecommend()) && bo.getRecommend()) {
            //推荐页就不分站点 只分国家
            if (Objects.nonNull(bo.getSiteId())) {
//                SohuSiteVo sohuSiteVo = siteService.selectSiteByPid(bo.getSiteId());
//                if (Objects.nonNull(sohuSiteVo)) {
//                    bo.setCountrySiteId(sohuSiteVo.getId());
//                    bo.setSiteId(null);
//                    airecQueryBo.setCountry(sohuSiteVo.getCountryCode());
//                }
                SohuSiteVo sohuSiteVo = siteService.getCountrySiteById(bo.getSiteId());
                if (Objects.nonNull(sohuSiteVo)) {
                    bo.setCountrySiteId(sohuSiteVo.getId());
                    bo.setSiteId(null);
                    airecQueryBo.setCountry(sohuSiteVo.getCountryCode());
                    airecQueryBo.setCity(null);
                }
            }
        }
    }

    /**
     * 构建智能推荐过滤参数
     *
     * @param siteId
     * @param rootRule
     */
    @Deprecated
    private void buildAirecJoinFilterRule(Long siteId, Long categoryId, AliyunAirecJoinFilterRule rootRule) {
        rootRule.setJoin(AliyunAirecQueryJoinEnum.AND.getCode());
        {
            //内容的类型
            AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
            rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
            rule.setField(AliyunAirecQueryFieldContentEnum.ITEM_TYPE.getCode());
            rule.setValue(AliyunAirecContentItemTypeEnum.ARTICLE.getCode());
            rootRule.getFilters().add(rule);
        }
        {
            //站点
            if (!Objects.isNull(siteId)) {
                AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
                rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
                rule.setField(AliyunAirecQueryFieldContentEnum.CITY.getCode());
                rule.setValue(siteId.toString());
                rootRule.getFilters().add(rule);
            }
        }
        {
            //TODO 进行判断
            AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
            rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
            rule.setField(AliyunAirecQueryFieldContentEnum.COUNTRY.getCode());
            SohuSiteVo sohuSiteVo = siteService.selectSiteByPid(siteId);
            if (Objects.nonNull(sohuSiteVo)) {
                String code = siteService.selectCountryCodeById(sohuSiteVo.getId());
                if (StrUtil.isNotBlank(code)) {
                    //设置国家编码
                    rule.setValue(code);
                }
            }
            rootRule.getFilters().add(rule);
        }
        {
            if (categoryId != null) {
                SohuAirecContentItemVo airecCategoryInfo = categoryService.getAirecCategoryInfoById(categoryId);
                if (StrUtil.isNotBlank(airecCategoryInfo.getCategoryPath())) {
                    AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
                    rule.setCond(AliyunAirecQueryCondEnum.CATEGORY_MATCH.getCode());
                    rule.setField(AliyunAirecQueryFieldContentEnum.CATEGORY_PATH.getCode());
                    rule.setValue(airecCategoryInfo.getCategoryPath());
                    rootRule.getFilters().add(rule);
                }
            }
        }
    }

    /**
     * 查询图文主体列表
     */
    @Override
    public List<SohuArticleVo> queryList(SohuArticleBo bo) {
        LambdaQueryWrapper<SohuArticle> lqw = buildQueryWrapper(bo);
        lqw.orderByAsc(SohuArticle::getSortIndex);
        lqw.orderByDesc(SohuArticle::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuArticle> buildQueryWrapper(SohuArticleBo bo) {
        LambdaQueryWrapper<SohuArticle> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuArticle::getUserId, bo.getUserId());
        lqw.eq(bo.getSiteId() != null, SohuArticle::getSiteId, bo.getSiteId());
        lqw.eq(bo.getCategoryId() != null, SohuArticle::getCategoryId, bo.getCategoryId());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), SohuArticle::getTitle, bo.getTitle());
        if (StrUtil.isEmpty(bo.getState())) {
            lqw.notIn(SohuArticle::getState, CommonState.Edit.getCode(), CommonState.Delete.getCode(), CommonState.ForceDelete.getCode());
        } else if (StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.OffShelf.getCode())) {
            lqw.in(SohuArticle::getState, CommonState.OffShelf.getCode(), CommonState.CompelOff.getCode());
        } else {
            lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuArticle::getState, bo.getState());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getType()), SohuArticle::getType, bo.getType());
        lqw.in(CollUtil.isNotEmpty(bo.getStateList()), SohuArticle::getState, bo.getStateList());
        lqw.in(CollUtil.isNotEmpty(bo.getUserIds()), SohuArticle::getUserId, bo.getUserIds());
        lqw.notIn(CollUtil.isNotEmpty(bo.getNotIds()), SohuArticle::getId, bo.getNotIds());
        lqw.notIn(CollUtil.isNotEmpty(bo.getNotCategoryIds()), SohuArticle::getCategoryId, bo.getNotCategoryIds());
        lqw.notIn(CollUtil.isNotEmpty(bo.getNotUserIds()), SohuArticle::getUserId, bo.getNotUserIds());
        lqw.in(CollUtil.isNotEmpty(bo.getCategoryIds()), SohuArticle::getCategoryId, bo.getCategoryIds());
        lqw.in(CollUtil.isNotEmpty(bo.getSiteIds()), SohuArticle::getSiteId, bo.getSiteIds());
        lqw.eq(StringUtils.isNotBlank(bo.getContentState()), SohuArticle::getState, bo.getContentState());
        lqw.eq(bo.getAppealStatus() != null, SohuArticle::getAppealStatus, bo.getAppealStatus());
        if (CollUtil.isNotEmpty(bo.getHandleIds())) {
            if (bo.getIsBlack()) {
                lqw.in(SohuArticle::getId, bo.getHandleIds());
            } else {
                lqw.notIn(SohuArticle::getId, bo.getHandleIds());
            }
        }
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            lqw.ge(StrUtil.isNotBlank(bo.getStartTime()), SohuArticle::getCreateTime, DateUtil.beginOfDay(DateUtil.parseDate(bo.getStartTime())));
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            lqw.le(StrUtil.isNotBlank(bo.getEndTime()), SohuArticle::getCreateTime, DateUtil.endOfDay(DateUtil.parseDate(bo.getEndTime())));
        }
        return lqw;
    }

    /**
     * 新增图文主体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SohuArticleBo bo) {
        SohuArticle add = BeanUtil.toBean(bo, SohuArticle.class);
        String state = StrUtil.isNotBlank(bo.getState()) && StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.Edit.name()) ? CommonState.Edit.name() : CommonState.WaitApprove.name();
        if (!(Objects.equals(CommonState.WaitApprove.name(), state) || Objects.equals(CommonState.Edit.name(), state))) {
            throw new RuntimeException("状态参数异常");
        }
        add.setState(state);
        if (StrUtil.isBlank(bo.getType())) {
            add.setType(VideoEnum.Type.general.getCode());
        }
        if (StrUtil.isBlank(bo.getVisibleType())) {
            add.setVisibleType(VisibleTypeEnum.open.getCode());
        }
        add.setUserId(LoginHelper.getUserId());
        validEntityBeforeSave(add);
        setMCNParams(add, bo);
        String scanText = null;
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
            add.setSubmitTime(new Date());
            add.setSubmitScene("自主发布");
            add.setSubmitNum(Constants.ONE);
//            scanText = aliyunAuditService.scanText(HtmlUtil.cleanHtmlTag(bo.getTitle() + bo.getContent()), AliyunAuditLabelEnum.textCheck);
//            if (StrUtil.isEmptyIfStr(scanText)) {
//                add.setState(CommonState.OnShelf.getCode());
//                add.setAuditTime(new Date());
//                add.setAuditState(CommonState.Pass.name());
//            } else {
            add.setState(CommonState.WaitApprove.getCode());
            add.setAuditState(CommonState.WaitApprove.name());
//                List<String> labels = parseLabelsFromJson(scanText);
//                // 匹配枚举并获取描述信息
//                List<String> descriptions = labels.stream()
//                        .map(AliyunAuditLabelEnum::getDescriptionByLabel)
//                        .filter(Optional::isPresent)
//                        .map(Optional::get)
//                        .collect(Collectors.toList());
//                String rejectReason = String.join("; ", descriptions);
//                add.setRejectReason(rejectReason);
//            }

        }
        //处理站点信息
        //如果站点没传 需要做一下兜底
        if (Objects.isNull(bo.getSiteId())) {
            SohuSiteVo siteVo = siteService.getSiteByIp(ServletUtils.getClientIP());
            add.setSiteId(siteVo.getId());
            if (Objects.equals(siteVo.getType(), SiteType.City.name())) {
                Long pid = siteVo.getPid();
                add.setCountrySiteId(pid);
            }
        } else {
            SohuSiteVo sohuSiteVo = siteService.selectSiteByPid(bo.getSiteId());
            if (Objects.nonNull(sohuSiteVo)) {
                add.setCountrySiteId(sohuSiteVo.getId());
            }
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            if (StrUtil.isNotBlank(scanText)) {
                // 保存审核记录
//                if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
//                    this.initCreateAudited(add);
//                }
                log.warn("【{}】标题或内容违规，审核结果：{}", add.getId(), scanText);
            }
            // 保存审核记录，机审通过的也要保存记录，修改日期，2024-11-27 17:42:58
            if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
                this.initCreateAudited(add, true);
                // 发布异步机审消息
                SohuRiskMqBo riskMqBo = new SohuRiskMqBo();
                riskMqBo.setBusyType(BusyType.Article);
                riskMqBo.setBusyCode(add.getId().toString());
                MqMessaging mqMessaging = new MqMessaging(JSONObject.toJSONString(riskMqBo), MqKeyEnum.HANDLE_RISK_MANAGEMENT.getKey());
                remoteStreamMqService.sendDelayMsg(mqMessaging, 2L);
            }
            SohuArticleInfoBo info = new SohuArticleInfoBo();
            info.setArticleId(add.getId());
            info.setContent(bo.getContent());
            info.setSiteId(bo.getSiteId());
            info.setLearnNum(bo.getLearnNum());
            info.setChildTaskNum(bo.getBusyInfo());
            info.setViewCount(Math.max(bo.getViewCount(), 0));
            // 保存内容详情
            iSohuArticleInfoService.insertByBo(info);
            // 保存内容
            bo.setId(add.getId());
            // 保存关联项
            addArticleRelate(bo);
            // 同步图文至万能表
            sohuSyncContentService.sync(add);
            //新增发布详情（蚁小二）
            this.sohuArticleDetailService.insertDetailByBo(add.getId(), McnHandlerTypeEnum.Article, bo.getDetails());
            //新增图文物料
            this.addSohuAirecContentItem(add, bo);
        }
        return flag;
    }

    /**
     * 保存关联项
     *
     * @param bo
     */
    public void addArticleRelate(SohuArticleBo bo) {
        //删除关联项
        iSohuArticleRelateService.deleteByArticleIds(Arrays.asList(bo.getId()));
        if (((StrUtil.equalsAnyIgnoreCase(bo.getRelateType(), BusyType.Window.name()) || bo.getBusyCode() != null) || StrUtil.isNotBlank(bo.getBusyInfo()) || StrUtil.isNotBlank(bo.getRelateType())) && StrUtil.isNotBlank(bo.getRelateType())) {
            SohuArticleRelateBo articleRelateBo = new SohuArticleRelateBo();
            articleRelateBo.setArticleId(bo.getId());
            articleRelateBo.setBusyType(bo.getRelateType());
            articleRelateBo.setBusyInfo(bo.getBusyInfo());
            Long busyCode = bo.getBusyCode();
            String busyTitle = bo.getBusyTitle();
            if (StrUtil.equalsAnyIgnoreCase(bo.getRelateType(), BusyType.Window.name())) {
                LoginUser loginUser = LoginHelper.getLoginUser();
                if (loginUser != null) {
                    busyTitle = loginUser.getNickname();
                    busyCode = loginUser.getUserId();
                }
            } else if (StrUtil.equalsAnyIgnoreCase(bo.getRelateType(), BusyType.GoodsWindow.name())) {
                LoginUser loginUser = LoginHelper.getLoginUser();
                if (loginUser != null) {
                    busyCode = loginUser.getUserId();
                }
            } else if (StrUtil.equalsAnyIgnoreCase(bo.getRelateType(), BusyType.BusyTask.name())) {
                LoginUser loginUser = LoginHelper.getLoginUser();
                if (loginUser != null) {
                    busyCode = loginUser.getUserId();
                }
            }
            articleRelateBo.setBusyCode(busyCode);
            articleRelateBo.setBusyTitle(busyTitle);
            iSohuArticleRelateService.insertByBo(articleRelateBo);
        }
    }

    /**
     * 设置MCN参数（蚁小二）
     *
     * @param entity
     * @param bo
     */
    private void setMCNParams(SohuArticle entity, SohuArticleBo bo) {
        if (CollectionUtils.isNotEmpty(bo.getDetails())) {
            //同步账号数（蚁小二）
            entity.setPlatformNum(bo.getDetails().size());
        } else {
            entity.setPlatformNum(0);
            entity.setPublishStatus(McnPublishMainStatusEnum.NoThirdParty.getCode());
        }
        infomationByLoginId(entity);
    }

    /**
     * 通过登录id赋予mcn信息
     *
     * @param entity
     */
    private void infomationByLoginId(SohuArticle entity) {
        LambdaQueryWrapper<SohuMcnUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuMcnUser::getUserId, LoginHelper.getUserId()).in(SohuMcnUser::getState, McnUserStateEnum.NORMAL.getCode(), McnUserStateEnum.DISABLED.getCode());
        SohuMcnUserVo sohuMcnUserVo = sohuMcnUserMapper.selectVoOne(wrapper);
        if (Objects.nonNull(sohuMcnUserVo)) {
            entity.setMcnUserId(sohuMcnUserVo.getMcnUserId());
        }
    }

    /**
     * 修改图文主体
     */
    @Override
    public Boolean updateByBo(SohuArticleBo bo) {
        //SohuArticleVo sohuArticleVo = baseMapper.selectVoById(bo.getId());
        SohuArticle sohuArticleVo = baseMapper.selectById(bo.getId());
        validateArticleExists(sohuArticleVo);
        if (!Objects.equals(LoginHelper.getUserId(), sohuArticleVo.getUserId())) {
            throw new RuntimeException("非法操作,这不是您的数据");
        }
        SohuArticle update = BeanUtil.toBean(bo, SohuArticle.class);
        String state = StrUtil.isBlankIfStr(bo.getState())
                ? CommonState.WaitApprove.name()
                : (StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.Edit.name())
                ? CommonState.Edit.name()
                : bo.getState());
        if (!(Objects.equals(CommonState.Refuse.name(), sohuArticleVo.getState())
                || Objects.equals(CommonState.Edit.name(), sohuArticleVo.getState())
                || Objects.equals(CommonState.CompelOff.name(), sohuArticleVo.getState())
                || Objects.equals(CommonState.OffShelf.name(), sohuArticleVo.getState()))) {
            throw new RuntimeException("此状态不支持修改");
        }
        bo.setState(state);
        update.setState(state);

        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
            if (sohuArticleVo.getSubmitNum().compareTo(SUBMIT_NUM_MAX_VALUE) > -1) {
                throw new RuntimeException("您已提交" + sohuArticleVo.getSubmitNum() + "次，最大提交次数为" + SUBMIT_NUM_MAX_VALUE);
            }
            fieldChangeChecker(bo, sohuArticleVo);
            update.setSubmitTime(new Date());
            if (sohuArticleVo.getSubmitNum() > 0) {
                update.setSubmitScene("下架整改");
            } else {
                update.setSubmitScene("自主发布");
            }
            update.setSubmitNum(sohuArticleVo.getSubmitNum() + 1);
            update.setAppealStatus(false);
//            String scanText = aliyunAuditService.scanText(HtmlUtil.cleanHtmlTag(bo.getTitle() + bo.getContent()), AliyunAuditLabelEnum.textCheck);
//            if (StrUtil.isNotBlank(scanText)) {
//                // 保存审核记录
//                if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
//                    this.initCreateAudited(update);
//                }
            update.setState(CommonState.WaitApprove.getCode());
            update.setAuditState(CommonState.WaitApprove.getCode());
//                List<String> labels = parseLabelsFromJson(scanText);
//                // 匹配枚举并获取描述信息
//                List<String> descriptions = labels.stream()
//                        .map(AliyunAuditLabelEnum::getDescriptionByLabel)
//                        .filter(Optional::isPresent)
//                        .map(Optional::get)
//                        .collect(Collectors.toList());
//                String rejectReason = String.join("; ", descriptions);
//                update.setRejectReason(rejectReason);
//                log.warn("【{}】标题或内容违规，审核结果：{}", update.getId(), scanText);
//            } else {
//                update.setState(CommonState.OnShelf.name());
//                update.setAuditTime(new Date());
//                update.setAuditState(CommonState.Pass.name());
//            }
        } else if (StrUtil.equalsAnyIgnoreCase(state, CommonState.Edit.name())) {
            update.setAuditState(null);
        }

//        update.setSiteId(sohuArticleVo.getSiteId());
        update.setUserId(sohuArticleVo.getUserId());
        validEntityBeforeSave(update);
        Long articleId = bo.getId();
        // 保存关联项
        addArticleRelate(bo);
        // 更新内容
        iSohuArticleInfoService.updateByArticleId(articleId, bo.getContent(), bo.getLearnNum());
        // 同步图文
        sohuSyncContentService.sync(update);
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name()) || StrUtil.equalsAnyIgnoreCase(state, CommonState.Edit.name())) {
            LambdaUpdateWrapper<SohuArticle> luw = new LambdaUpdateWrapper();
            luw.eq(SohuArticle::getId, update.getId());
            luw.set(SohuArticle::getAppealReason, null);
            luw.set(SohuArticle::getRejectReason, update.getRejectReason());
            luw.set(SohuArticle::getAuditState, update.getAuditState());
            update.setAppealReason(null);
            //update.setRejectReason(null);
            baseMapper.updateByIdThenEviction(update, luw);
        } else {
//            update.setState(null);
//            update.setAuditState(null);
            baseMapper.updateById(update);
        }
        SohuContentLifecycleBo lifecycleBo = new SohuContentLifecycleBo(sohuArticleVo.getId(), BusyType.Article.name(), sohuArticleVo.getState(), update.getState(), "提交审核");
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
            this.initCreateAudited(update, true);
            // 发布异步机审消息
            SohuRiskMqBo riskMqBo = new SohuRiskMqBo();
            riskMqBo.setBusyType(BusyType.Article);
            riskMqBo.setBusyCode(update.getId().toString());
            MqMessaging mqMessaging = new MqMessaging(JSONObject.toJSONString(riskMqBo), MqKeyEnum.HANDLE_RISK_MANAGEMENT.getKey());
            remoteStreamMqService.sendDelayMsg(mqMessaging, 2L);
        } else {
            lifecycleBo.setRemark("编辑");
        }
        this.createLifecycleLog(lifecycleBo);
        //新增图文物料
        this.addSohuAirecContentItem(update, bo);
        return true;
    }

    private void fieldChangeChecker(SohuArticleBo bo, SohuArticle entity) {
        if (StrUtil.equalsAnyIgnoreCase(entity.getState(), CommonState.Edit.name())) {
            return;
        }
        SohuContentLifecycleVo lifecycleVo = this.sohuContentLifecycleService.selectOfLast(entity.getId(), BusyType.Article.name());
        if (Objects.nonNull(lifecycleVo) && Objects.equals(entity.getState(), lifecycleVo.getLastState()) && Objects.equals(entity.getState(), lifecycleVo.getCurrentState())) {
            return;
        }
        SohuArticleBo oldValue = BeanUtil.copyProperties(entity, SohuArticleBo.class);
        SohuArticleInfoVo infoVo = iSohuArticleInfoService.queryByArticleId(entity.getId());
        oldValue.setContent(Objects.nonNull(infoVo) ? infoVo.getContent() : null);
        if (!SohuFieldChangeUtil.hasFieldChanged(oldValue, bo)) {
            throw new RuntimeException("请修改后再提交");
        }
    }

    /**
     * 从 JSON 数据中提取所有的 label 字段
     */
    private static List<String> parseLabelsFromJson(String jsonString) {
        List<String> labels = new ArrayList<>();
        JSONArray jsonArray = JSON.parseArray(jsonString);

        for (Object obj : jsonArray) {
            JSONObject jsonObject = (JSONObject) obj;
            String label = jsonObject.getString("label");
            if (label != null) {
                labels.add(label);
            }
        }
        return labels;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuArticle entity) {
        // TODO 做一些数据校验,如唯一约束
        entity.setUpdateTime(new Date());
        if (entity.getId() == null || entity.getId() < 1L) {
            entity.setCreateTime(new Date());
        }
        infomationByLoginId(entity);
        //校验当前操作id是否是作者
        if (!entity.getUserId().equals(LoginHelper.getUserId())) {
            throw new ServiceException(MessageUtils.message("no.power"));
        }
    }

    /**
     * 批量删除图文主体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        for (Long id : ids) {
            // 删除万能表数据
            iSohuContentMainService.deleteByObj(id, BusyType.Article.name());
            // 删除审核数据
            iSohuAuditService.deleteByObj(id, BusyType.Article.name());
            iSohuAirecContentItemService.updateStatusToOffShelf(id.toString(), AliyunAirecContentItemTypeEnum.ARTICLE.getCode());
        }
        // 删除关联数据
        iSohuArticleRelateService.deleteByArticleIds(ids);
        // 删除内容
        iSohuArticleInfoService.deleteByArticleIds(ids);
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 图文草稿提交至审核
     *
     * @param articleId 图文ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void commit(Long articleId) {
        SohuArticle sohuArticle = baseMapper.selectById(articleId);
        validateArticleExists(sohuArticle);
        sohuArticle.setState(CommonState.WaitApprove.name());
        this.baseMapper.updateById(sohuArticle);
        // 同步至万能表
        sohuSyncContentService.sync(sohuArticle);
        // 同步至审核
        this.initCreateAudited(sohuArticle);
    }

    @Override
    public Boolean submitAudit(Long id) {
        SohuArticle sohuArticle = baseMapper.selectById(id);
        validateArticleExists(sohuArticle);
        SohuArticleBo bo = BeanUtil.copyProperties(sohuArticle, SohuArticleBo.class);
        bo.setState(CommonState.WaitApprove.name());
        SohuArticleInfoVo infoVo = iSohuArticleInfoService.queryByArticleId(id);
        if (Objects.nonNull(infoVo)) {
            bo.setContent(infoVo.getContent());
            bo.setLearnNum(infoVo.getLearnNum());
        }
        return this.updateByBo(bo);
    }

    private void validateArticleExists(SohuArticleVo articleVo) {
        if (Objects.isNull(articleVo)) {
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
    }

    private void validateArticleExists(SohuArticle articleVo) {
        if (Objects.isNull(articleVo)) {
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
    }

    /**
     * 提交至审核
     *
     * @param sohuArticle 图文对象
     */
    private void initCreateAudited(SohuArticle sohuArticle) {
        this.initCreateAudited(sohuArticle, false);
    }

    /**
     * 提交至审核
     *
     * @param sohuArticle 图文对象
     */
    private void initCreateAudited(SohuArticle sohuArticle, Boolean isAiAudit) {
        SohuArticleBo bo = BeanUtil.toBean(sohuArticle, SohuArticleBo.class);
        sohuAuditInitService.initCreateAudited(bo, isAiAudit);
    }

    @Override
    public Long getMcnArticlePraiseStat(Long userId, Long mcnId) {
        // 获取图文集合
        List<SohuArticleInfo> articleInfoList = this.getArticleInfoList(userId, mcnId);
        // 汇总图文点赞数
        Long totalArticlePraiseCount = articleInfoList.stream().mapToLong(SohuArticleInfo::getPraiseCount).sum();

        return Optional.of(totalArticlePraiseCount).orElse(DEFAULT_STAT_VALUE);
    }

    @Override
    public Long getMcnArticleCommentStat(Long userId, Long mcnId) {
        // 获取图文集合
        List<SohuArticleInfo> articleInfoList = this.getArticleInfoList(userId, mcnId);
        // 汇总图文评论数
        Long totalArticleCommentCount = articleInfoList.stream().mapToLong(SohuArticleInfo::getCommentCount).sum();

        return Optional.of(totalArticleCommentCount).orElse(DEFAULT_STAT_VALUE);
    }

    @Override
    public Long getMcnArticleViewStat(Long userId, Long mcnId) {
        // 获取图文集合
        List<SohuArticleInfo> articleInfoList = this.getArticleInfoList(userId, mcnId);
        // 汇总图文浏览数
        Long totalArticleViewStat = articleInfoList.stream().mapToLong(SohuArticleInfo::getViewCount).sum();

        return Optional.of(totalArticleViewStat).orElse(DEFAULT_STAT_VALUE);
    }

    /**
     * 赚钱图文列表
     *
     * @param bo
     * @return
     */
    @Override
    public IPage<SohuArticleVo> businessArticleList(SohuBusinessArticleBo bo) {
        Page page = new Page(bo.getPageNum(), bo.getPageSize());
        IPage<SohuArticleVo> result = baseMapper.businessArticleList(page, bo);
//        List<SohuArticleVo> records = result.getRecords();
//        if (CollUtil.isNotEmpty(records)) {
//            Set<Long> userIds = new HashSet<>();
//            Set<Long> videoIds = new HashSet<>();
//            records.forEach(p -> {
//                userIds.add(p.getUserId());
//                videoIds.add(p.getId());
//            });
//            List<SohuArticleInfo> infoList = this.sohuArticleInfoMapper.selectList(SohuArticleInfo::getArticleId, videoIds);
//            Map<Long, SohuArticleInfo> infoMap = infoList.stream().collect(Collectors.toMap(SohuArticleInfo::getArticleId, u -> u));
//            Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
//            for (SohuArticleVo vo : result.getRecords()) {
//                LoginUser user = userMap.get(vo.getUserId());
//                if (Objects.nonNull(user)) {
//                    vo.setUserAvatar(user.getAvatar());
//                    vo.setUserName(user.getUsername());
//                    vo.setNickName(user.getNickname());
//                }
//                SohuArticleInfo info = infoMap.get(vo.getId());
//                if (Objects.isNull(info)) {
//                    continue;
//                }
//                vo.setViewCount(info.getViewCount());
//                vo.setCommentCount(info.getCommentCount());
//                vo.setPraiseCount(info.getPraiseCount());
//                this.buildRelate(vo);
//            }
//            result.setRecords(records);
//        }
        //this.buildArticleVo(result.getRecords());
        result.setRecords(this.buildArticleVo(result.getRecords()));
        return result;
    }

    /**
     * 构建返回参数，存在重复代码，请重构
     *
     * @param records
     */
    private List<SohuArticleVo> buildArticleVo(List<SohuArticleVo> records) {
        if (CollUtil.isEmpty(records)) {
            return records;
        }
        // 过滤掉下架的图文
        records = records.stream().filter(item -> StrUtil.equalsAnyIgnoreCase(item.getState(), CommonState.OnShelf.getCode(), CommonState.Pass.getCode())).collect(Collectors.toList());
        if (CollUtil.isEmpty(records)) {
            return records;
        }
        Set<Long> userIds = new HashSet<>();
        Set<Long> videoIds = new HashSet<>();
        records.forEach(p -> {
            userIds.add(p.getUserId());
            videoIds.add(p.getId());
        });
        List<SohuArticleInfo> infoList = this.sohuArticleInfoMapper.selectList(SohuArticleInfo::getArticleId, videoIds);
        Map<Long, SohuArticleInfo> infoMap = infoList.stream().collect(Collectors.toMap(SohuArticleInfo::getArticleId, u -> u));
        Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
        for (SohuArticleVo vo : records) {
            LoginUser user = userMap.get(vo.getUserId());
            if (Objects.nonNull(user)) {
                vo.setUserAvatar(user.getAvatar());
                vo.setUserName(user.getUsername());
                vo.setNickName(user.getNickname());
            }
            SohuArticleInfo info = infoMap.get(vo.getId());
            if (Objects.isNull(info)) {
                continue;
            }
            vo.setViewCount(info.getViewCount());
            vo.setCommentCount(info.getCommentCount());
            vo.setPraiseCount(info.getPraiseCount());
            this.buildRelate(vo);
        }
        return records;
    }

    @Override
    public TableDataInfo<SohuArticleVo> businessArticleListOfAirec(SohuBusinessArticleBo bo) {
        if (bo.getAiRec()) {
            if (StrUtil.isBlank(bo.getAiRecImei()) && Objects.isNull(LoginHelper.getUserId())) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (!(AliyunAirecConstant.SCENE_ARTICLE_ALL.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_ARTICLE_HOMEPAGE.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_ARTICLE_MONEYMAKING.equals(bo.getAiRecSceneId()))) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (Objects.nonNull(LoginHelper.getUserId())) {
                bo.setAiUserId(LoginHelper.getUserId().toString());
            }
            bo.setAiReturnCount(bo.getPageSize());
            //构造过滤的内容
            SohuAirecArticleQueryBo airecQueryBo = new SohuAirecArticleQueryBo();
            if (Objects.nonNull(bo.getSiteId())) {
                SohuSiteVo sohuSiteVo = siteService.queryById(bo.getSiteId());
                if (Objects.nonNull(sohuSiteVo)) {
                    if (Objects.equals(sohuSiteVo.getType(), SiteType.City.name())) {
                        airecQueryBo.setCity(String.valueOf(sohuSiteVo.getId()));
                    } else if (Objects.equals(sohuSiteVo.getType(), SiteType.Country.name())) {
                        airecQueryBo.setCountry(sohuSiteVo.getCountryCode());
                    }
                }
            }
            //airecQueryBo.setType(bo.getType());
            AliyunAirecJoinFilterRule rootRule = airecQueryBo.buildAirecFilterRule();
            //this.buildAirecJoinFilterRule(bo.getSiteId(), bo.getCategoryId(), rootRule);
            //获取阿里云智能推荐结果
            List<SohuArticleVo> resultList = AliyunAirecUtil.aiRecommendSingleType(rootRule, bo, itemIds -> baseMapper.selectVoBatchIds(itemIds));
            if (CollUtil.isNotEmpty(resultList)) {
                //getRecord(resultList, LoginHelper.getUserId());
                //this.buildArticleVo(resultList);
                resultList = this.buildArticleVo(resultList);
                return TableDataInfoUtils.build(resultList);
            }
        }
        IPage<SohuArticleVo> page = this.businessArticleList(bo);
        AliyunAirecUtil.buildAiRecommendSingleType(page.getRecords(), AliyunAirecContentItemTypeEnum.ARTICLE.getCode());
        return TableDataInfoUtils.build(page);
    }

    /**
     * 获取图文实体集合
     *
     * @param userId 当前用户id
     * @param mcnId  MCN机构id
     * @return List
     */
    private List<SohuArticleInfo> getArticleInfoList(Long userId, Long mcnId) {
        LambdaQueryWrapper<SohuArticle> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuArticle::getUserId, userId).eq(SohuArticle::getMcnUserId, mcnId);
        // 获取图文id集合
        List<SohuArticle> articleList = baseMapper.selectList(lqw);
        if (CollectionUtils.isEmpty(articleList)) {
            return new ArrayList<>();
        }
        List<Long> articleIds = articleList.stream().map(SohuArticle::getId).collect(Collectors.toList());

        LambdaQueryWrapper<SohuArticleInfo> infoWrapper = Wrappers.lambdaQuery();
        infoWrapper.in(SohuArticleInfo::getArticleId, articleIds);
        // 通过图文id获取图文集合
        List<SohuArticleInfo> articleInfoList = sohuArticleInfoMapper.selectList(infoWrapper);
        return articleInfoList;
    }

    @Override
    public void buildRelate(SohuArticleVo articleVo) {
        if (Objects.isNull(articleVo)) {
            return;
        }
        // 关联项设置
        SohuArticleRelateVo relateVo = iSohuArticleRelateService.getByArticleId(articleVo.getId());
        if (Objects.isNull(relateVo)) {
            return;
        }
        if (StrUtil.equalsAnyIgnoreCase(relateVo.getBusyType(), BusyType.GoodsWindow.name(), BusyType.Window.name(),
                BusyType.BusyTask.name(), BusyType.EntryRole.name(), BusyType.SohuLesson.name())) {
            articleVo.setRelation(buildRelationRespVo(relateVo));
        } else if (StrUtil.equalsAnyIgnoreCase(relateVo.getBusyType(), BusyType.Video.name())) {
            SohuVideoVo videoVo = sohuVideoService.queryById(relateVo.getBusyCode());
            RelationRespVo relation = new RelationRespVo();
            if (Objects.isNull(videoVo)) {
                return;
            }
            relation.setBusyCode(relateVo.getBusyCode());
            relation.setBusyType(BusyType.Video.name());
            relation.setBusyTitle(videoVo.getTitle());
            relation.setBusyBelonger(videoVo.getUserId());
            relation.setBusyCoverImg(videoVo.getCoverImage());
            relation.setBusyPublishTime(DateUtil.format(videoVo.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
            buildBelonged(relation, videoVo.getUserId());
            articleVo.setRelation(relation);
        }
    }

    /**
     * 图文添加至智能推荐物料
     *
     * @param article 当前用户id
     * @return List
     */
    public void addSohuAirecContentItem(SohuArticle article, SohuArticleBo bo) {
        //上架
//        if (StringUtils.equalsAnyIgnoreCase(CommonState.OnShelf.getCode(), article.getState())
//                && Objects.equals(VisibleTypeEnum.open.getCode(), article.getVisibleType())) {
        if (StringUtils.equalsAnyIgnoreCase(CommonState.OnShelf.getCode(), article.getState())) {
            SohuAirecContentItemBo model = buildAirecContentItemModel(article, StrUtil.isNotBlank(bo.getRelateType()));
            model.setTags(iSohuAirecTagRelationService.saveTagStr(article.getId(), AiRecTag.BizTypeEnum.ARTICLE.getCode(), model.getTags()));
            iSohuAirecContentItemService.saveAirecContentItem(model);
        }
    }

    @Override
    public void updateSohuAirecContentArticleItem(SohuArticle article) {
//        if (!Objects.equals(VisibleTypeEnum.open.getCode(), article.getVisibleType())) {
//            return;
//        }
        //上架
        if (StringUtils.equalsAnyIgnoreCase(CommonState.OnShelf.getCode(), article.getState())) {
            // 关联项
            SohuArticleRelate relate = sohuArticleRelateMapper.selectOne(SohuArticleRelate::getArticleId, article.getId());
            SohuAirecContentItemBo model = buildAirecContentItemModel(article, Objects.nonNull(relate));
            model.setTags(iSohuAirecTagRelationService.saveTagStr(article.getId(), AiRecTag.BizTypeEnum.ARTICLE.getCode(), model.getTags()));
            iSohuAirecContentItemService.saveAirecContentItem(model);
        } else {
            //下架
            iSohuAirecContentItemService.updateStatusToOffShelf(article.getId().toString(), AliyunAirecContentItemTypeEnum.ARTICLE.getCode());
        }

    }

    /**
     * 创建智能推荐物料数据
     *
     * @param article
     * @param hasRelate 存在关联
     * @return
     */
    public SohuAirecContentItemBo buildAirecContentItemModel(SohuArticle article, Boolean hasRelate) {
        SohuAirecContentItemBo model = new SohuAirecContentItemBo();
        model.setItemId(String.valueOf(article.getId()));
        model.setItemType(AliyunAirecContentItemTypeEnum.ARTICLE.getCode());
        //model.setSceneId(AliyunAirecConstant.SCENE_ARTICLE_HOMEPAGE);
        Date createTime = article.getCreateTime();
        long time = DateUtils.getTimeOfSecond(createTime);
        model.setPubTime(String.valueOf(time));
        model.setTitle(article.getTitle());
        model.setWeight(AliyunAirecItemWeightEnum.NOT_WEIGHT.getCode());
        model.setAuthor(article.getUserId().toString());
        model.setContent(StringUtils.isEmpty(article.getDigest()) ? article.getTitle() : StringUtils.substring(article.getDigest(), AliyunAirecConstant.CONTENT_SUBSTRING));
        model.setCity(String.valueOf(article.getSiteId()));
        SohuAirecContentItemVo airecCategoryInfo = categoryService.getAirecCategoryInfoById(article.getCategoryId());
        model.setCategoryPath(airecCategoryInfo.getCategoryPath());
        model.setCategoryLevel(airecCategoryInfo.getCategoryLevel());
        // TODO
        String code = siteService.selectCountryCodeById(article.getCountrySiteId());
        if (StrUtil.isNotBlank(code)) {
            model.setCountry(code);
        }
        //model.setStatus(AirecItemStatusEnum.CONTENT_YES.getCode());
        if (StringUtils.equalsAnyIgnoreCase(CommonState.OnShelf.getCode(), article.getState())
                && Objects.equals(VisibleTypeEnum.open.getCode(), article.getVisibleType())) {
            model.setStatus(AirecItemStatusEnum.CONTENT_YES.getCode());
        } else {
            model.setStatus(AirecItemStatusEnum.CONTENT_NO.getCode());
        }
        //贴标签
        this.buildTagsAndScene(article, hasRelate, model);
        return model;
    }

    /**
     * 贴标签和场景
     *
     * @param article
     * @param hasRelate 存在关联
     * @param model
     */
    private void buildTagsAndScene(SohuArticle article, Boolean hasRelate, SohuAirecContentItemBo model) {
        //标签
        Set<String> tagSet = new HashSet<>();
        //场景
        Set<String> sceneIdSet = new HashSet<>();
        //作品类型标签
        if (Objects.equals(VideoEnum.Type.general.getCode(), article.getType())) {
            tagSet.add(AliyunAirecTagsConstant.MEDIA_TYPE_GENERAL);
        } else if (Objects.equals(VideoEnum.Type.lesson.getCode(), article.getType())) {
            tagSet.add(AliyunAirecTagsConstant.MEDIA_TYPE_LESSON);
        }
        //关联标签
        if (hasRelate) {
            tagSet.add(AliyunAirecTagsConstant.MEDIA_HAS_RELATE);
            sceneIdSet.add(AliyunAirecConstant.SCENE_ARTICLE_MONEYMAKING);
        } else {
            sceneIdSet.add(AliyunAirecConstant.SCENE_ARTICLE_HOMEPAGE);
        }
        sceneIdSet.add(AliyunAirecConstant.SCENE_ARTICLE_ALL);
        model.setTags(String.join(AliyunAirecConstant.CONNECTED_COMMA, tagSet));
        model.setSceneId(String.join(AliyunAirecConstant.CONNECTED_COMMA, sceneIdSet));
    }

    @Override
    public Boolean initAirecContentItems() {
        //上架
//        LambdaQueryWrapper<SohuArticle> lqw = new LambdaQueryWrapper<>();
//        lqw.eq(SohuArticle::getState, CommonState.OnShelf.name())
//                .eq(SohuArticle::getVisibleType, VisibleTypeEnum.open.getCode());
        //设置分页参数
        // 查询总数
//        long total = baseMapper.selectCount(lqw);
        long total = baseMapper.selectCount();
        final int PAGE_SIZE = AliyunAirecConstant.BATCH_SIZE;
        // 总页数
        long totalPages = (total + PAGE_SIZE - 1) / PAGE_SIZE;
        for (int i = 1; i <= totalPages; i++) {
            // 分页查询
            Page<SohuArticle> page = new Page<>(i, PAGE_SIZE);
            log.info("初始化{} 第{}/{}", AliyunAirecContentItemTypeEnum.ARTICLE, i, PAGE_SIZE);
//            IPage<SohuArticle> pageResult = baseMapper.selectPage(page, lqw);
            IPage<SohuArticle> pageResult = baseMapper.selectPage(page, null);
            List<SohuArticle> list = pageResult.getRecords();
            // 处理查询结果
            if (CollUtil.isNotEmpty(list)) {
                //关联项
                List<Long> idList = list.stream().map(p -> p.getId()).collect(Collectors.toList());
                List<SohuArticleRelate> relateList = this.sohuArticleRelateMapper.selectList(SohuArticleRelate::getArticleId, idList);
                //存在关联项的id集合
                Set<Long> idSet = new HashSet<>();
                if (CollUtil.isNotEmpty(relateList)) {
                    idSet = relateList.stream().map(p -> p.getArticleId()).collect(Collectors.toSet());
                }
                //物料信息记录
                List<SohuAirecContentItemBo> modelList = new ArrayList<>();
                for (SohuArticle entity : list) {
                    SohuAirecContentItemBo model = buildAirecContentItemModel(entity, idSet.contains(entity.getId()));
                    model.setTags(iSohuAirecTagRelationService.saveTagStr(entity.getId(), AiRecTag.BizTypeEnum.ARTICLE.getCode(), model.getTags()));
                    modelList.add(model);
                }
                //保存物料信息
                iSohuAirecContentItemService.initAirecContentItems(modelList);
            }
        }
        return true;
    }

    /**
     * 构造图文其他信息,存在重复代码，请重构
     */
    public List<SohuArticleVo> getRecord(List<SohuArticleVo> records, Long userId) {
        Set<Long> ossIds = new HashSet<>();
        Set<Long> userIds = new HashSet<>();
        List<Long> busyCodes = new ArrayList<>();
        for (SohuArticleVo record : records) {
            if (NumberUtil.isNumber(record.getCoverImage())) {
                ossIds.add(Long.valueOf(record.getCoverImage()));
            }
            userIds.add(record.getUserId());
            busyCodes.add(record.getId());
        }
        records = records.stream().filter(item -> StrUtil.equalsAnyIgnoreCase(item.getState(), CommonState.OnShelf.getCode(), CommonState.Pass.getCode(), CommonState.Hide.getCode(), CommonState.CompelOff.getCode())).collect(Collectors.toList());
        if (CollUtil.isEmpty(records)) {
            return records;
        }
        Map<Long, String> map = remoteFileService.map(ossIds);
        Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
        Map<Long, SohuArticleInfoVo> infoMap = iSohuArticleInfoService.queryMap(busyCodes);
        Map<Long, SohuUserLikeVo> likeMap;
        if (userId != null && userId > 0L) {
            likeMap = sohuUserLikeService.queryMap(userId, BusyType.Article.name(), busyCodes);
        } else {
            likeMap = new HashMap<>();
        }
        records.forEach(record -> {
            if (StrUtil.isNotBlank(record.getCoverImage())) {
                if (NumberUtil.isNumber(record.getCoverImage())) {
                    record.setCoverUrl(map.get(Long.valueOf(record.getCoverImage())));
                    record.setCoverImage(record.getCoverUrl());
                } else {
                    record.setCoverUrl(record.getCoverImage());
                }
            }
            LoginUser user = userMap.get(record.getUserId());
            if (Objects.nonNull(user)) {
                record.setUserName(StrUtil.isNotBlank(user.getNickname()) ? user.getNickname() : user.getUsername());
                record.setNickName(user.getNickname());
                record.setUserAvatar(user.getAvatar());
            }
            if (userId != null && userId > 0L) {
                record.setPraiseObj(Objects.nonNull(likeMap.get(record.getId())));
            }
            SohuArticleInfoVo articleInfoVo = infoMap.get(record.getId());
            if (Objects.nonNull(articleInfoVo)) {
                record.setPraiseCount(articleInfoVo.getPraiseCount());
                record.setLearnNum(articleInfoVo.getLearnNum());
            }
        });
        return records;
    }

    @Override
    public Long getUserIdByPublishMediaId(String publishMediaId) {
        LambdaQueryWrapper<SohuArticle> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuArticle::getPublishMediaId, publishMediaId);
        SohuArticle sohuArticle = baseMapper.selectOne(wrapper);
        if (Objects.isNull(sohuArticle)) {
//            throw new RuntimeException("暂时没有这条数据:" + publishMediaId);
            return null;
        }
        return sohuArticle.getUserId();
    }

    /**
     * 返回每个标签下前五的图文
     * 双层数组结构返回
     */
    @Override
    public List<SohuTopArticleVo> labelTopFive() {
        SohuLessonLabelBo sohuLessonLabelBo = new SohuLessonLabelBo();
        sohuLessonLabelBo.setType(BusyType.Article.name());
        List<SohuLessonLabelVo> sohuLessonLabelVos = sohuLessonLabelService.queryList(sohuLessonLabelBo);
        if (CollUtil.isEmpty(sohuLessonLabelVos)) {
            return null;
        }
        List<SohuTopArticleVo> data = new ArrayList<>();
        for (SohuLessonLabelVo label : sohuLessonLabelVos) {
            SohuTopArticleVo vo = new SohuTopArticleVo();
            List<SohuArticleVo> sohuArticleVos = hotList(label.getId());
            if (CollUtil.isNotEmpty(sohuArticleVos)) {
                vo.setLessonLabelId(label.getId());
                vo.setLabelName(label.getName());
                vo.setList(sohuArticleVos);
                data.add(vo);
            }
        }
        return data;
    }

    @Override
    public TableDataInfo<SohuArticleVo> articlePageCenterByType(SohuArticleBo bo, PageQuery pageQuery) {
        Long loginUserId = LoginHelper.getUserId();
        SohuArticleBo dto = new SohuArticleBo();
        dto.setUserId(loginUserId);
        List<String> states = new ArrayList<>();
        states.add(BusyOrderStatus.OnShelf.name());
        dto.setStateList(states);
        dto.setType(bo.getType());
        if (StrUtil.isNotBlank(bo.getTitle())) {
            dto.setTitle(bo.getTitle());
        }
        if (dto.getUserId() == null) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        return queryPage(dto, pageQuery);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean comment(SohuCommentBo bo, Boolean commentCountAdd) {
        SohuArticleInfo info = iSohuArticleInfoService.queryEntityByArticleId(bo.getBusyCode());
        if (Objects.isNull(info) || info.getId() == null) {
            return false;
        }
        SohuArticleVo articleVo = this.baseMapper.selectVoById(bo.getBusyCode());
        if (BooleanUtil.isTrue(commentCountAdd)) {
            info.setCommentCount(info.getCommentCount() + 1);
        }
        iSohuArticleInfoService.updateById(info);
        SohuContentMain sohuContentMain = iSohuContentMainService.getEntityByObj(bo.getBusyCode(), BusyType.Article.name());
        if (Objects.nonNull(sohuContentMain)) {
            sohuContentMain.setCommentCount(info.getCommentCount());
            iSohuContentMainService.updateById(sohuContentMain);
        }
        bo.setTopType(BusyType.Article.name());
        bo.setTopCode(info.getArticleId());
        bo.setBusyCoverImage(articleVo.getCoverImage());
        bo.setBusyType(BusyType.Article.name());
        bo.setBusyUser(articleVo.getUserId());

        if (bo.getTopReplyUser() != null && bo.getTopReplyUser() > 0L) {
            SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildComment(bo, bo.getReplyUser(), NoticeInteractEnum.commentReceive.name());
            sohuInteractNoticeService.insertByBo(interactNoticeReceive);
        } else {
            if (!Objects.equals(bo.getReplyUser(), bo.getOperateUser())) {
                // 判断是不是自己回复自己的评论
                SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildComment(bo, bo.getReplyUser(), NoticeInteractEnum.commentReceive.name());
                sohuInteractNoticeService.insertByBo(interactNoticeReceive);
            }

            // 操作者收到
            SohuInteractNoticeBo interactNoticeSend = SohuInteractNoticeBo.buildComment(bo, bo.getOperateUser(), NoticeInteractEnum.commentSend.name());
            sohuInteractNoticeService.insertByBo(interactNoticeSend);

        }

        // 删除图文的拓展数据缓存
        iSohuArticleInfoService.evictArticleId(bo.getBusyCode());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean like(SohuBusyBO bo) {
        SohuArticleInfo info = iSohuArticleInfoService.queryEntityByArticleId(bo.getBusyCode());
        if (Objects.isNull(info) || info.getId() == null) {
            return false;
        }
        log.info("图文点赞:{}", JSONUtil.toJsonStr(bo));
        Integer oldCount = info.getPraiseCount();
        int count = bo.getIsAdd() != null && bo.getIsAdd() ? oldCount + 1 : Math.max((oldCount - 1), 0);
        info.setPraiseCount(count);

        bo.setTopCode(info.getArticleId());
        bo.setTopType(BusyType.Article.name());

        // 更新万能表的点赞数量
        sohuContentMainMapper.setPraiseCount(bo.getBusyCode(), BusyType.Article.name(), count);
        // 增加点赞
        if (bo.getIsAdd() != null && bo.getIsAdd()) {
            SohuArticleVo articleVo = baseMapper.selectVoById(bo.getBusyCode());
            bo.setSourceUser(articleVo.getUserId());
            bo.setOperateUser(bo.getOperateUser());
            bo.setBusyCoverImage(articleVo.getCoverImage());
            bo.setBusyUser(articleVo.getUserId());
            SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildBusy(bo, articleVo.getUserId(), NoticeInteractEnum.like.name());
            interactNoticeReceive.setContent(String.format(NoticeInteractEnum.likeContent, "图文"));
            sohuInteractNoticeService.insertByBo(interactNoticeReceive);
            // 图文点赞埋点
            buildArticleEventRecord(RecreationReportEnum.TWDZ, bo.getOperateUser());
        }
        // 删除图文的拓展数据缓存
        iSohuArticleInfoService.evictArticleId(bo.getBusyCode());
        return iSohuArticleInfoService.updateById(info);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean collect(SohuBusyBO bo) {
        SohuArticleInfo info = iSohuArticleInfoService.queryEntityByArticleId(bo.getBusyCode());
        if (Objects.isNull(info) || info.getId() == null) {
            return false;
        }
        log.info("收藏图文：{}", JSONUtil.toJsonStr(bo));
        Integer oldCount = info.getCollectCount();
        int count = bo.getIsAdd() ? oldCount + 1 : Math.max((oldCount - 1), 0);
        info.setCollectCount(count);
        boolean result = iSohuArticleInfoService.updateById(info);
        // 更新万能表的收藏数量
        sohuContentMainMapper.setCollectCount(bo.getBusyCode(), BusyType.Article.name(), count);

        if (bo.getIsAdd() != null && bo.getIsAdd()) {
            SohuArticleVo articleVo = baseMapper.selectVoById(bo.getBusyCode());

            bo.setSourceUser(articleVo.getUserId());
            bo.setOperateUser(bo.getOperateUser());
            bo.setBusyCoverImage(articleVo.getCoverImage());
            bo.setBusyUser(articleVo.getUserId());
            SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildBusy(bo, articleVo.getUserId(), NoticeInteractEnum.collect.name());
            interactNoticeReceive.setContent(String.format(NoticeInteractEnum.collectContent, "图文"));
            sohuInteractNoticeService.insertByBo(interactNoticeReceive);
        }

        // 删除图文的拓展数据缓存
        iSohuArticleInfoService.evictArticleId(bo.getBusyCode());
        return result;
    }

    @Override
    public Boolean updateState(SohuBusyUpdateStateBo bo) {
        String rejectReason = bo.getRejectReason();
        Long busyCode = bo.getBusyCode();
        SohuArticle article = baseMapper.selectById(busyCode);
        if (Objects.isNull(article)) {
            return Boolean.FALSE;
        }
        article.setState(StrUtil.isBlankIfStr(bo.getState()) ? CommonState.CompelOff.getCode() : bo.getState());
        article.setRejectReason(rejectReason);
        article.setUpdateTime(new Date());
        // 更新图文
        baseMapper.updateById(article);
        // 更新万能表
        sohuContentMainMapper.updateState(article.getState(), busyCode, BusyType.Article.name());
        // 智能推送表
        updateSohuAirecContentArticleItem(article);
        return Boolean.TRUE;
    }

    @Override
    public Boolean auditOnShelf(Long id) {
        SohuArticle article = baseMapper.selectById(id);
        if (Objects.isNull(article)) {
            throw new RuntimeException("内容不存在");
        }
        if (!(Objects.equals(article.getState(), CommonState.WaitApprove.name())
                || Objects.equals(article.getState(), CommonState.Refuse.name()))) {
            throw new RuntimeException("此状态不支持上架，请检查数据");
        }
        article.setState(CommonState.OnShelf.name());
        article.setAuditState(CommonState.Pass.name());
        article.setAuditTime(new Date());
        article.setAppealStatus(false);
        article.setAppealReason(null);
        LambdaUpdateWrapper<SohuArticle> luw = new LambdaUpdateWrapper<>();
        luw.set(SohuArticle::getAppealReason, null);
        luw.set(SohuArticle::getRejectReason, null);
        luw.eq(SohuArticle::getId, id);
        this.baseMapper.update(article, luw);
        // 更新万能表
        sohuContentMainMapper.updateState(article.getState(), id, BusyType.Article.name());
        // 智能推送表
        updateSohuAirecContentArticleItem(article);
        //
        return true;
    }

    @Override
    public Boolean auditRefuse(Long id, String rejectReason) {
        SohuArticle article = baseMapper.selectById(id);
        if (Objects.isNull(article)) {
            throw new RuntimeException("内容不存在");
        }
        if (!(Objects.equals(article.getState(), CommonState.WaitApprove.name())
                || Objects.equals(article.getState(), CommonState.Refuse.name()))) {
            throw new RuntimeException("此状态不支持上架，请检查数据");
        }
        article.setState(CommonState.Refuse.name());
        article.setAuditState(CommonState.Refuse.name());
        article.setAuditTime(new Date());
        article.setRejectReason(rejectReason);
        //article.setSubmitNum(article.getSubmitNum() + 1);
        this.baseMapper.updateById(article);
        // 更新万能表
        sohuContentMainMapper.updateState(article.getState(), id, BusyType.Article.name());
        // 智能推送表
        updateSohuAirecContentArticleItem(article);
        return true;
    }

    @Override
    public Boolean userAppeal(SohuUserContentAppealBo bo) {
        SohuArticle article = baseMapper.selectById(bo.getId());
        if (Objects.isNull(article)) {
            throw new RuntimeException("内容不存在");
        }
        if (!Objects.equals(article.getState(), CommonState.Refuse.name())) {
            throw new RuntimeException("非审核拒绝状态，不支持申诉");
        }
        if (BooleanUtil.isTrue(article.getAppealStatus())) {
            throw new RuntimeException("您已申诉过，只有一次申诉机会");
        }
        SohuArticle updateEntity = new SohuArticle();
        //updateEntity.setState(CommonState.WaitApprove.name());
        updateEntity.setAppealStatus(true);
        updateEntity.setAppealReason(bo.getAppealReason());
        updateEntity.setAuditState(CommonState.WaitApprove.name());
        updateEntity.setSubmitScene("申诉复审");
        LambdaUpdateWrapper<SohuArticle> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuArticle::getId, bo.getId());
        luw.eq(SohuArticle::getState, CommonState.Refuse.name());
        this.baseMapper.update(updateEntity, luw);
        //初始化审核
        article.setAppealStatus(updateEntity.getAppealStatus());
        article.setAppealReason(updateEntity.getAppealReason());
        article.setSubmitScene(updateEntity.getSubmitScene());
        article.setState(CommonState.WaitApprove.name());
        this.initCreateAudited(article);
        return true;
    }

    @Override
    public Boolean hideDataBatch(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        for (Long id : ids) {
            this.hideData(id);
        }
        return true;
    }

    public Boolean hideData(Long id) {
        SohuArticle entity = this.baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new RuntimeException("数据不存在");
        }
        if (!Objects.equals(LoginHelper.getUserId(), entity.getUserId())) {
            throw new RuntimeException("非法操作,这不是您的数据");
        }
        if (!Objects.equals(entity.getState(), CommonState.OnShelf.name())) {
            throw new RuntimeException("未上架，不支持隐藏");
        }
        SohuArticle updateEntity = new SohuArticle();
        updateEntity.setId(id);
        updateEntity.setState(CommonState.Hide.name());
        LambdaUpdateWrapper<SohuArticle> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuArticle::getState, entity.getState());
        luw.eq(SohuArticle::getId, id);
        if (this.baseMapper.update(updateEntity, luw) > 0) {
            this.iSohuAirecContentItemService.updateStatusToOffShelf(id.toString(), AliyunAirecContentItemTypeEnum.ARTICLE.getCode());
        }
        return true;
    }

    @Override
    public Boolean draftRetry(SohuBusyBO busyBO) {
        SohuArticle article = baseMapper.selectById(busyBO.getBusyCode());
        if (Objects.isNull(article)) {
            return Boolean.FALSE;
        }
        article.setState(CommonState.WaitApprove.getCode());
        baseMapper.updateById(article);

        this.initCreateAudited(article);
        SohuContentMain contentMain = iSohuContentMainService.getEntityByObj(busyBO.getBusyCode(), BusyType.Article.name());
        if (Objects.nonNull(contentMain)) {
            contentMain.setState(CommonState.WaitApprove.getCode());
            iSohuContentMainService.updateById(contentMain);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean forward(SohuBusyBO bo) {
        SohuArticleInfoVo infoVo = iSohuArticleInfoService.queryByArticleId(bo.getBusyCode());
        if (Objects.isNull(infoVo) || infoVo.getId() == null) {
            return Boolean.FALSE;
        }
        int count = infoVo.getForwardCount() + 1;
        SohuArticleInfoBo infoBo = new SohuArticleInfoBo();
        infoBo.setId(infoVo.getId());
        infoBo.setForwardCount(count);
        iSohuArticleInfoService.updateByBo(infoBo);
        SohuContentMainBo mainBo = new SohuContentMainBo();
        mainBo.setObjId(bo.getBusyCode());
        mainBo.setObjType(BusyType.Article.name());
        mainBo.setForwardCount(count);
        iSohuContentMainService.updateByBo(mainBo);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateUserArticleState(Long userId, String state) {
        if (userId == null || userId <= 0L) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<SohuArticle> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuArticle::getUserId, userId).set(SohuArticle::getState, state);
        // 修改万能表的状态
        iSohuContentMainService.updateUserObjState(userId, BusyType.Article.name(), state);
        return baseMapper.update(new SohuArticle(), luw) > 0;
    }

    /**
     * 内容站点数
     */
    @Override
    public Long queryArticleNumBySite(Long siteId, Long userId) {
        LambdaQueryWrapper<SohuArticle> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuArticle::getUserId, userId);
        wrapper.eq(SohuArticle::getSiteId, siteId);
        wrapper.notIn(SohuArticle::getState, CommonState.Delete.name());
        return baseMapper.selectCount(wrapper);
    }

    @Override
    public Map<Long, SohuArticleVo> map(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new HashMap<>();
        }
        List<SohuArticleVo> articles = baseMapper.selectVoBatchIds(ids);
        return CollUtil.isEmpty(articles) ? new HashMap<>() : articles.stream().collect(Collectors.toMap(SohuArticleVo::getId, u -> u));
    }

    /**
     * 根据标签查排序前五的图文
     */
    private List<SohuArticleVo> hotList(Long labelId) {
        LambdaQueryWrapper<SohuArticle> lqw = Wrappers.lambdaQuery();
        if (labelId != null && labelId > 0L) {
            lqw.eq(SohuArticle::getLessonLabelId, labelId);
        }
        lqw.eq(SohuArticle::getState, CommonState.OnShelf.getCode());
        lqw.orderByAsc(SohuArticle::getSortIndex);
        lqw.orderByDesc(SohuArticle::getCreateTime);
        lqw.last("limit 5");
        List<SohuArticleVo> sohuArticleVos = baseMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(sohuArticleVos)) {
            Set<Long> userIds = sohuArticleVos.stream().filter(p -> Objects.nonNull(p.getUserId())).map(p -> p.getUserId()).collect(Collectors.toSet());
            Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
            for (SohuArticleVo sohuArticleVo : sohuArticleVos) {
                LoginUser loginUser = userMap.get(sohuArticleVo.getUserId());
                if (Objects.nonNull(loginUser)) {
                    sohuArticleVo.setUserAvatar(loginUser.getAvatar());
                    sohuArticleVo.setNickName(loginUser.getNickname());
                }
//                SohuArticleInfoVo infoVo = iSohuArticleInfoService.queryByArticleId(sohuArticleVo.getId());
                SohuArticleInfoVo infoVo = iSohuArticleInfoService.queryArticleForCount(sohuArticleVo.getId());
                // 设置内容
                if (Objects.nonNull(infoVo)) {
                    sohuArticleVo.setCollectCount(infoVo.getCollectCount());
                    sohuArticleVo.setViewCount(infoVo.getViewCount());
                    sohuArticleVo.setCommentCount(infoVo.getCommentCount());
                    sohuArticleVo.setPraiseCount(infoVo.getPraiseCount());
                    sohuArticleVo.setForwardCount(infoVo.getForwardCount());
                    sohuArticleVo.setLearnNum(infoVo.getLearnNum());
                }
            }
        }
        return sohuArticleVos;
    }


    /**
     * 设置用户头像，名称
     */
    public void buildBelonged(RelationRespVo relation, Long userId) {
        LoginUser loginUser = sohuUserService.selectById(userId);
        if (Objects.nonNull(loginUser)) {
            relation.setBelongerAvatar(loginUser.getAvatar());
            relation.setBelongerName(loginUser.getUsername());
        }
    }

    @Override
    public Boolean updateBatchContentState(SohuContentBatchBo bo) {
        // 获取需要修改的主键id
        List<String> idList = StrUtil.split(bo.getIds(), COMMA);

        List<SohuArticle> sohuArticleList = baseMapper.selectBatchIds(idList);
        // 如果查询结果为空，或者数量与传入的 id 数量不一致，返回失败
        if (sohuArticleList == null || sohuArticleList.size() != idList.size()) {
            return Boolean.FALSE;
        }
        // 校验所有作品的作者 ID 是否一致
        Long currentUserId = LoginHelper.getUserId();
        boolean isAllAuthorMatched = sohuArticleList.stream()
                .allMatch(article -> article.getUserId().equals(currentUserId));

        if (!isAllAuthorMatched) {
            return Boolean.FALSE;
        }
        // 构建需要更新的内容
        List<SohuArticle> contentList = sohuArticleList.stream()
                .map(article -> {
                    SohuArticle content = new SohuArticle();
                    content.setId(article.getId());
                    content.setState(bo.getState());
                    return content;
                })
                .collect(Collectors.toList());

        // 执行批量更新
        return baseMapper.updateBatchById(contentList);
    }

    @Override
    public Boolean recoveryData(Long id) {
        SohuArticle entity = this.baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new RuntimeException("数据不存在");
        }
        if (!Objects.equals(LoginHelper.getUserId(), entity.getUserId())) {
            throw new RuntimeException("非法操作,这不是您的数据");
        }
        if (!(Objects.equals(entity.getState(), CommonState.Delete.name())
                || Objects.equals(entity.getState(), CommonState.ForceDelete.name()))) {
            throw new RuntimeException("非审核拒绝状态，不支持申诉");
        }
        SohuArticle updateEntity = new SohuArticle();
        updateEntity.setId(id);
        LambdaUpdateWrapper<SohuArticle> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuArticle::getState, entity.getState());
        luw.eq(SohuArticle::getId, id);
        SohuContentLifecycleVo lifecycleVo = this.sohuContentLifecycleService.selectOfLast(id, BusyType.Article.name());
        if (Objects.isNull(lifecycleVo)) {
            //兼容历史数据
            updateEntity.setState(CommonState.OffShelf.name());
        } else {
            updateEntity.setState(lifecycleVo.getLastState());
        }
        return this.baseMapper.update(updateEntity, luw) > 0;
    }

    @Override
    public Boolean deleteDataById(Long id) {
        SohuArticle sohuArticle = baseMapper.selectById(id);
        if (Objects.isNull(sohuArticle)) {
            return false;
        }
        if (!Objects.equals(LoginHelper.getUserId(), sohuArticle.getUserId())) {
            throw new RuntimeException("非法操作，这不是您的数据");
        }

        return sohuContentMainMapper.flushRecycleArticle(LoginHelper.getUserId(), Collections.singleton(id));
    }

    @Override
    public Boolean clearRecycleDataOfTimeOut() {
        LambdaUpdateWrapper<SohuArticle> luw = new LambdaUpdateWrapper<>();
        luw.set(SohuArticle::getDelFlag, Constants.TWO);
        luw.in(SohuArticle::getState, CommonState.Delete.name(), CommonState.ForceDelete.name());
        luw.lt(SohuArticle::getDelTime, DateUtil.offsetDay(new Date(), -CON_RECYCLE_DATA_TIME_OUT.intValue()));
        this.baseMapper.update(new SohuArticle(), luw);
        return true;
    }

    @Override
    public List<SohuArticleVo> queryArticleByIds(List<Long> ids, String state) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<SohuArticle>().in(SohuArticle::getId, ids)
                .eq(SohuArticle::getState, state)
                .eq(SohuArticle::getDelFlag, Constants.ZERO)
                .eq(SohuArticle::getAuditState, CommonState.Pass));
    }

    @Override
    public TableDataInfo<SohuArticleVo> getTopicList(SohuTopicContentQueryBo bo, PageQuery pageQuery) {
        Page<SohuArticleVo> sohuArticleVoPage = baseMapper.getTopicList(bo, PageQueryUtils.build(pageQuery));
        if (CollUtil.isNotEmpty(sohuArticleVoPage.getRecords())) {
            getRecord(sohuArticleVoPage.getRecords(), LoginHelper.getUserId());
        }
        return TableDataInfoUtils.build(sohuArticleVoPage);
    }

    @Override
    public List<SohuArticleVo> getLessonList() {
        SohuLessonLabelBo sohuLessonLabelBo = new SohuLessonLabelBo();
        sohuLessonLabelBo.setType(BusyType.Article.name());
        List<SohuLessonLabelVo> sohuLessonLabelVos = sohuLessonLabelService.queryList(sohuLessonLabelBo);
        if (CollUtil.isEmpty(sohuLessonLabelVos)) {
            return null;
        }
        List<SohuArticleVo> articleVoList = new ArrayList<>();
        for (SohuLessonLabelVo label : sohuLessonLabelVos) {
            if (articleVoList.size() >= 3) {
                return articleVoList.subList(0, 3);
            }
            articleVoList.addAll(hotList(label.getId()));
        }
        if (CollectionUtils.isNotEmpty(articleVoList) && articleVoList.size() >= 3) {
            return articleVoList.subList(0, 3);
        }
        return articleVoList;
    }

    @Override
    public void handleRobot(String busyCode, Boolean isPass, String reason) {
        SohuAuditBo auditBo = new SohuAuditBo();
        auditBo.setBusyCode(Long.valueOf(busyCode));
        auditBo.setBusyType(BusyType.Article.getType());
        List<SohuAuditVo> auditList = iSohuAuditService.queryList(auditBo);
        if (CollectionUtils.isEmpty(auditList)) {
            return;
        }
        SohuAuditVo auditVo = auditList.stream()
                .filter(record -> record != null && CommonState.WaitApprove.getCode().equals(record.getSysAuditState()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(auditVo)) {
            return;
        }
        SohuAuditBo newAuditBo = new SohuAuditBo();
        newAuditBo.setId(auditVo.getId());
        newAuditBo.setBusyType(BusyType.Article.getType());
        newAuditBo.setState(isPass ? CommonState.OnShelf.getCode() : CommonState.Refuse.getCode());
        newAuditBo.setRejectReason(reason);
        iSohuAuditService.process(newAuditBo);
    }
}
