package com.sohu.middleService.service;


import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.vo.SohuConentListStatVo;
import com.sohu.middle.api.vo.SohuLiteratureVo;
import com.sohu.middleService.domain.SohuLiterature;

import java.util.Collection;
import java.util.List;

/**
 * 文学主体Service接口
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
public interface ISohuLiteratureService extends ISohuBaseService<SohuLiterature, SohuLiteratureVo> {

    /**
     * 查询文学主体
     */
    SohuLiteratureVo queryById(Long id);

    /**
     * 查询图文主体-返回更多信息
     *
     * @param id 主键ID
     */
    SohuLiteratureVo selectVoById(Long id);

    /**
     * 查询文学主体列表
     */
    TableDataInfo<SohuLiteratureVo> queryPageList(SohuLiteratureBo bo, PageQuery pageQuery);

    /**
     * 查询文学主体列表
     */
    List<SohuLiteratureVo> queryList(SohuLiteratureBo bo);

    /**
     * 新增文学主体
     */
    Boolean insertByBo(SohuLiteratureBo bo);

    /**
     * 修改文学主体
     */
    Boolean updateByBo(SohuLiteratureBo bo);

    /**
     * 文学草稿提交至审核
     *
     * @param literatureId ID
     */
    void commit(Long literatureId);

    /**
     * 个人中心文学列表
     */
    TableDataInfo<SohuLiteratureVo> literaturePageCenter(Long userId, String type, PageQuery pageQuery);

    /**
     * 获取对象的作者id
     *
     * @param id
     * @return
     */
    Long getAuthorId(Long id);

    /**
     * 返回文学主体
     *
     * @param id
     * @return
     */
    SohuLiteratureVo get(Long id);

    /**
     * 逻辑删除
     *
     * @param id
     * @return
     */
    Boolean logicDeleteById(Long id);

    /**
     * 评论
     *
     * @param bo
     * @return
     */
    Boolean comment(SohuCommentBo bo, Boolean commentCountAdd);

    /**
     * 点赞
     *
     * @param bo
     * @return
     */
    Boolean like(SohuBusyBO bo);

    /**
     * 收藏
     *
     * @param bo
     * @return
     */
    Boolean collect(SohuBusyBO bo);

    /**
     * 修改状态，上下架，强制下架，通过，驳回等
     *
     * @param bo
     * @return
     */
    @Deprecated
    Boolean updateState(SohuBusyUpdateStateBo bo);

    /**
     * 审核上架
     * @param id
     * @return
     */
    Boolean auditOnShelf(Long id);

    /**
     * 审核拒绝
     * @param id
     * @param rejectReason
     * @return
     */
    Boolean auditRefuse(Long id,String rejectReason);

    /**
     * 批量修改用户作品状态
     *
     * @param bo
     * @return
     */
    Boolean updateBatchContentState(SohuContentBatchBo bo);
    /**
     * 用户申述
     *
     * @param bo
     * @return
     */
    Boolean userAppeal(SohuUserContentAppealBo bo);
    /**
     * 用户自主下架
     *
     * @param id
     * @return
     */
    Boolean updateOffShelfById(Long id);
    /**
     * 审核强制下架
     *
     * @param bo
     * @return
     */
    Boolean updateCompelOffById(SohuContentRefuseBo bo);
    /**
     * 回收站恢复
     *
     * @param id
     * @return
     */
    Boolean recoveryData(Long id);

    /**
     * 回收站删除
     *
     * @param id
     * @return
     */
    Boolean deleteDataById(Long id);

    /**
     * 批量删除诗文
     *
     * @param id
     * @return
     */
    Boolean logicForceDeleteById(Long id);

    /**
     * 批量隐藏诗文
     *
     * @param ids
     * @return
     */
    Boolean hideDataBatch(Collection<Long> ids);

    /**
     * 诗文 - 统计
     *
     * @param bo
     * @return
     */
    SohuConentListStatVo queryPageListStat(SohuLiteratureBo bo);

    /**
     * 基于时间统计诗文数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    Long countByTime(String startTime, String endTime);

    /**
     * 处理机审结果
     *
     * @param busyCode
     * @param isPass
     * @param reason
     * @param busyType
     */
    void handleRobot(String busyCode, Boolean isPass, String reason, String busyType);
}
