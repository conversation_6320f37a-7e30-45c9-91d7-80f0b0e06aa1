package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.noticeContent.NoticeContentDetail;
import com.sohu.im.api.noticeContent.NoticeSystemContent;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.notice.SohuInteractNoticeBo;
import com.sohu.middle.api.bo.risk.SohuRiskMqBo;
import com.sohu.middle.api.service.notice.RemoteMiddleSystemNoticeService;
import com.sohu.middle.api.vo.*;
import com.sohu.middleService.domain.SohuContentMain;
import com.sohu.middleService.domain.SohuLiterature;
import com.sohu.middleService.domain.SohuLiteratureInfo;
import com.sohu.middleService.mapper.SohuContentMainMapper;
import com.sohu.middleService.mapper.SohuLiteratureInfoMapper;
import com.sohu.middleService.mapper.SohuLiteratureMapper;
import com.sohu.middleService.service.*;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.third.aliyun.audit.constants.AliyunAuditLabelEnum;
import com.sohu.third.aliyun.audit.service.AliyunAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.text.StrPool.COMMA;

/**
 * 文学主体Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SohuLiteratureServiceImpl extends SohuBaseServiceImpl<SohuLiteratureMapper, SohuLiterature, SohuLiteratureVo>
        implements ISohuLiteratureService {

    private final SohuLiteratureMapper baseMapper;
    private final SohuLiteratureInfoMapper sohuLiteratureInfoMapper;
    private final SohuContentMainMapper sohuContentMainMapper;
    private final ISohuLiteratureInfoService sohuLiteratureInfoService;
    private final ISohuSiteService siteService;
    private final ISohuAuditService auditService;
    private final ISohuAuditRecordService iSohuAuditRecordService;
    private final ISohuSyncContentService sohuSyncContentService;
    private final ISohuInteractNoticeService interactNoticeService;
    private final ISohuContentMainService iSohuContentMainService;
    private final ISohuUserService sohuUserService;
    private final ISohuUserLikeService sohuUserLikeService;
    private final ISohuUserFollowService sohuUserFollowService;
    private final ISohuUserCollectService sohuUserCollectService;
    private final ISohuContentLifecycleService sohuContentLifecycleService;
    private final ISohuAuditInitService sohuAuditInitService;
    @DubboReference
    private RemoteMiddleSystemNoticeService remoteMiddleSystemNoticeService;
    @Resource
    private AliyunAuditService aliyunAuditService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    /**
     * 最大可提交次数，TODO 后期改为可配置参数
     */
    private static final Integer SUBMIT_NUM_MAX_VALUE = 3;

    @Override
    public SohuLiteratureVo selectVoById(Long id) {
        SohuLiteratureVo literatureVo = baseMapper.selectVoById(id);
        this.validateLiteratureExists(literatureVo);
        SohuLiteratureInfoVo infoVo = sohuLiteratureInfoService.queryByLiteratureId(id);
        // 设置内容
        literatureVo.setContent(Objects.nonNull(infoVo) ? infoVo.getContent() : null);
        return literatureVo;
    }

    /**
     * 查询文学主体
     */
    @Override
    public SohuLiteratureVo queryById(Long id) {
        SohuLiteratureVo literatureVo = baseMapper.selectVoById(id);
        this.validateLiteratureExists(literatureVo);
        SohuLiteratureInfoVo infoVo = sohuLiteratureInfoService.queryByLiteratureId(id);
        // 设置内容
        if (Objects.nonNull(infoVo)) {
            literatureVo.setContent(infoVo.getContent());
            literatureVo.setCollectCount(infoVo.getCollectCount());
            literatureVo.setViewCount(infoVo.getViewCount());
            literatureVo.setCommentCount(infoVo.getCommentCount());
            literatureVo.setPraiseCount(infoVo.getPraiseCount());
            literatureVo.setForwardCount(infoVo.getForwardCount());
            literatureVo.setInfo(infoVo);
        }
        // 浏览量+1 只有审核通过的才+1
        if (StrUtil.equalsAnyIgnoreCase(literatureVo.getState(), CommonState.OnShelf.getCode())) {
            sohuLiteratureInfoMapper.updateViewCount(id);
        }
        return literatureVo;
    }

    /**
     * 查询文学主体列表
     */
    @Override
    public TableDataInfo<SohuLiteratureVo> queryPageList(SohuLiteratureBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuLiterature> lqw = buildQueryWrapper(bo);
        lqw.eq(SohuLiterature::getDelFlag, false);
        if (StringUtils.isBlank(bo.getState()) || !bo.getState().equals(CommonState.Edit.name())) {
            lqw.notIn(SohuLiterature::getState, CommonState.Edit.name());
        }
        Page<SohuLiteratureVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            List<Long> literatureIds = new ArrayList<>();
            for (SohuLiteratureVo record : result.getRecords()) {
                literatureIds.add(record.getId());
            }
            SohuLiteratureInfoBo infoBo = new SohuLiteratureInfoBo();
            infoBo.setLiteratureIds(literatureIds);
            // 设置拓展数据
            List<SohuLiteratureInfoVo> literatureInfoVos = sohuLiteratureInfoService.queryList(infoBo);
            Map<Long, SohuLiteratureInfoVo> infoVoMap = literatureInfoVos.stream().collect(Collectors.toMap(SohuLiteratureInfoVo::getLiteratureId, u -> u));
            for (SohuLiteratureVo record : result.getRecords()) {
                //设置分类名
                record.setCategoryName(this.getBusyTypeDesc(record.getType()));
                SohuLiteratureInfoVo infoVo = infoVoMap.get(record.getId());
                if (Objects.nonNull(infoVo)) {
                    record.setContent(infoVo.getContent());
                    record.setInfo(infoVo);
                } else {
                    record.setInfo(new SohuLiteratureInfoVo());
                }
                // 设置作者关注状态
                Long userId = LoginHelper.getUserId();
                Map<Long, SohuUserFollowVo> userFollowMap;
                Map<Long, SohuUserLikeVo> likeVoMap;
                Map<Long, SohuUserCollectVo> collectVoMap;
                if (userId != null && userId > 0L) {
                    userFollowMap = sohuUserFollowService.mapUserFollows(userId, Collections.singletonList(record.getUserId()));
                    likeVoMap = sohuUserLikeService.queryMap(userId, record.getType(), Collections.singletonList(record.getId()));
                    collectVoMap = sohuUserCollectService.queryMap(userId, record.getType(), Collections.singletonList(record.getId()));
                } else {
                    userFollowMap = new HashMap<>();
                    likeVoMap = new HashMap<>();
                    collectVoMap = new HashMap<>();
                }
                // 我关注状态，true = 关注
                record.setFollowObj(Objects.nonNull(userFollowMap.get(record.getUserId())));
                // 我点赞状态，true = 点赞
                record.setPraiseObj(Objects.nonNull(likeVoMap.get(record.getId())));
                // 我收藏状态，true = 收藏
                record.setCollectObj(Objects.nonNull(collectVoMap.get(record.getId())));
            }
            result.setRecords(getRecord(result.getRecords(), LoginHelper.getUserId()));
        }
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询文学主体列表
     */
    @Override
    public List<SohuLiteratureVo> queryList(SohuLiteratureBo bo) {
        LambdaQueryWrapper<SohuLiterature> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuLiterature> buildQueryWrapper(SohuLiteratureBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuLiterature> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuLiterature::getUserId, bo.getUserId());
        lqw.eq(bo.getCategoryId() != null, SohuLiterature::getCategoryId, bo.getCategoryId());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), SohuLiterature::getTitle, bo.getTitle());
        if (StrUtil.isEmpty(bo.getState())) {
            lqw.notIn(SohuLiterature::getState, CommonState.Edit.getCode(), CommonState.Delete.getCode(), CommonState.ForceDelete.getCode());
        } else if (StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.OffShelf.getCode())) {
            lqw.in(SohuLiterature::getState, CommonState.OffShelf.getCode(), CommonState.CompelOff.getCode());
        } else {
            lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuLiterature::getState, bo.getState());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getType()), SohuLiterature::getType, bo.getType());
        lqw.in(CollUtil.isNotEmpty(bo.getStateList()), SohuLiterature::getState, bo.getStateList());
        lqw.in(CollUtil.isNotEmpty(bo.getUserIds()), SohuLiterature::getUserId, bo.getUserIds());
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            lqw.ge(StrUtil.isNotBlank(bo.getStartTime()), SohuLiterature::getCreateTime, DateUtil.beginOfDay(DateUtil.parseDate(bo.getStartTime())));
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            lqw.le(StrUtil.isNotBlank(bo.getEndTime()), SohuLiterature::getCreateTime, DateUtil.endOfDay(DateUtil.parseDate(bo.getEndTime())));
        }
        lqw.eq(Objects.nonNull(bo.getAppealStatus()), SohuLiterature :: getAppealStatus, bo.getAppealStatus());
        lqw.orderByAsc(SohuLiterature::getSortIndex);
        lqw.orderByDesc(SohuLiterature::getCreateTime);
        if (CollUtil.isNotEmpty(bo.getHandleIds())) {
            if (bo.getIsBlack()) {
                lqw.notIn(SohuLiterature::getId, bo.getHandleIds());
            } else {
                lqw.in(SohuLiterature::getId, bo.getHandleIds());
            }
        }
        return lqw;
    }

    /**
     * 新增文学主体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SohuLiteratureBo bo) {
        SohuLiterature add = BeanUtil.toBean(bo, SohuLiterature.class);
        String state = StrUtil.isNotBlank(bo.getState()) && StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.Edit.name()) ? CommonState.Edit.name() : CommonState.WaitApprove.name();
        if (!(Objects.equals(CommonState.WaitApprove.name(), state) || Objects.equals(CommonState.Edit.name(), state))) {
            throw new RuntimeException("状态参数异常");
        }
        add.setState(state);
        if (StrUtil.isBlank(bo.getType())) {
            add.setType(VideoEnum.Type.general.getCode());
        }
        add.setVisibleType(VisibleTypeEnum.open.getCode());
        add.setUserId(LoginHelper.getUserId());
        validEntityBeforeSave(add);
//        String scanText = null;
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
            add.setSubmitTime(new Date());
            add.setSubmitScene("自主发布");
            add.setSubmitNum(Constants.ONE);
//            scanText = aliyunAuditService.scanText(HtmlUtil.cleanHtmlTag(bo.getTitle() + bo.getContent()), AliyunAuditLabelEnum.textCheck);
//            if (StrUtil.isEmptyIfStr(scanText)) {
//                add.setState(CommonState.OnShelf.getCode());
//                add.setAuditTime(new Date());
//                add.setAuditState(CommonState.Pass.name());
//            } else {
                add.setState(CommonState.WaitApprove.getCode());
                add.setAuditState(CommonState.WaitApprove.name());
//                List<String> labels = parseLabelsFromJson(scanText);
//                // 匹配枚举并获取描述信息
//                List<String> descriptions = labels.stream()
//                        .map(AliyunAuditLabelEnum::getDescriptionByLabel)
//                        .filter(Optional::isPresent)
//                        .map(Optional::get)
//                        .collect(Collectors.toList());
//                String rejectReason = String.join("; ", descriptions);
//                add.setRejectReason(rejectReason);
//            }
        }
        //处理站点信息
        SohuSiteVo siteVo = siteService.getSiteByIp(ServletUtils.getClientIP());
        add.setSiteId(siteVo.getId());
        if (Objects.equals(siteVo.getType(), SiteType.City.name())) {
            Long pid = siteVo.getPid();
            add.setCountrySiteId(pid);
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
//            if (StrUtil.isNotBlank(scanText)) {
//                log.warn("【{}】标题或内容违规，审核结果：{}", add.getId(), scanText);
//            }
            // 保存审核记录
            if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
                this.initCreateAudited(add, Boolean.TRUE);
                // 发布异步机审消息
                SohuRiskMqBo riskMqBo = new SohuRiskMqBo();
                riskMqBo.setBusyType(BusyType.fromCode(add.getType()));
                riskMqBo.setBusyCode(add.getId().toString());
                MqMessaging mqMessaging = new MqMessaging(JSONObject.toJSONString(riskMqBo), MqKeyEnum.HANDLE_RISK_MANAGEMENT.getKey());
                remoteStreamMqService.sendDelayMsg(mqMessaging, 2L);
            }
            SohuLiteratureInfoBo info = new SohuLiteratureInfoBo();
            info.setLiteratureId(add.getId());
            info.setContent(bo.getContent());
            info.setSiteId(add.getSiteId());
            info.setViewCount(Math.max(bo.getViewCount(), 0));
            // 保存内容详情
            sohuLiteratureInfoService.insertByBo(info);
            // 保存内容
            bo.setId(add.getId());
            // 同步图文至万能表
            sohuSyncContentService.sync(add);
        }
        return flag;
    }

    /**
     * 修改文学主体
     */
    @Override
    public Boolean updateByBo(SohuLiteratureBo bo) {
        SohuLiteratureVo literature = baseMapper.selectVoById(bo.getId());
        this.validateLiteratureExists(literature);
        if (!Objects.equals(LoginHelper.getUserId(), literature.getUserId())) {
            throw new RuntimeException("非法操作,这不是您的数据");
        }
        SohuLiterature update = BeanUtil.toBean(bo, SohuLiterature.class);
        String state = StrUtil.isBlankIfStr(bo.getState())
                ? CommonState.WaitApprove.name()
                : (StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.Edit.name())
                ? CommonState.Edit.name()
                : bo.getState());
        if (!(Objects.equals(CommonState.Refuse.name(), literature.getState())
                || Objects.equals(CommonState.Edit.name(), literature.getState())
                || Objects.equals(CommonState.CompelOff.name(), literature.getState())
                || Objects.equals(CommonState.OffShelf.name(), literature.getState()))) {
            throw new RuntimeException("此状态不支持修改");
        }
        bo.setState(state);
        update.setState(state);
        // 保存审核记录时需要站点
        update.setSiteId(literature.getSiteId());
        bo.setState(update.getState());
        if (StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.WaitApprove.name())) {
            if (literature.getSubmitNum().compareTo(SUBMIT_NUM_MAX_VALUE) > -1) {
                throw new RuntimeException("您已提交" + literature.getSubmitNum() + "次，最大提交次数为" + SUBMIT_NUM_MAX_VALUE);
            }
            fieldChangeChecker(bo, literature);
            update.setSubmitTime(new Date());
            if (literature.getSubmitNum() > 0) {
                update.setSubmitScene("下架整改");
            } else {
                update.setSubmitScene("自主发布");
            }
            update.setSubmitNum(literature.getSubmitNum() + 1);
            update.setAppealStatus(false);
//            String scanText = aliyunAuditService.scanText(HtmlUtil.cleanHtmlTag(bo.getTitle() + bo.getContent()), AliyunAuditLabelEnum.textCheck);
//            if (StrUtil.isNotBlank(scanText)) {
                update.setState(CommonState.WaitApprove.getCode());
                update.setAuditState(CommonState.WaitApprove.getCode());
//                List<String> labels = parseLabelsFromJson(scanText);
//                // 匹配枚举并获取描述信息
//                List<String> descriptions = labels.stream()
//                        .map(AliyunAuditLabelEnum::getDescriptionByLabel)
//                        .filter(Optional::isPresent)
//                        .map(Optional::get)
//                        .collect(Collectors.toList());
//                String rejectReason = String.join("; ", descriptions);
//                update.setRejectReason(rejectReason);
//                log.warn("【{}】标题或内容违规，审核结果：{}", update.getId(), scanText);
//            } else {
//                update.setAuditTime(new Date());
//                update.setState(CommonState.OnShelf.getCode());
//                update.setAuditState(CommonState.Pass.name());
//            }
        } else if (StrUtil.equalsAnyIgnoreCase(state, CommonState.Edit.name())) {
            update.setAuditState(null);
        }
        update.setUserId(literature.getUserId());
        validEntityBeforeSave(update);
        Long literatureId = bo.getId();
        // 更新内容
        sohuLiteratureInfoService.updateByLiteratureId(literatureId, bo.getContent());
        LambdaUpdateWrapper<SohuLiterature> luw = new LambdaUpdateWrapper();
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name()) || StrUtil.equalsAnyIgnoreCase(state, CommonState.Edit.name())) {
            luw.eq(SohuLiterature::getId, update.getId());
            luw.set(SohuLiterature::getAppealReason, null);
            luw.set(SohuLiterature::getRejectReason, update.getRejectReason());
            luw.set(SohuLiterature::getAuditState, update.getAuditState());
            update.setAppealReason(null);
            //update.setRejectReason(null);
            baseMapper.updateByIdThenEviction(update, luw);
        }else {
            //update.setState(null);
            //update.setAuditState(null);
            baseMapper.updateById(update);
        }
        SohuContentLifecycleBo lifecycleBo = new SohuContentLifecycleBo(literature.getId(), literature.getType(), literature.getState(), update.getState(), "提交审核");
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
            this.initCreateAudited(update, true);
            // 发布异步机审消息
            SohuRiskMqBo riskMqBo = new SohuRiskMqBo();
            riskMqBo.setBusyType(BusyType.fromCode(literature.getType()));
            riskMqBo.setBusyCode(update.getId().toString());
            MqMessaging mqMessaging = new MqMessaging(JSONObject.toJSONString(riskMqBo), MqKeyEnum.HANDLE_RISK_MANAGEMENT.getKey());
            remoteStreamMqService.sendDelayMsg(mqMessaging, 2L);
        }else {
            lifecycleBo.setRemark("编辑");
        }
        this.createLifecycleLog(lifecycleBo);
        return true;
    }

    private void fieldChangeChecker(SohuLiteratureBo bo, SohuLiteratureVo entityVo) {
        if (StrUtil.equalsAnyIgnoreCase(entityVo.getState(), CommonState.Edit.name())) {
            return;
        }
        SohuContentLifecycleVo lifecycleVo = this.sohuContentLifecycleService.selectOfLast(entityVo.getId(), entityVo.getType());
        if(Objects.nonNull(lifecycleVo) && Objects.equals(entityVo.getState(),lifecycleVo.getLastState())&&Objects.equals(entityVo.getState(),lifecycleVo.getCurrentState())){
            return;
        }
        SohuLiteratureBo oldValue = BeanUtil.copyProperties(entityVo, SohuLiteratureBo.class);
        SohuLiteratureInfoVo infoVo = sohuLiteratureInfoService.queryByLiteratureId(entityVo.getId());
        // 设置内容
        oldValue.setContent(Objects.nonNull(infoVo) ? infoVo.getContent() : null);
        if (!SohuFieldChangeUtil.hasFieldChanged(oldValue, bo)) {
            throw new RuntimeException("请修改后再提交");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void commit(Long literatureId) {
        SohuLiterature literature = baseMapper.selectById(literatureId);
        if (Objects.isNull(literature)) {
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
        SohuLiteratureBo bo = BeanUtil.copyProperties(literature, SohuLiteratureBo.class);
        bo.setState(CommonState.WaitApprove.name());
        SohuLiteratureInfoVo infoVo = sohuLiteratureInfoService.queryByLiteratureId(literatureId);
        if (Objects.nonNull(infoVo)) {
            bo.setContent(infoVo.getContent());
        }
        this.updateByBo(bo);
    }

    @Override
    public TableDataInfo<SohuLiteratureVo> literaturePageCenter(Long userId, String type, PageQuery pageQuery) {
        Long loginUserId = LoginHelper.getUserId();
        if (userId == null) {
            userId = loginUserId;
        }
        SohuLiteratureBo dto = new SohuLiteratureBo();
        dto.setUserId(userId);
        dto.setType(type);
        List<String> states = new ArrayList<>();
        states.add(BusyOrderStatus.OnShelf.name());
        if (Objects.equals(loginUserId, userId)) {
            // 查看自己的
            states.add(BusyOrderStatus.WaitApprove.name());
            states.add(BusyOrderStatus.CompelOff.name());
            states.add(BusyOrderStatus.Hide.name());
        }
        dto.setStateList(states);
        if (dto.getUserId() == null) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        return queryPageList(dto, pageQuery);
    }

    @Override
    public Long getAuthorId(Long id) {
        if (CalUtils.isNullOrZero(id)) {
            return 0L;
        }
        SohuLiteratureVo literatureVo = this.get(id);
        return Objects.isNull(literatureVo) ? 0L : literatureVo.getUserId();
    }

    @Override
    public SohuLiteratureVo get(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public Boolean logicDeleteById(Long id) {
        SohuLiterature literature = baseMapper.selectById(id);
        if (Objects.isNull(literature)) {
            return false;
        }
        SohuContentLifecycleBo lifecycleBo = new SohuContentLifecycleBo(id, literature.getType(), literature.getState(),
                CommonState.Delete.name(), "自主删除");
        if (!Objects.equals(LoginHelper.getUserId(), literature.getUserId())) {
            throw new RuntimeException("非法操作，这不是您的数据");
        }
        literature.setState(CommonState.Delete.name());
        literature.setDelTime(DateUtils.getNowDate());
        baseMapper.updateById(literature);
        this.createLifecycleLog(lifecycleBo);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean comment(SohuCommentBo bo, Boolean commentCountAdd) {
        SohuLiteratureInfo info = sohuLiteratureInfoMapper.selectOne(Wrappers.
                <SohuLiteratureInfo>lambdaQuery().eq(SohuLiteratureInfo::getLiteratureId, bo.getBusyCode()));
        if (Objects.isNull(info) || info.getId() == null) {
            return false;
        }
        SohuLiterature literature = this.baseMapper.selectById(bo.getBusyCode());
        if (BooleanUtil.isTrue(commentCountAdd)) {
            info.setCommentCount(info.getCommentCount() + 1);
        }
        sohuLiteratureInfoMapper.updateById(info);
        SohuContentMain sohuContentMain = iSohuContentMainService.getEntityByObj(bo.getBusyCode(), this.getBusyType(literature.getType()));
        if (Objects.nonNull(sohuContentMain)) {
            sohuContentMain.setCommentCount(info.getCommentCount());
            iSohuContentMainService.updateById(sohuContentMain);
        }
        bo.setTopType(this.getBusyType(literature.getType()));
        bo.setTopCode(info.getLiteratureId());
        bo.setBusyCoverImage(literature.getCoverImage());
        bo.setBusyType(this.getBusyType(literature.getType()));
        bo.setBusyUser(literature.getUserId());
        if (bo.getTopReplyUser() != null && bo.getTopReplyUser() > 0L) {
            SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildComment(bo, bo.getReplyUser(), NoticeInteractEnum.commentReceive.name());
            interactNoticeService.insertByBo(interactNoticeReceive);
        } else {
            if (!Objects.equals(bo.getReplyUser(), bo.getOperateUser())) {
                // 判断是不是自己回复自己的评论
                SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildComment(bo, bo.getReplyUser(), NoticeInteractEnum.commentReceive.name());
                interactNoticeService.insertByBo(interactNoticeReceive);
            }
            // 操作者收到
            SohuInteractNoticeBo interactNoticeSend = SohuInteractNoticeBo.buildComment(bo, bo.getOperateUser(), NoticeInteractEnum.commentSend.name());
            interactNoticeService.insertByBo(interactNoticeSend);
        }
        // 删除拓展数据缓存
        sohuLiteratureInfoService.evictLiteratureId(bo.getBusyCode());
        return Boolean.TRUE;
    }

    @Override
    public Boolean like(SohuBusyBO bo) {
        SohuLiteratureInfo info = sohuLiteratureInfoMapper.selectOne(Wrappers.
                <SohuLiteratureInfo>lambdaQuery().eq(SohuLiteratureInfo::getLiteratureId, bo.getBusyCode()));
        if (Objects.isNull(info) || info.getId() == null) {
            return false;
        }
        SohuLiterature literature = this.baseMapper.selectById(bo.getBusyCode());
        Integer oldCount = info.getPraiseCount();
        int count = bo.getIsAdd() != null && bo.getIsAdd() ? oldCount + 1 : Math.max((oldCount - 1), 0);
        info.setPraiseCount(count);
        bo.setTopCode(info.getLiteratureId());
        bo.setTopType(this.getBusyType(literature.getType()));
        // 更新万能表的点赞数量
        sohuContentMainMapper.setPraiseCount(bo.getBusyCode(), this.getBusyType(literature.getType()), count);
        // 增加点赞
        if (bo.getIsAdd() != null && bo.getIsAdd()) {
            bo.setSourceUser(literature.getUserId());
            bo.setOperateUser(bo.getOperateUser());
            bo.setBusyCoverImage(literature.getCoverImage());
            bo.setBusyUser(literature.getUserId());
            SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildBusy(bo, literature.getUserId(), NoticeInteractEnum.like.name());
            interactNoticeReceive.setContent(String.format(NoticeInteractEnum.likeContent, this.getBusyTypeDesc(literature.getType())));
            interactNoticeService.insertByBo(interactNoticeReceive);
        }
        // 删除图文的拓展数据缓存
        sohuLiteratureInfoService.evictLiteratureId(bo.getBusyCode());
        sohuLiteratureInfoMapper.updateById(info);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean collect(SohuBusyBO bo) {
        SohuLiteratureInfo info = sohuLiteratureInfoMapper.selectOne(Wrappers.
                <SohuLiteratureInfo>lambdaQuery().eq(SohuLiteratureInfo::getLiteratureId, bo.getBusyCode()));
        if (Objects.isNull(info) || info.getId() == null) {
            return false;
        }
        SohuLiterature literature = this.baseMapper.selectById(bo.getBusyCode());
        Integer oldCount = info.getCollectCount();
        int count = bo.getIsAdd() ? oldCount + 1 : Math.max((oldCount - 1), 0);
        info.setCollectCount(count);
        // 更新万能表的收藏数量
        sohuContentMainMapper.setCollectCount(bo.getBusyCode(), this.getBusyType(literature.getType()), count);
        if (bo.getIsAdd() != null && bo.getIsAdd()) {
            bo.setSourceUser(literature.getUserId());
            bo.setOperateUser(bo.getOperateUser());
            bo.setBusyCoverImage(literature.getCoverImage());
            bo.setBusyUser(literature.getUserId());
            SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildBusy(bo, literature.getUserId(), NoticeInteractEnum.collect.name());
            interactNoticeReceive.setContent(String.format(NoticeInteractEnum.collectContent, this.getBusyTypeDesc(literature.getType())));
            interactNoticeService.insertByBo(interactNoticeReceive);
        }
        // 删除图文的拓展数据缓存
        sohuLiteratureInfoService.evictLiteratureId(bo.getBusyCode());
        sohuLiteratureInfoMapper.updateById(info);
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateState(SohuBusyUpdateStateBo bo) {
        String rejectReason = bo.getRejectReason();
        Long busyCode = bo.getBusyCode();
        SohuLiterature literature = baseMapper.selectById(busyCode);
        if (Objects.isNull(literature)) {
            return Boolean.FALSE;
        }
        literature.setState(StrUtil.isBlankIfStr(bo.getState()) ? CommonState.CompelOff.getCode() : bo.getState());
        literature.setRejectReason(rejectReason);
        literature.setUpdateTime(new Date());
        // 更新图文
        baseMapper.updateById(literature);
        // 更新万能表
        sohuContentMainMapper.updateState(literature.getState(), busyCode, literature.getType());
        return Boolean.TRUE;

    }

    @Override
    public Boolean auditOnShelf(Long id) {
        SohuLiterature entity = baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new RuntimeException("内容不存在");
        }
        if (!(Objects.equals(entity.getState(), CommonState.WaitApprove.name())
                || Objects.equals(entity.getState(), CommonState.Refuse.name()))) {
            throw new RuntimeException("此状态不支持上架，请检查数据");
        }
        entity.setState(CommonState.OnShelf.name());
        entity.setAuditState(CommonState.Pass.name());
        entity.setAuditTime(new Date());
        entity.setAppealStatus(false);
        entity.setAppealReason(null);
        LambdaUpdateWrapper<SohuLiterature> luw = new LambdaUpdateWrapper<>();
        luw.set(SohuLiterature::getAppealReason, null);
        luw.set(SohuLiterature::getRejectReason, null);
        luw.eq(SohuLiterature::getId, id);
        this.baseMapper.update(entity, luw);
        // 更新万能表
        sohuContentMainMapper.updateState(entity.getState(), id, entity.getType());
        return true;
    }

    @Override
    public Boolean auditRefuse(Long id, String rejectReason) {
        SohuLiterature entity = baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new RuntimeException("内容不存在");
        }
        if (!(Objects.equals(entity.getState(), CommonState.WaitApprove.name())
                || Objects.equals(entity.getState(), CommonState.Refuse.name()))) {
            throw new RuntimeException("此状态不支持上架，请检查数据");
        }
        entity.setState(CommonState.Refuse.name());
        entity.setAuditState(CommonState.Refuse.name());
        entity.setAuditTime(new Date());
        entity.setRejectReason(rejectReason);
        //entity.setSubmitNum(entity.getSubmitNum() + 1);
        this.baseMapper.updateById(entity);
        // 更新万能表
        sohuContentMainMapper.updateState(entity.getState(), id, entity.getType());
        return true;
    }

    @Override
    public Boolean updateBatchContentState(SohuContentBatchBo bo) {
        // 获取需要修改的主键id
        List<String> idList = StrUtil.split(bo.getIds(), COMMA);
        List<SohuLiterature> sohuLiteratureList = baseMapper.selectBatchIds(idList);
        // 如果查询结果为空，或者数量与传入的 id 数量不一致，返回失败
        if (sohuLiteratureList == null || sohuLiteratureList.size() != idList.size()) {
            return Boolean.FALSE;
        }
        // 校验所有作品的作者 ID 是否一致
        Long currentUserId = LoginHelper.getUserId();
        boolean isAllAuthorMatched = sohuLiteratureList.stream()
                .allMatch(literature -> literature.getUserId().equals(currentUserId));
        if (!isAllAuthorMatched) {
            return Boolean.FALSE;
        }
        // 构建需要更新的内容
        List<SohuLiterature> contentList = sohuLiteratureList.stream()
                .map(literature -> {
                    SohuLiterature content = new SohuLiterature();
                    content.setId(literature.getId());
                    content.setState(bo.getState());
                    return content;
                })
                .collect(Collectors.toList());
        // 执行批量更新
        return baseMapper.updateBatchById(contentList);
    }

    @Override
    public Boolean userAppeal(SohuUserContentAppealBo bo) {
        SohuLiterature literature = baseMapper.selectById(bo.getId());
        if (Objects.isNull(literature)) {
            throw new RuntimeException("内容不存在");
        }
        if (!Objects.equals(literature.getState(), CommonState.Refuse.name())) {
            throw new RuntimeException("非审核拒绝状态，不支持申诉");
        }
        if (BooleanUtil.isTrue(literature.getAppealStatus())) {
            throw new RuntimeException("您已申诉过，只有一次申诉机会");
        }
        SohuLiterature updateEntity = new SohuLiterature();
        updateEntity.setAppealStatus(true);
        updateEntity.setAppealReason(bo.getAppealReason());
        updateEntity.setAuditState(CommonState.WaitApprove.name());
        updateEntity.setSubmitScene("申诉复审");
        LambdaUpdateWrapper<SohuLiterature> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuLiterature::getId, bo.getId());
        luw.eq(SohuLiterature::getState, CommonState.Refuse.name());
        this.baseMapper.update(updateEntity, luw);
        //初始化审核
        literature.setAppealStatus(updateEntity.getAppealStatus());
        literature.setAppealReason(updateEntity.getAppealReason());
        literature.setSubmitScene(updateEntity.getSubmitScene());
        literature.setState(CommonState.WaitApprove.name());
        this.initCreateAudited(literature, Boolean.FALSE);
        return true;
    }

    @Override
    public Boolean updateOffShelfById(Long id) {
        SohuLiterature sohuLiterature = baseMapper.selectById(id);
        if (Objects.isNull(sohuLiterature)) {
            return false;
        }
        if (!Objects.equals(LoginHelper.getUserId(), sohuLiterature.getUserId())) {
            throw new RuntimeException("当前记录非本人记录,无法进行本操作");
        }
        sohuLiterature.setState(CommonState.OffShelf.name());
        baseMapper.updateById(sohuLiterature);
        return true;
    }

    @Override
    public Boolean updateCompelOffById(SohuContentRefuseBo bo) {
//        if (!LoginHelper.anyMatchRole(RoleCodeEnum.ADMIN.getCode(), RoleCodeEnum.CALL_ADMIN.getCode(), RoleCodeEnum.OPERATION_ADMIN.getCode())) {
//            throw new RuntimeException("非法操作，您无权操作");
//        }
        SohuLiterature sohuLiterature = baseMapper.selectById(bo.getId());
        if (Objects.isNull(sohuLiterature)) {
            return false;
        }
        sohuLiterature.setState(CommonState.CompelOff.name());
        sohuLiterature.setRejectReason(bo.getReason());
        baseMapper.updateById(sohuLiterature);
        this.sendMsgOfCompelOff(sohuLiterature);
        return true;
    }

    @Override
    public Boolean recoveryData(Long id) {
        SohuLiterature entity = this.baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new RuntimeException("数据不存在");
        }
        if (!Objects.equals(LoginHelper.getUserId(), entity.getUserId())) {
            throw new RuntimeException("非法操作,这不是您的数据");
        }
        if (!(Objects.equals(entity.getState(), CommonState.Delete.name())
                || Objects.equals(entity.getState(), CommonState.ForceDelete.name()))) {
            throw new RuntimeException("非审核拒绝状态，不支持申诉");
        }
        SohuLiterature updateEntity = new SohuLiterature();
        updateEntity.setId(id);
        LambdaUpdateWrapper<SohuLiterature> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuLiterature::getState, entity.getState());
        luw.eq(SohuLiterature::getId, id);
        SohuContentLifecycleVo lifecycleVo = this.sohuContentLifecycleService.selectOfLast(id, entity.getType());
        if (Objects.isNull(lifecycleVo)) {
            //兼容历史数据
            updateEntity.setState(CommonState.OffShelf.name());
        } else {
            updateEntity.setState(lifecycleVo.getLastState());
        }
        return this.baseMapper.update(updateEntity, luw) > 0;
    }

    @Override
    public Boolean deleteDataById(Long id) {
        SohuLiterature sohuLiterature = baseMapper.selectById(id);
        if (Objects.isNull(sohuLiterature)) {
            return false;
        }
        if (!Objects.equals(LoginHelper.getUserId(), sohuLiterature.getUserId())) {
            throw new RuntimeException("非法操作，这不是您的数据");
        }
        return sohuContentMainMapper.flushRecycleLiterature(LoginHelper.getUserId(), Collections.singleton(id));
    }

    @Override
    public Boolean logicForceDeleteById(Long id) {
//        if (!LoginHelper.anyMatchRole(RoleCodeEnum.ADMIN.getCode(), RoleCodeEnum.CALL_ADMIN.getCode(), RoleCodeEnum.OPERATION_ADMIN.getCode())) {
//            throw new RuntimeException("非法操作，您无权操作");
//        }
        SohuLiterature sohuLiterature = baseMapper.selectById(id);
        if (Objects.isNull(sohuLiterature)) {
            return false;
        }
        SohuContentLifecycleBo lifecycleBo = new SohuContentLifecycleBo(id, sohuLiterature.getType(), sohuLiterature.getState(),
                CommonState.ForceDelete.name(), "强制删除");
        sohuLiterature.setState(CommonState.ForceDelete.name());
        sohuLiterature.setDelTime(DateUtils.getNowDate());
        baseMapper.updateById(sohuLiterature);
        this.createLifecycleLog(lifecycleBo);
        return true;
    }

    @Override
    public Boolean hideDataBatch(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        for (Long id : ids) {
            this.hideData(id);
        }
        return true;
    }

    @Override
    public SohuConentListStatVo queryPageListStat(SohuLiteratureBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        LambdaQueryWrapper<SohuLiterature> lqw = buildQueryWrapper(bo);
        if (StrUtil.isNotBlank(bo.getState()) && (!Constants.ALL.equals(bo.getState()))) {
            lqw.eq(SohuLiterature::getState, bo.getState());
        }
        lqw.eq(SohuLiterature::getDelFlag, 0);
        SohuConentListStatVo vo = this.baseMapper.queryPageListStat(lqw);
        if (Objects.isNull(vo)) {
            vo = new SohuConentListStatVo();
        }
        Long allNum = vo.getOnShelfNum() + vo.getOffShelfNum() + vo.getWaitApproveNum() + vo.getRefuseNum();
        vo.setAllNum(allNum);
        return vo;
    }

    public Boolean hideData(Long id) {
        SohuLiterature entity = this.baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new RuntimeException("数据不存在");
        }
        if (!Objects.equals(LoginHelper.getUserId(), entity.getUserId())) {
            throw new RuntimeException("非法操作,这不是您的数据");
        }
        if (!Objects.equals(entity.getState(), CommonState.OnShelf.name())) {
            throw new RuntimeException("未上架，不支持隐藏");
        }
        SohuLiterature updateEntity = new SohuLiterature();
        updateEntity.setId(id);
        updateEntity.setState(CommonState.Hide.name());
        LambdaUpdateWrapper<SohuLiterature> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuLiterature::getState, entity.getState());
        luw.eq(SohuLiterature::getId, id);
        this.baseMapper.update(updateEntity, luw);
        return true;
    }

    @Override
    public Long countByTime(String startTime, String endTime) {
        LambdaQueryWrapper<SohuLiterature> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(SohuLiterature::getCreateTime, startTime, endTime);
        return this.baseMapper.selectCount(wrapper);
    }

    @Override
    public void handleRobot(String busyCode, Boolean isPass, String reason, String busyType) {
        SohuAuditBo auditBo = new SohuAuditBo();
        auditBo.setBusyCode(Long.valueOf(busyCode));
        auditBo.setBusyType(busyType);
        List<SohuAuditVo> auditList = auditService.queryList(auditBo);
        if (CollectionUtils.isEmpty(auditList)) {
            return;
        }
        SohuAuditVo auditVo = auditList.stream()
                .filter(record -> record != null && CommonState.WaitApprove.getCode().equals(record.getSysAuditState()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(auditVo)) {
            return;
        }
        SohuAuditBo newAuditBo = new SohuAuditBo();
        newAuditBo.setId(auditVo.getId());
        newAuditBo.setBusyType(busyType);
        newAuditBo.setState(isPass ? CommonState.OnShelf.getCode() : CommonState.Refuse.getCode());
        newAuditBo.setRejectReason(reason);
        auditService.process(newAuditBo);
    }



    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuLiterature entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    private void validateLiteratureExists(SohuLiteratureVo literatureVo) {
        if (Objects.isNull(literatureVo)) {
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
    }

    private String getBusyType(String type) {
        return type.equals(BusyType.Poetry.getType()) ? BusyType.Poetry.name() : BusyType.Prose.name();
    }

    private String getBusyTypeDesc(String type) {
        return type.equals(BusyType.Poetry.getType()) ? BusyType.Poetry.getDescription() : BusyType.Prose.getDescription();
    }

    /**
     * 提交至审核
     *
     * @param sohuLiterature 诗文对象
     */
    private void initCreateAudited(SohuLiterature sohuLiterature, Boolean isAiAudit) {
        SohuLiteratureBo bo = BeanUtil.toBean(sohuLiterature, SohuLiteratureBo.class);
        this.initCreateAudited(bo, isAiAudit);
    }

    /**
     * 提交至审核
     *
     * @param sohuLiteratureBo 诗文对象
     * @param isAiAudit        是否机审
     */
    private void initCreateAudited(SohuLiteratureBo sohuLiteratureBo, Boolean isAiAudit) {
        sohuAuditInitService.initCreateAudited(sohuLiteratureBo, isAiAudit);
    }

    /**
     * 构造诗文其他信息
     */
    public List<SohuLiteratureVo> getRecord(List<SohuLiteratureVo> records, Long userId) {
        Set<Long> userIds = new HashSet<>();
        List<Long> busyCodes = new ArrayList<>();
        for (SohuLiteratureVo record : records) {
            userIds.add(record.getUserId());
        }
        Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
        Map<Long, SohuLiteratureInfoVo> infoMap = sohuLiteratureInfoService.queryMap(busyCodes);
        Map<Long, SohuUserLikeVo> likeMap;
        if (userId != null && userId > 0L) {
            likeMap = sohuUserLikeService.queryMap(userId, BusyType.Article.name(), busyCodes);
        } else {
            likeMap = new HashMap<>();
        }
        records.forEach(record -> {
            LoginUser user = userMap.get(record.getUserId());
            if (Objects.nonNull(user)) {
                record.setUserName(StrUtil.isNotBlank(user.getNickname()) ? user.getNickname() : user.getUsername());
                record.setNickName(user.getNickname());
                record.setUserAvatar(user.getAvatar());
            }
            if (userId != null && userId > 0L) {
                record.setPraiseObj(Objects.nonNull(likeMap.get(record.getId())));
            }
            SohuLiteratureInfoVo literatureInfoVo = infoMap.get(record.getId());
            if (Objects.nonNull(literatureInfoVo)) {
                record.setPraiseCount(literatureInfoVo.getPraiseCount());
            }
        });
        return records;
    }

    /**
     * 记录生命周期
     *
     * @param lifecycleBo
     * @return
     */
    private Boolean createLifecycleLog(SohuContentLifecycleBo lifecycleBo) {
        if (Objects.equals(lifecycleBo.getLastState(), CommonState.OnShelf.name())
                || Objects.equals(lifecycleBo.getLastState(), CommonState.WaitApprove.name())) {
            if (Objects.equals(lifecycleBo.getCurrentState(), CommonState.ForceDelete.name())) {
                lifecycleBo.setCurrentState(CommonState.CompelOff.name());
                this.sohuContentLifecycleService.insertByBo(lifecycleBo);
                lifecycleBo.setId(null);
                lifecycleBo.setLastState(CommonState.CompelOff.name());
                lifecycleBo.setCurrentState(CommonState.ForceDelete.name());
            } else if (Objects.equals(lifecycleBo.getCurrentState(), CommonState.Delete.name())) {
                lifecycleBo.setCurrentState(CommonState.OffShelf.name());
                this.sohuContentLifecycleService.insertByBo(lifecycleBo);
                lifecycleBo.setId(null);
                lifecycleBo.setLastState(CommonState.OffShelf.name());
                lifecycleBo.setCurrentState(CommonState.Delete.name());
            }
        }
        this.sohuContentLifecycleService.insertByBo(lifecycleBo);
        return true;
    }

    /**
     * 从 JSON 数据中提取所有的 label 字段
     */
    private static List<String> parseLabelsFromJson(String jsonString) {
        List<String> labels = new ArrayList<>();
        JSONArray jsonArray = JSON.parseArray(jsonString);

        for (Object obj : jsonArray) {
            JSONObject jsonObject = (JSONObject) obj;
            String label = jsonObject.getString("label");
            if (label != null) {
                labels.add(label);
            }
        }
        return labels;
    }

    /**
     * 发送系统消息通知-强制下架
     */
    private void sendMsgOfCompelOff(SohuLiterature sohuLiterature) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.CONTENT_COMPEL_OFF_TITLE);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.articleCompelOff.name());
        content.setDetailId(sohuLiterature.getId());
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setId(sohuLiterature.getId());
        detail.setDesc(String.format(SystemNoticeEnum.CONTENT_COMPEL_OFF_DESC, sohuLiterature.getTitle(), sohuLiterature.getRejectReason()));
        detail.setStatus(CommonState.CompelOff.name());
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(sohuLiterature.getUserId(), SystemNoticeEnum.CONTENT_COMPEL_OFF_TITLE, contentJson, SystemNoticeEnum.Type.contentCompelOff);
    }
}
