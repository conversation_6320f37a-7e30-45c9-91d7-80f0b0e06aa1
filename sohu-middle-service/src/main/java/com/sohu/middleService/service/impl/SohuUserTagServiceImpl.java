package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.enums.ApplyStateEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuFriendsBo;
import com.sohu.middle.api.bo.SohuUserTagBo;
import com.sohu.middle.api.bo.SohuUserTagFriendAddBo;
import com.sohu.middle.api.vo.SohuFriendsVo;
import com.sohu.middle.api.vo.SohuUserTagVo;
import com.sohu.middleService.domain.SohuUserTag;
import com.sohu.middleService.mapper.SohuFriendsMapper;
import com.sohu.middleService.mapper.SohuUserTagMapper;
import com.sohu.middleService.service.ISohuFriendsService;
import com.sohu.middleService.service.ISohuUserTagService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 用户的标签Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-10
 */
@RequiredArgsConstructor
@Service
public class SohuUserTagServiceImpl extends SohuBaseServiceImpl<SohuUserTagMapper, SohuUserTag, SohuUserTagVo> implements ISohuUserTagService {

    private final SohuFriendsMapper sohuFriendsMapper;

    private final ISohuFriendsService sohuFriendsService;

    /**
     * 查询用户的标签
     */
    @Override
    public SohuUserTagVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询用户的标签列表
     */
    @Override
    public TableDataInfo<SohuUserTagVo> queryPageList(SohuUserTagBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuUserTag> lqw = buildQueryWrapper(bo);
        Page<SohuUserTagVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询用户的标签列表
     */
    @Override
    public List<SohuUserTagVo> queryList(SohuUserTagBo bo) {
        LambdaQueryWrapper<SohuUserTag> lqw = buildQueryWrapper(bo);
        List<SohuUserTagVo> list = baseMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        for (SohuUserTagVo sohuUserTagVo : list) {
            List<SohuFriendsVo> friendsVos = sohuFriendsMapper.friendList(bo.getUserId(), sohuUserTagVo.getId(), ApplyStateEnum.pass.name());
            sohuUserTagVo.setTagFriendCount(CollUtil.isEmpty(friendsVos) ? 0 : friendsVos.size());
        }
        return list;
    }

    private LambdaQueryWrapper<SohuUserTag> buildQueryWrapper(SohuUserTagBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuUserTag> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuUserTag::getUserId, bo.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getName()), SohuUserTag::getName, bo.getName());
        return lqw;
    }

    /**
     * 新增用户的标签
     */
    @Override
    public Long insertByBo(SohuUserTagBo bo) {
        SohuUserTag add = BeanUtil.toBean(bo, SohuUserTag.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return add.getId();
    }

    /**
     * 修改用户的标签
     */
    @Override
    public Boolean updateByBo(SohuUserTagBo bo) {
        SohuUserTag update = BeanUtil.toBean(bo, SohuUserTag.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuUserTag entity) {
        //TODO 做一些数据校验,如唯一约束
        entity.setUserId(LoginHelper.getUserId());
        SohuUserTag exist = this.baseMapper.selectOne(SohuUserTag::getUserId, entity.getUserId(), SohuUserTag::getName, entity.getName());
        if (entity.getId() == null || entity.getId() <= 0L) {
            // 新增标签
            if (Objects.nonNull(exist)) {
                throw new ServiceException("标签已存在!");
            }
        } else {
            // 编辑标签
            if (Objects.nonNull(exist) && !exist.getId().equals(entity.getId())) {
                throw new ServiceException("标签已存在!");
            }
        }
    }

    /**
     * 批量删除用户的标签
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        Long userId = LoginHelper.getUserId();
        for (Long id : ids) {
            sohuFriendsMapper.deleteUserTag(userId, id);
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<SohuUserTagVo> queryByUserId(Long userId, String tags) {
        LambdaQueryWrapper<SohuUserTag> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuUserTag::getUserId, userId);
        if (StrUtil.isBlankIfStr(tags)) {
            return this.baseMapper.selectVoList(lqw);
        }
        Set<String> names = new HashSet<>();
        StrUtil.split(tags, StrUtil.COMMA).forEach(tag -> {
            if (!NumberUtil.isNumber(tag)) {
                names.add(tag);
            }
        });
        return null;
    }

    @Override
    public Long count(Long loginId) {
        return baseMapper.selectCount(SohuUserTag::getUserId, loginId);
    }

    @Override
    public Boolean setFriendTag(SohuUserTagFriendAddBo bo) {
        Long loginId = LoginHelper.getUserId();
        if (loginId == null || loginId <= 0L) {
            log.warn("addFriendTag error,未获取到登录人id");
            return Boolean.FALSE;
        }
        Long tId = bo.getTagId();
        long tagId = 0L;
        if (CalUtils.isNullOrZero(tId)) {
            SohuUserTag exist = this.query(loginId, bo.getName());
            if (Objects.nonNull(exist)) {
                throw new ServiceException("标签已存在!");
            }
//            if (Objects.isNull(exist)) {
//                SohuUserTagBo tagBo = new SohuUserTagBo();
//                tagBo.setName(bo.getName());
//                tagId = insertByBo(tagBo);
//            } else {
//                tagId = exist.getId();
//            }
            SohuUserTagBo tagBo = new SohuUserTagBo();
            tagBo.setName(bo.getName());
            tagId = insertByBo(tagBo);
        } else {
            tagId = tId;
            SohuUserTag userTag = baseMapper.selectById(tId);
            if (Objects.isNull(userTag) || !Objects.equals(userTag.getUserId(), loginId)) {
                throw new ServiceException("标签不存在");
            }
            userTag.setName(bo.getName());
            baseMapper.updateById(userTag);
        }
        SohuFriendsBo friendsBo = new SohuFriendsBo();
        friendsBo.setIds(bo.getFriendIds());
        friendsBo.setTagIds(String.valueOf(tagId));
        return sohuFriendsService.setTag(friendsBo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delTag(Long tagId) {
        sohuFriendsService.delTag(tagId);
        baseMapper.deleteById(tagId);
        return Boolean.TRUE;
    }

    private SohuUserTag query(Long userId, String name) {
        return this.baseMapper.selectOne(SohuUserTag::getUserId, userId, SohuUserTag::getName, name);
    }

}
