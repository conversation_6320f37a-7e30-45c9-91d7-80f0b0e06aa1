package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuArticleRelateBo;
import com.sohu.middle.api.vo.RelationRespVo;
import com.sohu.middle.api.vo.SohuArticleRelateVo;
import com.sohu.middleService.domain.SohuArticleRelate;
import com.sohu.middleService.mapper.SohuArticleRelateMapper;
import com.sohu.middleService.service.ISohuArticleRelateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 图文与关联项的关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@RequiredArgsConstructor
@Service
public class SohuArticleRelateServiceImpl extends SohuBaseServiceImpl<SohuArticleRelateMapper, SohuArticleRelate, SohuArticleRelateVo> implements ISohuArticleRelateService {

//    private final SohuArticleRelateMapper baseMapper;

//    /**
//     * 查询图文与关联项的关联
//     */
//    @Override
//    public SohuArticleRelateVo queryById(Long id) {
//        return baseMapper.selectVoById(id);
//    }

    /**
     * 查询图文与关联项的关联列表
     */
    @Override
    public TableDataInfo<SohuArticleRelateVo> queryPageList(SohuArticleRelateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuArticleRelate> lqw = buildQueryWrapper(bo);
        Page<SohuArticleRelateVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询图文与关联项的关联列表
     */
    @Override
    public List<SohuArticleRelateVo> queryList(SohuArticleRelateBo bo) {
        LambdaQueryWrapper<SohuArticleRelate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuArticleRelate> buildQueryWrapper(SohuArticleRelateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuArticleRelate> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getArticleId() != null, SohuArticleRelate::getArticleId, bo.getArticleId());
        lqw.eq(StringUtils.isNotBlank(bo.getBusyType()), SohuArticleRelate::getBusyType, bo.getBusyType());
        lqw.eq(bo.getBusyCode() != null, SohuArticleRelate::getBusyCode, bo.getBusyCode());
        return lqw;
    }

    /**
     * 新增图文与关联项的关联
     */
    @Override
    public Boolean insertByBo(SohuArticleRelateBo bo) {
        SohuArticleRelate add = BeanUtil.toBean(bo, SohuArticleRelate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改图文与关联项的关联
     */
    @Override
    public Boolean updateByBo(SohuArticleRelateBo bo) {
        SohuArticleRelate update = BeanUtil.toBean(bo, SohuArticleRelate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuArticleRelate entity) {
        // TODO 做一些数据校验,如唯一约束
    }

//    /**
//     * 批量删除图文与关联项的关联
//     */
//    @Override
//    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        if (isValid) {
//            // TODO 做一些业务上的校验,判断是否需要校验
//        }
//        return baseMapper.deleteBatchIds(ids) > 0;
//    }

    @Override
    public SohuArticleRelateVo getByArticleId(Long articleId) {
        /*if (Constants.cacheOff) {
            LambdaQueryWrapper<SohuArticleRelate> lqw = new LambdaQueryWrapper<>();
            lqw.eq(SohuArticleRelate::getArticleId, articleId);
            return this.baseMapper.selectOne(lqw);
        }
        BaseEntity.Cache cache = SohuArticleRelate.class.getAnnotation(BaseEntity.Cache.class);
        if (CacheMgr.exists(cache.region(), String.valueOf(articleId))) {
            return (SohuArticleRelate) CacheMgr.get(cache.region(), String.valueOf(articleId));
        }*/
        LambdaQueryWrapper<SohuArticleRelate> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuArticleRelate::getArticleId, articleId);
        SohuArticleRelateVo relate = this.baseMapper.selectVoOne(lqw);
        if (Objects.isNull(relate)) {
            return null;
        }
        //CacheMgr.set(cache.region(), String.valueOf(articleId), relate);
        return relate;
    }

    @Override
    public void insert(String relateType, Long busyCode) {

    }

    @Override
    public void deleteByArticleIds(Collection<Long> ids) {
        this.baseMapper.delete(SohuArticleRelate::getArticleId, ids);
    }

    @Override
    public RelationRespVo buildRelationInfo(SohuArticleRelateVo relate) {
        RelationRespVo relationRespVO = new RelationRespVo();
        relationRespVO.setBusyCode(relate.getBusyCode());
        relationRespVO.setBusyType(relate.getBusyType());
        relationRespVO.setBusyTitle(relate.getBusyTitle());
        relationRespVO.setBusyInfo(relate.getBusyInfo());
        return relationRespVO;
    }

}
