package com.sohu.middleService.service.adInfo;

import com.sohu.middle.api.vo.SohuAdInfoVo;

/**
 * 广告信息构建器接口
 * 定义了构建不同类型广告信息的标准
 *
 * <AUTHOR>
 */
public interface AdInfoBuilder {

    /**
     * 判断是否支持构建该类型的广告
     *
     * @param objType 广告类型，例如 "Video", "Playlet", "Article" 等
     * @return 如果支持，返回 true，否则返回 false
     */
    boolean supports(String objType);

    /**
     * 构建广告信息
     *
     * @param adInfoVo 原始广告信息
     * @return 构建后的广告信息
     */
    SohuAdInfoVo build(SohuAdInfoVo adInfoVo);

    /**
     * 扩展广告信息
     *
     * @param adInfoVo 需要扩展的广告信息对象
     */
    void extendAdInfo(SohuAdInfoVo adInfoVo);
}