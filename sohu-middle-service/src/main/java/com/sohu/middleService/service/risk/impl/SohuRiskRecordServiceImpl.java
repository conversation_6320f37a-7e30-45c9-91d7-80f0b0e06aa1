package com.sohu.middleService.service.risk.impl;


import cn.hutool.core.bean.BeanUtil;
import com.sohu.middle.api.bo.risk.RiskCheckBo;
import com.sohu.middle.api.bo.risk.SohuRiskRecordBo;
import com.sohu.middle.api.vo.risk.SohuRiskRecordVo;
import com.sohu.middleService.domain.risk.SohuRiskRecord;
import com.sohu.middleService.mapper.risk.SohuRiskRecordMapper;
import com.sohu.middleService.service.risk.ISohuRiskRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 风控记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@RequiredArgsConstructor
@Service
public class SohuRiskRecordServiceImpl implements ISohuRiskRecordService {

    private final SohuRiskRecordMapper baseMapper;

    /**
     * 新增风控记录
     */
    @Override
    public Long insertByBo(SohuRiskRecordBo bo) {
        SohuRiskRecord add = BeanUtil.toBean(bo, SohuRiskRecord.class);
        baseMapper.insert(add);
        return add.getId();
    }

    /**
     * 查询风控记录
     *
     * @param id
     * @return
     */
    @Override
    public SohuRiskRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }
}
