package com.sohu.middleService.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuRegionBo;
import com.sohu.middle.api.vo.SohuRegionVo;
import com.sohu.middleService.domain.SohuRegion;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 城市Service接口
 *
 * <AUTHOR>
 * @date 2023-09-21
 */
public interface ISohuRegionService extends ISohuBaseService<SohuRegion, SohuRegionVo> {

    /**
     * 查询城市
     */
    SohuRegionVo queryById(Long id);

    /**
     * 查询城市列表
     */
    TableDataInfo<SohuRegionVo> queryPageList(SohuRegionBo bo, PageQuery pageQuery);

    /**
     * 查询城市列表
     */
    List<SohuRegionVo> queryList(SohuRegionBo bo);

    /**
     * 修改城市
     */
    Boolean insertByBo(SohuRegionBo bo);

    /**
     * 修改城市
     */
    Boolean updateByBo(SohuRegionBo bo);

    /**
     * 校验并批量删除城市信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取所有市
     *
     * @param cityId
     * @return
     */
    List<SohuRegionVo> getDistrictsByCityId(Long cityId);

    /**
     * 获取所有区
     *
     * @param parentId
     * @return
     */
    List<SohuRegionVo> getCitiesByProvinceId(Long parentId);

    Map<Long, List<SohuRegionVo>> mapDistrictsByCityId();

    Map<Long, List<SohuRegionVo>> mapCitiesByProvinceId();

    /**
     * 查询城市-属于国外
     * @return
     */
    List<SohuRegionVo> regionTreeOfInland();

    /**
     * 查询城市-属于国外
     * @return
     */
    List<SohuRegionVo> regionTreeOfOverseas();

    /**
     * 查询国家或地区-属于国外
     * @return
     */
    List<SohuRegionVo> selectCountryListOfOverseas();

    /**
     * 根据区划编码获取区划名称
     */
    List<String> queryRegionName(String[] agencyCode);
}
