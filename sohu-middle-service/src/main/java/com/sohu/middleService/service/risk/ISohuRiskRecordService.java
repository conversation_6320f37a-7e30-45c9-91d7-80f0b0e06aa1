package com.sohu.middleService.service.risk;


import com.sohu.middle.api.bo.risk.SohuRiskRecordBo;
import com.sohu.middle.api.vo.risk.SohuRiskRecordVo;

/**
 * 风控记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface ISohuRiskRecordService {
    /**
     * 新增风控记录
     *
     * @param bo 风控记录
     * @return 结果
     */
    Long insertByBo(SohuRiskRecordBo bo);

    /**
     * 查询风控记录
     *
     * @return
     */
    SohuRiskRecordVo queryById(Long id);
}
