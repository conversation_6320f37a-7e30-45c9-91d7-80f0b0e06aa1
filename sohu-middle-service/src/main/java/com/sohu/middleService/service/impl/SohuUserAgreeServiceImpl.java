package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.entry.api.RemotePlatformConfigService;
import com.sohu.entry.api.vo.SohuPlatformConfigVo;
import com.sohu.middle.api.bo.SohuUseBo;
import com.sohu.middle.api.bo.SohuUserAgreeBo;
import com.sohu.middle.api.bo.SohuUserAgreeQueryBo;
import com.sohu.middle.api.vo.SohuAgreeVersionVo;
import com.sohu.middle.api.vo.SohuUserAgreeInfoVo;
import com.sohu.middle.api.vo.SohuUserAgreeListVo;
import com.sohu.middle.api.vo.SohuUserAgreeVo;
import com.sohu.middleService.domain.SohuAgreeVersion;
import com.sohu.middleService.domain.SohuUserAgree;
import com.sohu.middleService.mapper.SohuAgreeVersionMapper;
import com.sohu.middleService.mapper.SohuUserAgreeMapper;
import com.sohu.middleService.service.ISohuUserAgreeService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 用户协议Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-21
 */
@RequiredArgsConstructor
@Service
public class SohuUserAgreeServiceImpl implements ISohuUserAgreeService {

    private final SohuUserAgreeMapper baseMapper;
    private final SohuAgreeVersionMapper sohuAgreeVersionMapper;

    @DubboReference
    private RemotePlatformConfigService remotePlatformConfigService;

    /**
     * 查询用户协议
     */
    @Override
    public SohuUserAgreeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询用户协议列表
     */
    @Override
    public TableDataInfo<SohuUserAgreeListVo> queryPageList(SohuUserAgreeQueryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuUserAgree> lqw = buildQueryWrapper(bo);
        Page<SohuUserAgree> sohuUserAgreePage = baseMapper.selectPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuUserAgree> records = sohuUserAgreePage.getRecords();
        List<SohuUserAgreeListVo> sohuUserAgreeListVos = BeanCopyUtils.copyList(records, SohuUserAgreeListVo.class);
        TableDataInfo<SohuUserAgreeListVo> tableDataInfo = TableDataInfoUtils.build(sohuUserAgreeListVos);
        tableDataInfo.setTotal(sohuUserAgreePage.getTotal());
        return tableDataInfo;
    }

    /**
     * 查询用户协议列表
     */
    @Override
    public List<SohuUserAgreeVo> queryList(SohuUserAgreeQueryBo bo) {
        LambdaQueryWrapper<SohuUserAgree> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuUserAgree> buildQueryWrapper(SohuUserAgreeQueryBo bo) {
        LambdaQueryWrapper<SohuUserAgree> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getType()), SohuUserAgree::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuUserAgree::getState, bo.getState());
        lqw.like(StringUtils.isNotBlank(bo.getName()), SohuUserAgree::getName, bo.getName());
        return lqw;
    }

    /**
     * 新增用户协议
     */
    @Override
    public Boolean insertByBo(SohuUserAgreeBo bo) {
        SohuUserAgree add = BeanUtil.toBean(bo, SohuUserAgree.class);
        validEntityBeforeSave(add);
        add.setCreateTime(new Date());
        add.setState(CommonState.Edit.getCode());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户协议
     */
    @Override
    public Boolean updateByBo(SohuUserAgreeBo bo) {
        SohuUserAgree update = BeanUtil.toBean(bo, SohuUserAgree.class);
        update.setCreateTime(new Date());
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 批量删除用户协议
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean updateState(SohuUseBo bo) {
        // 校验是否关联角色入驻配置
        if (StringUtils.isNotBlank(bo.getState()) && StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.OffShelf.getCode())) {
            List<SohuPlatformConfigVo> list = remotePlatformConfigService.queryListByAgreeId(bo.getId());
            if (CollUtil.isNotEmpty(list)) {
                throw new ServiceException("协议已关联入驻配置信息，无法禁用");
            }
        }

        SohuUserAgree userAgree = baseMapper.selectById(bo.getId());
        if (Objects.nonNull(userAgree)) {
            validEntityBeforeSave(userAgree);
            userAgree.setState(bo.getState());
            baseMapper.updateById(userAgree);
        }
        return true;
    }

    @Override
    public SohuUserAgreeInfoVo getInfoById(Long id) {
        SohuUserAgree userAgree = baseMapper.selectById(id);
        if (Objects.nonNull(userAgree)) {
            SohuUserAgreeInfoVo userAgreeInfoVo = BeanCopyUtils.copy(userAgree, SohuUserAgreeInfoVo.class);
            userAgreeInfoVo.setAgreeId(userAgree.getId());
            LambdaQueryWrapper<SohuAgreeVersion> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SohuAgreeVersion::getAgreeId, id);
            wrapper.eq(SohuAgreeVersion::getState, CommonState.OnShelf.getCode());
            SohuAgreeVersionVo sohuAgreeVersionVo = sohuAgreeVersionMapper.selectVoOne(wrapper);
            if (Objects.nonNull(sohuAgreeVersionVo)) {
                userAgreeInfoVo.setVersionId(sohuAgreeVersionVo.getId());
                BeanCopyUtils.copy(sohuAgreeVersionVo, userAgreeInfoVo);
            }
            userAgreeInfoVo.setName(userAgree.getName());
            return userAgreeInfoVo;
        }
        return null;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuUserAgree entity) {
        entity.setUserId(LoginHelper.getUserId());
        entity.setUpdateTime(new Date());
    }
}
