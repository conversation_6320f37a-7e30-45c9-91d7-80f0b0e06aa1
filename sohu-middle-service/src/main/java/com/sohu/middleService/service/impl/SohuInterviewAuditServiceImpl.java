package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.SystemNoticeEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.noticeContent.NoticeContentDetail;
import com.sohu.im.api.noticeContent.NoticeSystemContent;
import com.sohu.middle.api.bo.SohuInterviewAuditBo;
import com.sohu.middle.api.bo.SohuWhiteListBo;
import com.sohu.middle.api.service.RemoteMiddleWhiteListService;
import com.sohu.middle.api.service.notice.RemoteMiddleSystemNoticeService;
import com.sohu.middle.api.vo.SohuInterviewAuditVo;
import com.sohu.middleService.domain.SohuInterviewAudit;
import com.sohu.middleService.mapper.SohuInterviewAuditMapper;
import com.sohu.middleService.service.ISohuInterviewAuditService;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 外部链接访问审核Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@RequiredArgsConstructor
@Service
public class SohuInterviewAuditServiceImpl implements ISohuInterviewAuditService {

    private final SohuInterviewAuditMapper baseMapper;

    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteMiddleWhiteListService remoteMiddleWhiteListService;

    @DubboReference
    private RemoteMiddleSystemNoticeService remoteMiddleSystemNoticeService;

    /**
     * 查询外部链接访问审核
     */
    @Override
    public SohuInterviewAuditVo queryById(Long id) {
        SohuInterviewAuditVo sohuInterviewAuditVo = baseMapper.selectVoById(id);
        if (Objects.nonNull(sohuInterviewAuditVo)) {
            Long userId = sohuInterviewAuditVo.getUserId();
            LoginUser loginUser = remoteUserService.queryById(userId);
            sohuInterviewAuditVo.setUserName(loginUser.getNickname());
        }
        return sohuInterviewAuditVo;
    }

    /**
     * 查询外部链接访问审核列表
     */
    @Override
    public TableDataInfo<SohuInterviewAuditVo> queryPageList(SohuInterviewAuditBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuInterviewAudit> lqw = buildQueryWrapper(bo);
        Page<SohuInterviewAuditVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuInterviewAuditVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<Long> userIds = records.stream().map(SohuInterviewAuditVo::getUserId).collect(Collectors.toList());
            Map<Long, LoginUser> userMap = remoteUserService.selectMap(userIds);
            for (SohuInterviewAuditVo record : records) {
                LoginUser user = userMap.get(record.getUserId());
                record.setUserName(user.getNickname());
            }
        }
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询外部链接访问审核列表
     */
    @Override
    public List<SohuInterviewAuditVo> queryList(SohuInterviewAuditBo bo) {
        LambdaQueryWrapper<SohuInterviewAudit> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuInterviewAudit> buildQueryWrapper(SohuInterviewAuditBo bo) {
        LambdaQueryWrapper<SohuInterviewAudit> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getUrl()), SohuInterviewAudit::getUrl, bo.getUrl());
        lqw.eq(bo.getUserId() != null, SohuInterviewAudit::getUserId, bo.getUserId());
        lqw.eq(bo.getAuditTime() != null, SohuInterviewAudit::getAuditTime, bo.getAuditTime());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuInterviewAudit::getState, bo.getState());
        lqw.like(StringUtils.isNotBlank(bo.getRejectReason()), SohuInterviewAudit::getRejectReason, bo.getRejectReason());
        lqw.like(StringUtils.isNotBlank(bo.getApplyReason()), SohuInterviewAudit::getApplyReason, bo.getApplyReason());
        if (StringUtils.isNotBlank(bo.getStartTime())) {
            lqw.ge(StringUtils.isNotBlank(bo.getStartTime()), SohuInterviewAudit::getCreateTime, DateUtils.beginOfTime(bo.getStartTime()));
        }
        if (StringUtils.isNotBlank(bo.getStartTime())) {
            lqw.le(StringUtils.isNotBlank(bo.getEndTime()), SohuInterviewAudit::getCreateTime, DateUtils.endOfTime(bo.getEndTime()));
        }
        lqw.orderByDesc(SohuInterviewAudit::getCreateTime);
        return lqw;
    }

    /**
     * 新增外部链接访问审核
     */
    @Override
    public Boolean insertByBo(SohuInterviewAuditBo bo) {
        SohuInterviewAudit add = BeanUtil.toBean(bo, SohuInterviewAudit.class);
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        add.setUserId(userId);
        validEntityBeforeSave(add);
        add.setState(CommonState.WaitApprove.getCode());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改外部链接访问审核
     */
    @Override
    public Boolean audit(SohuInterviewAuditBo bo) {
        SohuInterviewAudit update = baseMapper.selectById(bo.getId());
        if (StrUtil.isNotBlank(bo.getRejectReason())) {
            update.setRejectReason(bo.getRejectReason());
        }
        update.setState(bo.getState());
        //审核通过 将该链接的域名加入白名单表
        if (bo.getState().equals(CommonState.Pass.getCode())) {
            if(!bo.getUrl().startsWith("http://") && !bo.getUrl().startsWith("https://")) {
                throw new ServiceException("链接非http/https , 不支持审核通过");
            }
            SohuWhiteListBo whiteListBo = new SohuWhiteListBo();
            whiteListBo.setUrl(bo.getUrl());
            whiteListBo.setUserId(bo.getUserId());
            whiteListBo.setIsUse(1);
            remoteMiddleWhiteListService.insertByBo(whiteListBo);
            //发送审核通过消息通知
            sendMsgOfInterviewPass(update);
        }
        //如果两个人同时申请审核访问链接 先通过又拒绝的情况 需要将白名单再挪出来
        if (bo.getState().equals(CommonState.Refuse.getCode())) {
            remoteMiddleWhiteListService.cancelByUrl(bo.getUrl());
            //发送审核未通过消息通知
            sendMsgOfInterviewRefuse(update);
        }
        update.setAuditTime(new Date());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 外部链接访问审核通过发送消息通知
     *
     * @param bo
     */
    public void sendMsgOfInterviewPass(SohuInterviewAudit bo) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.interviewPass);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.Type.interview.name());
        content.setDetailId(bo.getId());
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setUserId(bo.getUserId());
        detail.setLinkUrl(bo.getUrl());
        detail.setDesc(SystemNoticeEnum.interviewPassDesc);
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(bo.getUserId(), SystemNoticeEnum.interviewPass, contentJson, SystemNoticeEnum.Type.interview);
    }

    /**
     * 外部链接访问审核未通过发送消息通知
     *
     * @param bo
     */
    public void sendMsgOfInterviewRefuse(SohuInterviewAudit bo) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.interviewRefuse);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.Type.interview.name());
        content.setDetailId(bo.getId());
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setUserId(bo.getUserId());
        detail.setLinkUrl(bo.getUrl());
        detail.setRejectReason(bo.getRejectReason());
        detail.setDesc(SystemNoticeEnum.interviewRefuseDesc);
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(bo.getUserId(), SystemNoticeEnum.interviewRefuseDesc, contentJson, SystemNoticeEnum.Type.interview);
    }

    /**
     * 新增前的数据校验以及处理
     */
    private void validEntityBeforeSave(SohuInterviewAudit entity) {
        //同一个链接同一个账号审核状态下无法重复申请
        SohuInterviewAuditBo bo = new SohuInterviewAuditBo();
        bo.setState(CommonState.WaitApprove.getCode());
        bo.setUserId(entity.getUserId());
        bo.setUrl(entity.getUrl());
        List<SohuInterviewAuditVo> vos = baseMapper.selectVoList(buildQueryWrapper(bo));
        if (CollUtil.isNotEmpty(vos)) {
            throw new ServiceException(MessageUtils.message("interact.is.exist"));
        }
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除外部链接访问审核
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public SohuInterviewAuditVo queryByUrl(String url) {
        SohuInterviewAuditBo bo = new SohuInterviewAuditBo();
        bo.setUrl(url);
        bo.setUserId(LoginHelper.getUserId());
        LambdaQueryWrapper<SohuInterviewAudit> queryWrapper = buildQueryWrapper(bo);
        return baseMapper.selectVoList(queryWrapper).get(0);
    }
}
