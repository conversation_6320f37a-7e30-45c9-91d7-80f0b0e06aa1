package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.domain.BaseCommonBo;
import com.sohu.common.core.domain.MsgContent;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.core.vo.UserBaseVo;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.airec.SohuAirecContentItemBo;
import com.sohu.middle.api.bo.gameNovel.SohuGameNovelAppPageBo;
import com.sohu.middle.api.bo.playlet.*;
import com.sohu.middle.api.bo.risk.SohuRiskMqBo;
import com.sohu.middle.api.enums.AppPlayletHomeSortEnum;
import com.sohu.middle.api.vo.*;
import com.sohu.middle.api.vo.airec.SohuAirecContentItemVo;
import com.sohu.middle.api.vo.gameNovel.SohuGameNovelAppListVo;
import com.sohu.middle.api.vo.playlet.*;
import com.sohu.middleService.domain.*;
import com.sohu.middleService.domain.playlet.SohuPlayletCart;
import com.sohu.middleService.domain.playlet.SohuPlayletRelate;
import com.sohu.middleService.mapper.*;
import com.sohu.middleService.mapper.playlet.SohuPlayletCartMapper;
import com.sohu.middleService.mapper.playlet.SohuPlayletRelateMapper;
import com.sohu.middleService.service.*;
import com.sohu.middleService.service.airec.ISohuAirecContentItemService;
import com.sohu.middleService.service.gameNovel.ISohuGameNovelService;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.third.aliyun.airec.constants.AliyunAirecConstant;
import com.sohu.third.aliyun.airec.constants.AliyunAirecTagsConstant;
import com.sohu.third.aliyun.airec.enums.AirecItemStatusEnum;
import com.sohu.third.aliyun.airec.enums.AliyunAirecContentItemTypeEnum;
import com.sohu.third.aliyun.airec.enums.AliyunAirecItemWeightEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 短剧首页分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuPlayletServiceImpl extends SohuBaseServiceImpl<SohuPlayletMapper, SohuPlaylet, SohuPlayletVo> implements ISohuPlayletService {

    private final SohuVideoMapper sohuVideoMapper;
    private final SohuVideoInfoMapper sohuVideoInfoMapper;
    private final SohuCategoryMapper sohuCategoryMapper;
    private final SohuSiteMapper sohuSiteMapper;
    private final SohuUserCollectMapper sohuUserCollectMapper;
    private final SohuTradeRecordMapper sohuTradeRecordMapper;
    private final SohuPlayletPayMapper sohuPlayletPayMapper;
    private final SohuContentMainMapper sohuContentMainMapper;
    private final SohuPlayletInfoMapper sohuPlayletInfoMapper;

    private final ISohuTradeRecordService sohuTradeRecordService;
    private final ISohuSyncContentService sohuSyncContentService;
    private final ISohuCategoryService sohuCategoryService;
    private final ISohuAirecContentItemService iSohuAirecContentItemService;
    private final ISohuIndependentMaterialService iSohuIndependentMaterialService;
    private final ISohuUserLikeService sohuUserLikeService;
    private final ISohuUserService sohuUserService;
    private final ISohuContentMainService sohuContentMainService;
    private final ISohuUserSubscribeService sohuUserSubscribeService;
    private final ISohuPlayletUserService sohuPlayletUserService;
    private final ISohuUserFollowService sohuUserFollowService;
    private final ISohuUserCollectService sohuUserCollectService;
    private final ISohuGameNovelService sohuGameNovelService;
    private final SohuPlayletRelateMapper sohuPlayletRelateMapper;
    private final SohuPlayletCartMapper sohuPlayletCartMapper;
    private final AsyncConfig asyncConfig;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;

    /**
     * 查询剧集详情
     */
    @Override
    public SohuPlayletVo queryById(Long id) {
        SohuPlayletVo sohuPlayletVo = baseMapper.selectVoById(id);
        String episodeRelevance = sohuPlayletVo.getEpisodeRelevance();
        if (StringUtils.isNotBlank(episodeRelevance)) {
            LambdaQueryWrapper<SohuVideo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SohuVideo::getEpisodeRelevance, episodeRelevance);
            wrapper.eq(SohuVideo::getState, sohuPlayletVo.getState());
            wrapper.orderByAsc(SohuVideo::getEpisodeNumber);
            List<SohuVideoVo> sohuVideoVos = sohuVideoMapper.selectVoList(wrapper);
            if (CollUtil.isNotEmpty(sohuVideoVos)) {
                sohuVideoVos.forEach(e -> {
                    LambdaQueryWrapper<SohuVideoInfo> qw = new LambdaQueryWrapper<SohuVideoInfo>().eq(SohuVideoInfo::getVideoId, e.getId());
                    SohuVideoInfo sohuVideoInfo = sohuVideoInfoMapper.selectOne(qw);
                    if (ObjectUtil.isNotNull(sohuVideoInfo)) {
                        if (StringUtils.isNotBlank(sohuVideoInfo.getIntro())) {
                            e.setIntro(sohuVideoInfo.getIntro());
                        }
                    }
                });
                sohuPlayletVo.setSohuVideoVos(sohuVideoVos);
            }
        }
        sohuPlayletVo.setCategoryName(sohuCategoryService.queryById(sohuPlayletVo.getCategoryId()).getName());
        // 短剧分销金额
        if (Objects.nonNull(sohuPlayletVo.getPlayletPrice())) {
            BigDecimal playletDivide = BigDecimalUtils.divide(sohuPlayletVo.getPlayletPrice(), CalUtils.PERCENTAGE);
            sohuPlayletVo.setIndependentPrice(sohuPlayletVo.getPlayletPrice().multiply(playletDivide).setScale(2, RoundingMode.HALF_UP));
        }
        if (StrUtil.isNotBlank(sohuPlayletVo.getSysSource()) && StrUtil.equalsAnyIgnoreCase(sohuPlayletVo.getSysSource(), SysSourceEnum.MINGLEREELS.getCode())) {
            sohuPlayletVo.setSinglePrice(CalUtils.multiply(sohuPlayletVo.getSinglePrice(), new BigDecimal("10")));
        }
        //短剧拓展信息
        LambdaQueryWrapper<SohuPlayletInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuPlayletInfo::getPlayletId, sohuPlayletVo.getId());
        SohuPlayletInfoVo sohuPlayletInfoVo = sohuPlayletInfoMapper.selectVoOne(wrapper);
        if (Objects.nonNull(sohuPlayletInfoVo)) {
            sohuPlayletVo.setSohuPlayletInfoVo(sohuPlayletInfoVo);
        }
        return sohuPlayletVo;
    }

    @Override
    public SohuPlayletVo queryByEpisodeRelevance(String episodeRelevance) {
        LambdaQueryWrapper<SohuPlaylet> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuPlaylet::getEpisodeRelevance, episodeRelevance);
        return this.baseMapper.selectVoOne(lqw);
    }

    /**
     * 查询短剧首页分类列表
     */
    @Override
    public TableDataInfo<SohuPlayletVo> queryPageList(SohuPlayletBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuPlaylet> lqw = buildQueryWrapper(bo);
        Page<SohuPlayletVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            Set<Long> userIds = new HashSet<>();
            for (SohuPlayletVo record : result.getRecords()) {
                userIds.add(record.getUserId());
            }
            Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
            for (SohuPlayletVo record : result.getRecords()) {
                // 设置用户头像以及昵称
                LoginUser loginUser = userMap.get(record.getUserId());
                if (Objects.nonNull(loginUser)) {
                    record.setUserName(loginUser.getUsername());
                    record.setNickName(loginUser.getNickname());
                    record.setUserAvatar(loginUser.getAvatar());
                } else {
                    record.setUserName(Constants.DEFAULT_USER_NICKNAME);
                    record.setNickName(Constants.DEFAULT_USER_NICKNAME);
                    record.setUserAvatar(Constants.DEFAULT_AVATAR);
                }
            }
        }
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询短剧首页分类列表
     */
    @Override
    public List<SohuPlayletVo> queryList(SohuPlayletBo bo) {
        LambdaQueryWrapper<SohuPlaylet> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuPlaylet> buildQueryWrapper(SohuPlayletBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuPlaylet> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getSiteId() != null, SohuPlaylet::getSiteId, bo.getSiteId());
        lqw.eq(bo.getUserId() != null, SohuPlaylet::getUserId, bo.getUserId());
        lqw.eq(bo.getIsRecommend() != null, SohuPlaylet::getIsRecommend, bo.getIsRecommend());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), SohuPlaylet::getTitle, bo.getTitle());
        lqw.eq(bo.getCategoryId() != null, SohuPlaylet::getCategoryId, bo.getCategoryId());
        lqw.eq(StringUtils.isNotBlank(bo.getCoverImage()), SohuPlaylet::getCoverImage, bo.getCoverImage());
        lqw.eq(bo.getSortIndex() != null, SohuPlaylet::getSortIndex, bo.getSortIndex());
        if (StrUtil.isNotEmpty(bo.getState()) && bo.getState().equals(CommonState.WaitApprove.getCode())){
            lqw.in(SohuPlaylet::getState, Arrays.asList(CommonState.WaitApprove.getCode(),CommonState.WaitRobotApprove.getCode()));
        }else {
            lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuPlaylet::getState, bo.getState());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getRejectReason()), SohuPlaylet::getRejectReason, bo.getRejectReason());
        lqw.eq(bo.getIsPay() != null, SohuPlaylet::getIsPay, bo.getIsPay());
        lqw.eq(bo.getEpisodeCount() != null, SohuPlaylet::getEpisodeCount, bo.getEpisodeCount());
        lqw.eq(StringUtils.isNotBlank(bo.getEpisodeRelevance()), SohuPlaylet::getEpisodeRelevance, bo.getEpisodeRelevance());
        lqw.eq(StringUtils.isNotBlank(bo.getIntro()), SohuPlaylet::getIntro, bo.getIntro());
        lqw.orderByDesc(SohuPlaylet::getCreateTime);
        return lqw;
    }

    /**
     * 新增短剧首页分类
     */
    @Override
    public Boolean insertByBo(SohuPlayletBo bo) {
        SohuPlaylet add = BeanUtil.toBean(bo, SohuPlaylet.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改短剧首页分类
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(SohuPlayletBo bo) {
        SohuPlaylet exist = baseMapper.selectById(bo.getId());
        SohuPlaylet update = new SohuPlaylet();
        BeanCopyUtils.copy(bo, update);
        validEntityBeforeSave(exist);
        if (bo.getSysSource().equals(Constants.MINGLEREELS)) {
            bo.setState(exist.getState());
            bo.setEpisodeRelevance(exist.getEpisodeRelevance());
            bo.setSinglePrice(CalUtils.multiply(bo.getSinglePrice(), new BigDecimal("0.1")));
        } else {
            bo.setState(CommonState.WaitRobotApprove.getCode());
        }
        BeanCopyUtils.copy(bo, update);
        update.setRejectReason(null);
        update.setEpisodeCount(bo.getVideoBoList().size());
        int updateCount = baseMapper.updateById(update);
        //修改剧集关联的视频
        String episodeRelevance = bo.getEpisodeRelevance();
        if (StringUtils.isNotBlank(episodeRelevance)) {
            LambdaQueryWrapper<SohuVideo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SohuVideo::getEpisodeRelevance, episodeRelevance);
//            wrapper.eq(SohuVideo::getState, bo.getState());
            List<SohuVideo> sohuVideos = sohuVideoMapper.selectList(wrapper);
            if (CollUtil.isNotEmpty(sohuVideos)) {
                sohuVideos.forEach(e -> {
                    e.setState(CommonState.Delete.getCode());
                    iSohuAirecContentItemService.updateStatusToOffShelf(e.getId().toString(), AliyunAirecContentItemTypeEnum.VIDEO.getCode());
                    sohuVideoMapper.updateById(e);
                    LambdaQueryWrapper<SohuVideoInfo> qw = new LambdaQueryWrapper<SohuVideoInfo>().eq(SohuVideoInfo::getVideoId, e.getId());
                    SohuVideoInfo sohuVideoInfo = sohuVideoInfoMapper.selectOne(qw);
                    if (!ObjectUtil.isNotNull((sohuVideoInfo))) {
                        sohuVideoInfoMapper.deleteById(sohuVideoInfo.getId());
                    }
                });
            }
        }
        List<SohuVideoBo> sohuVideoBoList = bo.getVideoBoList();
        if (updateCount > 0 && CollUtil.isNotEmpty(sohuVideoBoList)) {
            if (bo.getSysSource().equals(Constants.SOHUGLOBAL)) {
                //创建剧集必须最少得保留2集免费，如果只有一集或者是两集都得免费
                List<SohuVideoBo> bos = sohuVideoBoList.stream().filter(sohuVideoBo -> !sohuVideoBo.getIsPay()).collect(Collectors.toList());
                if (sohuVideoBoList.size() <= 2 && bos.size() != sohuVideoBoList.size()) {
                    throw new RuntimeException("最少保留2集免费");
                } else if (bos.size() < 2) {
                    throw new RuntimeException("最少保留2集免费");
                }
            }
            handleVideoBoList(bo.getVideoBoList(), update, bo.getSysSource());
        }
        // 素材库数据同步
        iSohuIndependentMaterialService.deleteByCodeAndType(bo.getEpisodeRelevance(), BusyType.Playlet.getType());
        if (bo.getState().equals(CommonState.WaitRobotApprove.getCode())) {
            // 发布异步机审消息
            SohuRiskMqBo riskMqBo = new SohuRiskMqBo();
            riskMqBo.setBusyType(BusyType.BusyPlaylet);
            riskMqBo.setBusyCode(update.getId().toString());
            MqMessaging mqMessaging = new MqMessaging(JSONObject.toJSONString(riskMqBo), MqKeyEnum.HANDLE_RISK_MANAGEMENT.getKey());
            remoteStreamMqService.sendDelayMsg(mqMessaging, 2L);
        }
        return updateCount > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuPlaylet entity) {
        //TODO 做一些数据校验,如唯一约束
    }

//    /**
//     * 批量删除短剧首页分类
//     */
//    @Override
//    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        return baseMapper.deleteBatchIds(ids) > 0;
//    }

//    /**
//     * 阿里云智能推荐短剧物料
//     *
//     * @param sohuPlaylet
//     */
//    @Override
//    public void updateSohuAirecContentPlayletItem(SohuPlaylet sohuPlaylet) {
//        //根据短剧id和类型查询推荐表中是否存在
//        SohuAirecContentItemModel model = remoteSohuAirecContentItemService.getByItemId(String.valueOf(sohuPlaylet.getId()), AliyunAirecContentItemTypeEnum.SHORTVIDEO.getCode());
//        //不存在就是插入
//        if (ObjectUtil.isNull(model)) {
//            //当物料表中不存在，只处理上架状态的数据
//            if (StringUtils.equalsIgnoreCase(CommonState.OnShelf.getCode(), sohuPlaylet.getState())) {
//                //创建创建model
//                model = createModel(sohuPlaylet);
//                remoteSohuAirecContentItemService.insertContentItem(model);
//            }
//        } else {
//            //更新上架状态的数据为可推荐，更新Constants.notRecommend之中的数据为不可推荐
//            if (StringUtils.equalsIgnoreCase(CommonState.OnShelf.getCode(), sohuPlaylet.getState())) {
//                model.setStatus(AirecItemStatusEnum.CONTENT_YES.getCode());
//            } else if (StringUtils.equalsAnyIgnoreCase(sohuPlaylet.getState(), com.sohu.dao.constant.Constants.notRecommend)) {
//                model.setStatus(AirecItemStatusEnum.CONTENT_NO.getCode());
//            }
//            remoteSohuAirecContentItemService.updateContentItem(model);
//        }
//    }

    @Override
    public List<SohuPlayletVo> hotPlaylet(Long categoryId, String sysSource) {
        LambdaQueryWrapper<SohuPlaylet> lqw = Wrappers.lambdaQuery();
        if (categoryId != null && categoryId > 0L) {
            lqw.eq(SohuPlaylet::getCategoryId, categoryId);
        }
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        lqw.eq(SohuPlaylet::getIsRecommend, 0);
        lqw.eq(StrUtil.isNotBlank(sysSource), SohuPlaylet::getSysSource, sysSource);
        lqw.orderByDesc(SohuPlaylet::getViewCount);
        lqw.last("limit 3");
        return this.baseMapper.selectVoList(lqw);
    }

    @Override
    public List<SohuPlayletVo> hotPlaylet(Long categoryId, int limit) {
        LambdaQueryWrapper<SohuPlaylet> lqw = Wrappers.lambdaQuery();
        if (categoryId != null && categoryId > 0L) {
            lqw.eq(SohuPlaylet::getCategoryId, categoryId);
        }
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        lqw.eq(SohuPlaylet::getIsRecommend, 0);
        lqw.orderByDesc(SohuPlaylet::getViewCount);
        lqw.last("limit " + limit);
        return this.baseMapper.selectVoList(lqw);
    }

    public TableDataInfo<SohuPlayletVo> hotPlaylet(Long categoryId, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuPlaylet> lqw = Wrappers.lambdaQuery();
        if (categoryId != null && categoryId > 0L) {
            lqw.eq(SohuPlaylet::getCategoryId, categoryId);
        }
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        lqw.eq(SohuPlaylet::getIsRecommend, 0);
        lqw.orderByDesc(SohuPlaylet::getViewCount);

        IPage<SohuPlayletVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    @Override
    public List<SohuPlayletVo> hotPlaylet(String categoryIds, String sysSource, int limit) {
        LambdaQueryWrapper<SohuPlaylet> lqw = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(categoryIds)) {
            List<String> categoryIdList = StrUtil.splitTrim(categoryIds, StrUtil.C_COMMA);
            lqw.in(CollUtil.isNotEmpty(categoryIdList), SohuPlaylet::getCategoryId, categoryIdList);
        }
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        lqw.eq(StrUtil.isNotBlank(sysSource), SohuPlaylet::getSysSource, sysSource);
        lqw.orderByDesc(SohuPlaylet::getViewCount);
        lqw.last("limit " + limit);
        return this.baseMapper.selectVoList(lqw);
    }

//    public SohuAirecContentItemModel createModel(SohuPlaylet sohuPlaylet) {
//        SohuAirecContentItemModel model = new SohuAirecContentItemModel();
//        model.setItemId(String.valueOf(sohuPlaylet.getId()));
//        model.setItemType(AliyunAirecContentItemTypeEnum.SHORTVIDEO.getCode());
//        //model.setSceneId(AliyunAirecConstant.SCENE_LIFE_SHORT_VIDEO);
//        model.setDuration(com.sohu.dao.constant.Constants.DEFAULT_VIDEO_TIME);
//        Date createTime = sohuPlaylet.getCreateTime();
//        long time = DateUtils.getTimeOfSecond(createTime);
//        model.setPubTime(String.valueOf(time));
//        model.setTitle(sohuPlaylet.getTitle());
//        model.setWeight(AliyunAirecItemWeightEnum.NOT_WEIGHT.getCode());
//        model.setAuthor(sohuPlaylet.getUserId().toString());
//        model.setContent(StringUtils.isEmpty(sohuPlaylet.getIntro()) ? sohuPlaylet.getTitle() : StringUtils.substring(sohuPlaylet.getIntro(), AliyunAirecConstant.CONTENT_SUBSTRING));
//        model.setCity(String.valueOf(sohuPlaylet.getSiteId()));
//        model.setStatus(AirecItemStatusEnum.CONTENT_YES.getCode());
//        return model;
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addPlayLet(SohuPlayletBo bo) {
        if (bo.getSysSource().equals(Constants.MINGLEREELS)) {
            if (CommonState.Edit.getCode().equals(bo.getState())) {
                SohuPlayletVo playletVo = getEditInfo();
                if (Objects.nonNull(playletVo)) {
                    baseMapper.deleteById(playletVo.getId());
                }
            }
            if (StrUtil.isBlank(bo.getState())) {
                bo.setState(CommonState.WaitShelf.getCode());
            }
            if (bo.getIsPayNumber() != null) {
                if (bo.getIsPayNumber() > 0) {
                    bo.setIsPay(true);
                }
            }
            bo.setSinglePrice(CalUtils.multiply(bo.getSinglePrice(), new BigDecimal("0.1")));
        } else {
            String state = StrUtil.isNotBlank(bo.getState()) && StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.Edit.name()) ? CommonState.Edit.name() : CommonState.WaitRobotApprove.name();
            bo.setState(state);
        }
        SohuPlaylet add = BeanUtil.toBean(bo, SohuPlaylet.class);
        add.setEpisodeRelevance(NumberUtil.getOrderNo("DJ"));
        if (bo.getSiteId() == null || bo.getSiteId() <= 0L) {
            add.setSiteId(0L);
        }
        add.setState(bo.getState());
        add.setUserId(LoginHelper.getUserId());
        //设置总集数
        add.setEpisodeCount(bo.getVideoBoList().size());
        boolean flag = baseMapper.insert(add) > 0;
        if (bo.getSysSource().equals(Constants.MINGLEREELS) && Objects.nonNull(bo.getSohuPlayletInfoBo())) {
            addPlayletInfoBo(add.getId(), bo.getSohuPlayletInfoBo());
        }
        List<SohuVideoBo> sohuVideoBoList = bo.getVideoBoList();
        if (flag && CollUtil.isNotEmpty(sohuVideoBoList)) {
            if (bo.getSysSource().equals(Constants.SOHUGLOBAL)) {
                List<SohuVideoBo> bos = sohuVideoBoList.stream().filter(sohuVideoBo -> !sohuVideoBo.getIsPay()).collect(Collectors.toList());
                if (sohuVideoBoList.size() <= 2 && bos.size() != sohuVideoBoList.size()) {
                    throw new RuntimeException("最少保留2集免费");
                } else if (bos.size() < 2) {
                    throw new RuntimeException("最少保留2集免费");
                }
            }
            // 短剧视频处理
            handleVideoBoList(sohuVideoBoList, add, bo.getSysSource());
        }
        if (bo.getState().equals(CommonState.WaitRobotApprove.getCode())) {
            // 发布异步机审消息
            SohuRiskMqBo riskMqBo = new SohuRiskMqBo();
            riskMqBo.setBusyType(BusyType.BusyPlaylet);
            riskMqBo.setBusyCode(add.getId().toString());
            MqMessaging mqMessaging = new MqMessaging(JSONObject.toJSONString(riskMqBo), MqKeyEnum.HANDLE_RISK_MANAGEMENT.getKey());
            remoteStreamMqService.sendDelayMsg(mqMessaging, 2L);
        }
        return flag;
    }

    /**
     * 增加推广配置
     */
    private void addPlayletInfoBo(Long playletId, SohuPlayletInfoBo sohuPlayletInfoBo) {
        SohuPlayletInfo sohuPlayletInfo = BeanCopyUtils.copy(sohuPlayletInfoBo, SohuPlayletInfo.class);
        sohuPlayletInfo.setPlayletId(playletId);
        sohuPlayletInfoMapper.insert(sohuPlayletInfo);
    }

    // 短剧视频处理
    private void handleVideoBoList(List<SohuVideoBo> sohuVideoBoList, SohuPlaylet sohuPlaylet, String sysSource) {
        Map<Integer, SohuPlayletRelateVo> playletRelateVoMap;
        Map<Integer, List<SohuPlayletCartVo>> playletCartVoMap;
        if (StrUtil.equalsAnyIgnoreCase(sysSource, SysSourceEnum.MINGLEREELS.getCode())) {
            //编辑剧集的时候 需要更改相应集挂载的广告
            playletRelateVoMap = queryAdsByPlayletId(sohuPlaylet.getId());
            playletCartVoMap = queryCartAdsByPlayletId(sohuPlaylet.getId());
            //获取从第几集开始付费
            if (sohuPlaylet.getSinglePrice().compareTo(BigDecimal.ZERO) > 0) {
                Integer isPayNumber = sohuPlaylet.getIsPayNumber();
                int size = sohuVideoBoList.size();
                for (int i = isPayNumber; i <= size; i++) {
                    SohuVideoBo sohuVideoBo = sohuVideoBoList.get(i - 1);
                    sohuVideoBo.setIsPay(true);
                }
            }
        } else {
            playletCartVoMap = null;
            playletRelateVoMap = null;
        }
        sohuVideoBoList.forEach(e -> {
            SohuVideo sohuVideo = BeanUtil.toBean(e, SohuVideo.class);
            if (StrUtil.isBlank(sohuVideo.getType())) {
                sohuVideo.setType(VideoEnum.Type.general.getCode());
            }

            if (sohuPlaylet.getIsRecommend() != null && sohuPlaylet.getIsRecommend() == 0) {
                sohuVideo.setType(VideoEnum.Type.playlet.getCode());
            } else {
                sohuVideo.setType(VideoEnum.Type.recommendPlaylet.getCode());
            }
            sohuVideo.setEpisodePay(sohuVideo.getIsPay());
            sohuVideo.setEpisodeRelevance(sohuPlaylet.getEpisodeRelevance());
            sohuVideo.setUserId(LoginHelper.getUserId());
            sohuVideo.setState(sohuPlaylet.getState());
            sohuVideo.setCategoryId(sohuPlaylet.getCategoryId());
            sohuVideo.setSiteId(sohuPlaylet.getSiteId());
            sohuVideo.setSysSource(sysSource);
            if (sohuVideo.getId() != null && sohuVideo.getId() > 0L) {
                // 更新
                sohuVideoMapper.updateById(sohuVideo);
                if (StrUtil.isNotBlank(e.getIntro())) {
                    LambdaUpdateWrapper<SohuVideoInfo> luw = new LambdaUpdateWrapper<>();
                    luw.set(StrUtil.isNotBlank(e.getIntro()), SohuVideoInfo::getIntro, e.getIntro());
                    luw.eq(SohuVideoInfo::getVideoId, sohuVideo.getId());
                    sohuVideoInfoMapper.update(new SohuVideoInfo(), luw);
                }
            } else {
                sohuVideoMapper.insert(sohuVideo);
                //设置视频简介信息
                SohuVideoInfo sohuVideoInfo = new SohuVideoInfo();
                sohuVideoInfo.setSiteId(sohuPlaylet.getSiteId());
                sohuVideoInfo.setVideoId(sohuVideo.getId());
                if (StringUtils.isNotBlank(e.getIntro())) {
                    sohuVideoInfo.setIntro(e.getIntro());
                }
                sohuVideoInfoMapper.insert(sohuVideoInfo);
            }

            if (StrUtil.equalsAnyIgnoreCase(sysSource, Constants.MINGLEREELS)) {
                SohuPlayletRelateVo relateVo = playletRelateVoMap.get(sohuVideo.getEpisodeNumber());
                if (Objects.nonNull(relateVo)) {
                    SohuPlayletRelate playletRelate = new SohuPlayletRelate();
                    playletRelate.setId(relateVo.getId());
                    playletRelate.setVideoId(sohuVideo.getId());
                    sohuPlayletRelateMapper.updateById(playletRelate);
                }
                List<SohuPlayletCartVo> playletCartVos = playletCartVoMap.get(sohuVideo.getEpisodeNumber());
                if (CollUtil.isNotEmpty(playletCartVos)) {
                    for (SohuPlayletCartVo playletCartVo : playletCartVos) {
                        SohuPlayletCart playletCart = new SohuPlayletCart();
                        playletCart.setId(playletCartVo.getId());
                        playletCart.setVideoId(sohuVideo.getId());
                        sohuPlayletCartMapper.updateById(playletCart);
                    }
                }
            }

            sohuSyncContentService.sync(sohuVideo);
        });
    }


    /**
     * 根据剧集id查询广告
     *
     * @param playletId
     * @return
     */
    private Map<Integer, SohuPlayletRelateVo> queryAdsByPlayletId(Long playletId) {
        HashMap<Integer, SohuPlayletRelateVo> map = new HashMap<>();
        //编辑剧集的时候 需要更改相应集挂载的广告
        LambdaQueryWrapper<SohuPlayletRelate> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuPlayletRelate::getPlayletId, playletId);
        List<SohuPlayletRelateVo> sohuPlayletRelateVos = sohuPlayletRelateMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(sohuPlayletRelateVos)) {
            for (SohuPlayletRelateVo sohuPlayletRelateVo : sohuPlayletRelateVos) {
                SohuVideo sohuVideo = sohuVideoMapper.selectById(sohuPlayletRelateVo.getVideoId());
                Integer episodeNumber = sohuVideo.getEpisodeNumber();
                map.put(episodeNumber, sohuPlayletRelateVo);
            }
        }
        return map;
    }

    /**
     * 根据剧集id查询小黄车广告
     *
     * @param playletId
     * @return
     */
    private Map<Integer, List<SohuPlayletCartVo>> queryCartAdsByPlayletId(Long playletId) {
        HashMap<Integer, List<SohuPlayletCartVo>> map = new HashMap<>();
        //编辑剧集的时候 需要更改相应集挂载的广告
        LambdaQueryWrapper<SohuPlayletCart> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuPlayletCart::getPlayletId, playletId);
        List<SohuPlayletCartVo> sohuPlayletCartVos = sohuPlayletCartMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(sohuPlayletCartVos)) {
            for (SohuPlayletCartVo sohuPlayletCartVo : sohuPlayletCartVos) {
                SohuVideo sohuVideo = sohuVideoMapper.selectById(sohuPlayletCartVo.getVideoId());
                Integer episodeNumber = sohuVideo.getEpisodeNumber();
                //一个视频可能挂多个小黄车广告
                LambdaQueryWrapper<SohuPlayletCart> cartLqw = new LambdaQueryWrapper<>();
                cartLqw.eq(SohuPlayletCart::getVideoId, sohuPlayletCartVo.getVideoId());
                List<SohuPlayletCartVo> playletCartVos = sohuPlayletCartMapper.selectVoList(cartLqw);
                map.put(episodeNumber, playletCartVos);
            }
        }
        return map;
    }


    @Override
    public TableDataInfo<SohuPlayletVo> sohuPlayletVoList(SohuPlayletBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        // 超级管理员
        boolean isAdmin = loginUser.getRoles().stream().anyMatch(role -> RoleCodeEnum.ADMIN.getCode().equals(role.getRoleKey()));
        if (!isAdmin) {
            bo.setUserId(loginUser.getUserId());
        }
        LambdaQueryWrapper<SohuPlaylet> lqw = buildQueryWrapper(bo);
        IPage<SohuPlayletVo> sohuPlayletVoIPage = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(sohuPlayletVoIPage);
    }

    @Override
    public TableDataInfo<SohuVideoVo> listVideos(String id, PageQuery pageQuery) {
        SohuPlayletVo sohuPlayletVo = baseMapper.selectVoById(id);
        IPage<SohuVideoVo> sohuVideoVos = new Page<>();
        String episodeRelevance = sohuPlayletVo.getEpisodeRelevance();
        if (StringUtils.isNotBlank(episodeRelevance)) {
            LambdaQueryWrapper<SohuVideo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SohuVideo::getEpisodeRelevance, episodeRelevance);
            wrapper.ne(SohuVideo::getState, CommonState.Delete.getCode());
            wrapper.orderByAsc(SohuVideo::getEpisodeNumber);
            sohuVideoVos = sohuVideoMapper.selectVoPage(PageQueryUtils.build(pageQuery), wrapper);
        }
        return TableDataInfoUtils.build(sohuVideoVos);
    }

    @Override
    public Integer selectCartCount(Long playletId) {
        return baseMapper.selectCartCount(playletId);
    }

    @Override
    public Integer selectCartVideoCount(Long videoId) {
        return baseMapper.selectCartVideoCount(videoId);
    }

    @Override
    public Long selectAdsCount(String type, Long videoId) {
        LambdaQueryWrapper<SohuPlayletRelate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuPlayletRelate::getBusyType, type);
        wrapper.eq(SohuPlayletRelate::getState, CommonState.OnShelf.getCode());
        wrapper.eq(SohuPlayletRelate::getVideoId, videoId);
        return sohuPlayletRelateMapper.selectCount(wrapper);
    }

    @Override
    public TableDataInfo<PlayletOrderVo> queryOrderList(PlayletOrderBo bo, PageQuery pageQuery) {
        Page<PlayletOrderVo> playletOrderVoPage = baseMapper.queryOrderList(bo, PageQueryUtils.build(pageQuery));
        return TableDataInfoUtils.build(playletOrderVoPage);
    }

    @Override
    public List<PlayletOrderVo> exportOrder(PlayletOrderBo bo) {
        return baseMapper.queryOrderList(bo);
    }

    @Override
    public List<SohuVideoVo> getMaterialsInfo(Long playletId) {
        SohuPlayletVo sohuPlayletVo = baseMapper.selectVoById(playletId);
        String episodeRelevance = sohuPlayletVo.getEpisodeRelevance();
        if (StringUtils.isNotBlank(episodeRelevance)) {
            LambdaQueryWrapper<SohuVideo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SohuVideo::getEpisodeRelevance, episodeRelevance);
            wrapper.eq(SohuVideo::getType, VideoEnum.Type.materials.getCode());
            wrapper.orderByAsc(SohuVideo::getEpisodeNumber);
            List<SohuVideoVo> sohuVideoVos = sohuVideoMapper.selectVoList(wrapper);
            if (CollUtil.isNotEmpty(sohuVideoVos)) {
                sohuVideoVos.forEach(e -> {
                    LambdaQueryWrapper<SohuVideoInfo> qw = new LambdaQueryWrapper<SohuVideoInfo>().eq(SohuVideoInfo::getVideoId, e.getId());
                    SohuVideoInfo sohuVideoInfo = sohuVideoInfoMapper.selectOne(qw);
                    if (ObjectUtil.isNotNull(sohuVideoInfo)) {
                        if (StringUtils.isNotBlank(sohuVideoInfo.getIntro())) {
                            e.setIntro(sohuVideoInfo.getIntro());
                        }
                    }
                });
            }
            return sohuVideoVos;
        }
        return null;
    }

    @Override
    public Boolean updateMaterials(PlayletMaterialsBo bo) {
        List<SohuVideoVo> sohuVideoVos = getMaterialsInfo(bo.getPlayletId());
        if (CollUtil.isNotEmpty(sohuVideoVos)) {
            sohuVideoVos.forEach(e -> {
                iSohuAirecContentItemService.updateStatusToOffShelf(e.getId().toString(), AliyunAirecContentItemTypeEnum.VIDEO.getCode());
                sohuVideoMapper.deleteById(e.getId());
                LambdaQueryWrapper<SohuVideoInfo> qw = new LambdaQueryWrapper<SohuVideoInfo>().eq(SohuVideoInfo::getVideoId, e.getId());
                SohuVideoInfo sohuVideoInfo = sohuVideoInfoMapper.selectOne(qw);
                if (!ObjectUtil.isNotNull((sohuVideoInfo))) {
                    sohuVideoInfoMapper.deleteById(sohuVideoInfo.getId());
                }
            });
        }
        addMaterials(bo);
        return true;
    }

    @Override
    public Long countByState(String state) {
        return baseMapper.selectCount(Wrappers.<SohuPlaylet>lambdaQuery().eq(SohuPlaylet::getState, state));
    }

    @Override
    public Long countByTime(String startTime, String endTime) {
        LambdaQueryWrapper<SohuPlaylet> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(SohuPlaylet::getCreateTime, startTime, endTime);
        return this.baseMapper.selectCount(wrapper);
    }

    @Override
    public List<SohuPlayletVo> queryPlayletByIds(List<Long> ids, String state) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<SohuPlaylet>()
                .in(SohuPlaylet::getId, ids)
                .eq(SohuPlaylet::getState, state));
    }

    @Override
    public void handleRobot(String busyCode, Boolean isPass, String reason) {
        SohuPlaylet sohuPlaylet = baseMapper.selectById(busyCode);
        if (Objects.isNull(sohuPlaylet)) {
            return;
        }
        if (isPass && sohuPlaylet.getState().equals(CommonState.WaitRobotApprove.getCode())) {
            sohuPlaylet.setState(CommonState.WaitApprove.getCode());
        } else {
            sohuPlaylet.setState(CommonState.Refuse.getCode());
            sohuPlaylet.setRejectReason(reason);
        }
        baseMapper.updateById(sohuPlaylet);
    }

    private void addMaterials(PlayletMaterialsBo bo) {
        List<SohuVideoBo> sohuVideoBoList = bo.getVideoBoList();
        SohuPlaylet sohuPlaylet = baseMapper.selectById(bo.getPlayletId());
        String episodeRelevance = sohuPlaylet.getEpisodeRelevance();
        sohuVideoBoList.forEach(e -> {
            SohuVideo sohuVideo = BeanUtil.toBean(e, SohuVideo.class);
            sohuVideo.setType(VideoEnum.Type.materials.getCode());
            sohuVideo.setEpisodePay(sohuVideo.getIsPay());
            sohuVideo.setEpisodeRelevance(episodeRelevance);
            sohuVideo.setUserId(LoginHelper.getUserId());
            sohuVideo.setState(bo.getState());
            sohuVideo.setCategoryId(sohuPlaylet.getCategoryId());
            sohuVideo.setSiteId(sohuPlaylet.getSiteId());
            sohuVideo.setSysSource(Constants.MINGLEREELS);
            sohuVideoMapper.insert(sohuVideo);
            //设置视频简介信息
            SohuVideoInfo sohuVideoInfo = new SohuVideoInfo();
            sohuVideoInfo.setSiteId(sohuPlaylet.getSiteId());
            sohuVideoInfo.setVideoId(sohuVideo.getId());
            if (StringUtils.isNotBlank(e.getIntro())) {
                sohuVideoInfo.setIntro(e.getIntro());
            }
            sohuVideoInfoMapper.insert(sohuVideoInfo);
            sohuSyncContentService.sync(sohuVideo);
        });
    }


    @Override
    public Boolean deletePlayLet(Long id) {
        SohuPlaylet sohuPlaylet = baseMapper.selectById(id);
        String episodeRelevance = sohuPlaylet.getEpisodeRelevance();
        if (StringUtils.isNotBlank(episodeRelevance)) {
            LambdaQueryWrapper<SohuVideo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SohuVideo::getEpisodeRelevance, episodeRelevance);
            wrapper.eq(SohuVideo::getState, sohuPlaylet.getState());
            List<SohuVideo> sohuVideos = sohuVideoMapper.selectList(wrapper);
            if (CollUtil.isNotEmpty(sohuVideos)) {
                sohuVideos.forEach(e -> {
                    e.setState(CommonState.Delete.getCode());
                    iSohuAirecContentItemService.updateStatusToOffShelf(e.getId().toString(), AliyunAirecContentItemTypeEnum.VIDEO.getCode());
                    sohuVideoMapper.updateById(e);
                    LambdaQueryWrapper<SohuVideoInfo> qw = new LambdaQueryWrapper<SohuVideoInfo>().eq(SohuVideoInfo::getVideoId, e.getId());
                    SohuVideoInfo sohuVideoInfo = sohuVideoInfoMapper.selectOne(qw);
                    if (!ObjectUtil.isNotNull((sohuVideoInfo))) {
                        sohuVideoInfoMapper.deleteById(sohuVideoInfo.getId());
                    }
                });
            }
        }
        sohuPlaylet.setState(CommonState.Delete.getCode());
        baseMapper.updateById(sohuPlaylet);
        // 延迟队列--更新广告缓存信息
        MsgContent msgContent = new MsgContent(id, CommonState.Delete.getCode(), BusyType.Playlet.name());
        MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(msgContent), Constants.ADVERTISEMENT);
        RedisUtils.delayQueue(JSONUtil.toJsonStr(mqMessaging), Constants.AD_DELAY_LONG, TimeUnit.SECONDS);
        return true;
    }

    @Override
    public Boolean auditPlayLet(SohuPlayletBo bo) {
        SohuPlaylet sohuPlaylet = updateState(bo);
//        if (success) {
//            //只处理短剧剧集部分，关联部分不做处理
//            this.updateSohuAirecContentPlayletItem(sohuPlaylet);
//        }
        // 审核通过加入素材库
        if (bo.getState().equals(CommonState.OnShelf.getCode()) && sohuPlaylet.getIsIndependent()) {
            iSohuIndependentMaterialService.insertByBo(buildBo(sohuPlaylet.getEpisodeRelevance()));
        } else if (bo.getState().equals(CommonState.CompelOff.getCode())) {
            // 获取素材对象
            SohuIndependentMaterialVo sohuIndependentMaterialVo = iSohuIndependentMaterialService.queryByCodeAndType(sohuPlaylet.getEpisodeRelevance(), BusyType.Playlet.getType());
            if (Objects.nonNull(sohuIndependentMaterialVo)) {
                iSohuIndependentMaterialService.updateByBo(new SohuIndependentMaterialBo()
                        .setId(sohuIndependentMaterialVo.getId())
                        .setStatus(CommonState.OffShelf.getCode())
                );
            }
        }
        // 延迟队列--更新广告缓存信息
        MsgContent msgContent = new MsgContent(bo.getId(), bo.getState(), BusyType.Playlet.name());
        MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(msgContent), Constants.ADVERTISEMENT);
        RedisUtils.delayQueue(JSONUtil.toJsonStr(mqMessaging), Constants.AD_DELAY_LONG, TimeUnit.SECONDS);
        return true;
    }

    @Override
    public SohuPlaylet updateState(SohuPlayletBo bo) {
        SohuPlaylet sohuPlaylet = baseMapper.selectById(bo.getId());
        sohuPlaylet.setState(bo.getState());
        if (StringUtils.isNotBlank(bo.getRejectReason())) {
            sohuPlaylet.setRejectReason(bo.getRejectReason());
        }
        String episodeRelevance = sohuPlaylet.getEpisodeRelevance();
        if (StringUtils.isNotBlank(episodeRelevance)) {
            LambdaQueryWrapper<SohuVideo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SohuVideo::getEpisodeRelevance, episodeRelevance);
            wrapper.ne(SohuVideo::getState, CommonState.Delete.getCode());
            List<SohuVideo> sohuVideos = sohuVideoMapper.selectList(wrapper);
            if (CollUtil.isNotEmpty(sohuVideos)) {
                // 将原有的单个改为批量执行处理
                for (SohuVideo sohuVideo : sohuVideos) {
                    sohuVideo.setState(bo.getState());
                    if (StringUtils.isNotBlank(bo.getRejectReason())) {
                        sohuVideo.setRejectReason(bo.getRejectReason());
                    }
                }
                sohuVideoMapper.updateBatchById(sohuVideos);
                // 异步处理智能推荐
                CompletableFuture.runAsync(() -> sohuVideos.forEach(e -> updateSohuAirecContentVideoItem(e)),
                        asyncConfig.getAsyncExecutor());
            }
        }
        baseMapper.updateById(sohuPlaylet);
        return sohuPlaylet;
    }


    /**
     * 构建SohuIndependentMaterialBo
     *
     * @param episodeRelevance 短剧唯一id
     * @return SohuIndependentMaterialBo
     */
    private SohuIndependentMaterialBo buildBo(String episodeRelevance) {
        SohuPlayletVo sohuPlayletVo = this.getPlayletInfo(episodeRelevance, 1);
        SohuIndependentMaterialBo independentMaterialBo = new SohuIndependentMaterialBo();
        independentMaterialBo.setMaterialName(sohuPlayletVo.getTitle());
        independentMaterialBo.setMaterialType(BusyType.Playlet.getType());
        independentMaterialBo.setPrice(sohuPlayletVo.getPlayletPrice());
        independentMaterialBo.setMaterialUserId(sohuPlayletVo.getUserId());
        independentMaterialBo.setSiteId(sohuPlayletVo.getSiteId());
        independentMaterialBo.setMaterialCode(sohuPlayletVo.getEpisodeRelevance());
        independentMaterialBo.setCategoryId(sohuPlayletVo.getCategoryId());
        independentMaterialBo.setStatus(CommonState.OnShelf.getCode());
        // 短剧分销金额
        if (Objects.nonNull(sohuPlayletVo.getPlayletPrice())) {
            BigDecimal playletDivide = BigDecimalUtils.divide(sohuPlayletVo.getDistributorRatio(), CalUtils.PERCENTAGE);
            independentMaterialBo.setIndependentPrice(sohuPlayletVo.getPlayletPrice().multiply(playletDivide).setScale(2, RoundingMode.HALF_UP));
        }

        return independentMaterialBo;
    }

    public void updateSohuAirecContentVideoItem(SohuVideo sohuVideo) {
//        if (sohuVideo.getIsPay()) {
//            return;
//        }
        //上架
        if (StringUtils.equalsIgnoreCase(CommonState.OnShelf.getCode(), sohuVideo.getState())) {
            //创建model
            SohuAirecContentItemBo model = buildAirecContentItemModel(sohuVideo);
            iSohuAirecContentItemService.saveAirecContentItem(model);
        } else {
            //下架
            iSohuAirecContentItemService.updateStatusToOffShelf(sohuVideo.getId().toString(), AliyunAirecContentItemTypeEnum.VIDEO.getCode());

        }
    }

    /**
     * 创建智能推荐物料数据
     *
     * @param sohuVideo
     * @return
     */
    public SohuAirecContentItemBo buildAirecContentItemModel(SohuVideo sohuVideo) {
        SohuAirecContentItemBo model = new SohuAirecContentItemBo();
        model.setItemId(String.valueOf(sohuVideo.getId()));
        model.setItemType(AliyunAirecContentItemTypeEnum.VIDEO.getCode());
//        String sceneId = String.join(AliyunAirecConstant.CONNECTED_COMMA, AliyunAirecConstant.SCENE_VIDEO_HOMEPAGE, AliyunAirecConstant.SCENE_VIDEO_MY_MAY_LIKE);
//        model.setSceneId(sceneId);
        model.setDuration(Constants.DEFAULT_VIDEO_TIME);
        Date createTime = sohuVideo.getCreateTime();
        long time = DateUtils.getTimeOfSecond(createTime);
        model.setPubTime(String.valueOf(time));
        model.setTitle(sohuVideo.getTitle());
        model.setWeight(AliyunAirecItemWeightEnum.NOT_WEIGHT.getCode());
        model.setAuthor(sohuVideo.getUserId().toString());
        model.setContent(StringUtils.isEmpty(sohuVideo.getContent()) ? sohuVideo.getTitle() : StringUtils.substring(sohuVideo.getContent(), AliyunAirecConstant.CONTENT_SUBSTRING));
        model.setCity(String.valueOf(sohuVideo.getSiteId()));
//        model.setStatus(AirecItemStatusEnum.CONTENT_YES.getCode());
        if (StringUtils.equalsAnyIgnoreCase(CommonState.OnShelf.getCode(), sohuVideo.getState())
                && Objects.equals(VisibleTypeEnum.open.getCode(), sohuVideo.getVisibleType()) && !sohuVideo.getIsPay()) {
            model.setStatus(AirecItemStatusEnum.CONTENT_YES.getCode());
        } else {
            model.setStatus(AirecItemStatusEnum.CONTENT_NO.getCode());
        }
        SohuAirecContentItemVo airecCategoryInfo = sohuCategoryService.getAirecCategoryInfoById(sohuVideo.getCategoryId());
        model.setCategoryPath(airecCategoryInfo.getCategoryPath());
        model.setCategoryLevel(airecCategoryInfo.getCategoryLevel());
        String code = sohuSiteMapper.selectCountryCodeById(sohuVideo.getCountrySiteId());
        if (StrUtil.isNotBlank(code)) {
            model.setCountry(code);
        }
        //贴标签
        this.buildTagsAndScene(sohuVideo, model);
        return model;
    }


    /**
     * 贴标签和场景
     *
     * @param sohuVideo
     * @param model
     */
    private void buildTagsAndScene(SohuVideo sohuVideo, SohuAirecContentItemBo model) {
        //标签
        Set<String> tagSet = new HashSet<>();
        //场景
        Set<String> sceneIdSet = new HashSet<>();
        //作品类型标签
        if (Objects.equals(VideoEnum.Type.general.getCode(), sohuVideo.getType())) {
            tagSet.add(AliyunAirecTagsConstant.MEDIA_TYPE_GENERAL);
            sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_HOMEPAGE);
            sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_MY_MAY_LIKE);
        } else if (Objects.equals(VideoEnum.Type.lesson.getCode(), sohuVideo.getType())) {
            tagSet.add(AliyunAirecTagsConstant.MEDIA_TYPE_LESSON);
            sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_HOMEPAGE);
            sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_MY_MAY_LIKE);
        } else if (Objects.equals(VideoEnum.Type.playlet.getCode(), sohuVideo.getType())) {
            tagSet.add(AliyunAirecTagsConstant.MEDIA_TYPE_PLAYLET);
            sceneIdSet.add(AliyunAirecConstant.SCENE_SHORT_VIDEO_LIFE);
        } else if (Objects.equals(VideoEnum.Type.recommendPlaylet.getCode(), sohuVideo.getType())) {
            tagSet.add(AliyunAirecTagsConstant.MEDIA_TYPE_PLAYLET);
            sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_HOMEPAGE);
            sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_MY_MAY_LIKE);
        }
        sceneIdSet.add(AliyunAirecConstant.SCENE_VIDEO_ALL);
        model.setTags(String.join(AliyunAirecConstant.CONNECTED_COMMA, tagSet));
        model.setSceneId(String.join(AliyunAirecConstant.CONNECTED_COMMA, sceneIdSet));
    }


    @Override
    public List<SohuPlayletVo> hotList(String sysSource, Long categoryId) {
        LambdaQueryWrapper<SohuPlaylet> lqw = Wrappers.lambdaQuery();
        if (categoryId != null && categoryId > 0L) {
            lqw.eq(SohuPlaylet::getCategoryId, categoryId);
        }
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        lqw.eq(SohuPlaylet::getIsRecommend, 0);
        lqw.eq(StrUtil.isNotBlank(sysSource), SohuPlaylet::getSysSource, sysSource);
        lqw.orderByDesc(SohuPlaylet::getViewCount);
        lqw.last("limit 10");
        return this.baseMapper.selectVoList(lqw);

    }

    @Override
    public List<SohuTopPlayletVo> categoryTopTen(String sysSource, Long siteId) {
        if (siteId == null) {
            siteId = 0L;
        }
        List<SohuCategory> categoryList = sohuCategoryMapper.selectList(SohuCategory::getBusyType, BusyType.Playlet.name(), SohuCategory::getSiteId, siteId);
        if (CollUtil.isEmpty(categoryList)) {
            SohuSite sohuSite = sohuSiteMapper.selectById(siteId);
            categoryList = sohuCategoryMapper.selectList(SohuCategory::getBusyType, BusyType.Playlet.name(), SohuCategory::getSiteId, sohuSite.getPid());
        }
        if (CollUtil.isEmpty(categoryList)) {
            return null;
        }
        List<SohuTopPlayletVo> data = new ArrayList<>();
        for (SohuCategory category : categoryList) {
            SohuTopPlayletVo vo = new SohuTopPlayletVo();
            vo.setCategoryId(category.getId());
            vo.setCategoryName(category.getName());
            vo.setList(this.hotList(sysSource, vo.getCategoryId()));
            data.add(vo);
        }
        return data;
    }

    @Override
    public TableDataInfo<SohuPlayletVo> pagePlay(SohuAppPlayletBo bo) {
        log.info("pagePlay request 【{}】", JSONUtil.toJsonStr(bo));
        LambdaQueryWrapper<SohuPlaylet> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuPlaylet::getCategoryId, bo.getCategoryId());
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        lqw.eq(StrUtil.isNotBlank(bo.getSysSource()), SohuPlaylet::getSysSource, bo.getSysSource());
        lqw.like(StrUtil.isNotBlank(bo.getTitle()), SohuPlaylet::getTitle, bo.getTitle());
        lqw.eq(SohuPlaylet::getIsRecommend, 0);
        if (StrUtil.equalsAnyIgnoreCase(SohuAppPlayletBo.LAST, bo.getSort())) {
            lqw.orderByDesc(BaseEntity::getCreateTime);
        } else {
            lqw.orderByDesc(SohuPlaylet::getViewCount);
        }
        PageQuery pageQuery = new PageQuery(bo.getPageNum(), bo.getPageSize());
        Page<SohuPlayletVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    @Override
    public List<SohuPlayletVo> topTen(String sysSource, Long siteId) {
        log.info("topTen request 【sysSource:{},siteId:{}】", sysSource, siteId);
        LambdaQueryWrapper<SohuPlaylet> lqw = new LambdaQueryWrapper<>();
        if (siteId != null && siteId > 0L) {
            lqw.eq(SohuPlaylet::getSiteId, siteId);
        }
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        lqw.eq(SohuPlaylet::getIsRecommend, 0);
        lqw.eq(StrUtil.isNotBlank(sysSource), SohuPlaylet::getSysSource, sysSource);
        lqw.orderByAsc(SohuPlaylet::getSortIndex);
        lqw.last("limit 10");
        return this.baseMapper.selectVoList(lqw);
    }

    @Override
    public TableDataInfo<SohuPlayletVo> searchPlaylet(SohuSearchPlayletBo bo) {
        log.info("searchPlaylet request 【{}】", JSONUtil.toJsonStr(bo));
        LambdaQueryWrapper<SohuPlaylet> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        if (StrUtil.isNotBlank(bo.getKeyword())) {
            lqw.like(SohuPlaylet::getTitle, bo.getKeyword());
        }
        lqw.eq(StrUtil.isNotBlank(bo.getSysSource()), SohuPlaylet::getSysSource, bo.getSysSource());
        lqw.orderByDesc(SohuPlaylet::getViewCount);
        PageQuery pageQuery = new PageQuery(bo.getPageNum(), bo.getPageSize());
        IPage<SohuPlayletVo> sohuPlayletVoIPage = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(sohuPlayletVoIPage);
    }

    @Override
    public Boolean initAirecContentItems() {
        LambdaQueryWrapper<SohuPlaylet> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.name());
        //设置分页参数
        // 查询总数
        long total = baseMapper.selectCount(lqw);
        final int PAGE_SIZE = AliyunAirecConstant.BATCH_SIZE;
        // 总页数
        long totalPages = (total + PAGE_SIZE - 1) / PAGE_SIZE;
        for (int i = 1; i <= totalPages; i++) {
            // 分页查询
            Page<SohuPlaylet> page = new Page<>(i, PAGE_SIZE);
            IPage<SohuPlaylet> pageResult = baseMapper.selectPage(page, lqw);
            List<SohuPlaylet> list = pageResult.getRecords();
            // 处理查询结果
            if (CollUtil.isNotEmpty(list)) {
//                //物料信息记录
//                List<SohuAirecContentItemModel> modelList = list.stream().map(p -> createModel(p)).collect(Collectors.toList());
//
//                //保存物料信息
//                remoteSohuAirecContentItemService.initAirecContentItems(modelList);
                //todo
            }
        }
        return true;
    }

    @Override
    public Long needPayVideoCount(String episodeRelevance) {
        LambdaQueryWrapper<SohuVideo> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuVideo::getEpisodeRelevance, episodeRelevance);
        lqw.eq(SohuVideo::getState, CommonState.OnShelf.getCode());
        lqw.eq(SohuVideo::getEpisodePay, Boolean.TRUE);
        return sohuVideoMapper.selectCount(lqw);
    }

    @Override
    public Long userNeedPayVideoCount(String episodeRelevance, Long userId) {
        List<SohuVideo> payVideos = sohuVideoMapper.selectList(SohuVideo::getEpisodeRelevance, episodeRelevance, SohuVideo::getEpisodePay, true);
        if (CollUtil.isEmpty(payVideos)) {
            return 0L;
        }
        List<Long> videoIds = payVideos.stream().map(SohuVideo::getId).collect(Collectors.toList());
        LambdaQueryWrapper<SohuVideo> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuVideo::getEpisodeRelevance, episodeRelevance);
        lqw.eq(SohuVideo::getState, CommonState.OnShelf.getCode());
        lqw.eq(SohuVideo::getEpisodePay, Boolean.TRUE);
        LambdaQueryWrapper<SohuTradeRecord> lq = new LambdaQueryWrapper<>();
        lq.eq(SohuTradeRecord::getUserId, userId).eq(SohuTradeRecord::getType, SohuTradeRecordEnum.Type.Video.getCode()).eq(SohuTradeRecord::getConsumeType, SohuTradeRecordEnum.Type.Video.getCode()).in(SohuTradeRecord::getConsumeCode, videoIds);
        // 用户已经付费的视频数量
        Long paidCount = sohuTradeRecordMapper.selectCount(lq);
        // 短剧需要付费的视频数量
        Long totalNeedPayCount = sohuVideoMapper.selectCount(lqw);
        return paidCount == null ? totalNeedPayCount : totalNeedPayCount - paidCount;
    }

    @Override
    public TableDataInfo<SohuVideoVo> listCollect(SohuSearchPlayletBo bo) {
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            return TableDataInfoUtils.build();
        }
        Page<SohuVideoVo> ipage = new Page<>(bo.getPageNum(), bo.getPageSize());
        IPage<SohuVideoVo> page = sohuUserCollectMapper.selectVideoPage(userId, bo, ipage);
        List<SohuVideoVo> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfoUtils.build();
        }
        TableDataInfo<SohuVideoVo> result = TableDataInfoUtils.build();
        result.setTotal(page.getTotal());
        result.setData(records);
        return result;
    }

    @Override
    public TableDataInfo<SohuPlayletPayVo> purchaseList(SohuSearchPlayletBo bo) {
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            return TableDataInfoUtils.build();
        }
        PageQuery pageQuery = new PageQuery(bo.getPageNum(), bo.getPageSize());
        LambdaQueryWrapper<SohuPlayletPay> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuPlayletPay::getUserId, userId);
        IPage<SohuPlayletPayVo> page = sohuPlayletPayMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuPlayletPayVo> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfoUtils.build();
        }
        for (SohuPlayletPayVo record : records) {
            Long busyCode = record.getBusyCode();
            SohuPlayletVo playletVo = new SohuPlayletVo();
            if (StrUtil.equalsAnyIgnoreCase(record.getBusyType(), BusyType.Video.name())) {
                SohuVideoVo videoVo = sohuVideoMapper.selectVoById(busyCode);
                record.setVideoTitle(videoVo.getTitle());
                record.setVideoIntro(videoVo.getIntro());
                playletVo = this.queryByEpisodeRelevance(videoVo.getEpisodeRelevance());
            } else if (StrUtil.equalsAnyIgnoreCase(record.getBusyType(), BusyType.Playlet.name())) {
                playletVo = this.baseMapper.selectVoById(busyCode);
            }
            record.setPlayletCoverImage(playletVo.getCoverImage());
            record.setPlayletTitle(playletVo.getTitle());
            record.setPlayletIntro(playletVo.getIntro());
            record.setPayTime(record.getUpdateTime());
        }
        page.setRecords(records);
        return TableDataInfoUtils.build(page);
    }

    @Override
    public TableDataInfo<SohuPlayletVo> playletPageContentCenter(String title, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuPlaylet> lqw = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(title)) {
            lqw.like(SohuPlaylet::getTitle, title);

        }
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        lqw.orderByDesc(SohuPlaylet::getUpdateTime);
        IPage<SohuPlayletVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);

    }

    @Override
    public SohuPlayletVo getPlayletInfo(String episodeRelevance, Integer episodeNumber) {
        LambdaQueryWrapper<SohuPlaylet> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuPlaylet::getEpisodeRelevance, episodeRelevance);
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        SohuPlayletVo playletVo = this.baseMapper.selectVoOne(lqw);
        if (Objects.isNull(playletVo)) {
            throw new ServiceException("当前剧集信息不存在,请联系管理员或相关人员");
        }
        playletVo.setEpisodeNumber(episodeNumber);
      /*  if (!playletVo.getIsPay()) {
            return playletVo;
        }*/
        if (playletVo.getSinglePrice() == null) {
            return playletVo;
        }
        Long userId = LoginHelper.getUserId();
        Long count = 0L;
        if (userId == null || userId <= 0L) {
            // 查询需要付费的视频数量
            count = this.needPayVideoCount(episodeRelevance);
            playletVo.setPlayletPrice(CalUtils.multiply(playletVo.getSinglePrice(), new BigDecimal(count)));
        } else {
            count = this.userNeedPayVideoCount(episodeRelevance, userId);
            // 查询用户是否整部剧付费
            SohuTradeRecordVo tradeRecord = sohuTradeRecordService.queryOne(userId, SohuTradeRecordEnum.Type.Playlet.getCode(), SohuTradeRecordEnum.Type.Playlet.getCode(), String.valueOf(playletVo.getId()), PayStatus.Paid.name());
            playletVo.setAllPay(Objects.nonNull(tradeRecord));
        }
        // 单集乘以待付费金额
        BigDecimal amount = CalUtils.multiply(playletVo.getSinglePrice().abs(), new BigDecimal(count));
        if (playletVo.getIsPayLeave() != null && playletVo.getIsPayLeave() && playletVo.getDiscount() != null && CalUtils.isGreatZero(playletVo.getDiscount())) {
            // 总金额 * 折扣
            amount = CalUtils.multiply(playletVo.getSinglePrice().abs(), new BigDecimal(count), playletVo.getDiscount(), new BigDecimal(0.01));
        }
        playletVo.setPlayletPrice(amount);
        // TODO 版权方角色逻辑判断,目前版权方角色暂未定义 2024/9/30
        log.info("整部剧价格:{}", playletVo.getPlayletPrice());
        return playletVo;
    }

    @Override
    public TableDataInfo<SohuPlayletVo> getDistributionPlayletList(SohuPlayletBo bo, PageQuery pageQuery) {
        Page<SohuPlayletVo> page = baseMapper.getDistributionPlayletList(bo, PageQueryUtils.build(pageQuery));
        List<String> episodeRelevanceList = page.getRecords().stream().map(SohuPlayletVo::getEpisodeRelevance).collect(Collectors.toList());
        // 批量获取价格信息
        Map<String, SohuPlayletVo> playletInfoMap = getBatchPlayletInfo(episodeRelevanceList, LoginHelper.getUserId());
        for (SohuPlayletVo playletVo : page.getRecords()) {
            SohuPlayletVo updatedPlayletVo = playletInfoMap.get(playletVo.getEpisodeRelevance());
            if (updatedPlayletVo != null) {
                playletVo.setPlayletPrice(updatedPlayletVo.getPlayletPrice());
            }
        }
        return TableDataInfoUtils.build(page);
    }

    @Override
    public List<PlayletFollowListVo> getPublishPlayletListByPlayletUserId(Long playletUserId, Long fansUserId) {
        if (Objects.nonNull(playletUserId)) {
            List<SohuPlayletVo> sohuPlayletVoList = this.baseMapper.selectVoList(Wrappers.lambdaQuery(SohuPlaylet.class).eq(SohuPlaylet::getUserId, playletUserId).eq(SohuPlaylet::getState, CommonState.OnShelf.getCode()));
            if (CollUtil.isNotEmpty(sohuPlayletVoList)) {
                return convertSohuPlayletVoListToPlayletFollowListVo(sohuPlayletVoList, fansUserId);
            }
        }
        return new ArrayList<>();
    }

    @Override
    public TableDataInfo<PlayletPayedVo> playletPayedList(String uuid, PageQuery pageQuery) {
        Long loginId = LoginHelper.getUserId();
        if ((loginId == null || loginId <= 0L) && StrUtil.isBlankIfStr(uuid)) {
            return TableDataInfoUtils.build();
        }
        LambdaQueryWrapper<SohuTradeRecord> lqw = Wrappers.lambdaQuery();
        if (loginId != null && loginId > 0L) {
            lqw.eq(SohuTradeRecord::getUserId, loginId);
        } else {
            lqw.eq(StrUtil.isNotBlank(uuid), SohuTradeRecord::getUuid, uuid);
        }
        lqw.in(SohuTradeRecord::getType, Arrays.asList(SohuTradeRecordEnum.Type.Playlet.name(), SohuTradeRecordEnum.Type.Video.name()));
        lqw.eq(SohuTradeRecord::getPayStatus, PayStatus.Paid.name());
        lqw.orderByDesc(SohuTradeRecord::getId);
        IPage<SohuTradeRecordVo> page = sohuTradeRecordMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuTradeRecordVo> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfoUtils.build();
        }
        List<Long> videoIds = new ArrayList<>();
        for (SohuTradeRecordVo record : records) {
            if (StrUtil.isNotBlank(record.getConsumeCode()) && cn.hutool.core.util.NumberUtil.isNumber(record.getConsumeCode())) {
                videoIds.add(Long.valueOf(record.getConsumeCode()));
            }
        }
        List<SohuVideo> sohuVideos = sohuVideoMapper.selectBatchIds(videoIds);
        Map<Long, SohuVideo> videoMap = new HashMap<>();
        Set<String> episodeRelevanceList = new HashSet<>();
        for (SohuVideo sohuVideo : sohuVideos) {
            if (StrUtil.isNotBlank(sohuVideo.getEpisodeRelevance())) {
                episodeRelevanceList.add(sohuVideo.getEpisodeRelevance());
            }
            videoMap.put(sohuVideo.getId(), sohuVideo);
        }
        if (episodeRelevanceList.isEmpty()) {
            return TableDataInfoUtils.build();
        }
        List<SohuPlaylet> sohuPlaylets = baseMapper.selectList(SohuPlaylet::getEpisodeRelevance, episodeRelevanceList);
        Map<String, SohuPlaylet> playletMap = sohuPlaylets.stream().collect(Collectors.toMap(SohuPlaylet::getEpisodeRelevance, Function.identity()));
        List<PlayletPayedVo> list = new ArrayList<>();
        for (SohuTradeRecordVo record : records) {
            PlayletPayedVo payedVo = new PlayletPayedVo();
            SohuVideo sohuVideo = videoMap.get(Long.valueOf(record.getConsumeCode()));
            SohuPlaylet sohuPlaylet = playletMap.get(sohuVideo.getEpisodeRelevance());
            if (Objects.isNull(sohuPlaylet)) {
                continue;
            }
            payedVo.setVideoId(sohuVideo.getId());
            payedVo.setVideoUrl(sohuVideo.getVideoUrl());
            payedVo.setPlayletId(sohuPlaylet.getId());
            payedVo.setTitle(sohuPlaylet.getTitle());
            payedVo.setVideoTitle(sohuVideo.getTitle());
            payedVo.setPayTime(record.getPayTime());
            payedVo.setEpisodeNumber(sohuVideo.getEpisodeNumber());
            payedVo.setEpisodeCount(sohuPlaylet.getEpisodeCount());
            payedVo.setEpisodeRelevance(sohuVideo.getEpisodeRelevance());
            payedVo.setIntro(sohuPlaylet.getIntro());
            payedVo.setCoverImage(StrUtil.isBlankIfStr(sohuVideo.getCoverImage()) ? sohuPlaylet.getCoverImage() : sohuVideo.getCoverImage());
            payedVo.setPayNum(1);
            payedVo.setPayedType(record.getPayType());
            payedVo.setAmount(record.getAmount());
            list.add(payedVo);
        }
        return new TableDataInfo<>(list, page.getTotal());
    }

    @Override
    public R<Boolean> replace(String episodeRelevance, String url) {
        SohuPlaylet sohuPlaylet = baseMapper.selectOne(SohuPlaylet::getEpisodeRelevance, episodeRelevance);
        for (int i = 0; i < sohuPlaylet.getEpisodeCount(); i++) {
            SohuVideo video = new SohuVideo();
            video.setUserId(sohuPlaylet.getUserId());
            video.setEpisodeRelevance(sohuPlaylet.getEpisodeRelevance());
            video.setEpisodeNumber(i + 1);
            video.setTitle(sohuPlaylet.getTitle() + "-" + video.getEpisodeNumber());
            video.setCategoryId(sohuPlaylet.getCategoryId());
            video.setCoverImage(sohuPlaylet.getCoverImage());
            video.setVideoUrl(sohuPlaylet.getPreUrl() + video.getEpisodeNumber() + ".mp4");
            video.setSortIndex(Long.valueOf(video.getEpisodeNumber()));
            video.setState(CommonState.OnShelf.getCode());
            video.setCreateTime(new Date());
            if (i <= 6) {
                video.setIsPay(false);
                video.setEpisodePay(false);
            } else {
                video.setIsPay(true);
                video.setEpisodePay(true);
            }
            sohuVideoMapper.insert(video);

            SohuVideoInfo videoInfo = new SohuVideoInfo();
            videoInfo.setVideoId(video.getId());
            videoInfo.setIntro(sohuPlaylet.getIntro());
            sohuVideoInfoMapper.insert(videoInfo);
        }
        log.info("初始化一部短剧成功");
        return R.ok(true);
    }

    @Override
    public R<Boolean> replaceAll() {
        List<SohuPlaylet> playlets = baseMapper.selectList();
        for (SohuPlaylet sohuPlaylet : playlets) {
            String title = sohuPlaylet.getTitle();

            for (int i = 0; i < sohuPlaylet.getEpisodeCount(); i++) {
                SohuVideo video = new SohuVideo();
                video.setUserId(sohuPlaylet.getUserId());
                video.setEpisodeRelevance(sohuPlaylet.getEpisodeRelevance());
                video.setEpisodeNumber(i + 1);
                video.setTitle(title + "-" + video.getEpisodeNumber());
                video.setCategoryId(sohuPlaylet.getCategoryId());
                video.setCoverImage(sohuPlaylet.getCoverImage());
                video.setVideoUrl(sohuPlaylet.getPreUrl() + video.getEpisodeNumber() + ".mp4");
                video.setSortIndex(Long.valueOf(video.getEpisodeNumber()));
                video.setState(CommonState.OnShelf.getCode());
                video.setCreateTime(new Date());
                if (i <= 6) {
                    video.setIsPay(false);
                    video.setEpisodePay(false);
                } else {
                    video.setIsPay(true);
                    video.setEpisodePay(true);
                }
                sohuVideoMapper.insert(video);

                SohuVideoInfo videoInfo = new SohuVideoInfo();
                videoInfo.setVideoId(video.getId());
                videoInfo.setIntro(sohuPlaylet.getIntro());
                sohuVideoInfoMapper.insert(videoInfo);
            }
            log.info("初始化一部短剧成功");
        }
        return R.ok(Boolean.TRUE);
    }

    @Override
    public TableDataInfo<PlayletSearchVo> recommendList(String sysSource, PageQuery query) {
        LambdaQueryWrapper<SohuPlaylet> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        lqw.eq(StrUtil.isNotBlank(sysSource), SohuPlaylet::getSysSource, sysSource);
        lqw.eq(SohuPlaylet::getIsRecommend, 1);
        lqw.orderByDesc(SohuPlaylet::getViewCount);
        IPage<SohuPlayletVo> page = baseMapper.selectVoPage(new Page<>(query.getPageNum(), query.getPageSize()), lqw);
        if (CollUtil.isEmpty(page.getRecords())) {
            return TableDataInfoUtils.build();
        }
        TableDataInfo<PlayletSearchVo> result = new TableDataInfo<>();
        result.setData(convertToSerchVoList(page.getRecords()));
        result.setTotal(page.getTotal());
        return result;
    }

    @Override
    public TableDataInfo<PlayletCartListVo> playletList(PlayletPlayletCartQueryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuPlaylet> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(bo.getPlayletId() != null, SohuPlaylet::getId, bo.getPlayletId());
        wrapper.eq(StrUtil.isNotBlank(bo.getPlayletTitle()), SohuPlaylet::getTitle, bo.getPlayletTitle());
        wrapper.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        wrapper.eq(SohuPlaylet::getSysSource, Constants.MINGLEREELS);
        wrapper.orderByDesc(SohuPlaylet::getCreateTime);
        IPage<SohuPlayletVo> playletVos = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), wrapper);
        List<PlayletCartListVo> cartListVos = new ArrayList<>();
        List<SohuPlayletVo> vos = playletVos.getRecords();
        if (CollUtil.isNotEmpty(vos)) {
            for (SohuPlayletVo vo : vos) {
                PlayletCartListVo playletCartListVo = new PlayletCartListVo();
                playletCartListVo.setPlayletTitle(vo.getTitle());
                playletCartListVo.setPlayletId(vo.getId());
                playletCartListVo.setEpisodeCount(vo.getEpisodeCount());
                playletCartListVo.setEpisodeRelevance(vo.getEpisodeRelevance());
                Integer cartCount = baseMapper.selectCartCount(vo.getId());
                playletCartListVo.setCartCount(cartCount);
                String state = RedisUtils.getCacheObject(CacheConstants.PLAYLET_CART_STATE + vo.getId());
                if (StrUtil.isNotBlank(state)) {
                    playletCartListVo.setState(state);
                }
                cartListVos.add(playletCartListVo);
            }
        }
        TableDataInfo<PlayletCartListVo> dataInfo = TableDataInfoUtils.build(cartListVos);
        dataInfo.setTotal(playletVos.getTotal());
        return dataInfo;
    }

    @Override
    public List<PlayletCartShopListVo> playletCartList(PlayletPlayletCartQueryBo bo) {
        List<PlayletCartShopListVo> list = baseMapper.selectplayletCartList(bo);
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean initPlaylet(String episodeRelevance, int num, String preUrl) {
        SohuPlaylet sohuPlaylet = baseMapper.selectOne(SohuPlaylet::getEpisodeRelevance, episodeRelevance);
        if (Objects.isNull(sohuPlaylet)) {
            throw new RuntimeException("该剧不存在！");
        }
        for (int i = 0; i < num; i++) {
            SohuVideo video = new SohuVideo();
            video.setUserId(sohuPlaylet.getUserId());
            video.setEpisodeRelevance(sohuPlaylet.getEpisodeRelevance());
            video.setEpisodeNumber(i + 1);
            video.setCategoryId(sohuPlaylet.getCategoryId());
            video.setCoverImage(sohuPlaylet.getCoverImage());
            video.setVideoUrl(preUrl + video.getEpisodeNumber() + ".mp4");
            video.setSortIndex(Long.valueOf(video.getEpisodeNumber()));
            video.setState(CommonState.OnShelf.getCode());
            video.setCreateTime(new Date());
            video.setIsPay(false);
            sohuVideoMapper.insert(video);

            SohuVideoInfo videoInfo = new SohuVideoInfo();
            videoInfo.setVideoId(video.getId());
            videoInfo.setIntro(sohuPlaylet.getIntro());
            sohuVideoInfoMapper.insert(videoInfo);
        }
        log.info("初始化一部短剧成功");
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean like(SohuBusyBO bo) {
        SohuPlaylet SohuPlaylet = baseMapper.selectById(bo.getBusyCode());
        if (Objects.isNull(SohuPlaylet)) {
            return Boolean.FALSE;
        }
        Integer oldCount = SohuPlaylet.getPraiseCount();
        int count = bo.getIsAdd() != null && bo.getIsAdd() ? oldCount + 1 : Math.max((oldCount - 1), 0);
        SohuPlaylet.setPraiseCount(count);
        int result = baseMapper.updateById(SohuPlaylet);
        // 更新万能表的点赞数量
        sohuContentMainMapper.setPraiseCount(bo.getBusyCode(), BusyType.Playlet.name(), count);
        // 清除短剧缓存
        evictPlayletId(bo.getBusyCode());
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean collect(SohuBusyBO bo) {
        SohuPlaylet SohuPlaylet = baseMapper.selectById(bo.getBusyCode());
        if (Objects.isNull(SohuPlaylet)) {
            return Boolean.FALSE;
        }
        Integer oldCount = SohuPlaylet.getCollectCount();
        int count = bo.getIsAdd() != null && bo.getIsAdd() ? oldCount + 1 : Math.max((oldCount - 1), 0);
        SohuPlaylet.setCollectCount(count);
        int result = baseMapper.updateById(SohuPlaylet);
        // 更新万能表的收藏数量
        sohuContentMainMapper.setCollectCount(bo.getBusyCode(), BusyType.Playlet.name(), count);
        // 清除短剧缓存
        evictPlayletId(bo.getBusyCode());
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean comment(SohuCommentBo bo) {
        SohuPlaylet SohuPlaylet = baseMapper.selectById(bo.getBusyCode());
        if (Objects.isNull(SohuPlaylet)) {
            return Boolean.FALSE;
        }
        SohuPlaylet.setCommentCount(SohuPlaylet.getCommentCount() + 1);
        int result = baseMapper.updateById(SohuPlaylet);
        // 更新万能表的评论数量
        SohuContentMain sohuContentMain = sohuContentMainService.getEntityByObj(bo.getBusyCode(), BusyType.Playlet.name());
        if (Objects.nonNull(sohuContentMain)) {
            sohuContentMain.setCommentCount(SohuPlaylet.getCommentCount());
            sohuContentMainService.updateById(sohuContentMain);
        }
        // 清除短剧缓存
        evictPlayletId(bo.getBusyCode());
        return result > 0;
    }

    @Override
    public SohuPlayletVo getEditInfo() {
        Long userId = LoginHelper.getUserId();
        LambdaQueryWrapper<SohuPlaylet> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuPlaylet::getUserId, userId);
        wrapper.eq(SohuPlaylet::getState, CommonState.Edit.getCode());
        wrapper.orderByDesc(SohuPlaylet::getCreateTime);
        SohuPlayletVo sohuPlayletVo = baseMapper.selectVoOne(wrapper);
        if (Objects.nonNull(sohuPlayletVo)) {
            SohuPlayletVo vo = queryById(sohuPlayletVo.getId());
            return vo;
        }
        return null;
    }

    @Override
    public TableDataInfo<PlayletDetailListVo> queryAdminList(PlayletDetailQueryBo bo, PageQuery pageQuery) {
        Page<PlayletDetailListVo> list = baseMapper.queryPageList(bo, PageQueryUtils.build(pageQuery));
        return TableDataInfoUtils.build(list);
    }

    @Override
    public List<SohuPlayletVo> listDesc(int limit) {
        if (limit <= 0) {
            limit = 10;
        }
        LambdaQueryWrapper<SohuPlaylet> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuPlaylet::getState, CommonState.WaitShelf.getCode());
        lqw.orderByDesc(BaseEntity::getCreateTime);
        lqw.last(" limit " + limit);
        return baseMapper.selectVoList(lqw);
    }


    public TableDataInfo<SohuPlayletVo> listDesc(PageQuery pageQuery) {
        LambdaQueryWrapper<SohuPlaylet> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuPlaylet::getState, CommonState.WaitShelf.getCode());
        lqw.orderByDesc(BaseEntity::getCreateTime);
        IPage<SohuPlayletVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    @Override
    public Boolean subscribe(String episodeRelevance, BaseCommonBo commonBo) {
        SohuPlayletVo playletVo = queryByEpisodeRelevance(episodeRelevance);
        if (Objects.isNull(playletVo)) {
            throw new ServiceException(MessageUtils.message("illegal.request"));
        }
        return sohuUserSubscribeService.subscribe(BusyType.Playlet.getType(), playletVo.getId(), commonBo);
    }

    @Override
    public TableDataInfo<PlayletHomeVo> appPlayletPageList(BaseCommonBo commonBo, PlayletMoreListBo bo) {
        if (StrUtil.isBlankIfStr(bo.getIdent())) {
            return TableDataInfoUtils.build();
        }
        TableDataInfo<PlayletHomeVo> tableDataInfo = TableDataInfoUtils.build();
        PageQuery pageQuery = new PageQuery(bo.getPageNum(), bo.getPageSize());
        List<PlayletHomeVo> playletList = new LinkedList<>();
        if (StrUtil.equalsIgnoreCase(bo.getIdent(), AppPlayletHomeSortEnum.App.ContinueWatchIng.getIdent())) {
            TableDataInfo<PlayletUserViewVo> viewHistoryList = sohuPlayletUserService.getViewHistory(commonBo.getUuid(), pageQuery);
            List<PlayletUserViewVo> historyList = viewHistoryList.getData();
            if (CollUtil.isNotEmpty(historyList)) {
                for (PlayletUserViewVo userViewVo : historyList) {
                    PlayletHomeVo homeVo = new PlayletHomeVo();
                    homeVo.setPlayletId(userViewVo.getPlayletId());
                    homeVo.setTitle(userViewVo.getTitle());
                    homeVo.setViewTime(userViewVo.getViewTime());
                    homeVo.setViewCount(userViewVo.getViewCount());
                    homeVo.setCoverImage(userViewVo.getCoverImage());
                    homeVo.setEpisodeNumber(userViewVo.getEpisodeNumber());
                    homeVo.setEpisodeCount(userViewVo.getEpisodeCount());
                    homeVo.setEpisodeRelevance(userViewVo.getEpisodeRelevance());
                    // TODO 播放量
                    playletList.add(homeVo);
                }
                tableDataInfo.setTotal(viewHistoryList.getTotal());
            }
        } else if (StrUtil.equalsIgnoreCase(bo.getIdent(), AppPlayletHomeSortEnum.App.HotList.getIdent())) {
            // 热门短剧
            TableDataInfo<SohuPlayletVo> hotPlayletList = hotPlaylet(0L, pageQuery);
            if (CollUtil.isNotEmpty(hotPlayletList.getData())) {
                playletList = convertPlayletHomeVo(hotPlayletList.getData());
                tableDataInfo.setTotal(hotPlayletList.getTotal());
            }
        } else if (StrUtil.equalsIgnoreCase(bo.getIdent(), AppPlayletHomeSortEnum.App.MyList.getIdent())) {
            TableDataInfo<PlayletPayedVo> myListData = playletPayedList(commonBo.getUuid(), pageQuery);
            if (CollUtil.isNotEmpty(myListData.getData())) {
                playletList = new LinkedList<>();
                for (PlayletPayedVo payedVo : myListData.getData()) {
                    PlayletHomeVo myListVo = new PlayletHomeVo();
                    myListVo.setPlayletId(payedVo.getPlayletId());
                    myListVo.setTitle(payedVo.getTitle());
                    myListVo.setIntro(payedVo.getIntro());
                    myListVo.setCoverImage(payedVo.getCoverImage());
                    //查询当前观看集数
                    SohuPlayletUserVo sohuPlayletUserVo = sohuPlayletUserService.queryByPlayId(payedVo.getPlayletId(), commonBo.getUuid());
                    if (Objects.nonNull(sohuPlayletUserVo)) {
                        myListVo.setEpisodeNumber(sohuPlayletUserVo.getEpisodeNumber());
                        myListVo.setViewTime(sohuPlayletUserVo.getUpdateTime());
                    }
                    myListVo.setEpisodeCount(payedVo.getEpisodeCount());
                    myListVo.setEpisodeRelevance(payedVo.getEpisodeRelevance());
                    myListVo.setVideoId(payedVo.getVideoId());
                    myListVo.setVideoUrl(payedVo.getVideoUrl());
                    // TODO 播放量
                    playletList.add(myListVo);
                }
            }
            tableDataInfo.setTotal(myListData.getTotal());
        } else if (StrUtil.equalsIgnoreCase(bo.getIdent(), AppPlayletHomeSortEnum.App.ComingSoon.getIdent())) {
            TableDataInfo<SohuPlayletVo> comingSoonList = listDesc(pageQuery);
            if (CollUtil.isNotEmpty(comingSoonList.getData())) {
                playletList = convertPlayletHomeVo(comingSoonList.getData());
            }
            tableDataInfo.setTotal(comingSoonList.getTotal());
        } else if (StrUtil.equalsIgnoreCase(bo.getIdent(), AppPlayletHomeSortEnum.App.Free.getIdent())) {
            TableDataInfo<SohuPlayletVo> freeListData = freePlaylet(pageQuery);
            if (CollUtil.isNotEmpty(freeListData.getData())) {
                playletList = new LinkedList<>();
                for (SohuPlayletVo playletVo : freeListData.getData()) {
                    PlayletHomeVo homeVo = new PlayletHomeVo();
                    homeVo.setPlayletId(playletVo.getId());
                    homeVo.setTitle(playletVo.getTitle());
                    homeVo.setCoverImage(playletVo.getCoverImage());
                    homeVo.setEpisodeRelevance(playletVo.getEpisodeRelevance());
                    homeVo.setViewCount(playletVo.getViewCount());
                    playletList.add(homeVo);
                }
            }
            tableDataInfo.setTotal(freeListData.getTotal());
        } else if (StrUtil.equalsIgnoreCase(bo.getIdent(), AppPlayletHomeSortEnum.App.More.getIdent())) {
            TableDataInfo<SohuPlayletVo> moreListData = ListPlaylet(pageQuery);
            if (CollUtil.isNotEmpty(moreListData.getData())) {
                playletList = new LinkedList<>();
                for (SohuPlayletVo playletVo : moreListData.getData()) {
                    PlayletHomeVo homeVo = new PlayletHomeVo();
                    homeVo.setPlayletId(playletVo.getId());
                    homeVo.setTitle(playletVo.getTitle());
                    homeVo.setCoverImage(playletVo.getCoverImage());
                    homeVo.setEpisodeRelevance(playletVo.getEpisodeRelevance());
                    homeVo.setViewCount(playletVo.getViewCount());
                    playletList.add(homeVo);
                }
            }
            tableDataInfo.setTotal(moreListData.getTotal());
        }
        // 构建短剧作者信息
        buildPlayletAuthor(playletList);
        tableDataInfo.setCode(Constants.SUCCESS);
        tableDataInfo.setData(playletList);
        return tableDataInfo;
    }

    @Override
    public PlayletDetailInfoVo getPlayletDetailInfo(String episodeRelevance) {
        SohuPlayletVo playletInfo = this.getPlayletInfo(episodeRelevance, Constants.ONE);
        if (Objects.isNull(playletInfo)) {
            return new PlayletDetailInfoVo();
        }
        return converSohuPlayletVoToPlayletDetailInfoVo(playletInfo);
    }

    @Override
    public List<SohuPlayletVo> ListPlaylet(Long categoryId, int limit) {
        LambdaQueryWrapper<SohuPlaylet> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        if (categoryId != null && categoryId > 0L) {
            lqw.eq(SohuPlaylet::getCategoryId, categoryId);
            lqw.orderByDesc(BaseEntity::getCreateTime);
        } else {
            lqw.orderByDesc(SohuPlaylet::getViewCount);
        }
        lqw.eq(SohuPlaylet::getSysSource, Constants.MINGLEREELS);
        lqw.last("limit " + limit);
        return this.baseMapper.selectVoList(lqw);
    }

    public TableDataInfo<SohuPlayletVo> ListPlaylet(PageQuery pageQuery) {
        LambdaQueryWrapper<SohuPlaylet> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        lqw.orderByDesc(SohuPlaylet::getViewCount);
        IPage<SohuPlayletVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);

        return TableDataInfoUtils.build(result);
    }

    @Override
    public List<SohuPlayletVo> homePreferencePlaylet(String sysSource, int limit) {
        LambdaQueryWrapper<SohuPlaylet> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        lqw.eq(SohuPlaylet::getIsRecommend, 1);
        lqw.eq(StrUtil.isNotBlank(sysSource), SohuPlaylet::getSysSource, sysSource);
        lqw.orderByDesc(SohuPlaylet::getViewCount);
        lqw.last("limit " + limit);
        List<SohuPlayletVo> sohuPlayletVos = this.baseMapper.selectVoList(lqw);
        Collections.shuffle(sohuPlayletVos);
        return sohuPlayletVos;
    }

    @Override
    public List<SohuPlayletVo> homeLatestPublishPlaylet(String sysSource) {
        // 最新上线短剧列表(V1.0),当前版本先按照最简单的方式来,后续根据产品需求进行调整,此方法当数据量大会影响性能
        return baseMapper.homeLatestPublishPlaylet(sysSource);
    }

    @Override
    public List<PlayletBannerVo> bannerList(String sysSource) {
        LambdaQueryWrapper<SohuPlaylet> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        lqw.eq(StrUtil.isNotBlank(sysSource), SohuPlaylet::getSysSource, sysSource);
        lqw.eq(SohuPlaylet::getIsBanner, 1);
        List<SohuPlayletVo> sohuPlayletVos = baseMapper.selectVoList(lqw);
        return BeanCopyUtils.copyList(sohuPlayletVos, PlayletBannerVo.class);
    }

    @Override
    public List<UserBaseVo> hotPlayletAuthorList() {
        List<Long> userIds = baseMapper.selectUserIdHotPlayletAuthorList();
        if (CollUtil.isEmpty(userIds)) {
            return CollUtil.newArrayList();
        }
        Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
        Long loginId = LoginHelper.getUserId();
        // 设置作者关注状态
        Map<Long, SohuUserFollowVo> userFollowMap = sohuUserFollowService.mapUserFollows(loginId, userIds);

        List<UserBaseVo> result = new ArrayList<>();
        for (Map.Entry<Long, LoginUser> userEntry : userMap.entrySet()) {
            UserBaseVo vo = UserBaseVo.builder().build();
            LoginUser user = userEntry.getValue();
            vo.setUserId(user.getUserId());
            vo.setUserName(StrUtil.isBlankIfStr(user.getNickname()) ? user.getUsername() : user.getNickname());
            vo.setUserAvatar(StrUtil.isBlankIfStr(user.getAvatar()) ? Constants.DEFAULT_USER_AVATAR : user.getAvatar());
            vo.setIntro(user.getRemark());
            vo.setFollowAuthor(Objects.nonNull(userFollowMap.get(user.getUserId())));
            result.add(vo);
        }
        return result;
    }

    @Override
    public TableDataInfo<PlayletSearchVo> searchPlayletList(PlayletSearchBo bo) {
        LambdaQueryWrapper<SohuPlaylet> lqw = Wrappers.lambdaQuery();
        lqw.like(StrUtil.isNotBlank(bo.getSearchKey()), SohuPlaylet::getTitle, bo.getSearchKey());
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        lqw.eq(StrUtil.isNotBlank(bo.getSysSource()), SohuPlaylet::getSysSource, bo.getSysSource());
        lqw.orderByDesc(SohuPlaylet::getViewCount);
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(bo.getPageNum());
        pageQuery.setPageSize(bo.getPageSize());
        IPage<SohuPlayletVo> page = this.baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuPlayletVo> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new TableDataInfo<>(CollUtil.newArrayList(), 0L);
        }
        TableDataInfo<PlayletSearchVo> result = new TableDataInfo<>();
        result.setData(convertToSerchVoList(records));
        result.setTotal(page.getTotal());
        return result;
    }

    @Override
    public List<PlayletHomeVo> convertPlayletHomeVo(List<SohuPlayletVo> playletVos) {
        if (CollUtil.isEmpty(playletVos)) {
            return CollUtil.newArrayList();
        }
        List<Long> playletIds = new ArrayList<>();
        List<Long> userIds = new ArrayList<>();
        for (SohuPlayletVo playletVo : playletVos) {
            playletIds.add(playletVo.getId());
            userIds.add(playletVo.getUserId());
        }
        List<PlayletHomeVo> list = new LinkedList<>();
        Long loginId = LoginHelper.getUserId();
        //Map<Long, SohuUserLikeVo> likeVoMap = sohuUserLikeService.queryMap(loginId, BusyType.Playlet.getType(), playletIds);
        Map<Long, SohuUserCollectVo> collectVoMap = sohuUserCollectService.queryMap(loginId, BusyType.Playlet.getType(), playletIds);
        Map<String, Integer> statMaps = sohuUserCollectService.countStatMaps(BusyType.Playlet.getType(), playletIds);
        Map<Long, SohuUserSubscribeVo> subscribeVoMap = sohuUserSubscribeService.queryMap(loginId, BusyType.Playlet.getType(), playletIds);
        List<String> episodeRelevances = playletVos.stream().map(SohuPlayletVo::getEpisodeRelevance).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, Integer> forwardCountMap = getForwardCountMap(episodeRelevances);
        for (SohuPlayletVo playletVo : playletVos) {
            PlayletHomeVo homeVo = new PlayletHomeVo();
            homeVo.setPlayletId(playletVo.getId());
            homeVo.setTitle(playletVo.getTitle());
            homeVo.setCoverImage(playletVo.getCoverImage());
            homeVo.setIntro(playletVo.getIntro());
            homeVo.setViewCount(playletVo.getViewCount());
            homeVo.setCollect(Objects.nonNull(collectVoMap.get(playletVo.getId())));
            homeVo.setCollectCount(statMaps.getOrDefault(playletVo.getId().toString(), 0));
            homeVo.setEpisodeRelevance(playletVo.getEpisodeRelevance());
            homeVo.setSubscribe(Objects.nonNull(subscribeVoMap.get(playletVo.getId())));
            Long forwardCount = Long.valueOf(forwardCountMap.getOrDefault(playletVo.getEpisodeRelevance(), 0));
            homeVo.setForwardCount(forwardCount);
            homeVo.setPublishTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, playletVo.getCreateTime()));
            homeVo.setViewCount(playletVo.getViewCount());
            list.add(homeVo);
        }
        return list;
    }


    @Override
    public List<AppPlayletHomeVo> appPlayletList(BaseCommonBo commonBo) {
        String uuid = commonBo.getUuid();
        String preLabel = "app.playlet.api.home.";
        List<AppPlayletHomeVo> result = new LinkedList<>();
        PageQuery pageQuery = new PageQuery();
        for (AppPlayletHomeSortEnum.App app : AppPlayletHomeSortEnum.appList) {
            AppPlayletHomeVo vo = new AppPlayletHomeVo();
            if (StrUtil.isNotBlank(app.getLabelName())) {
                vo.setLabelName(MessageUtils.message(preLabel + app.getIdent(), commonBo.getLang()));
            }
            vo.setIdent(app.getIdent());
            vo.setIcon(app.getIcon());
            vo.setDirection(app.getDirection());
            List<PlayletHomeVo> playletList = new LinkedList<>();
            switch (app) {
                case MyList:
                    // 我的订阅，即我购买的短剧
                    pageQuery.setPageSize(6);
                    pageQuery.setPageNum(1);
                    TableDataInfo<PlayletPayedVo> myListData = playletPayedList(uuid, pageQuery);
                    if (CollUtil.isNotEmpty(myListData.getData())) {
                        playletList = new LinkedList<>();
                        for (PlayletPayedVo payedVo : myListData.getData()) {
                            PlayletHomeVo myListVo = new PlayletHomeVo();
                            myListVo.setPlayletId(payedVo.getPlayletId());
                            myListVo.setTitle(payedVo.getTitle());
                            myListVo.setIntro(payedVo.getIntro());
                            myListVo.setCoverImage(payedVo.getCoverImage());
                            myListVo.setEpisodeRelevance(payedVo.getEpisodeRelevance());
                            myListVo.setVideoId(payedVo.getVideoId());
                            myListVo.setVideoUrl(payedVo.getVideoUrl());
                            playletList.add(myListVo);
                        }
                    } else {
                        continue;
                    }
                    break;
                case GameNovel:
                    SohuGameNovelAppPageBo novelAppPageBo = new SohuGameNovelAppPageBo();
                    novelAppPageBo.setPageNum(1);
                    novelAppPageBo.setPageSize(6);
                    novelAppPageBo.setCommonBo(commonBo);
                    novelAppPageBo.setCategoryIdent("mobileGame");
                    TableDataInfo<SohuGameNovelAppListVo> novelAppListVoTableDataInfo = sohuGameNovelService.queryAppPageList(novelAppPageBo);
                    if (CollUtil.isNotEmpty(novelAppListVoTableDataInfo.getData())) {
                        List<SohuGameNovelAppListVo> list = novelAppListVoTableDataInfo.getData();
                        for (SohuGameNovelAppListVo novelAppListVo : list) {
                            PlayletHomeVo homeVo = new PlayletHomeVo();
                            homeVo.setCoverImage(novelAppListVo.getCoverBanner());
                            homeVo.setTitle(novelAppListVo.getTitle());
                            homeVo.setObjType("game");
                            homeVo.setJumpLink(novelAppListVo.getJumpLink());
                            playletList.add(homeVo);
                        }
                    }
                    // 游戏网文
                    break;
                case HotList:
                    // 热门短剧
                    playletList = convertPlayletHomeVo(hotPlaylet(0L, 6));
                    break;
                case ComingSoon:
                    // 即将上线
                    playletList = convertPlayletHomeVo(listDesc(6));
                    break;
                case ContinueWatchIng:
                    // 继续观看
                    pageQuery.setPageSize(6);
                    pageQuery.setPageNum(1);
                    TableDataInfo<PlayletUserViewVo> viewHistory = sohuPlayletUserService.getViewHistory(uuid, pageQuery);
                    List<PlayletUserViewVo> historyList = viewHistory.getData();
                    if (CollUtil.isNotEmpty(historyList)) {
                        playletList = new LinkedList<>();
                        for (PlayletUserViewVo userViewVo : historyList) {
                            PlayletHomeVo homeVo = new PlayletHomeVo();
                            homeVo.setPlayletId(userViewVo.getPlayletId());
                            homeVo.setTitle(userViewVo.getTitle());
                            homeVo.setCoverImage(userViewVo.getCoverImage());
                            homeVo.setEpisodeRelevance(userViewVo.getEpisodeRelevance());
                            playletList.add(homeVo);
                        }
                    } else {
                        continue;
                    }
                    break;
//                case PublicityMaterials:
//                    // 宣传资料,花絮
//                    pageQuery.setPageSize(2);
//                    pageQuery.setPageNum(1);
//                    TableDataInfo<SohuContentMainVo> materialsList = sohuContentMainService.queryMaterialsList(pageQuery);
//                    playletList = publicityMaterials(materialsList.getData());
//                    break;
                case Free:
                    // 免费
                    List<SohuPlayletVo> freePlaylets = freePlaylet(6);
                    if (CollUtil.isNotEmpty(freePlaylets)) {
                        playletList = new LinkedList<>();
                        for (SohuPlayletVo playletVo : freePlaylets) {
                            PlayletHomeVo homeVo = new PlayletHomeVo();
                            homeVo.setPlayletId(playletVo.getId());
                            homeVo.setTitle(playletVo.getTitle());
                            homeVo.setCoverImage(playletVo.getCoverImage());
                            homeVo.setEpisodeRelevance(playletVo.getEpisodeRelevance());
                            playletList.add(homeVo);
                        }
                    }
                    break;
                case More:
                    // 更多短剧
                    List<SohuPlayletVo> sohuPlayletVos = ListPlaylet(null, 6);
                    if (CollUtil.isNotEmpty(sohuPlayletVos)) {
                        playletList = new LinkedList<>();
                        for (SohuPlayletVo playletVo : sohuPlayletVos) {
                            PlayletHomeVo homeVo = new PlayletHomeVo();
                            homeVo.setPlayletId(playletVo.getId());
                            homeVo.setTitle(playletVo.getTitle());
                            homeVo.setCoverImage(playletVo.getCoverImage());
                            homeVo.setEpisodeRelevance(playletVo.getEpisodeRelevance());
                            playletList.add(homeVo);
                        }
                    }
                    break;
                case PlayletCategory:
                    // 短剧分类
                    break;
            }
            vo.setPlayletList(playletList);
            result.add(vo);
        }
        return result;
    }

    @Override
    public List<SohuPlayletVo> freePlaylet(int limit) {
        LambdaQueryWrapper<SohuPlaylet> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        lqw.eq(SohuPlaylet::getIsRecommend, 0);
        lqw.eq(SohuPlaylet::getIsPay, false);
        lqw.orderByDesc(SohuPlaylet::getViewCount);
        lqw.last("limit " + limit);
        return this.baseMapper.selectVoList(lqw);
    }

    public TableDataInfo<SohuPlayletVo> freePlaylet(PageQuery pageQuery) {
        LambdaQueryWrapper<SohuPlaylet> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlaylet::getState, CommonState.OnShelf.getCode());
        lqw.eq(SohuPlaylet::getIsRecommend, 0);
        lqw.eq(SohuPlaylet::getIsPay, false);
        lqw.orderByDesc(SohuPlaylet::getViewCount);
        IPage<SohuPlayletVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    @NotNull
    private Map<String, Integer> getForwardCountMap(List<String> episodeRelevances) {
        List<Map<String, Object>> forwardCountList = sohuVideoInfoMapper.getForwardCountByEpisodeRelevanceBatch(episodeRelevances);
        Map<String, Integer> forwardCountMap = forwardCountList.stream().collect(Collectors.toMap(map -> (String) map.get("episodeRelevance"), map -> ((Number) map.get("forwardCount")).intValue()));
        return forwardCountMap;
    }

    /**
     * 对象转换
     *
     * @param playletInfo 源对象
     * @return PlayletDetailInfoVo 目标对象
     */
    private PlayletDetailInfoVo converSohuPlayletVoToPlayletDetailInfoVo(SohuPlayletVo playletInfo) {
        // 获取短剧作者信息
        LoginUser playletUserInfo = sohuUserService.queryById(playletInfo.getUserId());
        // V1.0版本是对整部剧进行点赞、收藏操作
        Map<Long, SohuUserFollowVo> userFollowMap = sohuUserFollowService.mapUserFollows(LoginHelper.getUserId(), Collections.singletonList(playletInfo.getUserId()));
        Map<Long, SohuUserLikeVo> likeVoMap = sohuUserLikeService.queryMap(LoginHelper.getUserId(), BusyType.Playlet.getType(), Collections.singletonList(playletInfo.getId()));
        Map<Long, SohuUserCollectVo> collectVoMap = sohuUserCollectService.queryMap(LoginHelper.getUserId(), BusyType.Playlet.getType(), Collections.singletonList(playletInfo.getId()));

        Map<String, Integer> statMaps = sohuUserCollectService.countStatMaps(BusyType.Playlet.getType(), Arrays.asList(playletInfo.getId()));
        PlayletDetailInfoVo playletDetailInfoVo = new PlayletDetailInfoVo();
        playletDetailInfoVo.setPlayletUserId(playletInfo.getUserId());
        playletDetailInfoVo.setPlayletNickname(playletUserInfo.getNickname());
        playletDetailInfoVo.setPlayletUserAvatar(playletUserInfo.getAvatar());
        playletDetailInfoVo.setPlayletUserSignature(playletUserInfo.getRemark());
        playletDetailInfoVo.setPlayletTitle(playletInfo.getTitle());
        playletDetailInfoVo.setPlayletIntro(playletInfo.getIntro());
        playletDetailInfoVo.setVideoStyle(playletInfo.getVideoStyle());
        playletDetailInfoVo.setPlayletSummary(playletInfo.getSummary());
        playletDetailInfoVo.setSinglePrice(CalUtils.scale(CalUtils.multiply(playletInfo.getSinglePrice(), new BigDecimal(10))));
        playletDetailInfoVo.setCollectCount(sohuUserCollectService.countStatById(BusyType.Playlet.getType(), playletInfo.getId()));
        playletDetailInfoVo.setPraiseCount(sohuUserLikeService.countStatById(BusyType.Playlet.getType(), playletInfo.getId()));
        playletDetailInfoVo.setViewCount(Long.valueOf(playletInfo.getViewCount()));
        playletDetailInfoVo.setForwardCount(Objects.nonNull(sohuVideoInfoMapper.getForwardCountLong(playletInfo.getEpisodeRelevance())) ? sohuVideoInfoMapper.getForwardCountLong(playletInfo.getEpisodeRelevance()) : 0L);
//        playletDetailInfoVo.setForwardCount(Objects.nonNull(sohuVideoInfoMapper.selectVoById(playletInfo.getId())) ? sohuVideoInfoMapper.selectVoById(playletInfo.getId()).getForwardCount() : 0L);
        // 我关注状态，true = 关注
        playletDetailInfoVo.setFollowObj(Objects.nonNull(userFollowMap.get(playletInfo.getUserId())));
        // 我收藏状态，true = 收藏
        playletDetailInfoVo.setCollectObj(Objects.nonNull(collectVoMap.get(playletInfo.getId())));
        // 我点赞状态，true = 点赞
        playletDetailInfoVo.setPraiseObj(Objects.nonNull(likeVoMap.get(playletInfo.getId())));
        playletDetailInfoVo.setPlayetId(playletInfo.getId());
        playletDetailInfoVo.setCoverImage(playletInfo.getCoverImage());
        return playletDetailInfoVo;
    }

    private List<PlayletSearchVo> convertToSerchVoList(List<SohuPlayletVo> records) {
        Set<Long> playletAuthorIds = records.stream().map(SohuPlayletVo::getUserId).collect(Collectors.toSet());
        Map<Long, LoginUser> userMap = sohuUserService.selectMap(playletAuthorIds);
        List<PlayletSearchVo> list = new ArrayList<>();
        for (SohuPlayletVo playletVo : records) {
            PlayletSearchVo vo = new PlayletSearchVo();
            vo.setPlayletId(playletVo.getId());
            vo.setTitle(playletVo.getTitle());
            vo.setCoverImage(playletVo.getCoverImage());
            vo.setIntro(playletVo.getIntro());
            vo.setEpisodeRelevance(playletVo.getEpisodeRelevance());
            vo.setViewCount(playletVo.getViewCount());
            LoginUser user = userMap.get(playletVo.getUserId());
            vo.setUser(sohuUserService.convert(user));
            list.add(vo);
        }
        return list;
    }

    private List<PlayletPayedVo> convertToPayedVoList(List<SohuPlayletVo> records) {
        Set<Long> playletAuthorIds = records.stream().map(SohuPlayletVo::getUserId).collect(Collectors.toSet());
        Map<Long, LoginUser> userMap = sohuUserService.selectMap(playletAuthorIds);
        List<PlayletPayedVo> list = new ArrayList<>();
        for (SohuPlayletVo playletVo : records) {
            PlayletPayedVo vo = new PlayletPayedVo();
            vo.setPlayletId(playletVo.getId());
            vo.setTitle(playletVo.getTitle());
            vo.setCoverImage(playletVo.getCoverImage());
            vo.setIntro(playletVo.getIntro());
            vo.setEpisodeRelevance(playletVo.getEpisodeRelevance());
            list.add(vo);
        }
        return list;
    }

    /**
     * 宣传物料转换
     */
    private List<PlayletHomeVo> publicityMaterials(List<SohuContentMainVo> materialsList) {
        List<PlayletHomeVo> playletList = new LinkedList<>();
        if (CollUtil.isNotEmpty(materialsList)) {
            for (SohuContentMainVo mainVo : materialsList) {
                PlayletHomeVo myListVo = new PlayletHomeVo();
                myListVo.setPlayletId(mainVo.getObjId());
                myListVo.setTitle(mainVo.getObjTitle());
                myListVo.setCoverImage(mainVo.getCoverImage());
                myListVo.setVideoUrl(mainVo.getVideoUrl());
                myListVo.setObjType(mainVo.getObjType());
                myListVo.setPraise(mainVo.getPraiseObj());
                myListVo.setPraiseCount(mainVo.getPraiseCount());
                myListVo.setNickName(mainVo.getNickName());
                myListVo.setUserAvatar(mainVo.getUserAvatar());
                myListVo.setCreateTimeStamp(mainVo.getCreateTimeStamp());
                playletList.add(myListVo);
            }
        }
        return playletList;
    }

    /**
     * 清除短剧缓存
     */
    private void evictPlayletId(Long playletId) {
        if (playletId == null || playletId <= 0L) {
            return;
        }
    }

    /**
     * 对象转换--短剧专用
     *
     * @param sohuPlayletVoList 源对象
     * @param fansUserId        粉丝Id
     * @return List<PlayletFollowListVo> 目标对象
     */
    private List<PlayletFollowListVo> convertSohuPlayletVoListToPlayletFollowListVo(List<SohuPlayletVo> sohuPlayletVoList, Long fansUserId) {
        return sohuPlayletVoList.stream().map(sohuPlayletVo -> {
            PlayletFollowListVo playletFollowListVo = new PlayletFollowListVo();
            playletFollowListVo.setPlayetId(sohuPlayletVo.getId());
            playletFollowListVo.setPlayletTitle(sohuPlayletVo.getTitle());
            playletFollowListVo.setPlayletIntro(sohuPlayletVo.getIntro());
            playletFollowListVo.setCoverImage(sohuPlayletVo.getCoverImage());
            playletFollowListVo.setPraiseCount(sohuUserLikeService.countStatById(BusyType.Playlet.getType(), sohuPlayletVo.getId()));
            playletFollowListVo.setEpisodeRelevance(sohuPlayletVo.getEpisodeRelevance());
            playletFollowListVo.setPraiseObj(sohuUserLikeService.queryByUserId(fansUserId, sohuPlayletVo.getId()));
            return playletFollowListVo;
        }).collect(Collectors.toList());
    }

    public Map<String, SohuPlayletVo> getBatchPlayletInfo(List<String> episodeRelevanceList, Long userId) {
        if (episodeRelevanceList == null || episodeRelevanceList.isEmpty()) {
            return Collections.emptyMap();
        }

        // 批量查询所有剧集的信息
        List<SohuPlayletVo> playletList = baseMapper.selectBatchPlayletInfo(episodeRelevanceList);

        Map<String, SohuPlayletVo> playletInfoMap = new HashMap<>();

        for (SohuPlayletVo playletVo : playletList) {
            if (playletVo.getSinglePrice() == null) {
                playletInfoMap.put(playletVo.getEpisodeRelevance(), playletVo);
                continue;
            }

            Long count;
            BigDecimal amount;

            if (userId == null || userId <= 0L) {
                // 无用户ID时查询需要付费的视频数量
                count = this.needPayVideoCount(playletVo.getEpisodeRelevance());
                amount = CalUtils.multiply(playletVo.getSinglePrice().abs(), new BigDecimal(count));
            } else {
                // 有用户ID时计算用户应付集数和金额
                count = this.userNeedPayVideoCount(playletVo.getEpisodeRelevance(), userId);
                amount = CalUtils.multiply(playletVo.getSinglePrice().abs(), new BigDecimal(count));

                // 如果有折扣并且折扣大于零，计算折扣后的金额
                if (playletVo.getIsPayLeave() != null && playletVo.getIsPayLeave()
                        && playletVo.getDiscount() != null && CalUtils.isGreatZero(playletVo.getDiscount())) {
                    amount = CalUtils.multiply(playletVo.getSinglePrice().abs(), new BigDecimal(count),
                            playletVo.getDiscount(), new BigDecimal(0.01));
                }

                // 查询用户是否已整部剧付费
                SohuTradeRecordVo tradeRecord = sohuTradeRecordService.queryOne(userId,
                        SohuTradeRecordEnum.Type.Playlet.getCode(), SohuTradeRecordEnum.Type.Playlet.getCode(),
                        String.valueOf(playletVo.getId()), PayStatus.Paid.name());

                playletVo.setAllPay(Objects.nonNull(tradeRecord));
            }

            // 设置计算好的金额
            playletVo.setPlayletPrice(amount);
            playletInfoMap.put(playletVo.getEpisodeRelevance(), playletVo);
        }

        return playletInfoMap;
    }

    private void buildPlayletAuthor(List<PlayletHomeVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> playIds = list.stream().map(PlayletHomeVo::getPlayletId).collect(Collectors.toList());
        List<SohuPlaylet> sohuPlaylets = baseMapper.selectBatchIds(playIds);
        Map<Long, SohuPlaylet> playletMap = sohuPlaylets.stream().collect(Collectors.toMap(SohuPlaylet::getId, u -> u));
        // 短剧作者id 集合
        Set<Long> authorIds = new HashSet<>();
        for (PlayletHomeVo vo : list) {
            vo.setUserId(playletMap.get(vo.getPlayletId()).getUserId());
            authorIds.add(vo.getUserId());
        }
        Map<Long, LoginUser> userMap = sohuUserService.selectMap(authorIds);
        for (PlayletHomeVo vo : list) {
            LoginUser user = userMap.get(vo.getUserId());
            if (Objects.isNull(user)) {
                continue;
            }
            vo.setUserId(user.getUserId());
            vo.setNickName(StrUtil.isBlankIfStr(user.getNickname()) ? user.getUsername() : user.getNickname());
            vo.setUserAvatar(StrUtil.isBlankIfStr(user.getAvatar()) ? Constants.DEFAULT_USER_AVATAR : user.getAvatar());
            if (Objects.nonNull(vo.getViewTime())) {
                vo.setViewTimeStamp(vo.getViewTime().getTime());
            }
        }
    }


}
