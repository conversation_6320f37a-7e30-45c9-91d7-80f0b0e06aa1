package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.busyorder.api.RemoteBusyTaskSiteService;
import com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo;
import com.sohu.admin.api.RemoteBanRecordService;
import com.sohu.admin.api.bo.SohuBanRecordsBo;
import com.sohu.admin.api.vo.SohuBanRecordsVo;
import com.sohu.middle.api.bo.SohuTopicContentQueryBo;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.UserConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.noticeContent.NoticeContentDetail;
import com.sohu.im.api.noticeContent.NoticeSystemContent;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.airec.SohuAirecContentItemBo;
import com.sohu.middle.api.bo.notice.SohuInteractNoticeBo;
import com.sohu.middle.api.bo.risk.SohuRiskMqBo;
import com.sohu.middle.api.enums.AiRecTag;
import com.sohu.middle.api.enums.DislikeEnum;
import com.sohu.middle.api.service.RemoteMiddleIndependentMaterialService;
import com.sohu.middle.api.service.notice.RemoteMiddleSystemNoticeService;
import com.sohu.middle.api.vo.*;
import com.sohu.middle.api.vo.airec.SohuAirecContentItemVo;
import com.sohu.middleService.domain.*;
import com.sohu.middleService.domain.bo.SohuAirecArticleQueryBo;
import com.sohu.middleService.domain.bo.SohuAirecQuestionQueryBo;
import com.sohu.middleService.mapper.*;
import com.sohu.middleService.service.*;
import com.sohu.middleService.service.airec.ISohuAirecContentItemService;
import com.sohu.middleService.service.airec.ISohuAirecTagRelationService;
import com.sohu.middleService.utils.FormalTool;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.third.aliyun.airec.constants.AliyunAirecConstant;
import com.sohu.third.aliyun.airec.constants.AliyunAirecTagsConstant;
import com.sohu.third.aliyun.airec.domain.AliyunAirecJoinFilterRule;
import com.sohu.third.aliyun.airec.domain.AliyunAirecSingleFilterRule;
import com.sohu.third.aliyun.airec.enums.*;
import com.sohu.third.aliyun.airec.util.AliyunAirecUtil;
import com.sohu.third.aliyun.audit.constants.AliyunAuditLabelEnum;
import com.sohu.third.aliyun.audit.service.AliyunAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.text.CharPool.COMMA;

/**
 * 问题主体Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuQuestionServiceImpl extends SohuBaseServiceImpl<SohuQuestionMapper, SohuQuestion, SohuQuestionVo> implements ISohuQuestionService {

    private final SohuQuestionAnswerMapper sohuQuestionAnswerMapper;
    private final SohuQoraInfoMapper sohuQoraInfoMapper;
    private final SohuQoraRelateMapper sohuQoraRelateMapper;
    private final SohuVideoMapper sohuVideoMapper;
    private final SohuContentMainMapper sohuContentMainMapper;

    private final ISohuContentMainService sohuContentMainService;
    private final ISohuQoraInfoService sohuQoraInfoService;
    private final ISohuQoraRelateService sohuQoraRelateService;
    private final ISohuAuditService sohuAuditService;
    private final ISohuAuditRecordService iSohuAuditRecordService;
    private final ISohuCategoryService sohuCategoryService;
    private final ISohuSyncContentService sohuSyncContentService;
    private final ISohuLessonLabelService sohuLessonLabelService;
    private final ISohuDislikeService iSohuDislikeService;
    private final ISohuAirecTagRelationService iSohuAirecTagRelationService;
    private final ISohuAirecContentItemService iSohuAirecContentItemService;
    private final ISohuSiteService siteService;
    private final ISohuUserService sohuUserService;
    private final ISohuInteractNoticeService sohuInteractNoticeService;
    private final ISohuAuditInitService sohuAuditInitService;
    private final ISohuContentLifecycleService sohuContentLifecycleService;
    private final ISohuPlatformIndustryRelationService relationService;
    private final ISohuBusyBlackService busyBlackService;

    private final AsyncConfig asyncConfig;

    @DubboReference
    private RemoteMiddleSystemNoticeService remoteMiddleSystemNoticeService;
    @Resource
    private AliyunAuditService aliyunAuditService;
    @DubboReference
    private RemoteMiddleIndependentMaterialService remoteMiddleIndependentMaterialService;
    @DubboReference
    private RemoteBusyTaskSiteService remoteBusyTaskSiteService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    @DubboReference
    private RemoteBanRecordService remoteBanRecordService;

    /**
     * 最大可提交次数，TODO 后期改为可配置参数
     */
    private static final Integer SUBMIT_NUM_MAX_VALUE = 3;
    /**
     * 回收站超时时间，单位天
     */
    private final static Integer CON_RECYCLE_DATA_TIME_OUT = 14;

    @Override
    public Long getAuthorId(Long id) {
        if (CalUtils.isNullOrZero(id)) {
            return 0L;
        }
        SohuQuestionVo questionVo = this.get(id);
        return Objects.isNull(questionVo) ? 0L : questionVo.getUserId();
    }

    @Override
    public SohuQuestionVo get(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public SohuQuestionVo selectVoById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询问题主体
     */
    @Override
    public SohuQuestionVo queryById(Long id, Boolean type) {
        SohuQuestionVo questionVo = baseMapper.selectVoById(id);
        if (Objects.isNull(questionVo)) {
            return null;
        }
        questionVo.setRelation(this.buildRelate(id));
        // 学习人数+1 只有审核通过的才+1
        if (StrUtil.equalsAnyIgnoreCase(questionVo.getState(), CommonState.OnShelf.getCode())) {
            sohuQoraInfoMapper.updateLearnCount(id);
            sohuQoraInfoMapper.updateViewCount(id, BusyType.Question.name());
        }
        SohuCategoryVo categoryVo = sohuCategoryService.queryById(questionVo.getCategoryId());
        if (Objects.nonNull(categoryVo)) {
            questionVo.setCategoryName(categoryVo.getName());
        }
        Long userId = LoginHelper.getUserId();
        if (userId != null && userId > 0L && type) {
            // 记录用户数据浏览量
            String questionKey = CacheConstants.QUESTION + userId;
            Set<Long> cacheListIds = RedisUtils.getCacheObject(questionKey);
            if (CollUtil.isEmpty(cacheListIds)) {
                cacheListIds = new LinkedHashSet<>();
            }
            cacheListIds.add(id);
            RedisUtils.setCacheObject(questionKey, cacheListIds);
        }
        // 问题浏览量缓存键
        String questionKey = CacheConstants.QUESTION_VIEW + questionVo.getId();
        if (userId != null && userId > 0L) {
            // 问题数据浏览量
            RedisUtils.incrAtomicValue(questionKey, 1);
        }
        // 总回答数
        questionVo.setAnswerCount(sohuQuestionAnswerMapper.answerCount(questionVo.getId(), userId));
        // 总点赞数
        Long price = sohuQuestionAnswerMapper.countPrice(questionVo.getId(), userId);
        //初始学习人数
        questionVo.setLearnNum(sohuQoraInfoMapper.countLearnCount(questionVo.getId()));
        // 总浏览量
        questionVo.setViewCount(RedisUtils.getAtomicValue(questionKey));
        questionVo.setTotalPrise(price != null ? price : 0);
        LoginUser user = sohuUserService.selectById(questionVo.getUserId());
        if (Objects.nonNull(user)) {
            questionVo.setAuthorName(user.getNickname());
            questionVo.setAuthorAvatar(user.getAvatar());
        } else {
            questionVo.setAuthorName(Constants.DEFAULT_USER_NICKNAME);
            questionVo.setAuthorAvatar(Constants.DEFAULT_AVATAR);
        }
        return questionVo;
    }

    /**
     * 查询问题主体列表
     */
    @Override
    public TableDataInfo<SohuQuestionVo> queryPageList(SohuQuestionBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        // 超级管理员
        boolean isAdmin = LoginHelper.hasRole(loginUser, RoleCodeEnum.ADMIN);
        // 国家站长
        boolean isCountry = LoginHelper.hasRole(loginUser, RoleCodeEnum.CountryStationAgent);
        // 城市站长
        boolean isCity = LoginHelper.hasRole(loginUser, RoleCodeEnum.CityStationAgent);
        if (isAdmin || isCountry || isCity) {

        } else {
            bo.setUserId(LoginHelper.getUserId());
        }
        LambdaQueryWrapper<SohuQuestion> lqw = buildQueryWrapper(bo);
        lqw.eq(SohuQuestion::getDelFlag, false);
        lqw.orderByAsc(SohuQuestion::getSortIndex);
        lqw.orderByDesc(SohuQuestion::getCreateTime);
        Page<SohuQuestionVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuQuestionVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            Set<Long> categoryIds = new HashSet<>();
            Set<Long> userIds = new HashSet<>();
            List<Long> questionIds = new ArrayList<>();
            Set<Long> siteIds = new HashSet<>();
            records.stream().forEach(vo -> {
                categoryIds.add(vo.getCategoryId());
                userIds.add(vo.getUserId());
                questionIds.add(vo.getId());
                vo.setRelation(this.buildRelate(vo.getId()));
                siteIds.add(vo.getSiteId());
            });
            SohuQoraInfoBo infoBo = new SohuQoraInfoBo();
            infoBo.setQoraType(BusyType.Question.name());
            infoBo.setQoraIds(questionIds);
            List<SohuQoraInfoVo> qoraInfoVos = sohuQoraInfoService.queryList(infoBo);
            Map<Long, SohuCategoryVo> categoryVoMap = sohuCategoryService.queryMap(categoryIds);
            Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
            Map<Long, SohuQoraInfoVo> infoVoMap = qoraInfoVos.stream().collect(Collectors.toMap(SohuQoraInfoVo::getQoraId, u -> u));
            Map<Long, SohuSiteVo> siteModelMap = siteService.queryMap(siteIds);
            records.stream().forEach(vo -> {
                SohuCategoryVo categoryVo = categoryVoMap.get(vo.getCategoryId());
                if (Objects.nonNull(categoryVo)) {
                    vo.setCategoryName(categoryVo.getName());
                }
                // 设置审核状态以及站点名称
                setAuditStatus(vo, siteModelMap);
                vo.setContentState(vo.getState());
                vo.setBusyType(BusyType.Question.getType());
                LoginUser user = userMap.get(vo.getUserId());
                if (Objects.nonNull(user)) {
                    vo.setAuthorName(user.getNickname());
                    vo.setAuthorAvatar(user.getAvatar());
                } else {
                    vo.setAuthorName(Constants.DEFAULT_USER_NICKNAME);
                    vo.setAuthorAvatar(Constants.DEFAULT_AVATAR);
                }
                vo.setInfo(infoVoMap.get(vo.getId()));
                // 设置移出时间
                if (bo.getIsBlack()) {
                    SohuBusyBlackVo sohuBusyBlackVo = null;
                    if (Objects.nonNull(bo.getIndustryId())) {
                        sohuBusyBlackVo = busyBlackService.queryByParam(vo.getId(), 2, BusyType.Question.getType());
                    } else {
                        sohuBusyBlackVo = busyBlackService.queryByParam(vo.getId(), 1, BusyType.Question.getType());
                    }
                    vo.setRemoveTime(sohuBusyBlackVo.getCreateTime());
                }
            });
            result.setRecords(records);
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public SohuConentListStatVo queryPageListStat(SohuQuestionBo bo) {
        // 判断站点和行业，分别查询黑名单
        List<Long> handleIds = new ArrayList<>();
        if (Objects.nonNull(bo.getIndustryId())) {
            // 行业id不为空,则查询相关行业对应的分类
            SohuPlatformIndustryRelationBo relationBo = new SohuPlatformIndustryRelationBo();
            relationBo.setPlatformIndustryId(bo.getIndustryId());
            relationBo.setBusyType(BusyType.Content.getType());
            List<SohuPlatformIndustryRelationVo> relationList = relationService.queryList(relationBo);
            List<Long> categoryIds = relationList.stream().map(SohuPlatformIndustryRelationVo::getBusyCategoryId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(bo.getCategoryIds())) {
                bo.setCategoryIds(categoryIds);
            } else {
                // 取交集
                categoryIds.retainAll(bo.getCategoryIds());
                bo.setCategoryIds(categoryIds);
            }
            // 查询行业黑名单
            handleIds = busyBlackService.listBusyIds(bo.getIndustryId(), 2, BusyType.Question.getType());
        } else if (Objects.nonNull(bo.getSiteId()) && LoginHelper.hasRole(LoginHelper.getLoginUser(), RoleCodeEnum.CityStationAgent)) {
            // 站点id不为空,则查询行业黑名单
            handleIds = busyBlackService.listBusyIds(bo.getSiteId(), 1, BusyType.Question.getType());
        }
        if (bo.getIsBlack() && CollectionUtils.isEmpty(handleIds)) {
            handleIds.add(0L);
        }
        bo.setHandleIds(handleIds);
        bo.setUserId(LoginHelper.getUserId());
        LambdaQueryWrapper<SohuQuestion> lqw = buildQueryWrapper(bo);
        if (StrUtil.isNotBlank(bo.getState()) && (!Constants.ALL.equals(bo.getState()))) {
            lqw.eq(SohuQuestion::getState, bo.getState());
        }
        lqw.eq(SohuQuestion::getDelFlag, 0);
        SohuConentListStatVo vo = this.baseMapper.queryPageListStat(lqw);
        if (Objects.isNull(vo)) {
            vo = new SohuConentListStatVo();
        }
        Long allNum = vo.getOnShelfNum() + vo.getOffShelfNum() + vo.getWaitApproveNum() + vo.getRefuseNum();
        vo.setAllNum(allNum);
        return vo;
    }

    /**
     * 设置审核状态
     */
    private static void setAuditStatus(SohuQuestionVo record, Map<Long, SohuSiteVo> siteModelMap) {
        SohuSiteVo sohuSiteModel = siteModelMap.get(record.getSiteId());
        if (Objects.isNull(sohuSiteModel)) {
            return;
        }
        record.setSiteName(sohuSiteModel.getName());
        Set<String> allowedStates = new HashSet<>(Arrays.asList(CommonState.WaitApprove.getCode(), CommonState.OnShelf.getCode(), CommonState.Refuse.getCode()));
        // 获取当前状态
        String currentState = record.getState();
        // 判断当前状态是否在允许的状态列表中
        if (allowedStates.contains(currentState)) {
            record.setContentState(currentState);
        }
    }

    @Override
    public TableDataInfo<SohuQuestionVo> questionPageList(SohuQuestionBo bo, PageQuery pageQuery) {
//        bo.setState(CommonState.OnShelf.name());
        Long userId = LoginHelper.getUserId();
        //当前用户idset进去为了排序用
        //bo.setCurrentUserId(userId);
//        //是否需要推荐
//        if (bo.getAiRecommend()) {
//            TableDataInfo<SohuQuestionVo> sohuAirecContentQuestion = getSohuAirecContentQuestion(bo);
//            if (CollUtil.isNotEmpty(sohuAirecContentQuestion.getData())) {
//                if (bo.getCurrentUserId() != null && StringUtils.isEmpty(bo.getImei())) {
//                    getRecord(sohuAirecContentQuestion.getData(), userId);
//                }
//                //如果推荐内容不为空返回推荐
//                return sohuAirecContentQuestion;
//            }
//        }
        if (StrUtil.isBlank(bo.getTitle()) && bo.getUserId() == null && userId != null && !VideoEnum.Type.lesson.getCode().equals(bo.getType())) {
            // 不感兴趣 过滤游客，个人中心,以及搜索
            List<SohuDislikeVo> sohuDislikeVos = iSohuDislikeService.selectByUserId(userId);
            Set<Long> dislikeCategory = sohuDislikeVos.stream().filter(a -> DislikeEnum.DislikeCategory.name().equals(a.getDislikeType()) && UserConstants.DISLIKE_CATEGORY_COUNT <= a.getCount()).map(SohuDislikeVo::getDislikeId).collect(Collectors.toSet());
            bo.setNotCategoryIds(dislikeCategory);
            Set<Long> dislikeAuthor = sohuDislikeVos.stream().filter(a -> DislikeEnum.DislikeAuthor.name().equals(a.getDislikeType()) && UserConstants.DISLIKE_AUTHOR_COUNT <= a.getCount()).map(SohuDislikeVo::getDislikeId).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(bo.getNotUserIds())) {
                bo.setNotUserIds(Stream.concat(bo.getNotUserIds().stream(), dislikeAuthor.stream()).collect(Collectors.toSet()));
            } else {
                bo.setNotUserIds(dislikeAuthor);
            }
            String questionKey = CacheConstants.QUESTION + userId;
            Set<Long> cacheListIds = RedisUtils.getCacheObject(questionKey);
            bo.setNotIds(cacheListIds);
        }
//        LambdaQueryWrapper<SohuQuestion> lqw = buildQueryWrapper(bo);
//        Page<SohuQuestionVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        //2023-12-28 修改排序  hezhen
        Page<SohuQuestionVo> result = baseMapper.selectQuestionPages(PageQueryUtils.build(pageQuery), bo);
//        if (CollUtil.isNotEmpty(result.getRecords())) {
//            result.getRecords().forEach(item -> {
//                SohuQuestionAnswerVo questionAnswerVo = sohuQuestionAnswerMapper.answerTop(item.getId(), userId);
//                // 首条评论
//                item.setQuestionAnswerVo(questionAnswerVo);
//                // 总回答数
//                item.setAnswerCount(sohuQuestionAnswerMapper.answerCount(item.getId(), userId));
//                // 总点赞数
//                Long price = sohuQuestionAnswerMapper.countPrice(item.getId(), userId);
//                item.setTotalPrise(price != null ? price : 0);
//                item.setRelation(this.getRelationRespVo(item.getId()));
//                item.setLearnNum(sohuQoraInfoMapper.countLearnCount(item.getId()));
//                item.setContentText(getPlainText(item.getContent()));
//            });
//        }
        this.getRecord(result.getRecords(), userId);
        return TableDataInfoUtils.build(result);
    }

    @Override
    public TableDataInfo<SohuQuestionVo> questionPageListOfAirec(SohuQuestionBo bo, PageQuery pageQuery) {
        // 过滤封禁用户作品
        filterBanUserContent(bo);
        SohuAirecQuestionQueryBo airecQueryBo = new SohuAirecQuestionQueryBo();
        if (Objects.nonNull(bo.getSiteId())) {
            List<Long> handleIds = busyBlackService.listBusyIds(bo.getSiteId(), 1, BusyType.Article.getType());
            if (CollUtil.isNotEmpty(bo.getHandleIds())) {
                bo.setHandleIds(Stream.concat(bo.getHandleIds().stream(), handleIds.stream()).distinct().collect(Collectors.toList()));
                airecQueryBo.setBlackIds(Stream.concat(bo.getHandleIds().stream(), handleIds.stream()).distinct().collect(Collectors.toList()));
            } else {
                bo.setHandleIds(handleIds);
                airecQueryBo.setBlackIds(handleIds);
            }
        }
        buildSiteIdRule(bo, airecQueryBo);
        if (bo.getAiRec()) {
            if (StrUtil.isBlank(bo.getAiRecImei()) && Objects.isNull(LoginHelper.getUserId())) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (!(AliyunAirecConstant.SCENE_QUESTION_ALL.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_QUESTION_HOMEPAGE.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_QUESTION_MONEYMAKING.equals(bo.getAiRecSceneId()))) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (Objects.nonNull(LoginHelper.getUserId())) {
                bo.setAiUserId(LoginHelper.getUserId().toString());
            } else {
                bo.setVisibleType(VisibleTypeEnum.open.getCode());
            }
            bo.setAiReturnCount(pageQuery.getPageSize());
            //构造过滤的内容
            SohuAirecContentItemVo airecCategoryInfo = sohuCategoryService.getAirecCategoryInfoById(bo.getCategoryId());
            airecQueryBo.setCategoryPath(airecCategoryInfo.getCategoryPath());
            AliyunAirecJoinFilterRule rootRule = airecQueryBo.buildAirecFilterRule();
            //this.buildAirecJoinFilterRule(bo.getSiteId(), rootRule);
            //获取阿里云智能推荐结果
            List<SohuQuestionVo> resultList = AliyunAirecUtil.aiRecommendSingleType(rootRule, bo, itemIds -> baseMapper.selectVoBatchIds(itemIds));
            if (CollUtil.isNotEmpty(resultList)) {
                getRecord(resultList, LoginHelper.getUserId());
                return TableDataInfoUtils.build(resultList);
            }
        }
        bo.setState(CommonState.OnShelf.getCode());
        //首页问答不显示关联项--APP-1.0临时需求
//        if (bo.getIsRelate() == null && StringUtils.isBlank(bo.getTitle())) {
//            bo.setIsRelate(false);
//        }
        TableDataInfo<SohuQuestionVo> tableDataInfo = this.questionPageList(bo, pageQuery);
        AliyunAirecUtil.buildAiRecommendSingleType(tableDataInfo.getData(), AliyunAirecContentItemTypeEnum.RECIPE.getCode());
        return tableDataInfo;
    }

    /**
     * 过滤用户作品内容
     *
     * @param bo SohuArticleBo
     */
    private void filterBanUserContent(SohuQuestionBo bo) {
        SohuBanRecordsBo sohuBanRecordsBo = new SohuBanRecordsBo();
        sohuBanRecordsBo.setStatus("active");
        List<SohuBanRecordsVo> sohuBanRecordsVoList = remoteBanRecordService.queryList(sohuBanRecordsBo);
        // 聚合用户id集合
        Set<Long> banUserIds = sohuBanRecordsVoList.stream().map(SohuBanRecordsVo::getUserId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(banUserIds)) {
            bo.setNotUserIds(banUserIds);
        }
    }

    /**
     * 处理站点字段
     */
    private void buildSiteIdRule(SohuQuestionBo bo, SohuAirecQuestionQueryBo airecQueryBo) {
        if (Objects.isNull(bo.getSiteId())) {
            SohuSiteVo siteVo = siteService.getSiteByIp(ServletUtils.getClientIP());
            if (Objects.nonNull(siteVo)) {
                bo.setSiteId(siteVo.getId());
                if (Objects.equals(siteVo.getType(), SiteType.City.name())) {
                    airecQueryBo.setCity(String.valueOf(siteVo.getId()));
                } else if (Objects.equals(siteVo.getType(), SiteType.Country.name())) {
                    airecQueryBo.setCountry(siteVo.getCountryCode());
                }
            }
        }
        //首页的话 不查站点 只分国内国外
        if (Objects.nonNull(bo.getRecommend()) && bo.getRecommend()) {
            //推荐页就不分站点 只分国家
            if (Objects.nonNull(bo.getSiteId())) {
//                SohuSiteVo sohuSiteVo = siteService.selectSiteByPid(bo.getSiteId());
//                if (Objects.nonNull(sohuSiteVo)) {
//                    bo.setCountrySiteId(sohuSiteVo.getId());
//                    bo.setSiteId(null);
//                    airecQueryBo.setCountry(sohuSiteVo.getCountryCode());
//                }
                SohuSiteVo sohuSiteVo = siteService.getCountrySiteById(bo.getSiteId());
                if (Objects.nonNull(sohuSiteVo)) {
                    bo.setCountrySiteId(sohuSiteVo.getId());
                    bo.setSiteId(null);
                    airecQueryBo.setCountry(sohuSiteVo.getCountryCode());
                    airecQueryBo.setCity(null);
                }
            }
        }
    }

    /**
     * 构建智能推荐过滤参数
     *
     * @param siteId   站点Id
     * @param rootRule
     */
    @Deprecated
    private void buildAirecJoinFilterRule(Long siteId, AliyunAirecJoinFilterRule rootRule) {
        rootRule.setJoin(AliyunAirecQueryJoinEnum.AND.getCode());
        {
            //内容的类型
            AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
            rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
            rule.setField(AliyunAirecQueryFieldContentEnum.ITEM_TYPE.getCode());
            rule.setValue(AliyunAirecContentItemTypeEnum.RECIPE.getCode());
            rootRule.getFilters().add(rule);
        }
        {
            //站点
            if (!Objects.isNull(siteId)) {
                AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
                rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
                rule.setField(AliyunAirecQueryFieldContentEnum.CITY.getCode());
                rule.setValue(siteId.toString());
                rootRule.getFilters().add(rule);
            }
        }
        {
            //TODO 进行判断
            AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
            rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
            rule.setField(AliyunAirecQueryFieldContentEnum.COUNTRY.getCode());
            SohuSiteVo sohuSiteVo = siteService.selectSiteByPid(siteId);
            if (Objects.nonNull(sohuSiteVo)) {
                String code = siteService.selectCountryCodeById(sohuSiteVo.getId());
                if (StrUtil.isNotBlank(code)) {
                    //设置国家编码
                    rule.setValue(code);
                }
            }
            rootRule.getFilters().add(rule);
        }
    }

    @Override
    public TableDataInfo<SohuQuestionVo> questionPageContentCenter(Long userId, PageQuery pageQuery) {
        Long loginUserId = LoginHelper.getUserId();
        if (userId == null) {
            userId = loginUserId;
        }
        SohuQuestionBo dto = new SohuQuestionBo();
        dto.setUserId(userId);
        List<String> states = new ArrayList<>();
        states.add(BusyOrderStatus.OnShelf.name());
        if (Objects.equals(loginUserId, userId)) {
            // 查看自己的
            states.add(BusyOrderStatus.WaitApprove.name());
            states.add(BusyOrderStatus.CompelOff.name());
            states.add(BusyOrderStatus.Hide.name());
        }
        dto.setStateList(states);
        if (dto.getUserId() == null) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        return questionPageList(dto, pageQuery);
    }

    @Override
    public TableDataInfo<SohuQuestionVo> questionCollectList(String busyTitle, PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        if (userId == null) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        Page<SohuQuestionAnswerVo> result = baseMapper.questionCollectList(busyTitle, userId, PageQueryUtils.build(pageQuery));
        Page<SohuQuestionVo> list = new Page<>();
        if (CollUtil.isNotEmpty(result.getRecords())) {
            List<SohuQuestionVo> questionList = new ArrayList<>();
            result.getRecords().forEach(item -> {
                SohuQuestionVo questionVo = BeanCopyUtils.copy(item, SohuQuestionVo.class);
                // 问题数据
                questionVo.setId(item.getQuestionId());
                questionVo.setTitle(item.getTitle());
                // 总回答数
                questionVo.setAnswerCount(sohuQuestionAnswerMapper.answerCount(item.getQuestionId(), userId));
                // 总点赞数
                Long price = sohuQuestionAnswerMapper.countPrice(item.getQuestionId(), userId);
                questionVo.setTotalPrise(price != null ? price : 0);
                // 回答数据
                questionVo.setQuestionAnswerVo(item);
                questionList.add(questionVo);
            });
            list.setRecords(questionList);
            list.setTotal(result.getTotal());
        }
        return TableDataInfoUtils.build(list);
    }

    /**
     * 查询问题主体列表
     */
    @Override
    public List<SohuQuestionVo> queryList(SohuQuestionBo bo) {
        LambdaQueryWrapper<SohuQuestion> lqw = buildQueryWrapper(bo);
        lqw.orderByAsc(SohuQuestion::getSortIndex);
        lqw.orderByDesc(SohuQuestion::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuQuestion> buildQueryWrapper(SohuQuestionBo bo) {
        LambdaQueryWrapper<SohuQuestion> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getSiteId() != null, SohuQuestion::getSiteId, bo.getSiteId());
        lqw.eq(bo.getCategoryId() != null, SohuQuestion::getCategoryId, bo.getCategoryId());
        lqw.eq(bo.getUserId() != null, SohuQuestion::getUserId, bo.getUserId());
        lqw.eq(bo.getLessonLabelId() != null, SohuQuestion::getLessonLabelId, bo.getLessonLabelId());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), SohuQuestion::getTitle, bo.getTitle());
        lqw.like(StringUtils.isNotBlank(bo.getContent()), SohuQuestion::getContent, bo.getContent());
        if (StrUtil.isEmpty(bo.getState())) {
            lqw.notIn(SohuQuestion::getState, CommonState.Edit.getCode(), CommonState.Delete.getCode(), CommonState.ForceDelete.getCode());
        } else if (StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.OffShelf.getCode())) {
            lqw.in(SohuQuestion::getState, CommonState.OffShelf.getCode(), CommonState.CompelOff.getCode());
        } else {
            lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuQuestion::getState, bo.getState());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getType()), SohuQuestion::getType, bo.getType());
        lqw.in(CollUtil.isNotEmpty(bo.getStateList()), SohuQuestion::getState, bo.getStateList());
        lqw.notIn(CollUtil.isNotEmpty(bo.getNotIds()), SohuQuestion::getId, bo.getNotIds());
        lqw.in(CollUtil.isNotEmpty(bo.getIds()), SohuQuestion::getId, bo.getIds());
        lqw.in(CollUtil.isNotEmpty(bo.getCategoryIds()), SohuQuestion::getCategoryId, bo.getCategoryIds());
        lqw.in(CollUtil.isNotEmpty(bo.getSiteIds()), SohuQuestion::getSiteId, bo.getSiteIds());
        lqw.eq(StringUtils.isNotBlank(bo.getContentState()), SohuQuestion::getState, bo.getContentState());
        lqw.eq(bo.getAppealStatus() != null, SohuQuestion::getAppealStatus, bo.getAppealStatus());
        //狐少少课堂不过滤有关联项的问题
        List<SohuQoraRelate> sohuQoraRelates = sohuQoraRelateMapper.selectList();
        List<Long> ids = sohuQoraRelates.stream().map(SohuQoraRelate::getQoraId).collect(Collectors.toList());
        if (!VideoEnum.Type.lesson.getCode().equals(bo.getType())) {
            if (bo.getIsRelate() != null && !bo.getIsRelate()) {
                if (CollUtil.isNotEmpty(ids)) {
                    lqw.notIn(SohuQuestion::getId, ids);
                }
            }
        }
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            lqw.ge(StrUtil.isNotBlank(bo.getStartTime()), SohuQuestion::getCreateTime, DateUtil.beginOfDay(DateUtil.parseDate(bo.getStartTime())));
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            lqw.le(StrUtil.isNotBlank(bo.getEndTime()), SohuQuestion::getCreateTime, DateUtil.endOfDay(DateUtil.parseDate(bo.getEndTime())));
        }
        if (CollUtil.isNotEmpty(bo.getHandleIds())) {
            if (bo.getIsBlack()) {
                lqw.in(SohuQuestion::getId, bo.getHandleIds());
            } else {
                lqw.notIn(SohuQuestion::getId, bo.getHandleIds());
            }
        }
        return lqw;
    }

    /**
     * 新增问题主体
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(SohuQuestionBo bo) {
        SohuQuestion add = BeanUtil.toBean(bo, SohuQuestion.class);
        validEntityBeforeSave(add);
        String state = StrUtil.isNotBlank(bo.getState()) && StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.Edit.name()) ?
                CommonState.Edit.name() : CommonState.WaitApprove.name();
        add.setState(state);
        if (StrUtil.isBlank(bo.getType())) {
            add.setType(VideoEnum.Type.general.getCode());
        }
        if (StrUtil.isBlank(bo.getVisibleType())) {
            add.setVisibleType(VisibleTypeEnum.open.getCode());
        }
        add.setUserId(LoginHelper.getUserId());
//        String scanText = null;
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
            add.setSubmitTime(new Date());
            if (Objects.equals(bo.getDataSource(), Constants.THIRDPARTY)) {
                add.setSubmitScene("第三方");
            } else {
                add.setSubmitScene("自主发布");
            }
            add.setSubmitNum(Constants.ONE);
            // 更改机审信息
            add.setState(CommonState.WaitApprove.getCode());
            add.setAuditState(CommonState.WaitApprove.name());
//            scanText = aliyunAuditService.scanText(HtmlUtil.cleanHtmlTag(bo.getTitle() + bo.getContent()), AliyunAuditLabelEnum.textCheck);
//            if (StrUtil.isEmptyIfStr(scanText)) {
//                add.setState(CommonState.OnShelf.getCode());
//                add.setAuditTime(new Date());
//                add.setAuditState(CommonState.Pass.name());
//            } else {
//                add.setState(CommonState.WaitApprove.getCode());
//                add.setAuditState(CommonState.WaitApprove.name());
//                List<String> labels = parseLabelsFromJson(scanText);
//                // 匹配枚举并获取描述信息
//                List<String> descriptions = labels.stream()
//                        .map(AliyunAuditLabelEnum::getDescriptionByLabel)
//                        .filter(Optional::isPresent)
//                        .map(Optional::get)
//                        .collect(Collectors.toList());
//                String rejectReason = String.join("; ", descriptions);
//                add.setRejectReason(rejectReason);
//            }
        }
        //如果站点没传 需要做一下兜底
        if (Objects.isNull(bo.getSiteId())) {
            SohuSiteVo siteVo = siteService.getSiteByIp(ServletUtils.getClientIP());
            add.setSiteId(siteVo.getId());
            if (Objects.equals(siteVo.getType(), SiteType.City.name())) {
                Long pid = siteVo.getPid();
                add.setCountrySiteId(pid);
            }
        } else {
            SohuSiteVo sohuSiteVo = siteService.selectSiteByPid(bo.getSiteId());
            if (Objects.nonNull(sohuSiteVo)) {
                add.setCountrySiteId(sohuSiteVo.getId());
            }
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        // 保存至万能表
        sohuSyncContentService.sync(add);
        SohuQoraInfoBo qoraInfoBo = new SohuQoraInfoBo();
        qoraInfoBo.setQoraId(add.getId());
        qoraInfoBo.setQoraType(BusyType.Question.name());
        qoraInfoBo.setLearnNum(bo.getLearnNum());
        qoraInfoBo.setChildTaskNum(bo.getBusyInfo());
        // 保存拓展表
        sohuQoraInfoService.insertByBo(qoraInfoBo);
        // 保存关联项
        addQoraRelate(bo);
        // TODO 关联话题
        // 保存审核记录
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
            this.initCreateAudited(add, true);
            // 发布异步机审消息
            SohuRiskMqBo riskMqBo = new SohuRiskMqBo();
            riskMqBo.setBusyType(BusyType.Question);
            riskMqBo.setBusyCode(add.getId().toString());
            MqMessaging mqMessaging = new MqMessaging(JSONObject.toJSONString(riskMqBo), MqKeyEnum.HANDLE_RISK_MANAGEMENT.getKey());
            remoteStreamMqService.sendDelayMsg(mqMessaging, 2L);
        }
        //新增图文物料
        this.addSohuAirecContentItem(add, bo);
        return flag;
    }

    /**
     * 保存关联项
     *
     * @param bo
     */
    public void addQoraRelate(SohuQuestionBo bo) {
        //删除关联项
        //TODO 1.关注测试逻辑是否为需要删除之前的关联；2.以下代码操作busyCode为保存业务id还是保存loginUser.getUserId()
        sohuQoraRelateService.deleteByQuestionIds(Arrays.asList(bo.getId()));
        if (StrUtil.isNotBlank(bo.getBusyType()) && ((StrUtil.equalsAnyIgnoreCase(bo.getBusyType(), BusyType.Window.name()) || bo.getBusyCode() != null || StrUtil.isNotBlank(bo.getBusyTitle())) || StrUtil.isNotBlank(bo.getBusyInfo()) || StrUtil.isNotBlank(bo.getBusyType()))) {
            SohuQoraRelateBo relateBo = new SohuQoraRelateBo();
            relateBo.setQoraId(bo.getId());
            relateBo.setQoraType(BusyType.Question.name());
            relateBo.setBusyType(bo.getBusyType());
            relateBo.setBusyInfo(bo.getBusyInfo());
            Long busyCode = bo.getBusyCode();
            String busyTitle = bo.getBusyTitle();
            if (StrUtil.equalsAnyIgnoreCase(bo.getBusyType(), BusyType.Window.name())) {
                LoginUser loginUser = LoginHelper.getLoginUser();
                if (loginUser != null) {
                    busyTitle = loginUser.getNickname();
                    busyCode = loginUser.getUserId();
                }
            } else if (StrUtil.equalsAnyIgnoreCase(bo.getBusyType(), BusyType.GoodsWindow.name())) {
                LoginUser loginUser = LoginHelper.getLoginUser();
                if (loginUser != null) {
                    busyCode = loginUser.getUserId();
                }
            } else if (StrUtil.equalsAnyIgnoreCase(bo.getBusyType(), BusyType.BusyTask.name())) {
                LoginUser loginUser = LoginHelper.getLoginUser();
                if (loginUser != null) {
                    busyCode = loginUser.getUserId();
                }
            }
            relateBo.setBusyCode(busyCode);
            relateBo.setBusyTitle(busyTitle);
            sohuQoraRelateService.insertByBo(relateBo);
        }
    }

    /**
     * 修改问题主体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(SohuQuestionBo bo) {
        SohuQuestionVo sohuQuestionVo = baseMapper.selectVoById(bo.getId());
        validateArticleExists(sohuQuestionVo);
//        String state = StrUtil.isNotBlank(bo.getState()) && StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.Edit.name()) ?
//                CommonState.Edit.name() : CommonState.WaitApprove.name();
        String state = StrUtil.isBlankIfStr(bo.getState())
                ? CommonState.WaitApprove.name()
                : (StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.Edit.name())
                ? CommonState.Edit.name()
                : bo.getState());
        //String state = bo.getState();
        SohuQuestion update = BeanUtil.toBean(bo, SohuQuestion.class);
        if (!(Objects.equals(CommonState.Refuse.name(), sohuQuestionVo.getState())
                || Objects.equals(CommonState.Edit.name(), sohuQuestionVo.getState())
                || Objects.equals(CommonState.CompelOff.name(), sohuQuestionVo.getState())
                || Objects.equals(CommonState.OffShelf.name(), sohuQuestionVo.getState()))) {
            throw new RuntimeException("此状态不支持修改");
        }
        bo.setState(state);
        update.setState(state);
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
            if (sohuQuestionVo.getSubmitNum().compareTo(SUBMIT_NUM_MAX_VALUE) > -1) {
                throw new RuntimeException("您已提交" + sohuQuestionVo.getSubmitNum() + "次，最大提交次数为" + SUBMIT_NUM_MAX_VALUE);
            }
            fieldChangeChecker(bo, sohuQuestionVo);
            update.setSubmitTime(new Date());
            if (sohuQuestionVo.getSubmitNum() > 0) {
                update.setSubmitScene("下架整改");
            } else {
                if (Objects.equals(bo.getDataSource(), Constants.THIRDPARTY)) {
                    update.setSubmitScene("第三方");
                } else {
                    update.setSubmitScene("自主发布");
                }
                //update.setSubmitScene("自主发布");
            }
            update.setSubmitNum(sohuQuestionVo.getSubmitNum() + 1);
            update.setAppealStatus(false);
//            String scanText = aliyunAuditService.scanText(HtmlUtil.cleanHtmlTag(bo.getTitle() + bo.getContent()), AliyunAuditLabelEnum.textCheck);
//            if (StrUtil.isNotBlank(scanText)) {
//                update.setState(CommonState.WaitApprove.getCode());
//                update.setAuditState(CommonState.WaitApprove.getCode());
//                List<String> labels = parseLabelsFromJson(scanText);
//                // 匹配枚举并获取描述信息
//                List<String> descriptions = labels.stream()
//                        .map(AliyunAuditLabelEnum::getDescriptionByLabel)
//                        .filter(Optional::isPresent)
//                        .map(Optional::get)
//                        .collect(Collectors.toList());
//                String rejectReason = String.join("; ", descriptions);
//                update.setRejectReason(rejectReason);
//                log.warn("【{}】标题或内容违规，审核结果：{}", update.getId(), scanText);
//            } else {
//                update.setState(CommonState.OnShelf.name());
//                update.setAuditTime(new Date());
//                update.setAuditState(CommonState.Pass.name());
//            }
            update.setState(CommonState.WaitApprove.getCode());
            update.setAuditState(CommonState.WaitApprove.name());
        } else if (StrUtil.equalsAnyIgnoreCase(state, CommonState.Edit.name())) {
            update.setAuditState(null);
        }
        validEntityBeforeSave(update);
        // 更新至万能表
        sohuSyncContentService.sync(update);
        SohuQoraInfoBo sohuQoraInfoBo = new SohuQoraInfoBo();
        sohuQoraInfoBo.setLearnNum(bo.getLearnNum());
        sohuQoraInfoBo.setQoraId(bo.getId());
        //修改初始学习人数
        sohuQoraInfoMapper.updateByQoraId(sohuQoraInfoBo);
        //保存关联项
        addQoraRelate(bo);
        // 保存审核记录
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
            update.setUserId(sohuQuestionVo.getUserId());
//            update.setSiteId(sohuQuestionVo.getSiteId());
        }
//        this.initCreateAudited(update);
//        return baseMapper.updateById(update) > 0;
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name()) || StrUtil.equalsAnyIgnoreCase(state, CommonState.Edit.name())) {
            LambdaUpdateWrapper<SohuQuestion> luw = new LambdaUpdateWrapper();
            luw.eq(SohuQuestion::getId, update.getId());
            luw.set(SohuQuestion::getAppealReason, null);
            luw.set(SohuQuestion::getRejectReason, update.getRejectReason());
            luw.set(SohuQuestion::getAuditState, update.getAuditState());
            update.setAppealReason(null);
            //update.setRejectReason(null);
            baseMapper.updateByIdThenEviction(update, luw);
        } else {
//            update.setState(null);
//            update.setAuditState(null);
            baseMapper.updateById(update);
        }
        SohuContentLifecycleBo lifecycleBo = new SohuContentLifecycleBo(sohuQuestionVo.getId(), BusyType.Question.name(), sohuQuestionVo.getState(), update.getState(), "提交审核");
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.WaitApprove.name())) {
            this.initCreateAudited(update, true);
            // 发布异步机审消息
            SohuRiskMqBo riskMqBo = new SohuRiskMqBo();
            riskMqBo.setBusyType(BusyType.Question);
            riskMqBo.setBusyCode(update.getId().toString());
            MqMessaging mqMessaging = new MqMessaging(JSONObject.toJSONString(riskMqBo), MqKeyEnum.HANDLE_RISK_MANAGEMENT.getKey());
            remoteStreamMqService.sendDelayMsg(mqMessaging, 2L);
        } else {
            lifecycleBo.setRemark("编辑");
        }
        this.createLifecycleLog(lifecycleBo);
        //新增物料
        this.addSohuAirecContentItem(update, bo);
        return true;
    }

    private void fieldChangeChecker(SohuQuestionBo bo, SohuQuestionVo entityVo) {
        if (StrUtil.equalsAnyIgnoreCase(entityVo.getState(), CommonState.Edit.name())) {
            return;
        }
        SohuContentLifecycleVo lifecycleVo = this.sohuContentLifecycleService.selectOfLast(entityVo.getId(), BusyType.Question.name());
        if (Objects.nonNull(lifecycleVo) && Objects.equals(entityVo.getState(), lifecycleVo.getLastState()) && Objects.equals(entityVo.getState(), lifecycleVo.getCurrentState())) {
            return;
        }
        SohuQuestionBo oldValue = BeanUtil.copyProperties(entityVo, SohuQuestionBo.class);
        if (!SohuFieldChangeUtil.hasFieldChanged(oldValue, bo)) {
            throw new RuntimeException("请修改后再提交");
        }
    }

    /**
     * 从 JSON 数据中提取所有的 label 字段
     */
    private static List<String> parseLabelsFromJson(String jsonString) {
        List<String> labels = new ArrayList<>();
        JSONArray jsonArray = JSON.parseArray(jsonString);

        for (Object obj : jsonArray) {
            JSONObject jsonObject = (JSONObject) obj;
            String label = jsonObject.getString("label");
            if (label != null) {
                labels.add(label);
            }
        }
        return labels;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuQuestion entity) {
        // TODO 做一些数据校验,如唯一约束
        entity.setUpdateTime(new Date());
        if (entity.getId() == null || entity.getId() <= 0L) {
            entity.setCreateTime(new Date());
        }
    }

    /**
     * 批量删除问题主体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        //代码规范问题，注释掉
        /*if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }*/
        for (Long id : ids) {
            // 删除万能表数据
            sohuContentMainService.deleteByObj(id, BusyType.Question.name());
            // 删除审核数据
            sohuAuditService.deleteByObj(id, BusyType.Question.name());
            iSohuAirecContentItemService.updateStatusToOffShelf(id.toString(), AliyunAirecContentItemTypeEnum.RECIPE.getCode());
        }
        // 删除关联项数据
        sohuQoraRelateService.deleteByQuestionIds(ids);
        // 删除拓展数据
        sohuQoraInfoService.deleteByQuestionIds(ids);
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void commit(Long id) {
        SohuQuestion sohuQuestion = baseMapper.selectById(id);
        validateArticleExists(sohuQuestion);
        sohuQuestion.setState(CommonState.WaitApprove.name());
        this.baseMapper.updateById(sohuQuestion);
        // 同步至万能表
        sohuSyncContentService.sync(sohuQuestion);
        // 同步至审核
        this.initCreateAudited(sohuQuestion);
    }

    @Override
    public Boolean submitAudit(Long id) {
        SohuQuestion sohuQuestion = baseMapper.selectById(id);
        validateArticleExists(sohuQuestion);
        SohuQuestionBo bo = BeanUtil.copyProperties(sohuQuestion, SohuQuestionBo.class);
        bo.setState(CommonState.WaitApprove.name());
        SohuQoraInfoVo infoVo = sohuQoraInfoService.queryObj(id, BusyType.Question.name());
        if (Objects.nonNull(infoVo)) {
            bo.setLearnNum(infoVo.getLearnNum());
        }
        return this.updateByBo(bo);
    }

    private void validateArticleExists(SohuQuestionVo questionVo) {
        if (Objects.isNull(questionVo)) {
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
    }

    private void validateArticleExists(SohuQuestion sohuQuestion) {
        if (Objects.isNull(sohuQuestion)) {
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
    }

    /**
     * 问题同步至审核
     *
     * @param question 问题对象
     */
    private void initCreateAudited(SohuQuestion question) {
        this.initCreateAudited(question, false);
    }

    /**
     * 问题同步至审核
     *
     * @param question 问题对象
     */
    private void initCreateAudited(SohuQuestion question, Boolean isAiAudit) {
        SohuQuestionBo bo = BeanUtil.toBean(question, SohuQuestionBo.class);
        sohuAuditInitService.initCreateAudited(bo, isAiAudit);
    }

    @Override
    public Long getMcnQuestionPraiseStat(Long userId, Long mcnId) {
        // 获取问答集合
        List<SohuQoraInfo> QoraInfoList = this.getQuestionInfoList(userId, mcnId);
        // 汇总问答点赞数
        Long totalQuestionPraiseStat = QoraInfoList.stream()
                .mapToLong(SohuQoraInfo::getPraiseCount)
                .sum();

        return totalQuestionPraiseStat;
    }

    @Override
    public Long getMcnQuestionCommentStat(Long userId, Long mcnId) {
        // 获取问答集合
        List<SohuQoraInfo> QoraInfoList = this.getQuestionInfoList(userId, mcnId);
        // 汇总问答评论数
        Long totalQuestionCommentStat = QoraInfoList.stream()
                .mapToLong(SohuQoraInfo::getCommentCount)
                .sum();

        return totalQuestionCommentStat;
    }

    @Override
    public Long getMcnQuestionViewStat(Long userId, Long mcnId) {
        // 获取问答集合
        List<SohuQoraInfo> QoraInfoList = this.getQuestionInfoList(userId, mcnId);
        // 汇总问答浏览数
        Long totalQuestionViewStat = QoraInfoList.stream()
                .mapToLong(SohuQoraInfo::getViewCount)
                .sum();

        return totalQuestionViewStat;
    }

    @Override
    public IPage<SohuQuestionVo> businessQuestionList(SohuBusinessQuestionBo bo) {
        Page page = new Page(bo.getPageNum(), bo.getPageSize());
        IPage<SohuQuestionVo> result = baseMapper.businessQuestionList(page, bo);
        this.buildQuestionVo(result.getRecords());

        return result;
    }

    /**
     * 构建返回的参数，有相似代码，请重构
     *
     * @param records
     */
    private void buildQuestionVo(List<SohuQuestionVo> records) {
        if (CollUtil.isNotEmpty(records)) {
            Set<Long> userIds = new HashSet<>();
            Set<Long> questionIds = new HashSet<>();
            records.forEach(p -> {
                userIds.add(p.getUserId());
                questionIds.add(p.getId());
            });
            LambdaQueryWrapper<SohuQoraInfo> lqw = new LambdaQueryWrapper<>();
            lqw.eq(SohuQoraInfo::getQoraType, BusyType.Question.name()).in(SohuQoraInfo::getQoraId, questionIds);
            List<SohuQoraInfo> infoList = this.sohuQoraInfoMapper.selectList(lqw);
            Map<Long, SohuQoraInfo> infoMap = infoList.stream().collect(Collectors.toMap(SohuQoraInfo::getQoraId, u -> u));
            Map<Long, LoginUser> userMap = this.sohuUserService.selectMap(userIds);
            for (SohuQuestionVo vo : records) {
                LoginUser user = userMap.get(vo.getUserId());
                if (Objects.nonNull(user)) {
                    vo.setAuthorAvatar(user.getAvatar());
                    vo.setAuthorName(user.getUsername());
                }
                SohuQoraInfo info = infoMap.get(vo.getId());
                if (Objects.isNull(info)) {
                    continue;
                }
                vo.setRelation(this.buildRelate(vo.getId()));
            }
            this.getRecord(records, LoginHelper.getUserId());
        }
    }

    @Override
    public TableDataInfo<SohuQuestionVo> businessQuestionListOfAirec(SohuBusinessQuestionBo bo) {
        if (bo.getAiRec()) {
            if (StrUtil.isBlank(bo.getAiRecImei()) && Objects.isNull(LoginHelper.getUserId())) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (!(AliyunAirecConstant.SCENE_QUESTION_ALL.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_QUESTION_HOMEPAGE.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_QUESTION_MONEYMAKING.equals(bo.getAiRecSceneId()))) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (Objects.nonNull(LoginHelper.getUserId())) {
                bo.setAiUserId(LoginHelper.getUserId().toString());
            }
            bo.setAiReturnCount(bo.getPageSize());
            //构造过滤的内容
            SohuAirecQuestionQueryBo airecQueryBo = new SohuAirecQuestionQueryBo();
            if (Objects.nonNull(bo.getSiteId())) {
                SohuSiteVo sohuSiteVo = siteService.queryById(bo.getSiteId());
                if (Objects.nonNull(sohuSiteVo)) {
                    if (Objects.equals(sohuSiteVo.getType(), SiteType.City.name())) {
                        airecQueryBo.setCity(String.valueOf(sohuSiteVo.getId()));
                    } else if (Objects.equals(sohuSiteVo.getType(), SiteType.Country.name())) {
                        airecQueryBo.setCountry(sohuSiteVo.getCountryCode());
                    }
                }
            }
            SohuAirecContentItemVo airecCategoryInfo = sohuCategoryService.getAirecCategoryInfoById(bo.getCategoryId());
            airecQueryBo.setCategoryPath(airecCategoryInfo.getCategoryPath());
            AliyunAirecJoinFilterRule rootRule = airecQueryBo.buildAirecFilterRule();
            //this.buildAirecJoinFilterRule(bo.getSiteId(), rootRule);
            //获取阿里云智能推荐结果
            List<SohuQuestionVo> resultList = AliyunAirecUtil.aiRecommendSingleType(rootRule, bo, itemIds -> baseMapper.selectVoBatchIds(itemIds));
            if (CollUtil.isNotEmpty(resultList)) {
                buildQuestionVo(resultList);
                return TableDataInfoUtils.build(resultList);
            }
        }
        IPage<SohuQuestionVo> page = this.businessQuestionList(bo);
        AliyunAirecUtil.buildAiRecommendSingleType(page.getRecords(), AliyunAirecContentItemTypeEnum.RECIPE.getCode());
        return TableDataInfoUtils.build(page);
    }

    /**
     * 获取问答实体集合
     *
     * @param userId 当前用户id
     * @param mcnId  MCN机构id
     * @return List
     */
    private List<SohuQoraInfo> getQuestionInfoList(Long userId, Long mcnId) {
        LambdaQueryWrapper<SohuQuestion> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuQuestion::getUserId, userId)
                .eq(SohuQuestion::getMcnUserId, mcnId);
        // 获取视频id集合
        List<SohuQuestion> questionList = baseMapper.selectList(lqw);
        if (CollectionUtils.isEmpty(questionList)) {
            return new ArrayList<>();
        }
        List<Long> questionIds = questionList.stream().map(SohuQuestion::getId).collect(Collectors.toList());

        LambdaQueryWrapper<SohuQoraInfo> infoWrapper = Wrappers.lambdaQuery();
        infoWrapper.in(SohuQoraInfo::getQoraId, questionIds);
        // 根据视频id获取视频集合  SohuQoraInfo->问答详情实体
        List<SohuQoraInfo> QoraInfoList = sohuQoraInfoMapper.selectList(infoWrapper);
        return QoraInfoList;
    }

    /**
     * 问题添加至智能推荐物料
     *
     * @param question 当前用户id
     */
    @Override
    public void updateSohuAirecContentQuestionItem(SohuQuestion question) {
//        String questionState = question.getState();
//        if (StringUtils.isEmpty(questionState)) {
//            return;
//        }
//        Long questionId = question.getId();
//        //根据问题id和类型查询推荐表中是否存在
//        SohuAirecContentItemModel model = remoteSohuAirecContentItemService.getByItemId(String.valueOf(questionId), AliyunAirecContentItemTypeEnum.RECIPE.getCode());
//        //不存在就是插入
//        if (ObjectUtil.isNull(model)) {
//            model = createModel(question);
//            //当物料表中不存在，只处理上架状态的数据
//            if (StringUtils.equalsIgnoreCase(CommonState.OnShelf.getCode(), question.getState())) {
//                remoteSohuAirecContentItemService.insertContentItem(model);
//            }
//        } else {
//            //更新上架状态的数据为可推荐，更新Constants.notRecommend之中的数据为不可推荐
//            if (StringUtils.equalsIgnoreCase(CommonState.OnShelf.getCode(), question.getState())) {
//                model.setStatus(AirecItemStatusEnum.CONTENT_YES.getCode());
//            } else if (StringUtils.equalsAnyIgnoreCase(question.getState(), Constants.notRecommend)) {
//                model.setStatus(AirecItemStatusEnum.CONTENT_NO.getCode());
//            }
//            remoteSohuAirecContentItemService.updateContentItem(model);
//        }
//        if (!Objects.equals(VisibleTypeEnum.open.getCode(), question.getVisibleType())) {
//            return;
//        }
        //上架
        if (StringUtils.equalsAnyIgnoreCase(CommonState.OnShelf.getCode(), question.getState())) {
            // 关联项
            SohuQoraRelate relate = sohuQoraRelateMapper.selectOne(SohuQoraRelate::getQoraId, question.getId(), SohuQoraRelate::getQoraType, BusyType.Question.name());
            SohuAirecContentItemBo model = buildAirecContentItemModel(question, Objects.nonNull(relate));
            model.setTags(iSohuAirecTagRelationService.saveTagStr(question.getId(), AiRecTag.BizTypeEnum.QUESTION.getCode(), model.getTags()));
            iSohuAirecContentItemService.saveAirecContentItem(model);
        } else {
            //下架
            iSohuAirecContentItemService.updateStatusToOffShelf(question.getId().toString(), AliyunAirecContentItemTypeEnum.RECIPE.getCode());
        }

    }

    /**
     * 创建智能推荐物料数据
     *
     * @param question
     * @param hasRelate 存在关联
     * @return
     */
    public SohuAirecContentItemBo buildAirecContentItemModel(SohuQuestion question, Boolean hasRelate) {
        SohuAirecContentItemBo model = new SohuAirecContentItemBo();
        model.setItemId(String.valueOf(question.getId()));
        model.setItemType(AliyunAirecContentItemTypeEnum.RECIPE.getCode());
        //model.setSceneId(AliyunAirecConstant.SCENE_QUESTION_HOMEPAGE);
        Date createTime = question.getCreateTime();
        long time = DateUtils.getTimeOfSecond(createTime);
        model.setPubTime(String.valueOf(time));
        model.setTitle(question.getTitle());
        model.setWeight(AliyunAirecItemWeightEnum.NOT_WEIGHT.getCode());
        model.setAuthor(question.getUserId().toString());
        model.setContent(StringUtils.isEmpty(question.getContent()) ? question.getTitle() : StringUtils.substring(question.getContent(), AliyunAirecConstant.CONTENT_SUBSTRING));
        model.setCity(String.valueOf(question.getSiteId()));
//        model.setStatus(AirecItemStatusEnum.CONTENT_YES.getCode());
        if (StringUtils.equalsAnyIgnoreCase(CommonState.OnShelf.getCode(), question.getState())
                && Objects.equals(VisibleTypeEnum.open.getCode(), question.getVisibleType())) {
            model.setStatus(AirecItemStatusEnum.CONTENT_YES.getCode());
        } else {
            model.setStatus(AirecItemStatusEnum.CONTENT_NO.getCode());
        }
        SohuAirecContentItemVo airecCategoryInfo = sohuCategoryService.getAirecCategoryInfoById(question.getCategoryId());
        model.setCategoryPath(airecCategoryInfo.getCategoryPath());
        model.setCategoryLevel(airecCategoryInfo.getCategoryLevel());
        //贴标签
        String code = siteService.selectCountryCodeById(question.getCountrySiteId());
        if (StrUtil.isNotBlank(code)) {
            model.setCountry(code);
        }
        this.buildTagsAndScene(question, hasRelate, model);
        return model;
    }

    /**
     * 贴标签和场景
     *
     * @param question
     * @param hasRelate 存在关联
     * @param model
     */
    private void buildTagsAndScene(SohuQuestion question, Boolean hasRelate, SohuAirecContentItemBo model) {
        //标签
        Set<String> tagSet = new HashSet<>();
        //场景
        Set<String> sceneIdSet = new HashSet<>();
        //作品类型标签
        if (Objects.equals(VideoEnum.Type.general.getCode(), question.getType())) {
            tagSet.add(AliyunAirecTagsConstant.MEDIA_TYPE_GENERAL);
        } else if (Objects.equals(VideoEnum.Type.lesson.getCode(), question.getType())) {
            tagSet.add(AliyunAirecTagsConstant.MEDIA_TYPE_LESSON);
        }
        //关联标签
        if (hasRelate) {
            tagSet.add(AliyunAirecTagsConstant.MEDIA_HAS_RELATE);
            sceneIdSet.add(AliyunAirecConstant.SCENE_QUESTION_MONEYMAKING);
        } else {
            sceneIdSet.add(AliyunAirecConstant.SCENE_QUESTION_HOMEPAGE);
        }
        sceneIdSet.add(AliyunAirecConstant.SCENE_QUESTION_ALL);
        model.setTags(String.join(AliyunAirecConstant.CONNECTED_COMMA, tagSet));
        model.setSceneId(String.join(AliyunAirecConstant.CONNECTED_COMMA, sceneIdSet));
    }

//    /**
//     * 获取问题推荐
//     *
//     * @param sohuQuestionBo
//     * @return TableDataInfo
//     */
//    @Override
//    public TableDataInfo<SohuQuestionVo> getSohuAirecContentQuestion(SohuQuestionBo sohuQuestionBo) {
//        RecommendRequest request = new RecommendRequest();
//        request.setSceneId(AliyunAirecConstant.SCENE_QUESTION_HOMEPAGE);
//        request.setUserId(sohuQuestionBo.getCurrentUserId() == null ? null : String.valueOf(sohuQuestionBo.getCurrentUserId()));
//        request.setReturnCount(AliyunAirecConstant.AIREC_MAX);
//        if (StrUtil.isNotBlank(sohuQuestionBo.getImei())) {
//            request.setImei(DigestUtil.md5Hex(sohuQuestionBo.getImei()));
//        }
//        //构造过滤的内容
//        AliyunAirecJoinFilterRule rootRule = new AliyunAirecJoinFilterRule();
//        rootRule.setJoin(AliyunAirecQueryJoinEnum.AND.getCode());
//
//        {
//            AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
//            rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
//            rule.setField(AliyunAirecQueryFieldContentEnum.ITEM_TYPE.getCode());
//            rule.setValue(AliyunAirecContentItemTypeEnum.RECIPE.getCode());
//            rootRule.getFilters().add(rule);
//        }
//
//        //调用获取推荐的工具方法
//        AliyunAirecRecommendResponse recommend = AliyunAirecUtil.aliyunAirecRecommendResponse(request, rootRule);
//        Map<String, List<String>> idGroup = recommend.getIds();
//        List<String> ids = idGroup.get(AliyunAirecContentItemTypeEnum.RECIPE.getCode());
//        List<SohuQuestionVo> sohuQuestionVos = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(ids)) {
//            sohuQuestionVos = baseMapper.selectVoBatchIds(ids);
//            //存在极端返回时文章被删的情况
//            if (CollectionUtils.isNotEmpty(sohuQuestionVos)) {
//                //获取sohuArticleVos中对应的智能推荐返回的ResultItem
//                sohuQuestionVos.stream().forEach(vos -> {
//                    RecommendResponse.ResultItem resultItem = recommend.getResult().stream().filter(r ->
//                            StringUtils.equalsIgnoreCase(String.valueOf(vos.getId()), r.getItemId())
//                    ).findFirst().orElse(null);
//                    vos.setResultItem(resultItem);
//                });
//            }
//        }
//        return TableDataInfoUtils.build(sohuQuestionVos);
//    }

    /**
     * 初始化问题物料
     */
    @Override
    public Boolean initAirecContentItems() {
//        LambdaQueryWrapper<SohuQuestion> lqw = new LambdaQueryWrapper<>();
//        lqw.eq(SohuQuestion::getState, CommonState.OnShelf.name())
//                .eq(SohuQuestion::getVisibleType, VisibleTypeEnum.open.getCode());
        //设置分页参数
        // 查询总数
        //long total = baseMapper.selectCount(lqw);
        long total = baseMapper.selectCount();
        final int PAGE_SIZE = AliyunAirecConstant.BATCH_SIZE;
        // 总页数
        long totalPages = (total + PAGE_SIZE - 1) / PAGE_SIZE;
        for (int i = 1; i <= totalPages; i++) {
            // 分页查询
            Page<SohuQuestion> page = new Page<>(i, PAGE_SIZE);
            //IPage<SohuQuestion> pageResult = baseMapper.selectPage(page, lqw);
            IPage<SohuQuestion> pageResult = baseMapper.selectPage(page, null);
            List<SohuQuestion> list = pageResult.getRecords();
            // 处理查询结果
            if (CollUtil.isNotEmpty(list)) {
                //关联项
                List<Long> idList = list.stream().map(p -> p.getId()).collect(Collectors.toList());
                List<SohuQoraRelate> relateList = this.sohuQoraRelateMapper.selectList(SohuQoraRelate::getQoraId, idList);
                //存在关联项的id集合
                Set<Long> idSet = new HashSet<>();
                if (CollUtil.isNotEmpty(relateList)) {
                    idSet = relateList.stream().map(p -> p.getQoraId()).collect(Collectors.toSet());
                }
                //物料信息记录
                List<SohuAirecContentItemBo> modelList = new ArrayList<>();
                for (SohuQuestion entity : list) {
//                    SohuQuestionBo bo = new SohuQuestionBo();
//                    BeanUtil.copyProperties(entity, bo);
                    SohuAirecContentItemBo model = buildAirecContentItemModel(entity, idSet.contains(entity.getId()));
                    model.setTags(iSohuAirecTagRelationService.saveTagStr(entity.getId(), AiRecTag.BizTypeEnum.QUESTION.getCode(), model.getTags()));
                    modelList.add(model);
                }
                //保存物料信息
                iSohuAirecContentItemService.initAirecContentItems(modelList);
            }
        }
        return true;
    }

    /**
     * 返回每个标签下前五的问题
     * 双层数组结构返回
     */
    @Override
    public List<SohuTopQuestionVo> labelTopFive() {
        SohuLessonLabelBo sohuLessonLabelBo = new SohuLessonLabelBo();
        sohuLessonLabelBo.setType(BusyType.Question.name());
        List<SohuLessonLabelVo> sohuLessonLabelVos = sohuLessonLabelService.queryList(sohuLessonLabelBo);
        if (CollUtil.isEmpty(sohuLessonLabelVos)) {
            return null;
        }
        List<SohuTopQuestionVo> data = new ArrayList<>();
        for (SohuLessonLabelVo label : sohuLessonLabelVos) {
            SohuTopQuestionVo vo = new SohuTopQuestionVo();
            List<SohuQuestionVo> sohuQuestionVos = hotList(label.getId());
            if (CollUtil.isNotEmpty(sohuQuestionVos)) {
                vo.setLessonLabelId(label.getId());
                vo.setLabelName(label.getName());
                vo.setList(sohuQuestionVos);
                data.add(vo);
            }
        }
        return data;
    }

    @Override
    public TableDataInfo<SohuQuestionVo> questionPageCenterByType(SohuQuestionBo bo, PageQuery pageQuery) {
        Long loginUserId = LoginHelper.getUserId();
        SohuQuestionBo dto = new SohuQuestionBo();
        dto.setUserId(loginUserId);
        List<String> states = new ArrayList<>();
        states.add(BusyOrderStatus.OnShelf.name());
        dto.setStateList(states);
        dto.setType(bo.getType());
        if (StrUtil.isNotBlank(bo.getTitle())) {
            dto.setTitle(bo.getTitle());
        }
        if (dto.getUserId() == null) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        return questionPageList(dto, pageQuery);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean draftRetry(SohuBusyBO busyBO) {
        SohuQuestion question = this.baseMapper.selectById(busyBO.getBusyCode());
        if (Objects.isNull(question)) {
            return Boolean.FALSE;
        }
        question.setState(CommonState.WaitApprove.getCode());
        baseMapper.updateById(question);

        this.initCreateAudited(question);

        SohuContentMain contentMain = sohuContentMainService.getEntityByObj(busyBO.getBusyCode(), BusyType.Question.name());
        if (Objects.nonNull(contentMain)) {
            contentMain.setState(CommonState.WaitApprove.getCode());
            sohuContentMainService.updateById(contentMain);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean forward(SohuBusyBO bo) {
        SohuQoraInfoVo infoVo = sohuQoraInfoService.queryObj(bo.getBusyCode(), bo.getBusyType().name());
        if (Objects.isNull(infoVo) || infoVo.getId() == null) {
            return Boolean.FALSE;
        }
        int count = infoVo.getForwardCount() + 1;
        SohuQoraInfoBo infoBo = new SohuQoraInfoBo();
        infoBo.setId(infoVo.getId());
        infoBo.setForwardCount(count);
        sohuQoraInfoService.updateByBo(infoBo);
        SohuContentMainBo mainBo = new SohuContentMainBo();
        mainBo.setObjId(bo.getBusyCode());
        mainBo.setObjType(BusyType.Question.name());
        mainBo.setForwardCount(count);
        sohuContentMainService.updateByBo(mainBo);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateUserQuestionState(Long userId, String state) {
        if (userId == null || userId <= 0L) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<SohuQuestion> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuQuestion::getUserId, userId).set(SohuQuestion::getState, state);
        // 修改万能表的状态
        sohuContentMainService.updateUserObjState(userId, BusyType.Question.name(), state);
        return baseMapper.update(new SohuQuestion(), luw) > 0;
    }

    @Override
    public Boolean updateState(SohuBusyUpdateStateBo bo) {
        String rejectReason = bo.getRejectReason();
        Long busyCode = bo.getBusyCode();
        SohuQuestion question = baseMapper.selectById(busyCode);
        if (Objects.isNull(question)) {
            return Boolean.FALSE;
        }
        question.setState(StrUtil.isBlankIfStr(bo.getState()) ? CommonState.CompelOff.getCode() : bo.getState());
        question.setRejectReason(rejectReason);
        question.setUpdateTime(new Date());
        // 更新问题
        baseMapper.updateById(question);
        // 更新万能表
        sohuContentMainMapper.updateState(question.getState(), busyCode, BusyType.Question.name());
        // 智能推送表
        //SohuQuestionBo sohuQuestionBo = BeanUtil.copyProperties(question, SohuQuestionBo.class);
        updateSohuAirecContentQuestionItem(question);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean like(SohuBusyBO bo) {
        SohuQoraInfo info = sohuQoraInfoService.queryEntityObj(bo.getBusyCode(), bo.getBusyType().name());
        if (Objects.isNull(info) || info.getId() == null) {
            return false;
        }
        log.info("点赞问题：{}", JSONUtil.toJsonStr(bo));
        Integer oldCount = info.getPraiseCount();
        int count = bo.getIsAdd() != null && bo.getIsAdd() ? oldCount + 1 : Math.max((oldCount - 1), 0);
        info.setPraiseCount(count);
        // 更新万能表的点赞数量
        sohuContentMainMapper.setPraiseCount(bo.getBusyCode(), BusyType.Question.name(), count);
        // 增加点赞
        if (bo.getIsAdd() != null && bo.getIsAdd()) {
            SohuQuestionVo questionVo = baseMapper.selectVoById(bo.getBusyCode());
            bo.setSourceUser(questionVo.getUserId());
            bo.setOperateUser(bo.getOperateUser());
            bo.setBusyCoverImage(questionVo.getCoverImage());
            bo.setBusyUser(questionVo.getUserId());
            SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildBusy(bo, questionVo.getUserId(), NoticeInteractEnum.like.name());
            interactNoticeReceive.setContent(String.format(NoticeInteractEnum.likeContent, "问题"));
            sohuInteractNoticeService.insertByBo(interactNoticeReceive);
        }
        // 删除问题的拓展数据缓存
        sohuQoraInfoService.evictObj(bo.getBusyCode(), bo.getBusyType().name());
        return sohuQoraInfoMapper.updateById(info) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean collect(SohuBusyBO bo) {
        SohuQoraInfo info = sohuQoraInfoService.queryEntityObj(bo.getBusyCode(), bo.getBusyType().name());
        if (Objects.isNull(info) || info.getId() == null) {
            return false;
        }
        log.info("收藏问题：{}", JSONUtil.toJsonStr(bo));
        Integer oldCount = info.getCollectCount();
        int count = bo.getIsAdd() ? oldCount + 1 : Math.max((oldCount - 1), 0);
        info.setCollectCount(count);

        bo.setTopCode(info.getQoraId());
        bo.setTopType(BusyType.Question.name());

        boolean result = sohuQoraInfoMapper.updateById(info) > 0;
        // 更新万能表的收藏数量
        sohuContentMainMapper.setCollectCount(bo.getBusyCode(), BusyType.Question.name(), count);

        if (bo.getIsAdd() != null && bo.getIsAdd()) {
            SohuQuestionVo questionVo = baseMapper.selectVoById(bo.getBusyCode());

            bo.setSourceUser(questionVo.getUserId());
            bo.setOperateUser(bo.getOperateUser());
            bo.setBusyCoverImage(questionVo.getCoverImage());
            bo.setBusyUser(questionVo.getUserId());
            SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildBusy(bo, questionVo.getUserId(), NoticeInteractEnum.collect.name());
            interactNoticeReceive.setContent(String.format(NoticeInteractEnum.collectContent, "问题"));
            sohuInteractNoticeService.insertByBo(interactNoticeReceive);
        }

        // 删除问题的拓展数据缓存
        sohuQoraInfoService.evictObj(bo.getBusyCode(), bo.getBusyType().name());
        return result;
    }

    @Override
    public Long queryQuestionNumBySite(Long siteId, Long userId) {
        LambdaQueryWrapper<SohuQuestion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuQuestion::getUserId, userId);
        wrapper.eq(SohuQuestion::getSiteId, siteId);
        wrapper.notIn(SohuQuestion::getState, CommonState.Delete.name());
        return baseMapper.selectCount(wrapper);
    }

    @Override
    public void sendQuestionInteractNotice(Long answerId) {
        SohuQuestionAnswer sohuQuestionAnswer = sohuQuestionAnswerMapper.selectById(answerId);
        SohuQuestion sohuQuestion = baseMapper.selectById(sohuQuestionAnswer.getQuestionId());
        SohuBusyBO bo = new SohuBusyBO();
        bo.setBusyCode(sohuQuestionAnswer.getQuestionId());
        bo.setBusyType(BusyType.Question);
        bo.setTopCode(sohuQuestionAnswer.getQuestionId());
        bo.setTopType(BusyType.Question.getType());
        bo.setSourceType(BusyType.Answer.getType());
        bo.setSourceId(sohuQuestionAnswer.getId());
        bo.setSourceUser(sohuQuestion.getUserId());
        bo.setOperateUser(sohuQuestionAnswer.getUserId());
        bo.setBusyCoverImage(sohuQuestion.getCoverImage());
        bo.setBusyUser(sohuQuestion.getUserId());
        bo.setSourceId(sohuQuestionAnswer.getId());
        // 作者收到消息
        SohuInteractNoticeBo interactNoticeReceive = SohuInteractNoticeBo.buildBusy(bo, sohuQuestion.getUserId(), NoticeInteractEnum.questionReceive.name());
        String content = FormalTool.getPlainText(sohuQuestionAnswer.getContent());
        interactNoticeReceive.setContent(String.format(NoticeInteractEnum.questionContent, content));
        sohuInteractNoticeService.insertByBo(interactNoticeReceive);
    }

    /**
     * 根据标签查排序前五的问题
     */
    private List<SohuQuestionVo> hotList(Long labelId) {
        LambdaQueryWrapper<SohuQuestion> lqw = Wrappers.lambdaQuery();
        if (labelId != null && labelId > 0L) {
            lqw.eq(SohuQuestion::getLessonLabelId, labelId);
        }
        lqw.eq(SohuQuestion::getState, CommonState.OnShelf.getCode());
        lqw.orderByAsc(SohuQuestion::getSortIndex);
        lqw.orderByDesc(SohuQuestion::getCreateTime);
        lqw.last("limit 5");
        List<SohuQuestionVo> sohuQuestionVos = baseMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(sohuQuestionVos)) {
            Set<Long> userIds = sohuQuestionVos.stream().filter(p -> Objects.nonNull(p.getUserId())).map(p -> p.getUserId()).collect(Collectors.toSet());
            Map<Long, LoginUser> userMap = this.sohuUserService.selectMap(userIds);
            for (SohuQuestionVo questionVo : sohuQuestionVos) {
                Long userId = LoginHelper.getUserId();
                // 总回答数
                questionVo.setAnswerCount(sohuQuestionAnswerMapper.answerCount(questionVo.getId(), userId));
                // 总点赞数
                Long price = sohuQuestionAnswerMapper.countPrice(questionVo.getId(), userId);
                //初始学习人数
                questionVo.setLearnNum(sohuQoraInfoMapper.countLearnCount(questionVo.getId()));
                questionVo.setTotalPrise(price != null ? price : 0);
                LoginUser loginUser = userMap.get(questionVo.getUserId());
                if (Objects.nonNull(loginUser)) {
                    questionVo.setUserAvatar(loginUser.getAvatar());
                    questionVo.setNickName(loginUser.getNickname());
                }
            }
        }
        return sohuQuestionVos;
    }


    /**
     * 构造问题其他信息
     */
    public void getRecord(List<SohuQuestionVo> records, Long userId) {
        records.forEach(item -> {
            SohuQuestionAnswerVo questionAnswerVo = sohuQuestionAnswerMapper.answerTop(item.getId(), userId);
            // 首条评论
            item.setQuestionAnswerVo(questionAnswerVo);
            // 总回答数
            item.setAnswerCount(sohuQuestionAnswerMapper.answerCount(item.getId(), userId));
            // 总点赞数
            Long price = sohuQuestionAnswerMapper.countPrice(item.getId(), userId);
            // 总浏览数
            item.setViewCount(RedisUtils.getAtomicValue(CacheConstants.QUESTION_VIEW + item.getId()));
            item.setTotalPrise(price != null ? price : 0);
            item.setRelation(this.buildRelate(item.getId()));
            item.setLearnNum(sohuQoraInfoMapper.countLearnCount(item.getId()));
            item.setContentText(FormalTool.getPlainText(item.getContent()));
        });
    }

    /**
     * 根据id获取内容关联项
     *
     * @param questionId 问题主键ID
     * @return {@link RelationRespVo}
     */
    private RelationRespVo buildRelate(Long questionId) {
        RelationRespVo relation = new RelationRespVo();
        SohuQoraRelate relate = sohuQoraRelateService.getByQora(questionId, BusyType.Question.name());
        if (Objects.isNull(relate)) {
            return null;
        }
        if (StrUtil.equalsAnyIgnoreCase(relate.getBusyType(), BusyType.GoodsWindow.name(), BusyType.Window.name(),
                BusyType.BusyTask.name(), BusyType.SohuLesson.name(), BusyType.EntryRole.name())) {
            buildRelateInfo(relation, relate);
        } else if (StrUtil.equalsAnyIgnoreCase(relate.getBusyType(), BusyType.Video.name())) {
            SohuVideoVo videoVo = sohuVideoMapper.selectVoById(relate.getBusyCode());
            if (Objects.isNull(videoVo)) {
                return relation;
            }
            relation.setBusyCode(relate.getBusyCode());
            relation.setBusyType(BusyType.Video.name());
            relation.setBusyTitle(videoVo.getTitle());
            relation.setBusyBelonger(videoVo.getUserId());
            relation.setBusyCoverImg(videoVo.getCoverImage());
            relation.setBusyPublishTime(DateUtil.format(videoVo.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
            buildBelonged(relation, videoVo.getUserId());
        }
        return relation;
    }


    private void buildRelateInfo(RelationRespVo relation, SohuQoraRelate relate) {
        relation.setBusyCode(relate.getBusyCode());
        relation.setBusyType(relate.getBusyType());
        relation.setBusyTitle(relate.getBusyTitle());
        relation.setBusyInfo(relate.getBusyInfo());
        relation.setChildTaskNumber(relate.getBusyInfo());
    }

    /**
     * 设置用户头像，名称
     */
    public void buildBelonged(RelationRespVo relation, Long userId) {
        LoginUser loginUser = sohuUserService.selectById(userId);
        if (Objects.nonNull(loginUser)) {
            relation.setBelongerAvatar(loginUser.getAvatar());
            relation.setBelongerName(loginUser.getUsername());
        }
    }

    @Override
    public void updateViewCountJobHandler() {
        Set<String> questionIdKeys = RedisUtils.getCacheSet(CacheConstants.QUESTION_VIEW + "*");
        if (CollUtil.isEmpty(questionIdKeys)) {
            return;
        }

        // 更新数据库浏览量
        for (String questionIdKey : questionIdKeys) {
            Long questionId = Long.parseLong(questionIdKey.replace(CacheConstants.QUESTION_VIEW, ""));
            Long viewCount = RedisUtils.getAtomicValue(questionIdKey);
            baseMapper.updateViewCount(questionId, viewCount);
        }
    }

    @Override
    public Boolean updateBatchContentState(SohuContentBatchBo bo) {
        // 获取需要修改的主键id
        List<String> idList = StrUtil.split(bo.getIds(), COMMA);

        List<SohuQuestion> sohuQuestionList = baseMapper.selectBatchIds(idList);
        // 如果查询结果为空，或者数量与传入的 id 数量不一致，返回失败
        if (sohuQuestionList == null || sohuQuestionList.size() != idList.size()) {
            return Boolean.FALSE;
        }
        // 校验所有作品的作者 ID 是否一致
        Long currentUserId = LoginHelper.getUserId();
        boolean isAllAuthorMatched = sohuQuestionList.stream()
                .allMatch(article -> article.getUserId().equals(currentUserId));

        if (!isAllAuthorMatched) {
            return Boolean.FALSE;
        }
        // 构建需要更新的内容
        List<SohuQuestion> contentList = sohuQuestionList.stream()
                .map(article -> {
                    SohuQuestion content = new SohuQuestion();
                    content.setId(article.getId());
                    content.setState(bo.getState());
                    return content;
                })
                .collect(Collectors.toList());

        // 执行批量更新
        return baseMapper.updateBatchById(contentList);
    }

//    @Override
//    public Boolean submitAudit(Long id) {
//        SohuQuestion entity = this.baseMapper.selectById(id);
//        if (Objects.isNull(entity)) {
//            throw new RuntimeException("数据不存在");
//        }
//        if (!Objects.equals(LoginHelper.getUserId(), entity.getUserId())) {
//            throw new RuntimeException("非法操作,这不是您的数据");
//        }
//        if (!(Objects.equals(entity.getState(), CommonState.Delete.name())
//                || Objects.equals(entity.getState(), CommonState.ForceDelete.name()))) {
//            throw new RuntimeException("非审核拒绝状态，不支持申诉");
//        }
//        SohuQuestion updateEntity = new SohuQuestion();
//        updateEntity.setId(id);
//        LambdaUpdateWrapper<SohuQuestion> luw = new LambdaUpdateWrapper<>();
//        luw.eq(SohuQuestion::getState, entity.getState());
//        luw.eq(SohuQuestion::getId, id);
//        SohuContentLifecycleVo lifecycleVo = this.sohuContentLifecycleService.selectOfLast(id, BusyType.Question.getType());
//        if (Objects.isNull(lifecycleVo)) {
//            //兼容历史数据
//            updateEntity.setState(CommonState.OffShelf.name());
//        } else {
//            updateEntity.setState(lifecycleVo.getLastState());
//        }
//        return this.baseMapper.update(updateEntity, luw) > 0;
//    }

    @Override
    public boolean updateOffShelfById(Long id) {
        SohuQuestion sohuQuestion = baseMapper.selectById(id);
        if (Objects.isNull(sohuQuestion)) {
            return false;
        }
        if (!Objects.equals(LoginHelper.getUserId(), sohuQuestion.getUserId())) {
            throw new RuntimeException("非法操作，这不是您的数据");
        }
        sohuQuestion.setState(CommonState.OffShelf.name());
        baseMapper.updateById(sohuQuestion);
        iSohuAirecContentItemService.updateStatusToOffShelf(id.toString(), AliyunAirecContentItemTypeEnum.RECIPE.getCode());
        // 同步素材库
        syncIndependentMaterial(sohuQuestion);
        return true;
    }

    /**
     * 同步分销素材库
     *
     * @param sohuQuestion
     */
    private void syncIndependentMaterial(SohuQuestion sohuQuestion) {
        SohuQoraRelate relate = sohuQoraRelateService.getByQora(sohuQuestion.getId(), BusyType.Question.name());
        if (Objects.isNull(relate) || StrUtil.equalsAnyIgnoreCase(relate.getBusyType(), BusyType.BusyTask.getType())) {
            return;
        }
        SohuBusyTaskSiteVo taskSiteVo = remoteBusyTaskSiteService.queryByTaskNumber(relate.getBusyInfo());
        if (Objects.isNull(taskSiteVo)) {
            return;
        }
        remoteMiddleIndependentMaterialService.deleteByCodeAndType(taskSiteVo.getMasterTaskNumber(), taskSiteVo.getConstMark());
    }

    @Override
    public boolean updateCompelOffById(SohuContentRefuseBo bo) {
//        if (!LoginHelper.anyMatchRole(RoleCodeEnum.ADMIN.getCode(), RoleCodeEnum.CALL_ADMIN.getCode(), RoleCodeEnum.OPERATION_ADMIN.getCode())) {
//            throw new RuntimeException("非法操作，您无权操作");
//        }
        SohuQuestion sohuQuestion = baseMapper.selectById(bo.getId());
        if (Objects.isNull(sohuQuestion)) {
            return false;
        }
        sohuQuestion.setState(CommonState.CompelOff.name());
        sohuQuestion.setRejectReason(bo.getReason());
        baseMapper.updateById(sohuQuestion);
        iSohuAirecContentItemService.updateStatusToOffShelf(bo.getId().toString(), AliyunAirecContentItemTypeEnum.RECIPE.getCode());
        // 同步素材库
        syncIndependentMaterial(sohuQuestion);
        CompletableFuture.runAsync(() -> sendMsgOfCompelOff(sohuQuestion), asyncConfig.getAsyncExecutor());
        return true;
    }

    /**
     * 发送系统消息通知-强制下架
     */
    private void sendMsgOfCompelOff(SohuQuestion sohuQuestion) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.CONTENT_COMPEL_OFF_TITLE);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.questionCompelOff.name());
        content.setDetailId(sohuQuestion.getId());
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setId(sohuQuestion.getId());
        detail.setDesc(String.format(SystemNoticeEnum.CONTENT_COMPEL_OFF_DESC, sohuQuestion.getTitle(), sohuQuestion.getRejectReason()));
        detail.setStatus(CommonState.CompelOff.name());
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(sohuQuestion.getUserId(), SystemNoticeEnum.CONTENT_COMPEL_OFF_TITLE, contentJson, SystemNoticeEnum.Type.contentCompelOff);
    }

    @Override
    public Boolean recoveryData(Long id) {
        SohuQuestion entity = this.baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new RuntimeException("数据不存在");
        }
        if (!Objects.equals(LoginHelper.getUserId(), entity.getUserId())) {
            throw new RuntimeException("非法操作,这不是您的数据");
        }
        if (!(Objects.equals(entity.getState(), CommonState.Delete.name())
                || Objects.equals(entity.getState(), CommonState.ForceDelete.name()))) {
            throw new RuntimeException("非审核拒绝状态，不支持申诉");
        }
        SohuQuestion updateEntity = new SohuQuestion();
        updateEntity.setId(id);
        LambdaUpdateWrapper<SohuQuestion> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuQuestion::getState, entity.getState());
        luw.eq(SohuQuestion::getId, id);
        SohuContentLifecycleVo lifecycleVo = this.sohuContentLifecycleService.selectOfLast(id, BusyType.Question.name());
        if (Objects.isNull(lifecycleVo)) {
            //兼容历史数据
            updateEntity.setState(CommonState.OffShelf.name());
        } else {
            updateEntity.setState(lifecycleVo.getLastState());
        }
        return this.baseMapper.update(updateEntity, luw) > 0;
    }

    @Override
    public Boolean auditOnShelf(Long id) {
        SohuQuestion question = baseMapper.selectById(id);
        if (Objects.isNull(question)) {
            throw new RuntimeException("内容不存在");
        }
        if (!(Objects.equals(question.getState(), CommonState.WaitApprove.name())
                || Objects.equals(question.getState(), CommonState.Refuse.name()))) {
            throw new RuntimeException("此状态不支持上架，请检查数据");
        }
        question.setState(CommonState.OnShelf.name());
        question.setAuditState(CommonState.Pass.name());
        question.setAuditTime(new Date());
        question.setAppealStatus(false);
        question.setAppealReason(null);
        LambdaUpdateWrapper<SohuQuestion> luw = new LambdaUpdateWrapper<>();
        luw.set(SohuQuestion::getAppealReason, null);
        luw.set(SohuQuestion::getRejectReason, null);
        luw.eq(SohuQuestion::getId, id);
        this.baseMapper.update(question, luw);
        // 更新万能表
        sohuContentMainMapper.updateState(question.getState(), id, BusyType.Question.name());
        // 智能推送表
        updateSohuAirecContentArticleItem(question);
        return true;
    }

    @Override
    public Boolean auditRefuse(Long id, String rejectReason) {
        SohuQuestion question = baseMapper.selectById(id);
        if (Objects.isNull(question)) {
            throw new RuntimeException("内容不存在");
        }
        if (!(Objects.equals(question.getState(), CommonState.WaitApprove.name())
                || Objects.equals(question.getState(), CommonState.Refuse.name()))) {
            throw new RuntimeException("此状态不支持上架，请检查数据");
        }
        question.setState(CommonState.Refuse.name());
        question.setAuditState(CommonState.Refuse.name());
        question.setAuditTime(new Date());
        question.setRejectReason(rejectReason);
//        question.setSubmitNum(question.getSubmitNum() + 1);
        this.baseMapper.updateById(question);
        // 更新万能表
        sohuContentMainMapper.updateState(question.getState(), id, BusyType.Question.name());
        // 智能推送表
        updateSohuAirecContentArticleItem(question);
        return true;
    }

    @Override
    public Boolean userAppeal(SohuUserContentAppealBo bo) {
        SohuQuestion question = baseMapper.selectById(bo.getId());
        if (Objects.isNull(question)) {
            throw new RuntimeException("内容不存在");
        }
        if (!Objects.equals(question.getState(), CommonState.Refuse.name())) {
            throw new RuntimeException("非审核拒绝状态，不支持申诉");
        }
        if (BooleanUtil.isTrue(question.getAppealStatus())) {
            throw new RuntimeException("您已申诉过，只有一次申诉机会");
        }
        SohuQuestion updateEntity = new SohuQuestion();
        //updateEntity.setState(CommonState.WaitApprove.name());
        updateEntity.setAppealStatus(true);
        updateEntity.setAppealReason(bo.getAppealReason());
        updateEntity.setAuditState(CommonState.WaitApprove.name());
        updateEntity.setSubmitScene("申诉复审");
        LambdaUpdateWrapper<SohuQuestion> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuQuestion::getId, bo.getId());
        luw.eq(SohuQuestion::getState, CommonState.Refuse.name());
        this.baseMapper.update(updateEntity, luw);
        //初始化审核
        question.setAppealStatus(updateEntity.getAppealStatus());
        question.setAppealReason(updateEntity.getAppealReason());
        question.setSubmitScene(updateEntity.getSubmitScene());
        question.setState(CommonState.WaitApprove.name());
        this.initCreateAudited(question);
        return true;
    }

    @Override
    public Boolean hideDataBatch(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        for (Long id : ids) {
            this.hideData(id);
        }
        return true;
    }

    @Override
    public Boolean hideData(Long id) {
        SohuQuestion entity = this.baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new RuntimeException("数据不存在");
        }
        if (!Objects.equals(LoginHelper.getUserId(), entity.getUserId())) {
            throw new RuntimeException("非法操作,这不是您的数据");
        }
        if (!Objects.equals(entity.getState(), CommonState.OnShelf.name())) {
            throw new RuntimeException("未上架，不支持隐藏");
        }
        SohuQuestion updateEntity = new SohuQuestion();
        updateEntity.setId(id);
        updateEntity.setState(CommonState.Hide.name());
        LambdaUpdateWrapper<SohuQuestion> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuQuestion::getState, entity.getState());
        luw.eq(SohuQuestion::getId, id);
        if (this.baseMapper.update(updateEntity, luw) > 0) {
            this.iSohuAirecContentItemService.updateStatusToOffShelf(id.toString(), AliyunAirecContentItemTypeEnum.RECIPE.getCode());
        }
        return true;
    }

    /**
     * 问答添加至智能推荐物料
     *
     * @param question 当前用户id
     */
    public void addSohuAirecContentItem(SohuQuestion question, SohuQuestionBo bo) {
        //上架
        if (StringUtils.equalsAnyIgnoreCase(CommonState.OnShelf.getCode(), question.getState())) {
            SohuAirecContentItemBo model = buildAirecContentItemModel(question, StrUtil.isNotBlank(bo.getBusyType()));
            model.setTags(iSohuAirecTagRelationService.saveTagStr(question.getId(), AiRecTag.BizTypeEnum.QUESTION.getCode(), model.getTags()));
            iSohuAirecContentItemService.saveAirecContentItem(model);
        }
    }

    @Override
    public void updateSohuAirecContentArticleItem(SohuQuestion question) {
//        if (!Objects.equals(VisibleTypeEnum.open.getCode(), article.getVisibleType())) {
//            return;
//        }
        //上架
        if (StringUtils.equalsAnyIgnoreCase(CommonState.OnShelf.getCode(), question.getState())) {
            // 关联项
            SohuQoraRelate relate = sohuQoraRelateMapper.selectOne(SohuQoraRelate::getQoraId, question.getId());
            SohuAirecContentItemBo model = buildAirecContentItemModel(question, Objects.nonNull(relate));
            model.setTags(iSohuAirecTagRelationService.saveTagStr(question.getId(), AiRecTag.BizTypeEnum.QUESTION.getCode(), model.getTags()));
            iSohuAirecContentItemService.saveAirecContentItem(model);
        } else {
            //下架
            iSohuAirecContentItemService.updateStatusToOffShelf(question.getId().toString(), AliyunAirecContentItemTypeEnum.RECIPE.getCode());
        }

    }

    @Override
    public Boolean logicDeleteById(Long id) {
        SohuQuestion sohuQuestion = baseMapper.selectById(id);
        if (Objects.isNull(sohuQuestion)) {
            return false;
        }
        SohuContentLifecycleBo lifecycleBo = new SohuContentLifecycleBo(id, BusyType.Question.name(), sohuQuestion.getState(), CommonState.Delete.name(), "自主删除");
        if (!Objects.equals(LoginHelper.getUserId(), sohuQuestion.getUserId())) {
            throw new RuntimeException("非法操作，这不是您的数据");
        }
        sohuQuestion.setState(CommonState.Delete.name());
        sohuQuestion.setDelTime(DateUtils.getNowDate());
        baseMapper.updateById(sohuQuestion);
        this.createLifecycleLog(lifecycleBo);
        iSohuAirecContentItemService.updateStatusToOffShelf(id.toString(), AliyunAirecContentItemTypeEnum.RECIPE.getCode());
        return true;
    }

    @Override
    public boolean logicDeleteById(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        for (Long id : ids) {
            this.logicDeleteById(id);
        }
        return true;
    }

    @Override
    public boolean logicForceDeleteById(Long id) {
//        if (!LoginHelper.anyMatchRole(RoleCodeEnum.ADMIN.getCode(), RoleCodeEnum.CALL_ADMIN.getCode(), RoleCodeEnum.OPERATION_ADMIN.getCode())) {
//            throw new RuntimeException("非法操作，您无权操作");
//        }
        SohuQuestion sohuQuestion = baseMapper.selectById(id);
        if (Objects.isNull(sohuQuestion)) {
            return false;
        }
        SohuContentLifecycleBo lifecycleBo = new SohuContentLifecycleBo(id, BusyType.Question.name(), sohuQuestion.getState(), CommonState.ForceDelete.name(), "强制删除");
        sohuQuestion.setState(CommonState.ForceDelete.name());
        sohuQuestion.setDelTime(DateUtils.getNowDate());
        baseMapper.updateById(sohuQuestion);
        this.createLifecycleLog(lifecycleBo);
        iSohuAirecContentItemService.updateStatusToOffShelf(id.toString(), AliyunAirecContentItemTypeEnum.RECIPE.getCode());
        return true;
    }

    @Override
    public boolean logicForceDeleteById(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        for (Long id : ids) {
            this.logicForceDeleteById(id);
        }
        return true;
    }

    /**
     * 记录生命周期
     *
     * @param lifecycleBo
     * @return
     */
    private Boolean createLifecycleLog(SohuContentLifecycleBo lifecycleBo) {
        if (Objects.equals(lifecycleBo.getLastState(), CommonState.OnShelf.name())
                || Objects.equals(lifecycleBo.getLastState(), CommonState.WaitApprove.name())) {
            if (Objects.equals(lifecycleBo.getCurrentState(), CommonState.ForceDelete.name())) {
                lifecycleBo.setCurrentState(CommonState.CompelOff.name());
                this.sohuContentLifecycleService.insertByBo(lifecycleBo);
                lifecycleBo.setId(null);
                lifecycleBo.setLastState(CommonState.CompelOff.name());
                lifecycleBo.setCurrentState(CommonState.ForceDelete.name());
            } else if (Objects.equals(lifecycleBo.getCurrentState(), CommonState.Delete.name())) {
                lifecycleBo.setCurrentState(CommonState.OffShelf.name());
                this.sohuContentLifecycleService.insertByBo(lifecycleBo);
                lifecycleBo.setId(null);
                lifecycleBo.setLastState(CommonState.OffShelf.name());
                lifecycleBo.setCurrentState(CommonState.Delete.name());
            }
        }
        this.sohuContentLifecycleService.insertByBo(lifecycleBo);
        return true;
    }

    @Override
    public Boolean deleteDataById(Long id) {
        SohuQuestion sohuQuestion = baseMapper.selectById(id);
        if (Objects.isNull(sohuQuestion)) {
            return false;
        }
        if (!Objects.equals(LoginHelper.getUserId(), sohuQuestion.getUserId())) {
            throw new RuntimeException("非法操作，这不是您的数据");
        }

        return sohuContentMainMapper.flushRecycleQuestion(LoginHelper.getUserId(), Collections.singleton(id));
    }

    @Override
    public Boolean clearRecycleDataOfTimeOut() {
        LambdaUpdateWrapper<SohuQuestion> luw = new LambdaUpdateWrapper<>();
        luw.set(SohuQuestion::getDelFlag, Constants.TWO);
        luw.in(SohuQuestion::getState, CommonState.Delete.name(), CommonState.ForceDelete.name());
        luw.lt(SohuQuestion::getDelTime, DateUtil.offsetDay(new Date(), -CON_RECYCLE_DATA_TIME_OUT.intValue()));
        this.baseMapper.update(new SohuQuestion(), luw);
        return true;
    }

    @Override
    public TableDataInfo<SohuQuestionVo> queryPageListOfOnShelf(SohuQuestionBo bo, PageQuery pageQuery) {
//        LambdaQueryWrapper<SohuQuestion> lqw = buildQueryWrapper(bo);
//        lqw.eq(bo.getId() != null, SohuQuestion::getId, bo.getId());
//        lqw.eq(SohuQuestion::getState, CommonState.OnShelf.name());
//        lqw.eq(SohuQuestion::getVisibleType, VisibleTypeEnum.open.getCode());
//        lqw.in(CollUtil.isNotEmpty(bo.getCategoryIds()), SohuQuestion::getCategoryId, bo.getCategoryIds());
//        lqw.like(StringUtils.isNotBlank(bo.getTitle()), SohuQuestion::getTitle, bo.getTitle());
//        lqw.orderByAsc(SohuQuestion::getSortIndex);
//        lqw.orderByDesc(SohuQuestion::getCreateTime);
//        Page<SohuQuestionVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        Page<SohuQuestionVo> result = baseMapper.queryPageListOfOnShelf(PageQueryUtils.build(pageQuery), bo);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            Set<Long> ossIds = new HashSet<>();
            Set<Long> userIds = new HashSet<>();
            Set<Long> categoryIds = new HashSet<>();
            for (SohuQuestionVo record : result.getRecords()) {
                if (cn.hutool.core.util.NumberUtil.isNumber(record.getCoverImage())) {
                    ossIds.add(Long.valueOf(record.getCoverImage()));
                }
                userIds.add(record.getUserId());
                categoryIds.add(record.getCategoryId());
            }
            Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
            Map<Long, SohuCategoryVo> sohuCategoryVoMap = sohuCategoryService.queryMap(categoryIds);
            for (SohuQuestionVo record : result.getRecords()) {
                // 设置用户头像以及昵称
                LoginUser loginUser = userMap.get(record.getUserId());
                if (Objects.nonNull(loginUser)) {
                    record.setNickName(loginUser.getNickname());
                    record.setUserAvatar(loginUser.getAvatar());
                } else {
                    record.setNickName(Constants.DEFAULT_USER_NICKNAME);
                    record.setUserAvatar(Constants.DEFAULT_AVATAR);
                }
                SohuCategoryVo sohuCategoryVo = sohuCategoryVoMap.get(record.getCategoryId());
                if (Objects.nonNull(sohuCategoryVo)) {
                    record.setCategoryName(sohuCategoryVo.getName());
                }
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public TableDataInfo<SohuQuestionVo> getTopicList(SohuTopicContentQueryBo bo, PageQuery pageQuery) {
        Page<SohuQuestionVo> sohuQuestionVoPage = baseMapper.getTopicList(bo, PageQueryUtils.build(pageQuery));
        if (CollUtil.isNotEmpty(sohuQuestionVoPage.getRecords())) {
            getRecord(sohuQuestionVoPage.getRecords(), LoginHelper.getUserId());
        }
        return TableDataInfoUtils.build(sohuQuestionVoPage);
    }

    @Override
    public void handleRobot(String busyCode, Boolean isPass, String reason) {
        SohuAuditBo auditBo = new SohuAuditBo();
        auditBo.setBusyCode(Long.valueOf(busyCode));
        auditBo.setBusyType(BusyType.Question.getType());
        List<SohuAuditVo> auditList = sohuAuditService.queryList(auditBo);
        if (CollectionUtils.isEmpty(auditList)) {
            return;
        }
        SohuAuditVo auditVo = auditList.stream()
                .filter(record -> record != null && CommonState.WaitApprove.getCode().equals(record.getSysAuditState()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(auditVo)) {
            return;
        }
        SohuAuditBo newAuditBo = new SohuAuditBo();
        newAuditBo.setId(auditVo.getId());
        newAuditBo.setBusyType(BusyType.Question.getType());
        newAuditBo.setState(isPass ? CommonState.OnShelf.getCode() : CommonState.Refuse.getCode());
        newAuditBo.setRejectReason(reason);
        sohuAuditService.process(newAuditBo);
    }
}
