package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.PayStatus;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuPlayletPayBo;
import com.sohu.middle.api.vo.SohuPlayletPayVo;
import com.sohu.middle.api.vo.SohuPlayletVo;
import com.sohu.middle.api.vo.SohuVideoVo;
import com.sohu.middleService.domain.SohuPlaylet;
import com.sohu.middleService.domain.SohuPlayletPay;
import com.sohu.middleService.mapper.SohuPlayletMapper;
import com.sohu.middleService.mapper.SohuPlayletPayMapper;
import com.sohu.middleService.service.ISohuPlayletPayService;
import com.sohu.middleService.service.ISohuPlayletService;
import com.sohu.middleService.service.ISohuVideoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 短剧付费单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuPlayletPayServiceImpl implements ISohuPlayletPayService {

    private final SohuPlayletPayMapper baseMapper;
    private final SohuPlayletMapper sohuPlayletMapper;
    private final ISohuVideoService iSohuVideoService;
    private final ISohuPlayletService iSohuPlayletService;

    /**
     * 查询短剧付费单
     */
    @Override
    public SohuPlayletPayVo queryById(Long id) {
        SohuPlayletPayVo sohuPlayletPayVo = baseMapper.selectVoById(id);
        if (Objects.isNull(sohuPlayletPayVo)) {
            return new SohuPlayletPayVo();
        }
        if (StrUtil.equals(sohuPlayletPayVo.getBusyType(), BusyType.Video.name())) {
            SohuVideoVo sohuVideoVo = iSohuVideoService.selectVoById(sohuPlayletPayVo.getBusyCode());
            if (Objects.nonNull(sohuVideoVo)) {
                SohuPlayletVo sohuPlayletVo = iSohuPlayletService.queryByEpisodeRelevance(sohuVideoVo.getEpisodeRelevance());
                sohuPlayletPayVo.setCategoryName(sohuVideoVo.getCategoryName());
                sohuPlayletPayVo.setPlayletCoverImage(sohuVideoVo.getCoverUrl());
                sohuPlayletPayVo.setPlayletIntro(sohuPlayletVo.getIntro());
                sohuPlayletPayVo.setVideoUrl(sohuVideoVo.getVideoUrl());
                sohuPlayletPayVo.setPermitImage(sohuPlayletVo.getPermitImage());
                sohuPlayletPayVo.setVideoNumber(sohuVideoVo.getEpisodeNumber());
            }
            return sohuPlayletPayVo;
        }
        SohuPlayletVo sohuPlayletVo = iSohuPlayletService.queryById(sohuPlayletPayVo.getBusyCode());
        if (Objects.nonNull(sohuPlayletVo)) {
            sohuPlayletPayVo.setCategoryName(sohuPlayletVo.getCategoryName());
            sohuPlayletPayVo.setPlayletCoverImage(sohuPlayletVo.getCoverImage());
            sohuPlayletPayVo.setPlayletTitle(sohuPlayletVo.getTitle());
            sohuPlayletPayVo.setPlayletIntro(sohuPlayletVo.getIntro());
            sohuPlayletPayVo.setPermitImage(sohuPlayletVo.getPermitImage());
            sohuPlayletPayVo.setSohuVideoVos(sohuPlayletVo.getSohuVideoVos());
        }
        return sohuPlayletPayVo;
    }

    /**
     * 查询短剧付费单列表
     */
    @Override
    public TableDataInfo<SohuPlayletPayVo> queryPageList(SohuPlayletPayBo bo, PageQuery pageQuery) {
//        LambdaQueryWrapper<SohuPlayletPay> lqw = buildQueryWrapper(bo);
        Page<SohuPlayletPayVo> result = baseMapper.queryPlayletOrderList(PageQueryUtils.build(pageQuery), bo);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询短剧付费单列表
     */
    @Override
    public List<SohuPlayletPayVo> queryList(SohuPlayletPayBo bo) {
        LambdaQueryWrapper<SohuPlayletPay> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuPlayletPay> buildQueryWrapper(SohuPlayletPayBo bo) {
        LambdaQueryWrapper<SohuPlayletPay> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuPlayletPay::getUserId, bo.getUserId());
        lqw.eq(bo.getBusyCode() != null, SohuPlayletPay::getBusyCode, bo.getBusyCode());
        lqw.eq(StringUtils.isNotBlank(bo.getBusyType()), SohuPlayletPay::getBusyType, bo.getBusyType());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionId()), SohuPlayletPay::getTransactionId, bo.getTransactionId());
        lqw.eq(StringUtils.isNotBlank(bo.getPayNumber()), SohuPlayletPay::getPayNumber, bo.getPayNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getPayType()), SohuPlayletPay::getPayType, bo.getPayType());
        lqw.eq(StringUtils.isNotBlank(bo.getPayStatus()), SohuPlayletPay::getPayStatus, bo.getPayStatus());
        lqw.eq(bo.getPayAmount() != null, SohuPlayletPay::getPayAmount, bo.getPayAmount());
        lqw.eq(bo.getRefundAmount() != null, SohuPlayletPay::getRefundAmount, bo.getRefundAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getRefundId()), SohuPlayletPay::getRefundId, bo.getRefundId());
        return lqw;
    }

    /**
     * 新增短剧付费单
     */
    @Override
    public Boolean insertByBo(SohuPlayletPayBo bo) {
        SohuPlayletPay add = BeanUtil.toBean(bo, SohuPlayletPay.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改短剧付费单
     */
    @Override
    public Boolean updateByBo(SohuPlayletPayBo bo) {
        SohuPlayletPay update = BeanUtil.toBean(bo, SohuPlayletPay.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuPlayletPay entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除短剧付费单
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean saveVideoPay(Long userId, Long videoId, BigDecimal amount, String payNumber, String payType, String payStatus) {
        LambdaQueryWrapper<SohuPlayletPay> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlayletPay::getUserId, userId).eq(SohuPlayletPay::getBusyCode, videoId).eq(SohuPlayletPay::getBusyType, BusyType.Video.name());
        SohuPlayletPay playletPay = this.baseMapper.selectOne(lqw);
        if (Objects.nonNull(playletPay) && StrUtil.equalsAnyIgnoreCase(playletPay.getPayStatus(), PayStatus.Paid.name())) {
            return Boolean.TRUE;
        }
        if (Objects.nonNull(playletPay)) {
            playletPay.setPayNumber(payNumber);
            playletPay.setPayStatus(PayStatus.WaitPay.name());
            playletPay.setUpdateTime(new Date());
            this.baseMapper.updateById(playletPay);
            return Boolean.TRUE;
        }
        playletPay = new SohuPlayletPay();
        playletPay.setUserId(userId);
        playletPay.setBusyCode(videoId);
        playletPay.setBusyType(BusyType.Video.name());
        playletPay.setPayStatus(StrUtil.isBlankIfStr(payNumber) ? PayStatus.WaitPay.name() : payStatus);
        playletPay.setPayNumber(payNumber);
        playletPay.setPayType(payType);
        playletPay.setPayAmount(amount);
        playletPay.setCreateTime(new Date());
        this.baseMapper.insert(playletPay);
        return Boolean.TRUE;
    }

    @Override
    public Boolean saveVideoPay(Long userId, Long videoId, BigDecimal amount, String payNumber, String payType, String payStatus, Long playletCategoryId, String playletName) {
        LambdaQueryWrapper<SohuPlayletPay> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlayletPay::getUserId, userId).eq(SohuPlayletPay::getBusyCode, videoId).eq(SohuPlayletPay::getBusyType, BusyType.Video.name());
        SohuPlayletPay playletPay = this.baseMapper.selectOne(lqw);
        if (Objects.nonNull(playletPay) && StrUtil.equalsAnyIgnoreCase(playletPay.getPayStatus(), PayStatus.Paid.name())) {
            return Boolean.TRUE;
        }
        if (Objects.nonNull(playletPay)) {
            playletPay.setPayNumber(payNumber);
            playletPay.setPayStatus(PayStatus.WaitPay.name());
            playletPay.setUpdateTime(new Date());
            playletPay.setCategoryId(playletCategoryId);
            playletPay.setPlayletTitle(playletName);
            this.baseMapper.updateById(playletPay);
            return Boolean.TRUE;
        }
        playletPay = new SohuPlayletPay();
        playletPay.setUserId(userId);
        playletPay.setBusyCode(videoId);
        playletPay.setBusyType(BusyType.Video.name());
        playletPay.setPayStatus(StrUtil.isBlankIfStr(payNumber) ? PayStatus.WaitPay.name() : payStatus);
        playletPay.setPayNumber(payNumber);
        playletPay.setPayType(payType);
        playletPay.setPayAmount(amount);
        playletPay.setCreateTime(new Date());
        playletPay.setCategoryId(playletCategoryId);
        playletPay.setPlayletTitle(playletName);
        this.baseMapper.insert(playletPay);
        return Boolean.TRUE;
    }

    @Override
    public Boolean savePlayletPay(Long userId, Long playletId, Long count, BigDecimal amount, String payNumber, String payType) {
        LambdaQueryWrapper<SohuPlayletPay> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlayletPay::getUserId, userId).eq(SohuPlayletPay::getBusyCode, playletId)
                .eq(SohuPlayletPay::getBusyType, BusyType.Playlet.name());
        SohuPlayletPay playletPay = this.baseMapper.selectOne(lqw);
        if (Objects.nonNull(playletPay) && StrUtil.equalsAnyIgnoreCase(playletPay.getPayStatus(), PayStatus.Paid.name())) {
            return Boolean.TRUE;
        }
        if (Objects.nonNull(playletPay)) {
            playletPay.setBuyCount(count);
            playletPay.setPayNumber(payNumber);
            playletPay.setPayStatus(PayStatus.WaitPay.name());
            playletPay.setUpdateTime(new Date());
            this.baseMapper.updateById(playletPay);
            return Boolean.TRUE;
        }
        playletPay = new SohuPlayletPay();
        playletPay.setUserId(userId);
        playletPay.setBusyCode(playletId);
        playletPay.setBusyType(BusyType.Playlet.name());
        playletPay.setBuyCount(count);
        playletPay.setPayStatus(PayStatus.WaitPay.name());
        playletPay.setPayNumber(payNumber);
        playletPay.setPayType(payType);
        playletPay.setPayAmount(amount);
        playletPay.setCreateTime(new Date());
        this.baseMapper.insert(playletPay);
        return Boolean.TRUE;
    }

    @Override
    public Boolean savePlayletPay(Long userId, Long playletId, Long count, BigDecimal amount, String payNumber, String payType, Long playletCategoryId, String playletName) {
        LambdaQueryWrapper<SohuPlayletPay> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlayletPay::getUserId, userId).eq(SohuPlayletPay::getBusyCode, playletId)
                .eq(SohuPlayletPay::getBusyType, BusyType.Playlet.name());
        SohuPlayletPay playletPay = this.baseMapper.selectOne(lqw);
        if (Objects.nonNull(playletPay) && StrUtil.equalsAnyIgnoreCase(playletPay.getPayStatus(), PayStatus.Paid.name())) {
            return Boolean.TRUE;
        }
        if (Objects.nonNull(playletPay)) {
            playletPay.setBuyCount(count);
            playletPay.setPayNumber(payNumber);
            playletPay.setPayStatus(PayStatus.WaitPay.name());
            playletPay.setUpdateTime(new Date());
            playletPay.setCategoryId(playletCategoryId);
            playletPay.setPlayletTitle(playletName);
            this.baseMapper.updateById(playletPay);
            return Boolean.TRUE;
        }
        playletPay = new SohuPlayletPay();
        playletPay.setUserId(userId);
        playletPay.setBusyCode(playletId);
        playletPay.setBusyType(BusyType.Playlet.name());
        playletPay.setBuyCount(count);
        playletPay.setPayStatus(PayStatus.WaitPay.name());
        playletPay.setPayNumber(payNumber);
        playletPay.setPayType(payType);
        playletPay.setPayAmount(amount);
        playletPay.setCreateTime(new Date());
        playletPay.setCategoryId(playletCategoryId);
        playletPay.setPlayletTitle(playletName);
        this.baseMapper.insert(playletPay);
        return Boolean.TRUE;
    }

    @Override
    public Boolean queryPlayletPay(Long userId, String episodeRelevance) {
        SohuPlaylet sohuPlaylet = sohuPlayletMapper.selectOne(SohuPlaylet::getEpisodeRelevance, episodeRelevance);
        if (Objects.isNull(sohuPlaylet)) {
            return Boolean.FALSE;
        }
        LambdaQueryWrapper<SohuPlayletPay> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlayletPay::getUserId, userId).eq(SohuPlayletPay::getBusyCode, sohuPlaylet.getId())
                .eq(SohuPlayletPay::getBusyType, BusyType.Playlet.name())
                .eq(SohuPlayletPay::getPayStatus, PayStatus.Paid.name());
        return Objects.nonNull(this.baseMapper.selectOne(lqw));
    }

    @Override
    public Boolean queryVideoPay(Long userId, Long videoId) {
        LambdaQueryWrapper<SohuPlayletPay> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlayletPay::getUserId, userId).eq(SohuPlayletPay::getBusyCode, videoId)
                .eq(SohuPlayletPay::getBusyType, BusyType.Video.name())
                .eq(SohuPlayletPay::getPayStatus, PayStatus.Paid.name());
        return Objects.nonNull(this.baseMapper.selectOne(lqw));
    }

    @Override
    public Map<Long, Boolean> queryVideoListPay(Long userId, List<Long> videoIds) {
        LambdaQueryWrapper<SohuPlayletPay> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuPlayletPay::getUserId, userId).in(SohuPlayletPay::getBusyCode, videoIds)
                .eq(SohuPlayletPay::getBusyType, BusyType.Video.name())
                .eq(SohuPlayletPay::getPayStatus, PayStatus.Paid.name());
        List<SohuPlayletPay> pays = this.baseMapper.selectList(lqw);
        Map<Long, Boolean> map = new HashMap<>();
        if (CollUtil.isEmpty(pays)) {
            return map;
        }
        Map<Long, SohuPlayletPay> payMap = pays.stream().collect(Collectors.toMap(SohuPlayletPay::getBusyCode, u -> u));
        for (Long videoId : videoIds) {
            SohuPlayletPay playletPay = payMap.get(videoId);
            map.put(videoId, Objects.nonNull(playletPay));
        }
        return map;
    }

    @Override
    public Boolean paySuccess(String payNumber, String transactionId) {
        LambdaUpdateWrapper<SohuPlayletPay> lqw = new LambdaUpdateWrapper<>();
        lqw.set(SohuPlayletPay::getPayStatus, PayStatus.Paid.name())
                .set(SohuPlayletPay::getTransactionId, transactionId)
                .eq(SohuPlayletPay::getPayNumber, payNumber);
        log.info("更新短剧付费成功，payNumber:{}", payNumber);
        return this.baseMapper.update(new SohuPlayletPay(), lqw) > 0;
    }
}
