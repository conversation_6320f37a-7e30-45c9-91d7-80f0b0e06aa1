package com.sohu.middleService.service;


import com.sohu.middle.api.bo.SohuBindUserLabelBo;
import com.sohu.middle.api.bo.SohuUserLabelRelationBo;
import com.sohu.middle.api.vo.SohuUserLabelRelationVo;

import java.util.List;
import java.util.Map;

/**
 * 用户标签关联Service接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface ISohuUserLabelRelationService {

    /**
     * 根据标签id查询用户标签关联数量
     *
     * @param labelIds 标签id集合
     * @return Map<Long, Long>
     */
    Map<Long, Long> queryRelationUserCount(List<Long> labelIds);

    /**
     * 批量新增用户标签关联
     */
    void batchInsert(List<SohuUserLabelRelationBo> boList);

    /**
     * 批量新增用户标签关联
     */
//    void batchInsert(SohuSaveUserLabelRelationBo bo);

    /**
     * 批量更新用户标签关联
     * @param userId
     * @return
     */
    List<SohuUserLabelRelationVo> queryLabelsByUserId(Long userId);

    /**
     * 根据用户id删除用户标签关联
     *
     * @param userId    用户id
     * @param labelType
     */
    void deleteUserLabelByUserId(Long userId, String labelType);

    /**
     * 点击广告绑定用户标签
     */
    Boolean bindUserLabelWithClick(SohuBindUserLabelBo bo);
}
