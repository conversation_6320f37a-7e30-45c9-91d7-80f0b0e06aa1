package com.sohu.middleService.service;


import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuUseBo;
import com.sohu.middle.api.bo.SohuUserAgreeBo;
import com.sohu.middle.api.bo.SohuUserAgreeQueryBo;
import com.sohu.middle.api.vo.SohuUserAgreeInfoVo;
import com.sohu.middle.api.vo.SohuUserAgreeListVo;
import com.sohu.middle.api.vo.SohuUserAgreeVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户协议Service接口
 *
 * <AUTHOR>
 * @date 2024-09-21
 */
public interface ISohuUserAgreeService {

    /**
     * 查询用户协议
     */
    SohuUserAgreeVo queryById(Long id);

    /**
     * 查询用户协议列表
     */
    TableDataInfo<SohuUserAgreeListVo> queryPageList(SohuUserAgreeQueryBo bo, PageQuery pageQuery);

    /**
     * 查询用户协议列表
     */
    List<SohuUserAgreeVo> queryList(SohuUserAgreeQueryBo bo);

    /**
     * 新增用户协议
     */
    Boolean insertByBo(SohuUserAgreeBo bo);

    /**
     * 修改用户协议
     */
    Boolean updateByBo(SohuUserAgreeBo bo);

    /**
     * 校验并批量删除用户协议信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 用户协议启用/禁用
     */
    Boolean updateState(SohuUseBo bo);

    /**
     * 获取用户协议详细信息
     *
     * @param id
     * @return
     */
    SohuUserAgreeInfoVo getInfoById(Long id);
}
