package com.sohu.middleService.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuQoraRelateBo;
import com.sohu.middle.api.vo.SohuQoraRelateVo;
import com.sohu.middleService.domain.SohuQoraRelate;

import java.util.Collection;
import java.util.List;

/**
 * 问题关联Service接口
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
public interface ISohuQoraRelateService extends ISohuBaseService<SohuQoraRelate,SohuQoraRelateVo> {

//    /**
//     * 查询问题关联
//     */
//    SohuQoraRelateVo queryById(Long id);
//
    /**
     * 查询问题关联列表
     */
    TableDataInfo<SohuQoraRelateVo> queryPageList(SohuQoraRelateBo bo, PageQuery pageQuery);

    /**
     * 查询问题关联列表
     */
    List<SohuQoraRelateVo> queryList(SohuQoraRelateBo bo);

    /**
     * 修改问题关联
     */
    Boolean insertByBo(SohuQoraRelateBo bo);

    /**
     * 修改问题关联
     */
    Boolean updateByBo(SohuQoraRelateBo bo);
//
//    /**
//     * 校验并批量删除问题关联信息
//     */
//    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    SohuQoraRelate getByQora(Long qoraId, String type);

    void deleteByQuestionIds(Collection<Long> questionIds);
}
