package com.sohu.middleService.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuAgreeRecordBo;
import com.sohu.middle.api.vo.SohuAgreeRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 协议记录Service接口
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
public interface ISohuAgreeRecordService {

    /**
     * 查询协议记录
     */
    SohuAgreeRecordVo queryById(Long id);

    /**
     * 查询协议记录列表
     */
    TableDataInfo<SohuAgreeRecordVo> queryPageList(SohuAgreeRecordBo bo, PageQuery pageQuery);

    /**
     * 查询协议记录列表
     */
    List<SohuAgreeRecordVo> queryList(SohuAgreeRecordBo bo);

    /**
     * 新增协议记录
     */
    Boolean insertByBo(SohuAgreeRecordBo bo);

    /**
     * 修改协议记录
     */
    Boolean updateByBo(SohuAgreeRecordBo bo);

    /**
     * 校验并批量删除协议记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
