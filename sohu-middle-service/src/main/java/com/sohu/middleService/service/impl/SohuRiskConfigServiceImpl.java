package com.sohu.middleService.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.middle.api.bo.risk.SohuRiskConfigBo;
import com.sohu.middle.api.vo.risk.SohuRiskConfigVo;
import com.sohu.middleService.domain.SohuRiskConfig;
import com.sohu.middleService.mapper.SohuRiskConfigMapper;
import com.sohu.middleService.service.ISohuRiskConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


/**
 * 风控检测配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@RequiredArgsConstructor
@Service
public class SohuRiskConfigServiceImpl implements ISohuRiskConfigService {

    private final SohuRiskConfigMapper baseMapper;
    @Override
    public SohuRiskConfigVo queryByBo(SohuRiskConfigBo bo) {
        LambdaQueryWrapper<SohuRiskConfig> lambdaQueryWrapper = buildQueryWrapper(bo);
        return baseMapper.selectVoOne(lambdaQueryWrapper);
    }

    @Override
    public SohuRiskConfigVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    private LambdaQueryWrapper<SohuRiskConfig> buildQueryWrapper(SohuRiskConfigBo bo) {
        LambdaQueryWrapper<SohuRiskConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getBusyType()), SohuRiskConfig::getBusyType, bo.getBusyType());
        lqw.eq(bo.getDetectType() != null, SohuRiskConfig::getDetectType, bo.getDetectType());
        lqw.eq(StringUtils.isNotBlank(bo.getFieldName()), SohuRiskConfig::getFieldName, bo.getFieldName());
        lqw.eq(StringUtils.isNotBlank(bo.getFieldCode()), SohuRiskConfig::getFieldCode, bo.getFieldCode());
        lqw.eq(bo.getPassProcess() != null, SohuRiskConfig::getPassProcess, bo.getPassProcess());
        lqw.eq(bo.getSuspectProcess() != null, SohuRiskConfig::getSuspectProcess, bo.getSuspectProcess());
        lqw.eq(bo.getNoPassProcess() != null, SohuRiskConfig::getNoPassProcess, bo.getNoPassProcess());
        lqw.eq(StringUtils.isNotBlank(bo.getPlatform()), SohuRiskConfig::getPlatform, bo.getPlatform());
        return lqw;
    }
}
