package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuIndependentMaterialBo;
import com.sohu.middle.api.vo.SohuIndependentMaterialVo;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middleService.domain.SohuIndependentMaterial;
import com.sohu.middleService.mapper.SohuIndependentMaterialMapper;
import com.sohu.middleService.mapper.SohuIndependentMaterialUserMapper;
import com.sohu.middleService.service.ISohuIndependentMaterialService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 分销素材库Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-24
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SohuIndependentMaterialServiceImpl implements ISohuIndependentMaterialService {

    private final SohuIndependentMaterialMapper baseMapper;
    private final SohuIndependentMaterialUserMapper sohuIndependentMaterialUserMapper;

    /**
     * 查询分销素材库
     */
    @Override
    public SohuIndependentMaterialVo queryById(Long id, Boolean isMe) {
        Integer intIsMe = isMe ? 1 : 0;
        return baseMapper.queryById(id, LoginHelper.getUserId(), intIsMe);
    }

    /**
     * 查询分销素材库列表
     */
    @Override
    public TableDataInfo<SohuIndependentMaterialVo> queryPageList(SohuIndependentMaterialBo bo, PageQuery pageQuery) {
        Page<SohuIndependentMaterialVo> result = baseMapper.queryPageList(PageQueryUtils.build(pageQuery), bo, LoginHelper.getUserId());
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询分销素材库列表
     */
    @Override
    public List<SohuIndependentMaterialVo> queryList(SohuIndependentMaterialBo bo) {
        LambdaQueryWrapper<SohuIndependentMaterial> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuIndependentMaterial> buildQueryWrapper(SohuIndependentMaterialBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuIndependentMaterial> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getMaterialName()), SohuIndependentMaterial::getMaterialName, bo.getMaterialName());
        lqw.eq(bo.getId() != null, SohuIndependentMaterial::getId, bo.getId());
        lqw.eq(bo.getMaterialType() != null, SohuIndependentMaterial::getMaterialType, bo.getMaterialType());
        lqw.eq(bo.getPrice() != null, SohuIndependentMaterial::getPrice, bo.getPrice());
        lqw.eq(bo.getMaterialUserId() != null, SohuIndependentMaterial::getMaterialUserId, bo.getMaterialUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getMaterialCode()), SohuIndependentMaterial::getMaterialCode, bo.getMaterialCode());
        lqw.eq(bo.getSiteId() != null, SohuIndependentMaterial::getSiteId, bo.getSiteId());
        if (Objects.nonNull(bo.getMinIndependentPrice())) {
            lqw.between(SohuIndependentMaterial::getPrice, bo.getMinIndependentPrice(), bo.getMaxIndependentPrice());
        }
//        if (Objects.nonNull(bo.getMinSales())) {
//            lqw.between(SohuIndependentMaterial::getSales, bo.getMinSales(), bo.getMaxSales());
//        }
        if (Objects.nonNull(bo.getStartDate())) {
            lqw.ge(SohuIndependentMaterial::getCreateTime, bo.getStartDate());
            lqw.le(SohuIndependentMaterial::getCreateTime, bo.getEndDate());
        }
        return lqw;
    }

    /**
     * 新增分销素材库
     */
    @Override
    public Boolean insertByBo(SohuIndependentMaterialBo bo) {
        SohuIndependentMaterial add = BeanUtil.toBean(bo, SohuIndependentMaterial.class);
        validEntityBeforeSave(add);
        // 处理新增重复素材
        if (baseMapper.selectCount(new LambdaQueryWrapper<SohuIndependentMaterial>()
                .eq(SohuIndependentMaterial::getMaterialCode, add.getMaterialCode())
                .eq(SohuIndependentMaterial::getMaterialType, add.getMaterialType())
                .eq(SohuIndependentMaterial::getStatus, CommonState.OnShelf.getCode())) > 0) {
            return Boolean.TRUE;
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改分销素材库
     */
    @Override
    public Boolean updateByBo(SohuIndependentMaterialBo bo) {
        SohuIndependentMaterial update = BeanUtil.toBean(bo, SohuIndependentMaterial.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuIndependentMaterial entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除分销素材库
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean deleteByCodeAndType(String materialCode, String materialType) {
        LambdaUpdateWrapper<SohuIndependentMaterial> luw = new LambdaUpdateWrapper<>();
        luw.set(SohuIndependentMaterial::getStatus, CommonState.OffShelf);
        luw.eq(SohuIndependentMaterial::getMaterialCode, materialCode);
        luw.eq(SohuIndependentMaterial::getMaterialType, materialType);

        return baseMapper.update(new SohuIndependentMaterial(), luw) > 0;
    }

    @Override
    public TableDataInfo<SohuIndependentMaterialVo> myMaterialList(SohuIndependentMaterialBo bo, PageQuery pageQuery) {
        Page<SohuIndependentMaterialVo> result = baseMapper.myMaterialList(bo, PageQueryUtils.build(pageQuery), LoginHelper.getUserId());
        return TableDataInfoUtils.build(result);
    }

    @Override
    public SohuIndependentMaterialVo queryByCodeAndType(String materialCode, String materialType) {
        LambdaQueryWrapper<SohuIndependentMaterial> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuIndependentMaterial::getMaterialCode, materialCode);
        lqw.eq(SohuIndependentMaterial::getMaterialType, materialType);
        lqw.eq(SohuIndependentMaterial::getStatus, CommonState.OnShelf.getCode());
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public SohuIndependentMaterialVo queryByFlowTask(String materialCode, String materialType) {
        LambdaQueryWrapper<SohuIndependentMaterial> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuIndependentMaterial::getMaterialCode, materialCode);
        lqw.eq(SohuIndependentMaterial::getMaterialType, materialType);
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public Boolean checkData(Long materialId, Long materialShareUserId) {
        LambdaQueryWrapper<SohuIndependentMaterial> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuIndependentMaterial::getId, materialId);
        lqw.eq(SohuIndependentMaterial::getStatus, CommonState.OffShelf.getCode());

        return !baseMapper.exists(lqw);
    }
}
