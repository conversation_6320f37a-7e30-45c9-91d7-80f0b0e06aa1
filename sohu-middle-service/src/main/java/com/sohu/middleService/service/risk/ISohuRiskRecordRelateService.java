package com.sohu.middleService.service.risk;



import com.sohu.middle.api.bo.risk.SohuRiskRecordRelateBo;
import com.sohu.middle.api.vo.risk.SohuRiskRecordRelateVo;

import java.util.Collection;
import java.util.List;

/**
 * 风控记录检测Service接口
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface ISohuRiskRecordRelateService {

    /**
     * 新增风控记录检测
     * @param sohuRiskRecordRelateBo
     * @return
     */
    Boolean insertByBo(SohuRiskRecordRelateBo sohuRiskRecordRelateBo);

    /**
     * 批量新增风控记录检测
     * @param sohuRiskRecordRelateBoList
     * @return
     */
    Boolean insertBatch(List<SohuRiskRecordRelateBo> sohuRiskRecordRelateBoList);

    /**
     * 基于检测记录id查询对应的风控记录
     *
     * @param recordId
     * @return
     */
    List<SohuRiskRecordRelateVo> queryListByRecordId(Long recordId);
}
