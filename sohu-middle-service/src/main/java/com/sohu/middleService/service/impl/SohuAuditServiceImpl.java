package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.busyorder.api.RemoteBusyOrderService;
import com.sohu.busyorder.api.RemoteBusyTaskSiteService;
import com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.domain.MsgContent;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.BigDecimalUtils;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.entry.api.RemoteEntryAuthService;
import com.sohu.entry.api.RemoteEntryService;
import com.sohu.entry.api.model.SohuEntryAuthModel;
import com.sohu.entry.api.vo.SohuEntryAuthVo;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.enums.behavior.OperaTypeEnum;
import com.sohu.middle.api.service.*;
import com.sohu.middle.api.service.notice.RemoteMiddleSystemNoticeService;
import com.sohu.middle.api.vo.*;
import com.sohu.middleService.domain.SohuAudit;
import com.sohu.middleService.domain.SohuAuditRecord;
import com.sohu.middleService.domain.SohuReportInfo;
import com.sohu.middleService.mapper.SohuAuditMapper;
import com.sohu.middleService.mapper.SohuAuditRecordMapper;
import com.sohu.middleService.service.*;
import com.sohu.middleService.strategy.MiddleProcessor;
import com.sohu.resource.api.RemoteFileService;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteSysPropertiesConfigService;
import com.sohu.system.api.RemoteSysRoleService;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 业务审核Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuAuditServiceImpl extends SohuBaseServiceImpl<SohuAuditMapper, SohuAudit, SohuAuditVo> implements ISohuAuditService {

    private final SohuAuditRecordMapper sohuAuditRecordMapper;
    private final ISohuSiteService sohuSiteService;
    private final ISohuAuditRecordService sohuAuditRecordService;
    private final ISohuReportInfoService sohuReportInfoService;
    private final AsyncConfig asyncConfig;
    private final ISohuUserService sohuUserService;
    private final ISohuCategoryService sohuCategoryService;
    @DubboReference
    private RemoteFileService remoteFileService;
    @DubboReference
    private RemoteEntryService remoteEntryService;
    @DubboReference
    private RemoteBusyOrderService remoteBusyOrderService;
    @DubboReference
    private RemoteSysPropertiesConfigService remoteSysPropertiesConfigService;
    @DubboReference
    private RemoteMiddleSystemNoticeService remoteMiddleSystemNoticeService;
    @DubboReference
    private RemoteMiddleQuestionService remoteMiddleQuestionService;
    @DubboReference
    private final RemoteEntryAuthService entryAuthService;
    @DubboReference
    private final RemoteSysRoleService sysRoleService;
    @DubboReference
    private RemoteMiddleUserBehaviorRecordService remoteMiddleUserBehaviorRecordService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteMiddleIndependentMaterialService remoteMiddleIndependentMaterialService;
    @DubboReference
    private RemoteMiddleArticleService remoteMiddleArticleService;
    @DubboReference
    private RemoteMiddleVideoService remoteMiddleVideoService;
    @DubboReference
    private RemoteBusyTaskSiteService remoteBusyTaskSiteService;
    @DubboReference
    private RemoteMiddleCategoryService remoteMiddleCategoryService;

    /**
     * 查询业务审核
     */
    @Override
    public SohuAuditVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询业务审核列表
     */
    @Override
    public TableDataInfo<SohuAuditVo> queryPageList(SohuAuditBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuAudit> lqw = buildQueryWrapper(bo);
        Page<SohuAuditVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuAuditVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            Set<Long> ossIds = new HashSet<>();
            Set<Long> siteIds = new HashSet<>();
            //业务id集合
            Set<Long> busyCodes = new HashSet<>();
            // 归属人id集合
            Set<Long> userIds = new HashSet<>();
            // 分类id集合
            Set<Long> categoryIds = new HashSet<>();
            for (SohuAuditVo record : result.getRecords()) {
                siteIds.add(record.getCitySiteId());
                busyCodes.add(record.getBusyCode());
                if (!NumberUtil.isNumber(record.getBusyCoverImg())) {
                    continue;
                }
                ossIds.add(Long.valueOf(record.getBusyCoverImg()));
                userIds.add(record.getBusyBelonger());
                categoryIds.add(record.getCategoryId());
            }
            Map<Long, SohuSiteVo> siteMap = sohuSiteService.queryMap(siteIds);
            Map<Long, String> ossMap = remoteFileService.map(ossIds);
            Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
            Map<Long, SohuCategoryVo> categoryMap = sohuCategoryService.queryMap(categoryIds);
            if (Objects.isNull(ossMap)) {
                ossMap = new HashMap<>();
            }
            //入驻行业认证信息缓存
            Map<Long, SohuEntryAuthModel> entryAuthMap = null;
            if (StrUtil.equalsAnyIgnoreCase(bo.getBusyType(), BusyType.EntryIndustry.name())) {
                entryAuthMap = this.remoteEntryService.selectMapByEntryAuthIds(busyCodes);
            }

            for (SohuAuditVo record : result.getRecords()) {
                SohuSiteVo site = siteMap.get(record.getCitySiteId());
                if (Objects.isNull(site)) {
                    continue;
                }
                record.setCitySiteName(site.getName());
                //转换入驻行业认证信息数据
                this.convertEntryIndustry(record.getBusyCode(), record.getBusyType(), record, entryAuthMap);
                if (!NumberUtil.isNumber(record.getBusyCoverImg())) {
                    continue;
                }
                record.setBusyCoverUrl(ossMap.get(Long.valueOf(record.getBusyCoverImg())));
                LoginUser loginUser = userMap.get(record.getBusyBelonger());
                if (Objects.isNull(loginUser)) {
                    continue;
                }
                record.setBusyBelongerName(loginUser.getNickname());
                record.setCategoryName(categoryMap.get(record.getCategoryId()).getName());
            }
        }
        return TableDataInfoUtils.build(result);
    }

    /**
     * 转换入驻行业认证信息数据
     *
     * @param busyCode     业务id
     * @param busyType     审核业务类型{@link BusyType}
     * @param vo
     * @param entryAuthMap
     */
    private void convertEntryIndustry(Long busyCode, String busyType, SohuAuditVo vo, Map<Long, SohuEntryAuthModel> entryAuthMap) {
        if (StrUtil.equalsAnyIgnoreCase(busyType, BusyType.EntryIndustry.name())) {
            SohuEntryAuthModel model = entryAuthMap.get(busyCode);
            if (model != null) {
                vo.setEntryAuth(BeanUtil.copyProperties(model, SohuEntryAuthRespVo.class));
            }
        }
    }

    /**
     * 查询业务审核列表
     */
    @Override
    public List<SohuAuditVo> queryList(SohuAuditBo bo) {
        LambdaQueryWrapper<SohuAudit> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuAudit> buildQueryWrapper(SohuAuditBo bo) {
       /* String s = LoginHelper.getLoginUser().getPhoneNumber();
        //通过手机号找到对应的站长的站点
        SohuSiteVo sohuSiteVo = sohuSiteService.selectSiteByPhone(s);
        Long id = sohuSiteVo.getId();*/
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuAudit> lqw = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(bo.getState())) {
            if (LoginHelper.hasRole(RoleCodeEnum.CityStationAgent)) {
                SohuSiteVo sohuSite = sohuSiteService.selectSiteByUserId(LoginHelper.getUserId());
                bo.setCitySiteId(Objects.nonNull(sohuSite) ? sohuSite.getId() : 0L);
                bo.setCityAuditState(bo.getState());
            } else if (LoginHelper.hasRole(RoleCodeEnum.CountryStationAgent)) {
                SohuSiteVo sohuSite = sohuSiteService.selectSiteByUserId(LoginHelper.getUserId());
                bo.setCountrySiteId(Objects.nonNull(sohuSite) ? sohuSite.getId() : 0L);
                bo.setCityAuditState(CommonState.OnShelf.getCode());
                bo.setCountryAuditState(bo.getState());
                // TODO 超管能否审核
            } else if (LoginHelper.hasRole(RoleCodeEnum.ADMIN)) {
                bo.setCityAuditState(CommonState.OnShelf.getCode());
                bo.setCountryAuditState(CommonState.OnShelf.getCode());
                bo.setSysAuditState(bo.getState());
            }
            if (bo.getState().equals(CommonState.CompelOff.getCode())) {
                bo.setCityAuditState(bo.getState());
                bo.setCountryAuditState(bo.getState());
                bo.setSysAuditState(bo.getState());
            }
        }
        /*if (sohuSiteVo.getType().equals("City")){
            lqw.eq(id!=null,SohuAudit::getCitySiteId,id);
        }*/
        lqw.eq(bo.getCitySiteId() != null, SohuAudit::getCitySiteId, bo.getCitySiteId());
        lqw.eq(bo.getCountrySiteId() != null, SohuAudit::getCountrySiteId, bo.getCountrySiteId());
        lqw.eq(StrUtil.isNotBlank(bo.getCityAuditState()), SohuAudit::getCityAuditState, bo.getCityAuditState());
        lqw.eq(StrUtil.isNotBlank(bo.getCountryAuditState()), SohuAudit::getCountryAuditState, bo.getCountryAuditState());
        lqw.eq(Objects.nonNull(bo.getBusyCode()), SohuAudit::getBusyCode, bo.getBusyCode());
        lqw.eq(StrUtil.isNotBlank(bo.getSysAuditState()), SohuAudit::getSysAuditState, bo.getSysAuditState());
        lqw.eq(StrUtil.isNotBlank(bo.getBusyType()), SohuAudit::getBusyType, bo.getBusyType());
        lqw.in(CollUtil.isNotEmpty(bo.getBusyTypes()), SohuAudit::getBusyType, bo.getBusyTypes());
        lqw.like(StrUtil.isNotBlank(bo.getBusyTitle()), SohuAudit::getBusyTitle, bo.getBusyTitle());
        lqw.in(CollectionUtil.isNotEmpty(bo.getSiteIds()), SohuAudit::getCitySiteId, bo.getSiteIds());
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            lqw.ge(StrUtil.isNotBlank(bo.getStartTime()), SohuAudit::getPublishTime, DateUtil.beginOfDay(DateUtil.parseDate(bo.getStartTime())));
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            lqw.le(StrUtil.isNotBlank(bo.getEndTime()), SohuAudit::getPublishTime, DateUtil.endOfDay(DateUtil.parseDate(bo.getEndTime())));
        }
        if (Objects.nonNull(bo.getId()) && !bo.getId().equals(0L)) {
            lqw.lt(SohuAudit::getId, bo.getId());
        }
        lqw.orderByDesc(SohuAudit::getId);
        return lqw;
    }

    /**
     * 新增业务审核
     */
    @Override
    public Boolean insertByBo(SohuAuditBo bo) {
        SohuAudit add = BeanUtil.toBean(bo, SohuAudit.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改业务审核
     */
    @Override
    public Boolean updateByBo(SohuAuditBo bo) {
        SohuAudit update = BeanUtil.toBean(bo, SohuAudit.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuAudit entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除业务审核
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        if (isValid) {
//            // TODO 做一些业务上的校验,判断是否需要校验
//        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public SohuAuditVo selectByObj(Long objId, String objType) {
        LambdaQueryWrapper<SohuAudit> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuAudit::getBusyCode, objId).eq(SohuAudit::getBusyType, objType).orderByDesc(SohuAudit::getPublishTime).last(" limit 1");
        return this.baseMapper.selectVoOne(lqw);
    }

    @Override
    public void deleteByObj(Long objId, String objType) {
        this.baseMapper.deleteByObj(objId, objType);
        sohuAuditRecordMapper.deleteByObj(objId, objType);
    }

    @Override
    public void deleteByObjAndUserId(Long objId, String objType, Long userId) {
        baseMapper.delete(Wrappers.lambdaQuery(SohuAudit.class)
                .eq(SohuAudit::getBusyCode, objId)
                .eq(SohuAudit::getBusyType, objType)
                .eq(SohuAudit::getBusyBelonger, userId));
        sohuAuditRecordMapper.deleteByObj(objId, objType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
//    @Async("asyncExecutor")
    public Long initCreateAudited(SohuAuditBo auditBo) {
//        SohuAuditBo queryBo = new SohuAuditBo();
//        queryBo.setBusyType(auditBo.getBusyType());
//        queryBo.setBusyCode(auditBo.getBusyCode());
//        List<SohuAuditVo> sohuAuditVoList = this.queryList(queryBo);
//        if (CollUtil.isNotEmpty(sohuAuditVoList)) {
//            //删除旧的审核
//            this.baseMapper.delete(SohuAudit::getBusyCode, auditBo.getBusyCode(), SohuAudit::getBusyType, auditBo.sohuBusyType);
//            // 删除旧的审核记录
//            sohuAuditRecordMapper.deleteByObj(auditBo.getBusyCode(), auditBo.getBusyType());
//        }
        // 业务必须挂在城市站点
        SohuSiteVo sohuSite = sohuSiteService.queryById(auditBo.getCitySiteId());
        if (Objects.isNull(sohuSite)) {
            throw new ServiceException(MessageUtils.message("SITE_NOT_FOUNT"));
        }
        if (!sohuSite.getType().equals(SiteType.City.name()) && !StrUtil.equalsAnyIgnoreCase(auditBo.getBusyType(), BusyType.EntryIndustry.name())) {
            throw new ServiceException(MessageUtils.message("WRONG_BUSY_AUDIT_MUST_SITE"));
        }
        if (Objects.isNull(auditBo.getBusyBelonger())) {
            auditBo.setBusyBelonger(LoginHelper.getUserId());
        }
        auditBo.setCountrySiteId(sohuSite.getPid());
        auditBo.setCityAuditState(StrUtil.isBlankIfStr(auditBo.getCityAuditState()) ? CommonState.WaitApprove.getCode() : auditBo.getCityAuditState());
        auditBo.setCountryAuditState(StrUtil.isBlankIfStr(auditBo.getCountryAuditState()) ? CommonState.WaitApprove.getCode() : auditBo.getCountryAuditState());
        auditBo.setSysAuditState(StrUtil.isBlankIfStr(auditBo.getSysAuditState()) ? CommonState.WaitApprove.getCode() : auditBo.getSysAuditState());
        auditBo.setPublishTime(new Date());
        auditBo.setCategoryId(auditBo.getCategoryId());
//        Boolean insertByBo = this.insertByBo(auditBo);
//        if (StrUtil.equalsAnyIgnoreCase(auditBo.getBusyType(), BusyType.Entry.name())) {
//            remoteEntryService.hostRecordInsert(auditBo.getBusyCode(), LoginHelper.getUserId(), auditBo.getId(), CommonState.WaitApprove.getCode(), 1);
//        }
//        if (insertByBo == null || !insertByBo) {
//            log.error(MessageUtils.message("ADD_AUDIT_RECORDS_FAILED") + "，{}", JSONUtil.toJsonStr(auditBo));
//            throw new ServiceException(MessageUtils.message("ADD_AUDIT_RECORDS_FAILED"));
//        }
        if (!this.saveAudit(auditBo)) {
            log.error(MessageUtils.message("ADD_AUDIT_RECORDS_FAILED") + "，{}", JSONUtil.toJsonStr(auditBo));
            throw new ServiceException(MessageUtils.message("ADD_AUDIT_RECORDS_FAILED"));
        }
        Long auditId = auditBo.getId();
        // 初始化审核细节流程
        if (BooleanUtil.isTrue(auditBo.getIsAiAudit())) {
            String currentState = Objects.equals(auditBo.getState(), CommonState.OnShelf.name()) ? CommonState.OnShelf.name() : CommonState.Refuse.name();
            SohuAuditRecordBo auditRecordBo = new SohuAuditRecordBo();
            auditRecordBo.setAuditId(auditId);
            auditRecordBo.setBusyCode(auditBo.getBusyCode());
            auditRecordBo.setBusyType(auditBo.getBusyType());
            auditRecordBo.setAuditState(currentState);
            auditRecordBo.setAuditTime(new Date());
            auditRecordBo.setAuditer(0L);
            auditRecordBo.setTitle("智能机审");
            auditRecordBo.setAuditorName("机审");
            if (Objects.equals(CommonState.OnShelf.getCode(), auditBo.getState())) {
                auditRecordBo.setRemark(auditBo.getRecordRemark());
            } else {
                auditRecordBo.setRejectReason(auditBo.getRecordRemark());
            }
            sohuAuditRecordService.insertByBo(auditRecordBo);
        }
//        } else {
//            auditRecordBo.setAuditer(0L);
//            auditRecordBo.setTitle("审核初始化");
//            auditRecordBo.setAuditorName("系统");
//            auditRecordBo.setRemark(auditBo.getRecordRemark());
//            sohuAuditRecordService.insertByBo(auditRecordBo);
//        }
//        if (Objects.equals(CommonState.OnShelf.getCode(), auditBo.getCityAuditState())) {
//            auditRecordBo.setId(null);
//            auditRecordBo.setTitle("城市站");
//            auditRecordBo.setAuditorName("机审");
//            auditRecordBo.setAuditer(0L);
//            sohuAuditRecordService.insertByBo(auditRecordBo);
//        }
//        if (Objects.equals(CommonState.OnShelf.getCode(), auditBo.getCityAuditState())) {
//            auditRecordBo.setId(null);
//            auditRecordBo.setTitle("总站");
//            auditRecordBo.setAuditorName("机审");
//            auditRecordBo.setAuditer(0L);
//            sohuAuditRecordService.insertByBo(auditRecordBo);
//        }
//        if (Objects.equals(CommonState.OnShelf.getCode(), auditBo.getCityAuditState())) {
//            auditRecordBo.setId(null);
//            auditRecordBo.setTitle("平台运营");
//            auditRecordBo.setAuditorName("机审");
//            auditRecordBo.setAuditer(0L);
//            sohuAuditRecordService.insertByBo(auditRecordBo);
//        }
//        } else {
//            Long recordId = sohuAuditRecordService.createInitRecord(auditId, auditBo.getBusyCode(), auditBo.getBusyType(), currentState, null);
//            if (recordId == null || recordId <= 0L) {
//                log.error(MessageUtils.message("ADD_AUDIT_RECORDS_FAILED") + "，{}", JSONUtil.toJsonStr(auditBo));
//                throw new ServiceException(MessageUtils.message("ADD_AUDIT_RECORDS_FAILED"));
//            }
//        }
        return auditId;
    }

    /**
     * 保存审核信息
     *
     * @param auditBo
     * @return
     */
    private Boolean saveAudit(SohuAuditBo auditBo) {
        LambdaQueryWrapper<SohuAudit> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuAudit::getBusyCode, auditBo.getBusyCode());
        lqw.eq(SohuAudit::getBusyType, auditBo.getBusyType());
        lqw.last("limit 1");
        SohuAudit entity = this.baseMapper.selectOne(lqw);
        if (Objects.isNull(entity)) {
            return this.insertByBo(auditBo);
        } else {
            SohuAudit updateEntity = BeanUtil.copyProperties(auditBo, SohuAudit.class);
            updateEntity.setId(entity.getId());
            LambdaUpdateWrapper<SohuAudit> luw = new LambdaUpdateWrapper();
            luw.eq(SohuAudit::getId, entity.getId());
            luw.set(SohuAudit::getRejectReason, null);
            this.baseMapper.update(updateEntity, luw);
            auditBo.setId(entity.getId());
            return true;
        }
    }

    private void validateArticleExists(SohuAudit sohuAudit) {
        if (Objects.isNull(sohuAudit)) {
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R process(SohuAuditBo bo) {
        SohuAudit exist = this.baseMapper.selectById(bo.getId());
        validateArticleExists(exist);
        String busyType = exist.getBusyType();
        // 是否最后一层审核
        boolean lastAudit = false;

        if (LoginHelper.hasRole(RoleCodeEnum.CityStationAgent)) {
            exist.setCityAuditState(bo.getState());
        } else if (LoginHelper.hasRole(RoleCodeEnum.CountryStationAgent)) {
            if (!StrUtil.equalsAnyIgnoreCase(exist.getCityAuditState(), CommonState.OnShelf.name())) {
                throw new ServiceException("城市站长未审核通过");
            }
            exist.setCountryAuditState(bo.getState());
            exist.setCountryAudit(Boolean.TRUE);
        } else if (LoginHelper.hasRole(RoleCodeEnum.ADMIN)) {
            if (!StrUtil.equalsAnyIgnoreCase(exist.getCityAuditState(), CommonState.OnShelf.name())) {
                throw new ServiceException("城市站长未审核通过");
            }
            if (!StrUtil.equalsAnyIgnoreCase(exist.getCountryAuditState(), CommonState.OnShelf.name())) {
                throw new ServiceException("国家站长未审核通过");
            }
            exist.setSysAuditState(bo.getState());
            lastAudit = true;
        }
        if (StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.OffShelf.name())
                || StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.CompelOff.name())
                || StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.Refuse.name())) {
            exist.setCityAuditState(bo.getState());
            exist.setCountryAuditState(bo.getState());
            exist.setSysAuditState(bo.getState());
        }
        exist.setRejectReason(bo.getRejectReason());
        if (StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.OnShelf.name())) {
            // 通过才调这个方法
            if (!lastAudit) {
                this.buildAudit(exist);
            }
            lastAudit = StrUtil.equalsAnyIgnoreCase(exist.getSysAuditState(), CommonState.OnShelf.getCode());
            if (lastAudit) {
                exist.setCountryAuditState(CommonState.OnShelf.getCode());
                exist.setSysAuditState(CommonState.OnShelf.getCode());
            }
        }
        this.baseMapper.updateById(exist);

        /**
         * 审核细节流程记录
         */
        SohuAuditRecordBo auditRecordBo = new SohuAuditRecordBo();
        auditRecordBo.setAuditId(exist.getId());
        auditRecordBo.setBusyCode(exist.getBusyCode());
        auditRecordBo.setBusyType(exist.getBusyType());
        auditRecordBo.setAuditState(bo.getState());
        auditRecordBo.setAuditTime(new Date());
        if (Objects.isNull(LoginHelper.getLoginUser())) {
            auditRecordBo.setAuditorName("机审");
            auditRecordBo.setAuditer(1L);
        } else {
            auditRecordBo.setAuditorName(LoginHelper.getLoginUser().getNickname());
            auditRecordBo.setAuditer(LoginHelper.getUserId());
        }
        auditRecordBo.setRemark(bo.getRecordRemark());
        auditRecordBo.setRejectReason(bo.getRejectReason());
        if (LoginHelper.hasRole(RoleCodeEnum.CityStationAgent)) {
            auditRecordBo.setTitle("城市站");
        } else if (LoginHelper.hasRole(RoleCodeEnum.CountryStationAgent)) {
            auditRecordBo.setTitle("总站");
        } else if (LoginHelper.anyMatchRole(RoleCodeEnum.ADMIN.getCode(), RoleCodeEnum.OPERATION_ADMIN.getCode(), RoleCodeEnum.CALL_ADMIN.getCode())) {
            auditRecordBo.setTitle("平台运营");
        } else {
            auditRecordBo.setTitle("未知");
        }
        sohuAuditRecordService.insertByBo(auditRecordBo);

        if (StrUtil.equalsAnyIgnoreCase(busyType, BusyType.BusyOrder.name())) {
            remoteBusyOrderService.updateState(bo.getBusyCode(), bo.getState(),
                    bo.getRejectReason(), bo.getPrepayAmount(), bo.getPrepayCurrency(), bo.getPrepayTime(), lastAudit);
        } else if (StrUtil.equalsAnyIgnoreCase(exist.getSysAuditState(), CommonState.OnShelf.name()) || !StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.OnShelf.name())) {
            if (StrUtil.equalsAnyIgnoreCase(busyType, BusyType.Project.name())) {
                remoteEntryService.updateEntryMainState(exist.getBusyCode(), bo.getState(), bo.getRejectReason());
            } else {
                SohuBusyUpdateStateBo updateStateBo = new SohuBusyUpdateStateBo();
                // 审核
                updateStateBo.setId(bo.getId());
                updateStateBo.setBusyCode(exist.getBusyCode());
                updateStateBo.setBusyType(exist.getBusyType());
                updateStateBo.setState(exist.getSysAuditState());
                updateStateBo.setRejectReason(bo.getRejectReason());
                MiddleProcessor middleProcessor = new MiddleProcessor();
                if (Objects.isNull(middleProcessor.getStrategy(BusyType.valueOf(exist.getBusyType())))) {
                    SohuAuditBo sohuAuditBo = BeanUtil.toBean(updateStateBo, SohuAuditBo.class);
                    this.updateByBo(sohuAuditBo);
                } else {
                    middleProcessor.getStrategy(BusyType.valueOf(exist.getBusyType())).updateState(updateStateBo);
                }
            }
        }
        // 审核细节流程记录
        //sohuAuditRecordService.createInitRecord(exist.getId(), exist.getBusyCode(), busyType, bo.getState(), bo.getRejectReason());

//        // 角色认证发送消息通知
//        if (StrUtil.equalsAnyIgnoreCase(busyType, BusyType.EntryIndustry.name())) {
//            SohuEntryAuthVo model = entryAuthService.queryById(exist.getBusyCode());
//            if (StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.OnShelf.name())) {
//                sendMsgOfRoleAuthPass(model.getUserId(), model);
//            } else {
//                sendMsgOfRoleAuthNotPass(model.getUserId(), model);
//            }
//        }
        // 问答审核通过发送消息通知
        if (StrUtil.equalsAnyIgnoreCase(busyType, BusyType.Answer.name()) && StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.OnShelf.name())) {
            remoteMiddleQuestionService.sendQuestionInteractNotice(exist.getBusyCode());
        }
        // 延迟队列--更新广告缓存信息
        MsgContent msgContent = new MsgContent(exist.getBusyCode(), bo.getState(), busyType);
        MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(msgContent), Constants.ADVERTISEMENT);
        RedisUtils.delayQueue(JSONUtil.toJsonStr(mqMessaging), Constants.AD_DELAY_LONG, TimeUnit.SECONDS);
        //异步新增埋点数据
        CompletableFuture.runAsync(() -> {
            LoginUser loginUser = remoteUserService.queryById(exist.getBusyBelonger());
            if (StrUtil.equalsAnyIgnoreCase(bo.getState(), CommonState.OnShelf.name()) && Objects.nonNull(loginUser)) {
                SohuUserBehaviorRecordPointBo.EventAttribute eventAttribute = new SohuUserBehaviorRecordPointBo.EventAttribute();
                eventAttribute.setContentNo(String.valueOf(exist.getBusyCode()));
                eventAttribute.setContentName(exist.getBusyTitle());
                eventAttribute.setContentType(BusyType.mapBusyType.get(exist.getBusyType()).getDescription());
                syncContentUserBehavior(loginUser.getUserId(), loginUser.getNickname(), exist.getBusyType(), "content_new_success", "内容发布成功", eventAttribute);
            }
        }, asyncConfig.getAsyncExecutor());
        // 同步素材库
        processIndependentMaterialBo(bo.getState(), exist.getBusyCode(), busyType);

        return R.ok(Boolean.TRUE);
    }

    /**
     * 同步素材库
     *
     * @param state    审核状态
     * @param busyCode 业务id
     * @param busyType 业务类型
     */
    private void processIndependentMaterialBo(String state, Long busyCode, String busyType) {
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.OnShelf.name())) {
            switch (Objects.requireNonNull(BusyType.fromCode(busyType))) {
                case Question:
                    SohuQuestionVo sohuQuestionVo = remoteMiddleQuestionService.queryById(busyCode, Boolean.FALSE);
                    RelationRespVo questionVoRelation = sohuQuestionVo.getRelation();
                    if (Objects.isNull(questionVoRelation) || !StrUtil.equalsAnyIgnoreCase(questionVoRelation.getBusyType(), BusyType.BusyTask.getType())) {
                        return;
                    }
                    SohuBusyTaskSiteVo questionTaskSiteVo = remoteBusyTaskSiteService.queryByTaskNumber(questionVoRelation.getBusyInfo());
                    if (Objects.isNull(questionTaskSiteVo)) {
                        return;
                    }
                    SohuIndependentMaterialVo questionIndependentMaterialVo = remoteMiddleIndependentMaterialService.queryByCodeAndType(questionTaskSiteVo.getMasterTaskNumber(), questionTaskSiteVo.getConstMark());
                    if (Objects.isNull(questionIndependentMaterialVo)) {
                        return;
                    }
                    buildIndependentMaterialUserBo(questionIndependentMaterialVo.getId(), sohuQuestionVo.getUserId(), sohuQuestionVo.getAuthorName());
                    break;
                case Article:
                    SohuArticleVo sohuArticleVo = remoteMiddleArticleService.queryById(busyCode);
                    RelationRespVo articleVoRelation = sohuArticleVo.getRelation();
                    if (Objects.isNull(articleVoRelation) || !StrUtil.equalsAnyIgnoreCase(articleVoRelation.getBusyType(), BusyType.BusyTask.getType())) {
                        return;
                    }
                    SohuBusyTaskSiteVo articleTaskSiteVo = remoteBusyTaskSiteService.queryByTaskNumber(articleVoRelation.getBusyInfo());
                    if (Objects.isNull(articleTaskSiteVo)) {
                        return;
                    }
                    SohuIndependentMaterialVo articleIndependentMaterialVo = remoteMiddleIndependentMaterialService.queryByCodeAndType(articleTaskSiteVo.getMasterTaskNumber(), articleTaskSiteVo.getConstMark());
                    if (Objects.isNull(articleIndependentMaterialVo)) {
                        return;
                    }
                    buildIndependentMaterialUserBo(articleIndependentMaterialVo.getId(), sohuArticleVo.getUserId(), sohuArticleVo.getNickName());
                    break;
                case Video:
                    SohuVideoVo sohuVideoVo = remoteMiddleVideoService.queryById(busyCode, Boolean.FALSE, null);
                    RelationRespVo videoVoRelation = sohuVideoVo.getRelation();
                    if (Objects.isNull(videoVoRelation) || !StrUtil.equalsAnyIgnoreCase(videoVoRelation.getBusyType(), BusyType.BusyTask.getType())) {
                        return;
                    }
                    SohuBusyTaskSiteVo videoTaskSiteVo = remoteBusyTaskSiteService.queryByTaskNumber(videoVoRelation.getBusyInfo());
                    if (Objects.isNull(videoTaskSiteVo)) {
                        return;
                    }
                    SohuIndependentMaterialVo videoIndependentMaterialVo = remoteMiddleIndependentMaterialService.queryByCodeAndType(videoTaskSiteVo.getMasterTaskNumber(), videoTaskSiteVo.getConstMark());
                    if (Objects.isNull(videoIndependentMaterialVo)) {
                        return;
                    }
                    buildIndependentMaterialUserBo(videoIndependentMaterialVo.getId(), sohuVideoVo.getUserId(), sohuVideoVo.getNickName());
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 构建素材库对象
     *
     * @param materialId 素材库id
     * @param materialShareUserId 素材分享人id
     * @param materialShareUserName 素材分享人名称
     */
    private void buildIndependentMaterialUserBo(Long materialId, Long materialShareUserId, String materialShareUserName) {
        SohuIndependentMaterialUserBo independentMaterialUserBo = new SohuIndependentMaterialUserBo();
        independentMaterialUserBo.setMaterialId(materialId);
        independentMaterialUserBo.setMaterialShareUserId(materialShareUserId);
        independentMaterialUserBo.setMaterialShareUserName(materialShareUserName);
        remoteMiddleIndependentMaterialService.addMaterial(independentMaterialUserBo);
    }

    private void buildAudit(SohuAudit exist) {
        Map<String, Boolean> sysConfigMap = remoteSysPropertiesConfigService.sysConfigMap();
        Map<String, Boolean> countryConfigMap = remoteSysPropertiesConfigService.countryConfigMap(exist.getCountrySiteId());
        log.info("countryConfigMap:{}", JSONUtil.toJsonStr(countryConfigMap));
        log.info("sysConfigMap:{}", JSONUtil.toJsonStr(sysConfigMap));
        if (Objects.isNull(sysConfigMap) || sysConfigMap.isEmpty()) {
            return;
        }
        if (Objects.isNull(countryConfigMap) || countryConfigMap.isEmpty()) {
            return;
        }
        if (StrUtil.equalsAnyIgnoreCase(exist.getBusyType(), BusyType.Article.name())) {
            // 图文
            if (countryConfigMap.get("country_audit_article")) {
                exist.setCountryAuditState(CommonState.OnShelf.getCode());
                if (sysConfigMap.get("sys_audit_article")) {
                    exist.setSysAuditState(CommonState.OnShelf.getCode());
                }
            }
            if (sysConfigMap.get("sys_audit_article") && exist.getCountryAudit()) {
                exist.setSysAuditState(CommonState.OnShelf.getCode());
            }
        }
        if (StrUtil.equalsAnyIgnoreCase(exist.getBusyType(), BusyType.Video.name())) {
            // 视频
            if (countryConfigMap.get("country_audit_video")) {
                exist.setCountryAuditState(CommonState.OnShelf.getCode());
                if (sysConfigMap.get("sys_audit_video")) {
                    exist.setSysAuditState(CommonState.OnShelf.getCode());
                }
            }
            if (sysConfigMap.get("sys_audit_video") && exist.getCountryAudit()) {
                exist.setSysAuditState(CommonState.OnShelf.getCode());
            }
        }
        if (StrUtil.equalsAnyIgnoreCase(exist.getBusyType(), BusyType.Question.name())) {
            // 问题
            if (countryConfigMap.get("country_audit_question")) {
                exist.setCountryAuditState(CommonState.OnShelf.getCode());
                if (sysConfigMap.get("sys_audit_question")) {
                    exist.setSysAuditState(CommonState.OnShelf.getCode());
                }
            }
            if (sysConfigMap.get("sys_audit_question") && exist.getCountryAudit()) {
                exist.setSysAuditState(CommonState.OnShelf.getCode());
            }
        }
        if (StrUtil.equalsAnyIgnoreCase(exist.getBusyType(), BusyType.Answer.name())) {
            // 回答
            if (countryConfigMap.get("country_audit_answer")) {
                exist.setCountryAuditState(CommonState.OnShelf.getCode());
                if (sysConfigMap.get("sys_audit_answer")) {
                    exist.setSysAuditState(CommonState.OnShelf.getCode());
                }
            }
            if (sysConfigMap.get("sys_audit_answer") && exist.getCountryAudit()) {
                exist.setSysAuditState(CommonState.OnShelf.getCode());
            }
        }
        if (StrUtil.equalsAnyIgnoreCase(exist.getBusyType(), BusyType.BusyOrder.name())) {
            // 商单
            if (countryConfigMap.get("country_audit_busy_order")) {
                exist.setCountryAuditState(CommonState.OnShelf.getCode());
                if (sysConfigMap.get("sys_audit_busy_order")) {
                    exist.setSysAuditState(CommonState.OnShelf.getCode());
                }
            }
            if (sysConfigMap.get("sys_audit_busy_order") && exist.getCountryAudit()) {
                exist.setSysAuditState(CommonState.OnShelf.getCode());
            }
        }
        if (StrUtil.equalsAnyIgnoreCase(exist.getBusyType(), BusyType.Project.name())) {
            // 项目
            if (countryConfigMap.get("country_audit_project")) {
                exist.setCountryAuditState(CommonState.OnShelf.getCode());
                if (sysConfigMap.get("sys_audit_project")) {
                    exist.setSysAuditState(CommonState.OnShelf.getCode());
                }
            }
            if (sysConfigMap.get("sys_audit_project") && exist.getCountryAudit()) {
                exist.setSysAuditState(CommonState.OnShelf.getCode());
            }
        }
        if (StrUtil.equalsAnyIgnoreCase(exist.getBusyType(), BusyType.Prose.name())
                || StrUtil.equalsAnyIgnoreCase(exist.getBusyType(), BusyType.Poetry.name())) {
            // 诗文
            if (countryConfigMap.get("country_audit_article")) {
                exist.setCountryAuditState(CommonState.OnShelf.getCode());
                if (sysConfigMap.get("sys_audit_article")) {
                    exist.setSysAuditState(CommonState.OnShelf.getCode());
                }
            }
            if (sysConfigMap.get("sys_audit_article") && exist.getCountryAudit()) {
                exist.setSysAuditState(CommonState.OnShelf.getCode());
            }
        }
    }

    @Override
    public Map<Long, SohuAuditVo> queryMap(Collection<Long> ids) {
        List<SohuAuditVo> list = this.baseMapper.selectVoBatchIds(ids);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(SohuAuditVo::getId, u -> u));
    }

    @Override
    public Map<Long, SohuAuditVo> queryMap(Collection<Long> busyCodes, String busyType) {
        LambdaQueryWrapper<SohuAudit> query = new LambdaQueryWrapper<>();
        query.eq(SohuAudit::getBusyType, busyType).in(SohuAudit::getBusyCode, busyCodes);
        List<SohuAuditVo> list = this.baseMapper.selectVoList(query);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(SohuAuditVo::getBusyCode, u -> u));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean compelOff(SohuBusyUpdateStateBo bo) {
        Long id = bo.getId();
        SohuAudit sohuAudit = this.baseMapper.selectById(id);
        validateArticleExists(sohuAudit);
        String busyType = sohuAudit.getBusyType();
        bo.setBusyCode(sohuAudit.getBusyCode());
        SohuBusyUpdateStateBo updateStateBo = new SohuBusyUpdateStateBo();
        updateStateBo.setId(bo.getId());
        updateStateBo.setBusyCode(sohuAudit.getBusyCode());
        updateStateBo.setBusyType(busyType);
        updateStateBo.setState(CommonState.CompelOff.getCode());
        updateStateBo.setRejectReason(bo.getRejectReason());

        MiddleProcessor middleProcessor = new MiddleProcessor();
        middleProcessor.getStrategy(BusyType.valueOf(busyType)).updateState(updateStateBo);


        sohuAudit.setCityAuditState(CommonState.CompelOff.name());
        if (sohuAudit.getCountryAuditState() != null) {
            sohuAudit.setCountryAuditState(CommonState.CompelOff.name());
        }
        if (sohuAudit.getSysAuditState() != null) {
            sohuAudit.setSysAuditState(CommonState.CompelOff.name());
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(bo.getRejectReason())) {
            sohuAudit.setRejectReason(bo.getRejectReason());
        }
        this.baseMapper.updateById(sohuAudit);
        SohuAuditRecordBo recordBo = new SohuAuditRecordBo();
        recordBo.setAuditId(sohuAudit.getId());
        recordBo.setBusyCode(sohuAudit.getBusyCode());
        recordBo.setBusyType(sohuAudit.getBusyType());
        recordBo.setRejectReason(bo.getRejectReason());
        recordBo.setAuditer(LoginHelper.getUserId());
        recordBo.setAuditState(CommonState.CompelOff.getCode());
        recordBo.setAuditTime(new Date());
        // 插入强制下架记录
        sohuAuditRecordService.insertByBo(recordBo);

        // 强制下架发送消息通知
        SohuReportInfoBo infoBo = new SohuReportInfoBo();
        infoBo.setId(bo.getId());
        infoBo.setRejectReason(bo.getRejectReason());
        SohuReportInfo info = new SohuReportInfo();
        info.setTitle(sohuAudit.getBusyTitle());
        info.setAuthorId(sohuAudit.getBusyBelonger());
        CompletableFuture.runAsync(() -> sohuReportInfoService.sendMsgOfReport(infoBo, info, SystemNoticeEnum.SubType.reportOffShelf.name(), SystemNoticeEnum.reportOffShelf), asyncConfig.getAsyncExecutor());
        // 延迟队列--更新广告缓存信息
        MsgContent msgContent = new MsgContent(sohuAudit.getBusyCode(), CommonState.CompelOff.name(), busyType);
        MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(msgContent), Constants.ADVERTISEMENT);
        RedisUtils.delayQueue(JSONUtil.toJsonStr(mqMessaging), Constants.AD_DELAY_LONG, TimeUnit.SECONDS);
        return Boolean.TRUE;
    }

    @Override
    public Long countByBusyTypeAndState(String busyType, String state) {
        LambdaQueryWrapper<SohuAudit> query = new LambdaQueryWrapper<>();
        query.eq(SohuAudit::getBusyType, busyType).eq(SohuAudit::getSysAuditState, state);
        return baseMapper.selectCount(query);
    }

    /**
     * 角色认证审核通过发送消息通知
     *
     * @param userId
     * @param model
     */
    private void sendMsgOfRoleAuthPass(Long userId, SohuEntryAuthVo model) {
//        List<SysRole> roles = sysRoleService.listByRoleCodes(Collections.singletonList(model.getRoleKey()));
//        String roleName = roles.get(0).getRoleName();
//        NoticeSystemContent content = new NoticeSystemContent();
//        content.setTitle(SystemNoticeEnum.roleAuthPass);
//        content.setNoticeTime(DateUtils.getTime());
//        content.setType(SystemNoticeEnum.SubType.roleAuthPass.name());
//        content.setDetailId(model.getId());
//        NoticeContentDetail detail = new NoticeContentDetail();
//        detail.setId(model.getId());
//        detail.setUserId(userId);
//        detail.setDesc(String.format(SystemNoticeEnum.roleAuthPassDesc, roleName));
//        detail.setLinkTitle(SystemNoticeEnum.roleAuthPassLinkTitle);
//        detail.setLinkUrl(SystemNoticeEnum.roleAuthPassLinkUrl);
//        detail.setKeyWord(new String[]{roleName});
//        content.setContent(detail);
//        String contentJson = JSONUtil.toJsonStr(content);
//        remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.roleAuthPass, contentJson, SystemNoticeEnum.Type.roleAuth);
    }

    /**
     * 角色认证审核未通过发送消息通知
     *
     * @param userId
     * @param model
     */
    private void sendMsgOfRoleAuthNotPass(Long userId, SohuEntryAuthVo model) {
//        List<SysRole> roles = sysRoleService.listByRoleCodes(Collections.singletonList(model.getRoleKey()));
//        String roleName = roles.get(0).getRoleName();
//        NoticeSystemContent content = new NoticeSystemContent();
//        content.setTitle(SystemNoticeEnum.roleAuthNotPass);
//        content.setNoticeTime(DateUtils.getTime());
//        content.setType(SystemNoticeEnum.SubType.roleAuthNotPass.name());
//        content.setDetailId(model.getId());
//        NoticeContentDetail detail = new NoticeContentDetail();
//        detail.setId(model.getId());
//        detail.setUserId(userId);
//        detail.setDesc(String.format(SystemNoticeEnum.roleAuthNotPassDesc, roleName));
//        detail.setRejectReason(model.getRejectReason());
//        detail.setLinkTitle(SystemNoticeEnum.notPassLinkTitle);
//        detail.setKeyWord(new String[]{roleName});
//        content.setContent(detail);
//        String contentJson = JSONUtil.toJsonStr(content);
//        remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.roleAuthNotPass, contentJson, SystemNoticeEnum.Type.roleAuth);
    }

    @Override
    public TableDataInfo<SohuAuditVo> getAuditList(SohuAuditBo bo, PageQuery pageQuery) {
        SohuSiteVo sohuSite = sohuSiteService.selectSiteByUserId(LoginHelper.getUserId());
        if (LoginHelper.hasRole(RoleCodeEnum.ADMIN)) {
            // 超管不加查询条件

        } else if (LoginHelper.hasRole(RoleCodeEnum.CityStationAgent)) {
            bo.setCitySiteId(Objects.nonNull(sohuSite) ? sohuSite.getId() : 0L);
        } else if (LoginHelper.hasRole(RoleCodeEnum.CountryStationAgent)) {
            bo.setCountrySiteId(Objects.nonNull(sohuSite) ? sohuSite.getId() : 0L);
        }
        Page<SohuAuditVo> result = baseMapper.getAuditList(bo, PageQueryUtils.build(pageQuery));
        List<SohuAuditVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            Set<Long> siteIds = new HashSet<>();
            // 归属人id集合
            Set<Long> userIds = new HashSet<>();
            // 分类id集合
            Set<Long> categoryIds = new HashSet<>();
            for (SohuAuditVo record : result.getRecords()) {
                siteIds.add(record.getSiteId());
                userIds.add(record.getUserId());
                categoryIds.add(record.getCategoryId());
            }
            Map<Long, SohuSiteVo> siteMap = sohuSiteService.queryMap(siteIds);
            Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
            Map<Long, SohuCategoryVo> categoryMap = sohuCategoryService.queryMap(categoryIds);

            for (SohuAuditVo record : result.getRecords()) {
                SohuSiteVo site = siteMap.get(record.getSiteId());
                if (Objects.isNull(site)) {
                    continue;
                }
                record.setCitySiteName(site.getName());
                LoginUser loginUser = userMap.get(record.getUserId());
                if (Objects.isNull(loginUser)) {
                    continue;
                }
                record.setBusyBelongerName(loginUser.getNickname());
                record.setUserAvatar(loginUser.getAvatar());
                if (categoryMap.containsKey(record.getCategoryId())) {
                    record.setCategoryName(categoryMap.get(record.getCategoryId()).getName());
                }
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public SohuConentListStatVo queryPageListStat(SohuAuditBo bo) {
        if (LoginHelper.hasRole(RoleCodeEnum.ADMIN)) {
        } else if (LoginHelper.hasRole(RoleCodeEnum.CityStationAgent)) {
            SohuSiteVo sohuSite = sohuSiteService.selectSiteByUserId(LoginHelper.getUserId());
            bo.setCitySiteId(Objects.nonNull(sohuSite) ? sohuSite.getId() : 0L);
        } else if (LoginHelper.hasRole(RoleCodeEnum.CountryStationAgent)) {
            SohuSiteVo sohuSite = sohuSiteService.selectSiteByUserId(LoginHelper.getUserId());
            bo.setCountrySiteId(Objects.nonNull(sohuSite) ? sohuSite.getId() : 0L);
        }
        SohuConentListStatVo vo = baseMapper.queryPageListStat(bo);
        if (Objects.isNull(vo)) {
            vo = new SohuConentListStatVo();
        }
        Long allNum = vo.getOnShelfNum() + vo.getWaitApproveNum() + vo.getRefuseNum();
        vo.setAllNum(allNum);
        return vo;
    }

    @Override
    public SohuAuditVo selectNearByObj(Long objId, String objType, Long userId) {
        SohuAuditVo sohuAuditVo = baseMapper.selectVoOne(Wrappers.<SohuAudit>lambdaQuery()
                .eq(SohuAudit::getBusyCode, objId)
                .eq(SohuAudit::getBusyType, objType)
                .eq(SohuAudit::getBusyBelonger, userId)
                .orderByDesc(SohuAudit::getPublishTime).last("limit 1"));
        if (Objects.nonNull(sohuAuditVo)) {
            SohuAuditRecord sohuAuditRecord = sohuAuditRecordMapper.selectOne(Wrappers.<SohuAuditRecord>lambdaQuery()
                    .eq(SohuAuditRecord::getAuditId, sohuAuditVo.getId())
                    .orderByDesc(SohuAuditRecord::getAuditTime).last("limit 1"));
            if (Objects.nonNull(sohuAuditRecord)) {
                sohuAuditVo.setAuditTime(sohuAuditRecord.getAuditTime());
                sohuAuditVo.setAuditorName(sohuAuditRecord.getAuditorName());
                sohuAuditVo.setRejectReason(sohuAuditRecord.getRejectReason());
                sohuAuditVo.setRemark(sohuAuditRecord.getRemark());
            }
        }
        return sohuAuditVo;
    }

    @Override
    public List<SohuAuditVo> selectListByObj(Long objId, String objType) {
        List<SohuAuditVo> auditList = baseMapper.selectVoList(Wrappers.<SohuAudit>lambdaQuery()
                .eq(SohuAudit::getBusyCode, objId).eq(SohuAudit::getBusyType, objType)
                .orderByDesc(SohuAudit::getPublishTime).last("limit 2"));
        if (CollectionUtil.isNotEmpty(auditList)) {
            for (SohuAuditVo sohuAudit : auditList) {
                SohuAuditRecord sohuAuditRecord = sohuAuditRecordMapper.selectOne(Wrappers.<SohuAuditRecord>lambdaQuery()
                        .eq(SohuAuditRecord::getAuditId, sohuAudit.getId())
                        .orderByDesc(SohuAuditRecord::getAuditTime).last("limit 1"));
                if (Objects.nonNull(sohuAuditRecord)) {
                    sohuAudit.setAuditTime(sohuAuditRecord.getAuditTime());
                    sohuAudit.setAuditorName(sohuAuditRecord.getAuditorName());
                    sohuAudit.setRejectReason(sohuAuditRecord.getRejectReason());
                }
            }
        }
        return auditList;
    }

    @Override
    public Boolean createAudited(SohuAuditBo auditBo) {
        // 业务必须挂在城市站点
        SohuSiteVo sohuSite = sohuSiteService.queryById(auditBo.getCitySiteId());
        if (Objects.isNull(sohuSite)) {
            throw new ServiceException(MessageUtils.message("SITE_NOT_FOUNT"));
        }
        if (!sohuSite.getType().equals(SiteType.City.name()) && !StrUtil.equalsAnyIgnoreCase(auditBo.getBusyType(), BusyType.EntryIndustry.name())) {
            throw new ServiceException(MessageUtils.message("WRONG_BUSY_AUDIT_MUST_SITE"));
        }
        if (Objects.isNull(auditBo.getBusyBelonger())) {
            auditBo.setBusyBelonger(LoginHelper.getUserId());
        }
        auditBo.setCountrySiteId(sohuSite.getPid());
        return this.insertByBo(auditBo);
    }

    @Override
    public void audit(SohuAuditBo auditBo) {
        auditBo.setCityAuditState(auditBo.getState());
        auditBo.setCountryAuditState(auditBo.getState());
        auditBo.setSysAuditState(auditBo.getState());
        auditBo.setRejectReason(auditBo.getRejectReason());
        this.updateByBo(auditBo);
        SohuAuditRecordBo recordBo = new SohuAuditRecordBo();
        recordBo.setAuditId(auditBo.getId());
        recordBo.setBusyCode(auditBo.getBusyCode());
        recordBo.setBusyType(auditBo.getBusyType());
        recordBo.setAuditer(Objects.nonNull(LoginHelper.getUserId()) ? LoginHelper.getUserId() : 1);
        recordBo.setAuditState(auditBo.getState());
        recordBo.setAuditTime(new Date());
        recordBo.setRemark(auditBo.getRecordRemark());
        recordBo.setAuditorName(
                Objects.nonNull(LoginHelper.getLoginUser()) ?
                        LoginHelper.getLoginUser().getNickname() : "系统设置");
        recordBo.setRejectReason(auditBo.getRejectReason());
        if (LoginHelper.hasRole(RoleCodeEnum.CityStationAgent)) {
            recordBo.setTitle("城市站");
        } else if (LoginHelper.hasRole(RoleCodeEnum.CountryStationAgent)) {
            recordBo.setTitle("总站");
        } else if (LoginHelper.anyMatchRole(RoleCodeEnum.ADMIN.getCode(), RoleCodeEnum.OPERATION_ADMIN.getCode(), RoleCodeEnum.CALL_ADMIN.getCode())) {
            recordBo.setTitle("平台运营");
        } else {
            recordBo.setTitle("未知");
        }
        sohuAuditRecordService.insertByBo(recordBo);
    }

    @Override
    public Long countByBusyCodeAndBusyType(Long objId, String objType, String state) {
        LambdaQueryWrapper<SohuAudit> query = new LambdaQueryWrapper<>();
        query.eq(SohuAudit::getBusyCode, objId);
        query.eq(SohuAudit::getBusyType, objType);
        if (Objects.nonNull(state)) {
            query.eq(SohuAudit::getSysAuditState, state);
        }
        return baseMapper.selectCount(query);
    }

    @Override
    public TableDataInfo<SohuAuditListVo> queryPageAuditList(SohuAuditBo bo, PageQuery pageQuery) {
        Page<SohuAuditVo> result = new Page<>();
        if (Objects.isNull(bo.getSysAuditState()) || bo.getSysAuditState().equals(CommonState.WaitApprove.getCode())) {
            LambdaQueryWrapper<SohuAudit> lqw = buildQueryWrapper(bo);
            result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        } else {
            List<SohuAuditVo> auditList = baseMapper.selectAuditPage(bo);
            if (CollectionUtil.isNotEmpty(auditList)) {
                List<SohuAuditVo> sohuAuditList = new ArrayList<>();
                if (Objects.isNull(bo.getId()) || bo.getId().equals(0L)) {
                    if (sohuAuditList.size() <= pageQuery.getPageSize()) {
                        sohuAuditList.addAll(auditList);
                    } else {
                        for (int i = 0; i < pageQuery.getPageSize(); i++) {
                            sohuAuditList.add(auditList.get(i));
                        }
                    }
                } else {
                    int index = -1;
                    for (int i = 0; i < auditList.size(); i++) {
                        if (auditList.get(i).getId().equals(bo.getId())) {
                            index = i;
                            break;
                        }
                    }
                    // 如果找到了目标记录
                    if (index != -1) {
                        // 获取目标记录后面的最多 count 条数据
                        for (int i = index + 1; i < Math.min(auditList.size(), index + 1 + pageQuery.getPageSize()); i++) {
                            sohuAuditList.add(auditList.get(i));
                        }
                    }
                }
                // 需要处理
                result.setTotal(auditList.size());
                result.setSize(pageQuery.getPageSize());
                result.setCurrent(pageQuery.getPageNum());
                result.setRecords(sohuAuditList);
            }
        }
        Page<SohuAuditListVo> auditResult = new Page<>();
        auditResult.setTotal(result.getTotal());
        auditResult.setSize(result.getSize());
        auditResult.setCurrent(result.getCurrent());
        List<SohuAuditListVo> auditList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(result.getRecords())) {
            for (SohuAuditVo sohuAuditVo : result.getRecords()) {
                SohuAuditListVo auditListVo = BeanUtil.toBean(sohuAuditVo, SohuAuditListVo.class);
                if (!auditListVo.getSysAuditState().equals(CommonState.WaitApprove.getCode())) {
                    // 查询审核记录
                    List<SohuAuditRecordVo> recordList = sohuAuditRecordMapper.selectVoList(Wrappers.<SohuAuditRecord>lambdaQuery()
                            .eq(SohuAuditRecord::getAuditId, sohuAuditVo.getId()).orderByDesc(SohuAuditRecord::getAuditTime));
                    auditListVo.setRecordList(recordList);
                }
                auditList.add(auditListVo);
            }
            auditResult.setRecords(auditList);
        }
        return TableDataInfoUtils.build(auditResult);
    }

    protected void syncContentUserBehavior(Long userId, String nickName, String businessType, String eventSign, String eventName, SohuUserBehaviorRecordPointBo.EventAttribute eventAttribute) {
        SohuUserBehaviorRecordPointBo pointBo = new SohuUserBehaviorRecordPointBo();
        pointBo.setUserId(userId);
        pointBo.setUserName(nickName);
        pointBo.setEventSign(eventSign);
        pointBo.setEventName(eventName);
        pointBo.setBusinessType(businessType);
        pointBo.setOperaType(OperaTypeEnum.ADD.getOperaType());
        pointBo.setOperaSource(Constants.ZERO);
        pointBo.setSourceType("HSS");
        pointBo.setRequestId(UUID.randomUUID().toString());
        pointBo.setEventAttribute(eventAttribute);
        remoteMiddleUserBehaviorRecordService.addList(Arrays.asList(pointBo));
    }
}
