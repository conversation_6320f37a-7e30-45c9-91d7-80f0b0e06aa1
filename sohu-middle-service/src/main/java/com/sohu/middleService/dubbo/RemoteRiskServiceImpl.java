package com.sohu.middleService.dubbo;

import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.BusyType;
import com.sohu.middle.api.bo.risk.*;
import com.sohu.middle.api.enums.DetectTypeEnum;
import com.sohu.middle.api.service.RemoteRiskService;
import com.sohu.middle.api.vo.*;
import com.sohu.middle.api.vo.risk.FieldDetectionInfo;
import com.sohu.middle.api.vo.risk.SohuRiskConfigVo;
import com.sohu.middle.api.vo.risk.SohuRiskRecordRelateVo;
import com.sohu.middle.api.vo.risk.SohuRiskRecordVo;
import com.sohu.middleService.service.*;
import com.sohu.middleService.service.risk.ISohuRiskRecordRelateService;
import com.sohu.middleService.service.risk.ISohuRiskRecordService;
import com.sohu.middleService.utils.RiskCheckUtils;
import com.sohu.middleService.utils.RiskExtractDetectionUtil;
import com.sohu.resource.api.RemoteFileService;
import com.sohu.shopgoods.api.RemoteProductCategoryPcService;
import com.sohu.shopgoods.api.RemoteProductService;
import com.sohu.shopgoods.api.vo.SohuProductVo;
import com.sohu.system.api.RemoteUserService;
import io.seata.common.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/6/16 19:52
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteRiskServiceImpl implements RemoteRiskService {

    private final static String callBackUrl = "";
    private final ISohuRiskRecordService sohuRiskRecordService;
    private final ISohuRiskRecordRelateService sohuRiskRecordRelateService;
    private final ISohuRiskConfigService sohuRiskConfigService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteMerchantService remoteMerchantService;
    @DubboReference
    private RemoteProductService remoteProductService;
    @DubboReference
    private RemoteProductCategoryPcService remoteProductCategoryPcService;
    @DubboReference
    private RemoteBusyTaskService remoteBusyTaskService;
    @DubboReference
    private RemoteFileService remoteFileService;

    private final ISohuVideoService sohuVideoService;
    private final ISohuQuestionService questionService;
    private final ISohuLiteratureService literatureService;
    private final ISohuArticleService articleService;
    private final ISohuAdInfoService adInfoService;

    @Override
    public Boolean syncCheck(RiskSyncCheckBo riskSyncCheckBo) {
        // 查询配置信息
        SohuRiskConfigVo sohuRiskConfigVo = queryConfigInfo(riskSyncCheckBo);
        if (Objects.isNull(sohuRiskConfigVo)) {
            log.error("未查询到配置信息");
            return Boolean.FALSE;
        }
        if (!DetectTypeEnum.TEXT.getCode().equals(sohuRiskConfigVo.getDetectType()) && !DetectTypeEnum.IMAGE.getCode().equals(sohuRiskConfigVo.getDetectType())) {
            throw new RuntimeException("同步类型暂只支持文本与图片");
        }
        SohuRiskRecordBo sohuRiskRecordBo = new SohuRiskRecordBo();
        sohuRiskRecordBo.setBusyType(riskSyncCheckBo.getBusyType());
        sohuRiskRecordBo.setBusyCode(riskSyncCheckBo.getBusyCode());
        Long recordId = sohuRiskRecordService.insertByBo(sohuRiskRecordBo);
        SohuRiskRecordRelateBo recordRelateBo = new SohuRiskRecordRelateBo();
        recordRelateBo.setRiskRecordId(recordId);
        recordRelateBo.setRiskConfigId(sohuRiskConfigVo.getId());
        recordRelateBo.setContent(riskSyncCheckBo.getContent());
        DetectTypeEnum value = DetectTypeEnum.getByCode(riskSyncCheckBo.getDetectType());
        switch (value) {
            case TEXT:
                RiskCheckUtils.handleSyncText(sohuRiskConfigVo, riskSyncCheckBo.getContent(), recordRelateBo);
                break;
            case IMAGE:
                RiskCheckUtils.handleSyncImage(sohuRiskConfigVo, riskSyncCheckBo.getContent(), recordRelateBo);
                break;
        }
        sohuRiskRecordRelateService.insertByBo(recordRelateBo);
        if (recordRelateBo.getStatus() == Constants.ZERO) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public void asyncCheck(RiskSyncCheckBo riskSyncCheckBo) {
        // 查询配置信息
        SohuRiskConfigVo sohuRiskConfigVo = queryConfigInfo(riskSyncCheckBo);
        if (Objects.isNull(sohuRiskConfigVo)) {
            log.error("未查询到配置信息");
        }
        RiskCheckBo riskCheckBo = new RiskCheckBo();
        riskCheckBo.setBusyType(riskSyncCheckBo.getBusyType());
        riskCheckBo.setBusyCode(riskSyncCheckBo.getBusyCode());
        List<RiskCheckBo.Content> contentList = new ArrayList<>();
        RiskCheckBo.Content content = new RiskCheckBo.Content();
        content.setDetectType(riskSyncCheckBo.getDetectType());
        content.setContent(riskSyncCheckBo.getContent());
        content.setConfigId(sohuRiskConfigVo.getId());
        content.setSecretId(sohuRiskConfigVo.getSecretId());
        content.setSecretKey(sohuRiskConfigVo.getSecretKey());
        content.setBusinessId(sohuRiskConfigVo.getBusinessId());
        contentList.add(content);
        riskCheckBo.setContentList(contentList);
        // 调用三方检测
        handleRisk(riskCheckBo);
    }

    @Override
    public Boolean handleRisk(BusyType busyType, String busyCode) {
        switch (busyType) {
            case User:
                // 查询用户信息
//                LoginUser loginUser = remoteUserService.selectById(Long.valueOf(busyCode));
//                if (Objects.nonNull(loginUser)) {
//                   this.executeRiskDetection(busyType.getType(), busyCode, loginUser);
//                }
                break;
            case Video:
                // 查询短视频信息
                SohuVideoVo sohuVideoVo = sohuVideoService.queryById(Long.valueOf(busyCode));
                if (Objects.nonNull(sohuVideoVo)) {
                    this.executeRiskDetection(busyType.getType(), busyCode, sohuVideoVo);
                }
                break;
            case Shop:
                // 查询店铺信息
                SohuMerchantVo merchantVo = remoteMerchantService.getInfoById(Long.valueOf(busyCode));
                if (Objects.nonNull(merchantVo)) {
                    this.executeRiskDetection(busyType.getType(), busyCode, merchantVo);
                }
                break;
            case Question:
                // 查询问答信息
                SohuQuestionVo questionVo = questionService.get(Long.valueOf(busyCode));
                if (Objects.nonNull(questionVo)) {
                    this.executeRiskDetection(busyType.getType(), busyCode, questionVo);
                }
                break;
            case Poetry:
                // 查询诗歌信息
            case Prose:
                // 查询散文信息
                SohuLiteratureVo literatureVo = literatureService.queryById(Long.valueOf(busyCode));
                if (Objects.nonNull(literatureVo)) {
                    this.executeRiskDetection(busyType.getType(), busyCode, literatureVo);
                }
                break;
            case Novel:
                // 查询小说信息 TODO 小说不走许愿狐系统待定
                break;
            case Goods:
                // 查询商品信息
                SohuProductVo productVo = remoteProductService.getInfoById(Long.valueOf(busyCode));
                if (Objects.nonNull(productVo)) {
                    this.executeRiskDetection(busyType.getType(), busyCode, productVo);
                }
                break;
            case BusyTask:
                // 查询商单信息
//                remoteBusyTaskService.queryById
            case Article:
                // 查询图文信息
                SohuArticleVo sohuArticleVo = articleService.queryById(Long.valueOf(busyCode));
                if (Objects.nonNull(sohuArticleVo)) {
                    this.executeRiskDetection(busyType.getType(), busyCode, sohuArticleVo);
                }
                break;
            case AdInfo:
                // 查询广告信息
                SohuAdInfoVo sohuAdInfoVo = adInfoService.queryById(Long.valueOf(busyCode));
                if (Objects.nonNull(sohuAdInfoVo)) {
                    this.executeRiskDetection(busyType.getType(), busyCode, sohuAdInfoVo);
                }
                break;
            default:
        }
        return Boolean.TRUE;
    }

    @Override
    public void handleRiskResult(Long recordId) {
        SohuRiskRecordVo sohuRiskRecordVo = sohuRiskRecordService.queryById(recordId);
        if (Objects.isNull(sohuRiskRecordVo)) {
            log.info("检测记录不存在,recordId:{}", recordId);
            return;
        }
        // 查询检测明细
        List<SohuRiskRecordRelateVo> relateVoList = sohuRiskRecordRelateService.queryListByRecordId(recordId);
        if (CollectionUtils.isEmpty(relateVoList)) {
            log.info("检测记录明细不存在,recordId:{}", recordId);
            return;
        }
    }

    private SohuRiskConfigVo queryConfigInfo(RiskSyncCheckBo riskSyncCheckBo) {
        SohuRiskConfigBo sohuRiskConfigBo = new SohuRiskConfigBo();
        sohuRiskConfigBo.setBusyType(riskSyncCheckBo.getBusyType());
        sohuRiskConfigBo.setDetectType(riskSyncCheckBo.getDetectType());
        sohuRiskConfigBo.setFieldName(riskSyncCheckBo.getFieldName());
        sohuRiskConfigBo.setPlatform(riskSyncCheckBo.getPlatform());
        return sohuRiskConfigService.queryByBo(sohuRiskConfigBo);
    }

    /**
     * 分析并检测
     *
     * @param busyType
     * @param busyCode
     * @param object
     */
    public void executeRiskDetection(String busyType, String busyCode, Object object) {
        // 获取对象所有需要检测的字段
        List<FieldDetectionInfo> fieldInfos = RiskExtractDetectionUtil.extractDetectionFields(busyType, object);
        if (CollectionUtils.isEmpty(fieldInfos)) {
            log.info("busyType:{},busyCode:{},无待风控检测字段", busyType, busyCode);
            return;
        }
        RiskCheckBo checkBo = new RiskCheckBo();
        checkBo.setBusyCode(busyCode);
        checkBo.setBusyType(busyType);
        List<RiskCheckBo.Content> contentList = new ArrayList<>();
        for (FieldDetectionInfo fieldInfo : fieldInfos) {
            // 查询该字段的风控配置
            SohuRiskConfigBo sohuRiskConfigBo = new SohuRiskConfigBo();
            sohuRiskConfigBo.setBusyType(fieldInfo.getBusyType());
            sohuRiskConfigBo.setDetectType(fieldInfo.getDetectType());
            sohuRiskConfigBo.setPlatform(Constants.SOHUGLOBAL);
            sohuRiskConfigBo.setFieldCode(fieldInfo.getFieldCode());
            SohuRiskConfigVo sohuRiskConfigVo = sohuRiskConfigService.queryByBo(sohuRiskConfigBo);
            if (Objects.nonNull(sohuRiskConfigVo)) {
                // 组装调用对象
                RiskCheckBo.Content content = new RiskCheckBo.Content();
                content.setDetectType(fieldInfo.getDetectType());
                content.setContent(fieldInfo.getFieldValue());
                content.setConfigId(sohuRiskConfigVo.getId());
                content.setSecretId(sohuRiskConfigVo.getSecretId());
                content.setSecretKey(sohuRiskConfigVo.getSecretKey());
                content.setBusinessId(sohuRiskConfigVo.getBusinessId());
                contentList.add(content);
            }
        }
        checkBo.setContentList(contentList);
        // 调用三方检测
        handleRisk(checkBo);
    }

    /**
     * 通用校验方法
     *
     * @param riskCheckBo
     */
    private void handleRisk(RiskCheckBo riskCheckBo) {
        SohuRiskRecordBo sohuRiskRecordBo = new SohuRiskRecordBo();
        sohuRiskRecordBo.setBusyType(riskCheckBo.getBusyType());
        sohuRiskRecordBo.setBusyCode(riskCheckBo.getBusyCode());
        Long recordId = sohuRiskRecordService.insertByBo(sohuRiskRecordBo);
        List<RiskCheckBo.Content> contentList = riskCheckBo.getContentList();
        List<SohuRiskRecordRelateBo> recordRelateBoList = new ArrayList<>();
        for (RiskCheckBo.Content content : contentList) {
            DetectTypeEnum value = DetectTypeEnum.getByCode(content.getDetectType());
            SohuRiskRecordRelateBo recordRelateBo = new SohuRiskRecordRelateBo();
            recordRelateBo.setRiskRecordId(recordId);
            recordRelateBo.setRiskConfigId(content.getConfigId());
            recordRelateBo.setContent(content.getContent());
            switch (value) {
                case TEXT:
                    RiskCheckUtils.handleText(content, recordRelateBo);
                    break;
                case IMAGE:
                    RiskCheckUtils.handleImage(content, recordRelateBo);
                    break;
                case AUDIO:
                    RiskCheckUtils.handleAudio(content, recordRelateBo);
                    break;
                case VIDEO:
                    RiskCheckUtils.handleVideo(content, recordRelateBo);
                    break;
                case Link:
                    RiskCheckUtils.handleLink(content, recordRelateBo);
                    break;
                case FILE:
                    RiskCheckUtils.handleFile(content, recordRelateBo);
                    break;
            }
            recordRelateBoList.add(recordRelateBo);
        }
        sohuRiskRecordRelateService.insertBatch(recordRelateBoList);
    }
}
