package com.sohu.middleService.dubbo;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuIndependentMaterialBo;
import com.sohu.middle.api.bo.SohuIndependentMaterialUserBo;
import com.sohu.middle.api.service.RemoteMiddleIndependentMaterialService;
import com.sohu.middle.api.vo.SohuIndependentMaterialUserVo;
import com.sohu.middle.api.vo.SohuIndependentMaterialVo;
import com.sohu.middleService.service.ISohuIndependentMaterialService;
import com.sohu.middleService.service.ISohuIndependentMaterialUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleIndependentMaterialServiceImpl implements RemoteMiddleIndependentMaterialService {

    private final ISohuIndependentMaterialService sohuIndependentMaterialService;
    private final ISohuIndependentMaterialUserService sohuIndependentMaterialUserService;

    @Override
    public SohuIndependentMaterialVo queryById(Long id,Boolean isMe) {
        return sohuIndependentMaterialService.queryById(id,isMe);
    }

    @Override
    public TableDataInfo<SohuIndependentMaterialVo> queryPageList(SohuIndependentMaterialBo bo, PageQuery pageQuery) {
        return sohuIndependentMaterialService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<SohuIndependentMaterialVo> queryList(SohuIndependentMaterialBo bo) {
        return sohuIndependentMaterialService.queryList(bo);
    }

    @Override
    public Boolean insertByBo(SohuIndependentMaterialBo bo) {
        return sohuIndependentMaterialService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(SohuIndependentMaterialBo bo) {
        return sohuIndependentMaterialService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return sohuIndependentMaterialService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public Boolean addMaterial(SohuIndependentMaterialUserBo bo) {
        return sohuIndependentMaterialUserService.insertByBo(bo);
    }

    @Override
    public TableDataInfo<SohuIndependentMaterialVo> myMaterialList(SohuIndependentMaterialBo bo, PageQuery pageQuery) {
        return sohuIndependentMaterialService.myMaterialList(bo, pageQuery);
    }

    @Override
    public SohuIndependentMaterialUserVo queryMaterialUser(Long materialId, Long materialShareUserId) {
        return sohuIndependentMaterialUserService.queryMaterialUser(materialId, materialShareUserId);
    }

    @Override
    public SohuIndependentMaterialVo queryByCodeAndType(String materialCode, String materialType) {
        return sohuIndependentMaterialService.queryByCodeAndType(materialCode, materialType);
    }

    @Override
    public SohuIndependentMaterialVo queryByFlowTask(String materialCode, String materialType) {
        return sohuIndependentMaterialService.queryByFlowTask(materialCode, materialType);
    }

    @Override
    public Boolean deleteByCodeAndType(String materialCode, String materialType) {
        return sohuIndependentMaterialService.deleteByCodeAndType(materialCode, materialType);
    }

    @Override
    public Boolean deleteByCodeAndType(String materialCode, String materialType, Long userId) {
        return sohuIndependentMaterialService.deleteByCodeAndType(materialCode, materialType, userId);
    }

    @Override
    public Boolean checkData(Long materialId, Long materialShareUserId) {
        return sohuIndependentMaterialService.checkData(materialId, materialShareUserId);
    }
}
