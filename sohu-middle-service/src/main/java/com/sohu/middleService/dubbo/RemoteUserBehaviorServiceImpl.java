package com.sohu.middleService.dubbo;

import com.sohu.middle.api.service.RemoteUserBehaviorService;
import com.sohu.middle.api.vo.UserBehaviorGroupInfoVo;
import com.sohu.middleService.service.ISohuUserBehaviorRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25 10:01
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteUserBehaviorServiceImpl implements RemoteUserBehaviorService {

    private final ISohuUserBehaviorRecordService sohuUserBehaviorService;

    @Override
    public List<UserBehaviorGroupInfoVo> list(String sourceType, List<String> eventSigns, Date startTime, Date endTime) {
        return sohuUserBehaviorService.groupList(sourceType, eventSigns, startTime, endTime);
    }
}
