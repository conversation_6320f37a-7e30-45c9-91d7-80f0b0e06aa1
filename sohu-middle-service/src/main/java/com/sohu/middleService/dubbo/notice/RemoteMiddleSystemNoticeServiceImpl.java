package com.sohu.middleService.dubbo.notice;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.common.core.constant.UserConstants;
import com.sohu.common.core.enums.SysNoticeEnum;
import com.sohu.common.core.enums.SystemNoticeEnum;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.im.api.noticeContent.NoticeContentDetail;
import com.sohu.im.api.noticeContent.NoticeSystemContent;
import com.sohu.middle.api.bo.notice.SohuSystemNoticeBo;
import com.sohu.middle.api.service.notice.RemoteMiddleSystemNoticeService;
import com.sohu.middle.api.vo.notice.SohuSystemNoticeVo;
import com.sohu.middleService.service.ISohuSystemNoticeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * 系统通知
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleSystemNoticeServiceImpl implements RemoteMiddleSystemNoticeService {

    private final ISohuSystemNoticeService sohuSystemNoticeService;

    @Override
    public SohuSystemNoticeVo queryById(Long id) {
        return sohuSystemNoticeService.queryById(id);
    }

    @Override
    public TableDataInfo<SohuSystemNoticeVo> queryPageList(SohuSystemNoticeBo bo, PageQuery pageQuery) {
        return sohuSystemNoticeService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<SohuSystemNoticeVo> queryList(SohuSystemNoticeBo bo) {
        return sohuSystemNoticeService.queryList(bo);
    }

    @Override
    public Boolean insertByBo(SohuSystemNoticeBo bo) {
        return sohuSystemNoticeService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(SohuSystemNoticeBo bo) {
        return sohuSystemNoticeService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return sohuSystemNoticeService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public List<SohuSystemNoticeVo> noticeList() {
        return sohuSystemNoticeService.noticeList();
    }

    /**
     * 发送通知
     *
     * @param senderId   发送人ID，一般是超管ID
     * @param receiverId 接收人id，即用户ID
     * @param title      标题
     * @param content    内容，一般是json 格式
     * @param type       类型{@link SystemNoticeEnum}
     */
    @Override
    public void sendNotice(Long senderId, Long receiverId, String title, String content, SystemNoticeEnum.Type type) {
        sohuSystemNoticeService.sendNotice(senderId, receiverId, title, content, type.name());
    }

    /**
     * 发送通知
     *
     * @param receiverId 接收人id，即用户ID
     * @param title      标题
     * @param content    内容，一般是json 格式
     * @param type       类型{@link SystemNoticeEnum}
     */
    @Override
    public void sendAdminNotice(Long receiverId, String title, String content, SystemNoticeEnum.Type type) {
        sohuSystemNoticeService.sendNotice(UserConstants.ADMIN_ID, receiverId, title, content, type.name());
    }

    @Override
    public void sendSystemNotice(Long receiverId, Long linkDetaiId, SysNoticeEnum sysNoticeEnum, List<String> params) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(sysNoticeEnum.getTitle());
        content.setNoticeTime(DateUtils.getTime());
        content.setType(sysNoticeEnum.getType());
        content.setDetailId(linkDetaiId == null ? receiverId : linkDetaiId);
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setUserId(receiverId);
        if (CollUtil.isNotEmpty(params)) {
            String[] arr = params.toArray(new String[0]);
            detail.setDesc(StrUtil.format(sysNoticeEnum.getContent(), arr));
        } else {
            detail.setDesc(sysNoticeEnum.getContent());
        }
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        sohuSystemNoticeService.sendNotice(UserConstants.ADMIN_ID, receiverId, sysNoticeEnum.getTitle(), contentJson, sysNoticeEnum.getType());
    }

}
