package com.sohu.middleService.dubbo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.SiteType;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.ServletUtils;
import com.sohu.common.core.web.domain.SohuBaseBo;
import com.sohu.common.core.web.domain.SohuBaseVo;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.service.RemoteMiddleService;
import com.sohu.middle.api.vo.*;
import com.sohu.middle.api.vo.airec.SohuAirecContentItemVo;
import com.sohu.middleService.domain.bo.SohuAirecArticleQueryBo;
import com.sohu.middleService.domain.bo.SohuAirecContentQueryBo;
import com.sohu.middleService.service.ISohuArticleInfoService;
import com.sohu.middleService.service.ISohuCategoryService;
import com.sohu.middleService.service.ISohuContentService;
import com.sohu.middleService.strategy.MiddleProcessor;
import com.sohu.middleService.strategy.MiddleStrategy;
import com.sohu.third.aliyun.airec.constants.AliyunAirecConstant;
import com.sohu.third.aliyun.airec.domain.AliyunAirecJoinFilterRule;
import com.sohu.third.aliyun.airec.util.AliyunAirecUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 公共业务接口实现
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleServiceImpl<B extends SohuBaseBo, V extends SohuBaseVo> implements RemoteMiddleService<B, V> {

    @Resource
    private MiddleProcessor processor;
    @Resource
    private ISohuArticleInfoService sohuArticleInfoService;
    @Resource
    private ISohuContentService sohuContentService;
    @Resource
    private ISohuCategoryService categoryService;

    @Override
    public Boolean add(B entity) {
        return this.getStrategy(entity.getSohuBusyType()).add(entity);
    }

    @Override
    public Boolean update(B entity) {
        return this.getStrategy(entity.getSohuBusyType()).update(entity);
    }

    @Override
    public V query(BusyType busyType, Long id) {
        return this.getStrategy(busyType).query(id);
    }

    @Override
    public Boolean delete(BusyType busyType, Long id) {
        return this.getStrategy(busyType).delete(id);
    }

    @Override
    public Boolean delete(BusyType busyType, Collection<Long> ids) {
        return this.getStrategy(busyType).delete(ids);
    }

    @Override
    public Boolean comment(SohuCommentBo bo) {
        return this.getStrategy(bo.getSohuBusyType()).comment(bo, Boolean.TRUE);
    }

    @Override
    public Boolean like(SohuBusyBO bo) {
        return this.getStrategy(bo.getBusyType()).like(bo);
    }

    @Override
    public Boolean collect(SohuBusyBO bo) {
        return this.getStrategy(bo.getBusyType()).collect(bo);
    }

    @Override
    public Boolean share(BusyType busyType, Long id) {
        return this.getStrategy(busyType).share(id);
    }

    @Override
    public Boolean report(SohuReportInfoBo bo) {
        return null;
    }

    @Override
    public TableDataInfo<V> queryPageList(B bo, PageQuery pageQuery) {
        return this.getStrategy(bo.getSohuBusyType()).queryPageList(bo, pageQuery);
    }

    @Override
    public List<V> queryList(B bo) {
        return this.getStrategy(bo.getSohuBusyType()).queryList(bo);
    }

    @Override
    public SohuContentMainVo queryObj(BusyType busyType, Long id, Boolean queryMoreInfo) {
        if (busyType == null || (id == null || id <= 0L)) {
            return null;
        }
        SohuContentMainVo mainVo = new SohuContentMainVo();
        mainVo.setObjId(id);
        mainVo.setObjType(busyType.name());
        switch (busyType) {
            case Article:
                SohuArticleVo vo = (SohuArticleVo) query(BusyType.Article, id);
                mainVo.setUserId(vo.getUserId());
                mainVo.setObjTitle(vo.getTitle());
                mainVo.setCategory(vo.getCategoryId());
                if (queryMoreInfo != null && queryMoreInfo) {
                    SohuArticleInfoVo infoVo = sohuArticleInfoService.queryByArticleId(id);
                    if (Objects.nonNull(infoVo)) {
                        mainVo.setObjContent(infoVo.getContent());
                        mainVo.setViewCount(infoVo.getViewCount());
                        mainVo.setPraiseCount(infoVo.getPraiseCount());
                        mainVo.setCollectCount(infoVo.getCollectCount());
                        mainVo.setCommentCount(infoVo.getCommentCount());
                    }
                }
                return null;
            case Video:
                return null;
            case Question:
                return null;
            case Answer:
                return null;
            case Playlet:
                return null;
            case PlayletVideo:
                return null;
            default:
                return null;
        }
    }

    private MiddleStrategy<B, V> getStrategy(BusyType busyType) {
        return processor.getStrategy(busyType);
    }

    @Override
    public TableDataInfo<SohuContentVo> queryPageOfAirec(SohuContentListBo bo, PageQuery pageQuery) {
        SohuAirecContentQueryBo airecQueryBo = new SohuAirecContentQueryBo();
//        if (bo.getAiRec()) {
//            if (StrUtil.isBlank(bo.getAiRecImei()) && Objects.isNull(LoginHelper.getUserId())) {
//                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
//            }
//            if (!(AliyunAirecConstant.SCENE_ARTICLE_ALL.equals(bo.getAiRecSceneId())
//                    || AliyunAirecConstant.SCENE_ARTICLE_HOMEPAGE.equals(bo.getAiRecSceneId())
//                    || AliyunAirecConstant.SCENE_ARTICLE_MONEYMAKING.equals(bo.getAiRecSceneId()))) {
//                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
//            }
//            if (Objects.nonNull(LoginHelper.getUserId())) {
//                bo.setAiUserId(LoginHelper.getUserId().toString());
//            }
//            bo.setAiReturnCount(pageQuery.getPageSize());
//            //构造过滤的内容
//            List<String> categoryPathList = new ArrayList<>();
//            if (CollectionUtils.isNotEmpty(bo.getCategoryIds())) {
//                for (Long categoryId : bo.getCategoryIds()) {
//                    SohuAirecContentItemVo airecCategoryInfo = categoryService.getAirecCategoryInfoById(categoryId);
//                    categoryPathList.add(airecCategoryInfo.getCategoryPath());
//                }
//            }
//            airecQueryBo.setCategoryPathList(categoryPathList);
//            AliyunAirecJoinFilterRule rootRule = airecQueryBo.buildAirecFilterRule();
//            //获取阿里云智能推荐结果
//            List<SohuContentMainVo> resultList = AliyunAirecUtil.aiRecommendSingleType(rootRule, bo, itemIds -> {
//                return remoteMiddleContentMainService.listByObj(itemIds, BusyType.Video.name());
//            });
//
//
//            List<SohuContentVo> resultList = AliyunAirecUtil.aiRecommendSingleType(rootRule, bo, itemIds -> .selectVoBatchIds(itemIds));
//            if (CollUtil.isNotEmpty(resultList)) {
//                resultList = getRecord(resultList, LoginHelper.getUserId());
//                return TableDataInfoUtils.build(resultList);
//            }
//        }


        TableDataInfo<SohuContentVo> tableDataInfo = sohuContentService.queryPage(bo, pageQuery);
        return tableDataInfo;
    }

}
