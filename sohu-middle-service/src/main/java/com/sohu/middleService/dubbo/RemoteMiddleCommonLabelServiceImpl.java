package com.sohu.middleService.dubbo;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.service.RemoteMiddleCommonLabelService;
import com.sohu.middle.api.vo.SohuCommonLabelListVo;
import com.sohu.middle.api.vo.SohuCommonLabelVo;
import com.sohu.middle.api.vo.SohuUserLabelRelationVo;
import com.sohu.middleService.service.ISohuCommonLabelService;
import com.sohu.middleService.service.ISohuUserLabelRelationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * #通用标签Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleCommonLabelServiceImpl implements RemoteMiddleCommonLabelService {

    private final ISohuCommonLabelService iSohuCommonLabelService;
    private final ISohuUserLabelRelationService iSohuUserLabelRelationService;

    @Override
    public List<SohuCommonLabelListVo> queryPageList() {
        return iSohuCommonLabelService.queryPageList();
    }

    @Override
    public TableDataInfo<SohuCommonLabelVo> queryPageList(SohuCommonLabelListBo bo, PageQuery pageQuery) {
        return iSohuCommonLabelService.queryPageList(bo, pageQuery);
    }

    @Override
    public Boolean insertByBo(SohuCommonLabelBo bo) {
        return iSohuCommonLabelService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(SohuCommonLabelBo bo) {
        return iSohuCommonLabelService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return iSohuCommonLabelService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public void insertBatch(List<SohuUserLabelRelationBo> userLabelList) {
        iSohuUserLabelRelationService.batchInsert(userLabelList);
    }

    @Override
    public Map<Long, String> queryLabelNamesByIds(List<Long> ids) {
        return iSohuCommonLabelService.queryLabelNamesByIds(ids);
    }

    @Override
    public List<SohuCommonLabelListVo> queryLabelNameListByIds(List<Long> ids) {
        return iSohuCommonLabelService.queryLabelNameListByIds(ids);
    }

    @Override
    public List<SohuUserLabelRelationVo> queryLabelsByUserId(Long userId) {
        return iSohuUserLabelRelationService.queryLabelsByUserId(userId);
    }

    @Override
    public void deleteUserLabelByUserId(Long userId, String labelType) {
        iSohuUserLabelRelationService.deleteUserLabelByUserId(userId, labelType);
    }

    @Override
    public Boolean bindUserLabelWithClick(SohuBindUserLabelBo bo) {
        return iSohuUserLabelRelationService.bindUserLabelWithClick(bo);
    }

    @Override
    public void batchInsert(SohuSaveUserLabelRelationBo bo) {

    }
}
