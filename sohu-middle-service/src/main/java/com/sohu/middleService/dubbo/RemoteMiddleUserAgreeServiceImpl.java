package com.sohu.middleService.dubbo;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuUseBo;
import com.sohu.middle.api.bo.SohuUserAgreeBo;
import com.sohu.middle.api.bo.SohuUserAgreeQueryBo;
import com.sohu.middle.api.service.RemoteMiddleUserAgreeService;
import com.sohu.middle.api.vo.SohuUserAgreeInfoVo;
import com.sohu.middle.api.vo.SohuUserAgreeListVo;
import com.sohu.middle.api.vo.SohuUserAgreeVo;
import com.sohu.middleService.service.ISohuUserAgreeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 用户协议
 *
 * <AUTHOR>
 * @date 2024-09-23
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleUserAgreeServiceImpl implements RemoteMiddleUserAgreeService {

    private final ISohuUserAgreeService sohuUserAgreeService;

    @Override
    public SohuUserAgreeVo queryById(Long id) {
        return sohuUserAgreeService.queryById(id);
    }

    @Override
    public TableDataInfo<SohuUserAgreeListVo> queryPageList(SohuUserAgreeQueryBo bo, PageQuery pageQuery) {
        return sohuUserAgreeService.queryPageList(bo, pageQuery);
    }

    @Override
    public Boolean insertByBo(SohuUserAgreeBo bo) {
        return sohuUserAgreeService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(SohuUserAgreeBo bo) {
        return sohuUserAgreeService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return sohuUserAgreeService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public Boolean updateState(SohuUseBo bo) {
        return sohuUserAgreeService.updateState(bo);
    }

    @Override
    public List<SohuUserAgreeVo> queryList(SohuUserAgreeQueryBo bo) {
        return sohuUserAgreeService.queryList(bo);
    }

    @Override
    public SohuUserAgreeInfoVo getInfoById(Long id) {
        return sohuUserAgreeService.getInfoById(id);
    }
}
