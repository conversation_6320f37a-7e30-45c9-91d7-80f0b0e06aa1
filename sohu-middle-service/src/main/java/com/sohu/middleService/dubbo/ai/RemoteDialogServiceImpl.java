package com.sohu.middleService.dubbo.ai;

import cn.hutool.core.bean.BeanUtil;
import com.sohu.busyorder.api.enums.BusyTaskTypeEnum;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.middle.api.bo.ai.SohuDialogBo;
import com.sohu.middle.api.bo.ai.SohuDialogRecordBo;
import com.sohu.middle.api.enums.ai.DialogConfigEnum;
import com.sohu.middle.api.service.RemoteDialogService;
import com.sohu.middle.api.vo.SohuCategoryVo;
import com.sohu.middle.api.vo.SohuIndustryCategoryVo;
import com.sohu.middle.api.vo.ai.AiAnalysisVo;
import com.sohu.middle.api.vo.ai.SohuDialogInfoVo;
import com.sohu.middle.api.vo.ai.SohuDialogRecordVo;
import com.sohu.middle.api.vo.ai.SohuDialogVo;
import com.sohu.middleService.service.ISohuCategoryService;
import com.sohu.middleService.service.ISohuIndustryCategoryService;
import com.sohu.middleService.service.ai.ISohuDialogRecordService;
import com.sohu.middleService.service.ai.ISohuDialogService;
import com.sohu.middleService.utils.AnalysisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @Author: leibo
 * @Date: 2025/3/5 14:40
 **/
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteDialogServiceImpl implements RemoteDialogService {

    private final ISohuDialogService sohuDialogService;
    private final ISohuDialogRecordService sohuDialogRecordService;
    private final ISohuIndustryCategoryService sohuIndustryCategoryService;
    private final ISohuCategoryService sohuCategoryService;

    @Override
    public SohuDialogInfoVo getDialogInfo(String busyType) {
        return sohuDialogService.getDialogInfo(busyType);
    }

    @Override
    public List<SohuDialogRecordVo> listRecord(Long dialogId, Long userId) {
        return sohuDialogRecordService.listByDialogIdAndUser(dialogId, userId);
    }

    @Override
    public SohuDialogRecordVo recordInfo(Long recordId) {
        return sohuDialogRecordService.queryById(recordId);
    }

    @Override
    public AiAnalysisVo analysisRecord(Long recordId, String busyCode) {
        SohuDialogRecordVo recordVo = this.recordInfo(recordId);
        AiAnalysisVo aiAnalysisVo = new AiAnalysisVo();
        if (Objects.nonNull(recordVo)) {
            if (busyCode.equals(DialogConfigEnum.TASK.name())) {
                // 进行数据解析
                AiAnalysisVo.BusyTaskVo busyTaskVo = AnalysisUtils.buildBusyTask(recordVo.getAnswer());
                if (Objects.nonNull(busyTaskVo) && StringUtils.isEmpty(busyTaskVo.getTitle())) {
                    busyTaskVo = AnalysisUtils.buildBusyTaskNew(recordVo.getAnswer());
                }
                if (Objects.nonNull(busyTaskVo)) {
                    SohuIndustryCategoryVo industryCategory = sohuIndustryCategoryService.queryByName(busyTaskVo.getIndustryName());
                    if (Objects.nonNull(industryCategory)) {
                        busyTaskVo.setPid(industryCategory.getPid());
                        busyTaskVo.setIndustryType(industryCategory.getId());
                    }
                    // 设置分类
                    SohuCategoryVo categoryVo = sohuCategoryService.queryByName(busyTaskVo.getCategoryName());
                    if (Objects.nonNull(categoryVo)) {
                        busyTaskVo.setCategoryId(categoryVo.getId());
                    }
                    // 查询通用商单类型
                    SohuCategoryVo sohuCategoryVo = sohuCategoryService.queryConstMark(BusyTaskTypeEnum.COMMON_TASK.getCode());
                    if (Objects.nonNull(sohuCategoryVo)) {
                        busyTaskVo.setType(sohuCategoryVo.getId());
                        busyTaskVo.setTypeName(sohuCategoryVo.getName());
                    }
                    // 查询愿望分类
                    SohuCategoryVo categoryTypeVo = sohuCategoryService.queryByName(busyTaskVo.getCategoryTypeName());
                    if (Objects.nonNull(categoryTypeVo) && categoryTypeVo.getBusyType().equals("BusyTaskCategory")) {
                        busyTaskVo.setCategoryType(categoryTypeVo.getId());
                    }
                    // 查询愿望类型
                    SohuCategoryVo supplyTypeVo = sohuCategoryService.queryByName(busyTaskVo.getSupplyTypeName());
                    if (Objects.nonNull(supplyTypeVo) && supplyTypeVo.getBusyType().equals("BusyTaskSupply")) {
                        busyTaskVo.setSupplyType(supplyTypeVo.getId());
                    }
                }
                aiAnalysisVo.setBusyTaskVo(busyTaskVo);
            }
        }
        return aiAnalysisVo;
    }

    @Override
    public Boolean insertBo(SohuDialogBo sohuDialog) {
        return sohuDialogService.insertByBo(sohuDialog);
    }

    @Override
    public Boolean insertRecord(SohuDialogRecordBo record) {
        return sohuDialogRecordService.insertByBo(record);
    }

    @Override
    public Boolean updateRecord(SohuDialogRecordVo record) {
        SohuDialogRecordBo recordBo = BeanUtil.toBean(record, SohuDialogRecordBo.class);
        return sohuDialogRecordService.updateByBo(recordBo);
    }

    @Override
    public SohuDialogVo getNearDialog(Long userId) {
        return sohuDialogService.getNearDialog(userId);
    }

    @Override
    public List<SohuDialogRecordVo> listRecord(Long dialogId) {
        return sohuDialogRecordService.listByDialogId(dialogId);
    }

    @Override
    public SohuDialogRecordVo getNearDialogRecord(Long dialogId, Long userId) {
        return sohuDialogRecordService.getNearDialogRecord(dialogId, userId);
    }

    @Override
    public Boolean deleteByIds(List<Long> ids) {
        // 删除对话框
        sohuDialogService.deleteWithValidByIds(ids, Boolean.TRUE);
        // 删除对话记录
        return sohuDialogRecordService.deleteByDialogIds(ids);
    }

    @Override
    public List<SohuDialogVo> listByDialogName(String dialogName, String busyType) {
        return sohuDialogService.listByDialogName(dialogName, busyType);
    }

}
