package com.sohu.middleService.dubbo;

import com.sohu.middle.api.bo.SohuTopicContentQueryBo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.mcn.SohuMcnArticleReqBo;
import com.sohu.middle.api.service.RemoteMiddleArticleService;
import com.sohu.middle.api.vo.SohuArticleVo;
import com.sohu.middle.api.vo.SohuConentListStatVo;
import com.sohu.middle.api.vo.SohuTopArticleVo;
import com.sohu.middleService.service.ISohuArticleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleArticleServiceImpl implements RemoteMiddleArticleService {

    private final ISohuArticleService sohuArticleService;


    @Override
    public TableDataInfo<SohuArticleVo> businessArticleListOfAirec(SohuBusinessArticleBo bo) {
        return sohuArticleService.businessArticleListOfAirec(bo);
    }

    @Override
    public TableDataInfo<SohuArticleVo> queryPageOfAirec(SohuArticleBo bo, PageQuery pageQuery) {
        return sohuArticleService.queryPageOfAirec(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuArticleVo> articlePageCenter(Long userId, PageQuery pageQuery) {
        return sohuArticleService.articlePageCenter(userId, pageQuery);
    }

    @Override
    public TableDataInfo<SohuArticleVo> followPage(PageQuery pageQuery) {
        return sohuArticleService.followPage(pageQuery);
    }

    @Override
    public List<SohuTopArticleVo> labelTopFive() {
        return sohuArticleService.labelTopFive();
    }

    @Override
    public Boolean insertByBo(SohuArticleBo bo) {
        return sohuArticleService.insertByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return sohuArticleService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public TableDataInfo<SohuArticleVo> articlePageCenterByType(SohuArticleBo bo, PageQuery pageQuery) {
        return sohuArticleService.articlePageCenterByType(bo, pageQuery);
    }

    @Override
    public void buildRelate(SohuArticleVo articleVo) {
        sohuArticleService.buildRelate(articleVo);
    }

    @Override
    public Boolean draftRetry(SohuBusyBO busyBO) {
        return sohuArticleService.draftRetry(busyBO);
    }

    @Override
    public Boolean forward(SohuBusyBO bo) {
        return sohuArticleService.forward(bo);
    }

    @Override
    public void commit(Long articleId) {
        //sohuArticleService.commit(articleId);
        sohuArticleService.submitAudit(articleId);
    }

    @Override
    public TableDataInfo<SohuArticleVo> queryMCNArticleList(SohuMcnArticleReqBo bo, PageQuery pageQuery) {
        return sohuArticleService.queryMCNArticleList(bo, pageQuery);
    }

    @Override
    public Long getUserIdByPublishMediaId(String publishMediaId) {
        return sohuArticleService.getUserIdByPublishMediaId(publishMediaId);
    }

    @Override
    public Boolean initAirecContentItems() {
        return sohuArticleService.initAirecContentItems();
    }

    @Override
    public Boolean updateUserArticleState(Long userId, String state) {
        return sohuArticleService.updateUserArticleState(userId, state);
    }

    @Override
    public Boolean updateBatchContentState(SohuContentBatchBo bo) {
        return sohuArticleService.updateBatchContentState(bo);
    }

    @Override
    public Boolean userAppeal(SohuUserContentAppealBo bo) {
        return sohuArticleService.userAppeal(bo);
    }

    @Override
    public Boolean submitAudit(Long id) {
        return sohuArticleService.submitAudit(id);
    }

    @Override
    public boolean updateOffShelfById(Long id) {
        return sohuArticleService.updateOffShelfById(id);
    }

    @Override
    public Boolean recoveryData(Long id) {
        return sohuArticleService.recoveryData(id);
    }

    @Override
    public Boolean deleteDataById(Long id) {
        return sohuArticleService.deleteDataById(id);
    }

    @Override
    public boolean updateCompelOffById(SohuContentRefuseBo bo) {
        return sohuArticleService.updateCompelOffById(bo);
    }

    @Override
    public boolean logicForceDeleteById(Collection<Long> ids) {
        return sohuArticleService.logicForceDeleteById(ids);
    }

    @Override
    public Boolean hideDataBatch(Collection<Long> ids) {
        return sohuArticleService.hideDataBatch(ids);
    }

    @Override
    public SohuConentListStatVo queryPageListStat(SohuArticleBo bo) {
        return sohuArticleService.queryPageListStat(bo);
    }

    @Override
    public Boolean clearRecycleDataOfTimeOut() {
        return sohuArticleService.clearRecycleDataOfTimeOut();
    }

    @Override
    public TableDataInfo<SohuArticleVo> queryPageListOfOnShelf(SohuArticleBo bo, PageQuery pageQuery) {
        return sohuArticleService.queryPageListOfOnShelf(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuArticleVo> getTopicList(SohuTopicContentQueryBo bo, PageQuery pageQuery) {
        return sohuArticleService.getTopicList(bo, pageQuery);
    }

    @Override
    public List<SohuArticleVo> articleList() {
        return sohuArticleService.getLessonList();
    }
}
