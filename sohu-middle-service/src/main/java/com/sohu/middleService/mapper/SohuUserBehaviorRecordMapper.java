package com.sohu.middleService.mapper;

import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.middle.api.bo.UserBehaviorRecordBo;
import com.sohu.middle.api.vo.SohuUserBehaviorRecordVo;
import com.sohu.middle.api.vo.behavior.UserBehaviorRecordVo;
import com.sohu.middleService.domain.SohuUserBehaviorRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户行为记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-25
 */
public interface SohuUserBehaviorRecordMapper extends BaseMapperPlus<SohuUserBehaviorRecordMapper, SohuUserBehaviorRecord, SohuUserBehaviorRecordVo> {

    /**
     * 查询用户行为
     *
     * @param bo
     * @return
     */
    List<UserBehaviorRecordVo> list(@Param("bo") UserBehaviorRecordBo bo);
}
