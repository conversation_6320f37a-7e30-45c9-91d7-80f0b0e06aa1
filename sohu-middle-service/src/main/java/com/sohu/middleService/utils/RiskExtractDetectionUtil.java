package com.sohu.middleService.utils;

import com.sohu.middle.api.aspect.RiskDetectionField;
import com.sohu.middle.api.enums.DetectTypeEnum;
import com.sohu.middle.api.vo.risk.FieldDetectionInfo;
import com.sohu.middle.api.vo.risk.RichTextElement;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 校验对象处理方法
 *
 * @Author: leibo
 * @Date: 2025/6/17 19:28
 **/
@Slf4j
public class RiskExtractDetectionUtil {

    /**
     * 从对象中提取需要检测的字段信息（支持递归解析嵌套对象）
     */
    public static List<FieldDetectionInfo> extractDetectionFields(String busyType, Object object) {
        List<FieldDetectionInfo> fieldInfos = new ArrayList<>();
        if (object == null) {
            return fieldInfos;
        }
        // 获取对象所有字段
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                Object value = field.get(object);
                if (value == null) {
                    continue;
                }
                // 1. 检查当前字段是否有风控检测注解
                if (field.isAnnotationPresent(RiskDetectionField.class)) {
                    RiskDetectionField annotation = field.getAnnotation(RiskDetectionField.class);
                    DetectTypeEnum detectType = annotation.detectType();
                    String fieldName = field.getName();
                    String stringValue = value.toString();
                    // 处理不同类型的字段
                    switch (detectType) {
                        case IMAGE:
                            // 图片类型按逗号分割
                            String[] imageValues = stringValue.split(",");
                            for (String imageValue : imageValues) {
                                if (!imageValue.trim().isEmpty()) {
                                    fieldInfos.add(new FieldDetectionInfo(
                                            fieldName,
                                            imageValue.trim(),
                                            DetectTypeEnum.IMAGE.getCode(),
                                            busyType
                                    ));
                                }
                            }
                            break;
                        case RICH_TEXT:
                            // 富文本类型需要解析（如果有实现）
                            List<RichTextElement> richTextElements =
                                    RichTextUtils.parse(stringValue);
                            for (RichTextElement element : richTextElements) {
                                fieldInfos.add(new FieldDetectionInfo(
                                        element.getFieldCode(),
                                        element.getContent(),
                                        element.getDetectType().getCode(),
                                        element.getBusyType()
                                ));
                            }
                            break;
                        default:
                            // 其他类型直接添加
                            fieldInfos.add(new FieldDetectionInfo(
                                    fieldName,
                                    stringValue,
                                    detectType.getCode(),
                                    busyType
                            ));
                    }
                }
                // 2. 递归处理嵌套对象（非基本类型、非Java原生类型）
                else if (!isJavaClass(field.getType())) {
                    // 新增：处理集合类型
                    if (value instanceof Collection) {
                        for (Object item : (Collection<?>) value) {
                            if (!isJavaClass(item.getClass())) {
                                fieldInfos.addAll(extractDetectionFields(busyType, item));
                            }
                        }
                    } else {
                        fieldInfos.addAll(extractDetectionFields(busyType, value));
                    }
                }
            } catch (IllegalAccessException e) {
                log.error("字段解析失败：{}", field.getName(), e);
            }
        }
        return fieldInfos;
    }

    /**
     * 判断是否是Java原生类型或基本类型
     */
    private static boolean isJavaClass(Class<?> clazz) {
        return clazz.isPrimitive() ||
                clazz.getPackageName().startsWith("java.") ||
                clazz.getPackageName().startsWith("javax.");
    }

}
