package com.sohu.middleService.utils;

import com.netease.yidun.sdk.antispam.audio.check.async.v4.response.AudioAsyncCheckResponse;
import com.netease.yidun.sdk.antispam.crawler.v3.submit.response.CrawlerResourceSubmitV3Response;
import com.netease.yidun.sdk.antispam.file.v2.submit.response.FileSubmitV2Response;
import com.netease.yidun.sdk.antispam.image.v5.check.async.response.ImageV5AsyncCheckResp;
import com.netease.yidun.sdk.antispam.image.v5.check.sync.response.ImageV5AntispamResp;
import com.netease.yidun.sdk.antispam.text.v5.check.async.single.TextAsyncCheckResult;
import com.netease.yidun.sdk.antispam.text.v5.check.sync.single.TextCheckResult;
import com.netease.yidun.sdk.antispam.video.submit.v4.response.VideoCheckResp;
import com.sohu.common.core.constant.Constants;
import com.sohu.middle.api.bo.risk.RiskCheckBo;
import com.sohu.middle.api.bo.risk.SohuRiskRecordRelateBo;
import com.sohu.middle.api.vo.risk.SohuRiskConfigVo;
import com.sohu.third.yidun.object.TransformObjet;
import com.sohu.third.yidun.service.*;

import java.util.Arrays;
import java.util.Objects;
import java.util.UUID;

/**
 * @Author: leibo
 * @Date: 2025/6/18 11:57
 **/

public class RiskCheckUtils {
    /**
     * 校验文本
     *
     * @param content
     * @param recordRelateBo
     */
    public static void handleText(RiskCheckBo.Content content, String dataId, SohuRiskRecordRelateBo recordRelateBo, String callbackUrl) {
        TransformObjet transformObjet = new TransformObjet();
        transformObjet.setDataId(dataId);
        transformObjet.setContent(content.getContent());
        TextAsyncCheckResult.CheckText checkText = TextCheckService.singleAsyncText(content.getSecretId(), content.getSecretKey(), content.getBusinessId(), transformObjet, callbackUrl);
        recordRelateBo.setStatus(Objects.isNull(checkText) ? 2 : 3);
        recordRelateBo.setTaskId(Objects.isNull(checkText) ? "" : checkText.getTaskId());
    }

    /**
     * 校验图片
     *
     * @param content
     * @param recordRelateBo
     */
    public static void handleImage(RiskCheckBo.Content content, String dataId, SohuRiskRecordRelateBo recordRelateBo, String callbackUrl) {
        TransformObjet transformObjet = new TransformObjet();
        transformObjet.setDataId(dataId);
        transformObjet.setContent(content.getContent());
        ImageV5AsyncCheckResp.ImageRespDetail imageRespDetail = ImageCheckService.asyncBatchImage(content.getSecretId(), content.getSecretKey(), content.getBusinessId(), Arrays.asList(transformObjet), callbackUrl);
        recordRelateBo.setStatus(Objects.isNull(imageRespDetail) ? 2 : 3);
        recordRelateBo.setTaskId(Objects.isNull(imageRespDetail) ? "" : imageRespDetail.getTaskId());
    }

    /**
     * 校验音频
     *
     * @param content
     * @param recordRelateBo
     */
    public static void handleAudio(RiskCheckBo.Content content, String dataId, SohuRiskRecordRelateBo recordRelateBo, String callbackUrl) {
        TransformObjet transformObjet = new TransformObjet();
        transformObjet.setDataId(dataId);
        transformObjet.setContent(content.getContent());
        AudioAsyncCheckResponse.AudioSubmitV4Result submitResult = AudioCheckService.asyncAudio(content.getSecretId(), content.getSecretKey(), content.getBusinessId(), transformObjet, callbackUrl);
        recordRelateBo.setStatus(Objects.isNull(submitResult) ? 2 : 3);
        recordRelateBo.setTaskId(Objects.isNull(submitResult) ? "" : submitResult.getTaskId());
    }

    /**
     * 校验视频
     *
     * @param content
     * @param recordRelateBo
     */
    public static void handleVideo(RiskCheckBo.Content content, String dataId, SohuRiskRecordRelateBo recordRelateBo, String callbackUrl) {
        TransformObjet transformObjet = new TransformObjet();
        transformObjet.setDataId(dataId);
        transformObjet.setContent(content.getContent());
        VideoCheckResp.VideoCheckResult submitResult = VideoCheckService.asyncVideo(content.getSecretId(), content.getSecretKey(), content.getBusinessId(), transformObjet, callbackUrl);
        recordRelateBo.setStatus(Objects.isNull(submitResult) ? 2 : 3);
        recordRelateBo.setTaskId(Objects.isNull(submitResult) ? "" : submitResult.getTaskId());
    }

    /**
     * 校验链接
     *
     * @param content
     * @param recordRelateBo
     */
    public static void handleLink(RiskCheckBo.Content content, String dataId, SohuRiskRecordRelateBo recordRelateBo, String callbackUrl) {
        TransformObjet transformObjet = new TransformObjet();
        transformObjet.setDataId(dataId);
        transformObjet.setContent(content.getContent());
        CrawlerResourceSubmitV3Response.CrawlerResourceSubmitResult submitResult = LinkCheckService.asyncLink(content.getSecretId(), content.getSecretKey(), content.getBusinessId(), transformObjet, callbackUrl);
        recordRelateBo.setStatus(Objects.isNull(submitResult) ? 2 : 3);
        recordRelateBo.setTaskId(Objects.isNull(submitResult) ? "" : submitResult.getTaskId());
    }

    /**
     * 校验文件
     *
     * @param content
     * @param recordRelateBo
     */
    public static void handleFile(RiskCheckBo.Content content, String dataId, SohuRiskRecordRelateBo recordRelateBo, String callbackUrl) {
        TransformObjet transformObjet = new TransformObjet();
        transformObjet.setDataId(dataId);
        transformObjet.setContent(content.getContent());
        FileSubmitV2Response.FileSubmitV2Resp submitResult = FileCheckService.asyncFile(content.getSecretId(), content.getSecretKey(), content.getBusinessId(), transformObjet, callbackUrl);
        recordRelateBo.setStatus(Objects.isNull(submitResult) ? 2 : 3);
        recordRelateBo.setTaskId(Objects.isNull(submitResult) ? "" : submitResult.getTaskId());
    }


    /**
     * 同步文本校验
     *
     * @param sohuRiskConfigVo
     * @param content
     * @param recordRelateBo
     */
    public static void handleSyncText(SohuRiskConfigVo sohuRiskConfigVo, String content, SohuRiskRecordRelateBo recordRelateBo) {
        TransformObjet transformObjet = new TransformObjet();
        String dataId = UUID.randomUUID().toString();
        transformObjet.setDataId(dataId);
        transformObjet.setContent(content);
        TextCheckResult.Antispam antispam = TextCheckService.singleSyncText(sohuRiskConfigVo.getSecretId(), sohuRiskConfigVo.getSecretKey(), sohuRiskConfigVo.getBusinessId(), transformObjet);
        recordRelateBo.setDataId(dataId);
        if (Objects.nonNull(antispam)) {
            recordRelateBo.setStatus(antispam.getSuggestion());
            recordRelateBo.setTaskId(antispam.getTaskId());
            recordRelateBo.setRiskDescription(antispam.getRiskDescription());
        } else {
            recordRelateBo.setStatus(2);
        }
    }

    /**
     * 同步图片校验
     *
     * @param sohuRiskConfigVo
     * @param content
     * @param recordRelateBo
     */
    public static void handleSyncImage(SohuRiskConfigVo sohuRiskConfigVo, String content, SohuRiskRecordRelateBo recordRelateBo) {
        TransformObjet transformObjet = new TransformObjet();
        String dataId = UUID.randomUUID().toString();
        transformObjet.setDataId(dataId);
        transformObjet.setContent(content);
        ImageV5AntispamResp antispam = ImageCheckService.singleSyncImage(sohuRiskConfigVo.getSecretId(), sohuRiskConfigVo.getSecretKey(), sohuRiskConfigVo.getBusinessId(), transformObjet);
        recordRelateBo.setDataId(dataId);
        if (Objects.nonNull(antispam)) {
            if (antispam.getSuggestion() == Constants.ZERO && antispam.getStatus() == Constants.TWO) {
                recordRelateBo.setStatus(Constants.ZERO);
            } else {
                recordRelateBo.setStatus(Constants.TWO);
                recordRelateBo.setRiskDescription(antispam.getRiskDescription());
            }
            recordRelateBo.setTaskId(antispam.getTaskId());
        } else {
            recordRelateBo.setStatus(2);
        }
    }

}
