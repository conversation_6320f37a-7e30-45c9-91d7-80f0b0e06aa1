package com.sohu.middleService.utils;

import com.sohu.middle.api.vo.ai.AiAnalysisVo;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 解析公共方法
 *
 * @Author: leibo
 * @Date: 2025/3/14 14:44
 **/
@Slf4j
public class AnalysisUtils {

    /**
     * 组装构建对象(新)
     *
     * @param answer
     * @return
     */
    public static AiAnalysisVo.BusyTaskVo buildBusyTaskNew(String answer) {
        AiAnalysisVo.BusyTaskVo task = new AiAnalysisVo().new BusyTaskVo();
        // 定义正则表达式模式（启用多行匹配模式）
        String categoryTypeRegex = "\\[愿望分类\\]：(.*?)(?=\\s*\\[愿望类型\\]：|$)";
        String supplyTypeRegex = "\\[愿望类型\\]：(.*?)(?=\\s*\\[所属分类\\]：|$)";
        String categoryRegex = "\\[所属分类\\]：(.*?)(?=\\s*\\[所属行业\\]：|$)";
        String industryRegex = "\\[所属行业\\]：(.*?)(?=\\s*\\[愿望标题\\]：|$)";
        String titleRegex = "\\[愿望标题\\]：(.*?)(?=\\s*\\[愿望详情\\]：|$)";
        String contentRegex = "\\[愿望详情\\]：(.*?)(?=\\s*\\[潜在愿望\\]：|$)";
        String potentialRegex = "\\[潜在愿望\\]：(.*)";
        // 提取结构化数据
        task.setCategoryTypeName(extractField(answer, categoryTypeRegex));
        task.setSupplyTypeName(extractField(answer, supplyTypeRegex));
        task.setCategoryName(extractField(answer, categoryRegex));
        task.setIndustryName(extractField(answer, industryRegex));
        task.setTitle(extractField(answer, titleRegex));
        task.setContent(extractField(answer, contentRegex));
        // 4. 处理潜在需求（分割为数组）
        task.setPotentialDemand(
                Arrays.stream(extractField(answer, potentialRegex).split("\n"))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .collect(Collectors.toList()));
        return task;
    }

    /**
     * 组装构建对象
     *
     * @param answer
     * @return
     */
    public static AiAnalysisVo.BusyTaskVo buildBusyTask(String answer) {
        AiAnalysisVo.BusyTaskVo task = new AiAnalysisVo().new BusyTaskVo();
        // 定义正则表达式模式（启用多行匹配模式）
        String industryRegex = "\\[所属行业\\]：(.*?)(?=\\s*\\[商单标题\\]：|$)";
        String titleRegex = "\\[商单标题\\]：(.*?)(?=\\s*\\[商单内容\\]：|$)";
        String contentRegex = "\\[商单内容\\]：(.*?)(?=\\s*\\[推荐需求\\]：|$)";
        String potentialRegex = "\\[推荐需求\\]：(.*)";
        // 提取结构化数据
        task.setIndustryName(extractField(answer, industryRegex));
        task.setTitle(extractField(answer, titleRegex));
        task.setContent(extractField(answer, contentRegex));
        // 4. 处理潜在需求（分割为数组）
        task.setPotentialDemand(
                Arrays.stream(extractField(answer, potentialRegex).split("\n"))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .collect(Collectors.toList()));
        return task;
    }

    /**
     * 处理逻辑
     *
     * @param input
     * @param regex
     * @return
     */
    public static String extractField(String input, String regex) {
        Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);
        Matcher matcher = pattern.matcher(input);
        String value = "";
        if (matcher.find()) {
            value = matcher.group(1).trim()
                    // 清理多余空格
                    .replaceAll("\\n\\s*", "\n")
                    // 统一列表符号
                    .replaceAll("([•∙▪▫])", "•");
        }
        return value;
    }
}
