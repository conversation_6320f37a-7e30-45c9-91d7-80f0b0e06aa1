package com.sohu.middleService.utils;

import com.sohu.middle.api.enums.DetectTypeEnum;
import com.sohu.middle.api.vo.risk.RichTextElement;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.ArrayList;
import java.util.List;

/**
 * 富文本解析方法
 *
 * @Author: leibo
 * @Date: 2025/6/17 19:24
 **/
public class RichTextUtils {

    /**
     * 解析富文本内容
     *
     * @param richText 富文本内容
     * @return 解析后的内容元素列表
     */
    public static List<RichTextElement> parse(String richText) {
        List<RichTextElement> elements = new ArrayList<>();
        Document doc = Jsoup.parse(richText);

        // 解析纯文本
        String text = doc.text();
        if (!text.trim().isEmpty()) {
            elements.add(new RichTextElement("text", text, DetectTypeEnum.TEXT, "Other"));
        }

        // 解析图片
        Elements imgs = doc.select("img[src]");
        for (Element img : imgs) {
            String src = img.attr("src");
            if (!src.trim().isEmpty()) {
                elements.add(new RichTextElement("image", src, DetectTypeEnum.IMAGE, "Other"));
            }
        }
        // 解析视频
        Elements videos = doc.select("video[src], iframe[src]");
        for (Element video : videos) {
            String src = video.attr("src");
            if (!src.trim().isEmpty()) {
                elements.add(new RichTextElement("video", src, DetectTypeEnum.VIDEO, "Other"));
            }
        }
        // 解析链接
        Elements links = doc.select("a[href]");
        for (Element link : links) {
            String href = link.attr("href");
            if (!href.trim().isEmpty()) {
                elements.add(new RichTextElement("link", href, DetectTypeEnum.Link, "Other"));
            }
        }
        return elements;
    }

}
