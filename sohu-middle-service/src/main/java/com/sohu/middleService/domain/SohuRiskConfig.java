package com.sohu.middleService.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 风控检测配置对象 sohu_risk_config
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_risk_config")
public class SohuRiskConfig extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 业务类型
     */
    private String busyType;
    /**
     * 检测类型  1.文本  2.图片 3.视频 4.音频 5.链接 6.文档 
     */
    private Integer detectType;
    /**
     * 字段描述
     */
    private String fieldName;
    /**
     * 字段编码
     */
    private String fieldCode;
    /**
     * 通过处理结果  0.不允许发布 1.发布
     */
    private Boolean passProcess;
    /**
     * 疑似处理结果  0.不允许发布  1.发布
     */
    private Boolean suspectProcess;
    /**
     * 不通过处理结果 0.不允许发布  1.发布
     */
    private Boolean noPassProcess;
    /**
     * 平台 sohuglobal 许愿狐  catchfish 捕鱼 yougua 有瓜  hifocus Hi狐
     */
    private String platform;
    /**
     * secretId  易盾
     */
    private String secretId;
    /**
     * secretKey  易盾
     */
    private String secretKey;
    /**
     * businessId  易盾
     */
    private String businessId;

}
