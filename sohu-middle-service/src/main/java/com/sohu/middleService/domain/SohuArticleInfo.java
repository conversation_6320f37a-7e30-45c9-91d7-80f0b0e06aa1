package com.sohu.middleService.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * 图文拓展对象 sohu_article_info
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@Data
@TableName("sohu_article_info")
@BaseEntity.Cache(region = "SohuArticleInfo")
public class SohuArticleInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 站点ID
     */
    private Long siteId;
    /**
     * 图文ID
     */
    private Long articleId;
    /**
     * 内容
     */
    private String content;
    /**
     * 阅读数
     */
    private Integer viewCount;
    /**
     * 评论数
     */
    private Integer commentCount;
    /**
     * 点赞数
     */
    private Integer praiseCount;
    /**
     * 收藏数
     */
    private Integer collectCount;
    /**
     * 转发数
     */
    private Integer forwardCount;

    /**
     * 初始学习人数
     */
    private Integer learnNum;

}
