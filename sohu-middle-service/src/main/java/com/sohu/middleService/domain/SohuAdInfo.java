package com.sohu.middleService.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.sohu.common.core.enums.AdsType;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 站点广告主体对象 sohu_ad_info
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@Data
@TableName("sohu_ad_info")
@BaseEntity.Cache(region = "SohuAdInfo")
public class SohuAdInfo implements Serializable {

    /**
     * 广告主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 站点ID
     */
    private Long siteId;
    /**
     * 广告位表code
     */
    private String adPlace;
    /**
     * 广告标题
     */
    private String title;
    /**
     * 广告图片
     */
    private String image;
    /**
     * 附加广告图片
     */
    private String extraImage;
    /**
     * 广告链接
     */
    private String link;
    /**
     * 广告状态;Edit:编辑,OnShelf:上架,OffShelf:下架,Delete:删除
     */
    private String state;
    /**
     * 广告类型 {@link AdsType}
     */
    private String type;
    /**
     * 广告投放开始时间
     */
    private Date startTime;
    /**
     * 广告投放结束时间
     */
    private Date overTime;
    /**
     * 排序值
     */
    private Long sortIndex;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 端口 android 安卓、wechat-微信小程序、ios
     */
    private String port;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 发布人id
     */
    private Long userId;

    /**
     * 业务id
     */
    private Long objId;

    /**
     * 业务类型
     */
    private String objType;
    /**
     * 业务标题
     */
    private String objTitle;

    /**
     * 广告金额
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal price;
    /**
     * 广告倒计时
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer duration;

    /**
     * 展现形式:[IMAGE:纯图片，NATIVE:原生广告]
     */
    private String showForm;

    /**
     * 广告文案
     */
    private String adTxt;

    /**
     * 视频id
     */
    private Long videoId;

    /**
     * 视频标题
     */
    private String videoTitle;

    /**
     * 素材类型
     */
    private String materialType;

    /**
     * 驳回原因
     */
    private String rejectReason;
}
