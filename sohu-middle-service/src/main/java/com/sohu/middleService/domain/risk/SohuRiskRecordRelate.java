package com.sohu.middleService.domain.risk;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 风控记录检测对象 sohu_risk_record_relate
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_risk_record_relate")
public class SohuRiskRecordRelate extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 记录ID
     */
    private Long riskRecordId;
    /**
     * 风险配置ID
     */
    private Long riskConfigId;
    /**
     * 内容(拆分)
     */
    private String content;
    /**
     * 数据请求唯一标识
     */
    private String dataId;
    /**
     * 0：通过，1：嫌疑，2：不通过 3 审核中
     */
    private Integer status;
    /**
     * 风险描述
     */
    private String riskDescription;
    /**
     * 风控唯一标识,根据该标识查询数据最新结果
     */
    private String taskId;

}
