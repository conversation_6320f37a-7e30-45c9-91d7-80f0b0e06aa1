package com.sohu.middleService.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 行业分类对象 sohu_industry_category
 *
 * <AUTHOR>
 * @date 2023-06-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_industry_category")
public class SohuIndustryCategory extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 上级行业ID
     */
    private Long pid;
    /**
     * 行业标识(作废)
     */
    @Deprecated
    private String ident;
    /**
     * 行业名称
     */
    private String industryName;
    /**
     * 站点ID
     */
    @Deprecated
    private Long siteId;
    /**
     * 内容分类表(sohu_category)主键ID
     */
    @Deprecated
    private Long categoryId;

    /**
     * 需要认证资质：0-关闭，1-开启
     */
    private Boolean needProve;

    /**
     * 认证后授予的角色，功能角色key集合，英文逗号分开
     */
    private String roleKeysStr;

    /**
     * 删除标记，1=删除，0=未删除
     */
    @TableLogic
    private Boolean isDel;
}
