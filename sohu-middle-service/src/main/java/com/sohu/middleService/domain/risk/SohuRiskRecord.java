package com.sohu.middleService.domain.risk;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 风控记录对象 sohu_risk_record
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_risk_record")
public class SohuRiskRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 业务分类 Article-图文  Video-视频  Question-问答 ShortPlay 短剧  BusyOrder-商单 Goods-商品 Novel-小说  Game-游戏 Literature-诗歌散文
     */
    private String busyType;
    /**
     * 业务ID
     */
    private String busyCode;
}
