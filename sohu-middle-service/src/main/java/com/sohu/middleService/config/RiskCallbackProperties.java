package com.sohu.middleService.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/6/18 16:11
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "risk.callback")
public class RiskCallbackProperties {
    @Schema(description = "文本回调地址")
    private String textUrl;
    @Schema(description = "图片回调地址")
    private String imageUrl;
    @Schema(description = "音频回调地址")
    private String audioUrl;
    @Schema(description = "视频回调地址")
    private String videoUrl;
    @Schema(description = "链接回调地址")
    private String linkUrl;
    @Schema(description = "文件回调地址")
    private String fileUrl;
}
