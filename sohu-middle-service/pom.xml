<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sohu</groupId>
        <artifactId>sohu-dependency</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sohu-middle-service</artifactId>

    <description>
        中台服务，即公共模块
    </description>

    <dependencies>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-sentinel</artifactId>
        </dependency>

        <!--sohu Common Log -->
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-dict</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-seata</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-middle</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-entry</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zwztf.sdk</groupId>
            <artifactId>aliyun-audit</artifactId>
            <version>1.0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-admin</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-busy-order</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-app</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-im</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.sohu</groupId>-->
        <!--            <artifactId>sohu-api-busy-order</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-stream-rocketmq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-shop-goods</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-pay</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-novel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wangcaio2o.ipossa</groupId>
            <artifactId>ym-sdk-java</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>io.milvus</groupId>
            <artifactId>milvus-sdk-java</artifactId>
            <version>2.4.7</version>
        </dependency>
        <dependency>
            <groupId>com.zwztf.sdk</groupId>
            <artifactId>ai-sdk</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.zwztf.sdk</groupId>
            <artifactId>yidun-sdk</artifactId>
            <version>1.0.1</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
