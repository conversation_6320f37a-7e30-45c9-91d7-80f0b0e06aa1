FROM openjdk:11

MAINTAINER sohu

RUN mkdir -p /sohu/xxl-job-admin/logs \
    /sohu/skywalking/agent

WORKDIR /sohu/xxl-job-admin

ENV TZ=PRC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

EXPOSE 9900

ADD sohu-xxl-job-admin.jar ./app.jar

ENTRYPOINT ["java", \
            "-Djava.security.egd=file:/dev/./urandom", \
#            "-Dskywalking.agent.service_name=sohu-xxl-job-admin", \
#            "-javaagent:/sohu/skywalking/agent/skywalking-agent.jar", \
            "-jar", "app.jar"]
