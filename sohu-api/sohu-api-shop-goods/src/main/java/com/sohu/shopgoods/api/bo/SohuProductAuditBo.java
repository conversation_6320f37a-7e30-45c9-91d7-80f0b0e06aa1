package com.sohu.shopgoods.api.bo;

import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品pc后台审核业务对象
 *
 * <AUTHOR>
 * @date 2023-06-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuProductAuditBo extends SohuEntity implements Serializable {

    private static final long serialVersionUID = -452373239606480650L;

    /**
     * 主键id
     */
    @NotNull(message = "商品id不能为空")
    private Long id;

    /**
     * 审核状态(success,fail)
     */
    @NotEmpty(message = "审核状态不能为空")
    private String auditStatus;

    /**
     * 拒绝原因
     */
    @Length(max = 100, message = "拒绝原因长度不能超过100个字符")
    private String reason;

    /**
     * 商品类型，0普通商品，1返哺库商品。返哺库商品审核必传
     */
    private Integer productType;

    /**
     * 兑换所需积分，返哺库商品审核必传
     */
    private Integer wantIntegral;

    /**
     * 兑换所需金额，返哺库商品审核必传-暂时业务不需要
     */
    private BigDecimal productPrice;

}
