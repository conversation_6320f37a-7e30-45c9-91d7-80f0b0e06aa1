package com.sohu.shopgoods.api.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 优选供应链商品业务对象
 *
 * <AUTHOR>
 * @date 2025-07-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuProductZxhxSpuBo extends BaseEntity {

    /**
     * 主键 ID
     */
    @NotNull(message = "主键 ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 品牌 ID
     */
    @NotNull(message = "品牌 ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long thirdBrandId;

    /**
     * 分类 ID（可多级）
     */
    @NotBlank(message = "分类 ID（可多级）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long thirdCategoryId;

    /**
     * spuId
     */
    @NotNull(message = "spuId不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long thirdSpuId;

    /**
     * 供应商ID
     */
    @NotNull(message = "供应商ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long supplierId;

    /**
     * 主图 URL
     */
    @NotBlank(message = "主图 URL不能为空", groups = {AddGroup.class, EditGroup.class})
    private String image;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String storeName;

    /**
     * 轮播图
     */
    @NotBlank(message = "轮播图不能为空", groups = {AddGroup.class, EditGroup.class})
    private String sliderImage;

    /**
     * 商品类型（1:普通商品 2: 虚拟商品）
     */
    @NotNull(message = "商品类型（1:普通商品 2: 虚拟商品）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer productType;

    /**
     * 状态（0：未上架，1：上架）
     */
    @NotNull(message = "状态（0：未上架，1：上架）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer isShow;

    /**
     * 是否海外商品
     */
    @NotNull(message = "是否海外商品不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer isOverseas;

    /**
     * 商品详情（HTML 富文本）
     */
    @NotBlank(message = "商品详情（HTML 富文本）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String storeInfo;

    /**
     * 关键字
     */
    @NotBlank(message = "关键字不能为空", groups = {AddGroup.class, EditGroup.class})
    private String keyword;

    /**
     * 三方商品价格
     */
    @NotNull(message = "三方商品价格不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal price;

    /**
     * 商品零售价
     */
    @NotNull(message = "商品零售价不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal marketPrice;

    /**
     * 商品最低控价
     */
    @NotNull(message = "商品最低控价不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal groupPrice;

    /**
     * 商品利润
     */
    @NotNull(message = "商品利润不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal rate;

    /**
     * 规格 0单 1多
     */
    @NotNull(message = "规格 0单 1多不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer specType;

    /**
     * 单位名
     */
    @NotBlank(message = "单位名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String unitName;

    /**
     * 供应链同步时间
     */
    @NotNull(message = "供应链同步时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date syncTime;

    /**
     * 自动下架时间
     */
    @NotNull(message = "自动下架时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date listingTime;

    /**
     * 下架原因
     */
    private String offShelfReason;

    /**
     * 商品规格
     */
    private List<SohuProductZxhxSkuBo> skuList;
}
