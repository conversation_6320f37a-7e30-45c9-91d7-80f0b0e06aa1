package com.sohu.shopgoods.api.vo;

import java.io.Serializable;
import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 优选供应链商品规格视图对象
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@ExcelIgnoreUnannotated
public class SohuProductZxhxSkuVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @ExcelProperty(value = "主键 ID")
    private Long id;

    /**
     * 关联spuId
     */
    @ExcelProperty(value = "关联spuId")
    private Long spuId;

    /**
     * 关联三方spuId
     */
    @ExcelProperty(value = "关联三方spuId")
    private Long thirdSpuId;

    /**
     * 关联三方skuId
     */
    @ExcelProperty(value = "关联三方skuId")
    private Long thirdSkuId;

    /**
     * 商品属性索引值 (attr_value|attr_value[|....])
     */
    @ExcelProperty(value = "商品属性索引值 (attr_value|attr_value[|....])")
    private String sku;

    /**
     * 库存数量
     */
    @ExcelProperty(value = "库存数量")
    private Long stock;

    /**
     * SKU 图片
     */
    @ExcelProperty(value = "SKU 图片")
    private String image;

    /**
     * 售价
     */
    @ExcelProperty(value = "售价")
    private BigDecimal price;

    /**
     * 条形码
     */
    @ExcelProperty(value = "条形码")
    private String barCode;

    /**
     * 成本价
     */
    @ExcelProperty(value = "成本价")
    private BigDecimal cost;

    /**
     * 最低控价
     */
    @ExcelProperty(value = "最低控价")
    private BigDecimal groupPrice;

    /**
     * 服务费
     */
    @ExcelProperty(value = "服务费")
    private BigDecimal servicePrice;

    /**
     * SKU 描述
     */
    @ExcelProperty(value = "SKU 描述")
    private String attrValue;

    /**
     * 重量
     */
    @ExcelProperty(value = "重量")
    private BigDecimal weight;

    /**
     * 体积
     */
    @ExcelProperty(value = "体积")
    private BigDecimal volume;

    /**
     * 状态（0：未上架，1：上架）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=：未上架，1：上架")
    private Integer isShow;

    /**
     * 起购数量
     */
    @ExcelProperty(value = "起购数量")
    private Integer buyStartQty;


}
