package com.sohu.shopgoods.api;

import com.sohu.shopgoods.api.model.SohuProductAttrValueModel;
import com.sohu.shopgoods.api.vo.SohuProductAttrValueVo;

import java.util.List;

/**
 * 商品属性详情服务
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
public interface RemoteProductAttrValueService {

    /**
     * 根据id、类型查询
     *
     * @param productAttrId
     * @param productId
     * @param productActivityTypeNormals
     * @return SohuProductAttrValueModel
     */
    SohuProductAttrValueModel getByIdAndProductIdAndType(Long productAttrId, Long productId, String productActivityTypeNormals);

    /**
     * 普通商品规格扣库存
     *
     * @param attrValueId
     * @param num
     * @param sub
     * @param type
     * @param attrValueVersion
     */
    Boolean operationStock(Long attrValueId, Integer num, String sub, String type, Integer attrValueVersion);

    /**
     * 根据主键id查询
     *
     * @param productAttrValueId
     */
    SohuProductAttrValueModel queryById(Long productAttrValueId);

    /**
     * 根据id列表查询
     *
     * @param productAttrValueIds
     */
    List<SohuProductAttrValueVo> listByIds(List<Long> productAttrValueIds);

}
