package com.sohu.shopgoods.api.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 优选供应链商品视图对象
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@ExcelIgnoreUnannotated
public class SohuProductZxhxSpuVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @ExcelProperty(value = "主键 ID")
    private Long id;

    /**
     * 品牌 ID
     */
    @ExcelProperty(value = "品牌 ID")
    private Long thirdBrandId;

    /**
     * 分类 ID（可多级）
     */
    @ExcelProperty(value = "分类 ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "可=多级")
    private String thirdCategoryId;

    /**
     * spuId
     */
    @ExcelProperty(value = "spuId")
    private Long thirdSpuId;

    /**
     * 供应商ID
     */
    @ExcelProperty(value = "供应商ID")
    private Long supplierId;

    /**
     * 主图 URL
     */
    @ExcelProperty(value = "主图 URL")
    private String image;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String storeName;

    /**
     * 轮播图
     */
    @ExcelProperty(value = "轮播图")
    private String sliderImage;

    /**
     * 商品类型（1:普通商品 2: 虚拟商品）
     */
    @ExcelProperty(value = "商品类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=:普通商品,2=:,虚=拟商品")
    private Integer productType;

    /**
     * 状态（0：未上架，1：上架）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=：未上架，1：上架")
    private Integer isShow;

    /**
     * 是否海外商品
     */
    @ExcelProperty(value = "是否海外商品")
    private Integer isOverseas;

    /**
     * 商品详情（HTML 富文本）
     */
    @ExcelProperty(value = "商品详情", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "H=TML,富=文本")
    private String storeInfo;

    /**
     * 关键字
     */
    @ExcelProperty(value = "关键字")
    private String keyword;

    /**
     * 三方商品价格
     */
    @ExcelProperty(value = "三方商品价格")
    private BigDecimal price;

    /**
     * 商品零售价
     */
    @ExcelProperty(value = "商品零售价")
    private BigDecimal marketPrice;

    /**
     * 商品最低控价
     */
    @ExcelProperty(value = "商品最低控价")
    private BigDecimal groupPrice;

    /**
     * 商品利润
     */
    @ExcelProperty(value = "商品利润")
    private BigDecimal rate;

    /**
     * 规格 0单 1多
     */
    @ExcelProperty(value = "规格 0单 1多")
    private Integer specType;

    /**
     * 单位名
     */
    @ExcelProperty(value = "单位名")
    private String unitName;

    /**
     * 供应链同步时间
     */
    @ExcelProperty(value = "供应链同步时间")
    private Date syncTime;

    /**
     * 自动下架时间
     */
    @ExcelProperty(value = "自动下架时间")
    private Date listingTime;

    /**
     * 下架原因
     */
    @ExcelProperty(value = "下架原因")
    private String offShelfReason;
}
