package com.sohu.shopgoods.api.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.validate.SohuValidNumber;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品业务对象
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SohuProductBo extends SohuEntity implements Serializable {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 主键id-商品编号
     */
    @SohuValidNumber(message = "商品编号必须是数字类型(Long)",isLong = true)
    private String productNo;

    /**
     * 类型（1：出售中（已上架），2：仓库中（未上架），3：已售罄，4：警戒库存，5：回收站,6:待审核，7：审核失败）
     */
//    @NotNull(message = "商品类型不能为空")
//    @Range(min = 1, max = 7, message = "未知的商品类型")
    private int type;

    /**
     * 站点id
     */
//    @NotNull(message = "站点id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long siteId;

    /**
     * 商品审核开关:0-关闭，1-开启
     */
    private Boolean productSwitch;

    /**
     * 狐小店商户mer_id
     */
    @NotNull(message = "狐小店商户mer_id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long merId;

    /**
     * 商品图片
     */
    @NotBlank(message = "商品图片不能为空", groups = {AddGroup.class, EditGroup.class})
    private String image;

    /**
     * 展示图
     */
    private String flatPattern;

    /**
     * 轮播图
     */
    @NotBlank(message = "轮播图不能为空", groups = {AddGroup.class, EditGroup.class})
    private String sliderImage;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String storeName;

    /**
     * 商品简介
     */
    @NotBlank(message = "商品简介不能为空", groups = {AddGroup.class, EditGroup.class})
    private String storeInfo;

    /**
     * 关键字
     */
    @NotBlank(message = "关键字不能为空", groups = {AddGroup.class, EditGroup.class})
    private String keyword;

    /**
     * 商户分类id(逗号拼接)
     */
    @NotBlank(message = "商户分类id(逗号拼接)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String cateId;

    /**
     * 品牌id
     */
    @NotNull(message = "品牌id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long brandId;

    /**
     * 平台分类id
     */
    @NotNull(message = "平台分类id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long categoryId;

    /**
     * 保障服务ids(英文逗号拼接)
     */
    @NotBlank(message = "保障服务ids(英文逗号拼接)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String guaranteeIds;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 币种，默认CNY人名币
     * 人民币（CNY）、美元（USD）、欧元（EUR）、日元（JPY）、英镑（GBP）、加拿大元（CAD）、
     * 澳大利亚元（AUD）、瑞士法郎（CHF）、瑞典克朗（SEK）、新西兰元（NZD）
     */
//    @NotNull(message = "币种，默认'CNY'人名币", groups = {AddGroup.class, EditGroup.class})
    private String currency;

    /**
     * 会员价格
     */
    private BigDecimal vipPrice;

    /**
     * 市场价
     */
    private BigDecimal otPrice;

    /**
     * 邮费
     */
    @NotNull(message = "邮费不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal postage;

    /**
     * 单位名
     */
    @NotBlank(message = "单位名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String unitName;

    /**
     * 销量
     */
    private Long sales;

    /**
     * 库存
     */
    private Integer stock;

    /**
     * 获得积分
     */
    private Long giveIntegral;

    /**
     * 成本价
     */
    @NotNull(message = "成本价不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal cost;

    /**
     * 虚拟销量
     */
    private Long varSales;

    /**
     * 浏览量
     */
    private Long browse;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer sort;

    /**
     * 总后台排序
     */
    private Integer ranks;

    /**
     * 规格 0单 1多
     */
    @NotNull(message = "规格 false单 true多不能为空", groups = {AddGroup.class, EditGroup.class})
    private Boolean specType;

    /**
     * 商品标识（0普通商品，1返哺商品，2抖音虚拟商品，3拓展商品）
     */
    @NotNull(message = "商品标识（0普通商品，1返哺商品，2抖音虚拟商品，3拓展商品）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productType;

    /**
     * 是否回收站
     */
    private Boolean isRecycle;

    /**
     * 是否强制下架，0-否，1-是
     */
    private Boolean isForced;

    /**
     * 审核状态：WAITAPPROVE-待审核，PASS-审核成功，FALSE-审核拒绝
     */
    private String auditStatus;

    /**
     * 拒绝原因
     */
    private String reason;

    /**
     * 是否删除
     */
    private Boolean isDel;

    /**
     * 状态（0：未上架，1：上架）
     */
    private Boolean isShow;

    /**
     * 分佣开关状态（0：不启用分佣，1：启用分佣）
     */
    @NotNull(message = "分佣开关状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private Boolean independentIsShow;

    /**
     * 是否是分销商品：0不是 1是
     */
//    @NotNull(message = "是否是分销商品不能为空", groups = {AddGroup.class, EditGroup.class})
    private Boolean independentType;

    /**
     * 分销金额、默认0.00
     */
    private BigDecimal independentPrice;

    /**
     * 分佣比例、默认0.00
     */
    private BigDecimal independentRatio;

    /**
     * 商品属性
     */
    @NotEmpty(message = "商品属性不能为空")
    private List<SohuProductAttrBo> attrList;

    /**
     * 商品属性值详情
     */
    @NotEmpty(message = "商品属性详情不能为空")
    private List<SohuProductAttrValueBo> attrValueList;

    /**
     * 商品描述
     */
    private String content;

    /**
     * 优惠券id集合
     */
    private List<Long> couponIds;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 批量修改-ids
     */
    private List<Long> ids;

    /**
     * 平台一级分类id
     */
    @NotNull(message = "平台一级分类id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long firstCategoryId;

    /**
     * 运费模板id
     */
    @NotNull(message = "运费模板id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long freightTemplateId;

    /**
     * 系统来源(sohuglobal:狐少少,minglereels:海外短剧)
     */
    private String SysSource;

    /**
     * 是否需要用户输入充值账号：0-否，1-是
     */
    private Integer isAccount;

    /**
     * 充值账号类型 1 手机号
     */
    private Integer accountType;

    /**
     * 发货方式：1-手动，2-自动
     */
    private Integer deliveryType;

    /**
     * 自动发货方式 1 蜂助手
     */
    private Integer autoDeliveryType;

    /**
     * 商品发货模式 1 商家 2 蜂助手 3 优选供应链
     */
    private Integer deliveryMode;

}
