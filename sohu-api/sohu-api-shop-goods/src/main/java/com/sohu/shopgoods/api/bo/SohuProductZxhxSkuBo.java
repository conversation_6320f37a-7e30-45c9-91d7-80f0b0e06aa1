package com.sohu.shopgoods.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.math.BigDecimal;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 优选供应链商品规格业务对象
 *
 * <AUTHOR>
 * @date 2025-07-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuProductZxhxSkuBo extends BaseEntity {

    /**
     * 主键 ID
     */
    @NotNull(message = "主键 ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 关联spuId
     */
    @NotNull(message = "关联spuId不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long spuId;

    /**
     * 关联三方spuId
     */
    @NotNull(message = "关联三方spuId不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long thirdSpuId;

    /**
     * 关联三方skuId
     */
    @NotNull(message = "关联三方skuId不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long thirdSkuId;

    /**
     * 商品属性索引值 (attr_value|attr_value[|....])
     */
    @NotBlank(message = "商品属性索引值 (attr_value|attr_value[|....])不能为空", groups = {AddGroup.class, EditGroup.class})
    private String sku;

    /**
     * 库存数量
     */
    @NotNull(message = "库存数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long stock;

    /**
     * SKU 图片
     */
    @NotBlank(message = "SKU 图片不能为空", groups = {AddGroup.class, EditGroup.class})
    private String image;

    /**
     * 售价
     */
    @NotNull(message = "售价不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal price;

    /**
     * 条形码
     */
    @NotBlank(message = "条形码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String barCode;

    /**
     * 成本价
     */
    @NotNull(message = "成本价不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal cost;

    /**
     * 最低控价
     */
    @NotNull(message = "最低控价不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal groupPrice;

    /**
     * 服务费
     */
    @NotNull(message = "服务费不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal servicePrice;

    /**
     * SKU 描述
     */
    @NotBlank(message = "SKU 描述不能为空", groups = {AddGroup.class, EditGroup.class})
    private String attrValue;

    /**
     * 重量
     */
    @NotNull(message = "重量不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal weight;

    /**
     * 体积
     */
    @NotNull(message = "体积不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal volume;

    /**
     * 状态（0：未上架，1：上架）
     */
    @NotNull(message = "状态（0：未上架，1：上架）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer isShow;

    /**
     * 起购数量
     */
    @NotNull(message = "起购数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer buyStartQty;


}
