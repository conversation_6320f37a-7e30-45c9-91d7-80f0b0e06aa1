package com.sohu.shopgoods.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import com.sohu.middle.api.aspect.RiskDetectionField;
import com.sohu.middle.api.enums.DetectTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品视图对象
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@Data
@ExcelIgnoreUnannotated
public class SohuProductVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 站点id
     */
    @ExcelProperty(value = "站点id")
    private Long siteId;

    /**
     * 狐小店商户mer_id
     */
    @ExcelProperty(value = "狐小店商户mer_id")
    private Long merId;

    /**
     * 商品图片
     */
    @ExcelProperty(value = "商品图片")
    @RiskDetectionField(detectType = DetectTypeEnum.IMAGE)
    private String image;

    /**
     * 轮播图
     */
    @ExcelProperty(value = "轮播图")
    private String flatPattern;

    /**
     * 轮播图
     */
    @ExcelProperty(value = "轮播图")
    @RiskDetectionField(detectType = DetectTypeEnum.IMAGE)
    private String sliderImage;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    @RiskDetectionField(detectType = DetectTypeEnum.TEXT)
    private String storeName;

    /**
     * 商品简介
     */
    @ExcelProperty(value = "商品简介")
    @RiskDetectionField(detectType = DetectTypeEnum.TEXT)
    private String storeInfo;

    /**
     * 关键字
     */
    @ExcelProperty(value = "关键字")
    @RiskDetectionField(detectType = DetectTypeEnum.TEXT)
    private String keyword;

    /**
     * 商户分类id(逗号拼接)
     */
    @ExcelProperty(value = "商户分类id(逗号拼接)")
    private String cateId;

    /**
     * 品牌id
     */
    @ExcelProperty(value = "品牌id")
    private Long brandId;

    /**
     * 平台分类id
     */
    @ExcelProperty(value = "平台分类id")
    private Long categoryId;

    /**
     * 保障服务ids(英文逗号拼接)
     */
    @ExcelProperty(value = "保障服务ids(英文逗号拼接)")
    private String guaranteeIds;

    /**
     * 商品价格
     */
    @ExcelProperty(value = "商品价格")
    private BigDecimal price;

    /**
     * 币种，默认CNY人名币
     * 人民币（CNY）、美元（USD）、欧元（EUR）、日元（JPY）、英镑（GBP）、加拿大元（CAD）、
     * 澳大利亚元（AUD）、瑞士法郎（CHF）、瑞典克朗（SEK）、新西兰元（NZD）
     */
    @ExcelProperty(value = "币种")
    private String currency;

    /**
     * 会员价格
     */
    @ExcelProperty(value = "会员价格")
    private BigDecimal vipPrice;

    /**
     * 市场价
     */
    @ExcelProperty(value = "市场价")
    private BigDecimal otPrice;

    /**
     * 邮费
     */
    @ExcelProperty(value = "邮费")
    private BigDecimal postage;

    /**
     * 单位名
     */
    @ExcelProperty(value = "单位名")
    @RiskDetectionField(detectType = DetectTypeEnum.TEXT)
    private String unitName;

    /**
     * 销量
     */
    @ExcelProperty(value = "销量")
    private Long sales;

    /**
     * 库存
     */
    @ExcelProperty(value = "库存")
    private Integer stock;

    /**
     * 获得积分
     */
    @ExcelProperty(value = "获得积分")
    private Long giveIntegral;

    /**
     * 成本价
     */
    @ExcelProperty(value = "成本价")
    private BigDecimal cost;

    /**
     * 虚拟销量
     */
    @ExcelProperty(value = "虚拟销量")
    private Long varSales;

    /**
     * 浏览量
     */
    @ExcelProperty(value = "浏览量")
    private Long browse;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sort;

    /**
     * 总后台排序
     */
    @ExcelProperty(value = "总后台排序")
    private Integer ranks;

    /**
     * 规格 0单 1多
     */
    @ExcelProperty(value = "规格 0单 1多")
    private Boolean specType;

    /**
     * 商品标识（0普通商品，1返哺商品，2抖音虚拟商品，3拓展商品）
     */
    @ExcelProperty(value = "商品标识", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=普通商品，1返哺商品，2抖音虚拟商品，3拓展商品")
    private Long productType;

    /**
     * 是否回收站
     */
    @ExcelProperty(value = "是否回收站")
    private Boolean isRecycle;

    /**
     * 是否强制下架，0-否，1-是
     */
    @ExcelProperty(value = "是否强制下架，0-否，1-是")
    private Boolean isForced;

    /**
     * 审核状态：WAITAPPROVE-待审核，PASS-审核成功，FALSE-审核拒绝
     */
    @ExcelProperty(value = "审核状态：WAITAPPROVE-待审核，PASS-审核成功，FALSE-审核拒绝")
    private String auditStatus;

    /**
     * 拒绝原因
     */
    @ExcelProperty(value = "拒绝原因")
    private String reason;

    /**
     * 是否删除
     */
    @ExcelProperty(value = "是否删除")
    private Boolean isDel;

    // 以下为商户数据
    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 是否自营：0-非自营，1-自营
     */
    private Boolean isSelf;

    /**
     * 收藏数量
     */
    private Integer collectCount;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 商品属性
     */
    @NotEmpty(message = "商品属性不能为空")
    private List<SohuProductAttrVo> attrList;

    /**
     * 商品属性值详情
     */
    @NotEmpty(message = "商品属性详情不能为空")
    private List<SohuProductAttrValueVo> attrValueList;

    /**
     * 商品描述
     */
    private String content;

    /**
     * 优惠券id集合
     */
    private List<Long> couponIds;

    /**
     * 优惠券列表（平台端）
     */
    private List<CouponSimpleVo> couponList;

    /**
     * 状态（0：未上架，1：上架）
     */
    private Boolean isShow;

    /**
     * 分佣开关状态（0：不启用分佣，1：启用分佣）
     */
    private Boolean independentIsShow;

    /**
     * 是否是分销商品：0不是 1是
     */
    private Boolean independentType;

    /**
     * 分佣比例、默认0.00
     */
    private BigDecimal independentRatio;

    /**
     * 分销人分佣金额
     */
    private BigDecimal independentPrice;

    /**
     * 销售量
     */
    private Integer saleCount;

    /**
     * 游览量
     */
    private Integer viewCount;

    /**
     * 曝光量
     */
    private Integer exposeCount;

    /**
     * 退货量
     */
    private Integer refundCount;

    /**
     * 平台一级分类id
     */
    private Long firstCategoryId;

    /**
     * 运费模板id
     */
    private Long freightTemplateId;

    @Schema(title = "第三方商品唯一标识", description = "用于查询修改商品操作", example = "12345678", maxLength = 64)
    @RiskDetectionField(detectType = DetectTypeEnum.RICH_TEXT)
    private String thirdProductId;

    /**
     * 商品发货模式 1 商家 2 蜂助手 3 优选供应链
     */
    @Schema(title = "商品发货模式 1 商家 2 蜂助手 3 优选供应链")
    @ExcelProperty(value = "商品发货模式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=商家,2=蜂助手,3=优选供应链")
    private Integer deliveryMode;

    /**
     * 开放平台三方客户端ID
     */
    private Long openClientId;

    /**
     * 用户是否购买该店铺商品
     */
    private Boolean isBuy;

    /**
     * 是否需要用户输入充值账号：0-否，1-是
     */
    @ExcelProperty(value = "是否需要用户输入充值账号：0-否，1-是")
    private Integer isAccount;

    /**
     * 充值账号类型 1 手机号
     */
    @ExcelProperty(value = "充值账号类型 1 手机号")
    private Integer accountType;

    /**
     * 发货方式：1-手动，2-自动
     */
    @ExcelProperty(value = "发货方式：1-手动，2-自动")
    private Integer deliveryType;

    /**
     * 自动发货方式 1 蜂助手
     */
    @ExcelProperty(value = "自动发货方式 1 蜂助手")
    private Integer autoDeliveryType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 移出时间
     */
    private Date removeTime;

}
