package com.sohu.shopgoods.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品属性值规格视图对象
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@Data
@ExcelIgnoreUnannotated
public class SohuProductAttrValueVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 商品ID
     */
    @ExcelProperty(value = "商品ID")
    private Long productId;

    /**
     * 商品属性索引值 (attr_value|attr_value[|....])
     */
    @ExcelProperty(value = "商品属性索引值 (attr_value|attr_value[|....])")
    private String sku;

    /**
     * 属性对应的库存
     */
    @ExcelProperty(value = "属性对应的库存")
    private Integer stock;

    /**
     * 销量
     */
    @ExcelProperty(value = "销量")
    private Long sales;

    /**
     * 属性金额
     */
    @ExcelProperty(value = "属性金额")
    private BigDecimal price;
    /**
     * 币种，默认CNY人名币
     * 人民币（CNY）、美元（USD）、欧元（EUR）、日元（JPY）、英镑（GBP）、加拿大元（CAD）、
     * 澳大利亚元（AUD）、瑞士法郎（CHF）、瑞典克朗（SEK）、新西兰元（NZD）
     */
    @ExcelProperty(value = "币种")
    private String currency;

    /**
     * 兑换所需积分
     */
    @ExcelProperty(value = "兑换所需积分")
    private Integer wantIntegral;

    /**
     * 图片
     */
    @ExcelProperty(value = "图片")
    private String image;

    /**
     * 成本价
     */
    @ExcelProperty(value = "成本价")
    private BigDecimal cost;

    /**
     * 商品条码
     */
    @ExcelProperty(value = "商品条码")
    private String barCode;

    /**
     * 原价
     */
    @ExcelProperty(value = "原价")
    private BigDecimal otPrice;

    /**
     * 重量
     */
    @ExcelProperty(value = "重量")
    private BigDecimal weight;

    /**
     * 体积
     */
    @ExcelProperty(value = "体积")
    private BigDecimal volume;

    /**
     * 一级返佣
     */
    @ExcelProperty(value = "一级返佣")
    private BigDecimal brokerage;

    /**
     * 二级返佣
     */
    @ExcelProperty(value = "二级返佣")
    private BigDecimal brokerageTwo;

    /**
     * 活动类型- PRODUCT=商品，SECKILL=秒杀，BARGAIN=砍价，GROUP=拼团
     */
    @ExcelProperty(value = "活动类型- PRODUCT=商品，SECKILL=秒杀，BARGAIN=砍价，GROUP=拼团")
    private String type;

    /**
     * 活动限购数量
     */
    @ExcelProperty(value = "活动限购数量")
    private Integer quota;

    /**
     * 活动限购数量显示
     */
    @ExcelProperty(value = "活动限购数量显示")
    private Integer quotaShow;

    /**
     * attr_values 创建更新时的属性对应
     */
    @ExcelProperty(value = "attr_values 创建更新时的属性对应")
    private String attrValue;

    /**
     * 是否删除,0-否，1-是
     */
    @ExcelProperty(value = "是否删除,0-否，1-是")
    private Boolean isDel;

    /**
     * 分佣开关状态（0：不启用分佣，1：启用分佣）
     */
    private Boolean independentIsShow;

    /**
     * 是否是分销商品：0不是 1是
     */
    private Boolean independentType;

    /**
     * 分销金额、默认0.00
     */
    @ExcelProperty(value = "分销金额、默认0.00")
    private BigDecimal independentPrice;

    /**
     * 虚拟商品skuID
     */
    @ExcelProperty(value = "虚拟商品skuID")
    private Long virtualSkuId;

    /**
     * 虚拟商品名称
     */
    @ExcelProperty(value = "虚拟商品名称")
    private String virtualSkuName;

    /**
     * 虚拟商品编码
     */
    @ExcelProperty(value = "虚拟商品编码")
    private String virtualSkuCode;

    /**
     * 第三方商品规格唯一标识
     */
    private String thirdSkuId;

    /**
     * 商品发货模式 1 商家 2 蜂助手 3 优选供应链
     */
    @ExcelProperty(value = "商品发货模式")
    private Integer deliveryMode;

    /**
     * 状态（0：未上架，1：上架）
     */
    private Boolean isShow;

}
