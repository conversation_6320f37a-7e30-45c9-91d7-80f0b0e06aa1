package com.sohu.shopgoods.api.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品搜索
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ShopProductReqBo implements Serializable {

    private static final long serialVersionUID = 3481659942630712958L;

    /**
     * 商户id-商户商品搜索的时候必传
     */
    private Long merId;
    /**
     * 搜索关键字
     */
    private String keyword;
    /**
     * 分类id
     */
    private Long cid;
    /**
     * 价格排序-asc,desc
     */
    private String priceOrder;
    /**
     * 销量排序-asc,desc
     */
    private String salesOrder;
    /**
     * 最低价
     */
    private BigDecimal minPrice;
    /**
     * 最高价
     */
    private BigDecimal maxPrice;
    /**
     * 商品标识（0普通商品，1返哺商品,2抖音商品
     */
    private Integer productType;

    /**
     * siteId国家站点id
     */
    private Long siteId;

    /**
     * 分佣开关状态（0：不启用分佣，1：启用分佣）
     */
    private Boolean independentIsShow;

    /**
     * 是否是分销商品：0不是 1是
     */
    private Boolean independentType;

    /**
     * 商品橱窗需要传入的被看人的用户id
     */
    private Long userId;

    /**
     * mcnId
     */
    private Long mcnId;

    /**
     * 降序desc 升序asc
     */
    private String sort;

    /**
     * 商品id集合--Long转String =使用了find_in_set函数需要转字符串
     */
    private List<String> productIds;

    /**
     * 商品id集合--Long
     */
    private List<Long> ids;

    /**
     * 平台一级分类id
     */
    private Long firstCategoryId;

    /**
     * 类型（OnSale：出售中（已上架），2：仓库中（未上架），3：已售罄，4：警戒库存，5：回收站,6:待审核，7：审核失败）
     */
    private int type;

    /**
     * 商户分类id(逗号拼接)
     */
    private String cateId;

    /**
     * 平台分类id
     */
    private Long categoryId;

    /**
     * 系统来源(sohuglobal:狐少少,minglereels:海外短剧),用来区分是哪个系统
     */
    private String sysSource;

    /**
     * 商品发货类型 1 商家 2 蜂助手 3 优选供应链
     */
    private Integer deliveryType;

    /**
     * 分类集合
     */
    private List<Long> categoryIds;

}
