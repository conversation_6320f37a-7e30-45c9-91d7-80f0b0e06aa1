package com.sohu.shopgoods.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品属性值规格业务对象
 *
 * <AUTHOR>
 * @date 2023-06-26
 */

@Data
public class SohuProductAttrValueBo implements Serializable {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 商品ID
     */
    @NotBlank(message = "商品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 商品属性索引值 (attr_value|attr_value[|....])
     */
    @NotBlank(message = "商品属性索引值 (attr_value|attr_value[|....])不能为空", groups = {AddGroup.class, EditGroup.class})
    private String sku;

    /**
     * 属性对应的库存
     */
    @NotBlank(message = "属性对应的库存不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer stock;

    /**
     * 销量
     */
    @NotBlank(message = "销量不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long sales;

    /**
     * 属性金额
     */
    @NotNull(message = "属性金额不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal price;

    /**
     * 币种，默认CNY人名币
     * 人民币（CNY）、美元（USD）、欧元（EUR）、日元（JPY）、英镑（GBP）、加拿大元（CAD）、
     * 澳大利亚元（AUD）、瑞士法郎（CHF）、瑞典克朗（SEK）、新西兰元（NZD）
     */
    @NotNull(message = "币种，默认'CNY'人名币", groups = {AddGroup.class, EditGroup.class})
    private String currency;


    /**
     * 兑换所需积分
     */
    @NotNull(message = "兑换所需积分不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer wantIntegral;

    /**
     * 图片
     */
    @NotBlank(message = "图片不能为空", groups = {AddGroup.class, EditGroup.class})
    private String image;

    /**
     * 成本价
     */
    @NotNull(message = "成本价不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal cost;

    /**
     * 商品条码
     */
    @NotBlank(message = "商品条码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String barCode;

    /**
     * 原价
     */
    @NotNull(message = "原价不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal otPrice;

    /**
     * 重量
     */
    @NotNull(message = "重量不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal weight;

    /**
     * 体积
     */
    @NotNull(message = "体积不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal volume;

    /**
     * 一级返佣
     */
    @NotNull(message = "一级返佣不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal brokerage;

    /**
     * 二级返佣
     */
    @NotNull(message = "二级返佣不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal brokerageTwo;

    /**
     * 活动类型- PRODUCT=商品，SECKILL=秒杀，BARGAIN=砍价，GROUP=拼团
     */
    @NotBlank(message = "活动类型- PRODUCT=商品，SECKILL=秒杀，BARGAIN=砍价，GROUP=拼团不能为空", groups = {AddGroup.class, EditGroup.class})
    private String type;

    /**
     * 活动限购数量
     */
    @NotNull(message = "活动限购数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer quota;

    /**
     * 活动限购数量显示
     */
    @NotNull(message = "活动限购数量显示不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer quotaShow;

    /**
     * attr_values 创建更新时的属性对应
     */
    @NotBlank(message = "attr_values 创建更新时的属性对应不能为空", groups = {AddGroup.class, EditGroup.class})
    private String attrValue;

    /**
     * 是否删除,0-否，1-是
     */
    @NotNull(message = "是否删除,0-否，1-是不能为空", groups = {AddGroup.class, EditGroup.class})
    private Boolean isDel;

    /**
     * 分佣开关状态（0：不启用分佣，1：启用分佣）
     */
    private Boolean independentIsShow;

    /**
     * 是否是分销商品：0不是 1是
     */
    private Boolean independentType;

    /**
     * 分销金额、默认0.00
     */
    private BigDecimal independentPrice;

    /**
     * 虚拟商品skuID
     */
    private Long virtualSkuId;

    /**
     * 第三方商品规格唯一标识
     */
    private String thirdSkuId;

    /**
     * 商品发货模式 1 商家 2 蜂助手 3 优选供应链
     */
    private Integer deliveryMode;

    /**
     * 状态（0：未上架，1：上架）
     */
    private Boolean isShow;

}
