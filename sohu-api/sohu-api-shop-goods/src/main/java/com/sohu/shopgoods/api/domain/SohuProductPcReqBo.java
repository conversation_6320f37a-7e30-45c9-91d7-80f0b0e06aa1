package com.sohu.shopgoods.api.domain;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品业务对象-pc
 *
 * <AUTHOR>
 * @date 2023-06-26
 */

@Data
public class SohuProductPcReqBo implements Serializable {

    /**
     * 类型（1：出售中（已上架），2：仓库中（未上架），3：已售罄，4：警戒库存，5：回收站,6:待审核，7：审核失败）
     */
    @NotNull(message = "商品类型不能为空")
    @Range(min = 1, max = 7, message = "未知的商品类型")
    private int type;

    /**
     * 站点id
     */
    @NotNull(message = "站点id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long siteId;

    /**
     * 狐小店商户mer_id
     */
    @NotNull(message = "狐小店商户mer_id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long merId;

    /**
     * 关键字
     */
    @NotBlank(message = "关键字不能为空", groups = {AddGroup.class, EditGroup.class})
    private String keyword;

    /**
     * 平台分类id
     */
    @NotNull(message = "平台分类id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long categoryId;

    /**
     * 商户是否自营：0-非自营，1-自营
     */
    private Integer isSelf;

    /**
     * 商品标识（0普通商品，1返哺商品，2抖音虚拟商品，3拓展商品）
     */
    @NotNull(message = "商品标识（0普通商品，1返哺商品，2抖音虚拟商品，3拓展商品）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productType;

    /**
     * 分佣开关状态（0：不启用分佣，1：启用分佣）
     */
    @NotNull(message = "分佣开关状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private Boolean independentIsShow;

    /**
     * 是否是分销商品：0不是 1是
     */
    @NotNull(message = "是否是分销商品不能为空", groups = {AddGroup.class, EditGroup.class})
    private Boolean independentType;

    /**
     * 分佣比例、默认0.00
     */
    private BigDecimal independentRatio;
    /**
     * 系统来源(sohuglobal:狐少少,minglereels:海外短剧)
     */
    private String SysSource;

    /**
     * 商品发货模式 1 商家 2 蜂助手 3 优选供应链
     */
    private Integer deliveryMode;

}
