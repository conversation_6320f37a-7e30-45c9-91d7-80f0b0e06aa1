package com.sohu.admin.api;

import com.sohu.admin.api.bo.SohuQualificationListBo;
import com.sohu.admin.api.vo.SohuAgentUserVo;
import com.sohu.admin.api.vo.SohuCategoryBrandBusinessSettingsVo;
import com.sohu.admin.api.vo.SohuInviteChannelVo;
import com.sohu.admin.api.vo.SohuQualificationVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.middle.api.vo.SohuVideoVo;

import java.util.List;

public interface RemoteAdminService {

    /**
     * 绑定代理机构成员
     */
    @Deprecated
    void bindAgentUser(Long agentId, String roleKey);

    /**
     * 绑定代理机构成员
     */
    void bindAgentUser(Long agentId, String roleKey, Long userId, Long siteId, String agentRole, Long agentChannelId);

    /**
     * 更新代理列表邀请状态
     */
    void updateInviteState(Long userId, String merchantName, String accountType, String contactEmail);

    /**
     * 判断用户是否存在邀请记录
     */
    Boolean existsAgentUserOfJoin(Long userId, Long agentId);

    /**
     * 判断用户是否存在待认证记录
     *
     * @param userId
     * @return
     */
    Boolean existsAgentUserOfWaitJoin(Long userId);

    /**
     * 添加角色
     *
     * @param userId
     * @param roleKey
     * @return
     */
    Boolean addRoleKey(Long userId, String roleKey);

    /**
     * 根据专题分类获取视频内容列表
     */
    List<SohuVideoVo> queryVideoListByTopicId(String topicIdent);

    /**
     * 根据专题分类获取视频内容列表,分页查询
     */
    List<SohuVideoVo> queryVideoListByTopicId(String topicIdent, PageQuery pageQuery);

    /**
     * 刷新热门推荐列表定时任务
     */
    void refreshHotRecommendListJobHandler();

    /**
     * 获取类目/品牌资质列表
     */
    List<SohuQualificationVo> getQualificationList(SohuQualificationListBo bo);

    /**
     * 根据业务id和业务类型查询类目品牌业务设置信息
     */
    SohuCategoryBrandBusinessSettingsVo getCategoryBrandBusinessSettings(Long businessId, String businessType);

    /**
     * 查询邀请列表
     *
     * @return
     */
    List<SohuAgentUserVo> queryInviteList();

    /**
     * 获取邀请渠道信息
     *
     * @param consumerUserId 被邀请人Id
     * @return
     */
    SohuInviteChannelVo queryInviteChannel(Long consumerUserId);

    /**
     * 保存用户留存数据
     */
    void saveUserRetentionStats();

    /**
     * 查询代理机构用户列表
     *
     * @param agentId   代理机构ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 代理机构用户列表
     */
    List<SohuAgentUserVo> queryAgentUserList(Long agentId, String startTime, String endTime);

    /**
     * 获取绑定信息
     *
     * @param userId
     * @param agentId
     * @param roleType
     * @param status
     * @return
     */
    SohuAgentUserVo getBindInfo(Long userId, Long agentId, String roleType, String status);
}
