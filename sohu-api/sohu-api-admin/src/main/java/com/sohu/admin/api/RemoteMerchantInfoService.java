package com.sohu.admin.api;

import com.sohu.admin.api.model.SohuMerchantInfoModel;

/**
 * 商户信息对外接口
 *
 * <AUTHOR>
 */
public interface RemoteMerchantInfoService {

    /**
     * 根据merId查商户信息
     */
    SohuMerchantInfoModel selectByMerId(Long merId);

    /**
     * 处理机审结果
     *
     * @param busyCode
     * @param isPass
     * @param reason
     */
    void handleRobot(String busyCode, Boolean isPass, String reason);

}
