package com.sohu.admin.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import static com.sohu.common.core.utils.DateUtils.TIME_ZONE_DEFAULT;


/**
 * 风控策略核心视图对象
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@ExcelIgnoreUnannotated
public class SohuBanPolicyVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 策略ID
     */
    @ExcelProperty(value = "策略ID")
    private Long id;

    /**
     * 策略名称（例如：轻度限制策略）
     */
    @ExcelProperty(value = "策略名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "例=如：轻度限制策略")
    private String policyName;

    /**
     * 策略级别（1-10级，数字越大限制越严）
     */
    @ExcelProperty(value = "策略级别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=-10级，数字越大限制越严")
    private Long policyLevel;

    /**
     * 策略应用类型
     */
    @ExcelProperty(value = "策略应用类型")
    private String policyType;

    /**
     * 是否全局策略（0：否 1：是）
     */
    @ExcelProperty(value = "是否全局策略", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=：否,1=：是")
    private Integer isGlobal;

    /**
     * 策略详细描述
     */
    @ExcelProperty(value = "策略详细描述")
    private String description;

    /**
     * 策略状态
     */
    @ExcelProperty(value = "策略状态")
    private String activeStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS, timezone = TIME_ZONE_DEFAULT)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS, timezone = TIME_ZONE_DEFAULT)
    private Date updateTime;

}
