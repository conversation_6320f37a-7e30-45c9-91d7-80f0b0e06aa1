package com.sohu.admin.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 受控接口资源业务对象
 *
 * <AUTHOR>
 * @date 2025-06-17
 */

@Data
public class SohuBanInterfaceBo implements Serializable {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 接口名称（例如：发布帖子）
     */
    @NotBlank(message = "接口名称（例如：发布帖子）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String interfaceName;

    /**
     * 接口路径（例如：/api/content/create）
     */
    @NotBlank(message = "接口路径（例如：/api/content/create）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String interfaceUrl;

    /**
     * HTTP方法（GET/POST/PUT/DELETE/ALL）
     */
    @NotBlank(message = "HTTP方法（GET/POST/PUT/DELETE/ALL）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String httpMethod;

    /**
     * 风险权重（1-10）
     */
    @NotNull(message = "风险权重（1-10）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer riskWeight;

    /**
     * 接口功能描述
     */
    private String description;


}
