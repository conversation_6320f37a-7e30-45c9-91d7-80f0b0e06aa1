package com.sohu.admin.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * [多渠道]商品品牌映射业务对象
 *
 * <AUTHOR>
 * @date 2025-07-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ProductBrandMappingBo extends SohuEntity {

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 渠道标识
     */
    @NotBlank(message = "渠道标识不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channel;

    /**
     * 我方品牌ID (sohu_product_brand.id)
     */
    @NotNull(message = "我方品牌ID (sohu_product_brand.id)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long ourBrandId;

    /**
     * 三方品牌ID (third_party_brand.id)
     */
    @NotNull(message = "三方品牌ID (third_party_brand.id)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long thirdPartyBrandId;

    /**
     * 
     */
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ourBrandName;

    /**
     * 
     */
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String thirdPartyBrandName;

    /**
     * 最后操作人
     */
    @NotBlank(message = "最后操作人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String operator;

}
