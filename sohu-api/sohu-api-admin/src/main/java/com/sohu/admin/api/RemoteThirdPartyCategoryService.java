package com.sohu.admin.api;

import com.sohu.admin.api.bo.ThirdPartyCategoryBo;
import com.sohu.admin.api.vo.ThirdPartyCategoryVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 三方平台商品分类远程服务接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface RemoteThirdPartyCategoryService {

    /**
     * 查询三方平台商品分类
     */
    ThirdPartyCategoryVo queryById(Long id);

    /**
     * 查询三方平台商品分类列表
     */
    TableDataInfo<ThirdPartyCategoryVo> queryPageList(ThirdPartyCategoryBo bo, PageQuery pageQuery);

    /**
     * 查询三方平台商品分类列表
     */
    List<ThirdPartyCategoryVo> queryList(ThirdPartyCategoryBo bo);

    /**
     * 新增三方平台商品分类
     */
    Boolean insertByBo(ThirdPartyCategoryBo bo);

    /**
     * 修改三方平台商品分类
     */
    Boolean updateByBo(ThirdPartyCategoryBo bo);

    /**
     * 校验并批量删除三方平台商品分类信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
