package com.sohu.admin.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 风控策略核心业务对象
 *
 * <AUTHOR>
 * @date 2025-06-17
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuBanPolicyBo extends SohuEntity {

    public transient static final String ACTIVE = "ACTIVE";

    /**
     * 策略ID
     */
    @NotNull(message = "策略ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 策略名称（例如：轻度限制策略）
     */
    @NotBlank(message = "策略名称（例如：轻度限制策略）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String policyName;

    /**
     * 策略级别（1-10级，数字越大限制越严）
     */
    @NotNull(message = "策略级别（1-10级，数字越大限制越严）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long policyLevel;

    /**
     * 策略应用类型
     */
    @NotBlank(message = "策略应用类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String policyType;

    /**
     * 是否全局策略（0：否 1：是）
     */
    @NotNull(message = "是否全局策略（0：否 1：是）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer isGlobal;

    /**
     * 策略详细描述
     */
    @NotBlank(message = "策略详细描述不能为空", groups = {AddGroup.class, EditGroup.class})
    private String description;

    /**
     * 策略状态
     */
    @NotBlank(message = "策略状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private String activeStatus;


}
