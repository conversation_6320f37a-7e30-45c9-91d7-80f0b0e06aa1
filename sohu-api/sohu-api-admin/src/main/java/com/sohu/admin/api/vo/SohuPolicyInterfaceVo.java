package com.sohu.admin.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 策略与接口关联视图对象
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@ExcelIgnoreUnannotated
public class SohuPolicyInterfaceVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private String id;

    /**
     * sohu_ban_policy表ID
     */
    @ExcelProperty(value = "sohu_ban_policy表ID")
    private String policyId;

    /**
     * sohu_policy_interface表ID
     */
    @ExcelProperty(value = "sohu_policy_interface表ID")
    private String interfaceId;

    /**
     * 限制类型
     */
    @ExcelProperty(value = "限制类型")
    private String restrictionType;


}
