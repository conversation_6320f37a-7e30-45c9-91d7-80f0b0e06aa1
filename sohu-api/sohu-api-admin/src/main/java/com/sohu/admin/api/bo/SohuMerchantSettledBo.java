package com.sohu.admin.api.bo;

import com.sohu.admin.api.bo.merchant.AccountInfo;
import com.sohu.admin.api.bo.merchant.CategoryInfo;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/27 15:50
 */
@Data
public class SohuMerchantSettledBo implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "商户ID不能为空", groups = {EditGroup.class})
    @Schema(name = "id", description = "商户ID", example = "1")
    private Long id;

    @NotBlank(message = "商户名称-唯一值不能为空", groups = {AddGroup.class, EditGroup.class})
    @Schema(name = "name", description = "商户名称", example = "狐小店个人店")
    private String name;

    @NotNull(message = "商户分类不能为空", groups = {AddGroup.class, EditGroup.class})
    @Schema(name = "merchantType", description = "商户类型：personal-个人，business-企业 person_business 个体工商户", example = "personal")
    private String merchantType;

    @NotNull(message = "经营类目信息不能为空", groups = {AddGroup.class})
    @Schema(name = "categoryInfos", description = "经营类目信息", example = "1")
    private List<CategoryInfo> categoryInfos;

    @Schema(name = "brandInfos", description = "品牌信息", example = "1")
    private List<CategoryInfo> brandInfos;

    @NotNull(message = "账户信息不能为空", groups = {AddGroup.class})
    @Schema(name = "accountInfo", description = "账户信息", example = "1")
    private AccountInfo accountInfo;

    @Schema(name = "isUpgrage", description = "是否升级 0 否 1 是", example = "0")
    private Integer isUpgrage;
}
