package com.sohu.admin.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * [多渠道]商品品牌映射视图对象
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@ExcelIgnoreUnannotated
public class ProductBrandMappingVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 渠道标识
     */
    @ExcelProperty(value = "渠道标识")
    private String channel;

    /**
     * 我方品牌ID (sohu_product_brand.id)
     */
    @ExcelProperty(value = "我方品牌ID (sohu_product_brand.id)")
    private Long ourBrandId;

    /**
     * 三方品牌ID (third_party_brand.id)
     */
    @ExcelProperty(value = "三方品牌ID (third_party_brand.id)")
    private Long thirdPartyBrandId;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String ourBrandName;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String thirdPartyBrandName;

    /**
     * 最后操作人
     */
    @ExcelProperty(value = "最后操作人")
    private Long operator;

}
