package com.sohu.admin.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;



/**
 * 三方平台商品分类视图对象
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ExcelIgnoreUnannotated
public class ThirdPartyCategoryVo extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 三方分类ID
     */
    @ExcelProperty(value = "三方分类ID")
    private Long id;

    /**
     * 三方父分类ID
     */
    @ExcelProperty(value = "三方父分类ID")
    private Long parentId;

    /**
     * 三方分类名称
     */
    @ExcelProperty(value = "三方分类名称")
    private String name;

    /**
     * 层级 (1, 2, 3)
     */
    @ExcelProperty(value = "层级 (1, 2, 3)")
    private Long level;

    /**
     * 分类全路径
     */
    @ExcelProperty(value = "分类全路径")
    private String fullPath;

    /**
     * 是否叶子节点 (level=3的节点)
     */
    @ExcelProperty(value = "是否叶子节点 (level=3的节点)")
    private Integer isLeaf;
}
