package com.sohu.admin.api;

import com.sohu.admin.api.bo.SohuMerchantCateBo;
import com.sohu.admin.api.bo.SohuMerchantEnableBo;
import com.sohu.admin.api.bo.SohuMerchantSettledBo;
import com.sohu.admin.api.bo.playlet.PlayletMerchantSearchBo;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.admin.api.vo.*;
import com.sohu.admin.api.vo.playlet.PlayletMerchantApplyListVo;
import com.sohu.common.core.vo.NodeVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.vo.SohuMerchantVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商户服务
 *
 * <AUTHOR>
 */
public interface RemoteMerchantService {

    /**
     * 狐少少店铺申请及重新申请
     */
    Boolean insertByBo(SohuMerchantModel bo);

    /**
     * 根据id查详情
     */
    SohuMerchantModel selectById(Long id);

    /**
     * 根据userId查商户列表
     */
    List<SohuMerchantModel> selectByUserId(Long userId);


    /**
     * 根据姓名模糊查询所有商户列表
     */
    List<SohuMerchantModel> queryList(String name);


    /**
     * 根据userId及站点查商户
     */
    SohuMerchantModel selectByUserIdAndCitySiteId(Long userId, Long siteId);

    /**
     * 根据userId及国家站点查商户,flag查询自己的列表
     */
    List<SohuMerchantModel> selectByUserIdAndSiteId(Long userId, Long siteId, Boolean flag);

    /**
     * 根据ids查询
     *
     * @param merIdList
     */
    Map<Long, SohuMerchantModel> getMerIdMapByIdList(Collection<Long> merIdList);

    /**
     * 站点商户列表
     *
     * @param keyword
     * @param sort
     * @param siteId
     * @param citySiteId
     * @param pageQuery
     */
    TableDataInfo<SohuMerchantModel> pageBySiteId(String keyword, String sort, Long siteId, Long citySiteId, PageQuery pageQuery);

    /**
     * 商户详情
     */
    SohuMerchantModel getInfo(Long id);

    /**
     * 商户详情
     */
    SohuMerchantModel getByPhone(String phone);

    /**
     * 批量修改销售量
     *
     * @param merchantModelList
     */
    Boolean updateBatch(List<SohuMerchantModel> merchantModelList);

    /**
     * 根据ids查询店铺
     *
     * @param merIds
     */
    List<SohuMerchantModel> selectByIds(Collection<Long> merIds);

    Map<Long, SohuMerchantModel> selectMapByIds(Collection<Long> merIds);


    /**
     * 商户列表查询
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<PlayletMerchantApplyListVo> getMerchantApplyList(PlayletMerchantSearchBo bo, PageQuery pageQuery);

    /**
     * 获取所有开启的商户
     *
     * @return
     */
    List<SohuMerchantModel> getMerchantOpenList(String sysSource);

    /**
     * 获取商品店铺列表
     *
     * @param sysSource
     * @return
     */
    List<SohuMerchantModel> getProductMerchantList(String sysSource);

    /**
     * 店铺入驻申请
     *
     * @param bo
     * @return
     */
    Boolean settled(SohuMerchantSettledBo bo);

    /**
     * 店铺入驻修改
     *
     * @param bo
     * @return
     */
    Boolean settledEdit(SohuMerchantSettledBo bo);

    /**
     * 店铺详情
     *
     * @param id
     * @return
     */
    SohuMerchantSettledVo detail(Long id);

    /**
     * 获取用户余额及在途订单
     *
     * @return
     */
    SohuUserCondVo getUserCond(Long userId);

    /**
     * 账户升级
     *
     * @param upgradeBo
     * @return
     */
    Boolean upgrage(SohuMerchantSettledBo upgradeBo);

    /**
     * 修改商户状态
     *
     * @param userId
     * @param status
     * @param reason
     * @return
     */
    Boolean updateMerchantStatus(Long userId,Long merId, String status, String reason);

    /**
     * 新增类目-资质
     * @param bo
     * @return
     */
    Boolean addClass(SohuMerchantCateBo bo);

    /**
     * 新增品牌-资质
     * @param bo
     * @return
     */
    Boolean addBrand(SohuMerchantCateBo bo);

    /**
     * 申请闭店
     * @param id
     * @return
     */
    Boolean close(Long id);

    /**
     * 获取保证金列表
     * @param id
     * @return
     */
    List<SohuMerchantBondVo> bondList(Long id);

    /**
     * 获取类目列表
     * @param id
     * @return
     */
    List<NodeVo> getCateList(Long id);

    /**
     * 获取品牌列表
     * @param id
     * @return
     */
    List<SohuMerchantBrandVo> getBrandList(Long id);

    /**
     * 启用、禁用类目
     * @param bo
     * @return
     */
    Boolean enableClass(SohuMerchantEnableBo bo);

    /**
     * 启用、禁用品牌
     * @param bo
     * @return
     */
    Boolean enableBrand(SohuMerchantEnableBo bo);

    /**
     * 获取商家经营基本信息
     * @param merId 商家ID
     * @return {@link SohuMerchantQualificationInfoVo}
     */
    SohuMerchantQualificationInfoVo getMerchantQualificationInfo(Long merId);

    /**
     * 根据状态查询商家
     * @param status
     * @return
     */
    List<SohuMerchantModel> selectByStatus(List<String> status);

    /**
     * 检测商家名称是否重复
     * @param merchantName
     * @return
     */
    Boolean existMerchantName(String merchantName, Long merchantId);

    /**
     * 账户升级成功，门店对应升级
     * @param userId
     * @return
     */
    Boolean merchantUpgradeHandler(Long userId);

    /**
     * 获取商家已通过类目
     * @param merchantId
     * @return
     */
    List<Long> getCateIdByPass(Long merchantId);

    /**
     * 获取商家已通过品牌
     * @param merchantId
     * @param cateId
     * @return
     */
    List<Long> getBrandIdByPass(Long merchantId,Long cateId);

    /**
     * 基于id查询商家明细
     *
     * @param id
     * @return
     */
    SohuMerchantVo getInfoById(Long id);

    /**
     * 处理机审结果
     *
     * @param busyCode
     * @param isPass
     * @param reason
     */
    void handleRobot(String busyCode, Boolean isPass, String reason);
}