package com.sohu.admin.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import static com.sohu.common.core.utils.DateUtils.TIME_ZONE_DEFAULT;


/**
 * 受控接口资源视图对象
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@ExcelIgnoreUnannotated
public class SohuBanInterfaceVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 接口名称（例如：发布帖子）
     */
    @ExcelProperty(value = "接口名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "例=如：发布帖子")
    private String interfaceName;

    /**
     * 接口路径（例如：/api/content/create）
     */
    @ExcelProperty(value = "接口路径", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "例=如：/api/content/create")
    private String interfaceUrl;

    /**
     * HTTP方法（GET/POST/PUT/DELETE/ALL）
     */
    @ExcelProperty(value = "HTTP方法", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "G=ET/POST/PUT/DELETE/ALL")
    private String httpMethod;

    /**
     * 风险权重（1-10）
     */
    @ExcelProperty(value = "风险权重", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=-10")
    private Integer riskWeight;

    /**
     * 接口功能描述
     */
    @ExcelProperty(value = "接口功能描述")
    private String description;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS, timezone = TIME_ZONE_DEFAULT)
    private Date createTime;


}
