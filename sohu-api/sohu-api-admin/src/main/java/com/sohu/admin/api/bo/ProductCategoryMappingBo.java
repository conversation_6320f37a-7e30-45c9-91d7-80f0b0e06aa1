package com.sohu.admin.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.io.Serializable;
import java.util.Date;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 商品分类映射业务对象
 *
 * <AUTHOR>
 * @date 2025-07-09
 */

@Data
public class ProductCategoryMappingBo implements Serializable {

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 渠道标识 (如: YOUXUAN, A, B)
     */
    @NotBlank(message = "渠道标识 (如: YOUXUAN, A, B)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channel;

    /**
     * 我方商户Id
     */
    @NotBlank(message = "我方商户Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String merId;

    /**
     * 我方二级分类ID (sohu_product_category.id)
     */
    @NotNull(message = "我方二级分类ID (sohu_product_category.id)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long ourCategoryId;

    /**
     * 三方三级分类ID (third_party_category.id)
     */
    @NotNull(message = "三方三级分类ID (third_party_category.id)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long thirdPartyCategoryId;

    /**
     * 我方分类路径，如: 服装/男装T恤
     */
    @NotBlank(message = "我方分类路径，如: 服装/男装T恤不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ourCategoryPath;

    /**
     * 三方分类路径，如: 服饰穿搭/男装/T恤
     */
    @NotBlank(message = "三方分类路径，如: 服饰穿搭/男装/T恤不能为空", groups = { AddGroup.class, EditGroup.class })
    private String thirdPartyCategoryPath;

    /**
     * 最后操作人
     */
    @NotBlank(message = "最后操作人不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long operator;

}
