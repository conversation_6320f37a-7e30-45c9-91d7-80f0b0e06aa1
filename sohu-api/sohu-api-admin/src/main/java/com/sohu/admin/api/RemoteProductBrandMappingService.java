package com.sohu.admin.api;

import com.sohu.admin.api.bo.ProductBrandMappingBo;
import com.sohu.admin.api.vo.ProductBrandMappingVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 商品品牌映射远程服务接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface RemoteProductBrandMappingService {

    /**
     * 查询商品品牌映射
     */
    ProductBrandMappingVo queryById(Long id);

    /**
     * 查询商品品牌映射列表
     */
    TableDataInfo<ProductBrandMappingVo> queryPageList(ProductBrandMappingBo bo, PageQuery pageQuery);

    /**
     * 查询商品品牌映射列表
     */
    List<ProductBrandMappingVo> queryList(ProductBrandMappingBo bo);

    /**
     * 新增商品品牌映射
     */
    Boolean insertByBo(ProductBrandMappingBo bo);

    /**
     * 修改商品品牌映射
     */
    Boolean updateByBo(ProductBrandMappingBo bo);

    /**
     * 校验并批量删除商品品牌映射信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
