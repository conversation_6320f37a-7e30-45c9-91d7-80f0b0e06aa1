package com.sohu.admin.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

/**
 * 封禁记录业务对象
 *
 * <AUTHOR>
 * @date 2025-06-12
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuBanRecordsBo extends SohuEntity {

    /**
     * 封禁记录的唯一ID，自增主键
     */
    @NotNull(message = "封禁记录的唯一ID，自增主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 被封禁用户的ID (关联用户表)
     */
    @NotNull(message = "被封禁用户的ID (关联用户表)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 封禁类型 (例如: IP, 设备, ACCOUNT账号)
     */
    @NotBlank(message = "封禁类型 (例如: IP, 设备, ACCOUNT账号)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String banType;

    /**
     * 封禁时长描述 (例如: 永封, 1年, 30天)
     */
    @NotBlank(message = "封禁时长描述 (例如: 永封, 1年, 30天)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String durationDescription;

    /**
     * 封禁执行/开始的时间戳
     */
    @NotNull(message = "封禁执行/开始的时间戳不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date banDatetime;

    /**
     * 封禁预计自然结束的时间戳 (永久封禁时为NULL)
     */
    @NotNull(message = "封禁预计自然结束的时间戳 (永久封禁时为NULL)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date expectedEndDatetime;

    /**
     * 当前封禁状态 (ACTIVE:封禁中, LIFTED:已解封-手动, EXPIRED:已解封-到期)
     */
    @NotBlank(message = "当前封禁状态 (ACTIVE:封禁中, LIFTED:已解封-手动, EXPIRED:已解封-到期)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 执行封禁的原因 (例如: 涉嫌诈骗)
     */
    @NotBlank(message = "执行封禁的原因 (例如: 涉嫌诈骗)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String banReason;

    /**
     * 执行封禁的管理员ID/用户名或系统标识
     */
    @NotBlank(message = "执行封禁的管理员ID/用户名或系统标识不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long banOperatorId;

    /**
     * 解封的时间戳
     */
    @NotNull(message = "解封的时间戳不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date unbanDatetime;

    /**
     * 解封的原因 (例如: 到期自动解封, 已整改)
     */
    @NotBlank(message = "解封的原因 (例如: 到期自动解封, 已整改)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String unbanReason;

    /**
     * 执行解封的管理员ID/用户名或系统/自动标识
     */
    @NotBlank(message = "执行解封的管理员ID/用户名或系统/自动标识不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long unbanOperatorId;

    /**
     * 此记录最后一次重要操作的时间戳 (封禁或解封时间)
     */
    @NotNull(message = "此记录最后一次重要操作的时间戳 (封禁或解封时间)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date lastOperationDatetime;

    /**
     * 被封禁的IP地址 (当封禁类型为IP时使用)
     */
    private String banIp;

    /**
     * 被封禁的设备号 (当封禁类型为DEVICE时使用)
     */
    private String banDevice;

}
