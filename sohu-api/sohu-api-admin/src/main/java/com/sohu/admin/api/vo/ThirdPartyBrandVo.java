package com.sohu.admin.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;



/**
 * [多渠道]三方平台商品品牌视图对象
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@ExcelIgnoreUnannotated
public class ThirdPartyBrandVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 三方品牌ID
     */
    @ExcelProperty(value = "三方品牌ID")
    private Long id;

    /**
     * 渠道标识 (如: ZHENXIN, PLATFORM_B)
     */
    @ExcelProperty(value = "渠道标识 (如: ZHENXIN, PLATFORM_B)")
    private String channel;

    /**
     * 三方品牌名称
     */
    @ExcelProperty(value = "三方品牌名称")
    private String name;

    /**
     * 品牌logo的URL (对应thumbnail_img)
     */
    @ExcelProperty(value = "品牌logo的URL (对应thumbnail_img)")
    private String logoUrl;

    /**
     * 关联的三方分类ID列表 (以逗号分隔的字符串)
     */
    @ExcelProperty(value = "关联的三方分类ID列表 (以逗号分隔的字符串)")
    private String relatedCategoryIds;

    /**
     * 是否被逻辑删除
     */
    @ExcelProperty(value = "是否被逻辑删除")
    private Integer isDeleted;


}
