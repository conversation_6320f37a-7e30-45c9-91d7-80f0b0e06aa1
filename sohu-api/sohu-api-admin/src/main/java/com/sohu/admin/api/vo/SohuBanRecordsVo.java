package com.sohu.admin.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;



/**
 * 封禁记录视图对象
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@ExcelIgnoreUnannotated
public class SohuBanRecordsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 封禁记录的唯一ID，自增主键
     */
    private Long id;

    /**
     * 被封禁用户的ID (关联用户表)
     */
    @ExcelProperty(value = "用户的ID")
    private Long userId;

    /**
     * 用户昵称
     */
    @ExcelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 用户手机
     */
    @ExcelProperty(value = "用户手机")
    private String phoneNumber;

    /**
     * 用户头像
     */
    @ExcelProperty(value = "用户头像")
    private String avatar;

    /**
     * 被封禁的IP地址
     */
    private String ip;

    /**
     * 被封禁的设备标识 (例如: 设备ID, 设备指纹)
     */
    private String device;

    /**
     * 封禁类型 (例如: IP, 设备, ACCOUNT账号)
     */
    @ExcelProperty(value = "封禁类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "ACCOUNT=账号")
    private String banType;

    /**
     * 封禁时长描述 (例如: 永封, 1年, 30天)
     */
    @ExcelProperty(value = "封禁时长描述")
    private String durationDescription;

    /**
     * 封禁执行/开始的时间戳
     */
    private Date banDatetime;

    /**
     * 封禁预计自然结束的时间戳 (永久封禁时为NULL)
     */
    private Date expectedEndDatetime;

    /**
     * 当前封禁状态 (ACTIVE:封禁中, LIFTED:已解封-手动, EXPIRED:已解封-到期)
     */
    @ExcelProperty(value = "当前封禁状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "active=封禁中,lifted=已解封")
    private String status;

    /**
     * 执行封禁的原因 (例如: 涉嫌诈骗)
     */
    @ExcelProperty(value = "执行封禁的原因 (例如: 涉嫌诈骗)")
    private String banReason;

    /**
     * 执行封禁的管理员ID/用户名或系统标识
     */
    private Long banOperatorId;

    /**
     * 解封的时间戳
     */
    private Date unbanDatetime;

    /**
     * 解封的原因 (例如: 到期自动解封, 已整改)
     */
    @ExcelProperty(value = "解封理由")
    private String unbanReason;

    /**
     * 执行解封的管理员ID/用户名或系统/自动标识
     */
    @ExcelProperty(value = "操作员")
    private Long unbanOperatorId;

    /**
     * 此记录最后一次重要操作的时间戳 (封禁或解封时间)
     */
    private Date lastOperationDatetime;

    /**
     * 记录创建时间 (数据插入时的时间戳)
     */
    private Date createdTime;

    /**
     * 记录最后更新时间 (数据更新时的时间戳)
     */
    @ExcelProperty(value = "记录最后更新时间")
    private Date updatedTime;
    /**
     * 封禁策略ID (sohu_ban_policy主键ID)
     */
    private Long banPolicyId;

}
