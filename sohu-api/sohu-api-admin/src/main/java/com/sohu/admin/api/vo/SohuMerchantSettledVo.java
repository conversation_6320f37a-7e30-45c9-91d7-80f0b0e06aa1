package com.sohu.admin.api.vo;

import com.sohu.admin.api.bo.merchant.AccountInfo;
import com.sohu.admin.api.bo.merchant.CategoryInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/27 15:50
 */
@Data
public class SohuMerchantSettledVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(name = "id", description = "商户ID", example = "1")
    private Long id;

    @Schema(name = "name", description = "商户名称", example = "狐小店个人店")
    private String name;

    @Schema(name = "merchantType", description = "商户类型：personal-个人，business-企业 person_business-个体工商户", example = "personal")
    private String merchantType;

    @Schema(name = "auditStatus", description = "审核状态：WaitApprove-待审核，Pass-审核成功，Refuse-审核拒绝 AuthFail 认证未过 CloseApprove-闭店待审核 CloseRefuse-闭店审核拒绝  ClosePass-闭店审核通过", example = "WaitApprove")
    private String auditStatus;

    @Schema(name = "categoryInfos", description = "经营类目信息", example = "1")
    private List<CategoryInfo> categoryInfos;

    @Schema(name = "brandInfos", description = "品牌信息", example = "1")
    private List<CategoryInfo> brandInfos;

    @Schema(name = "accountInfo", description = "账户信息", example = "1")
    private AccountInfo accountInfo;

    @Schema(name = "hasMerchantBondPay", description = "是否需要缴纳保证金", example = "true")
    private Boolean hasMerchantBondPay;

    @Schema(name = "createTime", description = "创建时间", example = "2023-04-27 15:50:00")
    private Date createTime;

    @Schema(name = "denialReason", description = "拒绝原因", example = "审核不通过原因")
    private String denialReason;

    @Schema(name = "avatar", description = "店铺头像", example = "https://www.baidu.com/img/bd_logo1.png")
    private String avatar;
}
