package com.sohu.admin.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 策略与接口关联业务对象
 *
 * <AUTHOR>
 * @date 2025-06-17
 */

@Data
public class SohuPolicyInterfaceBo implements Serializable {

    /**
     * 主键ID
     */
    @NotBlank(message = "主键ID不能为空", groups = {EditGroup.class})
    private String id;

    /**
     * sohu_ban_policy表ID
     */
    @NotBlank(message = "sohu_ban_policy表ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String policyId;

    /**
     * sohu_policy_interface表ID
     */
    @NotBlank(message = "sohu_policy_interface表ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String interfaceId;

    /**
     * 限制类型
     */
    @NotBlank(message = "限制类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String restrictionType;


}
