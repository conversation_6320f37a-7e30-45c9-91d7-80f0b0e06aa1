package com.sohu.admin.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * [多渠道]三方平台商品品牌业务对象
 *
 * <AUTHOR>
 * @date 2025-07-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ThirdPartyBrandBo extends SohuEntity {

    /**
     * 三方品牌ID
     */
    @NotNull(message = "三方品牌ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 渠道标识 (如: ZHENXIN, PLATFORM_B)
     */
    @NotBlank(message = "渠道标识 (如: ZHENXIN, PLATFORM_B)不能为空", groups = { EditGroup.class })
    private String channel;

    /**
     * 三方品牌名称
     */
    @NotBlank(message = "三方品牌名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 品牌logo的URL (对应thumbnail_img)
     */
    @NotBlank(message = "品牌logo的URL (对应thumbnail_img)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String logoUrl;

    /**
     * 关联的三方分类ID列表 (以逗号分隔的字符串)
     */
    @NotBlank(message = "关联的三方分类ID列表 (以逗号分隔的字符串)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String relatedCategoryIds;

    /**
     * 是否被逻辑删除
     */
    @NotNull(message = "是否被逻辑删除不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer isDeleted;


}
