package com.sohu.admin.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 三方平台商品分类业务对象
 *
 * <AUTHOR>
 * @date 2025-07-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ThirdPartyCategoryBo extends SohuEntity {

    /**
     * 三方分类ID
     */
    @NotNull(message = "三方分类ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 三方父分类ID
     */
    @NotNull(message = "三方父分类ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long parentId;

    /**
     * 三方分类名称
     */
    @NotBlank(message = "三方分类名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 层级 (1, 2, 3)
     */
    @NotNull(message = "层级 (1, 2, 3)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long level;

    /**
     * 分类全路径
     */
    @NotBlank(message = "分类全路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fullPath;

    /**
     * 是否叶子节点 (level=3的节点)
     */
    @NotNull(message = "是否叶子节点 (level=3的节点)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer isLeaf;
}
