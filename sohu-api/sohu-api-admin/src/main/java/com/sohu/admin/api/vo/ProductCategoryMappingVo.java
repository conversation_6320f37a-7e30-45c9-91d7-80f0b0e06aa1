package com.sohu.admin.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;



/**
 * 商品分类映射视图对象
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@ExcelIgnoreUnannotated
public class ProductCategoryMappingVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 渠道标识 (如: YOUXUAN, A, B)
     */
    @ExcelProperty(value = "渠道标识 (如: YOUXUAN, A, B)")
    private String channel;

    /**
     * 我方商户Id
     */
    @ExcelProperty(value = "我方商户Id")
    private String merId;

    /**
     * 我方二级分类ID (sohu_product_category.id)
     */
    @ExcelProperty(value = "我方二级分类ID (sohu_product_category.id)")
    private Long ourCategoryId;

    /**
     * 三方三级分类ID (third_party_category.id)
     */
    @ExcelProperty(value = "三方三级分类ID (third_party_category.id)")
    private Long thirdPartyCategoryId;

    /**
     * 我方分类路径，如: 服装/男装T恤
     */
    @ExcelProperty(value = "我方分类路径，如: 服装/男装T恤")
    private String ourCategoryPath;

    /**
     * 三方分类路径，如: 服饰穿搭/男装/T恤
     */
    @ExcelProperty(value = "三方分类路径，如: 服饰穿搭/男装/T恤")
    private String thirdPartyCategoryPath;

    /**
     * 最后操作人
     */
    @ExcelProperty(value = "最后操作人")
    private String operator;

}
