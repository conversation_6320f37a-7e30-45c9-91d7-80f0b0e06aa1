package com.sohu.shoporder.api;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.shoporder.api.bo.*;
import com.sohu.shoporder.api.domain.*;
import com.sohu.shoporder.api.model.*;
import com.sohu.shoporder.api.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 商品订单服务
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
public interface RemoteShopOrderService {

    /**
     * app用户订单列表
     */
    TableDataInfo<SohuShopOrderModel> getOrderListByType(SohuOrderReqBo bo, PageQuery pageQuery);

    /**
     * app用户待支付订单列表
     *
     * @param pageQuery
     */
    TableDataInfo<SohuMasterOrderAwaitPayModel> getWaitOrderListByType(PageQuery pageQuery, SohuOrderReqBo bo);

    /**
     * 根据masterOrderNo查询子订单
     *
     * @param masterOrderNo
     * @return List<SohuShopOrderModel>
     */
    List<SohuShopOrderModel> getListByMasterNo(String masterOrderNo);

    /**
     * 批量修改子订单
     *
     * @param storeOrderList
     * @return Boolean
     */
    Boolean updateBatchById(List<SohuShopOrderModel> storeOrderList);

    /**
     * 创建订单
     *
     * @param createOrderReqBo
     */
    Map<String, Object> createOrder(SohuCreateOrderReqBo createOrderReqBo);

    /**
     * 预下单
     *
     * @param preOrderReqBo
     */
    Map<String, Object> preOrder(SohuPreOrderReqBo preOrderReqBo);

    /**
     * 加载预下单
     *
     * @param preOrderNo
     * @return SohuPreOrderModel
     */
    SohuPreOrderModel loadPreOrder(String preOrderNo);

    /**
     * 取消订单
     *
     * @param orderNo
     * @return
     */
    Boolean cancelOrder(String orderNo);

    /**
     * 根据masterOrderNo取消订单
     *
     * @param masterOrderNo
     * @param isUser
     * @return
     */
    Boolean cancelByMasterNo(String masterOrderNo, boolean isUser);

    /**
     * 申请退款
     *
     * @param orderNo
     */
    SohuApplyRefundOrderInfoModel refundApplyOrder(String orderNo);

    /**
     * 根据商户订单号查询订单
     *
     * @param shopOrderNo
     */
    SohuShopOrderModel getByOrderNo(String shopOrderNo);

    /**
     * 根据对象修改
     *
     * @param storeOrder
     */
    Boolean updateById(SohuShopOrderModel storeOrder);

    /**
     * 订单退款申请
     *
     * @param refundApplyReqBo
     */
    Boolean refundApply(SohuOrderRefundApplyReqBo refundApplyReqBo);

    /**
     * 确认收货
     *
     * @param orderNo
     */
    Boolean takeOrder(String orderNo);

    /**
     * app主订单详情
     *
     * @param orderNo
     */
    SohuMasterOrderInfoModel getMasterOrderInfo(String orderNo);

    /**
     * app商户订单详情
     *
     * @param orderNo
     */
    SohuStoreOrderInfoResModel getShopOrderInfo(String orderNo);

    /**
     * 退款订单详情
     *
     * @param orderNo
     */
    SohuRefundOrderInfoModel refundOrderInfo(String orderNo);

    /**
     * app售后列表
     *
     * @param pageQuery
     */
    TableDataInfo<SohuRefundOrderModel> getRefundOrderList(PageQuery pageQuery, SohuOrderReqBo bo);

    /**
     * 删除订单-用户app
     *
     * @param orderNo
     */
    Boolean deleteOrder(String orderNo);

    /**
     * 查询物流信息
     *
     * @param param
     */
    KuaidiModel queryTrack(SohuQueryTrackBo param);

    /**
     * 查询子单详情
     *
     * @param orderNos
     * @return
     */
    Map<String, SohuShopOrderInfoModel> queryMap(List<String> orderNos);

    /**
     * 查询子单列表
     *
     * @param orderNos
     * @return
     */
    List<SohuShopOrderInfoModel> list(List<String> orderNos);

    /**
     * 通过商品id查询子订单号
     *
     * @param productIds
     * @return
     */
    List<SohuShopOrderInfoModel> getOrdersByProductId(List<Long> productIds);

    /**
     * 通过子订单id查询主订单
     *
     * @param orderNoList
     * @return
     */
    List<SohuShopOrderModel> getOrderListByOrderNo(List<String> orderNoList);
//    /**
//     *
//     * MCN达人带货下单榜
//     *
//     * @param mcnId
//     * @param articleUserId 有mcn机构的创作者id
//     * @param pageQuery
//     * @return
//     */
//    @Deprecated
//    TableDataInfo<SohuShopOrderStatModel> queryShopOrderTop(Long mcnId, Long articleUserId, PageQuery pageQuery);

    /**
     * 查询商户订单列表-商户
     */
    TableDataInfo<SohuShopOrderListVo> queryStorePageList(SohuQueryOrderBo bo, PageQuery pageQuery);

    /**
     * 根据订单号查询订单详情
     *
     * @param orderNo
     * @return SohuShopOrderVo
     */
    SohuShopOrderDetailVo queryByOrderNo(String orderNo);

    /**
     * 商户备注订单
     */
    Boolean remark(SohuRemarkOrderReqBo bo);

    /**
     * pc端订单分页
     */
    TableDataInfo<SohuShopOrderListVo> queryPcPageList(SohuQueryOrderBo bo, PageQuery pageQuery);

    /**
     * pc平台评论
     *
     * @param bo
     */
    Boolean remarkPc(SohuRemarkOrderReqBo bo);

    /**
     * 商户订单发货
     *
     * @param bo
     */
    Boolean send(SohuSendOrderReqBo bo);

    /**
     * 获取MCN交易统计
     */
    SohuShopOrderStatModel getTradeStat(Long mcnId);

    /**
     * 获取MCN售后统计
     */
    SohuShopOrderStatModel getAfterSaleStat(Long mcnId);

    /**
     * MCN达人带货下单榜
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuShopOrderStatModel> queryShopOrderTop(SohuShopOrderMcnReqBo bo, PageQuery pageQuery);


    /**
     * * 待收货状态的订单物流显示已签收后七天变成已完成
     */
    void updateOrderStateSigned();

    /**
     * mq延迟队列消费。从MQ服务com.sohu.stream.mq.consumer.DelayConsumer#delayConfirm迁移至此，只为解耦，未改变原代码
     *
     * @param shopOrder
     * @return
     */
    Boolean mqDelayConfirm(SohuShopOrderModel shopOrder);

    /**
     * 商城订单分账
     * @param shopOrder
     * @return
     */
    Boolean mqDelayConfirmV2(SohuShopOrderModel shopOrder);

    /**
     * mq延迟队列消费。从MQ服务com.sohu.stream.mq.consumer.updateBatchMerchants#delayConfirm迁移至此，只为解耦，未改变原代码
     *
     * @return
     */
    void mqUpdateBatchMerchants(SohuOperateReqBo operateBo, Boolean isRefund);

    /**
     * 更新订单评论状态
     *
     * @param orderNo     商户订单编号
     * @param orderInfoId 订单详情id
     * @param isReply     是否评价，0-未评价，1-已评价
     * @return
     */
    Boolean updateReplyStatus(String orderNo, Long orderInfoId, Integer isReply);

    /**
     * 查询用户店铺订单
     */
    TableDataInfo<SohuShopOrderVo> getStoreOrderList(String merId, PageQuery pageQuery);

    /**
     * 查询用户店铺订单数量
     *
     * @param merId
     * @param userId
     * @return
     */
    Long getUserOrderByStore(Long merId, Long userId);

    /**
     * PC查询用户订单数量信息
     *
     * @return OrderDataVo
     */
    OrderDataVo orderData();

    /**
     * 查询用户店铺订单
     */
    TableDataInfo<SohuShopOrderVo> getStoreOrderList(SohuStoreOrderBo bo, PageQuery pageQuery);

    /**
     * 根据用户地址获取运费
     */
    Map<String, Object> getFreight(SohuShopOrderFreightBo bo);

    /**
     * 获取订单列表信息(开放平台)
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuOpenShopOrderVo> openList(SohuOpenShopOrderPageQueryBo bo, PageQuery pageQuery);

    /**
     * 根据商户订单号获取订单信息(开放平台)
     * @param orderNo
     * @param openClientId
     * @return
     */
    SohuOpenShopOrderVo getByOrderNo(String orderNo, Long openClientId);

    /**
     * 根据物流单号获取物流信息
     * @param expNo
     * @return
     */
    KuaidiModel getLogisticsByExpNo(String expNo);

    /**
     * 商户订单发货(开放平台)
     *
     * @param bo
     */
    Boolean openSaveDeliverInfo(SohuOpenShopOrderDeliverBo bo);

    /**
     * 查询用户分销商单列表
     */
    TableDataInfo<SohuShopOrderVo> queryIndependentList(Long userId, Long pmSharePubId, PageQuery pageQuery);

    /**
     * 更新订单状态
     * @param orderNo  订单号
     * @param sourceStaus 原状态
     * @param targetStatus 更新后的状态
     * @return
     */
    Boolean updateOrderStatus(String orderNo, String sourceStaus, String targetStatus);

    /**
     * 确认发货
     * @param bo
     * @return
     */
    Boolean sendGoods(SohuSendOrderBo bo);

    /**
     * 商家销售统计
     * @param day
     */
    void merchantSalesExcute(String day);

    /**
     * 获取店铺在途订单数
     * @param merId
     * @param states
     * @return
     */
    Long inTransitOrder(List<Long> merId,List<String> states);

    /**
     * 订单检测
     * @param masterOrderNo
     * @return
     */
    Boolean orderCheck(String masterOrderNo);
}
