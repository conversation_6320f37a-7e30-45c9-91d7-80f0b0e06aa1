package com.sohu.busyorder.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.sohu.busyorder.api.enums.SettleTypeEnums;
import com.sohu.busyorder.api.enums.BusyTaskVisualTypeEnums;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import com.sohu.third.aliyun.airec.domain.vo.ISohuAiRecResVo;
import com.sohu.third.aliyun.airec.domain.vo.SohuAiRecResultItemVo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商单与站点关联视图对象
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Data
@ExcelIgnoreUnannotated
public class SohuBusyTaskSiteVo implements Serializable, ISohuAiRecResVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 商单id
     */
    @ExcelProperty(value = "商单id")
    private Long busyTaskId;

    /**
     * 站点id
     */
    @ExcelProperty(value = "站点id")
    private Long siteId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 审核人昵称
     */
    private String auditName;

    /**
     * 审核人名称
     */
    private String userName;

    /**
     * 创建者昵称
     */
    private String nickName;

    /**
     * 用户备注
     */
    private String userRemark;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 子任务状态：WaitApprove-待审核,WaitReceive-待接单，Execute-执行中，WaitApproveSettle-结算申请中 ，WaitSettle-待结算(待提现)，OverSettle（已完结），Exceed （已过期），Cancel（已取消）
     */
    @ExcelProperty(value = "子任务状态：WaitReceive-待接单，Execute-执行中，WaitApproveSettle-结算申请中 ，WaitSettle-待结算(待提现)，OverSettle（已完结），Exceed （已过期），Cancel（已取消）")
    private String state;

    /**
     * 子任务状态：OnShelf：上架，OffShelf：下架，CompelOff：强制下架
     */
    private String shelfState;

    /**
     * 拒绝理由
     */
    @ExcelProperty(value = "拒绝理由")
    private String refuseMsg;

    /**
     * 审核人ID;审核人
     */
    @ExcelProperty(value = "审核人ID;审核人")
    private Long auditUser;

    /**
     * 审核时间
     */
    @ExcelProperty(value = "审核时间")
    private Date auditTime;

    /**
     * 上下架人ID
     */
    @ExcelProperty(value = "上下架人ID")
    private Long shelfUser;

    /**
     * 上下架时间;上下架时间
     */
    @ExcelProperty(value = "上下架时间;上下架时间")
    private Date shelfTime;

    /**
     * 价值金额
     */
    @ExcelProperty(value = "价值金额")
    private BigDecimal fullAmount;

    /**
     * 价值币种
     */
    @ExcelProperty(value = "价值币种")
    private Long fullCurrency;

    /**
     * 子任务编号
     */
    @ExcelProperty(value = "子任务编号")
    private String taskNumber;

    /**
     * 作者id
     */
    @ExcelProperty(value = "作者id")
    private Long userId;

    /**
     * 接单人id
     */
    private Long receiveUserId;

    /**
     * 接单人姓名
     */
    private String receiveUserName;

    /**
     * 父级ID
     */
    @ExcelProperty(value = "父级ID")
    private Long pid;

    /**
     * 商单类型ID
     */
    @ExcelProperty(value = "商单类型")
    private Long type;

    /**
     * 行业类型ID
     */
    @ExcelProperty(value = "行业类型")
    private Long industryType;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 内容描述
     */
    @ExcelProperty(value = "内容描述")
    private String content;

    /**
     * 附件（url列表，逗号隔开）
     */
    @ExcelProperty(value = "附件", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "u=rl列表，逗号隔开")
    private String annex;

    /**
     * 是否有接单人限制;false=默认全部可以接单 true=部分人可以接单
     */
    @ExcelProperty(value = "是否有接单人限制")
    private Boolean receiveLimit;

    /**
     * 指派接单人用户id
     */
    private Long receiveOnlyUserId;

    /**
     * 交付天数，从被接单时间开始计算
     */
    @ExcelProperty(value = "交付天数，从被接单时间开始计算")
    private Integer deliveryDay;

    /**
     * 交付说明
     */
    @ExcelProperty(value = "交付说明")
    private String deliveryMsg;

    /**
     * {@link SettleTypeEnums}
     */
    @ExcelProperty(value = "结算方式;1=先执行后付款 2=依据进度付款")
    private Integer settleType;

    /**
     * 结算方式名称
     */
    @ExcelProperty(value = "结算方式名称")
    private String settleTypeName;

    /**
     * 是否是阶段性交付,true=是，false=否
     */
    @ExcelProperty(value = "是否是阶段性交付")
    private Boolean deliveryStep;

    /**
     * 佣金类型;none:无设置，percentage:百分比，price:一口价
     */
    @ExcelProperty(value = "佣金类型;none:无设置，percentage:百分比，price:一口价")
    private String kickbackType;

    /**
     * 佣金类型值;佣金类型值
     */
    @ExcelProperty(value = "佣金类型值;佣金类型值")
    private BigDecimal kickbackValue;

    /**
     * 主任务编号
     */
    private String masterTaskNumber;

    /**
     * 是否是草稿,true=是，false=否
     */
    @ExcelProperty(value = "是否是草稿")
    private Boolean isDraft;

    /**
     * 拆单状态.true=已拆单 false=未拆单
     */
    @ExcelProperty(value = "拆单状态.true=已拆单 false=未拆单")
    private Boolean splitState;

    /**
     * 创建者头像
     */
    private String userAvatar;

    /**
     * 用户简介
     */
    private String remark;

    /**
     * 账号类型（personal=个人  business=企业认证）
     */
    private String accountType;

    /**
     * 保证金
     */
    private BigDecimal promiseAmount;

    /**
     * 是否有人申请接单：0没有，1有
     */
    private Boolean isReceive;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 商单任务类型名称
     */
    @ExcelProperty(value = "商单任务类型名称")
    private String typeName;

    /**
     * 行业名称
     */
    @ExcelProperty(value = "行业名称")
    private String industryName;

    /**
     * 关联任务编号
     */
    private String relateTaskNumber;

    /**
     * 阶段交付列表
     */
    private List<SohuBusyTaskDeliveryVo> deliveryList;

    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 商单接单通过时间
     */
    private String receiveTime;

    /**
     * 商单接单完成时间
     */
    private String finishTime;

    /**
     * 接单人数量
     */
    private int receiveCount = 0;

    /**
     * 终止任务数量
     */
    private int abortCount = 0;

    /**
     * 更改接单人数量
     */
    private int replaceCount = 0;

    /**
     * 任务模版
     */
    private SohuBusyTaskTemplateNumberVo templateVo;

    /**
     * 接单信息
     */
    private SohuBusyTaskReceiveVo taskReceiveVo;

    /**
     * 是否支付佣金
     */
    private Boolean isIndependent;

    /**
     * 关联子任务信息
     */
    private SohuBusyTaskSiteVo relateBusyTaskSiteVo;

    /**
     * 分销人金额
     */
    private BigDecimal distributionAmount;

    /**
     * 智能推荐返回结果详情(包含TraceId，TraceInfo)
     */
    private SohuAiRecResultItemVo aiResultItem;

    /**
     * 浏览量
     */
    private Long viewCount = 0L;

    /**
     * 是否正在售后状态.1=是 0=否
     */
    private Boolean isAfterSales;

    /**
     * 是否收藏当前数据
     */
    private Boolean collectObj = Boolean.FALSE;

    /**
     * MCN机构ID(用户ID)
     */
    private Long mcnId;

    /**
     * 售后状态 ToConfirmed=待接单方确认， ReceiveRefuse=接单方拒绝 , Intervention=平台介入处理中， Terminate=已终止, Ended = 已完结 ,Closed = 售后已关闭,Canceled = 售后已取消,ToSettlement = 待结算
     */
    private String afterSalesState;

    /**
     * 售后类型 Abort=终止任务，Replace=更换接单人
     */
    private String afterSalesType;

    /**
     * 售后原因
     */
    private String afterSalesReason;

    /**
     * 售后单ID
     */
    private Long afterSalesId;

    /**
     * 售后报酬
     */
    private BigDecimal remuneration;

    /**
     * 支付状态;支付状态：WaitPay-待支付，Paid-已支付，Fail-支付失败，TimeOut-超时
     */
    private String payState;
    /**
     * 支付金额
     */
    private BigDecimal payAmount;
    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 平台介入状态  1=已介入 ，0=未介入
     */
    private Integer platformInterventionState;

    /**
     * 可见范围：ALL-所有人；GROUP-分组；INDIVIDUAL-达人
     * {@link BusyTaskVisualTypeEnums}
     */
    @ExcelProperty(value = "可见范围：ALL-所有人；GROUP-分组；INDIVIDUAL-达人")
    private String visualType;

    /**
     * MCN任务库ID
     */
    private Long windowId;

    /**
     * 关联任务id
     */
    private Long relationId;

    /**
     * 关联任务标题
     */
    private String relationTitle;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 地址
     */
    private String address;

    /**
     * 位置、经纬度
     */
    private String location;

    /**
     * 关联售后历史列表
     */
    private List<SohuBusyTaskAfterSalesHistroyVo> sohuBusyTaskAfterSalesHistroyVoList;

    /**
     * 关联售后记录
     */
    private SohuBusyTaskAfterSalesVo sohuBusyTaskAfterSalesVo;

    /**
     * 是否实名认证: FALSE=未认证，TRUE=已认证
     */
    private String loginUserAccountType;

    /**
     * 是否绑定银行卡: FALSE=未绑定，TRUE=已绑定
     */
    private Boolean isAccountBank = Boolean.FALSE;

    /**
     * 售后申诉次数
     */
    private Long explainCount;

    /**
     * 素材id
     */
    private Long materialId;

    /**
     * 分销人id
     */
    private Long sharePerson;

    /**
     * 是否群关联
     */
    @TableField(exist = false)
    private Boolean groupRelate;

    /**
     * 子任务编号
     */
    private String childTaskNumber;
    /**
     * 时间单位 1.天 2.小时 3.分钟
     */
    private Integer timeUnit;
    /**
     * 交付类型 1.按时间交付 2.按次数交付
     */
    private Integer deliveryType;
    /**
     * 交付标准 大于0的正整数
     */
    private Integer deliveryStandard;
    /**
     * 交付单位 按时间交付 1.秒 2.分钟 3.小时  按次数交付 1.次
     */
    private Integer deliveryTimeUnit;
    /**
     * 过期时间
     */
    private Date expiryTime;
    /**
     * 已接单数量
     */
    private Integer receivedNum;
    /**
     * 单个金额
     */
    private BigDecimal singleAmount;
    /**
     * 总接单数量
     */
    private Integer receiveNum;
    /**
     * 流量商单接单附加对象
     */
    private SohuFlowTaskReceiveVO flowTaskReceive;
    /**
     * 返回编码类型
     */
    private String constMark;

    /**
     * 待达标人数
     */
    private Long waitPassPersonNum;

    /**
     * 排序
     */
    private Integer sortIndex;
    /**
     * 是否需要审核接单 0.否  1.是
     */
    private Boolean isApproveReceive;
    /**
     * 申请理由
     */
    private String applyMsg;
    /**
     * 申请附件
     */
    private String applyAnnex;

    /**
     * 发单时的ip地址
     */
    private String ipAddress;

    /**
     * IP所在地
     */
    private String ipInfo;

    /**
     * 服务费
     */
    private BigDecimal platformFee;

    /**
     * 剩余支付时间
     */
    private Long remainPayTime;

    @Override
    public String getAiItemId() {
        return this.id == null ? null : String.valueOf(this.id);
    }
}
