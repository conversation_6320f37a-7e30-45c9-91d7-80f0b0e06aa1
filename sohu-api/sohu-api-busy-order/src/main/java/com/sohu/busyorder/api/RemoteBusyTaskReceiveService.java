package com.sohu.busyorder.api;

import com.sohu.busyorder.api.domain.SohuBusyTaskReceiveReqBo;
import com.sohu.busyorder.api.model.SohuBusyTaskReceiveModel;
import com.sohu.busyorder.api.model.SohuBusyTaskSiteModel;
import com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo;
import com.sohu.busyorder.api.vo.TaskGroupVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface RemoteBusyTaskReceiveService {

    /**
     * 查询商单接单
     */
    SohuBusyTaskReceiveModel queryById(Long id);

    /**
     * 查询任务方申请接单列表
     */
    TableDataInfo<SohuBusyTaskReceiveModel> queryPageList(SohuBusyTaskReceiveReqBo bo, PageQuery pageQuery);

    /**
     * 查询商单接单列表
     */
    List<SohuBusyTaskReceiveModel> queryList(SohuBusyTaskReceiveReqBo bo);

    /**
     * 修改商单接单
     */
    Boolean insertByBo(SohuBusyTaskReceiveReqBo bo);

    /**
     * 修改商单接单
     */
    Boolean updateByBo(SohuBusyTaskReceiveReqBo bo);

    /**
     * 校验并批量删除商单接单信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询同意的商单接单
     *
     * @param busyTaskId 商单ID
     * @return
     */
//    SohuBusyTaskReceive queryPass(Long busyTaskId);

    /**
     * 接单审核
     *
     * @param bo
     * @return
     */
    Boolean audit(SohuBusyTaskReceiveReqBo bo);

    /**
     * 接单并且支付保证金
     *
     * @param bo
     */
    String pay(SohuBusyTaskReceiveReqBo bo);

    /**
     * 查询任务方申请接单列表-接单方
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuBusyTaskReceiveModel> selectTaskReceiveUserPage(SohuBusyTaskReceiveReqBo bo, PageQuery pageQuery);

    /**
     * 查询已接单的
     *
     * @param taskNumber
     */
    List<SohuBusyTaskReceiveVo> queryByReceive(String taskNumber);

    /**
     * 批量查询接单商单列表
     */
    List<SohuBusyTaskReceiveVo> queryReceiveListByTaskNumber(List<String> taskNumbers);

    /**
     * 接单方申请接单，被淘汰或者已选定其他接单人极光推送
     */
    void pushApplyTaskOutNotice(SohuBusyTaskSiteModel bo);

    /**
     * 更改过期时间
     *
     * @param id
     * @param expireTime
     */
    void updateExpireTime(Long id, Date expireTime);

    /**
     * 基于状态查询相关接单记录
     *
     * @param state
     * @return
     */
    List<SohuBusyTaskReceiveVo> queryListByState(String state);

    /**
     * 通过Id查询商单接单详情
     *
     * @param id
     * @return
     */
    SohuBusyTaskReceiveVo selectById(Long id);

    /**
     * 获取接单详情
     *
     * @param taskNumber 子订单编号
     * @return SohuBusyTaskReceiveVo
     */
    SohuBusyTaskReceiveVo getReceiveDetailByTaskNumber(String taskNumber);

    /**
     * 基于主单编码及状态查询相关接单信息
     *
     * @param masterTaskNumber
     * @param stateList
     * @return
     */
    List<SohuBusyTaskReceiveVo> queryListByMasterTaskNumberAndStateList(String masterTaskNumber, List<String> stateList);

    /**
     * 获取在途任务
     *
     * @param userId
     * @param status
     * @param freezeTime
     * @return
     */
    Long inTransitTask(Long userId, List<String> status, Date freezeTime);

    /**
     * 基于主单编码及接单人查询相关接单信息
     *
     * @param taskNumber
     * @param receiveUserId
     * @return
     */
    SohuBusyTaskReceiveVo queryByMasterTaskNumberrAndReceiveUserId(String taskNumber, Long receiveUserId);


    /**
     * 获取接单分组列表
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<TaskGroupVo> getReceiveTaskGroupList(Date startTime, Date endTime);
}
