package com.sohu.busyorder.api.model;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import com.sohu.third.aliyun.airec.domain.vo.ISohuAiRecResVo;
import com.sohu.third.aliyun.airec.domain.vo.SohuAiRecResultItemVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商单与站点关联视图对象
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Data
@ExcelIgnoreUnannotated
public class SohuBusyTaskSiteModel extends SohuEntity implements ISohuAiRecResVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 商单id
     */
    @ExcelProperty(value = "商单id")
    private Long busyTaskId;

    /**
     * 站点id
     */
    @ExcelProperty(value = "站点id")
    private Long siteId;

    /**
     * 主任务编号
     */
    private String masterTaskNumber;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 审核人昵称
     */
    private String auditName;

    /**
     * 审核人名称
     */
    private String userName;

    /**
     * 创建者昵称
     */
    private String nickName;

    /**
     * 子任务状态：WaitApprove-待审核，Pass-通过，Refuse-驳回，Finish-完结，Execute-执行中
     */
    @ExcelProperty(value = "子任务状态：WaitApprove-待审核，Pass-通过，Refuse-驳回，Finish-完结，Execute-执行中")
    private String state;

    /**
     * 子任务状态：OnShelf：上架，OffShelf：下架，CompelOff：强制下架
     */
    private String shelfState;

    /**
     * 拒绝理由
     */
    @ExcelProperty(value = "拒绝理由")
    private String refuseMsg;

    /**
     * 审核人ID;审核人
     */
    @ExcelProperty(value = "审核人ID;审核人")
    private Long auditUser;

    /**
     * 审核时间
     */
    @ExcelProperty(value = "审核时间")
    private Date auditTime;

    /**
     * 上下架人ID
     */
    @ExcelProperty(value = "上下架人ID")
    private Long shelfUser;

    /**
     * 上下架时间;上下架时间
     */
    @ExcelProperty(value = "上下架时间;上下架时间")
    private Date shelfTime;

    /**
     * 价值金额
     */
    @ExcelProperty(value = "价值金额")
    private BigDecimal fullAmount;
    /**
     * 单个金额
     */
    private BigDecimal singleAmount;

    /**
     * 价值币种
     */
    @ExcelProperty(value = "价值币种")
    private Long fullCurrency;

    /**
     * 子任务编号
     */
    @ExcelProperty(value = "子任务编号")
    private String taskNumber;

    /**
     * 作者id
     */
    @ExcelProperty(value = "作者id")
    private Long userId;

    /**
     * 接单人id
     */
    private Long receiveUserId;

    /**
     * 接单人姓名
     */
    private String receiveUserName;

    /**
     * 父级ID
     */
    @ExcelProperty(value = "父级ID")
    private Long pid;

    /**
     * 商单类型ID
     */
    @ExcelProperty(value = "商单类型")
    private Long type;

    /**
     * 行业类型ID
     */
    @ExcelProperty(value = "行业类型")
    private Long industryType;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 内容描述
     */
    @ExcelProperty(value = "内容描述")
    private String content;

    /**
     * 附件（url列表，逗号隔开）
     */
    @ExcelProperty(value = "附件", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "u=rl列表，逗号隔开")
    private String annex;

    /**
     * 是否有接单人限制;false=默认全部可以接单 true=部分人可以接单
     */
    @ExcelProperty(value = "是否有接单人限制")
    private Boolean receiveLimit;

    /**
     * 交付天数，从被接单时间开始计算
     */
    @ExcelProperty(value = "交付天数，从被接单时间开始计算")
    private Integer deliveryDay;

    /**
     * 交付说明
     */
    @ExcelProperty(value = "交付说明")
    private String deliveryMsg;

    /**
     * 结算方式;1=先执行后付款 2=依据进度付款
     */
    @ExcelProperty(value = "结算方式;1=先执行后付款 2=依据进度付款")
    private Integer settleType;

    /**
     * 是否是阶段性交付,true=是，false=否
     */
    @ExcelProperty(value = "是否是阶段性交付")
    private Boolean deliveryStep;

    /**
     * 佣金类型;none:无设置，percentage:百分比，price:一口价
     */
    @ExcelProperty(value = "佣金类型;none:无设置，percentage:百分比，price:一口价")
    private String kickbackType;

    /**
     * 佣金类型值;佣金类型值
     */
    @ExcelProperty(value = "佣金类型值;佣金类型值")
    private BigDecimal kickbackValue;

    /**
     * 是否是草稿,true=是，false=否
     */
    @ExcelProperty(value = "是否是草稿")
    private Boolean isDraft;

    /**
     * 拆单状态.true=已拆单 false=未拆单
     */
    @ExcelProperty(value = "拆单状态.true=已拆单 false=未拆单")
    private Boolean splitState;

    /**
     * 创建者头像
     */
    private String userAvatar;

    /**
     * 用户简介
     */
    private String remark;

    /**
     * 账号类型（personal=个人  business=企业认证）
     */
    private String accountType;

    /**
     * 保证金
     */
    private BigDecimal promiseAmount;

    /**
     * 商单任务类型名称
     */
    @ExcelProperty(value = "商单任务类型名称")
    private String typeName;

    /**
     * 行业名称
     */
    @ExcelProperty(value = "行业名称")
    private String industryName;

    /**
     * 关联任务编号
     */
    private String relateTaskNumber;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 商单接单通过时间
     */
    private String receiveTime;

    /**
     * 商单接单完成时间
     */
    private String finishTime;

    /**
     * 接单人数量
     */
    private int receiveCount = 0;

    /**
     * 是否支付佣金
     */
    private Boolean isIndependent;

    /**
     * 分销人金额
     */
    private BigDecimal distributionAmount;

    /**
     * MCN机构ID(用户ID)
     */
    private Long mcnId;

    /**
     * 可见范围：ALL-所有人；GROUP-分组；INDIVIDUAL-达人
     */
    @ExcelProperty(value = "可见范围：ALL-所有人；GROUP-分组；INDIVIDUAL-达人")
    private String visualType;

    /**
     * MCN任务库ID
     */
    private Long windowId;

    /**
     * 是否群关联
     */
    private Boolean groupRelate;

    /**
     * 智能推荐返回结果详情(包含TraceId，TraceInfo)
     */
    private SohuAiRecResultItemVo aiResultItem;

    /**
     * 子任务编号
     */
    private String childTaskNumber;

    /**
     * 服务费
     */
    private BigDecimal platformFee;

    /**
     * 接单状态
     */
    private String receiveState;

    /**
     * 剩余支付时间
     */
    private Long remainPayTime;

    /**
     * 是否需要审核接单 0.否  1.是
     */
    private Boolean isApproveReceive;

    /**
     * 分类标识
     */
    private String constMark;

    @Override
    public String getAiItemId() {
        return this.id == null ? null : String.valueOf(this.id);
    }

}
