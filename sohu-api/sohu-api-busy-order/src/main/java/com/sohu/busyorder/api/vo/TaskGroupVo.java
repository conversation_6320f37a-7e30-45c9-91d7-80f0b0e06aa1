package com.sohu.busyorder.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/25 11:10
 */
@Data
public class TaskGroupVo implements Serializable {

    @Schema(name = "userId", description = "用户ID", example = "1")
    private Long userId;
    @Schema(name = "taskCount", description = "任务数量", example = "1")
    private Long taskCount;
}
