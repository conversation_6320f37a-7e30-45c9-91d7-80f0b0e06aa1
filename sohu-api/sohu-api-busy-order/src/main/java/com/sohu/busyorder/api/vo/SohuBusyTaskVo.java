package com.sohu.busyorder.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 任务主体视图对象
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Data
@ExcelIgnoreUnannotated
public class SohuBusyTaskVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 作者id
     */
    @ExcelProperty(value = "作者id")
    private Long userId;

    /**
     * 商单类型ID
     */
    @ExcelProperty(value = "商单类型")
    private Long type;

    /**
     * 行业类型ID
     */
    @ExcelProperty(value = "行业类型")
    private Long industryType;

    /**
     * 行业类型父ID
     */
    private Long pid;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 内容描述
     */
    @ExcelProperty(value = "内容描述")
    private String content;

    /**
     * 附件（url列表，逗号隔开）
     */
    @ExcelProperty(value = "附件", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "u=rl列表，逗号隔开")
    private String annex;

    /**
     * 是否有接单人限制;false=默认全部可以接单 true=部分人可以接单
     */
    @ExcelProperty(value = "是否有接单人限制")
    private Boolean receiveLimit;

    /**
     * 交付天数，从被接单时间开始计算
     */
    @ExcelProperty(value = "交付天数，从被接单时间开始计算")
    private Integer deliveryDay;

    /**
     * 交付说明
     */
    @ExcelProperty(value = "交付说明")
    private String deliveryMsg;

    /**
     * 审核人ID;审核人
     */
    private Long auditUser;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 结算方式;1=先执行后付款 2=依据进度付款
     */
    @ExcelProperty(value = "结算方式;1=先执行后付款 2=依据进度付款")
    private Integer settleType;

    /**
     * 是否是阶段性交付,true=是，false=否
     */
    @ExcelProperty(value = "是否是阶段性交付")
    private Boolean deliveryStep;

    /**
     * 指派接单人用户id
     */
    private Long receiveOnlyUserId;

    /**
     * 价值金额
     */
    @ExcelProperty(value = "价值金额")
    private BigDecimal fullAmount;

    /**
     * 价值币种
     */
    @ExcelProperty(value = "价值币种")
    private Long fullCurrency;

    /**
     * 佣金类型;none:无设置，percentage:百分比，price:一口价
     */
    @ExcelProperty(value = "佣金类型;none:无设置，percentage:百分比，price:一口价")
    private String kickbackType;

    /**
     * 佣金类型值;佣金类型值
     */
    @ExcelProperty(value = "佣金类型值;佣金类型值")
    private BigDecimal kickbackValue;

    /**
     * 是否需要拆单，ture=是，false=否
     */
    @ExcelProperty(value = "是否需要拆单")
    private Boolean needSplit;

    /**
     * 主任务编号
     */
    @ExcelProperty(value = "主任务编号")
    private String taskNumber;


    /**
     * 发布的国家站点
     */
    @ExcelProperty(value = "发布的国家站点")
    private Long countrySiteId;

    /**
     * 是否是草稿,true=是，false=否
     */
    @ExcelProperty(value = "是否是草稿")
    private Boolean isDraft;

    /**
     * 拆单状态.true=已拆单 false=未拆单
     */
    @ExcelProperty(value = "拆单状态.true=已拆单 false=未拆单")
    private Boolean splitState;

    /**
     * 主任务状态：WaitPay-待付款，WaitReceive-待接单，Execute-执行中，OverSettle-已完结，CompelOff-强制下架
     */
    @ExcelProperty(value = "主任务状态：WaitPay-待付款，WaitReceive-待接单，Execute-执行中，OverSettle-已完结，CompelOff-强制下架")
    private String state;

    /**
     * 子任务状态：OnShelf：上架，OffShelf：下架，CompelOff：强制下架
     */
    private String shelfState;

    /**
     * 创建者昵称
     */
    private String nickName;

    /**
     * 创建者名称
     */
    private String userName;

    /**
     * 拒绝理由
     */
    private String refuseMsg;

    /**
     * 创建者头像
     */
    private String userAvatar;

    /**
     * 接单人头像
     */
    private String receiveAvatar;

    /**
     * 接单人昵称
     */
    private String receiveNickName;

    /**
     * 用户备注
     */
    private String userRemark;

    /**
     * 用户简介
     */
    private String remark;

    /**
     * 账号类型（personal=个人  business=企业认证）
     */
    private String accountType;

    /**
     * 保证金
     */
    private BigDecimal promiseAmount;

    /**
     * 商单任务类型名称
     */
    @ExcelProperty(value = "商单任务类型名称")
    private String typeName;

    /**
     * 审核人昵称
     */
    private String auditName;

    /**
     * 行业名称
     */
    @ExcelProperty(value = "行业名称")
    private String industryName;

    /**
     * 关联任务编号
     */
    private String relateTaskNumber;

    /**
     * 站点关联列表
     */
    private List<SohuBusyTaskSiteVo> siteList;

    /**
     * 站点ids
     */
    private String siteIds;

    /**
     * 站点ids
     */
    private List<Long> siteVoIds;

    /**
     * 站点名称
     */
    private List<String> siteVoNames;

    /**
     * 阶段交付列表
     */
    private List<SohuBusyTaskDeliveryVo> deliveryList;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 商单接单通过时间
     */
    private String receiveTime;

    /**
     * 商单接单完成时间
     */
    private String finishTime;

    /**
     * 接单人数量
     */
    private int receiveCount = 0;

    /**
     * 模板列表
     */
    private List<SohuBusyTaskTemplateVo> templateVoList;

    /**
     * 任务模版
     */
    private SohuBusyTaskTemplateNumberVo templateVo;

    /**
     * 分销人金额
     */
    private BigDecimal distributionAmount;

    /**
     * 阅读数
     */
    private Long viewCount;

    /**
     * 是否收藏当前数据
     */
    private Boolean collectObj = Boolean.FALSE;

    /**
     * 关联任务id
     */
    private Long relationId;

    /**
     * 关联任务标题
     */
    private String relationTitle;

    /**
     * 关联售后历史列表
     */
    private List<SohuBusyTaskAfterSalesHistroyVo> sohuBusyTaskAfterSalesHistroyVoList;

    /**
     * 关联售后记录
     */
    private SohuBusyTaskAfterSalesVo sohuBusyTaskAfterSalesVo;

    /**
     * 是否群关联
     */
    private Boolean groupRelate;

    /**
     * 售后状态 ToConfirmed=待接单方确认， ReceiveRefuse=接单方拒绝 , Intervention=平台介入处理中， Terminate=已终止, Ended = 已完结 ,Closed = 售后已关闭,Canceled = 售后已取消,ToSettlement = 待结算
     */
    private String afterSalesState;

    /**
     * 售后类型 Abort=终止任务，Replace=更换接单人
     */
    private String afterSalesType;

    /**
     * 售后原因
     */
    private String afterSalesReason;

    /**
     * 售后单ID
     */
    private Long afterSalesId;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 地址
     */
    private String address;

    /**
     * 位置、经纬度
     */
    private String location;
    /**
     * 时间单位 1.天 2.小时 3.分钟
     */
    private Integer timeUnit;
    /**
     * 交付类型 1.按时间交付 2.按次数交付
     */
    private Integer deliveryType;
    /**
     * 交付标准 大于0的正整数
     */
    private Integer deliveryStandard;
    /**
     * 交付单位 按时间交付 1.秒 2.分钟 3.小时  按次数交付 1.次
     */
    private Integer deliveryTimeUnit;
    /**
     * 小说名称/视频名称
     */
    private String videoName;
    /**
     * 单个金额
     */
    private BigDecimal singleAmount;
    /**
     * 接单人数
     */
    private Integer receiveNum;
    /**
     * 已经接单人数
     */
    private Integer actuallyReceiveNum;
    /**
     * 是否需要审核接单  0.否  1.是
     */
    private Boolean isApproveReceive;
    /**
     * 素材地址
     */
    private List<String> sourceMaterialList;
    /**
     * 标签数组
     */
    private List<Label> labelList;
    /**
     * 返回编码类型
     */
    private String constMark;

    /**
     * 剩余招募人数
     */
    private Integer waitPassPersonNum;

    /**
     * 接单子列表信息
     */
    private List<SohuBusyTaskChildVo> childVoList;

    /**
     * 是否存在审核记录
     */
    private Boolean isAuditRecord = false;

    /**
     * 待审核数量
     */
    private Integer waitApproveNum;

    /**
     * 主任务已达标人数
     */
    private Long passPersonNum;

    /**
     * 主任务已完成金额
     */
    private BigDecimal completedAmount;

    /**
     * 主群id
     */
    private Long groupId;

    @Schema(name = "receiveUserId",description = "接单人id", example = "123")
    private Long receiveUserId;

    @Schema(name = "receiveId",description = "接单id", example = "123")
    private Long receiveId;
    /**
     * 是否开启保证金 0 关闭 1 开启
     */
    @Schema(name = "isReceiveDeposit",description = "是否开启保证金 False 关闭 true 开启", example = "123")
    private Boolean isReceiveDeposit;

    /**
     * 服务费
     */
    private BigDecimal platformFee;

    /**
     * 实付金额
     */
    private BigDecimal payAmount;

    /**
     * 剩余支付时间
     */
    @Schema(name = "remainPayTime",description = "剩余支付时间(单位秒)", example = "123")
    private Long remainPayTime;

    /**
     * 子任务编号
     */
    @Schema(name = "childTaskNumber",description = "子任务编号", example = "CT123")
    private String childTaskNumber;

    /**
     * 移出时间
     */
    private Date removeTime;

    @Data
    public static class Label {

        /**
         * 标签id
         */
        private Long labelId;

        /**
         * 标签名称
         */
        private String labelName;

    }

    /**
     * 愿望分类id
     */
    private Long categoryType;

    /**
     * 供需类型id
     */
    private Long supplyType;

    /**
     * 愿望分类名称
     */
    @ExcelProperty(value = "愿望分类名称")
    private String categoryTypeName;


    /**
     * 供需类型名称
     */
    @ExcelProperty(value = "供需类型名称")
    private String supplyTypeName;

    /**
     * 是否已处理
     */
    private Boolean isHandle;
}
