package com.sohu.busyorder.api.bo;

import com.sohu.busyorder.api.vo.SohuBusyTaskDeliveryVo;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.third.aliyun.airec.domain.bo.ISohuAiRecReqBo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 商单与站点关联业务对象
 *
 * <AUTHOR>
 * @date 2023-12-11
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuBusyTaskSiteBo extends SohuEntity implements ISohuAiRecReqBo {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 作者id--国家站、超管查询时不传或者筛选传
     */
    private Long userId;

    /**
     * 售后类型 Abort=终止任务，Replace=更换接单人
     */
    private String afterSalesType;

//      `is_receive` 
    /**
     * 是否有人申请接单：0没有，1有
     */
    private Boolean isReceive;

    /**
     * 接单人id
     */
    private Long receiveUserId;

    /**
     * 城市站点id--城市站长查询不能为空
     */
    private Long siteId;

    /**
     * 国家站点id--国家站长查询不能为空
     */
    private Long countrySiteId;

    /**
     * 主任务编号
     */
    private String masterTaskNumber;

    /**
     * 是否支付佣金
     */
    private Boolean isIndependent;

    /**
     * 子任务状态：WaitApprove-待审核，Pass-通过，Refuse-驳回，Finish-完结，Execute-执行中
     */
    private String state;

    /**
     * 子任务状态：OnShelf：上架，OffShelf：下架，CompelOff：强制下架
     */
    private String shelfState;

    /**
     * 拒绝理由
     */
    private String refuseMsg;

    /**
     * 审核人ID;审核人
     */
    private Long auditUser;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 上下架人ID
     */
    private Long shelfUser;

    /**
     * 上下架时间;上下架时间
     */
    private Date shelfTime;

    /**
     * 价值金额
     */
    private BigDecimal fullAmount;

    /**
     * 价值币种
     */
    private Long fullCurrency;

    /**
     * 子任务编号
     */
    private String taskNumber;

    /**
     * 子任务编号
     */
    private List<String> taskNumbers;

    /**
     * 商单类型ID
     */
    private Long type;
    /**
     * 行业类型ID
     */
    private Long industryType;

    /**
     * 行业分类id集合
     */
    private List<Long> industryTypeIds;

    /**
     * 标签id集合
     */
    /**
     * 标题
     */
    private String title;
    /**
     * 内容描述
     */
    private String content;
    /**
     * 附件（url列表，逗号隔开）
     */
    private String annex;
    /**
     * 是否有接单人限制;false=默认全部可以接单 true=部分人可以接单
     */
    private Boolean receiveLimit;
    /**
     * 交付天数，从被接单时间开始计算
     */
    private Integer deliveryDay;
    /**
     * 交付说明
     */
    private String deliveryMsg;
    /**
     * 结算方式;1=先执行后付款 2=依据进度付款
     */
    private Integer settleType;

    /**
     * 模板id
     */
    private Long templateId;
    /**
     * 是否是阶段性交付,true=是，false=否
     */
    private Boolean deliveryStep;

    /**
     * 佣金类型;none:无设置，percentage:百分比，price:一口价
     */
    private String kickbackType;
    /**
     * 佣金类型值;佣金类型值
     */
    private BigDecimal kickbackValue;
    /**
     * 是否需要拆单，ture=是，false=否
     */
    private Boolean needSplit;

    /**
     * 是否是草稿,true=是，false=否
     */
    private Boolean isDraft;
    /**
     * 拆单状态.true=已拆单 false=未拆单
     */
    private Boolean splitState;

    /**
     * 指派接单人用户id
     */
    private Long receiveOnlyUserId;

    /**
     * 阶段性交付列表
     */
    private List<SohuBusyTaskDeliveryBo> deliveryList;

    /**
     * 价值金额-最小值
     */
    private BigDecimal minAmount;

    /**
     * 价值金额-最大值
     */
    private BigDecimal maxAmount;

    /**
     * 分销人金额
     */
    private BigDecimal distributionAmount;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 排序字段(createTime--最新发布,priceAsc--价格升序,priceDesc--价格降序,comprehensive--综合排序,sort-置顶降序)
     */
    private String sortBy;

    /**
     * 交付开始时间
     */
    private String deliveryStartDay;

    /**
     * 交付结束时间
     */
    private String deliveryEndDay;

    /**
     * 【圈子】当前用户关注的人发布的任务
     */
    private Boolean isFocus = false;

    /**
     * 是否设置佣金;true=是,false=否
     */
    private Boolean hasKickType;

    /**
     * 关注的用户以及好友集合
     */
    private List<Long> focusUserIdList;

    /**
     * 是否智能推荐(支持智能推荐)
     */
    private Boolean aiRec = false;

    /**
     * 用户设备id，安卓设备是（imei），IOS设备是（idfa）
     * (支持智能推荐)(已登录的用户可不填，未登录用户必填)
     */
    private String aiRecImei;

    /**
     * 需要推荐的目标用户,和imei至少一个不为空
     *
     * @return
     */
    @Schema(hidden = true)
    private String aiUserId;

    /**
     * 场景id
     * （智能推荐必传）
     */
    private String aiRecSceneId;

    /**
     * 智能推荐，单次请求返回的推荐结果数量，建议取值20,最大值50
     */
    @Schema(hidden = true)
    private Integer aiReturnCount;

    /**
     * 是否正在售后状态.1=是 0=否
     */
    private Boolean isAfterSales;

    /**
     * MCN机构ID(用户ID)
     */
    private Long mcnId;

    /**
     * 售后状态 ToConfirmed=待接单方确认， ReceiveRefuse=接单方拒绝 , Intervention=平台介入处理中， Terminate=已终止, Ended = 已完结 ,Closed = 售后已关闭,Canceled = 售后已取消,ToSettlement = 待结算
     */
    private String afterSalesState;

    /**
     * 群ID-查询群任务是否关联必传
     */
    private Long groupId;

    /**
     * 是否是推荐页
     */
    private Boolean recommend;

    /**
     * 类型数组
     */
    private List<Long> typeIds;

    /**
     * 交付时间查询类型
     */
    private Integer deliveryTimeType;

    /**
     * 开始交付时间
     */
    private Long startDeliveryTime;
    /**
     * 结束交付时间
     */
    private Long endDeliveryTime;

    /**
     * 是否过滤商单
     */
    private String filterTask;

    /**
     * 标签id数组
     */
    private List<Long> labelList;

    /**
     * 行业类型ID数组
     */
    private List<Long> industryTypeList;

    /**
     * 子任务编号-模糊查询
     */
    private String taskNumberLikeQuery;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 业务ids
     */
    private List<Long> handleIds;

    /**
     * 不存在用户id
     */
    private Set<Long> notUserIds;
}
