package com.sohu.busyorder.api.bo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 任务主体业务对象
 *
 * <AUTHOR>
 * @date 2023-12-11
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuBusyTaskBo extends BaseEntity {

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 作者id--国家站、超管查询时不传或者筛选传
     */
    private Long userId;
    /**
     * 父级ID
     */
    private Long pid;
    /**
     * 商单类型ID
     */
    @NotNull(message = "任务类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long type;
    /**
     * 行业类型ID
     */
    private Long industryType;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容描述
     */
    private String content;
    /**
     * 附件（url列表，逗号隔开）
     */
    private String annex;
    /**
     * 是否有接单人限制;false=默认全部可以接单 true=部分人可以接单
     */
    private Boolean receiveLimit;
    /**
     * 交付天数，从被接单时间开始计算
     */
    @Max(value = 1000, message = "交付天数不能超过1000天")
    private Integer deliveryDay;
    /**
     * 交付说明
     */
    private String deliveryMsg;
    /**
     * 结算方式;1=先执行后付款 2=依据进度付款
     */
    private Integer settleType;
    /**
     * 是否是阶段性交付,true=是，false=否
     */
    private Boolean deliveryStep;
    /**
     * 价值金额
     */
    @Max(value = 100000000L, message = "金额不能超过100000000")
    private BigDecimal fullAmount;
    /**
     * 价值币种
     */
    private Long fullCurrency;
    /**
     * 佣金类型;none:无设置，percentage:百分比，price:一口价
     */
    private String kickbackType;
    /**
     * 佣金类型值;佣金类型值
     */
    private BigDecimal kickbackValue;
    /**
     * 是否需要拆单，ture=是，false=否
     */
    private Boolean needSplit;
    /**
     * 主任务编号
     */
    private String taskNumber;
    /**
     * 主任务编号集合
     */
    private Collection<String> taskNumbers;
    /**
     * 是否是草稿,true=是，false=否
     */
    private Boolean isDraft;
    /**
     * 拆单状态.true=已拆单 false=未拆单
     */
    private Boolean splitState;
    /**
     * 主任务状态：WaitPend-待拆单，WaitApprove-待审核，OnSale-出售中，OffShelf-下架，CompelOff-强制下架
     */
    private String state;

    /**
     * 拒绝理由--如果是拒绝状态必须填写
     */
    private String refuseMsg;

    /**
     * 子任务状态：OnShelf：上架，OffShelf：下架，CompelOff：强制下架
     */
    private String shelfState;

    /**
     * 指派接单人用户id
     */
    private Long receiveOnlyUserId;

    /**
     * 阶段性交付列表
     */
    private List<SohuBusyTaskDeliveryBo> deliveryList;

    /**
     * 价值金额-最小值
     */
    @Min(value = 0, message = "金额不能小于0")
    private BigDecimal minAmount;

    /**
     * 价值金额-最大值
     */
    @Max(value = 100000000L, message = "金额不能超过100000000")
    private BigDecimal maxAmount;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 发布站点ID集合
     */
    private List<Long> addSiteIds;

    /**
     * 发布的国家站点
     */
    private Long countrySiteId;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 商单与站点关联入参
     */
    private List<SohuBusyTaskSiteBo> taskSiteBoList;

    /**
     * 只查自己的,true -只查自己
     */
    private Boolean onlySelf = Boolean.FALSE;

    /**
     * 只查已拆单的
     */
    private Boolean splitQuery;

    /**
     * 是否是保存草稿操作
     */
    private Boolean isSaveDraft;

    /**
     * 交付开始时间
     */
    private String deliveryStartDay;

    /**
     * 交付结束时间
     */
    private String deliveryEndDay;

    /**
     * 排序字段(createTime--最新发布,priceAsc--价格升序,priceDesc--价格降序,comprehensive--综合排序)
     */
    private String sortBy;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 地址
     */
    private String address;

    /**
     * 位置、经纬度
     */
    private String location;

    /**
     * 阅读数
     */
    private Long viewCount;

    /**
     * 关联任务id
     */
    private Long relationId;

    /**
     * 群ID
     */
    private Long groupId;

    /**
     * 时间单位 1.天 2.小时 3.分钟
     */
    private Integer timeUnit;
    /**
     * 交付类型 1.按时间交付 2.按次数交付
     */
    private Integer deliveryType;
    /**
     * 交付标准 大于0的正整数
     */
    private Integer deliveryStandard;
    /**
     * 交付单位 按时间交付 1.秒 2.分钟 3.小时  按次数交付 1.次
     */
    private Integer deliveryTimeUnit;
    /**
     * 创建数量
     */
    private Integer num;
    /**
     * 单个金额
     */
    private BigDecimal singleAmount;
    /**
     * 接单人数
     */
    private Integer receiveNum;
    /**
     * 是否需要审核接单  0.否  1.是
     */
    private Boolean isApproveReceive;

    /**
     * 是否收取保证金 0.否  1.是
     */
    @Schema(description = "是否收取保证金 0.否  1.是", example = "0")
    private Boolean isReceiveDeposit;

    /**
     * 素材地址
     */
    private List<String> sourceMaterialList;
    /**
     * 标签id
     */
    private List<Long> labelList;

    @Schema(name = "searchStatus", description = "搜索状态集合", example = "[\"WaitPend\",\"WaitApprove\",\"OnSale\",\"OffShelf\",\"CompelOff\"]")
    private List<String> searchStatus;

    /**
     * 愿望分类id
     */
    private Long categoryType;

    /**
     * 供需类型id
     */
    private Long supplyType;

    /**
     * 是否查询黑名单
     */
    private Boolean isBlack = Boolean.FALSE;

    /**
     * 平台行业id
     */
    private Long industryId;

    /**
     * 需要处理的id集合
     */
    private List<Long> handleIds;

    /**
     * 行业分类数组
     */
    private List<Long> industryTypeList;

    /**
     * 站点id
     */
    private Long siteId;

}
