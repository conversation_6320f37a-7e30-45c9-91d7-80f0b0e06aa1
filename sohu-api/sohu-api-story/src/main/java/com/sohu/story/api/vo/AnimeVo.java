package com.sohu.story.api.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 小说对象
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Data
@ExcelIgnoreUnannotated
public class AnimeVo {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private Long id;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long userId;

    /**
     * 付费章节金额
     */
    @ExcelProperty(value = "付费章节金额")
    private Long coin;

    /**
     * 1:漫画，2：小说，3：听说
     */
    @ExcelProperty(value = "1:漫画，2：小说，3：听说")
    private Long btype;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String title;

    /**
     * 作者
     */
    @ExcelProperty(value = "作者")
    private String author;

    /**
     * 封面图片
     */
    @ExcelProperty(value = "封面图片")
    private String coverpic;

    /**
     * 详情图片
     */
    @ExcelProperty(value = "详情图片")
    private String infopic;

    /**
     * 简介
     */
    @ExcelProperty(value = "简介")
    private String remark;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String desc;

    /**
     * 发布区域
     */
    @ExcelProperty(value = "发布区域")
    private String areas;

    /**
     * 漫画分类
     */
    @ExcelProperty(value = "漫画分类")
    private String cateids;

    /**
     * 漫画状态
     */
    @ExcelProperty(value = "漫画状态")
    private Long iswz;

    /**
     * 漫画属性
     */
    @ExcelProperty(value = "漫画属性")
    private Long isfw;

    /**
     * 新书/非新书
     */
    @ExcelProperty(value = "新书/非新书")
    private Long isnew;

    /**
     * 长篇/短篇
     */
    @ExcelProperty(value = "长篇/短篇")
    private Long islong;

    /**
     * 男频/女频
     */
    @ExcelProperty(value = "男频/女频")
    private Long issex;

    /**
     * 总章节数
     */
    @ExcelProperty(value = "总章节数")
    private Long allchapter;

    /**
     * 付费章节
     */
    @ExcelProperty(value = "付费章节")
    private Long paychapter;

    /**
     * 推荐关注章节
     */
    @ExcelProperty(value = "推荐关注章节")
    private Long schapter;

    /**
     * 默认推广章节
     */
    @ExcelProperty(value = "默认推广章节")
    private Long tchapter;

    /**
     * 人气值
     */
    @ExcelProperty(value = "人气值")
    private Long hots;

    /**
     * 热门指数
     */
    @ExcelProperty(value = "热门指数")
    private Long thermal;

    /**
     * 评论数
     */
    @ExcelProperty(value = "评论数")
    private Long comments;

    /**
     * 收藏数
     */
    @ExcelProperty(value = "收藏数")
    private Long likes;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long listOrder;

    /**
     * 分享标题
     */
    @ExcelProperty(value = "分享标题")
    private String sharetitle;

    /**
     * 分享简介
     */
    @ExcelProperty(value = "分享简介")
    private String sharedesc;

    /**
     * 是否为推荐，1：是，0：否
     */
    @ExcelProperty(value = "是否为推荐，1：是，0：否")
    private Integer isrecommend;

    /**
     * 是否上架
     */
    @ExcelProperty(value = "是否上架")
    private Long status;

    /**
     * 前端是否显示
     */
    @ExcelProperty(value = "前端是否显示")
    private Long ishow;

    /**
     * 打赏金额
     */
    @ExcelProperty(value = "打赏金额")
    private BigDecimal prize;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Integer isbg;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String selectpic;

    /**
     * 标签
     */
    @ExcelProperty(value = "标签")
    private String tag;

    /**
     * 是否为会员专享，1：是，0：否
     */
    @ExcelProperty(value = "是否为会员专享，1：是，0：否")
    private Integer isvip;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Date createdAt;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Date updatedAt;

    /**
     * 审核状态
     */
    @ExcelProperty(value = "审核状态")
    private Long reviewStatus;

    /**
     * 打赏扣量
     */
    @ExcelProperty(value = "打赏扣量")
    private Long rewardWithhold;
    /**
     * 图片地址
     */
    private String picture;


}
