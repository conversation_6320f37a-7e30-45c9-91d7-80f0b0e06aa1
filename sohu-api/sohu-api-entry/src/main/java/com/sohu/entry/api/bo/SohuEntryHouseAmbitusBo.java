package com.sohu.entry.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 房源配套业务对象
 *
 * <AUTHOR>
 * @date 2023-08-29
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuEntryHouseAmbitusBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 房源主体表id
     */
    @NotNull(message = "房源主体表id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long houseId;

    /**
     * 学校医院,json里面保存图片和关键词
     */
    private String schoolHospital;

    /**
     * 餐饮娱乐
     */
    private String entertainment;

    /**
     * 周边楼盘
     */
    private String ambitusHouse;

    /**
     * 交通方式
     */
    private String traffic;


}
