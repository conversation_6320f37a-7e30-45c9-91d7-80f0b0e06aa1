package com.sohu.report.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 用户浏览量统计视图对象
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
@ExcelIgnoreUnannotated
public class SohuUserViewReportVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 浏览次数
     */
    @ExcelProperty(value = "浏览次数")
    private Long viewCount;

    /**
     * 业务类型  Article 图文  Video 视频  Question 问答 ShortPlay 短剧  BusyOrder  商单 Novel 小说  Game 游戏 Literature 诗歌散文 Other 其他
     */
    @ExcelProperty(value = "业务类型  Article 图文  Video 视频  Question 问答 ShortPlay 短剧  BusyOrder  商单 Novel 小说  Game 游戏 Literature 诗歌散文 Other 其他")
    private String businessType;

    /**
     * 统计日期
     */
    @ExcelProperty(value = "统计日期")
    private String recordDate;


}
