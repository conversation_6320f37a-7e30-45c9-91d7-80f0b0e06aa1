package com.sohu.report.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 用户浏览量统计业务对象
 *
 * <AUTHOR>
 * @date 2025-06-24
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuUserViewReportBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 浏览次数
     */
    @NotNull(message = "浏览次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer viewCount;

    /**
     * 业务类型  Article 图文  Video 视频  Question 问答 ShortPlay 短剧  BusyOrder  商单 Novel 小说  Game 游戏 Literature 诗歌散文 Other 其他
     */
    @NotBlank(message = "业务类型  Article 图文  Video 视频  Question 问答 ShortPlay 短剧  BusyOrder  商单 Novel 小说  Game 游戏 Literature 诗歌散文 Other 其他不能为空", groups = { AddGroup.class, EditGroup.class })
    private String businessType;

    /**
     * 统计日期
     */
    @NotBlank(message = "统计日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private String recordDate;


}
