package com.sohu.report.api;

import com.sohu.report.api.bo.SohuUserTaskReportBo;
import com.sohu.report.api.bo.SohuUserViewReportBo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25 10:53
 */
public interface RemoteUserReportService {

    /**
     * 批量保存用户行为数据
     * @param bos
     */
    Boolean userBehaviorBatchSave(List<SohuUserViewReportBo> bos);

    /**
     * 批量保存用户任务数据
     * @param bos
     */
    Boolean userTaskBatchSave(List<SohuUserTaskReportBo> bos);
}
