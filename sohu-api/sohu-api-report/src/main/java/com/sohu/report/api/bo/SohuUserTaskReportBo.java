package com.sohu.report.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商单用户统计业务对象
 *
 * <AUTHOR>
 * @date 2025-06-24
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuUserTaskReportBo extends SohuEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 发单量
     */
    @NotNull(message = "发单量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long dispatchCount;

    /**
     * 接单量
     */
    @NotNull(message = "接单量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long acceptCount;

    /**
     * 统计日期
     */
    @NotBlank(message = "统计日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private String recordDate;


}
