package com.sohu.report.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 商单用户统计视图对象
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
@ExcelIgnoreUnannotated
public class SohuUserTaskReportVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 发单量
     */
    @ExcelProperty(value = "发单量")
    private Long dispatchCount;

    /**
     * 接单量
     */
    @ExcelProperty(value = "接单量")
    private Long acceptCount;

    /**
     * 统计日期
     */
    @ExcelProperty(value = "统计日期")
    private String recordDate;


}
