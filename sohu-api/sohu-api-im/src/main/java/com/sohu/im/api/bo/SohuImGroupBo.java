package com.sohu.im.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * im群组业务对象
 *
 * <AUTHOR>
 * @date 2023-08-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuImGroupBo extends BaseEntity {

    /**
     * 群组id
     */
    @NotNull(message = "群组id不能为空", groups = {EditGroup.class})
    @Schema(name = "id", description = "群id，新增不要传，编辑必传！", example = "2")
    private Long id;

    @Schema(name = "pid", description = "父群ID，创建子群时候，该值必传", example = "1")
    private Long pid;

    @Schema(name = "description", description = "群描述", example = "这是一个官方bug反馈群")
    private String description;

    @Schema(name = "name", description = "群名称", example = "官方bug反馈群")
    @NotBlank(message = "群名称不能为空", groups = {AddGroup.class})
    private String name;

    @Schema(name = "userId", description = "群主用户ID,前端不用传,后端自己处理", example = "1")
    private Long userId;

    @Schema(name = "userId", description = "群logo，前端不传的话，后端设置默认logo",
            example = "https://sohugloba.oss-cn-beijing.aliyuncs.com/2024/11/22/cc96fc7c72cb4c9a8fa7662c0efb3305_84x84.png")
    private String logo;

    @Schema(name = "groupGreet", description = "进群招呼", example = "欢迎新人进群")
    private String groupGreet;

    @Schema(name = "groupNotice", description = "群公告", example = "1、本群为任务沟通群聊，大家可在群沟通任务。任务结束后，此群会解散。\n" +
            "2、请遵守规范，礼貌友好沟通\n" +
            "3、禁止发布广告，恶意刷屏，站外引导")
    private String groupNotice;

    @Schema(name = "needConfirm", description = "进群是否需要确认（false=不需要 true=需要）", example = "true")
    private Boolean needConfirm;

    @Schema(name = "groupUserNum", description = "成员数", example = "10")
    @Hidden
    private int groupUserNum;

    @Schema(name = "userIds", description = "建群，传此字段，用户id集合，以英文逗号间隔组成的字符串", example = "1,2,3")
    private Object userIds;

    @Schema(name = "forbid", description = "是否禁言（false=否 true=是）", example = "true")
    private Boolean forbid;

    @Schema(name = "maxGroupUserNum", description = "最大成员数", example = "50")
    @Hidden
    private int maxGroupUserNum;

    @Schema(name = "maxGroupAdminNum", description = "最大群管理员人数", example = "10")
    @Hidden
    private int maxGroupAdminNum;

    /**
     * 群是否是匿名群(normal=不是匿名群,即普通群，groupTask=任务群)
     */
    private String groupType;

    /**
     * 是否需要授权（false=否 true=是）
     */
    private Boolean authorize;

    /**
     * 授权扩展字段
     * 如：
     * {
     * "authorizeTitle":"授权标题",
     * "authorizeContent":"授权内容",
     * "authorizePhoto":"授权图片"
     * }
     */
    private String authorizeExt;
    /**
     * 群扩展字段
     */
    private String groupExt;

    /**
     * 唯一值，为了解决dubbo重复建群
     */
    private Long unq;
    /**
     * 进群是否绑定拉新关系（false=否 true=是）
     */
    private Boolean bindUser;
    /**
     * 是否禁止添加好友(false=否 true=是)
     */
    private Boolean addFriend;
    /**
     * 是否按时间段禁言（false=否 true=是）
     */
    private Boolean forbidTime;

    @Schema(name = "groupWord", description = "进群口令", example = "qwer1234")
    private String groupWord;
    /**
     * 群状态字段（默认0，0=正常 1=群禁用）
     */
    private Integer state;
}
