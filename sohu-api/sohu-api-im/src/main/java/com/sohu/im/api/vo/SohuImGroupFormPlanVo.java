package com.sohu.im.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class SohuImGroupFormPlanVo implements Serializable {

    @Schema(name = "taskTitle", description = "任务名称", example = "汽车意向用户100人，有实力的接")
    private String taskTitle;

    @Schema(name = "fullAmount", description = "任务价值金额", example = "100")
    private BigDecimal fullAmount;

    @Schema(name = "deliveryStandard", description = "期望人数", example = "100")
    private Integer deliveryStandard ;

    @Schema(name = "standNum", description = "达标人数", example = "10")
    private Integer standNum = 0;

    @Schema(name = "standAmount", description = "达标金额", example = "50")
    private BigDecimal standAmount = BigDecimal.ZERO;

    @Schema(name = "receiveNum", description = "接单人数", example = "100")
    private Integer receiveNum;

    @Schema(name = "state", description = "任务状态:Execute-执行中，WaitApproveSettle-结算申请中，WaitSettle-待结算，OverSettle-已结算", example = "Execute")
    private String state;

    @Schema(name = "deliveryNum", description = "已交付人数", example = "30")
    private Integer deliveryNum = 0;

    @Schema(name = "taskReceUserName", description = "接单方用户名称", example = "张***")
    private String taskReceUserName;

    @Schema(name = "busyTaskUpdateTime", description = "商单进度更新时间", example = "2023-04-18 14:50:00")
    private Date busyTaskUpdateTime;

    @Schema(name = "groupUserUpdateTime", description = "群成员数据更新时间", example = "2023-04-18 14:50:00")
    private Date groupUserUpdateTime;
}
