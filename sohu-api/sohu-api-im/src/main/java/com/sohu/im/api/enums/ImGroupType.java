package com.sohu.im.api.enums;

import cn.hutool.core.util.StrUtil;

import java.io.Serializable;
import java.util.List;

/**
 * 群类型
 */
public enum ImGroupType implements Serializable {

    // 不是匿名群,即普通群
    group,
    // 普通任务群-匿名群
    groupTask,
    // 任务-客户群
    groupTaskCustom,
    // 表单任务主群
    groupForm,
    // 表单任务客户群
    groupFormCustom,
    // 通用商单群
    groupFromGeneral
    ;

    public transient static final List<String> specialGroupTypes = List.of(groupTask.name(), groupTaskCustom.name(), groupForm.name(), groupFormCustom.name());

    /**
     * 是否是特殊群
     *
     * @param type 群类型
     * @return 是否是特殊群
     */
    public static boolean isSpecialGroup(String type) {
        return StrUtil.equalsAnyIgnoreCase(type, groupTask.name(), groupTaskCustom.name(), groupForm.name(), groupFormCustom.name());
    }

    /**
     * 群成员需要打码的群
     *
     * @param type 群类型
     * @return 是否是特殊群
     */
    public static boolean isSensitiveGroup(String type) {
        return StrUtil.equalsAnyIgnoreCase(type, groupTask.name(), groupTaskCustom.name(), groupForm.name(), groupFormCustom.name());
    }
}
