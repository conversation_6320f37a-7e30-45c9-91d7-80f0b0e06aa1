package com.sohu.im.api.vo;

import com.sohu.im.api.bo.ImShareBo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 消息返回体结构
 */
@NoArgsConstructor
@Data
public class ImChatResponseVo implements Serializable {

    @Schema(name = "id", description = "SohuImChatMessage表主键自增ID，唯一", example = "1")
    private Long id;

    @Schema(name = "chatId", description = "聊天id，唯一,可用于消息删除，撤回", example = "867011392639800")
    private Long chatId;

    @Schema(name = "localId", description = "这个是用来本地识别消息关联发送状态，移动端使用的字段", example = "54D0D9B5-A7CF-451D-B2FC-FA88EF4DFB31")
    private String localId;

    @Schema(name = "sessionType", description = "会话类型:single - 单聊、group-群聊,groupTask-任务群", example = "group")
    private String sessionType;

    @Schema(name = "messageType", description = "消息类型:text - 文本、photo - 图片、video - 视频、voice - 语音、share - 分享、notice - 公告、file - 文件、command - 命令" +
            "voiceCall - 发起或收到语音、videoCall-发起或收到视频、voiceCallCancel - 取消或拒绝语音、videoCallCancel - 取消或拒绝视频、" +
            "voiceCallFinish - 结束语音、videoCallFinish - 结束视频、voiceCallBusy - 对方语音忙碌中、videoCallBusy - 对方视频忙碌", example = "text")
    private String messageType;

    @Schema(name = "readType", description = "消息是否已读", example = "true")
    private Boolean readType = Boolean.FALSE;

    @Schema(name = "mediaCall", description = "仅 messageType 为 videoCall 或 voiceCall 时，才填充该对象")
    private MediaCall mediaCall;

    @Schema(name = "body", description = "消息体")
    private BodyDTO body;

    @Schema(name = "sender", description = "发送者信息")
    private SenderDTO sender;

    @Schema(name = "receiver", description = "接收者信息")
    private ReceiverDTO receiver;

    @Schema(name = "share", description = "分享对象")
    private ImShareBo share;

    @Schema(name = "commandType", description = "命令数据信息(撤回、踢群、群解散、邀请人进群等等)：inviteMembers - 邀请进群、removeMembers - 移除群成员、" +
            "exitMember - 群成员主动退群、modifyName - 修改群成员、modifyLogo - 修改群logo、modifyApprove - 审核开关、addAdmin - 添加管理员、" +
            "removeAdmin - 移除管理员、modifyForbid - 群组禁言、addForbid - 禁言群成员、removeForbid - 移除禁言成员、dissolve - 解散群组、" +
            "createSubgroup - 创建子群、recall - 撤回", example = "modifyName")
    private String commandType;

    @Schema(name = "createTime", description = "创建时间", example = "8978956756")
    private long createTime;

    @Schema(name = "updateTime", description = "更新时间", example = "5678678689")
    private long updateTime;

    @Schema(name = "isRecall", description = "是否消息撤回", example = "true")
    private Boolean isRecall = false;

    @Schema(name = "unreadCount", description = "未读消息数", example = "1")
    private int unreadCount;

    @Schema(name = "asTopTime", description = "置顶时间", example = "5675689")
    private Long asTopTime;

    @Schema(name = "groupUserCount", description = "群人数", example = "22")
    private int groupUserCount;

    @Schema(name = "official", description = "是否官方人员", example = "true")
    private boolean official;

    @Schema(name = "userId", description = "对方用户ID", example = "1")
    private Long userId;

    @Schema(name = "userName", description = "对方用户名，优先获取对好友备注", example = "1234")
    private String userName;

    @Schema(name = "userAvatar", description = "对方用户头像", example = "https://sohugloba.oss-cn-beijing.aliyuncs.com/app/groupPrivacyLogo.png")
    private String userAvatar;

    @Schema(name = "ext", description = "拓展字段，json格式，如：{\"roomId\":\"116-9\"}", example = "{\"roomId\":\"116-9\"}")
    private String ext;

    @Schema(name = "atIds", description = "at 群内成员的userId，英文逗号分隔，如果列表中有0即表示@所有人", example = "1")
    private String atIds;

    /**
     * 通话挂断才保存此值
     * {@link com.sohu.im.api.enums.MediaCallTypeEnum}
     */
    @Schema(name = "mediaType", description = "通话挂断才保存此值")
    private String mediaType;

    @Schema(name = "merchant", description = "店铺信息")
    private Merchant merchant;

    @Schema(name = "file", description = "文件属性")
    private File file;

    @Schema(name = "msgSource", description = "消息来源")
    private String msgSource;

    @Schema(name = "nanoTime", description = "消息入库纳秒时间")
    private Long nanoTime;

    @Schema(name = "hidden", description = "是否隐藏消息", example = "false", defaultValue = "false")
    private boolean hidden;

    @NoArgsConstructor
    @Data
    public static class BodyDTO {

        @Schema(name = "content", description = "消息内容，如果是文本消息，即输入的文本，图片消息就是图片的url,视频消息就是视频的Url、语音消息的Url", example = "你好")
        private String content;

        @Schema(name = "thumbnail", description = "视频消息的封面图Url", example = "")
        private String thumbnail;

        @Schema(name = "err", description = "被拦截的原因，比如文字涉恐、反共、非好友、或其他规则", example = "涉恐")
        private String err;

        @Schema(name = "duration", description = "语音消息或视频消息的时长", example = "15")
        private int duration;

        @Schema(name = "size", description = "文件大小", example = "234546")
        private long size;
    }

    /**
     * 发送者信息
     */
    @NoArgsConstructor
    @Data
    public static class SenderDTO {

        @Schema(name = "id", description = "发送人ID", example = "1")
        private Long id;

        @Schema(name = "name", description = "用户昵称", example = "test123")
        private String name;

        @Schema(name = "groupUserNickName", description = "用户在本群的昵称", example = "客服08")
        private String groupUserNickName;

        @Schema(name = "avatar", description = "用户头像", example = "https://sohugloba.oss-cn-beijing.aliyuncs.com/app/groupPrivacyLogo.png")
        private String avatar;

        @Schema(name = "role", description = "身份类型（group_leader=群主 group_admin=管理员 group_user=普通成员），当sessionType=group 即群聊 才显示身份", example = "group_admin")
        private String role;

        @Schema(name = "specialRole", description = "特殊身份，当sessionType =groupTask, taskPublish=发单方，taskRece=接单方", example = "taskPublish")
        private String specialRole;

        @Schema(name = "forbid", description = "发送人是否被禁言（false=否 true=是）", example = "true", defaultValue = "false")
        private Boolean forbid = false;
    }


    /**
     * 接收者信息
     */
    @NoArgsConstructor
    @Data
    public static class ReceiverDTO {

        @Schema(name = "id", description = "群id或接收人id", example = "1")
        private Long id;

        @Schema(name = "name", description = "群昵称或接收人昵称", example = "官方反馈群")
        private String name;

        @Schema(name = "avatar", description = "群logo或接收人头像", example = "https://sohugloba.oss-cn-beijing.aliyuncs.com/app/groupPrivacyLogo.png")
        private String avatar;
    }

    @Data
    public static class MediaCall {

        @Schema(name = "roomId", description = "房间ID，发起人id-接收人id", example = "1-2")
        private String roomId;

        @Schema(name = "originUserId", description = "最开始的call发起人，固定不变", example = "1")
        private Long originUserId;

        @Schema(name = "userId", description = "发起人id", example = "1")
        private Long userId;

        @Schema(name = "members", description = "聊天成员id集合-移动端给的")
        private Collection<Long> members;

        @Schema(name = "realMembers", description = "在房间中的实际成员id集合")
        private Collection<Long> realMembers;

        @Schema(name = "memberList", description = "聊天成员id集合")
        private List<MemberUser> memberList;

        /**
         * 在房间中的实际成员id集合
         */
        @Schema(name = "realMemberList", description = "在房间中的实际成员id集合")
        private List<MemberUser> realMemberList;

        /**
         * {@link com.sohu.im.api.enums.MediaCallTypeEnum}
         */
        private String type;

        @Schema(name = "isFront", description = "是否为前置摄像头", example = "true")
        private boolean isFront;

        @Schema(name = "sessionDescription", description = "仅type 为 offer 或 answer 时填充该对象", example = "")
        private Object sessionDescription;

        @Schema(name = "iceCandidate", description = "仅 type 为 ice 时才填充该对象", example = "")
        private Object iceCandidate;

        @Schema(name = "video", description = "是否开启视频 open、 close， type 为 video 时填充", example = "open")
        private String video;

        @Schema(name = "fromUserId", description = "join发送人ID", example = "1")
        private Long fromUserId;

    }

    @Data
    public static class MemberUser {

        @Schema(name = "userId", description = "用户id", example = "1")
        private Long userId;

        @Schema(name = "userName", description = "用户名称", example = "hss_423434")
        private String userName;

        @Schema(name = "nickName", description = "用户昵称", example = "官方客服")
        private String nickName;

        @Schema(name = "userAvatar", description = "头像", example = "true")
        private String userAvatar;

        @Schema(name = "alias", description = "备注", example = "true")
        private String alias;
    }

    /**
     * 店铺信息
     */
    @NoArgsConstructor
    @Data
    public static class Merchant {

        @Schema(name = "id", description = "店铺ID", example = "1")
        private Long id;

        @Schema(name = "userId", description = "商家归属用户ID", example = "1")
        private Long userId;

        @Schema(name = "name", description = "店铺名称", example = "狐少少店铺")
        private String name;

        @Schema(name = "avatar", description = "店铺头像", example = "https://sohugloba.oss-cn-beijing.aliyuncs.com/app/groupPrivacyLogo.png")
        private String avatar;

        @Schema(name = "senderIsMerchant", description = "发送方是否是商户", example = "false")
        private Boolean senderIsMerchant = false;
    }

    @Data
    public static class File {

        @Schema(name = "fileSize", description = "文件大小", example = "821315")
        private long fileSize;

        @Schema(name = "fileName", description = "文件名", example = "加班日期.docx")
        private String fileName;
    }
}
