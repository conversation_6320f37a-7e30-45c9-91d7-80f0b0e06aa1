package com.sohu.im.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;

/**
 * im群用户业务对象
 *
 * <AUTHOR>
 * @date 2023-08-31
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SohuImGroupUserBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 群id
     */
    @NotNull(message = "群id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long groupId;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 通知级别(group=群级别,user=用户级别)
     */
    private String notifyLevel;

    /**
     * 群权限(group_leader=群主 group_admin=管理员 group_user=普通成员)
     */
    private String permissionType;

    /**
     * 资源名片id
     */
    private Long cardId;
    /**
     * 是否禁言（false=否 true=是）
     */
    private Boolean forbid;

    /**
     * 邀请人ID
     */
    private Long inviteUser;

    /**
     * 排序
     */
    private Integer sortIndex;

    /**
     * 主键ids
     */
    private Object ids;

    /**
     * 多个用户ID
     */
    private Object userIds;

    /**
     * 拓展字段
     */
    private String ext;
    /**
     * 用户在本群的昵称
     */
    private String groupUserNickName;
    /**
     * 是否打码，即加密（0=不打码，1=打码）
     */
    private Boolean sensitiveSwitch;

    @Schema(name = "inviteUserId", description = "邀请人ID(用户id)", example = "1")
    @Hidden
    private Long inviteUserId;

}
