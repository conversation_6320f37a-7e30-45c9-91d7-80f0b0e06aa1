package com.sohu.im.api.bo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@NoArgsConstructor
@Data
public class ImShareBo implements Serializable {

    /**
     * 分享类型
     */
    private String type;
    /**
     * 分享对象id，非必须 如分享类型为 名片、邀请码 等可不传
     */
    private Long id;
    /**
     * 分享对象归属人id，必须   反查用户名头像名字
     */
    private Long userId;
    /**
     * 分享人ID
     */
    private Long shareUserId;
    /**
     * 分享人名称
     */
    private String shareUserName;
    /**
     * 分享人头像
     */
    private String shareUserAvatar;
    /**
     * 卡片标题，必须
     */
    private String title;
    /**
     * 封面图，任何对象都用该字段传递，非必须（图文、视频）
     */
    private String coverImage;
    /**
     * 短剧、剧集关联，非必须
     */
    private String episodeRelevance;
    /**
     * 集数 默认0，短剧要大于0，非必须
     */
    private String episodeNumber;
    /**
     * 外部链接，使用浏览器打开，非必须
     */
    private String link;
    /**
     * 分享内容，分享文件时是下载地址、分享邀请码时则是邀请码，分享任务时则是订单号，非必须
     */
    private String content;
    /**
     * 分享对象归属人头像，非必须
     */
    private String avatar;
    /**
     * 分享对象归属人头像，非必须
     */
    private String name;
    /**
     * 金额，非必须
     */
    private BigDecimal amount;
    /**
     * 原价金额，非必须
     */
    private BigDecimal originAmount;
    /**
     * 订单状态（UNPAID：待支付 WAIT_SENT：待发货：WAIT_RECEIVE 待收货,2：RECEIVE 已收货,COMPLETED：已完成，CANCEL：已取消）
     */
    private String orderStatus;
    /**
     * 订单商品个数
     */
    private int orderNum;
    /**
     * 订单创建时间
     */
    private String orderCreateTime;

    /**
     * 站长类型 1 城市站长 2 行业站长
     */
    private Integer siteType;
    /**
     * 站点id
     */
    private Long siteId;

}
