package com.sohu.im.api.noticeContent;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 系统消息内容
 */
@NoArgsConstructor
@Data
public class NoticeContentDetail {

    /**
     * id
     */
    private Long id;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 标题描述
     */
    private String desc;

    /**
     * 时间
     */
    private String time;

    /**
     * 身份证
     */
    private String idCard;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 链接标题
     */
    private String linkTitle;

    /**
     * 链接地址
     */
    private String linkUrl;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 关键字
     */
    private String[] keyWord;

    /**
     * 封面图
     */
    private String coverImage;

    /**
     * 内容标题
     */
    private String contentTitle;

    /**
     * 状态
     */
    private String status;

    /**
     * 类型
     */
    private String typeInfo;

    /**
     * 结果
     */
    private String result;

    /**
     * 角色标识
     */
    private String roleKey;

    /**
     * 角色标识-中文
     */
    private String role;

}
