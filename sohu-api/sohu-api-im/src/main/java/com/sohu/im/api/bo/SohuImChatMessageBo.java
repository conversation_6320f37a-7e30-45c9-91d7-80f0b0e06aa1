package com.sohu.im.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * im消息业务对象
 *
 * <AUTHOR>
 * @date 2024-04-01
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuImChatMessageBo extends SohuEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;
    /**
     * 被拦截的原因，比如文字涉恐、反共、非好友、或其他规则
     */
    private String err;
    /**
     * 发送人ID
     */
    @NotNull(message = "发送人ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long senderId;

    /**
     * 接收人id，用户ID或群ID
     */
    @NotNull(message = "接收人id，用户ID或群ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long receiverId;

    /**
     * 会话类型:system - 系统类型、single - 单聊、group-群聊
     */
    @NotBlank(message = "会话类型:system - 系统类型、single - 单聊、group-群聊不能为空", groups = {AddGroup.class, EditGroup.class})
    private String sessionType;

    /**
     * 消息类型;(text - 文本、photo - 图片、video - 视频、voice - 语音、share - 分享、voiceCall - 语音通话、groupVoiceCall-群语音通话 ,videoCall - 视频通话、groupCall - 群视频、notice - 公告、file - 文件、command - 命令)
     */
    @NotBlank(message = "消息类型;(text - 文本、photo - 图片、video - 视频、voice - 语音、share - 分享、voiceCall - 语音通话、groupVoiceCall-群语音通话 ,videoCall - 视频通话、groupCall - 群视频、notice - 公告、file - 文件、command - 命令)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String messageType;

    /**
     * 分享类型，task-任务，good-商品，article-图文，video-视频，goodWindow-商品橱窗
     */
    private String shareType;
    /**
     * json，不做处理
     */
    private String shareParam;

    /**
     * 消息是否已读(1=已读 0=未读)
     */
    private Boolean readType;

    /**
     * 是否显示（1=隐藏 0=显示）
     */
    private Boolean hidden;

    /**
     * 分享数据的id
     */
    private Long shareId;

    /**
     * 唯一值，纳秒
     */
    @NotNull(message = "唯一值，纳秒不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long chatId;

    /**
     * 会话id
     */
    @NotNull(message = "会话id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long msgId;

    /**
     * 语音消息或视频消息的时长
     */
    private Integer duration;

    /**
     * 文件大小，单位kb
     */
    private long fileSize;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 文件是否删除（true=1,false=0）
     */
    private boolean fileDelete;
    /**
     * 这个是用来本地识别消息关联发送状态
     */
    private String localId;

    /**
     * 消息类型
     */
    private List<String> messageTypeList;
    /**
     * 命令类型
     * {@link com.sohu.im.api.enums.ImCommandTypeEnum}
     */
    private String commandType;
    /**
     * 商户店铺id
     */
    private Long merchantId;
    /**
     * 消息来源
     */
    private String msgSource;
}
