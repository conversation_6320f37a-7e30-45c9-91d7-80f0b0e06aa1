package com.sohu.im.api.service;

import com.sohu.common.page.core.TableDataInfo;
import com.sohu.im.api.bo.*;
import com.sohu.im.api.enums.ImRiskTypeEnum;
import com.sohu.im.api.vo.SohuImGroupUserVo;
import com.sohu.im.api.vo.SohuImGroupVo;

import java.util.Date;
import java.util.List;

/**
 * IM服务
 *
 * <AUTHOR>
 */
public interface RemoteImService {

    /**
     * 删除历史消息
     *
     * @param userId
     * @param friendId
     */
    void deleteHistory(Long userId, Long friendId);

    /**
     * 获取群用户列表ID
     *
     * @param groupId 群Id
     * @return {@link List}
     */
    List<Long> getGroupUserIds(Long groupId);

    /**
     * 查询群的所有用户
     *
     * @param groupId 群ID
     * @return {@link List}
     */
    List<SohuImGroupUserVo> groupUsers(Long groupId);

    /**
     * 发送消息-新
     *
     * @param senderId 发送者ID
     * @param bo       socket消息入参
     */
    void sendMessage(Long senderId, ImChatRequestBo bo);

    /**
     * 更新群人数，用于xxl-job定时任务来更新群人数不对问题
     *
     * @param firstUpdate 是否是第一次更新，true-第一次更新，false-非第一次更新（只会更新近一个小时内有群消息的群人数）
     */
    void updateGroupCount(boolean firstUpdate);

    /**
     * 共同群数量
     */
    int togetherGroupCount(Long loginId, Long userId);

    /**
     * 共同群列表
     */
    List<SohuImGroupVo> togetherGroupList(Long loginId, Long userId);

    /**
     * 建群,可建大群或小群
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean createGroup(SohuImGroupCreateBo bo);

    /**
     * 删除群
     *
     * @param groupId    群Id
     * @param canDismiss 是否可以强制解散群
     * @return {@link Boolean}
     */
    Boolean deleteGroup(Long groupId, Boolean canDismiss);

    /**
     * 通过子群单号删除群-需要登录才能调
     * 如果是回调这种，调用会报错
     *
     * @param publishUserId   发单方用户ID
     * @param childTaskNumber 子群单号
     * @return {@link Boolean}
     */
    Boolean deleteGroupTask(Long publishUserId, String childTaskNumber);

    /**
     * 通过子群单号删除群-不需要登录才能调
     *
     * @param publishUserId   发单方用户ID
     * @param childTaskNumber 子群单号
     * @return {@link Boolean}
     */
    Boolean deleteGroupTaskNoLogin(Long publishUserId, String childTaskNumber);

    /**
     * 推荐群聊
     *
     * @return
     */
    List<SohuImGroupVo> recommGroupList();

    /**
     * IM上传文件超时7天处理
     *
     * @return {@link Boolean}
     */
    Boolean imFileOverTimeHandler();

    /**
     * 举报IM群
     *
     * @param bo
     * @return
     */
    Boolean reportGroup(SohuImGroupReportBo bo);

    /**
     * 群修改
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean updateGroup(SohuImGroupBo bo);

    /**
     * 获取入群口令
     *
     * @param groupId 群id
     * @return {@link String} 入群口令
     */
    String getGroupWord(Long groupId);

    /**
     * 群禁用
     *
     * @return {@link Boolean}
     */
    Boolean groupDisable(SohuImGroupDisableBo bo);

    /**
     * 后台群启用
     *
     * @param groupId 群id
     * @return {@link Boolean}
     */
    Boolean groupEnable(Long groupId);

    /**
     * 群列表分页查询
     *
     * @return {@link TableDataInfo}
     */
    TableDataInfo<SohuImGroupVo> groupPage(SohuImPageQueryBo bo);

    /**
     * 群查询
     *
     * @param groupId 群id
     * @return {@link SohuImGroupVo}
     */
    SohuImGroupVo queryById(Long groupId);

    /**
     * 查询我的群聊
     *
     * @return
     */
    List<SohuImGroupVo> queryMyImGroup(SohuMyImGroupQueryBo bo);

    /**
     * 通过接单号获取群信息
     *
     * @param groupType  群类型 {@link com.sohu.im.api.enums.ImGroupType}
     * @param taskNumber 接单号
     * @return {@link SohuImGroupVo}
     */
    SohuImGroupVo queryByTaskNumber(String groupType, String taskNumber);

    /**
     * 清理聊天数据缓存
     *
     * @param beforeTime 在这个时间之前的数据
     * @return {@link Boolean}
     */
    Boolean clearChatDataCache(Date beforeTime);

    /**
     * 更新IM风险信息
     *
     * @param busyType  检测类型 {@link ImRiskTypeEnum}
     * @param busyCode  检测对象，对应表主键ID或者其它唯一值
     * @param checkPass 是否通过检测 ,true=检测通过
     * @return
     */
    Boolean updateImRisk(String busyType, String busyCode, boolean checkPass);

}
