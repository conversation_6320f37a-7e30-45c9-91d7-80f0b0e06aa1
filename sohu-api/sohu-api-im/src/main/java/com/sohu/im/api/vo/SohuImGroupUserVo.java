package com.sohu.im.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sohu.common.core.utils.DateUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import static com.sohu.common.core.utils.DateUtils.TIME_ZONE_DEFAULT;


/**
 * im群用户视图对象
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
@Data
@ExcelIgnoreUnannotated
public class SohuImGroupUserVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 群id
     */
    @ExcelProperty(value = "群id")
    private Long groupId;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 通知级别(group=群级别,user=用户级别)
     */
    @ExcelProperty(value = "通知级别(group=群级别,user=用户级别)")
    private String notifyLevel;

    /**
     * 群权限(group_leader=群主 group_admin=管理员 group_user=普通成员)
     */
    @ExcelProperty(value = "群权限(group_leader=群主 group_admin=管理员 group_user=普通成员)")
    private String permissionType;
    /**
     * 特殊身份，当sessionType =groupTask, taskPublish=发单方，taskRece=接单方 {@link com.sohu.im.api.enums.ImGroupTaskRole}
     */
    private String specialRole;

    /**
     * 资源名片id
     */
    @ExcelProperty(value = "资源名片id")
    private Long cardId;

    /**
     * 群头像或者用户头像
     */
    private String userAvatar;

    /**
     * 群名称或者用户名称
     */
    private String userName;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 备注
     */
    private String alias;
    /**
     * 是否禁言（0=否 1=是）
     */
    private boolean forbid;
    /**
     * 邀请人ID
     */
    private Long inviteUser;

    /**
     * 排序
     */
    private Integer sortIndex;

    /**
     * 拓展字段
     */
    private String ext;

    /**
     * 进群时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM, timezone = TIME_ZONE_DEFAULT)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM, timezone = TIME_ZONE_DEFAULT)
    private Date updateTime;
    /**
     * 用户在本群的昵称
     */
    private String groupUserNickName;
    /**
     * 是否打码，即加密（0=不打码，1=打码）
     */
    private Boolean sensitiveSwitch;
}
