package com.sohu.im.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sohu.common.core.utils.DateUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import static com.sohu.common.core.utils.DateUtils.TIME_ZONE_DEFAULT;


/**
 * im群用户视图对象
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
@Data
@ExcelIgnoreUnannotated
public class SohuImGroupUserVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "ID")
    @Schema(name = "id", description = "主键ID", example = "1")
    private Long id;

    @ExcelProperty(value = "群id")
    @Schema(name = "groupId", description = "群id", example = "1")
    private Long groupId;

    @ExcelProperty(value = "用户id")
    @Schema(name = "userId", description = "用户id", example = "116")
    private Long userId;

    @ExcelProperty(value = "通知级别(group=群级别,user=用户级别)")
    @Schema(name = "notifyLevel", description = "通知级别(group=群级别,user=用户级别)", example = "user")
    private String notifyLevel;

    @ExcelProperty(value = "群权限(group_leader=群主 group_admin=管理员 group_user=普通成员)")
    @Schema(name = "permissionType", description = "群权限(group_leader=群主 group_admin=管理员 group_user=普通成员)", example = "group_user")
    private String permissionType;

    /**
     * 特殊身份，当sessionType =groupTask, taskPublish=发单方，taskRece=接单方 {@link com.sohu.im.api.enums.ImGroupTaskRole}
     */
    @Schema(name = "specialRole", description = "特殊身份，当sessionType =groupTask, taskPublish=发单方，taskRece=接单方", example = "taskPublish")
    private String specialRole;

    @ExcelProperty(value = "资源名片id")
    @Schema(name = "cardId", description = "资源名片id", example = "1")
    private Long cardId;

    @Schema(name = "userAvatar", description = "群头像或者用户头像", example = "http://**************:9000/sohuglobal/2024/11/22/da7d9ecc84d14e09a65145f8118b9161_1024x1024.png")
    private String userAvatar;

    @Schema(name = "userName", description = "群名称或者用户名称", example = "张三")
    private String userName;

    @Schema(name = "nickName", description = "昵称", example = "张三")
    private String nickName;

    @Schema(name = "alias", description = "备注", example = "小张")
    private String alias;

    @Schema(name = "forbid", description = "是否禁言（0=否 1=是）", example = "true")
    private boolean forbid;

    @Schema(name = "inviteUser", description = "邀请人ID", example = "1")
    private Long inviteUser;

    @Schema(name = "sortIndex", description = "排序", example = "1")
    private Integer sortIndex;

    @Schema(name = "ext", description = "拓展字段")
    private String ext;

    @Schema(name = "createTime", description = "进群时间", example = "2025-6-24 15:21:16")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM, timezone = TIME_ZONE_DEFAULT)
    private Date createTime;

    @Schema(name = "updateTime", description = "更新时间", example = "2025-6-24 15:21:26")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM, timezone = TIME_ZONE_DEFAULT)
    private Date updateTime;

    @Schema(name = "groupUserNickName", description = "用户在本群的昵称", example = "老张")
    private String groupUserNickName;

    @Schema(name = "sensitiveSwitch", description = "是否打码，即加密（0=不打码，1=打码）", example = "张**")
    private Boolean sensitiveSwitch;

    @Schema(name = "official", description = "是否官方人员", example = "true")
    private boolean official;

    @Schema(name = "customService", description = "是否是客服", example = "true")
    private boolean customService;

}
