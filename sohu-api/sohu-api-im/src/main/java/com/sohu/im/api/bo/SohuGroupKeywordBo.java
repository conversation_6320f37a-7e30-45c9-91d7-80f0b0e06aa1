package com.sohu.im.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 群聊关键词设置业务对象
 *
 * <AUTHOR>
 * @date 2024-05-11
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuGroupKeywordBo extends SohuEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 群ID
     */
    @NotNull(message = "群ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long groupId;

    /**
     * 关键词开关，默认关闭（true-1-开启）
     */
    private Boolean keywordOpen;

    /**
     * 关键词,逗号隔开
     */
    private String keyword;

    /**
     * 白名单用户id,逗号隔开
     */
    private String whiteList;


}
