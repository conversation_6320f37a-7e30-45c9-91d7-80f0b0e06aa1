package com.sohu.im.api.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * 通用商单建群
 */
@Data
public class SohuImGroupFormGeneralCreateBo implements Serializable {

    /**
     * 群logo
     */
    public transient static final String GROUP_FROM_LOGO = "https://sohugloba.oss-cn-beijing.aliyuncs.com/app/groupFromGeneralLogo.png";

    /**
     * 群logo
     */
    public transient static final String GROUP_FROM_LOGO_GRAY = "https://sohugloba.oss-cn-beijing.aliyuncs.com/app/groupFromGeneralLogoGray.png";

    /**
     * 群公告
     */
    public transient static final String ANONYMOUS_NOTICE = "\uD83C\uDF89 欢迎加入【狐少少】全球许愿池！本群禁广告/外链/涉密  ，备注（行业+称呼），其他需求可寻求平台客服！";

    /**
     * 第一条提示语
     */
    public transient static final String NOTICE_ONE = "欢迎加入圆梦群!为保障交易安全，所有愿望信息的讨论，请务必在本群内进行。感谢配合";

    /**
     * 主单号
     */
    private String masterTaskNumber;
    /**
     * 子单号
     */
    private String taskNumber;

    /**
     * 接单方用户ID
     */
    private Long receiveUserId;

}
