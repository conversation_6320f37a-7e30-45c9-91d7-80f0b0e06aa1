package com.sohu.im.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 群发内容记录业务对象
 *
 * <AUTHOR>
 * @date 2024-04-23
 */

@Data
public class SohuBatchSendBo implements Serializable {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;

    /**
     * 类型:single - 单聊、group-群聊
     */
    @NotBlank(message = "类型:single - 单聊、group-群聊不能为空", groups = {AddGroup.class, EditGroup.class})
    private String type;

    /**
     * 接收人id，即用户ID或群ID，英文逗号隔开
     */
    @NotBlank(message = "接收人id，即用户ID或群ID，英文逗号隔开不能为空", groups = {AddGroup.class, EditGroup.class})
    private String receiverId;


}
