package com.sohu.im.api.enums;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * IM封控类型说明
 */
@Getter
@AllArgsConstructor
public enum ImRiskTypeEnum {

    ImFriendNoteImage("ImFriendNoteImage", 2, "通讯录照片"),
    ImChatSingleImage("ImChatSingleImage", 2, "单聊-照片"),
    ImChatGroupImage("ImChatGroupImage", 2, "群聊-照片"),
    ImFriendTag("ImFriendTag", 1, "标签-通讯录"),
    ImTagAdd("ImTagAdd", 1, "标签-新增"),
    ImFriendAlias("ImFriendAlias", 1, "备注-通讯录"),
    ImFriendNoteTxt("ImFriendNoteTxt", 1, "描述-通讯录"),
    ImChatSingleText("ImChatSingleText", 1, "单聊-文本"),
    ImChatGroupLogo("ImChatGroupLogo", 2, "群头像"),
    ImChatGroupName("ImChatGroupName", 1, "群聊-名称"),
    ImChatGroupGreet("ImChatGroupGreet", 1, "群聊-欢迎语"),
    ImChatGroupNotice("ImChatGroupNotice", 1, "群聊-群公告"),
    ImChatGroupNickname("ImChatGroupNickname", 1, "群聊-我在本群昵称"),
    ImChatGroupQuickReply("ImChatGroupQuickReply", 1, "群聊资料-群快捷回复"),
    ImChatGroupBatchQuickReply("ImChatGroupBatchQuickReply", 1, "群聊资料-群发消息"),
    ImChatGroupText("ImChatGroupText", 1, "群聊-文本"),
    ImChatGroupApplyMsg("ImChatGroupApplyMsg", 1, "群聊-进群申请备注"),
    ImFriendApplyAlias("ImFriendApplyAlias", 1, "好友申请备注"),
    ImFriendApplyMessage("ImFriendApplyMessage", 1, "好友申请信息"),
    ImFriendApplyDesc("ImFriendApplyDesc", 1, "好友申请描述"),
    ImChatSingleVoice("ImChatSingleVoice", 4, "单聊-语音说话"),
    ImChatGroupVoice("ImChatGroupVoice", 4, "群聊-语音说话"),
    ImChatSingleVideo("ImChatSingleVideo", 3, "单聊-视频"),
    ImChatGroupVideo("ImChatGroupVideo", 3, "群聊-视频"),
    ImChatSingleTextLink("ImChatSingleTextLink", 5, "单聊-链接"),
    ImChatSingleFile("ImChatSingleFile", 6, "单聊-文件"),
    ImChatGroupFile("ImChatGroupFile", 6, "群聊-文件"),
    ImChatGroupChannelName("ImChatGroupChannelName", 1, "进群-渠道-名称"),
    ImChatGroupChannelAlias("ImChatGroupChannelAlias", 1, "进群-渠道-备注"),
    ImChatGroupChannelImage("ImChatGroupChannelImage", 1, "进群-渠道-备注图片");

    @Schema(name = "检测对象", description = "检测对象", example = "ImChatSingleImage")
    private String busyType;

    @Schema(name = "detectType", description = "检测类型  1.文本  2.图片 3.视频 4.音频 5.链接 6.文档", example = "1")
    private int detectType;

    @Schema(name = "desc", description = "描述", example = "单聊-照片")
    private String desc;

    /**
     * 默认风险图片地址
     */
    public static final String RISK_DEFAULT_IMAGE = "https://sohugloba.oss-cn-beijing.aliyuncs.com/app/risk_default.png";

    /**
     * IM聊天类型列表
     */
    public static final List<String> IM_CHAT_TYPE_LIST = List.of(
            ImRiskTypeEnum.ImChatSingleImage.getBusyType(),
            ImRiskTypeEnum.ImChatGroupImage.getBusyType(),
            ImRiskTypeEnum.ImChatSingleVideo.getBusyType(),
            ImRiskTypeEnum.ImChatGroupVideo.getBusyType(),
            ImRiskTypeEnum.ImChatSingleFile.getBusyType(),
            ImRiskTypeEnum.ImChatGroupFile.getBusyType(),
            ImRiskTypeEnum.ImChatSingleVoice.getBusyType(),
            ImRiskTypeEnum.ImChatGroupVoice.getBusyType(),
            ImRiskTypeEnum.ImChatSingleTextLink.getBusyType(),
            ImRiskTypeEnum.ImChatGroupText.getBusyType(),
            ImRiskTypeEnum.ImChatSingleText.getBusyType()
    );

    public static ImRiskTypeEnum getByImMessageType(String sessionType, String messageType) {
        if (StrUtil.equalsAnyIgnoreCase(messageType, ImMessageTypeEnum.Image.getCode())) {
            return StrUtil.equalsAnyIgnoreCase(sessionType, ImSessionTypeEnum.single.getCode(), ImSessionTypeEnum.merchant.getCode()) ?
                    ImRiskTypeEnum.ImChatSingleImage : ImRiskTypeEnum.ImChatGroupImage;
        } else if (StrUtil.equalsAnyIgnoreCase(messageType, ImMessageTypeEnum.Video.getCode())) {
            return StrUtil.equalsAnyIgnoreCase(sessionType, ImSessionTypeEnum.single.getCode(), ImSessionTypeEnum.merchant.getCode()) ?
                    ImRiskTypeEnum.ImChatSingleVideo : ImRiskTypeEnum.ImChatGroupVideo;
        } else if (StrUtil.equalsAnyIgnoreCase(messageType, ImMessageTypeEnum.File.getCode())) {
            return StrUtil.equalsAnyIgnoreCase(sessionType, ImSessionTypeEnum.single.getCode(), ImSessionTypeEnum.merchant.getCode()) ?
                    ImRiskTypeEnum.ImChatSingleFile : ImRiskTypeEnum.ImChatGroupFile;
        } else if (StrUtil.equalsAnyIgnoreCase(messageType, ImMessageTypeEnum.Voice.getCode())) {
            return StrUtil.equalsAnyIgnoreCase(sessionType, ImSessionTypeEnum.single.getCode(), ImSessionTypeEnum.merchant.getCode()) ?
                    ImRiskTypeEnum.ImChatSingleVoice : ImRiskTypeEnum.ImChatGroupVoice;
        } else {
            return StrUtil.equalsAnyIgnoreCase(sessionType, ImSessionTypeEnum.single.getCode(), ImSessionTypeEnum.merchant.getCode()) ?
                    ImRiskTypeEnum.ImChatSingleText : ImRiskTypeEnum.ImChatGroupText;
        }
    }


}
