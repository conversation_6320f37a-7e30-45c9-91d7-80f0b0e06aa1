package com.sohu.im.api.bo;

import com.sohu.im.api.vo.SohuImGroupUserVo;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 建群
 */
@Data
public class SohuImGroupCreateBo implements Serializable {

    /**
     * 匿名群logo
     */
    public transient static final String GROUP_PRIVACY_LOGO = "https://sohugloba.oss-cn-beijing.aliyuncs.com/app/groupPrivacyLogo.png";

    /**
     * 匿名群公告
     */
    public static final String ANONYMOUS_NOTICE = "1、本群为任务沟通群聊，大家可在群沟通任务。任务结束后，此群会解散。\n" +
            "2、请遵守规范，礼貌友好沟通\n" +
            "3、禁止发布广告，恶意刷屏，站外引导";

    @Hidden
    private Long groupId;
    /**
     * 父级群ID
     */
    private Long groupPid;
    /**
     * 父群组ID
     */
    private Long realPid;
    /**
     * 群主名称
     */
    private String groupHeaderName;
    /**
     * 群名称
     */
    private String name;

    /**
     * 群主用户ID
     */
    private Long userId;

    /**
     * 群logo
     */
    private String logo;
    /**
     * 群是否是匿名群(normal=不是匿名群,即普通群，groupTask=任务群)
     */
    private String groupType;
    /**
     * 进群是否需要确认（0,false=不需要 true,1=需要）
     */
    private Boolean needConfirm;
    /**
     * 群成员
     */
    private List<SohuImGroupUserBo> groupUsers;

    /**
     * 是否发送建群通知
     */
    private Boolean sendFirstGroupMsg;

    /**
     * 群扩展字段
     */
    private String groupExt;

    /**
     * 唯一值，为了解决dubbo重复建群
     */
    private Long unq;

    /**
     * 群公告
     */
    private String groupNotice;

}
