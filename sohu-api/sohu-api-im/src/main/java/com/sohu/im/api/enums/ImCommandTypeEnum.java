package com.sohu.im.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 指令类型
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ImCommandTypeEnum {

    groupName("groupName", "修改群名称"),
    groupLogo("groupLogo", "修改群logo"),
    groupApprove("groupApprove", "审核开关"),
    ungroup("ungroup", "解散群组"),
    createSubgroup("createSubgroup", "创建子群"),
    recall("recall", "撤回"),
    systemRecall("systemRecall", "系统撤回"),
    deleteChatMessage("deleteChatMessage", "消息删除"),
    createGroup("createGroup", "创建群聊(非子群)"),
    groupMembers("groupMembers", "群成员发生变化"),
    groupAdministrator("groupAdministrator", "群管理员发生变化"),
    groupForbid("groupForbid", " 群组禁言状态变化"),
    groupMemberForbid("groupMemberForbid", "群组禁言成员发生变化"),
    groupRoomRefresh("groupRoomRefresh", "群通话房间人数变化"),
    chatLimitErrTip("chatLimitErrTip", "聊天错误提示"),
    addFriendPass("addFriendPass", "通过好友申请"),
    groupGreet("groupGreet", "入群欢迎语"),
    merchantwelcome("merchantWelcome", "第一次和商家对话，客户主动发，商家被动发送欢迎语"),
    userInfoUpdate("userInfoUpdate", "用户信息发生变更"),
    groupAddFriend("groupAddFriend", "群添加好友限制变化"),
    groupForbidTime("groupForbidTime", " 群组分时段禁言状态变化"),
    joinGroup("joinGroup", "进群"),
    quitGroup("quitGroup", "主动退群"),
    kickGroup("kickGroup", "被动退群，即 被群主踢出群"),
    groupDisable("groupDisable", "群禁用"),
    groupTaskFinish("groupTaskFinish", "通用商单结束指令，设置群状态等于2，禁止聊天"),
    groupEnable("groupEnable", "群启用"),
    groupSessionTypeChange("groupSessionTypeChange", "群类型变换"),
    messageRiskUpdate("messageRiskUpdate", "消息风险检测结果")
    ;

    private String code;
    private String desc;
}
