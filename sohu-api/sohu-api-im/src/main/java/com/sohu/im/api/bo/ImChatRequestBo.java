package com.sohu.im.api.bo;

import com.sohu.im.api.enums.ImMessageTypeEnum;
import com.sohu.im.api.enums.ImShareTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;
import java.util.Set;

/**
 * socket消息入参
 */
@NoArgsConstructor
@Data
public class ImChatRequestBo implements Serializable {

    public static final String DEFAULT_AVATAR = "https://sohugloba.oss-cn-beijing.aliyuncs.com/app/default_avatar.png";

    /**
     * 消息唯一标识符,唯一性，可用于消息删除，撤回
     */
    private Long chatId;

    /**
     * 本地生成的唯一标识符
     */
    private String localId;

    /**
     * 接收者(群组或用户)唯一标识
     */
    private Long receiverId;

    /**
     * 店铺ID
     */
    private Long merchantId;

    /**
     * 会话类型:single - 单聊、group-群聊
     */
    private String sessionType;

    /**
     * 消息类型:
     * text - 文本、photo - 图片、video - 视频、voice - 语音、share - 分享、notice - 公告、file - 文件、command - 命令
     * voiceCall - 发起或收到语音、videoCall-发起或收到视频、voiceCallCancel - 取消或拒绝语音、videoCallCancel - 取消或拒绝视频、
     * voiceCallFinish - 结束语音、videoCallFinish - 结束视频、voiceCallBusy - 对方语音忙碌中、videoCallBusy - 对方视频忙碌
     * {@link ImMessageTypeEnum}
     */
    private String messageType;

    /**
     * 仅 messageType 为 videoCall 或 voiceCall 时，才填充该对象
     */
    private MediaCall mediaCall;


    private String type;

    /**
     * 消息内容：
     * 如果是文本消息，即输入的文本，图片消息就是图片的url,视频消息就是视频的Url、语音消息的Url
     */
    private String content;
    /**
     * 被拦截的原因，比如文字涉恐、反共、非好友、或其他规则
     */
    private String err;

    /**
     * 分享的类型
     * {@link ImShareTypeEnum}
     */
    private String shareType;

    /**
     * 分享对象的id
     */
    private Long shareId;

    /**
     * 命令数据信息(撤回、踢群、群解散、邀请人进群等等)：
     * b)inviteMembers - 邀请进群、removeMembers - 移除群成员、exitMember - 群成员主动退群、modifyName - 修改群成员、
     * modifyLogo - 修改群logo、modifyApprove - 审核开关、addAdmin - 添加管理员、removeAdmin - 移除管理员、
     * modifyForbid - 群组禁言、addForbid - 禁言群成员、removeForbid - 移除禁言成员、dissolve - 解散群组、createSubgroup - 创建子群、recall - 撤回
     */
    private String commandType;

    /**
     * 是否隐藏消息
     */
    private Boolean hidden = false;

    /**
     * 是否保存里层消息
     */
    private Boolean saveInnerMessage = true;

    /**
     * 是否自增未读消息
     */
    private Boolean addUnreadMessage = true;

    /**
     * 是否保存外层消息
     */
    private Boolean saveOuterMessage = true;
    /**
     * 极光推送用户id
     */
    private Collection<Long> jiguangUsers;

    /**
     * 语音消息或视频消息的时长
     */
    private Integer duration;

    /**
     * 分享对象-BO
     */
    private ImShareBo share;

    /**
     * 拓展字段，json格式，如：{"roomId":"116-9"}
     */
    private String ext;

    /**
     * at 群内成员的userId，英文逗号分隔，如果列表中有0即表示@所有人
     */
    private String atIds;

    /**
     * 消息发送方-不变
     */
    private Long realSenderId;
    /**
     * 消息接收方-不变
     */
    private Long realReceiverId;

    /**
     * 文件属性
     */
    private File file;

    @Schema(name = "msgSource", description = "消息来源")
    private String msgSource;

    /**
     * 消息入库纳秒时间
     */
    private Long nanoTime;


    @Data
    public static class MediaCall {
        /**
         * 房间ID，发起人id-接收人id
         */
        private String roomId;
        /**
         * 最开始的call发起人，固定不变
         */
        private Long originUserId;
        /**
         * 发起人id
         */
        private Long userId;
        /**
         * 聊天成员id集合-移动端给的
         */
        private Set<Long> members;
        /**
         * 在房间中的实际成员id集合
         */
        private Set<Long> realMembers;
        /**
         * {@link com.sohu.im.api.enums.MediaCallTypeEnum}
         */
        private String type;
        /**
         * 是否为前置摄像头
         */
        private boolean isFront;
        /**
         * 仅type 为 offer 或 answer 时填充该对象
         */

        private Object sessionDescription;
        /**
         * 仅 type 为 ice 时才填充该对象
         */
        private Object iceCandidate;
        /**
         * 是否开启视频 open、 close， type 为 video 时填充
         */
        private String video;

        /**
         * join发送人ID
         */
        private Long fromUserId;

    }

    @Data
    public static class MemberUser {
        /**
         * 用户id
         */
        private Long userId;
        /**
         * 用户昵称
         */
        private String nickName;
        /**
         * 用户头像
         */
        private String userAvatar;
    }

    @Data
    public static class File {
        /**
         * 文件大小
         */
        private long fileSize;
        /**
         * 文件名
         */
        private String fileName;
    }


}
