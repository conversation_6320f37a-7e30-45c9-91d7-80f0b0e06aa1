package com.sohu.system.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 警告消息推送对象
 *
 */
@Data
public class SohuWarnBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    @Schema(name = "userId", description = "用户id", example = "123")
    private Long userId;

    /**
     * 警告消息
     */
    @NotNull(message = "警告消息不能为空")
    @Schema(name = "warnMsg", description = "警告消息", example = "警告消息")
    private String warnMsg;

}
