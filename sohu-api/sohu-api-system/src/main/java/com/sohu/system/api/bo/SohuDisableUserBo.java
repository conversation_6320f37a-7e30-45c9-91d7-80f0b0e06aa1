package com.sohu.system.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 用户禁用对象
 */
@Data
public class SohuDisableUserBo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "operationType", description = "操作类型", example = "RESET_AVATAR--重置头像, RESET_NICKNAME--重置昵称, RESET_REMARK--重置签名, BAN_ACCOUNT--封禁账号, BAN_IP--封禁IP")
    private String operationType;

    /**
     * 用户id
     */
    @Schema(name = "userId", description = "用户id", example = "123")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    /**
     * 重置头像
     */
    @Schema(name = "avatar", description = "头像", example = "https://sohugloba.oss-cn-beijing.aliyuncs.com/app/default_avatar.png")
    private String avatar;

    /**
     * 重置昵称
     */
    @Schema(name = "nickName", description = "昵称", example = "张三")
    private String nickName;

    /**
     * 重置签名
     */
    @Schema(name = "remark", description = "签名", example = "我是一个好人")
    private String remark;

    /**
     * 禁用原因
     */
    @Schema(name = "reason", description = "禁用原因", example = "涉黄")
    private String reason;

    /**
     * 封禁类型列表 (支持多种类型同时封禁)
     */
    @Schema(name = "banTypes", description = "封禁类型列表", example = "[\"ACCOUNT\", \"IP\", \"DEVICE\"]")
    private List<String> banTypes;

    /**
     * 封禁类型 (兼容旧版本，单一类型)
     */
    @Schema(name = "type", description = "封禁类型", example = "IP-ip地址,ACCOUNT-账号,DEVICE-设备")
    private String type;

    /**
     * 封禁时长描述 (例如: 永封, 1年, 30天)
     */
    @Schema(name = "durationDescription", description = "封禁时长描述", example = "永封,1年,30天")
    private String durationDescription;

    /**
     * 封禁时间
     */
    @Schema(name = "disableTime", description = "封禁时间", example = "")
    private String disableTime;

    /**
     * 实际IP地址
     */
    @Schema(name = "ip", description = "实际IP地址", example = "127.0.0.1")
    private String ip;

    /**
     * 设备码
     */
    @Schema(name = "device", description = "设备码", example = "123")
    private String device;
}
