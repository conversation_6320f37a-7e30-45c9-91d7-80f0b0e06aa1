package com.sohu.system.api.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sohu.common.core.annotation.Sensitive;
import com.sohu.common.core.constant.UserConstants;
import com.sohu.common.core.enums.SensitiveStrategy;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.core.xss.Xss;
import com.sohu.system.api.vo.SysPlatformRoleVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
@BaseEntity.Cache(region = "SysUser")
public class SysUser extends BaseEntity {

    /**
     * 用户ID
     */
    @TableId(value = "user_id", type = IdType.AUTO)
    private Long userId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 用户账号
     */
    @Xss(message = "用户账号不能包含脚本字符")
    @NotBlank(message = "用户账号不能为空", groups = {AddGroup.class})
    @Size(min = 0, max = 50, message = "用户账号长度不能超过50个字符")
    private String userName;

    /**
     * 用户昵称
     */
    @Xss(message = "用户昵称不能包含脚本字符")
    @NotBlank(message = "用户昵称不能为空", groups = {AddGroup.class})
    @Size(min = 0, max = 50, message = "用户昵称长度不能超过50个字符")
    private String nickName;

    /**
     * 用户类型（SYS_PLATFORM:总后台,DISTRIBUTION_USER:分销平台,CLIENT_USER:C端）
     * {@link com.sohu.common.core.enums.UserType}
     */
    private String userType;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 用户邮箱
     */
    @Sensitive(strategy = SensitiveStrategy.EMAIL)
    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    private String email;

    /**
     * 手机号码
     */
    @Sensitive(strategy = SensitiveStrategy.PHONE)
    private String phoneNumber;

    /**
     * 用户性别
     */
    private Integer sex;

    /**
     * 用户头像
     */
    //private String avatar = Constants.DEFAULT_AVATAR;
    private String avatar;

    /**
     * 密码
     */
    @TableField(
            insertStrategy = FieldStrategy.NOT_EMPTY,
            updateStrategy = FieldStrategy.NOT_EMPTY,
            whereStrategy = FieldStrategy.NOT_EMPTY
    )
    private String password;

    @JsonIgnore
    @JsonProperty
    public String getPassword() {
        return password;
    }

    /**
     * 帐号状态（0正常 1停用）
     * {@link com.sohu.common.core.enums.UserStatus}
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 部门对象
     */
    @TableField(exist = false)
    private SysDept dept;

    /**
     * 角色对象
     */
    @TableField(exist = false)
    private List<SysRole> roles;

    /**
     * 平台角色对象
     */
    @TableField(exist = false)
    private List<SysPlatformRoleVo> platformRoles;

    /**
     * 角色组(功能组)
     */
    @TableField(exist = false)
    private Long[] roleIds;

    /**
     * 角色组（平台角色）
     */
    @TableField(exist = false)
    private Long[] platformRoleIds;

    /**
     * 岗位组
     */
    @TableField(exist = false)
    private Long[] postIds;

    /**
     * 数据权限 当前角色ID
     */
    @TableField(exist = false)
    private Long roleId;

    /**
     * 站点ID
     */
    @TableField(exist = false)
    private Long siteId;

    /**
     * 站点类型（1-站点，0-行业）
     */
    @TableField(exist = false)
    private Boolean siteCate;

    /**
     * 平台行业id
     */
    @TableField(exist = false)
    private Long platformIndustryId;

    private Long uid;

    /**
     * 支付密码
     */
    private String payPassword;

    /**
     * 申请注销理由
     */
    private String withdrawReason;

    /**
     * 申请注销图片url
     */
    private String withdrawPic;

    /**
     * 申请注销审批人ID
     */
    private String withdrawAudit;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 通用标签集合
     */
    @TableField(exist = false)
    private List<Long> commonLabelIds;

    /**
     * 行业标签集合
     */
    @TableField(exist = false)
    private List<Long> industryLabelIds;

    /**
     * 内容标签集合
     */
    @TableField(exist = false)
    private List<Long> contentTagIds;

    /**
     * 邀请人信息
     */
    private Long inviteUserId;

    /**
     * 封禁类型
     */
    private String banType;

    /**
     * 封禁状态
     */
    private String banStatus;

    /**
     * 注册开始时间
     */
    @TableField(exist = false)
    private String startTime;

    /**
     * 注册结束时间
     */
    @TableField(exist = false)
    private String endTime;

    public SysUser(Long userId) {
        this.userId = userId;
    }

    /**
     * 是否管理员
     */
    public boolean isAdmin() {
        return UserConstants.ADMIN_ID.equals(this.userId);
    }

}
