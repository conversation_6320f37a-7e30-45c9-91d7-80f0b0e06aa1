package com.sohu.system.api.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 封禁用户对象导出VO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SysBanUserExportVo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户序号")
    private Long userId;

    /**
     * 用户昵称
     */
    @ExcelProperty(value = "用户名称")
    private String nickName;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码")
    private String phoneNumber;

    /**
     * 用户性别
     */
    @ExcelProperty(value = "用户性别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_user_sex")
    private String sex;

    /**
     * 最后登录IP
     */
    @ExcelProperty(value = "最后登录IP")
    private String loginIp;

    /**
     * 头像
     */
    @ExcelProperty(value = "头像")
    private String avatar;

    /**
     * 封禁状态
     */
    @ExcelProperty(value = "封禁状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "active=封禁中,lifted=已解封")
    private String banStatus;

    /**
     * 简介
     */
    @ExcelProperty(value = "简介")
    private String remark;

    /**
     * 注册时间
     */
    @ExcelProperty(value = "注册时间")
    private Date createTime;

}
