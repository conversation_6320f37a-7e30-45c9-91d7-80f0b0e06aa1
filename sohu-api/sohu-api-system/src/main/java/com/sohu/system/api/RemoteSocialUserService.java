package com.sohu.system.api;

import com.sohu.system.api.domain.SocialUserDomain;

/**
 * 三方登录绑定关系服务
 */
public interface RemoteSocialUserService {

    SocialUserDomain getByUnionId(String unionId, String source);

    /**
     * 根据userId和第三方用户来源查询
     * @param userId
     * @param source
     * @return
     */
    SocialUserDomain getByUserId(Long userId, String source);

    void bindThirdUser(SocialUserDomain socialUserIos);
}
