<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sohu</groupId>
        <artifactId>sohu-api</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sohu-api-middle</artifactId>

    <description>
        sohu-api-middle接口模块
    </description>

    <dependencies>

        <!-- RuoYi Common Core-->
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zwztf.sdk</groupId>
            <artifactId>aliyun-airec</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-common-page</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-im</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-resource</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sohu</groupId>
            <artifactId>sohu-api-stream-rocketmq</artifactId>
        </dependency>
    </dependencies>

</project>
