package com.sohu.middle.api.vo;

import com.sohu.common.core.enums.BusyType;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: leibo
 * @Date: 2025/6/21 10:04
 **/
@Data
public class SohuCommonContentVo implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 标识是图文还是视频  article 图文   video 视频
     */
    private String type;
    /**
     * 标题
     */
    private String title;
    /**
     * 封面图
     */
    private String coverImage;

    /**
     * 视频地址
     */
    private String videoUrl;


    /**
     * 单个SohuVideoVo转换为SohuCommonContentVo
     * @param videoVo 视频对象
     * @return 通用内容对象
     */
    public SohuCommonContentVo (SohuVideoVo videoVo) {
        this.id = videoVo.getId();
        this.type = BusyType.Video.getType();
        this.title = videoVo.getTitle();
        this.coverImage = videoVo.getCoverImage();
        this.videoUrl = videoVo.getVideoUrl();
    }

    /**
     * 单个SohuArticleVo转换为SohuCommonContentVo
     * @param articleVo 图文对象
     * @return 通用内容对象
     */
    public SohuCommonContentVo (SohuArticleVo articleVo) {
        this.id = articleVo.getId();
        this.type = BusyType.Article.getType();
        this.title = articleVo.getTitle();
        this.coverImage = articleVo.getCoverImage();
    }

    /**
     * 单个SohuPlayletVo转换为SohuCommonContentVo
     * @param playletVo 短剧对象
     * @return 通用内容对象
     */
    public SohuCommonContentVo (SohuPlayletVo playletVo) {
        this.id = playletVo.getId();
        this.type = BusyType.BusyPlaylet.getType();
        this.title = playletVo.getTitle();
        this.coverImage = playletVo.getCoverImage();
    }

    public SohuCommonContentVo() {

    }

    public SohuCommonContentVo(Long id, String type, String title, String coverImage, String videoUrl) {
        this.id = id;
        this.type = type;
        this.title = title;
        this.coverImage = coverImage;
        this.videoUrl = videoUrl;
    }
}
