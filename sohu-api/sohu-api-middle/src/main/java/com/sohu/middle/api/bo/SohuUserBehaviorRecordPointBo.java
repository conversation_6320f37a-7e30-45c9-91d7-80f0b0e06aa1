package com.sohu.middle.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/31 15:08
 */
@Data
public class SohuUserBehaviorRecordPointBo implements Serializable {

    @Schema(title = "userId", description = "userId", defaultValue = "123445")
    private Long userId;

    @Schema(title = "userName", description = "用户昵称", defaultValue = "wangwei")
    private String userName;

    @Schema(title = "operaSource", description = "平台 0.其他 1.web端  2.安卓  3.IOS 4.小程序", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer operaSource;

    @Schema(title = "operUrl", description = "页面Id", defaultValue = "sd_list")
    private String operUrl;

    @Schema(title = "pageName", description = "页面名称", defaultValue = "愿望列表页")
    private String pageName;

    @Schema(title = "operIp", description = "请求IP", defaultValue = "127.0.0.1")
    private String operIp;

    @Schema(title = "requestId", description = "请求Id", defaultValue = "123456789")
    private String requestId;

    @Schema(title = "eventAttribute", description = "事件属性", defaultValue = "浏览时长")
    private EventAttribute eventAttribute;

    @Schema(title = "businessType", description = "业务类型  Article 图文  Video 视频  Question 问答 ShortPlay 短剧  BusyOrder  商单 Novel 小说  Game 游戏  Literature 诗歌散文 Ad 广告 IM 社交 FiveRegimen 五养 Shop 商城 Other 其他", defaultValue = "Article", requiredMode = Schema.RequiredMode.REQUIRED)
    private String businessType;

    @Schema(title = "operaType", description = "事件类型 1、查看列表 2、查看详情 3、点赞 4、评论 5、收藏 6、关注 7、打赏 8、新建 9、编辑  10、删除 11、下架  12、转发 13 启动 14 登录 15 其他 ", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer operaType;
    @Schema(title = "sourceType", description = "事件来源 HSS 狐少少 FOCUS 搜狐焦点海外", defaultValue = "HSS 狐少少 FOCUS 搜狐焦点海外", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sourceType;

    @Schema(title = "eventSign", description = "事件标识", defaultValue = "user_login")
    private String eventSign;

    @Schema(title = "eventName", description = "事件名称", defaultValue = "用户登录")
    private String eventName;

    @Schema(title = "elementNo", description = "元素编号", defaultValue = "new_sd")
    private String elementNo;

    @Schema(title = "elementName", description = "元素名称", defaultValue = "创建愿望")
    private String elementName;

    @Schema(title = "version", description = "应用版本", defaultValue = "1.0.0")
    private String version;

    @Schema(title = "deviceNo", description = "设备号", defaultValue = "123456789")
    private String deviceNo;

    @Schema(title = "deviceUnitNo", description = "设备型号", defaultValue = "iPhone 13 Pro Max")
    private String deviceUnitNo;

    @Schema(title = "modelNo", description = "机型号", defaultValue = "iPhone 13 Pro Max")
    private String modelNo;

    @Schema(title = "eventTime", description = "事件触发时间", defaultValue = "2023-03-31 15:08:00")
    private Date eventTime;

    @Schema(title = "operResult", description = "搜狐焦点同步用", defaultValue = "成功")
    private String operResult;

    @Data
    public static class EventAttribute implements Serializable{

        @Schema(title = "duration", description = "浏览时长", defaultValue = "10")
        private Long duration;

        @Schema(title = "contentNo", description = "内容编号", defaultValue = "123456789")
        private String contentNo;

        @Schema(title = "contentName", description = "内容名称", defaultValue = "我的愿望")
        private String contentName;

        @Schema(title = "contentTitle", description = "内容标题", defaultValue = "我的愿望")
        private String contentTitle;

        @Schema(title = "contentType", description = "内容类型", defaultValue = "1")
        private String contentType;

        @Schema(title = "contentStatus", description = "内容状态", defaultValue = "待接单")
        private String contentStatus;

        @Schema(title = "contentEpisodes", description = "集数", defaultValue = "1")
        private Integer contentEpisodes;

        @Schema(title = "orderNo", description = "订单编号", defaultValue = "123456789")
        private String orderNo;

        @Schema(title = "amount", description = "金额", defaultValue = "10.00")
        private BigDecimal amount;

        @Schema(title = "payAmount", description = "保证金金额/结算金额", defaultValue = "10.00")
        private BigDecimal payAmount;

        @Schema(title = "contentRelationNo", description = "关联内容编号", defaultValue = "123456789")
        private String contentRelationNo;

        @Schema(title = "contentReply", description = "内容反馈/回复/评论", defaultValue = "好好好")
        private String contentReply;

        @Schema(title = "contentRelationType", description = "内容关联类型", defaultValue = "举报")
        private String contentRelationType;

        @Schema(title = "contentRelationName", description = "内容关联名称", defaultValue = "举报")
        private String contentRelationName;

        @Schema(title = "contentDuration", description = "内容时长", defaultValue = "10")
        private Long contentDuration;

        @Schema(title = "progressDuration", description = "进度时长", defaultValue = "10")
        private Long progressDuration;

        @Schema(title = "role", description = "角色", defaultValue = "发单方")
        private String role;
    }
}
