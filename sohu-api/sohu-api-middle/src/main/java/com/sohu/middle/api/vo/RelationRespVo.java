package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
@Schema(description = "内容关联项分页查询响应结果 Response VO")
@Data
@ToString(callSuper = true)
public class RelationRespVo implements Serializable {

    @Schema(description = "业务类型", example = "Project", required = true)
    private String busyType;

    @Schema(description = "业务标题", example = "塞浦路斯塞岛家园")
    private String busyTitle;

    @Schema(description = "业务ID", example = "56")
    private Long busyCode;

    @Schema(description = "封面图", example = "https://sohugloba.oss-cn-beijing.aliyuncs.com/20230511/35b98ee6637be1ef9982c7b94ab778fcf9dff2ae400b0585902dbf0cde6e0a1a.png")
    private String busyCoverImg;

    @Schema(description = "创作人头像", example = "https://sohugloba.oss-cn-beijing.aliyuncs.com/20230511/35b98ee6637be1ef9982c7b94ab778fcf9dff2ae400b0585902dbf0cde6e0a1a.png")
    private String belongerAvatar;

    @Schema(description = "创作人名称", example = "13026152281")
    private String belongerName;

    @Schema(description = "创作人ID", example = "195")
    private Long busyBelonger;

    @Schema(description = "发布时间", example = "2023-5-13 11:09:52")
    private String busyPublishTime;

    @Schema(description = "关联项单位", required = true, example = "1000万每套")
    private String busyUnit;
    /**
     * 关联项内容
     */
    @Schema(description = "关联项内容", example = "盛大开业")
    private String busyContent;

    /**
     * 关联项收藏数
     */
    @Schema(description = "关联项收藏数", example = "999")
    private Integer busyCollectCount;

    /**
     * 是否收藏了关联项
     */
    @Schema(description = "是否收藏了关联项", example = "true")
    private Boolean busyCollect = false;

    /**
     * 是否点赞了关联项
     */
    @Schema(description = "是否点赞了关联项", example = "true")
    private Boolean busyLike = false;

    /**
     * 价格区间最小值
     */
    @ExcelProperty(value = "价格区间最小值")
    private BigDecimal minAmount;

    /**
     * 价格区间最大值
     */
    @ExcelProperty(value = "价格区间最大值")
    private BigDecimal maxAmount;

    /**
     * 币种名称
     */
    private String currencyName;
    /**
     * 币种id(sohu_currency表主键ID)
     */
    @ExcelProperty(value = "币种id(sohu_currency表主键ID)")
    private Long currencyId;

    /**
     * 收藏数
     */
    private Integer collectNum;
    /**
     * 项目分类id
     */
    @ExcelProperty(value = "项目分类id")
    private Long categoryId;
    /**
     * 推广信息(如商品ids|逗号隔开)
     */
    @Schema(description = "推广信息(如商品ids|逗号隔开)")
    private String busyInfo;

    /**
     * 子任务编号
     */
    public String childTaskNumber;
}
