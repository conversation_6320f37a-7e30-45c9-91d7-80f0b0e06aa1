package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 图文拓展视图对象
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@Data
@ExcelIgnoreUnannotated
public class SohuArticleInfoVo implements Serializable {

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 站点ID
     */
    @ExcelProperty(value = "站点ID")
    private Long siteId;

    /**
     * 图文ID
     */
    @ExcelProperty(value = "图文ID")
    private Long articleId;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容")
    private String content;

    /**
     * 阅读数
     */
    @ExcelProperty(value = "阅读数")
    private Integer viewCount = 0;

    /**
     * 评论数
     */
    @ExcelProperty(value = "评论数")
    private Integer commentCount = 0;

    /**
     * 点赞数
     */
    @ExcelProperty(value = "点赞数")
    private Integer praiseCount = 0;

    /**
     * 收藏数
     */
    @ExcelProperty(value = "收藏数")
    private Integer collectCount = 0;

    /**
     * 转发数
     */
    private Integer forwardCount = 0;

    /**
     * 初始学习人数
     */
    private Integer learnNum = 0;
}
