package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.web.domain.SohuBaseVo;
import com.sohu.middle.api.aspect.RiskDetectionField;
import com.sohu.middle.api.enums.DetectTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 商户-狐少少视图对象
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ExcelIgnoreUnannotated
public class SohuMerchantVo extends SohuBaseVo {

    private static final long serialVersionUID = 1L;

    /**
     * 商户ID
     */
    @ExcelProperty(value = "商户ID")
    private Long id;

    /**
     * 商户名称-唯一值
     */
    @ExcelProperty(value = "商户名称-唯一值")
    @RiskDetectionField(detectType = DetectTypeEnum.TEXT)
    private String name;

    /**
     * 商户所有者id-用户id
     */
    @ExcelProperty(value = "商户所有者id-用户id")
    private Long userId;

    /**
     * 商户分类ID
     */
    @ExcelProperty(value = "商户分类ID")
    private Long categoryId;

    /**
     * 站点id—国家站
     */
    @ExcelProperty(value = "站点id—国家站")
    private Long siteId;

    /**
     * 城市站点id
     */
    @ExcelProperty(value = "城市站点id")
    private Long citySiteId;

    /**
     * 商户类型ID
     */
    @ExcelProperty(value = "商户类型ID")
    private Long typeId;

    /**
     * 商户姓名
     */
    @ExcelProperty(value = "商户姓名")
    private String realName;

    /**
     * 商户邮箱
     */
    @ExcelProperty(value = "商户邮箱")
    private String email;

    /**
     * 商户手机号
     */
    @ExcelProperty(value = "商户手机号")
    private String phone;

    /**
     * 手续费(%)
     */
    @ExcelProperty(value = "手续费(%)")
    private BigDecimal handlingFee;

    /**
     * 商户关键字
     */
    @ExcelProperty(value = "商户关键字")
    @RiskDetectionField(detectType = DetectTypeEnum.TEXT)
    private String keywords;

    /**
     * 商户地址
     */
    @ExcelProperty(value = "商户地址")
    @RiskDetectionField(detectType = DetectTypeEnum.TEXT)
    private String address;

    /**
     * 是否自营：1-自营，0-非自营
     */
    @ExcelProperty(value = "是否自营：1-自营，0-非自营")
    private Boolean isSelf;

    /**
     * 是否推荐:0-不推荐，1-推荐
     */
    @ExcelProperty(value = "是否推荐:0-不推荐，1-推荐")
    private Boolean isRecommend;

    /**
     * 商户开关:0-关闭，1-开启
     */
    @ExcelProperty(value = "商户开关:0-关闭，1-开启")
    private Boolean isSwitch;

    /**
     * 审核状态：WaitApprove-待审核，Pass-审核成功，Refuse-审核拒绝 AuthFail 认证未过 CloseApprove-闭店待审核 CloseRefuse-闭店审核拒绝  ClosePass-闭店审核通过
     * {@link CommonState}
     */
    @ExcelProperty(value = "审核状态：WaitApprove-待审核，Pass-审核成功，Refuse-审核拒绝 AuthFail 认证未过 CloseApprove-闭店待审核 CloseRefuse-闭店审核拒绝  ClosePass-闭店审核通过")
    private String auditStatus;

    /**
     * 拒绝原因
     */
    @ExcelProperty(value = "拒绝原因")
    private String denialReason;

    /**
     * 审核员ID
     */
    @ExcelProperty(value = "审核员ID")
    private Long auditorId;

    /**
     * 商品审核开关:0-关闭，1-开启
     */
    @ExcelProperty(value = "商品审核开关:0-关闭，1-开启")
    private Boolean productSwitch;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @RiskDetectionField(detectType = DetectTypeEnum.TEXT)
    private String remark;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private String sort;

    /**
     * 资质图片
     */
    @ExcelProperty(value = "资质图片")
    private String qualificationPicture;

    /**
     * 商户背景图
     */
    @ExcelProperty(value = "商户背景图")
    @RiskDetectionField(detectType = DetectTypeEnum.IMAGE)
    private String backImage;

    /**
     * 商户头像
     */
    @ExcelProperty(value = "商户头像")
    @RiskDetectionField(detectType = DetectTypeEnum.IMAGE)
    private String avatar;

    /**
     * 商户街背景图
     */
    @ExcelProperty(value = "商户街背景图")
    @RiskDetectionField(detectType = DetectTypeEnum.IMAGE)
    private String streetBackImage;

    /**
     * 商户简介
     */
    @ExcelProperty(value = "商户简介")
    private String intro;

    /**
     * 商户创建类型：admin-管理员创建，apply-商户入驻申请
     */
    @ExcelProperty(value = "商户创建类型：admin-管理员创建，apply-商户入驻申请")
    private String createType;

    /**
     * pcBanner
     */
    @ExcelProperty(value = "pcBanner")
    @RiskDetectionField(detectType = DetectTypeEnum.IMAGE)
    private String pcBanner;

    /**
     * pc背景图
     */
    @ExcelProperty(value = "pc背景图")
    @RiskDetectionField(detectType = DetectTypeEnum.IMAGE)
    private String pcBackImage;

    /**
     * 复制商品数量
     */
    @ExcelProperty(value = "复制商品数量")
    private Long copyProductNum;

    /**
     * 商户余额
     */
    @ExcelProperty(value = "商户余额")
    private BigDecimal balance;

    /**
     * 商户星级1-5
     */
    @ExcelProperty(value = "商户星级1-5")
    private Integer starLevel;

    /**
     * 是否删除
     */
    @ExcelProperty(value = "是否删除")
    private Boolean isDel;

    /**
     * 商户分类
     */
    private String merCategory;

    /**
     * 商户类型
     */
    private String merType;

    /**
     * 商户信息Vo
     */
    private SohuMerchantInfoVo infoVo;

    /**
     * 城市站名称
     */
    private String citySiteName;

    /**
     * 店铺销量
     */
    private Long saleNums;

    /**
     * 国家区号
     */
    private String countryCode;

    /**
     * 国家名称
     */
    private String countryName;

    @Schema(name = "merchantType", description = "商户类型：personal-个人，business-企业", example = "personal")
    private String merchantType;

    /**
     * 闭店状态：CloseApprove-闭店待审核 CloseRefuse-闭店审核拒绝  ClosePass-闭店审核通过
     */
    private String closeStatus;

}
