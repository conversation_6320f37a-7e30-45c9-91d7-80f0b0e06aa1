package com.sohu.middle.api.aspect;

import com.sohu.middle.api.enums.DetectTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface RiskDetectionField {

    DetectTypeEnum detectType();
}