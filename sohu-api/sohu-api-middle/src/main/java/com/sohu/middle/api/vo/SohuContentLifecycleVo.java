package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 内容作品生命周期视图对象
 *
 * <AUTHOR>
 * @date 2024-11-29
 */
@Data
@ExcelIgnoreUnannotated
public class SohuContentLifecycleVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 对象id
     */
    @ExcelProperty(value = "对象id")
    private Long busyCode;

    /**
     * 业务类型
     */
    @ExcelProperty(value = "业务类型")
    private String busyType;

    /**
     * 上一步状态
     */
    @ExcelProperty(value = "上一步状态")
    private String lastState;

    /**
     * 当前状态
     */
    @ExcelProperty(value = "当前状态")
    private String currentState;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
