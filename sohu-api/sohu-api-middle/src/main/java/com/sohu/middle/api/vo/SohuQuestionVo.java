package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.web.domain.SohuBaseVo;
import com.sohu.middle.api.aspect.RiskDetectionField;
import com.sohu.middle.api.enums.DetectTypeEnum;
import com.sohu.third.aliyun.airec.domain.vo.ISohuAiRecResVo;
import com.sohu.third.aliyun.airec.domain.vo.SohuAiRecResultItemVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;


/**
 * 问题主体视图对象
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ExcelIgnoreUnannotated
public class SohuQuestionVo extends SohuBaseVo implements Serializable, ISohuAiRecResVo {

    private static final long serialVersionUID = 1L;

    /**
     * 问题实体表主键
     */
    @ExcelProperty(value = "问题实体表主键")
    private Long id;

    /**
     * 站点id
     */
    @ExcelProperty(value = "站点id")
    private Long siteId;

    /**
     * 分类id
     */
    @ExcelProperty(value = "分类id")
    private Long categoryId;

    /**
     * 分类名称
     */
    @ExcelProperty(value = "分类名称")
    private String categoryName;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    @RiskDetectionField(detectType = DetectTypeEnum.TEXT)
    private String title;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容")
    @RiskDetectionField(detectType = DetectTypeEnum.RICH_TEXT)
    private String content;

    /**
     * 内容文字
     */
    @ExcelProperty(value = "内容文字")
    private String contentText;

    /**
     * 作者id
     */
    @ExcelProperty(value = "作者id")
    private Long userId;

    /**
     * {@link com.sohu.common.core.enums.CommonState}
     * 状态;Delete:删除，Edit:草稿，WaitApprove:待审核，OnShelf:上架，OffShelf:下架，CompelOff:强制下架，Private:仅自己可见，Refuse:已拒绝
     */
    @ExcelProperty(value = "状态;Delete:删除，Edit:草稿，WaitApprove：待审核,OnShelf：上架，OffShelf：下架，CompelOff：强制下架,Refuse：已拒绝")
    private String state;

    /**
     * 问题状态
     * {@link com.sohu.common.core.enums.CommonState}
     * 状态;Delete:删除，Edit:草稿，WaitApprove:待审核，OnShelf:上架，OffShelf:下架，CompelOff:强制下架，Private:仅自己可见，Refuse:已拒绝
     */
    @ExcelProperty(value = "状态;Delete:删除，Edit:草稿，WaitApprove：待审核,OnShelf：上架，OffShelf：下架，CompelOff：强制下架,Refuse：已拒绝")
    private String questionState;

    /**
     * 驳回理由
     */
    private String rejectReason;

    /**
     * 关联项
     */
    private RelationRespVo relation;

    /**
     * 创建人名称
     */
    private String authorName;
    /**
     * 创建人头像
     */
    private String authorAvatar;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 拓展数据
     */
    private SohuQoraInfoVo info;

    /**
     * 最高回答
     */
    private SohuQuestionAnswerVo questionAnswerVo;

    /**
     * 总回答数
     */
    private Long answerCount;

    /**
     * 总点赞数
     */
    private Long totalPrise;

    /**
     * 总浏览数
     */
    private Long viewCount;

    /**
     * 同步到草稿箱（蚁小二）：Y-是 ；N-否
     */
    private String syncDraft;

    /**
     * 子图
     */
    @ExcelProperty(value = "子图")
    private String images;

    /**
     * 作品类型（普通视频-general，狐少少课堂-lesson）
     * {@link com.sohu.common.core.enums.VideoEnum}
     */
    private String type;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Long sortIndex;

    /**
     * 学习人数
     */
    private Integer learnNum = 0;

//    /**
//     * 智能推荐返回结果详情(包含TraceId，TraceInfo)
//     */
//    private RecommendResponse.ResultItem resultItem;

    /**
     * 狐少少课堂标签id
     */
    private Long lessonLabelId;

    /**
     * 封面图
     */
    private String coverImage;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 智能推荐返回结果详情(包含TraceId，TraceInfo)
     */
    private SohuAiRecResultItemVo aiResultItem;

    @Override
    public String getAiItemId() {
        return this.id == null ? null : String.valueOf(this.id);
    }

    /**
     * 作品可见类型(仅自己-only,公开-open)
     */
    private String visibleType;

    /**
     * 作品是否可分享:默认是true
     */
    private Boolean isShare = true;

    /**
     * 作品是否可下载:默认是true
     */
    private Boolean isDownload = true;


    /**
     * 提交次数
     */
    private Integer submitNum;

    /**
     * 提交场景
     */
    private String submitScene;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 是否已申述
     */
    private Boolean appealStatus;

    /**
     * 申述原因
     */
    private String appealReason;

    /**
     * 作品状态
     */
    @Schema(description = "作品状态", example = "WaitApprove-审核中,OnShelf-已通过, Refuse-未通过")
    private String contentState;

    /**
     * 审核状态（WaitApprove：审核中,Pass：已通过，Refuse：未通过）
     * {@link com.sohu.common.core.enums.CommonState}
     */
    private String auditState;

    /**
     * 站点名称
     */
    @Schema(description = "站点名称", example = "武汉市")
    private String siteName;

    /**
     * 删除时间
     */
    private Date delTime;

    /**
     * 作品类型(用于预览)
     */
    private String busyType;

    /**
     * 数据来源(thirdparty:第三方)
     */
    private String dataSource;

    /**
     * 移出时间
     */
    private Date removeTime;
}
