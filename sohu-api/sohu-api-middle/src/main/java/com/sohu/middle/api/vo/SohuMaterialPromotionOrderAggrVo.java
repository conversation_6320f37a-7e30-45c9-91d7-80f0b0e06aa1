package com.sohu.middle.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/25 09:34
 **/
@Data
public class SohuMaterialPromotionOrderAggrVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单数量
     */
    private Long orderCount = 0L;

    /**
     * 订单总金额
     */
    private BigDecimal orderAmount = BigDecimal.ZERO;

    /**
     * 分销总金额
     */
    private BigDecimal independentAmount = BigDecimal.ZERO;
}
