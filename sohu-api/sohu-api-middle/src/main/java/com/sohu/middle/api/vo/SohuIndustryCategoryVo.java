package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;


/**
 * 行业分类视图对象
 *
 * <AUTHOR>
 * @date 2023-06-29
 */
@Data
@ExcelIgnoreUnannotated
public class SohuIndustryCategoryVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 上级行业ID
     */
    @ExcelProperty(value = "上级行业ID")
    private Long pid;

    /**
     * 行业名称
     */
    @ExcelProperty(value = "行业名称")
    private String industryName;
    /**
     * 站点ID(作废)
     */
    @Deprecated
    private Long siteId;

    /**
     * 行业标识(作废)
     */
    @Deprecated
    private String ident;

    /**
     * 内容分类表(sohu_category)主键ID(作废)
     */
    @Deprecated
    private Long categoryId;

    /**
     * 入驻资料（新增二级行业时传入）
     */
    private String entryProfile;

    /**
     * 需要认证资质：0-关闭，1-开启
     */
    private Boolean needProve;

    private List<SohuIndustryCategoryVo> children;

    /**
     * 认证后授予的角色，功能角色key集合，英文逗号分开
     */
    @Schema(title = "功能角色key集合", description = "认证后授予的角色，功能角色key集合，英文逗号分开", example = "article,common")
    private String roleKeysStr;

}
