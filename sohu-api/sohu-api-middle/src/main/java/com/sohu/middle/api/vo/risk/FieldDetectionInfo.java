package com.sohu.middle.api.vo.risk;

import com.sohu.middle.api.enums.DetectTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 字段检测
 *
 * @Author: leibo
 * @Date: 2025/6/17 12:18
 **/
@Data
@AllArgsConstructor
public class FieldDetectionInfo {

    /**
     * 字段对象
     */
    private String fieldCode;
    /**
     * 字段内容
     */
    private String fieldValue;
    /**
     * 检测类型
     */
    private Integer detectType;
    /**
     * 业务类型
     */
    private String busyType;
}
