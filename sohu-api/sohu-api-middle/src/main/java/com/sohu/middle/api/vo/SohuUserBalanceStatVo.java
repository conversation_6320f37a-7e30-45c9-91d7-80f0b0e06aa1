package com.sohu.middle.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
public class SohuUserBalanceStatVo implements Serializable {


    @Schema(description = "累计收入", required = true, example = "99")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal balance = BigDecimal.ZERO;

    @Schema(description = "可提现余额", required = true, example = "20")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal walletBalance = BigDecimal.ZERO;

    @Schema(description = "今日收益", required = true, example = "55")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal todayIncome = BigDecimal.ZERO;

    @Schema(description = "昨日收益", required = true, example = "55")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal yesterdayIncome = BigDecimal.ZERO;

    @Schema(description = "昨日提现", required = true, example = "55")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal yesterdayWithdrawal = BigDecimal.ZERO;


    @Schema(description = "累计预付款", required = true, example = "55")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal preAmount = BigDecimal.ZERO;

    @Schema(description = "对比昨日收益", required = true, example = "55")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal compareIncome = BigDecimal.ZERO;

}
