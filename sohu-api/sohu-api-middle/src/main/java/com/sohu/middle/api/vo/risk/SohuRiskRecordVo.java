package com.sohu.middle.api.vo.risk;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 风控记录视图对象
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
public class SohuRiskRecordVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @Schema(name = "busyType", description = "业务分类 Article-图文  Video-视频  Question-问答 ShortPlay 短剧  BusyOrder-商单 Goods-商品 Novel-小说  Game-游戏 Literature-诗歌散文", example = "Article")
    private String busyType;


    @Schema(name = "busyCode", description = "业务ID", example = "1")
    private String busyCode;
}
