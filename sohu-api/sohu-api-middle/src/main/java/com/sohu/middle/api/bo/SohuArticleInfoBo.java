package com.sohu.middle.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.core.web.domain.SohuBaseBo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 图文拓展业务对象
 *
 * <AUTHOR>
 * @date 2023-06-21
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuArticleInfoBo extends SohuBaseBo {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 站点ID
     */
    @NotNull(message = "站点ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long siteId;

    /**
     * 图文ID
     */
    @NotNull(message = "图文ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long articleId;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;

    /**
     * 阅读数
     */
    private Integer viewCount;
    /**
     * 评论数
     */
    private Integer commentCount;
    /**
     * 点赞数
     */
    private Integer praiseCount;
    /**
     * 收藏数
     */
    private Integer collectCount;
    /**
     * 转发数
     */
    private Integer forwardCount;

    private List<Long> articleIds;

    /**
     * 初始学习人数
     */
    private Integer learnNum;

    /**
     * 子任务订单
     */
    private String childTaskNum;
}
