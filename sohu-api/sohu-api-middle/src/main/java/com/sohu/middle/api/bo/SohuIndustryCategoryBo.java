package com.sohu.middle.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 行业分类业务对象
 *
 * <AUTHOR>
 * @date 2023-06-29
 */

@Data
//@EqualsAndHashCode(callSuper = true)
public class SohuIndustryCategoryBo implements Serializable {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 上级行业ID
     */
    @NotNull(message = "上级行业ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long pid;

    /**
     * 行业名称
     */
    @Size(min = 1, max = 10, message = "行业名称不能超过10个字", groups = {AddGroup.class, EditGroup.class})
    @NotBlank(message = "行业名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String industryName;
    /**
     * 站点ID(作废)
     */
    @Deprecated
    private Long siteId;
    /**
     * 行业标识(作废)
     */
    @Deprecated
    private String ident;


    /**
     * 内容分类表(sohu_category)主键ID(作废)
     */
    @Deprecated
    private Long categoryId;

    /**
     * 入驻资料（新增二级行业时传入）
     */
    private String entryProfile;

    /**
     * 是否必填
     */
//    @NotNull(message = "是否必填（0：否，1：是）",groups = {AddGroup.class, EditGroup.class})
//    private String state;

    /**
     * 需要认证资质：0-关闭，1-开启
     */
    private Boolean needProve;

    /**
     * 认证后授予的角色，功能角色key集合，英文逗号分开
     */
    @Schema(title = "功能角色key集合", description = "认证后授予的角色，功能角色key集合，英文逗号分开", example = "article,common")
    //@NotBlank(message = "功能角色key集合不能为空")
    private String roleKeysStr;

}
