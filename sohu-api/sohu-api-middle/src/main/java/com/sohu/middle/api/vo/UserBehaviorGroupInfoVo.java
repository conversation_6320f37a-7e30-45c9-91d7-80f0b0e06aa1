package com.sohu.middle.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/25 10:20
 */
@Data
public class UserBehaviorGroupInfoVo implements Serializable {

    private static final long serialVersionUID = -1L;

    @Schema(name = "userId", description = "用户id", defaultValue = "1")
    private Long userId;

    @Schema(name = "business_type", description = "业务类型  Article 图文  Video 视频  Question 问答 shortPlay 短剧  busyOrder  商单 Novel 小说  game 游戏  literature 诗歌散文", defaultValue = "Article")
    private String businessType;

    @Schema(name = "viewCount", description = "浏览次数", defaultValue = "1")
    private Integer viewCount;
}
