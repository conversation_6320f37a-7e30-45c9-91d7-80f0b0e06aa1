package com.sohu.middle.api.bo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.annotation.SohuFieldChange;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuBaseBo;
import com.sohu.middle.api.bo.mcn.SohuArticleDetailAddBo;
import com.sohu.middle.api.enums.McnMediaContentTypeEnum;
import com.sohu.middle.api.enums.McnPublishMainStatusEnum;
import com.sohu.third.aliyun.airec.domain.bo.ISohuAiRecReqBo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 视频业务对象
 *
 * <AUTHOR>
 * @date 2023-06-21
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuVideoBo extends SohuBaseBo implements ISohuAiRecReqBo {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 站点ID
     */
    @NotNull(message = "站点ID不能为空", groups = {AddGroup.class, EditGroup.class})
    @ExcelProperty(value = "站点")
    private Long siteId;

    /**
     * 作者id
     */
    private Long userId;

    /**
     * 作者id集合 关注列表使用
     */
    private List<Long> userIds;

    /**
     * 分类ID
     */
    @SohuFieldChange
    @NotNull(message = "分类ID不能为空", groups = {AddGroup.class, EditGroup.class})
    @ExcelProperty(value = "分类id")
    private Long categoryId;

    /**
     * 视频标题
     */
    @SohuFieldChange
    @NotBlank(message = "视频标题不能为空", groups = {AddGroup.class, EditGroup.class})
    @ExcelProperty(value = "作品标题")
    private String title;

    /**
     * 封面图
     */
    @ExcelProperty(value = "封面地址")
    private String coverImage;

    /**
     * 视频
     */
    @SohuFieldChange
    @NotBlank(message = "视频不能为空", groups = {AddGroup.class, EditGroup.class})
    @ExcelProperty(value = "视频地址")
    private String videoUrl;

    /**
     * 排序
     */
    private Long sortIndex;

    /**
     * 是否显示关联
     */
    private Boolean isRelate;

    /**
     * 是否需要付费 0不需要、1需要付费
     */
    private Boolean isPay;

    /**
     * 当前集数是否需要付费 0不需要、1需要付费
     */
    private Boolean episodePay;

    /**
     * 集数 默认0、短剧要大于0
     */
    private Integer episodeNumber;

    /**
     * 总集数
     */
    private Integer episodeCount;

    /**
     * 剧集关联
     */
    private String episodeRelevance;

    /**
     * {@link com.sohu.common.core.enums.CommonState}
     * 状态;Delete:删除，Edit:草稿，WaitApprove:待审核，OnShelf:上架，OffShelf:下架，CompelOff:强制下架，Private:仅自己可见，Refuse:已拒绝，Hide:隐藏
     */
    private String state;

    /**
     * 状态列表
     */
    private List<String> stateList;

    /**
     * 话题名称集合
     */
    @Schema(description = "话题名称集合,最多关联5个话题")
    private List<String> tagNameList;

    /**
     * 关联项 dict::问答关联类型:[User:用户,Project:项目,Goods:商品,BusyOrder:商单,Shop:店铺,Address:地址]
     */
    @Schema(description = "关联项 dict::问答关联类型:[User:用户,Project:项目,Goods:商品,BusyOrder:商单,Shop:店铺,Address:地址,Window:我的橱窗,GoodsWindow:橱窗商品]", example = "Project")
    private String relateType;

    /**
     * 关联项 业务id
     */
    @Schema(description = "关联项 业务id")
    private Long busyCode;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 驳回理由
     */
    private String rejectReason;
    /**
     * 视频内容
     */
    //@NotBlank(message = "内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;

    /**
     * 不存在id
     */
    private Set<Long> notIds;

    /**
     * 不存在分类id
     */
    private Set<Long> notCategoryIds;

    /**
     * 不存在用户id
     */
    private Set<Long> notUserIds;

    /**
     * 存在id
     */
    private List<Long> ids;
    /**
     * 同步到草稿箱（蚁小二）：Y-是 ；N-否
     */
    @Schema(description = "同步到草稿箱（蚁小二）：Y-是 ；N-否")
    private String syncDraft;

    /**
     * 内容类型（蚁小二）:[Video-横版视频;MiniVideo-竖版视频;MicroArticle-短内容]
     * {@link McnMediaContentTypeEnum}
     */
    @Schema(description = "内容类型（蚁小二）:[Video-横版视频;MiniVideo-竖版视频;MicroArticle-短内容]")
    private String mediaContentType;

    /**
     * 发布平台详情集合（蚁小二）
     */
    @Schema(description = "发布平台详情集合（蚁小二）")
    private List<SohuArticleDetailAddBo> details;

    // 在 BO 类中添加 addToNotIds 方法
    public void addToNotIds(Set<Long> cacheListIds) {
        if (notIds == null) {
            notIds = new HashSet<>();
        }
        if (CollUtil.isNotEmpty(cacheListIds)) {
            notIds.addAll(cacheListIds);
        }
    }

    /**
     * 关联MCN(用户ID)
     */
    private Long mcnUserId;

    /**
     * 当前用户id
     */
    private Long currentUserId;

    /**
     * 开始时间
     */
    private DateTime startDate;

    /**
     * 结束时间
     */
    private DateTime endDate;

    /**
     * startTime 字符串转startDate
     *
     * @return
     */
    public DateTime getStartDate() {
        if (StrUtil.isNotBlank(this.startTime) && this.startDate == null) {
            this.startDate = DateUtil.beginOfDay(DateUtil.parseDate(this.startTime));
        }
        return this.startDate;
    }

    /**
     * endTime 字符串转endDate
     *
     * @return
     */
    public DateTime getEndDate() {
        if (StrUtil.isNotBlank(this.endTime) && this.endDate == null) {
            this.endDate = DateUtil.endOfDay(DateUtil.parseDate(this.endTime));
        }
        return this.endDate;
    }

    /**
     * 推广标题
     */
    @Schema(description = "推广标题")
    private String busyTitle;

    /**
     * 推广信息(如商品ids|逗号隔开)
     */
    @Schema(description = "推广信息(如商品ids|逗号隔开)")
    private String busyInfo;

    /**
     * 剧集
     */
    private SohuPlayletBo playLetBo;


    /**
     * 初始学习人数
     */
    @Schema(description = "初始学习人数")
    private Integer learnNum;

    /**
     * 作品类型（普通视频-general，狐少少课堂-lesson）,默认为general
     * {@link com.sohu.common.core.enums.VideoEnum}
     */
    @ExcelProperty(value = "作品类型")
    private String type;

    /**
     * 简介
     */
    private String intro;

//    /**
//     * 是否智能推荐
//     */
//    private Boolean aiRecommend = false;
//
//    /**
//     * 未登录用户
//     * 安卓：MD5（imei），IOS：MD5（idfa）
//     */
//    private String imei;

    /**
     * 登录用户
     */
    private String loginUser;

    /**
     * 发布之前的唯一标识
     */
    private String publishMediaId;

    /**
     * 狐少少课堂标签id
     */
    private Long lessonLabelId;

    /**
     * 是否智能推荐(支持智能推荐)
     */
    private Boolean aiRec = false;


    /**
     * 是否是推荐页
     */
    private Boolean recommend;

    /**
     * 用户设备id，安卓设备是（imei），IOS设备是（idfa）
     * (支持智能推荐)(已登录的用户可不填，未登录用户必填)
     */
    private String aiRecImei;

    /**
     * 需要推荐的目标用户,和imei至少一个不为空
     *
     * @return
     */
    @Schema(hidden = true)
    private String aiUserId;

    /**
     * 场景id
     * （智能推荐必传）
     */
    private String aiRecSceneId;

    /**
     * 智能推荐，单次请求返回的推荐结果数量，建议取值20,最大值50
     */
    @Schema(hidden = true)
    private Integer aiReturnCount;

    /**
     * 是否临时启用
     */
    private Boolean isEnable = false;

    /**
     * 作品可见类型(仅自己-only,公开-open)
     */
    private String visibleType;

    /**
     * 作品是否可分享:默认是true
     */
    private Boolean isShare = true;

    /**
     * 作品是否可下载:默认是true
     */
    private Boolean isDownload = true;
    /**
     * 发布状态（蚁小二）[PushIng:同步中,AllSucceed:全部发布成功,PartSucceed:部分发布成功,Fail:全部发布失败]{@link McnPublishMainStatusEnum}
     */
    private String publishStatus;

    /**
     * 国家站点ID
     */
    private Long countrySiteId;
    /**
     * 系统来源(sohuglobal:狐少少,minglereels:海外短剧),用来区分是哪个系统
     */
    private String sysSource;

    /**
     * 站点ID集合
     */
    @Schema(name = "siteIds", description = "站点ID集合", example = "1,2")
    private List<Long> siteIds;

    /**
     * 作品状态
     * {@link com.sohu.common.core.enums.CommonState}
     */
    @Schema(name = "contentState", description = "作品状态", example = "WaitApprove-发布中,OnShelf-已发布,OffShelf-已下架(自主),CompelOff-已下架(强制),Delete-已删除(自主),ForceDelete-已删除(强制),Refuse-发布失败")
    private String contentState;

    /**
     * 分类ID集合
     */
    @Schema(name = "categoryIds", description = "分类ID集合", example = "116,111")
    private List<Long> categoryIds;

    /**
     * 是否已申述
     */
    @Schema(description = "是否已申述", example = "true")
    private Boolean appealStatus;

    public SohuVideoBo() {
        this.sohuBusyType = BusyType.Video;
    }

    /**
     * 删除时间
     */
    private Date delTime;

    /**
     * 商单编号
     */
    private String taskNumber;

    /**
     * 用户昵称
     */
    private String nickName;
    /**
     * 宽高比
     */
    private Integer aspectRatio;

    /**
     * 是否查询黑名单
     */
    private Boolean isBlack = Boolean.FALSE;

    /**
     * 平台行业id
     */
    private Long industryId;

    /**
     * 需要处理的id集合
     */
    private List<Long> handleIds;
}
