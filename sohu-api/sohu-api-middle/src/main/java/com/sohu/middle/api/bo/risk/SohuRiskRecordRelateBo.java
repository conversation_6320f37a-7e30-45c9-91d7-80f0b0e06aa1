package com.sohu.middle.api.bo.risk;

import com.sohu.common.core.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 风控记录检测业务对象
 *
 * <AUTHOR>
 * @date 2025-06-16
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuRiskRecordRelateBo extends BaseEntity {

    @Schema(name = "id", description = "ID", example = "1")
    private Long id;

    @Schema(name = "riskRecordId", description = "记录ID", example = "1")
    private Long riskRecordId;

    @Schema(name = "riskConfigId", description = "风控配置ID", example = "1")
    private Long riskConfigId;

    @Schema(name = "content", description = "内容(拆分)", example = "1")
    private String content;

    @Schema(name = "dataId", description = "数据请求唯一标识", example = "1")
    private String dataId;

    @Schema(name = "status", description = "0：通过，1：嫌疑，2：不通过 3 审核中", example = "1")
    private Integer status;

    @Schema(name = "taskId", description = "风控唯一标识,根据该标识查询数据最新结果", example = "1")
    private String taskId;

    @Schema(name = "riskDescription", description = "风控描述", example = "1")
    private String riskDescription;


}
