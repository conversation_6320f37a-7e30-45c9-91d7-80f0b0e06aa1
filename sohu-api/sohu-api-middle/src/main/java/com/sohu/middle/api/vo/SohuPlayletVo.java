package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.web.domain.SohuBaseVo;
import com.sohu.middle.api.aspect.RiskDetectionField;
import com.sohu.middle.api.enums.DetectTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 短剧首页分类视图对象
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ExcelIgnoreUnannotated
public class SohuPlayletVo extends SohuBaseVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 站点ID
     */
    @ExcelProperty(value = "站点ID")
    private Long siteId;

    /**
     * 作者id
     */
    @ExcelProperty(value = "作者id")
    private Long userId;

    /**
     * 视频标题
     */
    @ExcelProperty(value = "视频标题")
    @RiskDetectionField(detectType = DetectTypeEnum.TEXT)
    private String title;

    /**
     * 分类ID
     */
    @ExcelProperty(value = "分类ID")
    private Long categoryId;

    /**
     * 封面图
     */
    @ExcelProperty(value = "封面图")
    @RiskDetectionField(detectType = DetectTypeEnum.IMAGE)
    private String coverImage;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sortIndex;

    /**
     * 播放量
     */
    @ExcelProperty(value = "播放量")
    private Integer viewCount;

    /**
     * 状态;Delete:删除，Edit:草稿，WaitApprove：待审核,OnShelf：上架，OffShelf：下架，CompelOff：强制下架，Private:仅自己可见
     */
    @ExcelProperty(value = "状态;Delete:删除，Edit:草稿，WaitApprove：待审核,OnShelf：上架，OffShelf：下架，CompelOff：强制下架，Private:仅自己可见")
    private String state;

    /**
     * 拒绝理由
     */
    @ExcelProperty(value = "拒绝理由")
    private String rejectReason;

    /**
     * 是否需要付费 0不需要、1需要付费
     */
    @ExcelProperty(value = "是否需要付费 0不需要、1需要付费")
    private Boolean isPay;

    /**
     * 集数 默认0、短剧要大于0
     */
    @ExcelProperty(value = "集数 默认0、短剧要大于0")
    private Integer episodeNumber;

    /**
     * 总共集数
     */
    @ExcelProperty(value = "总共集数")
    private Integer episodeCount;

    /**
     * 剧集关联
     */
    @ExcelProperty(value = "剧集关联")
    private String episodeRelevance;

    /**
     * 短剧总简介
     */
    @ExcelProperty(value = "短剧总简介")
    @RiskDetectionField(detectType = DetectTypeEnum.TEXT)
    private String intro;


    /**
     * 视频列表
     */
    @ExcelProperty(value = "视频列表")
    private List<SohuVideoVo> sohuVideoVos;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 备案许可证
     */
    private String permitImage;

    /**
     * 解锁剩余全部剧集 0 关闭 1打开
     */
    private Boolean isPayLeave;

    /**
     * 单集价格
     */
    private BigDecimal singlePrice;

    /**
     * 解锁整部剧价格
     */
    private BigDecimal playletPrice;

    /**
     * 解锁剩余全部剧集折扣
     */
    private BigDecimal discount;

    /**
     * 是否已经购买了所有集
     */
    private Boolean allPay;

    /**
     * 分销配置
     */
    private Boolean isIndependent;

    /**
     * 分销比例
     */
    private BigDecimal distributorRatio;

    /**
     * 短剧类型 0-短剧 1-推荐
     */
    private Integer isRecommend;

    /**
     * 短剧分类名称
     */
    private String categoryName;

    /**
     * 分销金额
     */
    private BigDecimal independentPrice;

    /**
     * 素材id
     */
    private Long materialId;

    /**
     * 是否是平台
     */
    private Boolean isPlatform;

    /**
     * 海报图
     */
    private String posterImage;

    /**
     * 短剧小总结
     */
    private String summary;

    /**
     * 视频展示样式 0-横版 1-竖版
     */
    private Integer videoStyle;

    /**
     * 默认语言 Chinese-中文、English-英文
     */
    private String language;

    /**
     * 显示开始时间
     */
    private Date startTime;

    /**
     * 显示结束时间
     */
    private Date overTime;

    /**
     * 开始收费集数
     */
    private Integer isPayNumber;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 点赞数
     */
    private Integer praiseCount;

    /**
     * 收藏数
     */
    private Integer collectCount;

    /**
     * 发布人
     */
    private String userName;

    /**
     * 充值付费分销比例
     */
    private BigDecimal rechargeRatio;
    /**
     * 系统来源(sohuglobal:狐少少,minglereels:海外短剧),用来区分是哪个系统
     */
    private String sysSource;
    /**
     * 短剧拓展信息
     */
    private SohuPlayletInfoVo sohuPlayletInfoVo;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户头像
     */
    private String userAvatar;

}
