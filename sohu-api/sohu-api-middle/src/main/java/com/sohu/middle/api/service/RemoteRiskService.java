package com.sohu.middle.api.service;

import com.sohu.common.core.enums.BusyType;
import com.sohu.middle.api.bo.risk.RiskCheckBo;
import com.sohu.middle.api.bo.risk.RiskSyncCheckBo;

/**
 * <AUTHOR>
 * @date 2025/6/16 14:24
 */
public interface RemoteRiskService {

    /**
     * 同步风控校验处理
     *
     * @param riskSyncCheckBo
     * @return
     */
    Boolean syncCheck(RiskSyncCheckBo riskSyncCheckBo);

    /**
     * 异步风控校验处理
     *
     * @param riskSyncCheckBo
     * @return
     */
    void asyncCheck(RiskSyncCheckBo riskSyncCheckBo);

    /**
     * 风控校验处理
     *
     * @param busyType
     * @param busyCode
     * @return
     */
    Boolean handleRisk(BusyType busyType, String busyCode);

    /**
     * 处理风控校验结果
     *
     * @param recordId
     */
    void handleRiskResult(Long recordId);

    /**
     * 处理风控回调
     *
     * @param dataId
     * @param stattus
     * @param riskDescription
     */
    void handleCallback(String dataId, Integer stattus, String riskDescription);
}
