package com.sohu.middle.api.enums;

import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/17 10:31
 */
@Getter
@NoArgsConstructor
public enum DetectTypeEnum {

    TEXT(1, "文本"),
    IMAGE(2, "图片"),
    VIDEO(3, "视频"),
    AUDIO(4, "音频"),
    Link(5, "链接"),
    FILE(6, "文件"),
    RICH_TEXT(7, "富文本"),
    ;
    private Integer code;
    private String desc;

    DetectTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DetectTypeEnum getByCode(Integer code) {
        for (DetectTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        // 如果没有找到匹配的code，可以返回null或者一个默认值
        return null;
    }
}
