package com.sohu.middle.api.vo.risk;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 风控记录检测视图对象
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
public class SohuRiskRecordRelateVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "id", description = "ID", example = "1")
    private Long id;

    @Schema(name = "recordId", description = "记录ID", example = "1")
    private Long riskRecordId;

    @Schema(name = "riskConfigId", description = "风控配置ID", example = "1")
    private Long riskConfigId;

    @Schema(name = "content", description = "内容(拆分)", example = "1")
    private String content;

    @Schema(name = "dataId", description = "数据请求唯一标识", example = "1")
    private String dataId;

    @Schema(name = "status", description = "0：通过，1：嫌疑，2：不通过 3 审核中", example = "1")
    private Integer status;

    @Schema(name = "riskDescription", description = "风险描述", example = "1")
    private String riskDescription;

    @Schema(name = "taskId", description = "任务ID", example = "1")
    private String taskId;


}
