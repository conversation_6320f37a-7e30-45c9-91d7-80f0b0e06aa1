package com.sohu.middle.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 广告位说明
 */
@Getter
@AllArgsConstructor
public enum AdPlaceEnum {

    SDZX_SY_LB("SDZX_SY_LB", "商单中心-首页-轮播"),
    JDHW_SY_LB("JDHW_SY_LB", "焦点海外-首页-轮播"),
    JDHW_HWFC_SY_LB("JDHW_HWFC_SY_LB", "焦点海外-海外房产-首页-轮播"),
    JDHW_HWLY_SY_LB("JDHW_HWLY_SY_LB", "焦点海外-海外旅游-首页-轮播"),
    JDHW_HWYJ_SY_LB("JDHW_HWYJ_SY_LB", "焦点海外-海外永居-首页-轮播"),
    JDHW_HWLX_SY_LB("JDHW_HWLX_SY_LB", "焦点海外-海外留学-首页-轮播"),
    JDHW_HWJY_SY_LB("JDHW_HWJY_SY_LB", "焦点海外-海外就业-首页-轮播"),
    ZQ_RMZY_SY_LB("ZQ_RMZY_SY_LB", "赚钱-人脉资源-首页-轮播"),
    MY_GUESS_YOU_LIKE("MY_GUESS_YOU_LIKE","我的-猜你喜欢"),
    APP_TASK_LIST_INFO("APP-SDLB-XIL","app-商单列表-信息流"),
    APP_ARTICLE_LIST_INFO("APP-TWLB-XIL","app-图文列表-信息流"),
    APP_VIDEO_LIST_INFO("APP-SPBF-XIL","app-视频播放页-信息流"),
    TASK_TOP("APP-SDLB-DB","app-商单列表顶部"),
    APP_SD_AIFBSD("APP-SD-AIFBSD","app-商单-AI发布商单")
    ;

    private String placeCode;
    private String placePage;

    public static final List<String> placeCodes = new ArrayList<>();

    static {
        for (AdPlaceEnum adPlaceEnum : AdPlaceEnum.values()) {
            placeCodes.add(adPlaceEnum.getPlaceCode());
        }
    }
}
