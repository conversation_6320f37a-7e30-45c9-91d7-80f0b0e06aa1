package com.sohu.middle.api.bo.risk;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 风控记录业务对象
 *
 * <AUTHOR>
 * @date 2025-06-16
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuRiskRecordBo extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 业务分类 Article-图文  Video-视频  Question-问答 ShortPlay 短剧  BusyOrder-商单 Goods-商品 Novel-小说  Game-游戏 Literature-诗歌散文
     */
    @NotBlank(message = "业务分类 Article-图文  Video-视频  Question-问答 ShortPlay 短剧  BusyOrder-商单 Goods-商品 Novel-小说  Game-游戏 Literature-诗歌散文不能为空", groups = {AddGroup.class, EditGroup.class})
    private String busyType;

    /**
     * 业务ID
     */
    @NotBlank(message = "业务ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String busyCode;
}
