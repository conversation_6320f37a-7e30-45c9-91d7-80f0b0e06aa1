package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 短剧拓展信息视图对象
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@Data
@ExcelIgnoreUnannotated
public class SohuPlayletInfoVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 剧集id
     */
    @ExcelProperty(value = "剧集id")
    private Long playletId;

    /**
     * 分享链接
     */
    @ExcelProperty(value = "分享链接")
    private String promoteUrl;

    /**
     * 推广文案
     */
    @ExcelProperty(value = "推广文案")
    private String promoteContent;

    /**
     * 网盘类型
     */
    @ExcelProperty(value = "网盘类型")
    private String webDiskType;

    /**
     * 网盘链接
     */
    @ExcelProperty(value = "网盘链接")
    private String webDiskUrl;


}
