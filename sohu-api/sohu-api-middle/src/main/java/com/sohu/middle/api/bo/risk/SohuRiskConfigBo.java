package com.sohu.middle.api.bo.risk;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 风控检测配置业务对象
 *
 * <AUTHOR>
 * @date 2025-06-16
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuRiskConfigBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String busyType;

    /**
     * 检测类型  1.文本  2.图片 3.视频 4.音频 5.链接 6.文档 
     */
    @NotNull(message = "检测类型  1.文本  2.图片 3.视频 4.音频 5.链接 6.文档 不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer detectType;

    /**
     * 字段描述
     */
    @NotBlank(message = "字段描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fieldName;

    /**
     * 字段编码
     */
    @NotBlank(message = "字段编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fieldCode;

    /**
     * 通过处理结果  0.不允许发布 1.发布
     */
    @NotNull(message = "通过处理结果  0.不允许发布 1.发布不能为空", groups = { AddGroup.class, EditGroup.class })
    private Boolean passProcess;

    /**
     * 疑似处理结果  0.不允许发布  1.发布
     */
    @NotNull(message = "疑似处理结果  0.不允许发布  1.发布不能为空", groups = { AddGroup.class, EditGroup.class })
    private Boolean suspectProcess;

    /**
     * 不通过处理结果 0.不允许发布  1.发布
     */
    @NotNull(message = "不通过处理结果 0.不允许发布  1.发布不能为空", groups = { AddGroup.class, EditGroup.class })
    private Boolean noPassProcess;

    /**
     * 平台 sohuglobal 许愿狐  catchfish 捕鱼 yougua 有瓜  hifocus Hi狐
     */
    @NotBlank(message = "平台 sohuglobal 许愿狐  catchfish 捕鱼 yougua 有瓜  hifocus Hi狐不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platform;

    /**
     * secretId  易盾
     */
    private String secretId;
    /**
     * secretKey  易盾
     */
    private String secretKey;
    /**
     * businessId  易盾
     */
    private String businessId;

}
