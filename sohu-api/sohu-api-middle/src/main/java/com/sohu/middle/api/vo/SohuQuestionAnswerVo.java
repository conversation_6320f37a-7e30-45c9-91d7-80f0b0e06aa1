package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.web.domain.SohuBaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;


/**
 * 回答主体视图对象
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ExcelIgnoreUnannotated
public class SohuQuestionAnswerVo extends SohuBaseVo {

    private static final long serialVersionUID = 1L;

    /**
     * 回答主键id
     */
    @ExcelProperty(value = "回答主键id")
    private Long id;

    /**
     * 问题状态
     * {@link com.sohu.common.core.enums.CommonState}
     * 状态;Delete:删除，Edit:草稿，WaitApprove:待审核，OnShelf:上架，OffShelf:下架，CompelOff:强制下架，Private:仅自己可见，Refuse:已拒绝
     */
    @ExcelProperty(value = "状态;Delete:删除，Edit:草稿，WaitApprove：待审核,OnShelf：上架，OffShelf：下架，CompelOff：强制下架,Refuse：已拒绝")
    private String questionState;
    /**
     * 问题id
     */
    @ExcelProperty(value = "问题id")
    private Long questionId;

    /**
     * 上级id
     */
    @ExcelProperty(value = "上级id")
    private Long pid;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容")
    private String content;

    /**
     * 作者id
     */
    @ExcelProperty(value = "作者id")
    private Long userId;

    /**
     * 状态;Delete:删除，Edit:草稿，WaitApprove：待审核,OnShelf：上架，OffShelf：下架，CompelOff：强制下架
     */
    @ExcelProperty(value = "状态;Delete:删除，Edit:草稿，WaitApprove：待审核,OnShelf：上架，OffShelf：下架，CompelOff：强制下架")
    private String state;

    /**
     * 关联项
     */
    private RelationRespVo relation;

    /**
     * 问题标题
     */
    private String title;

    /**
     * 问题站点
     */
    private Long siteId;

    /**
     * 问题分类
     */
    private Long categoryId;

    /**
     * 回答人昵称
     */
    private String nickName;

    /**
     * 回答人名称
     */
    private String authorName;
    /**
     * 回答人头像
     */
    private String authorAvatar;

    /**
     * 我关注他状态，true = 关注
     */
    private Boolean followObj = Boolean.FALSE;
    /**
     * 是否点赞当前对象
     */
    private Boolean praiseObj = Boolean.FALSE;

    /**
     * 是否收藏当前对象
     */
    private Boolean collectObj = Boolean.FALSE;

    /**
     * 点赞数
     */
    private Integer praiseCount = 0;
    /**
     * 评论数
     */
    private Integer commentCount = 0;
    /**
     * 浏览数
     */
    private Integer viewCount = 0;
    /**
     * 收藏数
     */
    private Integer collectCount = 0;
    /**
     * 转发数
     */
    private Integer forwardCount = 0;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 回答评论列表
     */
    List<SohuCommentVo> commentList;

}
