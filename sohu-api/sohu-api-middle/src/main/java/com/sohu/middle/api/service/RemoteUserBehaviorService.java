package com.sohu.middle.api.service;

import com.sohu.middle.api.vo.UserBehaviorGroupInfoVo;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25 10:00
 */
public interface RemoteUserBehaviorService {

    /**
     * 获取用户行为分组信息
     * @param sourceType
     * @param eventSigns
     * @param startTime
     * @param endTime
     * @return
     */
    List<UserBehaviorGroupInfoVo> list(String sourceType, List<String> eventSigns, Date startTime, Date endTime);
}
