package com.sohu.middle.api.service;

import com.sohu.common.core.domain.MsgContent;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.playlet.PlayletOpenAdsQueryBo;
import com.sohu.middle.api.bo.playlet.PlayletPatchesAdsQueryBo;
import com.sohu.middle.api.vo.*;
import com.sohu.middle.api.vo.playlet.PlayletOpenAdsListVo;
import com.sohu.middle.api.vo.playlet.PlayletOpenAdsVo;
import com.sohu.middle.api.vo.playlet.PlayletPatchesAdsListVo;
import com.sohu.middle.api.vo.playlet.PlayletPatchesAdsVo;

import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;

public interface RemoteMiddleAdInfoService {

    /**
     * 查询站点广告主体
     */
    SohuAdInfoVo queryById(Long id);

    /**
     * 查询站点广告主体列表
     */
    @Deprecated
    TableDataInfo<SohuAdInfoVo> queryPageList(SohuAdInfoBo bo, PageQuery pageQuery);

    /**
     * 查询站点广告主体列表-管理
     */
    TableDataInfo<SohuAdInfoVo> queryPageListV2(SohuAdInfoQueryBo bo, PageQuery pageQuery);

    /**
     * 查询站点广告主体列表
     */
    List<SohuAdInfoVo> queryList(SohuAdInfoBo bo);

    /**
     * 修改站点广告主体
     */
    Boolean insertByBo(SohuAdInfoBo bo);

    /**
     * 修改站点广告主体
     */
    Boolean updateByBo(SohuAdInfoBo bo);

    /**
     * 校验并批量删除站点广告主体信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 校验并批量删除站点广告主体信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, String type);

    /**
     * 广告上下架
     *
     * @param bo
     * @return
     */
    Boolean operate(SohuAdInfoBo bo);

    /**
     * 开屏广告分页查询
     */
    TableDataInfo<PlayletOpenAdsListVo> queryOpenAdsList(PlayletOpenAdsQueryBo bo, PageQuery pageQuery);

    /**
     * 定时任务处理开屏广告状态
     *
     * @return
     */
    Boolean updateAdInfoState();

    /**
     * 贴片广告分页查询
     */
    TableDataInfo<PlayletPatchesAdsListVo> queryPatchesAdsList(PlayletPatchesAdsQueryBo bo, PageQuery pageQuery);

    /**
     * 查询贴片广告详情
     *
     * @param id
     * @return
     */
    PlayletPatchesAdsVo queryPatchesById(Long id);

    /**
     * 开屏广告查询
     */
    PlayletOpenAdsVo queryOpenDetail();

    /**
     * 贴片广告详情查询（根据视频id）
     *
     * @param videoId
     * @return
     */
    PlayletPatchesAdsVo queryPatchesAdsByVideoId(Long videoId);

    /**
     * 广告位定时任务处理
     * @return
     */
    Boolean advertisementJobHandler();

    /**
     * 刷新广告缓存
     * @param msgContent
     * @return
     */
    Boolean refreshAdvertisementCache(MsgContent msgContent);

    /**
     * 我的-猜你喜欢列表
     */
    TableDataInfo<SohuGuessYouLikeVo> queryPageListOfAirec(SohuGuessYouLikeQueryBo bo, PageQuery pageQuery);

    /**
     * 根据广告位编号刷新广告主体信息-广告位发送修改时调用
     * @param adPlace
     * @return
     */
    Boolean refreshAdInfoByAdPlace(String adPlace);

    /**
     * 刷新数据-用于发版时处理兼容处理旧数据
     * @return
     */
    Boolean refreshAdInfoTest();

    /**
     * 视频-广告
     */
    TableDataInfo<SohuVideoAdInfoVo> queryPageListOfVideo(SohuVideoAdInfoQueryBo bo, PageQuery pageQuery);

    /**
     * 图文-广告
     */
    TableDataInfo<SohuArticleAdInfoVo> queryPageListOfArticle(SohuArticleAdInfoQueryBo bo, PageQuery pageQuery);

    /**
     * 商单-广告
     */
    TableDataInfo<SohuTaskSiteAdInfoVo> queryPageListOfTaskSite(SohuTaskSiteAdInfoQueryBo bo, PageQuery pageQuery);

    /**
     * 随机获取一个广告
     *
     * @param excludeIds 需要排除的广告ID列表
     * @return 随机广告信息
     */
    SohuAdInfoVo getRandomAd(List<Long> excludeIds, String adPlaceCode);

    /**
     * 根据广告位获取广告列表
     *
     * @param adPlaceCode 广告位Code
     * @return List<SohuAdInfoVo>
     */
    List<SohuAdInfoVo> getAdListByAdPlaceCode(String adPlaceCode);

    /**
     * 根据群聊id下架广告
     */
    void offShelfAdInfoByGroupId(Long groupId);
}
