package com.sohu.middle.api.vo.risk;

import com.sohu.middle.api.enums.DetectTypeEnum;
import lombok.Data;

/**
 * 富文本解析结果对象
 *
 * @Author: leibo
 * @Date: 2025/6/17 19:25
 **/
@Data
public class RichTextElement {

    private String fieldCode;
    private String content;
    private DetectTypeEnum detectType;
    private String busyType;

    public RichTextElement(String fieldCode, String content, DetectTypeEnum detectType, String busyType) {
        this.content = content;
        this.detectType = detectType;
        this.fieldCode = fieldCode;
        this.busyType = busyType;
    }
}
