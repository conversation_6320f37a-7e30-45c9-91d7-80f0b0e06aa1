package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 平台行业视图对象
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Data
@ExcelIgnoreUnannotated
public class SohuPlatformIndustryVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 行业名称
     */
    @ExcelProperty(value = "行业名称")
    private String name;
    /**
     * 是否默认
     */
    private Boolean isDefault;
    /**
     * 默认序号
     */
    private Integer sort;

    /**
     * 关联愿望行业名称数组
     */
    private List<String> industryCategoryNames;
    /**
     * 关联商品分类名称数组
     */
    private List<String> productCategoryNames;
    /**
     * 关联内容分类名称数组
     */
    private List<String> projectCategoryNames;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 是否已使用  1.已使用  2.未使用
     */
    private Boolean isUse;
    /**
     * 模版id
     */
    private Long templateId;

}
