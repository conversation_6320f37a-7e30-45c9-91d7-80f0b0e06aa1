package com.sohu.middle.api.bo.risk;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/13 15:43
 */
@Data
public class RiskCheckBo implements Serializable {
    private static final long serialVersionUID = -1L;

    @Schema(name = "busyType", description = "业务分类 Article-图文  Video-视频  Question-问答 ShortPlay 短剧  BusyOrder-商单 Goods-商品 Novel-小说  Game-游戏 Literature-诗歌散文", example = "IM")
    private String busyType;

    @Schema(name = "busyCode", description = "业务码", example = "1000")
    private String busyCode;

    @Schema(name = "contentList", description = "内容列表", example = "[\"内容1\",\"内容2\"]")
    private List<Content> contentList;

    @Data
    public static class Content {
        @Schema(name = "detectType", description = "检测类型  1.文本  2.图片 3.视频 4.音频 5.链接 6.文档", example = "1")
        private Integer detectType;
        @Schema(name = "content", description = "内容", example = "内容")
        private String content;
        @Schema(name = "configId", description = "配置id", example = "1")
        private Long configId;
        @Schema(name = "secretId", description = "易盾secretId", example = "f87f85a5fbb21b2f93f666f3a886c653")
        private String secretId;
        @Schema(name = "secretKey", description = "易盾secretKey", example = "97ca9e910741790bfcb4ab99b9f25fc3")
        private String secretKey;
        @Schema(name = "businessId", description = "易盾businessId", example = "dd3896ff636627418286840d84b28d9a")
        private String businessId;
    }
}
