package com.sohu.middle.api.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuIndependentMaterialBo;
import com.sohu.middle.api.bo.SohuIndependentMaterialUserBo;
import com.sohu.middle.api.vo.SohuIndependentMaterialUserVo;
import com.sohu.middle.api.vo.SohuIndependentMaterialVo;

import java.util.Collection;
import java.util.List;

public interface RemoteMiddleIndependentMaterialService {

    /**
     * 查询分销素材库
     */
    SohuIndependentMaterialVo queryById(Long id,Boolean isMe);

    /**
     * 查询分销素材库列表
     */
    TableDataInfo<SohuIndependentMaterialVo> queryPageList(SohuIndependentMaterialBo bo, PageQuery pageQuery);

    /**
     * 查询分销素材库列表
     */
    List<SohuIndependentMaterialVo> queryList(SohuIndependentMaterialBo bo);

    /**
     * 修改分销素材库
     */
    Boolean insertByBo(SohuIndependentMaterialBo bo);

    /**
     * 修改分销素材库
     */
    Boolean updateByBo(SohuIndependentMaterialBo bo);

    /**
     * 校验并批量删除分销素材库信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 新增素材到我的素材库
     * @param bo SohuIndependentMaterialUserBo
     * @return Boolean
     */
    Boolean addMaterial(SohuIndependentMaterialUserBo bo);

    /**
     * 查询我的素材库列表
     * @param pageQuery 分页参数
     * @return TableDataInfo<SohuIndependentMaterialVo>
     */
    TableDataInfo<SohuIndependentMaterialVo> myMaterialList(SohuIndependentMaterialBo bo, PageQuery pageQuery);

    /**
     * 用户分销素材库
     */
    SohuIndependentMaterialUserVo queryMaterialUser(Long materialId, Long materialShareUserId);

    /**
     * 根据素材库类型以及素材code查询素材信息
     */
    SohuIndependentMaterialVo queryByCodeAndType(String materialCode, String materialType);

    /**
     * 查询流量商单素材信息
     * @param materialCode
     * @param materialType
     * @return
     */
    SohuIndependentMaterialVo queryByFlowTask(String materialCode, String materialType);

    /**
     * 据素材编号和素材类型删除素材信息
     *
     * @param materialCode 素材code
     * @param materialType 素材类型
     * @return Boolean
     */
    Boolean deleteByCodeAndType(String materialCode, String materialType);

    /**
     * 据素材编号和素材类型删除素材信息
     *
     * @param materialCode 素材code
     * @param materialType 素材类型
     * @return Boolean
     */
    Boolean deleteByCodeAndType(String materialCode, String materialType, Long userId);

    /**
     * 校验数据是否存在
     *
     * @param materialId 素材id
     * @param materialShareUserId 分享人id
     * @return Boolean
     */
    Boolean checkData(Long materialId, Long materialShareUserId);
}
