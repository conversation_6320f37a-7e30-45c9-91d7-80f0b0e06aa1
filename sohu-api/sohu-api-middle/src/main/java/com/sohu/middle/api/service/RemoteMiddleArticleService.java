package com.sohu.middle.api.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.mcn.SohuMcnArticleReqBo;
import com.sohu.middle.api.vo.SohuArticleVo;
import com.sohu.middle.api.vo.SohuConentListStatVo;
import com.sohu.middle.api.vo.SohuTopArticleVo;

import java.util.Collection;
import java.util.List;

/**
 * 图文
 *
 * <AUTHOR>
 */
public interface RemoteMiddleArticleService {

    /**
     * 赚钱图文列表-智能推荐
     *
     * @param bo
     * @return
     */
    TableDataInfo<SohuArticleVo> businessArticleListOfAirec(SohuBusinessArticleBo bo);

    /**
     * 图文列表（单表-新）-智能推荐
     */
    TableDataInfo<SohuArticleVo> queryPageOfAirec(SohuArticleBo bo, PageQuery pageQuery);

    /**
     * 个人中心图文列表
     */
    TableDataInfo<SohuArticleVo> articlePageCenter(Long userId, PageQuery pageQuery);

    /**
     * 关注图文
     */
    TableDataInfo<SohuArticleVo> followPage(PageQuery pageQuery);

    /**
     * 返回每个标签下前五的图文
     * 双层数组结构返回
     */
    List<SohuTopArticleVo> labelTopFive();

    /**
     * 新增图文主体
     */
    Boolean insertByBo(SohuArticleBo bo);

    /**
     * 校验并批量删除图文主体信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 投流图文
     */
    TableDataInfo<SohuArticleVo> articlePageCenterByType(SohuArticleBo bo, PageQuery pageQuery);

    /**
     * 构建关联项
     *
     * @param articleVo
     */
    void buildRelate(SohuArticleVo articleVo);

    /**
     * 草稿重发
     *
     * @param busyBO
     * @return {@link Boolean}
     */
    Boolean draftRetry(SohuBusyBO busyBO);

    /**
     * 转发图文
     * @param bo
     * @return {@link Boolean}
     */
    Boolean forward(SohuBusyBO bo);

    /**
     * 图文草稿提交至审核
     *
     * @param articleId 图文ID
     */
    void commit(Long articleId);

    /**
     * 图文分页查询
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuArticleVo> queryMCNArticleList(SohuMcnArticleReqBo bo, PageQuery pageQuery);

    /**
     * 根据发布前的唯一表示查询发布者id
     */
    Long getUserIdByPublishMediaId(String publishMediaId);

    /**
     * 初始化智能推荐物料
     */
    Boolean initAirecContentItems();

    /**
     * 修改用户所有图文的状态，删除的话是假删除
     *
     * @param userId 用户ID
     * @param state  {@link com.sohu.common.core.enums.CommonState}
     * @return {@link Boolean}
     */
    @Deprecated
    Boolean updateUserArticleState(Long userId, String state);

    /**
     * 批量修改作品状态
     * 
     * @param bo SohuContentBatchBo
     * @return Boolean
     */
    Boolean updateBatchContentState(SohuContentBatchBo bo);

    /**
     * 用户申述
     */
    Boolean userAppeal(SohuUserContentAppealBo bo);

    /**
     * 提交草稿至审核
     * @param id
     * @return
     */
    Boolean submitAudit(Long id);

    /**
     * 图文下架-自主下架
     *
     * @param id
     * @return
     */
    boolean updateOffShelfById(Long id);

    /**
     * 从回收站恢复数据
     * @param id
     * @return
     */
    Boolean recoveryData(Long id);

    /**
     * 从回收站删除数据
     * @param id
     * @return
     */
    Boolean deleteDataById(Long id);

    /**
     * 图文下架-强制下架
     *
     * @param bo
     * @return
     */
    boolean updateCompelOffById(SohuContentRefuseBo bo);

    /**
     * 图文逻辑删除-强制删除-批量
     *
     * @param ids
     * @return
     */
    boolean logicForceDeleteById(Collection<Long> ids);

    /**
     * 隐藏数据
     * @param ids
     * @return
     */
    Boolean hideDataBatch(Collection<Long> ids);

    /**
     * 查询图文主体列表-统计
     * @param bo
     * @return
     */
    SohuConentListStatVo queryPageListStat(SohuArticleBo bo);

    /**
     * 清空回收站数据-过期
     * @return
     */
    Boolean clearRecycleDataOfTimeOut();

    /**
     * 查询图文主体列表-OnShelf
     */
    TableDataInfo<SohuArticleVo> queryPageListOfOnShelf(SohuArticleBo bo, PageQuery pageQuery);

    /**
     * 查询专题内容列表
     */
    TableDataInfo<SohuArticleVo> getTopicList(SohuTopicContentQueryBo bo, PageQuery pageQuery);

    /**
     * 查询图文（只查狐少少课堂的）
     *
     * @return
     */
    List<SohuArticleVo> articleList();
}
