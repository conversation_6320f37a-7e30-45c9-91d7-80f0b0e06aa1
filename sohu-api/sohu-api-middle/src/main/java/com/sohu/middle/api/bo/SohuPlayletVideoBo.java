package com.sohu.middle.api.bo;

import com.sohu.third.aliyun.airec.domain.bo.ISohuAiRecReqBo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 短剧首页视频列表
 *
 * <AUTHOR>
 * @date 2024-03-12
 */

@Data
public class SohuPlayletVideoBo implements ISohuAiRecReqBo, Serializable {

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 当前页数
     */
    private Integer pageNum;

    /**
     * 是否智能推荐,非必传
     */
    private Boolean aiRecommend = false;

    /**
     * 设备号，未登录必传
     * 未登录用户
     * 安卓：MD5（imei），IOS：MD5（idfa）
     */
    private String imei;

    /**
     * 剧集关联
     */
    private String episodeRelevance;

    /**
     * 是否智能推荐(支持智能推荐)
     */
    private Boolean aiRec = false;

    /**
     * 用户设备id，安卓设备是（imei），IOS设备是（idfa）
     * (支持智能推荐)(已登录的用户可不填，未登录用户必填)
     */
    private String aiRecImei;

    /**
     * 需要推荐的目标用户,和imei至少一个不为空
     * @return
     */
    @Schema(hidden = true)
    private String aiUserId;

    /**
     * 场景id
     * （智能推荐必传）
     */
    private String aiRecSceneId;

    /**
     * 智能推荐，单次请求返回的推荐结果数量，建议取值20,最大值50
     */
    @Schema(hidden = true)
    private Integer aiReturnCount;

}
