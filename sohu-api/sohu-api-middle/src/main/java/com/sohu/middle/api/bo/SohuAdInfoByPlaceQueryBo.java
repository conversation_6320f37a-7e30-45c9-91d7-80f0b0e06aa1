package com.sohu.middle.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 根据广告位标识查询广告列表请求对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "根据广告位标识查询广告列表请求对象")
public class SohuAdInfoByPlaceQueryBo implements Serializable {

    /**
     * 广告位标识
     */
    @NotBlank(message = "广告位标识不能为空")
    @Schema(title = "广告位标识", description = "广告位标识，如：APP-TWXQ-MOUNT", example = "APP-TWXQ-MOUNT", required = true)
    private String adPlaceCode;

    /**
     * 用户ID（用于过滤已看过的广告）
     */
    @Schema(title = "用户ID", description = "当前登录用户ID，用于过滤已看过的广告", example = "123")
    private Long userId;

    /**
     * 设备UUID（游客用户标识）
     */
    @Schema(title = "设备UUID", description = "游客用户的设备标识，用于过滤已看过的广告", example = "uuid-123456")
    private String uuid;

    /**
     * 排序方式
     */
    @Schema(title = "排序方式", description = "排序方式：exposure_asc-曝光数升序，exposure_desc-曝光数降序，sort_index_asc-排序值升序，sort_index_desc-排序值降序", 
            example = "exposure_asc", allowableValues = {"exposure_asc", "exposure_desc", "sort_index_asc", "sort_index_desc"})
    private String orderBy = "exposure_asc";

    /**
     * 返回数量限制
     */
    @Schema(title = "返回数量限制", description = "返回的广告数量限制，默认10条", example = "10")
    private Integer limit = 10;

    /**
     * 是否过滤已看过的广告
     */
    @Schema(title = "是否过滤已看过的广告", description = "是否过滤用户已看过的广告，默认true", example = "true")
    private Boolean filterViewed = true;

    /**
     * 广告状态
     */
    @Schema(title = "广告状态", description = "广告状态，默认OnShelf-上架", example = "OnShelf")
    private String state = "OnShelf";

    /**
     * 端口类型
     */
    @Schema(title = "端口类型", description = "端口类型：android、ios、wechat等", example = "android")
    private String port;
}
