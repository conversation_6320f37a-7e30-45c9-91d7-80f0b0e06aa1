package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 用户的标签视图对象
 *
 * <AUTHOR>
 * @date 2024-01-10
 */
@Data
@ExcelIgnoreUnannotated
public class SohuUserTagVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 标签名称
     */
    @ExcelProperty(value = "标签名称")
    private String name;

    /**
     * 标签排序值
     */
    @ExcelProperty(value = "标签排序值")
    private Integer sortIndex;

    /**
     * 标签下的好友数量
     */
    private long tagFriendCount;


}
