package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 城市站点内容分类黑名单(即不可见)视图对象
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
@Data
@ExcelIgnoreUnannotated
public class SohuCategoryBlackVo implements Serializable {

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * sohu_category表ID
     */
    @ExcelProperty(value = "sohu_category表ID")
    private Long categoryId;

    /**
     * 城市站点ID
     */
    @ExcelProperty(value = "城市站点ID")
    private Long citySiteId;


}
