package com.sohu.middle.api.vo.ai;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * AI解析对象
 *
 * @Author: leibo
 * @Date: 2025/3/13 09:38
 **/
@Data
public class AiAnalysisVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商单对象
     */
    private BusyTaskVo busyTaskVo;


    /**
     * 商单对象
     */
    @Data
   public class BusyTaskVo implements Serializable{

        /**
         * 标题
         */
        private String title;

        /**
         * 内容描述
         */
        private String content;

        /**
         * 分类类型
         */
        private Long categoryId;

        /**
         * 分类名称
         */
        private String categoryName;

        /**
         * 愿望分类
         */
        private Long categoryType;

        /**
         * 愿望分类名称
         */
        private String categoryTypeName;

        /**
         * 供需类型
         */
        private Long supplyType;

        /**
         * 供需类型名称
         */
        private String supplyTypeName;

        /**
         * 行业父id
         */
        private Long pid;

        /**
         * 行业类型
         */
        private Long industryType;

        /**
         * 行业名称
         */
        private String industryName;

        /**
         * 商单类型
         */
        private Long type;

        /**
         * 商单类型名称
         */
        private String typeName;

        /**
         * 潜在需求
         */
        private List<String> potentialDemand;
    }

}
