package com.sohu.middle.api.service;

import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.web.domain.SohuBaseBo;
import com.sohu.common.core.web.domain.SohuBaseVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuBusyBO;
import com.sohu.middle.api.bo.SohuCommentBo;
import com.sohu.middle.api.bo.SohuContentListBo;
import com.sohu.middle.api.bo.SohuReportInfoBo;
import com.sohu.middle.api.vo.SohuContentMainVo;
import com.sohu.middle.api.vo.SohuContentVo;

import java.util.Collection;
import java.util.List;

/**
 * 公共业务接口
 *
 * <AUTHOR>
 */
public interface RemoteMiddleService<B extends SohuBaseBo, V extends SohuBaseVo> {

    /**
     * 添加
     *
     * @param entity 实体类
     * @return {@link Boolean}
     */
    Boolean add(B entity);

    /**
     * 修改
     *
     * @param entity 实体类
     * @return {@link Boolean}
     */
    Boolean update(B entity);

    /**
     * 查询
     *
     * @param id 对象主键ID
     * @return {@link Boolean}
     */
    V query(BusyType busyType, Long id);

    /**
     * 删除
     *
     * @param id 对象主键ID
     * @return {@link Boolean}
     */
    Boolean delete(BusyType busyType, Long id);

    /**
     * 删除
     *
     * @param ids 对象主键ID集合
     * @return {@link Boolean}
     */
    Boolean delete(BusyType busyType, Collection<Long> ids);

    /**
     * 评论
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean comment(SohuCommentBo bo);

    /**
     * 点赞
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean like(SohuBusyBO bo);

    /**
     * 收藏
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean collect(SohuBusyBO bo);

    /**
     * 分享
     *
     * @param id 对象主键ID
     * @return {@link Boolean}
     */
    Boolean share(BusyType busyType, Long id);

    /**
     * 举报
     *
     * @return {@link Boolean}
     */
    Boolean report(SohuReportInfoBo bo);

    /**
     * 分页查询
     *
     * @return {@link TableDataInfo}
     */
    TableDataInfo<V> queryPageList(B bo, PageQuery pageQuery);

    /**
     * 不分页查询
     *
     * @param bo
     * @return {@link Boolean}
     */
    List<V> queryList(B bo);

    /**
     * 根据type查询对于的对象
     *
     * @param busyType      {@link BusyType}
     * @param id            对象的主键ID
     * @param queryMoreInfo 是否查询更多信息
     * @return 返回万能对象VO
     */
    SohuContentMainVo queryObj(BusyType busyType, Long id, Boolean queryMoreInfo);

    /**
     * 查询内容列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuContentVo> queryPageOfAirec(SohuContentListBo bo, PageQuery pageQuery);
}
