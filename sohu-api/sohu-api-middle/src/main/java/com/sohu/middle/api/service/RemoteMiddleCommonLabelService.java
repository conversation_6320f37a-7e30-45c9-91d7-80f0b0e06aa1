package com.sohu.middle.api.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.vo.SohuCommonLabelListVo;
import com.sohu.middle.api.vo.SohuCommonLabelVo;
import com.sohu.middle.api.vo.SohuUserLabelRelationVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 通用标签远程接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface RemoteMiddleCommonLabelService {

    /**
     * 查询通用标签列表
     */
    List<SohuCommonLabelListVo> queryPageList();

    /**
     * 查询通用标签列表
     */
    TableDataInfo<SohuCommonLabelVo> queryPageList(SohuCommonLabelListBo bo, PageQuery pageQuery);

    /**
     * 修改通用标签
     */
    Boolean insertByBo(SohuCommonLabelBo bo);

    /**
     * 修改通用标签
     */
    Boolean updateByBo(SohuCommonLabelBo bo);

    /**
     * 校验并批量删除通用标签信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量插入用户标签关系
     *
     * @param userLabelList 用户标签关系列表
     */
    void insertBatch(List<SohuUserLabelRelationBo> userLabelList);

    /**
     * 根据主键id批量获取标签名称
     */
    Map<Long, String> queryLabelNamesByIds(List<Long> ids);

    /**
     * 根据主键id集合批量获取标签名称集合
     */
    List<SohuCommonLabelListVo> queryLabelNameListByIds(List<Long> ids);

    /**
     * 根据用户id查询标签列表
     *
     * @param userId 用户id
     * @return 标签列表
     */
    List<SohuUserLabelRelationVo> queryLabelsByUserId(Long userId);

    /**
     * 根据用户id删除标签
     *
     * @param userId 用户id
     * @param labelType 标签类型
     */
    void deleteUserLabelByUserId(Long userId, String labelType);

    /**
     * 点击广告绑定用户标签
     */
    Boolean bindUserLabelWithClick(SohuBindUserLabelBo bo);

    /**
     * 批量插入用户标签
     *
     * @param bo SohuSaveUserLabelRelationBo
     */
    void batchInsert(SohuSaveUserLabelRelationBo bo);
}
