package com.sohu.middle.api.bo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuBaseBo;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.sohu.common.core.utils.DateUtils.TIME_ZONE_DEFAULT;

/**
 * 站点广告主体业务对象
 *
 * <AUTHOR>
 * @date 2023-06-21
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuAdInfoBo extends SohuBaseBo {

    /**
     * 广告主键ID
     */
    @NotNull(message = "广告主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 站点ID
     */
    @NotNull(message = "站点ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long siteId;

    /**
     * 广告位表code
     */
    @NotBlank(message = "广告位表code不能为空", groups = {AddGroup.class, EditGroup.class})
    private String adPlace;

    /**
     * 广告标题
     */
    private String title;

    /**
     * 广告图片
     */
    //@NotBlank(message = "广告图片不能为空", groups = {AddGroup.class, EditGroup.class})
    private String image;

    /**
     * 附加广告图片
     */
    private String extraImage;

    /**
     * 广告链接
     */
    private String link;

    /**
     * 广告状态;Edit:编辑,OnShelf:上架,OffShelf:下架,Delete:删除
     */
    private String state;

    /**
     * 广告投放开始时间
     */
    @NotNull(message = "广告投放开始时间不能为空", groups = {AddGroup.class, EditGroup.class})
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS, timezone = TIME_ZONE_DEFAULT)
    private Date startTime;

    /**
     * 广告投放结束时间
     */
    @NotNull(message = "广告投放结束时间不能为空", groups = {AddGroup.class, EditGroup.class})
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS, timezone = TIME_ZONE_DEFAULT)
    private Date overTime;

    /**
     * 排序值
     */
    private Long sortIndex;

    /**
     * 页面标题
     */
    private String placePage;

    /**
     * 广告类型
     */
    @Schema(name = "type", description = "OPEN:开屏广告;PATCHES:贴片广告;GOODS:商品广告;BUSINESS:商单广告",example = "OPEN")
    private String type;

    /**
     * 端口 android 安卓、wechat-微信小程序、ios
     */
    @Deprecated
    @Hidden
    private String port;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 业务id
     */
    private Long objId;

    /**
     * 业务类型
     */
    private String objType;
    /**
     * 业务标题
     */
    private String objTitle;

    /**
     * 广告金额
     */
    private BigDecimal price;

    /**
     * 发布者id
     */
    private Long userId;

    /**
     * 关联短剧id
     */
    private Long playletId;

    /**
     * 关联剧集集数
     */
    private Integer episodeNumber;

    /**
     * 关联视频id
     */
    private Long videoId;

    /**
     * 关联商品id
     */
    private Long productId;

    /**
     * 关联商户id
     */
    private Long merId;

    /**
     * 关联商单id
     */
    private Long busyTaskId;

    /**
     * 是否是关联整个剧 0-否 1-是
     */
    private Boolean isPlaylet;

    /**
     * 视频展示样式 0-横版 1-竖版
     */
    private Integer videoStyle;

    /**
     * 广告倒计时
     */
    private Integer duration;

    public SohuAdInfoBo() {
        this.sohuBusyType = BusyType.AdInfo;
    }

    @Schema(title = "标签id",description = "标签id，数组",example = "1")
    private List<Long> labelIdList;

    @Schema(title = "展现形式",description = "展现形式:[IMAGE:纯图片，NATIVE:原生广告]",example = "IMAGE")
    private String showForm;

    @Schema(title = "广告文案",description = "广告文案",example = "我是广告文案")
    private String adTxt;

    /**
     * 视频标题
     */
    private String videoTitle;

}
