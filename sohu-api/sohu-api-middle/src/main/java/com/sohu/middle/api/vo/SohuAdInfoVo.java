package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.web.domain.SohuBaseVo;
import com.sohu.middle.api.aspect.RiskDetectionField;
import com.sohu.middle.api.enums.DetectTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 站点广告主体视图对象
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ExcelIgnoreUnannotated
public class SohuAdInfoVo extends SohuBaseVo {

    /**
     * 广告主键ID
     */
    @ExcelProperty(value = "广告主键ID")
    private Long id;

    /**
     * 站点ID
     */
    @ExcelProperty(value = "站点ID")
    private Long siteId;

    /**
     * 广告位表主键ID
     */
    @ExcelProperty(value = "广告位表主键ID")
    private String adPlace;

    /**
     * 广告标题
     */
    @ExcelProperty(value = "广告标题")
    @RiskDetectionField(detectType = DetectTypeEnum.TEXT)
    private String title;

    /**
     * 广告图片
     */
    @ExcelProperty(value = "广告图片")
    @RiskDetectionField(detectType = DetectTypeEnum.IMAGE)
    private String image;
    /**
     * 附加广告图片
     */
    @ExcelProperty(value = "附加广告图片")
    private String extraImage;
    /**
     * 广告链接
     */
    @ExcelProperty(value = "广告链接")
    @RiskDetectionField(detectType = DetectTypeEnum.Link)
    private String link;

    /**
     * 广告状态;Edit:编辑,OnShelf:上架,OffShelf:下架,Delete:删除
     */
    @ExcelProperty(value = "广告状态;Edit:编辑,OnShelf:上架,OffShelf:下架,Delete:删除")
    private String state;

    /**
     * 广告投放开始时间
     */
    @ExcelProperty(value = "广告投放开始时间")
    private Date startTime;

    /**
     * 广告投放结束时间
     */
    @ExcelProperty(value = "广告投放结束时间")
    private Date overTime;

    /**
     * 排序值
     */
    @ExcelProperty(value = "排序值")
    private Long sortIndex;

    /**
     * 广告位所属页面
     */
    private String adPlacePage;

    /**
     * 类型
     */
    private String type;

    /**
     * 图片ID
     */
    private Long ossId;

    /**
     * 端口 android 安卓、wechat-微信小程序、ios
     */
    private String port;

    /**
     * 发布人id
     */
    private Long userId;

    /**
     * 业务id
     */
    private Long objId;

    /**
     * 业务类型
     * {@link com.sohu.common.core.enums.BusyType}
     */
    @Schema(title = "业务类型", description = "Video：视频；Article：图文；Playlet：短剧；Novel：小说；BusyOrder：商单；Goods：商品；AdInfo：广告")
    private String objType;
    /**
     * 业务标题
     */
    private String objTitle;

    /**
     * 广告金额
     */
    private BigDecimal price;

    /**
     * 发布人昵称
     */
    private String nickName;

    /**
     * 发布人头像
     */
    private String avatar;

    /**
     * 业务阅读数
     */
    private Integer objViewCount;

    /**
     * 业务点赞数
     */
    private Integer objPraiseCount;

    /**
     * 是否点赞当前数据
     */
    private Boolean objPraiseObj = Boolean.FALSE;

    /**
     * 业务金额
     */
    private BigDecimal objPrice;

    /**
     * 业务销售量
     */
    private Integer objSalesCount;

    /**
     * 业务封面
     */
    @Schema(name = "objCoverImage", description = "业务封面", example = "http://xxx.jpg")
    private String objCoverImage;

    /**
     * 业务链接url
     */
    private String objUrl;

    /**
     * 业务编码
     */
    private String objCode;

    /**
     * 业务关联
     */
    private String objRelevance;
    /**
     * 业务作者id
     */
    private Long objUserId;

    /**
     * 广告倒计时
     */
    @Schema(title = "广告倒计时", description = "单位：秒", example = "3")
    private Integer duration;

    @Schema(title = "曝光次数", example = "100")
    private Long exposureNum = 0L;
    @Schema(title = "点击次数", example = "5")
    private Long clickNum = 0L;
    @Schema(title = "点击率", description = "百分比", example = "5")
    private BigDecimal clickRate = BigDecimal.ZERO;

    @Schema(title = "标签id",description = "标签id，数组",example = "1")
    private List<Long> labelIdList;

    @Schema(title = "标签",description = "标签，数组",example = "标签1")
    private List<SohuAdInfoLabelRelationVo> labelList;

    @Schema(title = "展现形式",description = "展现形式:[IMAGE:纯图片，NATIVE:原生广告]",example = "IMAGE")
    private String showForm;

    @Schema(title = "广告文案",description = "广告文案",example = "我是广告文案")
    private String adTxt;

    /**
     * 视频-id
     */
    private Long videoId;

    /**
     * 视频-标题
     */
    private String videoTitle;

    /**
     * 视频-发布人昵称
     */
    private String videoNickName;

    /**
     * 视频-发布人头像
     */
    private String videoAvatar;

    /**
     * 视频-点赞数
     */
    private Integer videoPraiseCount=0;

    /**
     * 视频-是否点赞当前数据
     */
    private Boolean videoPraiseObj = Boolean.FALSE;

    /**
     * 视频-封面
     */
    @Schema(title = "视频-封面", description = "视频-封面", example = "http://xxx.jpg")
    private String videoCoverImage;

    /**
     * 视频链接url
     */
    private String videoUrl;

    /**
     * 视频评论数
     */
    private Integer videoCommentCount = 0;

    /**
     * 视频作者id
     */
    private Long videoUserId;

    /**
     * 是否关注当前作者
     */
    private Boolean videoFollow0bj;

    /**
     * 视频转发数
     */
    private Integer videoForwardCount;

    /**
     * 视频收藏数
     */
    private Integer videoCollectCount;

    /**
     * 是否收藏
     */
    private Boolean videoCollectObj = Boolean.FALSE;

    /**
     * 素材类型
     */
    @Schema(title = "素材类型", description = "素材类型:[photo:图片，video:视频]", example = "video")
    private String materialType;

    /**
     * 宽高比
     */
    private Integer aspectRatio;
    /**
     * 驳回原因
     */
    private String rejectReason;
}
