package com.sohu.resource.util;

import cn.hutool.json.JSONUtil;
import cn.jiguang.sdk.api.DeviceApi;
import cn.jiguang.sdk.api.PushApi;
import cn.jiguang.sdk.bean.device.DeviceSetParam;
import cn.jiguang.sdk.bean.push.PushSendParam;
import cn.jiguang.sdk.bean.push.PushSendResult;
import cn.jiguang.sdk.bean.push.options.Options;
import cn.jiguang.sdk.bean.push.other.CidGetResult;
import cn.jiguang.sdk.enums.platform.Platform;
import cn.jiguang.sdk.exception.ApiErrorException;
import com.google.common.collect.Maps;
import com.sohu.common.core.enums.PushJiGuangBizTypeEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.JsonUtils;
import com.sohu.common.core.utils.SpringUtils;
import com.sohu.resource.config.JiguangProperties;
import com.sohu.resource.domain.bo.SohuJiguangPushBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/4/24
 */
@Slf4j
public class JiguangUtil {

    /**
     * 默认推送请求下发通道配置
     * distribution通知栏消息下发逻辑：secondary_push：表示推送优先走极光，极光不在线再走厂商，厂商作为辅助（建议此种方式）。
     * distribution_fcm通知栏消息 fcm+ 国内厂商组合类型下发逻辑：secondary_pns_push：表示针对 fcm+ 国内厂商组合类型用户，推送优先走极光，极光不在线再走厂商通道，厂商作为辅助。
     */
    private static final Map<String, Object> DEFAULT_CHANNEL_CONFIG_MAP = Map.of(
            "distribution", "secondary_push"
    );


    /**
     * 获取cid
     *
     * @return
     */
    public static String getCid() {
        PushApi pushApi = SpringUtils.getBean(PushApi.class);
        CidGetResult cidForPush = null;
        try {
            cidForPush = pushApi.getCidForPush(1);
            if (CollectionUtils.isEmpty(cidForPush.getCidList())) {
                throw new ServiceException("推送失败，原因：未能获取极光cid");
            }
        } catch (ApiErrorException e) {
            log.error("jiguang get cid error, e = ", e);
            String msg = e.getApiError().getError().getMessage();
            throw new ServiceException(String.format("极光获取cid失败，原因:%s", msg));
        }
        return cidForPush.getCidList().get(0);
    }

    /**
     * 发送消息通知
     */
    public static void sendPush(SohuJiguangPushBo jiguangPushBo) {
        log.info("商品极光 jiguangPushBo:{}", JsonUtils.toJsonString(jiguangPushBo));
        PushSendParam param = new PushSendParam();
        // 指定通知内容
        param.setNotification(jiguangPushBo.getNotification());
        // 指定目标人群
        param.setAudience(jiguangPushBo.getAudience());
        // 指定平台
        param.setPlatform(Arrays.asList(Platform.android, Platform.ios));
        //指定cid
        String cid = getCid();
        param.setCid(cid);
        //选项
        Options options = new Options();
        Map<String, Object> thirdPartyChannel = getThirdPartyChannel(jiguangPushBo.getBizType());
        options.setThirdPartyChannel(thirdPartyChannel);
        JiguangProperties jiguangProperties = SpringUtils.getBean(JiguangProperties.class);
        options.setApnsProduction(jiguangProperties.getApnsProduction());
        /**
         * 极光不对指定的消息类型进行判断或校准，会以开发者自行指定的消息类型适配 Android 厂商通道。不填默认为 0。
         * 0：代表运营消息。
         * 1：代表系统消息。
         */
        options.setClassification(0);
        /**
         * 推送当前用户不在线时，为该用户保留多长时间的离线消息，以便其上线时再次推送。
         * 默认 86400 （1 天）
         */
        options.setTimeToLive(86400L);
        param.setOptions(options);

        // 发送
        try {
            log.info("jiguang push request :{}", JsonUtils.toJsonString(param));
            PushApi pushApi = SpringUtils.getBean(PushApi.class);
            PushSendResult result = pushApi.send(param);
            log.info("jiguang push msg result = {}", JsonUtils.toJsonString(result));
        } catch (ApiErrorException e) {
            log.error("jiguang push msg error, e = ", e);
            String msg = e.getApiError().getError().getMessage();
            throw new ServiceException(String.format("极光推送失败，原因:%s", msg));
        }
    }

    /**
     * 推送请求下发通道
     *
     * @return
     */
    private static Map<String, Object> getThirdPartyChannel(PushJiGuangBizTypeEnum bizType) {
        Map<String, Object> thirdPartyChannel = Maps.newHashMap();
        //小米
        Map<String, Object> xiaomi = getDefaultChannelConfigMap();
        xiaomi.put("channel_id", bizType.getXmChannelId());     //TODO 变化
        thirdPartyChannel.put("xiaomi", xiaomi);

        //华为
        Map<String, Object> huawei = getDefaultChannelConfigMap();
        huawei.put("receipt_id", "RCPB8D114B2");
        huawei.put("importance", "HIGH");
        huawei.put("category", bizType.getHwCategory());  //TODO 变化
        huawei.put("target_user_type", 0);
        thirdPartyChannel.put("huawei", huawei);
        //荣耀
        Map<String, Object> honor = getDefaultChannelConfigMap();
        thirdPartyChannel.put("honor", honor);
        //oppo
        Map<String, Object> oppo = getDefaultChannelConfigMap();
        oppo.put("channel_id", "100005");
        thirdPartyChannel.put("oppo", oppo);
        //vivo
        Map<String, Object> vivo = getDefaultChannelConfigMap();
        vivo.put("callback_id", "3702");
        vivo.put("push_mode", 0);
        thirdPartyChannel.put("vivo", vivo);
        return thirdPartyChannel;
    }

    /**
     * 获取默认下发渠道配置
     *
     * @return
     */
    private static Map<String, Object> getDefaultChannelConfigMap() {
        Map<String, Object> copyMap = Maps.newHashMap();
        copyMap.putAll(DEFAULT_CHANNEL_CONFIG_MAP);
        return copyMap;
    }


    /**
     * 设置设备信息
     * tags：支持 add, remove 或者空字符串。当 tags 参数为空字符串的时候，表示清空所有的 tags；add/remove 下是增加或删除指定的 tag；
     * alias：更新设备的别名属性；当别名为空串时，删除指定设备的别名；
     * <p>
     * FIXME:  极光API addTags、removeTags 不能为null或empty，所以调用默认传 Tag
     *
     * @param registrationId 极光登记Id
     * @param alias          别名，2024-11-8 12:20:29  改成  用户自增id,原因，因为目前是设置成手机号的，
     *                       导致报错（ 极光修改设备信息失败，原因:set alias 13026152281 exceed the limit of max numbers per uid failed.）
     * @param mobile         手机号
     * @param addTags        添加的别名
     * @param removeTags     移除的别名
     */
    public static void updateDevice(String registrationId, String alias, String mobile,
                                    List<String> addTags, List<String> removeTags) {
        DeviceSetParam deviceSetParam = new DeviceSetParam();
        deviceSetParam.setAlias(alias);
        deviceSetParam.setMobile(mobile);
        DeviceSetParam.Tags tag = new DeviceSetParam.Tags();
        tag.setAdd(addTags);
        tag.setRemove(removeTags);
        deviceSetParam.setTags(tag);
        try {
            DeviceApi deviceApi = SpringUtils.getBean(DeviceApi.class);
            deviceApi.setDevice(registrationId, deviceSetParam);
        } catch (ApiErrorException e) {
            log.info("jiguang update deviceSetParam:{}", JSONUtil.toJsonStr(deviceSetParam));
            log.error("jiguang update device error, e = ", e);
            String msg = e.getApiError().getError().getMessage();
            throw new ServiceException(String.format("极光修改设备信息失败，原因:%s", msg));
        }
    }
}
