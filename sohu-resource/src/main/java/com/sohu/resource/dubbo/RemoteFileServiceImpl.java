package com.sohu.resource.dubbo;

import cn.hutool.core.collection.CollUtil;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.utils.file.FileUtils;
import com.sohu.common.oss.core.OssClient;
import com.sohu.common.oss.entity.UploadResult;
import com.sohu.common.oss.factory.OssFactory;
import com.sohu.middle.api.service.RemoteMiddleVideoService;
import com.sohu.resource.api.RemoteFileService;
import com.sohu.resource.api.domain.SysFile;
import com.sohu.resource.domain.SysOss;
import com.sohu.resource.domain.bo.SysOssBo;
import com.sohu.resource.domain.vo.SysOssVo;
import com.sohu.resource.mapper.SysOssMapper;
import com.sohu.resource.service.ISysOssMultiPartTaskService;
import com.sohu.resource.service.ISysOssService;
import com.sohu.resource.util.VideoUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件请求处理
 *
 * <AUTHOR> Li
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteFileServiceImpl implements RemoteFileService {

    private final ISysOssService sysOssService;
    private final SysOssMapper sysOssMapper;
    private final ISysOssMultiPartTaskService sysOssMultiPartTaskService;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @DubboReference
    private RemoteMiddleVideoService remoteMiddleVideoService;

    /**
     * 文件上传请求
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysFile upload(String name, String originalFilename, String contentType, byte[] file) throws ServiceException {
        try {
            String suffix = StringUtils.substring(originalFilename, originalFilename.lastIndexOf("."), originalFilename.length());
            OssClient storage = OssFactory.instance();
            UploadResult uploadResult = storage.uploadSuffix(activeProfile, file, suffix, contentType);
            // 保存文件信息
            SysOssBo oss = new SysOssBo();
            oss.setUrl(uploadResult.getUrl());
            oss.setFileSuffix(suffix);
            oss.setFileName(uploadResult.getFileName());
            oss.setOriginalName(originalFilename);
            oss.setService(storage.getConfigKey());
            sysOssService.insertByBo(oss);
            SysFile sysFile = new SysFile();
            sysFile.setOssId(oss.getOssId());
            sysFile.setName(uploadResult.getFileName());
            sysFile.setUrl(uploadResult.getUrl());
            return sysFile;
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw new ServiceException("上传文件失败");
        }
    }

    /**
     * 通过ossId查询对应的url
     *
     * @param ossIds ossId串逗号分隔
     * @return url串逗号分隔
     */
    @Override
    public String selectUrlByIds(String ossIds) {
        return sysOssService.selectUrlByIds(ossIds);
    }

    @Override
    public List<SysFile> list(Collection<Long> ossIds) {
        List<SysOssVo> sysOssVos = sysOssService.listByIds(ossIds);
        if (CollUtil.isEmpty(sysOssVos)) {
            return null;
        }
        List<SysFile> result = new ArrayList<>();
        for (SysOssVo ossVo : sysOssVos) {
            SysFile sysFile = new SysFile();
            sysFile.setOssId(ossVo.getOssId());
            sysFile.setUrl(ossVo.getUrl());
            sysFile.setName(ossVo.getFileName());
            result.add(sysFile);
        }
        return result;
    }

    @Override
    public Map<Long, String> map(Collection<Long> ossIds) {
        if (CollUtil.isEmpty(ossIds)) {
            return new HashMap<>();
        }
        List<SysOssVo> sysOssVos = sysOssService.listByIds(ossIds);
        if (CollUtil.isEmpty(sysOssVos)) {
            return new HashMap<>();
        }
        Map<Long, String> result = new HashMap<>();
        for (SysOssVo ossVo : sysOssVos) {
            result.put(ossVo.getOssId(), ossVo.getUrl());
        }
        return result;
    }

    @Override
    public String getUrl(Long ossId) {
        SysOssVo ossVo = sysOssService.getById(ossId);
        return Objects.isNull(ossVo) ? null : ossVo.getUrl();
    }

    @Override
    public Long save(String url) {
        if (StringUtils.isBlank(url)) {
            return 0L;
        }
        List<SysOss> sysOsses = sysOssMapper.selectList(SysOss::getUrl, url);
        if (CollUtil.isNotEmpty(sysOsses)) {
            return sysOsses.get(0).getOssId();
        }
        OssClient storage = OssFactory.instance();
        SysOssBo bo = new SysOssBo();
        bo.setFileSuffix("." + FileUtils.getSuffix(url));
        bo.setFileName(StringUtils.substring(url, url.indexOf(".com/") + 5, url.length()));
        bo.setOriginalName(FileUtils.getName(url));
        bo.setService(storage.getConfigKey());
        bo.setUrl(url);
        sysOssService.insertByBo(bo);
        return bo.getOssId();
    }

    @Override
    public void clearUploadTimeoutTask() {
        sysOssMultiPartTaskService.clearUploadTimeoutTask();
    }

    @Override
    public String createPreSignedGetUrl(String originUrl, Long day) {
        return sysOssService.createPreSignedGetUrl(originUrl, day);
    }

    @Override
    public Boolean ossDelete(List<String> urlList) {
        if (CollUtil.isEmpty(urlList)) {
            return Boolean.FALSE;
        }
        List<SysOss> list = sysOssMapper.selectList(SysOss::getUrl, urlList);
        if (CollUtil.isEmpty(list)) {
            return Boolean.FALSE;
        }
        Set<Long> ids = list.stream().map(SysOss::getOssId).collect(Collectors.toSet());
        return sysOssService.deleteWithValidByIds(ids, false);
    }

    @Override
    public void handleVideoRatio(Long videoId, String videoUrl) {
        BigDecimal ratio = this.getAspectRatioByUrl(videoUrl);
        Integer ratioEnum = VideoUtils.findClosestRatio(ratio);
        remoteMiddleVideoService.updateRation(videoId, ratioEnum);
    }

    @Override
    public Integer determineAspectRatio(String videoUrl) {
        BigDecimal ratio = this.getAspectRatioByUrl(videoUrl);
        return VideoUtils.findClosestRatio(ratio);
    }

//    private BigDecimal getAspectRatioByUrl(String url) {
//        FFmpegFrameGrabber grabber = null;
//        try {
//            OssClient storage = OssFactory.instance();
//            grabber = new FFmpegFrameGrabber(storage.getObjectContent(url));
//            grabber.setOption("analyzeduration", "0");  // 减少分析时长
//            grabber.setOption("probesize", "32000");       // 缩小探测数据大小
//            grabber.setOption("skip_frame", "nokey"); // 跳过非关键帧
//            grabber.setOption("an", "null");       // 禁用音频解码
//            grabber.setOption("vn", "null");       // 禁用视频解码（仅元数据）
//            grabber.start();
//            int width = grabber.getImageWidth();
//            int height = grabber.getImageHeight();
//            return CalUtils.divide(new BigDecimal(width), height);
//        } catch (Exception e) {
//            log.info("获取数据失败，exception:{}", e.getMessage());
//            throw new ServiceException("获取数据失败");
//        } finally {
//            if (grabber != null) {
//                try {
//                    grabber.stop();     // 停止解码
//                    grabber.close();    // 关闭资源
//                } catch (Exception e) {
//                    log.warn("FFmpegFrameGrabber 资源释放失败", e);
//                }
//            }
//        }
//    }

    /**
     * 调整逻辑,防止内存溢出
     *
     * @param url
     * @return
     */
    private BigDecimal getAspectRatioByUrl(String url) {
        FFmpegFrameGrabber grabber = null;
        // 临时文件路径
        File tempFile = null;
        try {
            tempFile = File.createTempFile("oss_video_", ".mp4");
            this.downloadFromOSS(url, tempFile);
            grabber = new FFmpegFrameGrabber(tempFile.getAbsolutePath());
            grabber.setOption("analyzeduration", "0");  // 减少分析时长
            grabber.setOption("probesize", "32000");       // 缩小探测数据大小
            grabber.setOption("skip_frame", "nokey"); // 跳过非关键帧
            grabber.setOption("an", "null");       // 禁用音频解码
            grabber.setOption("vn", "null");       // 禁用视频解码（仅元数据）
            grabber.start();
            int width = grabber.getImageWidth();
            int height = grabber.getImageHeight();
            return CalUtils.divide(new BigDecimal(width), height);
        } catch (Exception e) {
            log.info("获取数据失败，exception:{}", e.getMessage());
            throw new ServiceException("获取数据失败");
        } finally {
            if (grabber != null) {
                try {
                    grabber.stop();     // 停止解码
                    grabber.close();    // 关闭资源
                } catch (Exception e) {
                    log.warn("FFmpegFrameGrabber 资源释放失败", e);
                }
            }
            // 3. 删除临时文件
            deleteTempFile(tempFile);
        }
    }

    /**
     * 下载视频到硬盘
     *
     * @param url
     * @param outputFile
     */
    private void downloadFromOSS(String url, File outputFile) {
        try {
            OssClient storage = OssFactory.instance();
            InputStream ossInputStream = storage.getObjectContent(url);
            FileOutputStream fos = new FileOutputStream(outputFile);
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = ossInputStream.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            throw new RuntimeException("下载视频失败", e);
        }
    }

    /**
     * 删除临时文件
     *
     * @param file
     */
    private void deleteTempFile(File file) {
        if (file.exists() && !file.delete()) {
            System.err.println("警告：临时文件删除失败 - " + file.getAbsolutePath());
        }
    }

}
