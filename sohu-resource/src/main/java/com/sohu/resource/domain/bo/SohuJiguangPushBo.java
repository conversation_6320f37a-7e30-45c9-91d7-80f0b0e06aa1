package com.sohu.resource.domain.bo;

import cn.hutool.core.collection.CollectionUtil;
import cn.jiguang.sdk.bean.push.audience.Audience;
import cn.jiguang.sdk.bean.push.message.notification.NotificationMessage;
import cn.jiguang.sdk.enums.platform.Platform;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sohu.common.core.enums.AppPathTypeEnum;
import com.sohu.common.core.enums.PushJiGuangBizTypeEnum;
import com.sohu.resource.enums.JPushAudienceTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 极光推送
 *
 * <AUTHOR>
 * @since 2024/4/24
 */
@Data
public class SohuJiguangPushBo implements Serializable {

    private static final long serialVersionUID = -6692754969428902201L;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 通知内容
     */
    private String alert;

    /**
     * 推送目标，{@link JPushAudienceTypeEnum}
     */
    private List<String> target = Lists.newArrayList();

    /**
     * 推送目标类型，{@link JPushAudienceTypeEnum}
     */
    private JPushAudienceTypeEnum audienceType;

    /**
     * 跳转路径
     */
    private AppPathTypeEnum jumpPathType;

    /**
     * 业务类型
     */
    private PushJiGuangBizTypeEnum bizType;

    /**
     * 参数信息
     */
    private Map<String, Object> parameters;

    /**
     * voip参数
     */
    private Map<String, String> voipParam;


    /**
     * 用于防止 api 调用端重试造成服务端的重复推送而定义的一个标识符
     */
    private String cid;


    public SohuJiguangPushBo withRegistrationIds(List<String> registrationIds) {
        this.setTarget(registrationIds);
        this.setAudienceType(JPushAudienceTypeEnum.REGISTRATION_ID);
        return this;
    }

    public SohuJiguangPushBo withTags(List<String> tags) {
        this.setTarget(tags);
        this.setAudienceType(JPushAudienceTypeEnum.TAG);
        return this;
    }


    /**
     * 获取推送设备
     */
    public Object getAudience() {
        if (JPushAudienceTypeEnum.ALL != this.audienceType
                && CollectionUtil.isEmpty(target)) {
            throw new IllegalArgumentException("推送目标缺少参数，请重新核对数据");
        }
        Audience audience = new Audience();
        switch (this.audienceType) {
            case ALL: {
                return "all";
            }
            case TAG: {
                audience.setTagOrList(target);
                break;
            }
            case ALIAS: {
                audience.setAliasList(target);
                break;
            }
            case REGISTRATION_ID: {
                audience.setRegistrationIdList(target);
                break;
            }
        }
        return audience;
    }

    /**
     * 获取通知内容
     */
    public NotificationMessage getNotification() {
        //安卓推送内容
        NotificationMessage.Android androidNotification = new NotificationMessage.Android();
        androidNotification.setTitle(this.getTitle());
        androidNotification.setAlert(this.alert);
        Map<String, Object> androidExt = getExtraMap(Platform.android);
        androidNotification.setExtras(androidExt);
        androidNotification.setPriority(2);
        androidNotification.setSound("default");
        //设置跳转路径
        if (Objects.nonNull(this.getJumpPathType())
                && StringUtils.isNotBlank(this.getJumpPathType().getAndroid())) {
            NotificationMessage.Android.Intent intent = new NotificationMessage.Android.Intent();

            String androidPath = this.jumpPathType.fullParams(AppPathTypeEnum::getAndroid, this.getParameters());
            intent.setUrl(androidPath);
            androidNotification.setIntent(intent);
        }

        //ios推送内容
        NotificationMessage.IOS iosNotification = new NotificationMessage.IOS();
        Map<String, String> iosAlertMap = Maps.newHashMap();
        iosAlertMap.put("title", this.getTitle());
        iosAlertMap.put("body", this.getAlert());
        iosNotification.setAlert(iosAlertMap);
        iosNotification.setBadge(1);
        iosNotification.setCategory("IM");
        iosNotification.setContentAvailable(true);
        Map<String, Object> iosExt = getExtraMap(Platform.ios);
        iosNotification.setExtras(iosExt);
        iosNotification.setSound("default");

        //推送内容
        NotificationMessage notificationMessage = new NotificationMessage();
        notificationMessage.setAlert(this.alert);
        notificationMessage.setAndroid(androidNotification);
        notificationMessage.setIos(iosNotification);

        // voip
        notificationMessage.setVoip(this.getVoipParam());
        return notificationMessage;
    }

    /**
     * 获取拓展信息
     */
    private Map<String, Object> getExtraMap(Platform platform) {
        if (Objects.isNull(jumpPathType)
                && MapUtils.isEmpty(this.getParameters())) {
            return Maps.newHashMapWithExpectedSize(0);
        }
        Map<String, Object> extraMap = Maps.newHashMap();
        String jumpPathName = null;
        if (Objects.nonNull(jumpPathType)) {
            switch (platform) {
                case android: {
                    jumpPathName = jumpPathType.getAndroid();
                    break;
                }
                case ios: {
                    jumpPathName = jumpPathType.getIos();
                    break;
                }
            }
        }
        extraMap.put("jumpPathName", jumpPathName);
        extraMap.put("parameters", this.getParameters());
        return extraMap;
    }
}
