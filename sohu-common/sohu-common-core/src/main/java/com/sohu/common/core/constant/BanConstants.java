package com.sohu.common.core.constant;

/**
 * 封禁相关常量
 *
 * <AUTHOR>
 */
public class BanConstants {

    /**
     * 封禁任务Redis ZSet Key
     */
    public static final String BAN_TASKS_ZSET_KEY = "ban:tasks:zset";

    /**
     * 封禁记录缓存Key前缀
     */
    public static final String BAN_RECORD_CACHE_KEY = "ban:record:";

    /**
     * 用户封禁缓存Key前缀
     */
    public static final String USER_BAN_KEY = "ban:user:";

    /**
     * IP封禁缓存Key前缀
     */
    public static final String IP_BAN_KEY = "ban:ip:";

    /**
     * 设备封禁缓存Key前缀
     */
    public static final String DEVICE_BAN_KEY = "ban:device:";

    /**
     * 策略绑定的接口列表
     */
    public static final String BAN_POLICY = "ban:policy:";

}
