package com.sohu.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * 业务类型
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BusyType {
    Article("Article", "图文"),
    House("House", "房产"),
    Video("Video", "视频"),
    User("User", "用户"),
    Shop("Shop", "店铺"),
    ShopInfo("ShopInfo", "店铺详情"),
    Goods("Goods", "商品"),
    Question("Question", "问题"),
    Answer("Answer", "回答"),
    Poetry("Poetry", "诗歌"),
    Prose("Prose", "散文"),
    Content("Content", "内容"),
    ReceiveBusyTask("ReceiveBusyTask", "申请接单"),
    SettleBusyTask("SettleBusyTask", "申请商单结算"),


    // 废弃
    Project("Project", "项目"),
    Professional("Professional", "专业服务"),
    BusyModel("BusyModel", "生意模式"),
    Card("Card", "资源卡片"),
    Comment("Comment", "评论"),
    Role("Role", "角色"),
    // 废弃
    BusyOrder("BusyOrder", "商单"),
    BusyTask("BusyTask", "任务"),
    BusyTaskFlow("BusyTaskFlow", "流量商单"),
    BusyTaskCommon("BusyTaskCommon", "通用商单"),
    BusyPlaylet("BusyPlaylet", "短剧"),
    ShortPlay("ShortPlay", "短剧"),
    // 火山引擎视频 -废弃
    Volcengine("Volcengine", "火山引擎视频"),
    Entry("Entry", "入驻认证"),
    @Deprecated
    EntryIndustry("EntryIndustry", "角色行业认证"),
    ShareVideo("ShareVideo", "视频分享"),
    GoodsWindow("GoodsWindow", "商品厨窗"),
    EntryRole("EntryRole", "角色认证"),
    SohuLesson("SohuLesson", "狐少少课堂"),
    Playlet("Playlet", "剧集"),
    PlayletVideo("PlayletVideo", "剧集单集"),
    Window("Window", "橱窗"),
    FINANCE_SHARE("FINANCE_SHARE", "FINANCE_SHARE"),
    Like("Like", "点赞"),
    Collect("Collect", "收藏"),
    AdInfo("AdInfo", "广告"),
    UserAddress("UserAddress", "用户收货地址"),
    LessonLabel("LessonLabel", "狐少少课堂标签"),
    HotContent("HotContent", "热点内容"),
    Currency("Currency", "币种"),
    City("City", "城市"),
    Category("Category", "分类"),

    CountryCode("CountryCode", "国家区号"),
    GameNovel("GameNovel", "游戏网文"),
    PM_SHARE_PUB("PmSharePub", "分销商单"),
    Novel("Novel","小说"),
    Bond("Bond", " 保证金"),

    Invite("Invite", " 邀请"),
    GoodsCategory("GoodsCategory", "商品分类"),
    ;

    private String type;
    private String description;

    public static final Map<String, BusyType> mapBusyType = new HashMap<>();
    public static final Map<String, String> mapBusyDesc = new HashMap<>();

    static {
        for (BusyType busyType : BusyType.values()) {
            mapBusyType.put(busyType.getType(), busyType);
            mapBusyDesc.put(busyType.getType().toLowerCase(), busyType.getDescription());
        }
    }

    // 根据 code 获取枚举实例的静态方法
    public static BusyType fromCode(String code) {
        for (BusyType type : BusyType.values()) {
            if (type.getType().equals(code)) {
                return type;
            }
        }
        return null;
    }

}
