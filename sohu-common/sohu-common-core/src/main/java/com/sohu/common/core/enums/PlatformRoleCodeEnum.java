package com.sohu.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 平台角色编码
 */
@Getter
@AllArgsConstructor
public enum PlatformRoleCodeEnum {

    /**
     * admin-超级管理员
     */
    ADMIN("admin", "超级管理员服务中心","超级管理员"),
    /**
     * life-本地生活商家
     */
    @Deprecated
    LIFE("life", "本地生活商家服务中心","本地生活商家"),
    /**
     * cityStationAgent-城市站长
     */
    CityStationAgent("cityStationAgent", "城市站长服务中心","城市站长"),
    /**
     * countryStationAgent-国家站长
     */
    CountryStationAgent("countryStationAgent", "国家站长服务中心","国家站长"),
    /**
     * project-项目方
     */
    @Deprecated
    Project("project", "项目方服务中心","项目方"),
    /**
     * article-创作者
     */
    Article("article", "创作者服务中心","创作者"),
    /**
     * conversion-转化方
     */
    @Deprecated
    Conversion("conversion", "转化方服务中心","转化方"),
    /**
     * server-服务方
     */
    @Deprecated
    Server("server", "服务方服务中心","服务方"),
    /**
     * agent--代理服务商(后续通过不同服务商类型来做区分)
     */
    Agent("agent", "企业主服务中心","企业主"),
    /**
     * mcn-MCN机构
     */
    MCN("mcn", "MCN机构服务中心","MCN机构"),
    /**
     * professor-拆单方服务中心
     */
    @Deprecated
    Professor("professor", "拆单方服务中心","拆单方"),
    /**
     * open-开放平台
     */
    OPEN("open", "开放平台服务中心","开放平台"),
    /**
     * shop-店铺商家
     */
    SHOP("shop", "狐少少店铺商家服务中心","狐少少店铺商家"),
    /**
     * merchant-海外短剧商户
     */
    MERCHANT("merchant", "海外短剧商家服务中心","海外短剧商户"),

    IM_TENANT("imTenant","IM租户服务中心","IM租户");


    /**
     * 角色编码
     */
    private final String code;
    /**
     * 名字
     */
    private final String name;

    /**
     * 副名称(简称)
     */
    private final String subName;

    public static final Map<String, String> map = new HashMap<>();
    /**
     * 存放副名称(简称)
     */
    public static final Map<String, String> subMap = new HashMap<>();

    static {
        for (PlatformRoleCodeEnum codeEnum : PlatformRoleCodeEnum.values()) {
            map.put(codeEnum.getCode().toLowerCase(), codeEnum.getName());
            subMap.put(codeEnum.getCode(), codeEnum.getSubName());
        }
    }

}
