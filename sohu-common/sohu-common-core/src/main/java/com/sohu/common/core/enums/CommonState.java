package com.sohu.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 通用状态
 * 状态（Edit=草稿 WaitApprove=审核中 OnShelf=上架  OffShelf=下架  CompelOff=强制下架  Refuse=审核拒绝）
 */
@Getter
@AllArgsConstructor
public enum CommonState {

    Edit("Edit", "草稿"),
    WaitRobotApprove("WaitRobotApprove", "机审中"),
    WaitApprove("WaitApprove", "审核中"),
    OnShelf("OnShelf", "上架"),
    Pass("Pass", "通过"),
    WaitShelf("WaitShelf", "待上架"),
    OffShelf("OffShelf", "下架"),
    CompelOff("CompelOff", "强制下架"),
    Refuse("Refuse", "审核拒绝"),
    Delete("Delete", "刪除"),
    ForceDelete("ForceDelete", "强制删除"),
    Exceed("Exceed", "已过期"),
    Hide("Hide", "隐藏"),
    FINISH("Finish","完成"),
    AuthFail("AuthFail","认证未过"),
    CloseApprove("CloseApprove","关店审核中"),
    CloseRefuse("CloseRefuse","关店审核拒绝"),
    ClosePass("ClosePass","关店审核通过"),
    WaitPay("WaitPay", "待支付"),
    Paid("Paid", "已支付"),
    FreezeUpgrade("FreezeUpgrade", "冻结升级中"),
    ;

    private String code;
    private String desc;

    public static final Map<String, String> map = new HashMap<>();

    static {
        for (CommonState commonState : CommonState.values()) {
            map.put(commonState.getCode(), commonState.getDesc());
        }
    }


}
