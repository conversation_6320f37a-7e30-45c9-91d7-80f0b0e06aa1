package com.sohu.common.core.enums;

/**
 * 系统通知
 */
public class SystemNoticeEnum {

    /**
     * 类型：
     * 实名认证-personalAuth
     * 银行卡-bank
     * 角色认证-roleAuth
     * 行业申请-industryApply
     * 账号安全-account
     * 其他-other
     * 举报-report
     * 内容下架-contentCompelOff
     * 保证金-bail
     * 封禁警告-warn
     */
    public enum Type {
        personalAuth,
        invite,
        roleAuth,
        interview,
        industryApply,
        account,
        other,
        report,
        bank,
        contentCompelOff,
        bail,
        withdraw,
        warn
    }

    public enum SubType {
        // 实名认证
        personalAuthSubmit,
        personalAuthPass,
        personalAuthNotPass,
        //银行卡绑定
        bankBind,

        // 角色认证
        roleAuthSubmit,
        roleAuthPass,
        roleAuthNotPass,

        // 举报
        report,
        reportOffShelf,
        reportPass,
        reportRefuse,

        // 行业申请
        industryApply,
        industryApplyPass,
        industryApplyNotPass,

        // 账户安全
        updatePayPwd,
        updatePwd,
        updatePhone,
        updateEmail,

        //内容下架
        articleCompelOff,
        videoCompelOff,
        questionCompelOff,

        //保证金
        bailPayWarn,
        bailPayPass,
        withdrawPass
    }

    /**
     * 实名认证提交
     */
    public static final String personalAuthSubmit = "实名认证提交";

    /**
     * 实名认证审核通过
     */
    public static final String personalAuthPass = "实名认证审核通过";

    /**
     * 实名认证审核未通过
     */
    public static final String personalAuthNotPass = "实名认证审核未通过";

    public static final String personalAuthSubmitDesc = "您已成功提交实名认证申请。我们将在 24 小时内完成审核，审核结果将通过系统消息通知您。请您耐心等待。感谢您的支持！";

    public static final String personalAuthPassDesc = "您的实名认证申请已通过审核。您的账号信息如下：";

    public static final String personalAuthPassLinkTitle = "使用电脑访问以下地址体验更多功能！";

    public static final String personalAuthPassLinkUrl = "https://sohuglobal.com";

    public static final String personalAuthNotPassDesc = "您的实名认证申请未通过审核";

    public static final String notPassLinkTitle = "如您有疑问，请联系我们。感谢您的支持！";

    public static final String BIND_BANK_CARD="绑定银行卡";
    public static final String BIND_BANK_CARD_DESC="您还未绑定银行卡，绑定银行卡即可完成接单提现。";

    /**
     * 角色认证提交
     */
    public static final String roleAuthSubmit = "角色认证提交";

    /**
     * 角色认证审核通过
     */
    public static final String roleAuthPass = "角色认证审核通过";

    /**
     * 角色认证审核未通过
     */
    public static final String roleAuthNotPass = "角色认证审核未通过";

    public static final String roleAuthSubmitDesc = "您已成功提交角色认证申请。我们将在 24 小时内完成审核，审核结果将通过系统消息通知您。请您耐心等待。感谢您的支持！";

    public static final String roleAuthPassDesc = "您的%s角色认证申请已通过审核。可前往PC端-头像-工作台，或者PC端直接点击本条通知，进入角色工作台。";

    public static final String roleAuthPassLinkTitle = "https://sohuglobal.com";

    public static final String roleAuthPassLinkUrl = "https://sohuglobal.com";

    public static final String roleAuthFailLinkUrl = "https://sohuglobal.com";

    public static final String roleAuthNotPassDesc = "您申请的%s角色认证申请未通过审核。";

    public static final String updatePayPwd = "修改支付密码成功";

    public static final String updatePayPwdDesc = "您的支付密码修改成功。请妥善保管您的密码，不要将密码告诉他人。";

    public static final String updatePwd = "登录密码修改成功";

    public static final String updatePwdDesc = "您的登录密码修改成功。请妥善保管您的密码，不要将密码告诉他人。";

    public static final String updatePhone = "手机号修改成功";

    public static final String updatePhoneDesc = "您的手机号修改成功。可直接使用%s用于账号登录、修改密码等操作。";

    public static final String updateEmail = "邮箱地址修改成功";

    public static final String updateEmailDesc = "您的邮箱地址修改成功。可直接使用%s用于账号登录、修改密码等操作。";

    public static final String invite = "邀请新用户成功";

    public static final String inviteDesc = "您成功邀请了一位新用户注册 。感谢您使用 ！";

    public static final String interviewPass = "站内链接访问申请通过";

    public static final String interviewPassDesc = "您申请的链接站内跳转审核通过";

    public static final String interviewRefuse = "站内链接访问申请未通过";

    public static final String interviewRefuseDesc = "您申请的链接站内跳转审核未通过";


    public static final String report = "内容被举报，已违规下架";

    public static final String reportOffShelf = "内容已被强制下架";

    public static final String reportStatus = "已下架";

    public static final String reportResult = "举报结果反馈";

    public static final String reportPass = "举报通过，已被处理";

    public static final String reportRefuse = "举报未通过";

    /**
     * 新增行业申请
     */
    public static final String industryApply = "新增行业申请";

    /**
     * 新增行业申请审核通过
     */
    public static final String industryApplyPass = "新增行业申请审核通过";

    /**
     * 新增行业申请审核未通过
     */
    public static final String industryApplyNotPass = "新增行业申请审核未通过";

    public static final String industryApplyDesc = "您已成功提交新增行业申请。我们将在 24 小时内完成审核，审核结果将通过系统消息通知您。请您耐心等待。感谢您的支持！";

    public static final String industryApplyPassDesc = "您认证的%s行业已通过审核。";

    public static final String industryApplyNotPassDesc = "您认证新增%s行业未通过审核。";

    /**
     * 强制下架通知
     */
    public static final String CONTENT_COMPEL_OFF_TITLE = "内容被下架";
    public static final String CONTENT_COMPEL_OFF_DESC = "尊敬的用户您好，您的作品%s因%s原因已被系统下架，请修改后再申请上架。";

    /**
     * 保证金缴纳提醒-标题
     */
    public static final String bailPayWarnTitle = "保证金缴纳提醒";
    /**
     * 保证金缴纳成功-标题
     */
    public static final String bailPayPassTitle = "保证金缴纳成功";

    /**
     * 保证金缴纳提醒-描述
     */
    public static final String bailPayWarnDesc = "您拥有企业主角色，缴纳保证金即可参加商品出海活动。可前往PC端-头像-工作台，或者PC端直接点击本条通知，缴纳保证金。如有疑问，可联系在线客服。感谢您的支持！";

    /**
     * 保证金缴纳成功-描述
     */
    public static final String bailPayPassDesc = "企业主保证金缴纳成功，稍后将会有官方账号联系您，注意留意IM消息。如您有疑问，可联系在线客服。感谢您的支持！";

    public static final String withdraw = "提现成功";
    /**
     * 提现成功-描述
     */
    public static final String withdrawDesc = "预计1-3个工作日到账，休息日、法定节假日顺延";

    /**
     * 封禁警告-标题
     */
    public static final String warnTitle = "封禁警告";

    /**
     * 封禁警告-描述
     */
    public static final String warnDesc = "您因%s原因，违反平台规定，如继续违反平台规定，会触发处罚，如有疑问，请咨询平台客服。";

}
