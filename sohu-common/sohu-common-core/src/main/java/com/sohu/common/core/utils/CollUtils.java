package com.sohu.common.core.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Collection;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CollUtils extends CollUtil {

    /**
     * 忽略大小写判断集合是否包含字符串
     */
    public static boolean containsIgnoreCase(Collection<String> collection, String value) {
        return CollUtil.isNotEmpty(collection) &&
                collection.stream().anyMatch(item -> StrUtil.equalsIgnoreCase(item, value));
    }

}
