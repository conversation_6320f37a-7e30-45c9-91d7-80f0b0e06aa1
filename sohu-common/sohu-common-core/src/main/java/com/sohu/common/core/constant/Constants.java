package com.sohu.common.core.constant;

import java.math.BigDecimal;

/**
 * 通用常量信息
 *
 * <AUTHOR> Li
 */
public interface Constants {

    /**
     * 默认批次数量(公用)
     */
    int DEFAULT_BATCH_SIZE = 1000;

    /**
     * UTF-8 字符集
     */
    String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    String GBK = "GBK";

    /**
     * www主域
     */
    String WWW = "www.";

    /**
     * http请求
     */
    String HTTP = "http://";

    /**
     * https请求
     */
    String HTTPS = "https://";

    /**
     * 成功标记
     */
    Integer SUCCESS = 200;

    /**
     * 失败标记
     */
    Integer FAIL = 500;

    /**
     * 登录成功状态
     */
    String LOGIN_SUCCESS_STATUS = "0";

    /**
     * 登录失败状态
     */
    String LOGIN_FAIL_STATUS = "1";

    /**
     * 登录成功
     */
    String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    String LOGOUT = "Logout";

    /**
     * 注册
     */
    String REGISTER = "Register";

    /**
     * 登录失败
     */
    String LOGIN_FAIL = "Error";

    /**
     * 验证码有效期（分钟）
     */
    long CAPTCHA_EXPIRATION = 5;

    /**
     * 查看个人隐私信息的验证码有效期（分钟）
     */
    long PRIVACY_EXPIRATION = 5;

    /**
     * 防重提交 redis key
     */
    String REPEAT_SUBMIT_KEY = "repeat_submit:";

    String ACCESS_TOKEN = "accessToken";

    /**
     * 阿里云OSS 域名
     */
    String DOMAIN_APP = "https://sohugloba.oss-cn-beijing.aliyuncs.com/app";

    //升序排序
    String SORT_ASC = "asc";

    //降序排序
    String SORT_DESC = "desc";

    String Y = "Y";

    String N = "N";

    String NONE = "none";

    String SYSTEM = "system";

    String TRUE = "true";

    String FALSE = "false";

    String ALL = "all";

    /**
     * 获取Ip地址信息url
     */
    String IP_INFO_URI = "https://ipapi.co/{}/json";

    /**
     * 默认头像 - 灰色背景
     */
    String DEFAULT_AVATAR = "https://sohugloba.oss-cn-beijing.aliyuncs.com/app/default_avatar_new.png";

    /**
     * 注销昵称
     */
    String DEFAULT_USER_NICKNAME = "用户已注销";

    /**
     * 狐少少默认头像  - 不是灰色背景
     */
    String DEFAULT_USER_AVATAR = "https://sohugloba.oss-cn-beijing.aliyuncs.com/app/default_avatar_new.png";

    /**
     * 短剧默认头像
     */
    String PLAYLET_DEFAULT_USER_AVATAR = "https://s3.dualstack.ap-southeast-1.amazonaws.com/sohuglobal/2024/10/22/cef9ac5388094e418f1c7e586225e29c_60x60.png";

    String DEFAULT_GROUP_LOGO = "https://sohugloba.oss-cn-beijing.aliyuncs.com/2024/11/22/cc96fc7c72cb4c9a8fa7662c0efb3305_84x84.png";

    /**
     * pc渠道
     */
    String CHANNEL_PC = "PC";

    /**
     * 线上渠道
     */
    String CHANNEL_MOBILE = "Mobile";

    String DEFAULT_CODE = "sohu123654987";

    /**
     * 参与位运算
     */
    long CALCULATE = 3141592653L;

    int INSTITUTION_EXPIRATION = 10;

    String STR_1 = "12345678901234567890";

    int ZERO = 0;

    int ONE = 1;

    int TWO = 2;

    int THRID = 3;

    int FOUR = 4;

    /**
     * 默认排序值
     */
    int DEFAULT_SORT_INDEX = 999;

    /**
     * 翼码请求成功状态码
     */
    String YIMAO_SUCCESS_CODE = "0000";

    /**
     * 安卓路径前缀
     */
    String ANDROID_PREFIX_PATH = "intent:#Intent;component=com.sohuglobal.world";

    //场景ID 默认视频时长，单位为秒，300秒
    String DEFAULT_VIDEO_TIME = "300";

    /**
     * 用于接口校验
     */
    String API_CHECK_PASSWORD = "sohuglobal123";

    /**
     * 系统来源
     */
    @Deprecated
    String SYS_SOURCE = "Syssource";

    /**
     * 系统来源-sohuglobal
     */
    String SOHUGLOBAL = "sohuglobal";

    /**
     * 系统来源-minglereels
     */
    String MINGLEREELS = "minglereels";

    /**
     * 系统来源 - hi狐
     */
    String HIFOCUS = "hifocus";

    /**
     * 有瓜故事会
     */
    String YOUGUA = "yougua";

    /**
     * 捕鱼游戏
     */
    String CATCHFISH = "catchfish";

    /**
     * 数据来源-第三方
     */
    String THIRDPARTY = "thirdparty";

    /**
     * 语言
     */
    String LANG = "Lang";

    /**
     * 游客ID
     */
    String UUID = "Uuid";

    /**
     * 设备系统类型：Android，ios
     */
    String PLATFORM = "Platform";

    /**
     * 移动端版本号：1.0.0
     */
    String VERSION = "Version";
    /**
     * 机型
     */
    String CHANNEL = "Channel";

    /**
     * 系统通知详情类型-国内短信
     */
    String NOTIFICATION_DETAIL_TYPE_SMS = "sms";

    /**
     * 系统通知详情类型-邮箱
     */
    String NOTIFICATION_DETAIL_TYPE_EMAIL = "email";

    /**
     * 系统通知详情类型-国外短信
     */
    String NOTIFICATION_DETAIL_TYPE_OVERSEA_SMS = "overseaSms";

    /**
     * 后端环境标识
     */
    String PROFILE_PROD = "prod";

    /**
     * 金额：金币 比例
     */
    BigDecimal VIRTUAL_AMOUNT_RATIO = new BigDecimal(100);

    /**
     * 金币：金融 比例
     */
    BigDecimal AMOUNT_VIRTUAL_RATIO = new BigDecimal(0.01);

    /**
     * 增加
     */
    String ADD = "add";

    /**
     * 减少
     */
    String SUB = "sub";

    /**
     * 银行卡正则表达式
     */
    String BANK_NO_REGEXP = "^(?:\\d{15}|\\d{16}|\\d{17}|\\d{19})$";

    /**
     * 广告标识
     */
    String ADVERTISEMENT = "advertisement";

    /**
     * 更新广告广告延迟时间
     */
    Long AD_DELAY_LONG = 5L;
}
