package com.sohu.entry.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.common.core.enums.PlatformRoleCodeEnum;
import com.sohu.common.core.enums.RoleCodeEnum;
import com.sohu.common.core.enums.SystemNoticeEnum;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.entry.api.bo.SohuAccountEnterBo;
import com.sohu.entry.api.vo.SohuAccountEnterVo;
import com.sohu.im.api.noticeContent.NoticeContentDetail;
import com.sohu.im.api.noticeContent.NoticeSystemContent;
import com.sohu.middle.api.enums.AuditState;
import com.sohu.middle.api.service.notice.RemoteMiddleSystemNoticeService;
import com.sohu.resource.api.RemoteMailService;
import com.sohu.resource.api.RemoteSmsService;
import com.sohu.system.api.RemotePlatformRoleService;
import com.sohu.system.api.RemoteUserService;
import com.sohu.system.api.vo.SysPlatformRoleVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuEntrySendMsgUtil {

    @DubboReference
    private RemoteMiddleSystemNoticeService remoteMiddleSystemNoticeService;
    @DubboReference
    private RemotePlatformRoleService remotePlatformRoleService;
    @DubboReference
    private RemoteMailService remoteMailService;
    @DubboReference
    private RemoteSmsService remoteSmsService;
    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 角色认证审核未通过发送消息通知
     */
    @Async("asyncExecutor")
    public void sendMsgOfRoleAuthNotPass(SohuAccountEnterBo bo) {
        SysPlatformRoleVo sysPlatformRoleVo = remotePlatformRoleService.selectVoByRoleKey(bo.getRoleKey());
        String roleName = sysPlatformRoleVo.getRoleName();
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.roleAuthNotPass);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.roleAuthNotPass.name());
        content.setDetailId(bo.getId());
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setId(bo.getId());
        detail.setUserId(bo.getUserId());
        detail.setDesc(String.format(SystemNoticeEnum.roleAuthNotPassDesc, roleName));
        detail.setRejectReason(bo.getRejectReason());
        detail.setLinkTitle(SystemNoticeEnum.notPassLinkTitle);
        detail.setStatus(bo.getState());
        if (bo.getPlatformId() != null) {
            detail.setLinkUrl(SystemNoticeEnum.roleAuthFailLinkUrl + "/entry/entryIndex?entryId=" + bo.getPlatformId());
        } else {
            detail.setLinkUrl(SystemNoticeEnum.roleAuthFailLinkUrl);
        }
        detail.setKeyWord(new String[]{roleName});
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(bo.getUserId(), SystemNoticeEnum.roleAuthNotPass, contentJson, SystemNoticeEnum.Type.roleAuth);
    }

    /**
     * 角色认证审核通过-保证金缴纳提醒
     */
    @Async("asyncExecutor")
    public void sendMsgOfBailPayWarn(SohuAccountEnterVo vo) {
        if (!Objects.equals(PlatformRoleCodeEnum.Agent.getCode(), vo.getRoleKey())) {
            return;
        }
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.bailPayWarnTitle);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.bailPayWarn.name());
        content.setDetailId(vo.getId());
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setId(vo.getId());
        detail.setUserId(vo.getUserId());
        detail.setDesc(SystemNoticeEnum.bailPayWarnDesc);
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(vo.getUserId(), SystemNoticeEnum.bailPayWarnTitle, contentJson, SystemNoticeEnum.Type.bail);
    }

    /**
     * 角色认证审核通过发送消息通知
     */
    @Async("asyncExecutor")
    public void sendMsgOfRoleAuthPass(SohuAccountEnterVo vo) {
        SysPlatformRoleVo sysPlatformRoleVo = remotePlatformRoleService.selectVoByRoleKey(vo.getRoleKey());
        String roleName = sysPlatformRoleVo.getRoleName();
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.roleAuthPass);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.roleAuthPass.name());
        content.setDetailId(vo.getId());
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setId(vo.getId());
        detail.setUserId(vo.getUserId());
        detail.setDesc(String.format(SystemNoticeEnum.roleAuthPassDesc, roleName));
        detail.setStatus(vo.getState());
        // detail.setLinkTitle(SystemNoticeEnum.roleAuthPassLinkTitle);
        if (vo.getPlatformId() != null) {
            detail.setLinkUrl(SystemNoticeEnum.roleAuthFailLinkUrl + "/entry/entryIndex?entryId=" + vo.getPlatformId());
            detail.setRoleKey(vo.getRoleKey());
        } else {
            detail.setLinkUrl(SystemNoticeEnum.roleAuthFailLinkUrl);
        }
        // detail.setLinkUrl(SystemNoticeEnum.roleAuthPassLinkUrl);
        detail.setKeyWord(new String[]{roleName});

        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(vo.getUserId(), SystemNoticeEnum.roleAuthPass, contentJson, SystemNoticeEnum.Type.roleAuth);
    }

    /**
     * 角色认证审核通过发送邮件
     */
    @Async("asyncExecutor")
    public void sendEmailOfEntry(SohuAccountEnterVo sohuAccountEnterVo) {
        if (sohuAccountEnterVo.getState().equals(AuditState.Pass.name())) {
            remoteMailService.send(sohuAccountEnterVo.getNotifyEmail(), String.format("%s入驻通知", PlatformRoleCodeEnum.subMap.get(sohuAccountEnterVo.getRoleKey())), String.format("尊敬的用户您好！您申请的%s已通过审核，请登录：http://admin.sohuglobal.com/，账号为：%s，初始密码为：123456，如果您已经有平台账号，请直接使用账号原有密码登录即可，请勿泄露您的账号密码注意隐私保护", PlatformRoleCodeEnum.subMap.get(sohuAccountEnterVo.getRoleKey()), sohuAccountEnterVo.getContactPhone(), sohuAccountEnterVo.getPassword()));
        } else if (sohuAccountEnterVo.getState().equals(AuditState.Refuse.name())) {
            remoteMailService.send(sohuAccountEnterVo.getNotifyEmail(), String.format("%s入驻通知", PlatformRoleCodeEnum.subMap.get(sohuAccountEnterVo.getRoleKey())), String.format("尊敬的用户您好！您申请的%s入驻未通过，未通过原因为：%s，请重新编辑再次提交审核谢谢。", PlatformRoleCodeEnum.subMap.get(sohuAccountEnterVo.getRoleKey()), sohuAccountEnterVo.getRejectReason()));
        }
    }

    /**
     * 机构角色认证审核通过发送短信
     */
    @Async("asyncExecutor")
    public void sendMsgOfEntry(SohuAccountEnterVo sohuAccountEnterVo) {
        //String phoneNumber = remoteUserService.queryById(sohuAccountEnterVo.getUserId()).getPhoneNumber();
        String templateId = null;
        LinkedHashMap<String, String> param = new LinkedHashMap<>();
        param.put("userType", PlatformRoleCodeEnum.subMap.get(sohuAccountEnterVo.getRoleKey()));
        if (sohuAccountEnterVo.getState().equals(AuditState.Pass.name())) {
            if (StringUtils.equalsAnyIgnoreCase(sohuAccountEnterVo.getRoleKey(), PlatformRoleCodeEnum.Agent.getCode())) {
                templateId = "SMS_472120101";
            } else if (StringUtils.equalsAnyIgnoreCase(sohuAccountEnterVo.getRoleKey(), PlatformRoleCodeEnum.MCN.getCode())) {
                templateId = "SMS_472125087";
            } else if (StringUtils.equalsAnyIgnoreCase(sohuAccountEnterVo.getRoleKey(), PlatformRoleCodeEnum.Professor.getCode())) {
                templateId = "SMS_464481028";
            }
            //模板未改，待修改
            param.put("phone", sohuAccountEnterVo.getContactPhone());
            param.put("password", sohuAccountEnterVo.getPassword());
            remoteSmsService.send(sohuAccountEnterVo.getContactPhone(), templateId, param);
        } else if (sohuAccountEnterVo.getState().equals(AuditState.Refuse.name())) {
            if (StringUtils.equalsAnyIgnoreCase(sohuAccountEnterVo.getRoleKey(), PlatformRoleCodeEnum.Agent.getCode())) {
                templateId = "SMS_472145108";
            } else if (StringUtils.equalsAnyIgnoreCase(sohuAccountEnterVo.getRoleKey(), PlatformRoleCodeEnum.MCN.getCode())) {
                templateId = "SMS_472155121";
            } else if (StringUtils.equalsAnyIgnoreCase(sohuAccountEnterVo.getRoleKey(), PlatformRoleCodeEnum.Professor.getCode())) {
                templateId = "SMS_464476072";
            }
            param.put("content", sohuAccountEnterVo.getRejectReason());
            remoteSmsService.send(sohuAccountEnterVo.getContactPhone(), templateId, param);
        }
    }

    /**
     * 入驻服务商审核通过 发送 邀请建立专属服务群
     */
    @Async("asyncExecutor")
    public void sendMsgOfCreateAgentGroup(SohuAccountEnterVo vo) {
        String roleKey = vo.getRoleKey();
        if (!StrUtil.equalsAnyIgnoreCase(roleKey, RoleCodeEnum.Agent.getCode())) {
            return;
        }
        LoginUser user = remoteUserService.queryById(vo.getUserId());
        if (Objects.isNull(user)) {
            return;
        }
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.createAgentGroupTitle);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.createAgentGroupPass.name());
        content.setDetailId(vo.getId());
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setId(vo.getId());
        detail.setUserId(vo.getUserId());
        detail.setDesc(String.format(SystemNoticeEnum.createAgentGroupDesc, user.getUsername()));
        detail.setRejectReason(vo.getRejectReason());
        detail.setLinkTitle(SystemNoticeEnum.notPassLinkTitle);
        detail.setStatus(vo.getState());
        detail.setRoleKey(vo.getRoleKey());
        detail.setRole(RoleCodeEnum.subMap.get(vo.getRoleKey()));
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(vo.getUserId(), SystemNoticeEnum.createAgentGroupTitle, contentJson, SystemNoticeEnum.Type.createAgentGroup);
    }

}
