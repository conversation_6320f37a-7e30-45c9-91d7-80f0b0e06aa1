package com.sohu.entry;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 入驻
 *
 * <AUTHOR>
 */
@EnableDubbo
@SpringBootApplication(scanBasePackages = {"com.sohu"})
public class SohuEntryApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(SohuEntryApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  入驻模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
