package com.sohu.entry.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.sohu.admin.api.RemoteAdminService;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.enums.AppPathTypeEnum;
import com.sohu.common.core.enums.PlatformRoleCodeEnum;
import com.sohu.common.core.enums.PushJiGuangBizTypeEnum;
import com.sohu.common.core.enums.UserAuthEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.entry.api.RemoteAccountEnterService;
import com.sohu.entry.api.bo.SohuAccountEnterAptitudeBo;
import com.sohu.entry.api.bo.SohuAccountEnterAptitudeReqBo;
import com.sohu.entry.api.bo.SohuAccountEnterBo;
import com.sohu.entry.api.model.SohuAccountEnterModel;
import com.sohu.entry.api.vo.SohuAccountEnterAptitudeVo;
import com.sohu.entry.api.vo.SohuAccountEnterVo;
import com.sohu.entry.api.vo.SohuPlatformConfigVo;
import com.sohu.entry.domain.SohuAccountEnter;
import com.sohu.entry.mapper.SohuAccountEnterMapper;
import com.sohu.entry.service.*;
import com.sohu.entry.util.SohuEntrySendMsgUtil;
import com.sohu.middle.api.enums.AuditState;
import com.sohu.middle.api.service.mcn.RemoteMiddleMcnGroupService;
import com.sohu.middle.api.service.mcn.RemoteMiddleMcnUserService;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.pay.api.bo.SohuAccountBo;
import com.sohu.pay.api.enums.AccountEnum;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.resource.api.RemoteJiguangService;
import com.sohu.resource.api.domain.bo.SohuJiguangPush2UserReqBo;
import com.sohu.system.api.RemotePlatformRoleService;
import com.sohu.system.api.RemoteSysNoticeService;
import com.sohu.system.api.RemoteSysRoleService;
import com.sohu.system.api.RemoteUserService;
import com.sohu.system.api.domain.SysUser;
import com.sohu.system.api.vo.SysPlatformRoleVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 账户入驻Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-04
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuAccountEnterServiceImpl extends YmBaseService implements ISohuAccountEnterService {

    private final SohuAccountEnterMapper baseMapper;
    private final AsyncConfig asyncConfig;

//    /**
//     * 随机名称数
//     */
//    public static final int NAME_OF_RANDOM = 9;
//    /**
//     * 初始密码
//     */
//    public static final String DEFAULT_PWD = "123456";
//    /**
//     * 翼码企业商户
//     */
//    private static final String YIMA_BUSINESS_MERCHANT_TYPE = "1";
//    /**
//     * 翼码小微商户(无营业执照)
//     */
//    private static final String YIMA_PERSONAL_MERCHANT_TYPE = "3";
    /**
     * 入驻常量
     */
    private static final String ENTER = "enter";
    /**
     * 实名认证
     */
    private static final String ACCOUNT = "account";

    private final ISohuEntryAuthService iSohuEntryAuthService;

    private final ISohuAccountEnterAptitudeService iSohuAccountEnterAptitudeService;
    private final ISohuPlatformConfigService iSohuPlatformConfigService;

    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteSysRoleService remoteSysRoleService;
    @DubboReference
    private RemoteAccountService remoteAccountService;
    @DubboReference
    private RemoteJiguangService remoteJiguangService;
    @DubboReference
    private RemoteAccountEnterService remoteAccountEnterService;
    @DubboReference
    private RemoteMiddleMcnUserService remoteMcnUserService;
    @DubboReference
    private RemoteMiddleMcnGroupService remoteMiddleMcnGroupService;

    @DubboReference
    private RemotePlatformRoleService remotePlatformRoleService;

    @DubboReference
    private RemoteAdminService remoteAdminService;

    @DubboReference
    private RemoteSysNoticeService remoteSysNoticeService;

    private final SohuEntrySendMsgUtil sohuEntrySendMsgUtil;

    /**
     * 验证码禁用
     */
    @Value("${sohu.captcha.disabled:false}")
    private Boolean captchaDisabled;


    @Override
    public SohuAccountEnterVo queryById(String id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public SohuAccountEnterVo queryIncludeAccountById(String id) {
        SohuAccountEnterVo vo = this.queryById(id);
        if (Objects.isNull(vo)) {
            return vo;
        }
        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserId(vo.getUserId());
        this.buildAccountEnterByAccount(vo, sohuAccountVo);
        return vo;
    }

    @Override
    public SohuAccountEnterVo queryByUserId(Long userId) {
        LambdaQueryWrapper<SohuAccountEnter> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuAccountEnter::getUserId, userId);
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public SohuAccountEnterVo queryByUserIdAndRoleKey(Long userId, String roleKey) {
        LambdaQueryWrapper<SohuAccountEnter> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuAccountEnter::getUserId, userId);
        lqw.eq(SohuAccountEnter::getRoleKey, roleKey);
        lqw.last("limit 1");
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public SohuAccountEnterVo queryIncludeAccountByUserId(Long userId) {
        SohuAccountEnterVo vo = this.queryByUserId(userId);
        if (Objects.isNull(vo)) {
            vo = new SohuAccountEnterVo();
        }
        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserId(userId);
        this.buildAccountEnterByAccount(vo, sohuAccountVo);
        return vo;
    }

    @Override
    public SohuAccountEnterVo queryIncludeAccountByUserIdAndRoleKey(Long userId, String roleKey) {
        SohuAccountEnterVo vo = this.queryByUserIdAndRoleKey(userId, roleKey);
        if (Objects.isNull(vo)) {
            vo = new SohuAccountEnterVo();
        }
        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserId(userId);
        this.buildAccountEnterByAccount(vo, sohuAccountVo);
        return vo;
    }

    @Override
    public TableDataInfo<SohuAccountEnterVo> queryPageList(SohuAccountEnterBo bo, PageQuery pageQuery) {
//        LambdaQueryWrapper<SohuAccountEnter> lqw = buildQueryWrapper(bo);
//        Page<SohuAccountEnterVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        Page<SohuAccountEnterVo> result = baseMapper.queryPageList(PageQueryUtils.build(pageQuery), bo);
//        if (CollectionUtil.isNotEmpty(result.getRecords())) {
//            result.getRecords().forEach(f -> {
//                f.setRoleName(PlatformRoleCodeEnum.subMap.get(f.getRoleKey()));
//            });
//        }
        if (CollUtil.isNotEmpty(result.getRecords())) {
            Set<String> roleKeys = result.getRecords().stream().map(SohuAccountEnterVo::getRoleKey).collect(Collectors.toSet());
            Map<String, SysPlatformRoleVo> platformRoleMap = remotePlatformRoleService.queryByRoleKeys(roleKeys);
            for (SohuAccountEnterVo vo : result.getRecords()) {
                SysPlatformRoleVo platformRoleVo = platformRoleMap.get(vo.getRoleKey());
                if (Objects.nonNull(platformRoleVo)) {
                    vo.setRoleName(platformRoleVo.getRoleName());
                }
            }
        }

        return TableDataInfoUtils.build(result);
    }

    @Override
    public List<SohuAccountEnterVo> queryList(SohuAccountEnterBo bo) {
        LambdaQueryWrapper<SohuAccountEnter> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuAccountEnter> buildQueryWrapper(SohuAccountEnterBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuAccountEnter> lqw = Wrappers.lambdaQuery();
        lqw.eq(ObjectUtil.isNotEmpty(bo.getUserId()), SohuAccountEnter::getUserId, bo.getUserId());
//        lqw.eq(StringUtils.isNotBlank(bo.getAccountType()), SohuAccountEnter::getAccountType, bo.getAccountType());
//        lqw.eq(StringUtils.isNotBlank(bo.getMerchantId()), SohuAccountEnter::getMerchantId, bo.getMerchantId());
//        lqw.like(StringUtils.isNotBlank(bo.getMerchantName()), SohuAccountEnter::getMerchantName, bo.getMerchantName());
//        lqw.eq(StringUtils.isNotBlank(bo.getIdentityType()), SohuAccountEnter::getIdentityType, bo.getIdentityType());
//        lqw.like(StringUtils.isNotBlank(bo.getLegalName()), SohuAccountEnter::getLegalName, bo.getLegalName());
//        lqw.eq(StringUtils.isNotBlank(bo.getIdentityNo()), SohuAccountEnter::getIdentityNo, bo.getIdentityNo());
//        lqw.eq(StringUtils.isNotBlank(bo.getIdentityBeginDate()), SohuAccountEnter::getIdentityBeginDate, bo.getIdentityBeginDate());
//        lqw.eq(StringUtils.isNotBlank(bo.getIdentityEndDate()), SohuAccountEnter::getIdentityEndDate, bo.getIdentityEndDate());
//        lqw.eq(StringUtils.isNotBlank(bo.getIdentityValidityType()), SohuAccountEnter::getIdentityValidityType, bo.getIdentityValidityType());
//        lqw.eq(StringUtils.isNotBlank(bo.getContactPhone()), SohuAccountEnter::getContactPhone, bo.getContactPhone());
//        lqw.eq(StringUtils.isNotBlank(bo.getCertPhotoA()), SohuAccountEnter::getCertPhotoA, bo.getCertPhotoA());
//        lqw.eq(StringUtils.isNotBlank(bo.getCertPhotoB()), SohuAccountEnter::getCertPhotoB, bo.getCertPhotoB());
//        lqw.like(StringUtils.isNotBlank(bo.getContactName()), SohuAccountEnter::getContactName, bo.getContactName());
//        lqw.eq(StringUtils.isNotBlank(bo.getContactEmail()), SohuAccountEnter::getContactEmail, bo.getContactEmail());
//        lqw.eq(StringUtils.isNotBlank(bo.getLicensePhoto()), SohuAccountEnter::getLicensePhoto, bo.getLicensePhoto());
//        lqw.eq(StringUtils.isNotBlank(bo.getLicenseCode()), SohuAccountEnter::getLicenseCode, bo.getLicenseCode());
//        lqw.eq(StringUtils.isNotBlank(bo.getLicenseBeginDate()), SohuAccountEnter::getLicenseBeginDate, bo.getLicenseBeginDate());
//        lqw.eq(StringUtils.isNotBlank(bo.getLicenseEndDate()), SohuAccountEnter::getLicenseEndDate, bo.getLicenseEndDate());
//        lqw.eq(StringUtils.isNotBlank(bo.getLicenseValidityType()), SohuAccountEnter::getLicenseValidityType, bo.getLicenseValidityType());
//        lqw.eq(StringUtils.isNotBlank(bo.getLicenseProvinceCode()), SohuAccountEnter::getLicenseProvinceCode, bo.getLicenseProvinceCode());
//        lqw.eq(StringUtils.isNotBlank(bo.getLicenseCityCode()), SohuAccountEnter::getLicenseCityCode, bo.getLicenseCityCode());
//        lqw.eq(StringUtils.isNotBlank(bo.getLicenseAreaCode()), SohuAccountEnter::getLicenseAreaCode, bo.getLicenseAreaCode());
//        lqw.eq(StringUtils.isNotBlank(bo.getLicenseAddress()), SohuAccountEnter::getLicenseAddress, bo.getLicenseAddress());
//        lqw.eq(StringUtils.isNotBlank(bo.getCertPhotoFront()), SohuAccountEnter::getCertPhotoFront, bo.getCertPhotoFront());
//        lqw.eq(StringUtils.isNotBlank(bo.getCertPhotoBack()), SohuAccountEnter::getCertPhotoBack, bo.getCertPhotoBack());
//        lqw.eq(StringUtils.isNotBlank(bo.getQualificationPhoto()), SohuAccountEnter::getQualificationPhoto, bo.getQualificationPhoto());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuAccountEnter::getState, bo.getState());
        lqw.eq(StringUtils.isNotBlank(bo.getRejectReason()), SohuAccountEnter::getRejectReason, bo.getRejectReason());
//        lqw.like(StringUtils.isNotBlank(bo.getLicenseProvinceName()), SohuAccountEnter::getLicenseProvinceName, bo.getLicenseProvinceName());
//        lqw.like(StringUtils.isNotBlank(bo.getLicenseCityName()), SohuAccountEnter::getLicenseCityName, bo.getLicenseCityName());
//        lqw.like(StringUtils.isNotBlank(bo.getLicenseAreaName()), SohuAccountEnter::getLicenseAreaName, bo.getLicenseAreaName());
//        lqw.eq(StringUtils.isNotBlank(bo.getLicenseType()), SohuAccountEnter::getLicenseType, bo.getLicenseType());
        lqw.eq(StringUtils.isNotBlank(bo.getRoleKey()), SohuAccountEnter::getRoleKey, bo.getRoleKey());
        lqw.like(StringUtils.isNotBlank(bo.getInstitutionName()), SohuAccountEnter::getInstitutionName, bo.getInstitutionName());
        lqw.eq(StringUtils.isNotBlank(bo.getInstitutionAbout()), SohuAccountEnter::getInstitutionAbout, bo.getInstitutionAbout());
        lqw.like(StringUtils.isNotBlank(bo.getPhoneNumber()), SohuAccountEnter::getPhoneNumber, bo.getPhoneNumber());
//        lqw.eq(StringUtils.isNotBlank(bo.getPassword()), SohuAccountEnter::getPassword, bo.getPassword());
        lqw.eq(StringUtils.isNotBlank(bo.getNotifyEmail()), SohuAccountEnter::getNotifyEmail, bo.getNotifyEmail());
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            lqw.ge(SohuAccountEnter::getCreateTime, DateUtils.beginOfTime(bo.getStartTime()));
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            lqw.le(SohuAccountEnter::getCreateTime, DateUtils.endOfTime(bo.getEndTime()));
        }
        lqw.orderByDesc(SohuAccountEnter::getCreateTime);
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SohuAccountEnterBo bo) {
        Long userId = LoginHelper.getUserId();
        if (!RedisUtils.setObjectIfAbsent("account:entry:lock:" + userId, 1, Duration.ofSeconds(5))) {
            throw new ServiceException("请勿重复提交用户入驻信息");
        }
//        SohuAccountEnterVo vo = this.queryByUserIdAndRoleKey(userId, bo.getRoleKey());
//        Objects.requireNonNull(vo, "该角色入驻信息已存在，请勿重复提交");
//
//        // 获取当前用户角色
//        PlatformRoleCodeEnum currentRole = remotePlatformRoleService.getCurrentExclusiveRole(userId);
//        if (currentRole != null && (!StrUtil.equalsAnyIgnoreCase(bo.getRoleKey(), currentRole.getCode()))) {
//            throw new RuntimeException("您已入驻其它角色：" + currentRole.getSubName());
//        }
        LoginUser user = remoteUserService.selectById(LoginHelper.getUserId());
        if (Objects.isNull(user)) {
            throw new ServiceException("用户不存在");
        }
        SohuPlatformConfigVo sohuPlatformConfigVo = getSohuPlatformConfigVo(bo);

        bo.setUserId(LoginHelper.getUserId());
        bo.setContactPhone(bo.getContactPhone());
        bo.setPhoneNumber(user.getPhoneNumber());

        //SohuAccountEnterVo vo = this.queryByUserId(LoginHelper.getUserId());
        List<String> stateList = new ArrayList<>();
        stateList.add(AuditState.WaitApprove.name());
        stateList.add(AuditState.Pass.name());
        LambdaQueryWrapper<SohuAccountEnter> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuAccountEnter::getUserId, LoginHelper.getUserId());
        lqw.eq(SohuAccountEnter::getRoleKey, bo.getRoleKey());
        lqw.in(SohuAccountEnter::getState, stateList);
        lqw.last("limit 1");
        SohuAccountEnterVo enterVo = baseMapper.selectVoOne(lqw);
        if (Objects.nonNull(enterVo)) {
            throw new RuntimeException("请勿重复提交角色入驻信息");
        }
        //保存开户账号(实名认证部分)，从角色入驻里进行实名认证，不发实名认证消息
        bo.setNeedMsgNotify(false);
        this.saveAccountBo(bo);
        SohuAccountEnter add = BeanUtil.toBean(bo, SohuAccountEnter.class);
        //validEntityBeforeSave(add);
        boolean flag;
        try {
            // 执行插入数据的操作
            add.setState(AuditState.WaitApprove.name());
            flag = baseMapper.insertOrUpdate(add);
        } catch (DuplicateKeyException e) {
            // 处理重复插入的情况
            throw new RuntimeException(MessageUtils.message("auth.exists"));
        }
        if (flag) {
            bo.setId(add.getId());
        }
        // 保存入驻资质信息
        if (CollectionUtil.isNotEmpty(bo.getAptitudeList())) {
            List<SohuAccountEnterAptitudeBo> list = setAptitudeListBo(bo.getAptitudeList(), add.getId());
            iSohuAccountEnterAptitudeService.batchInsert(list);
        }
        // 角色认证提交发送消息通知
        CompletableFuture.runAsync(() -> iSohuEntryAuthService.sendMsgOfEntrySubmit(add.getId(), add.getUserId(), sohuPlatformConfigVo.getId()), asyncConfig.getAsyncExecutor());
        return flag;
    }

    @NotNull
    private SohuPlatformConfigVo getSohuPlatformConfigVo(SohuAccountEnterBo bo) {
        SohuPlatformConfigVo sohuPlatformConfigVo = this.iSohuPlatformConfigService.queryByRoleKey(bo.getRoleKey());
        if (Objects.isNull(sohuPlatformConfigVo)) {
            throw new ServiceException("角色不存在");
        }
        return sohuPlatformConfigVo;
    }

    /**
     * 保存开户账号(实名认证部分)
     *
     * @param bo
     * @return
     */
    private Boolean saveAccountBo(SohuAccountEnterBo bo) {
        SohuAccountBo sohuAccountBo = BeanUtil.copyProperties(bo, SohuAccountBo.class);
        sohuAccountBo.setId(null);
        sohuAccountBo.setState(null);
        return remoteAccountService.saveByBo(sohuAccountBo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(SohuAccountEnterBo bo) {
        Long userId = LoginHelper.getUserId();
        if (!RedisUtils.setObjectIfAbsent("account:entry:lock:" + userId, 1, Duration.ofSeconds(5))) {
            throw new ServiceException("请勿重复提交用户入驻信息");
        }
        SohuAccountEnterVo vo = this.queryByUserIdAndRoleKey(LoginHelper.getUserId(), bo.getRoleKey());
//        if (Objects.isNull(vo)) {
//            throw new RuntimeException("角色入驻信息不存在");
//        }
//        // 获取当前用户角色
//        PlatformRoleCodeEnum currentRole = remotePlatformRoleService.getCurrentExclusiveRole(LoginHelper.getUserId());
//        if (currentRole != null && (!StrUtil.equalsAnyIgnoreCase(bo.getRoleKey(), currentRole.getCode()))) {
//            throw new RuntimeException("您已入驻其它角色:" + currentRole.getSubName());
//        }
        LoginUser user = remoteUserService.selectById(LoginHelper.getUserId());
        if (Objects.isNull(user)) {
            throw new ServiceException("用户不存在");
        }
        if (!Objects.equals(vo.getId(), bo.getId())) {
            throw new RuntimeException("非法修改");
        }
        if (Objects.equals(vo.getState(), AuditState.WaitApprove.name())
                || Objects.equals(vo.getState(), AuditState.Pass.name())) {
            throw new RuntimeException("入驻失败才能修改");
        }
        bo.setContactPhone(user.getPhoneNumber());
        bo.setPhoneNumber(user.getPhoneNumber());
        SohuPlatformConfigVo sohuPlatformConfigVo = getSohuPlatformConfigVo(bo);
        //保存开户账号(实名认证部分)
        bo.setNeedMsgNotify(false);
        this.saveAccountBo(bo);
        //其它辅助信息
        bo.setState(AuditState.WaitApprove.name());
        SohuAccountEnter update = BeanUtil.toBean(bo, SohuAccountEnter.class);
        LambdaUpdateWrapper<SohuAccountEnter> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuAccountEnter::getId, bo.getId())
                .set(SohuAccountEnter::getAuditTime, null)
                .set(SohuAccountEnter::getRejectReason, null);
        boolean flag = baseMapper.update(update, luw) > 0;
        // 保存入驻资质信息
        if (CollectionUtil.isNotEmpty(bo.getAptitudeList())) {
            //删除历史的资质信息
            iSohuAccountEnterAptitudeService.deleteByEntryId(bo.getId());
            //封装资质信息
            List<SohuAccountEnterAptitudeBo> list = setAptitudeListBo(bo.getAptitudeList(), bo.getId());
            // 批量入库
            iSohuAccountEnterAptitudeService.batchInsert(list);
        }
        // 角色认证重新提交发送消息通知
        CompletableFuture.runAsync(() -> iSohuEntryAuthService.sendMsgOfEntrySubmit(update.getId(), update.getUserId(), sohuPlatformConfigVo.getId()), asyncConfig.getAsyncExecutor());
        return flag;
    }

    private List<SohuAccountEnterAptitudeBo> setAptitudeListBo(List<SohuAccountEnterAptitudeReqBo> aptitudeList, Long accountEnterId) {
        List<SohuAccountEnterAptitudeBo> list = aptitudeList.stream().map(item -> {
            SohuAccountEnterAptitudeBo aptitudeBo = new SohuAccountEnterAptitudeBo();
            aptitudeBo.setContent(item.getContent());
            aptitudeBo.setAptitudeConfigId(item.getAptitudeConfigId());
            aptitudeBo.setType(item.getType());
            aptitudeBo.setAccountEnterId(accountEnterId);
            return aptitudeBo;
        }).collect(Collectors.toList());
        return list;
    }

    private void validBoBeforeSave(SohuAccountEnterBo bo) {
        if (AccountEnum.AccountTypeEnum.BUSINESS.getCode().equals(bo.getAccountType())) {
            if (PlatformRoleCodeEnum.MCN.getCode().equals(bo.getRoleKey()) || PlatformRoleCodeEnum.Agent.getCode().equals(bo.getRoleKey())) {

            } else {
                throw new RuntimeException("当前角色不符合企业入驻条件");
            }
        } else if (AccountEnum.AccountTypeEnum.PERSONAL.getCode().equals(bo.getAccountType())) {
            if (PlatformRoleCodeEnum.Professor.getCode().equals(bo.getRoleKey())) {

            } else {
                throw new RuntimeException("当前角色不符合个人入驻条件");
            }
        } else {
            throw new RuntimeException("当前不符合入驻条件");
        }
    }

    private void validEntityBeforeSave(SohuAccountEnter entity) {
//        LambdaQueryWrapper<SohuAccountEnter> lqw = Wrappers.lambdaQuery();
//        lqw.eq(SohuAccountEnter::getUserId, entity.getUserId());
//        if (entity.getId() != null) {
//            lqw.ne(SohuAccountEnter::getState, AuditState.Refuse.name());
//        }
//        if (baseMapper.exists(lqw)) {
//            throw new RuntimeException(MessageUtils.message("auth.exists"));
//        }
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
//        if (isValid) {
//            //TODO 做一些业务上的校验,判断是否需要校验
//        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean checkUserNameUnique(String institutionName) {
        SysUser user = new SysUser();
        user.setUserName(institutionName);
        // 此时用户处于未登录状态
        //user.setUserId(LoginHelper.getUserId());

        return remoteUserService.checkUserNameUnique(user);
    }

    @Override
    public Boolean checkPhoneUnique(String phoneNumber) {
        SysUser user = new SysUser();
        user.setPhoneNumber(phoneNumber);
        // 此时用户处于未登录状态
        //user.setUserId(LoginHelper.getUserId());

        return remoteUserService.checkPhoneUnique(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean audit(SohuAccountEnterBo bo) {
        // 校验前端参数
        validateParams(bo);
        // 获取审核信息
        SohuAccountEnterVo vo = baseMapper.selectVoById(bo.getId());
        // 校验审核信息
        validateEntityBeforeAudit(vo);

        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserId(vo.getUserId());
        if (Objects.isNull(sohuAccountVo)) {
            throw new RuntimeException("实名认证信息不存在");
        }
        SohuPlatformConfigVo sohuPlatformConfigVo = this.iSohuPlatformConfigService.queryByRoleKey(vo.getRoleKey());
        if (Objects.isNull(sohuPlatformConfigVo)) {
            throw new ServiceException("入驻通道未开启");
        }
        // 审核通过
        bo.setPlatformId(sohuPlatformConfigVo.getId());
        vo.setPlatformId(sohuPlatformConfigVo.getId());
        if (AuditState.Pass.name().equals(bo.getState())) {
            // 获取当前用户角色
//            PlatformRoleCodeEnum currentRole = remotePlatformRoleService.getCurrentExclusiveRole(bo.getUserId());
//            if (currentRole != null && (!StrUtil.equalsAnyIgnoreCase(bo.getRoleKey(), currentRole.getCode()))) {
//                vo.setRejectReason("您已入驻其它角色:" + currentRole.getSubName());
//                this.updateOfRefuse(vo);
//                this.syncSohuAccount(vo.getUserId(), AuditState.Refuse.name(), vo.getRejectReason());
//                return false;
//            }
            remoteSysNoticeService.sendNotice(sohuAccountVo.getUserId(), sohuAccountVo.getRoleKey(), new Date());

            if (sohuAccountVo.getState().equals(AuditState.Pass.name()) && !StrUtil.equals(sohuAccountVo.getAccountType(), bo.getAccountType())) {
                vo.setRejectReason("您已实名其它角色");
                this.updateOfRefuse(vo);
                this.syncSohuAccount(vo.getUserId(), AuditState.Refuse.name(), vo.getRejectReason());
                return false;
            }
            //实名认证审核
            String msg = this.syncSohuAccount(vo.getUserId(), bo.getState(), bo.getRejectReason());
            if (StrUtil.isNotBlank(msg)) {
                vo.setRejectReason(msg);
                this.updateOfRefuse(vo);
                return false;
            }
            // 数据更新
            vo.setState(AuditState.Pass.name());
            this.updateSohuAccount(vo);
            // 认证通过，赋予平台角色
            this.remotePlatformRoleService.insertUserRole(vo.getRoleKey(), vo.getUserId());
            // 认证通过，赋予角色
            //this.remoteSysRoleService.insertUserRole(vo.getRoleKey(), vo.getUserId());
//            //因为没有配置页面，目前先授予对应平台的所有功能角色
//            List<SysRoleVo> enterRoleList = this.remoteSysRoleService.selectListOfEnableByPlatformRoleKey(vo.getRoleKey());
//            this.remoteSysRoleService.insertList(BeanUtil.copyToList(enterRoleList,SysRole.class),vo.getUserId());

            if (Objects.isNull(sohuPlatformConfigVo)) {
                throw new ServiceException("入驻通道未开启");
            }
            if (StrUtil.isNotBlank(sohuPlatformConfigVo.getRoleKeysStr())) {
                String[] roleKeyArray = sohuPlatformConfigVo.getRoleKeysStr().split(",");
                this.remoteSysRoleService.batchInsertList(Arrays.asList(roleKeyArray), vo.getUserId());
                this.remoteUserService.flushLoginCacheByUserId(vo.getUserId());
            }
            // 初始化MCN机构分组
            if (StringUtils.equalsAnyIgnoreCase(PlatformRoleCodeEnum.MCN.getCode(), vo.getRoleKey())) {
                remoteMiddleMcnGroupService.initDefaultData(vo.getUserId());
            }
            // 角色认证审核通过发送消息通知
            sohuEntrySendMsgUtil.sendMsgOfRoleAuthPass(vo);
            // 角色认证审核通过-保证金缴纳提醒
            sohuEntrySendMsgUtil.sendMsgOfBailPayWarn(vo);
            // 发送 邀请建立专属服务群
            sohuEntrySendMsgUtil.sendMsgOfCreateAgentGroup(vo);
            // 角色认证审核更新客户邀请角色字段
            CompletableFuture.runAsync(() -> remoteAdminService.addRoleKey(vo.getUserId(), vo.getRoleKey()), asyncConfig.getAsyncExecutor());
        } else {
            vo.setRejectReason(bo.getRejectReason());
            this.updateOfRefuse(vo);
            this.syncSohuAccount(vo.getUserId(), AuditState.Refuse.name(), vo.getRejectReason());
            // 角色认证审核未通过发送消息通知
            sohuEntrySendMsgUtil.sendMsgOfRoleAuthNotPass(bo);
        }
        return true;
    }

    /**
     * 同步审核信息到实名认证
     *
     * @param state
     * @param rejectReason
     * @return
     */
    private String syncSohuAccount(Long userId, String state, String rejectReason) {
        //实名认证审核
        SohuAccountBo sohuAccountBo = new SohuAccountBo();
        sohuAccountBo.setState(state);
        sohuAccountBo.setRejectReason(rejectReason);
        sohuAccountBo.setUserId(userId);
        return remoteAccountService.auditByThirdParty(sohuAccountBo);
    }

    private Boolean updateOfRefuse(SohuAccountEnterVo vo) {
        vo.setState(AuditState.Refuse.name());
        return this.updateSohuAccount(vo);
    }

//    /**
//     * 处理未实名认证用户
//     *
//     * @param sohuAccountEnterVo SohuAccountEnterVo
//     */
//    @Deprecated
//    private void handleUnverifiedUser(SohuAccountEnterVo sohuAccountEnterVo) {
//        // 图片处理
//        //uploadPicture(sohuAccountEnterVo);
//        //validateCertPhotos(sohuAccountEnterVo);
//        // 注册翼码用户
//        //String msg = registerMerchant(sohuAccountEnterVo);
//        // 处理用户信息,通过成功码,非通过错误信息
//        //handleUserAfterVerification(msg, sohuAccountEnterVo);
//    }

//    /**
//     * 处理实名认证用户
//     *
//     * @param msg                翼码创建返回信息
//     * @param sohuAccountEnterVo SohuAccountEnterVo
//     */
//    private void handleUserAfterVerification(String msg, SohuAccountEnterVo sohuAccountEnterVo) {
//        boolean isSuccess = StringUtils.equalsAnyIgnoreCase(msg, Constants.YIMAO_SUCCESS_CODE);
//        String rejectReason = isSuccess ? null : msg;
//        String newState = isSuccess ? AuditState.Pass.name() : AuditState.Refuse.name();
//        sohuAccountEnterVo.setState(newState);
//        sohuAccountEnterVo.setRejectReason(rejectReason);
//
//        if (isSuccess) {
//            String roleKey = sohuAccountEnterVo.getRoleKey();
//            Long userId = sohuAccountEnterVo.getUserId();
//            // 认证通过，赋予角色
//            this.remoteSysRoleService.insertUserRole(roleKey, userId);
//            // 初始化MCN机构分组
//            if (StringUtils.equalsAnyIgnoreCase(RoleCodeEnum.MCN.getCode(), roleKey)) {
//                remoteMiddleMcnGroupService.initDefaultData(userId);
//            }
//        }
//    }


//    /**
//     * 处理实名认证用户
//     *
//     * @param sohuAccountModel   SohuAccountModel
//     * @param sohuAccountEnterVo SohuAccountEnterVo
//     */
//    private void handleVerifiedUser(SohuAccountVo sohuAccountModel, SohuAccountEnterVo sohuAccountEnterVo) {
//        // 获取账户类型和角色
//        String accountType = sohuAccountModel.getAccountType();
//        String roleKey = sohuAccountEnterVo.getRoleKey();
//
//        // 判断是否符合入驻条件
//        boolean isPersonalAccount = UserAuthEnum.personal.getType().equalsIgnoreCase(accountType);
//        boolean isProfessorRole = StringUtils.equalsAnyIgnoreCase(roleKey, RoleCodeEnum.Professor.getCode());
//        boolean isBusinessAccount = UserAuthEnum.business.getType().equals(accountType);
//        boolean isMcnOrAgentRole = StringUtils.equalsAnyIgnoreCase(roleKey, RoleCodeEnum.MCN.getCode()) || StringUtils.equalsAnyIgnoreCase(roleKey, RoleCodeEnum.Agent.getCode());
//
//        if (isPersonalAccount && isProfessorRole) {
//            this.remoteSysRoleService.insertUserRole(roleKey, sohuAccountEnterVo.getUserId());
//            sohuAccountEnterVo.setState(AuditState.Pass.name());
//        } else if (isBusinessAccount && isMcnOrAgentRole) {
//            this.remoteSysRoleService.insertUserRole(roleKey, sohuAccountEnterVo.getUserId());
//            sohuAccountEnterVo.setState(AuditState.Pass.name());
//            // 如果是 MCN 角色，则初始化机构分组
//            if (StringUtils.equalsAnyIgnoreCase(RoleCodeEnum.MCN.getCode(), roleKey)) {
//                remoteMiddleMcnGroupService.initDefaultData(sohuAccountEnterVo.getUserId());
//            }
//        } else {
//            // 如果不符合入驻条件，则设置状态为拒绝
//            sohuAccountEnterVo.setState(AuditState.Refuse.name());
//            sohuAccountEnterVo.setRejectReason("当前账户不符合入驻条件");
//        }
//    }


    /**
     * 审核后更新数据库数据
     *
     * @param vo SohuAccountEnterVo
     * @return Boolean
     */
    private Boolean updateSohuAccount(SohuAccountEnterVo vo) {
        SohuAccountEnter update = BeanUtil.toBean(vo, SohuAccountEnter.class);
        update.setAuditTime(new Date());
        baseMapper.updateById(update);

        LoginUser loginUser = remoteUserService.queryById(vo.getUserId());
        vo.setContactPhone(loginUser.getPhoneNumber());
        vo.setPassword(loginUser.getPassword());
        //角色授予后发送信息
        sohuEntrySendMsgUtil.sendMsgOfEntry(vo);
        //角色授予后发送邮箱
        sohuEntrySendMsgUtil.sendEmailOfEntry(vo);
        // 发送MCN消息通知 TODO
//        this.sendMCNNotice(vo);
        //发送极光推送审核结果通知
        CompletableFuture.runAsync(() -> this.pushAuditResultNotice(update, vo.getState()), asyncConfig.getAsyncExecutor());
        return true;
    }

    /**
     * 推送角色认证审核结果通知
     *
     * @param enter
     */
    private void pushAuditResultNotice(SohuAccountEnter enter, String state) {
        SohuJiguangPush2UserReqBo reqBo = new SohuJiguangPush2UserReqBo();
        reqBo.setUserIds(Lists.newArrayList(enter.getUserId()));
        reqBo.setJumpPathType(AppPathTypeEnum.ENTRY_ROLE_ENTRY);
        reqBo.setBizType(PushJiGuangBizTypeEnum.ACCOUNT);
        String title = "";
        String alert = "";
        if (StringUtils.equals(AuditState.Pass.name(), state)) {
            title = "角色入驻审核通过";
            alert = "您可以通过任务广场发布任务！";
        } else if (StringUtils.equals(AuditState.Refuse.name(), state)) {
            title = "角色入驻审核未通过";
            String roleName = Stream.of(PlatformRoleCodeEnum.values())
                    .filter(x -> x.name().equals(enter.getRoleKey()))
                    .map(PlatformRoleCodeEnum::getSubName)
                    .findFirst()
                    .orElse("");
            alert = String.format("您申请的%s角色认证申请未通过审核。", roleName);
        }
        reqBo.setTitle(title);
        reqBo.setAlert(alert);
        try {
            remoteJiguangService.push2User(reqBo);
        } catch (Exception e) {
            log.warn("角色入驻审核极光推送异常，原因: {}", e.getMessage());
        }
    }

    /**
     * 前端传递参数校验
     *
     * @param bo SohuAccountBo
     */
    private void validateParams(SohuAccountEnterBo bo) {
        if (bo.getId() == null || StringUtils.isBlank(bo.getState())) {
            throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
        }
    }

    /**
     * 审核前的数据校验
     *
     * @param sohuAccountVo 审核对象
     */
    private void validateEntityBeforeAudit(SohuAccountEnterVo sohuAccountVo) {
        if (ObjectUtil.isEmpty(sohuAccountVo)) {
            throw new RuntimeException(MessageUtils.message("WRONG_AUDIT_INFO_IS_NOT_EXIST"));
        }
        if (!sohuAccountVo.getState().equals(AuditState.WaitApprove.name())) {
            throw new RuntimeException(MessageUtils.message("already.audit"));
        }
    }
//
//    /**
//     * 校验翼码图片是否完整
//     *
//     * @param sohuAccountVo SohuAccountVo
//     */
//    @Deprecated
//    private void validateCertPhotos(SohuAccountEnterVo sohuAccountVo) {
//        if (sohuAccountVo.getCertPhotoFront().isEmpty() || sohuAccountVo.getCertPhotoBack().isEmpty()) {
//            throw new RuntimeException("证件照正反面不能为空");
//        }
//        if (StringUtils.isEmpty(sohuAccountVo.getCertPhotoA()) || StringUtils.isEmpty(sohuAccountVo.getCertPhotoB())) {
//            throw new RuntimeException("上传翼码证件照正反面不能为空");
//        }
//    }

//    /**
//     * 注册翼码信息
//     *
//     * @param sohuAccountEnterVo SohuAccountVo
//     * @return String
//     */
//    @Deprecated
//    public String registerMerchant(SohuAccountEnterVo sohuAccountEnterVo) {
//        boolean business = sohuAccountEnterVo.getAccountType().equals(UserAuthEnum.business.name());
//
//        if (StringUtils.isBlank(sohuAccountEnterVo.getMerchantId())) {
//            // 获取用户数据
//            LoginUser user = remoteUserService.queryById(sohuAccountEnterVo.getUserId());
//            // 添加翼码用户
//            String merchantType = business ? YIMA_BUSINESS_MERCHANT_TYPE : YIMA_PERSONAL_MERCHANT_TYPE;
//            MerchantCreateResponse response = createMerchant(user, sohuAccountEnterVo.getMerchantName(), merchantType);
//            if (!response.isSuccess()) {
//                handleFailure(sohuAccountEnterVo, response.getResult().getComment());
//                return response.getResult().getComment();
//            }
//            sohuAccountEnterVo.setMerchantId(response.getMerchantId());
//        }
//
//        //开通分账功能
//        if (business) {
//            return registerBusinessAccount(sohuAccountEnterVo);
//        } else {
//            return registerPersonalAccount(sohuAccountEnterVo);
//        }
//    }

//    /**
//     * 组装翼码请求参数
//     */
//    private MerchantCreateResponse createMerchant(LoginUser user, String merchantName, String merchantType) {
//        MerchantCreateRequest request = new MerchantCreateRequest();
//        MerchantCreate create = new MerchantCreate();
//        create.setMerchantShortName(user.getUsername());
//        create.setMerchantName(merchantName);
//        create.setPhone(user.getPhoneNumber());
//        create.setType(merchantType);
//        request.setMerchantCreateRequest(create);
//        request.setPosSeq(generateUniqueSequence("KH"));
//        return getYmClient().execute(request);
//    }

//    /**
//     * 翼码开通企业账户
//     *
//     * @param sohuAccountEnterVo SohuAccountEnterVo
//     * @return String 若出现错误,返回错误信息
//     */
//    private String registerBusinessAccount(SohuAccountEnterVo sohuAccountEnterVo) {
//        MerchantEntregisterRequest merchant = new MerchantEntregisterRequest();
//        MerchantEntregister bo = BeanUtil.copyProperties(sohuAccountEnterVo, MerchantEntregister.class);
//        if (StringUtils.isEmpty(bo.getCertPhotoB())) {
//            bo.setCertPhotoB(bo.getCertPhotoA());
//        }
//        merchant.setMerchantEntregisterRequest(bo);
//        merchant.setPosSeq(generateUniqueSequence("QY"));
//        MerchantEntregisterResponse execute = getYmClient().execute(merchant);
//        if (!execute.isSuccess()) {
//            handleFailure(sohuAccountEnterVo, execute.getResult().getComment());
//            return execute.getResult().getComment();
//        }
//        return Constants.YIMAO_SUCCESS_CODE;
//    }

//    /**
//     * 翼码开通个人账户
//     *
//     * @param sohuAccountEnterVo SohuAccountEnterVo
//     * @return String 若出现错误,返回错误信息
//     */
//    private String registerPersonalAccount(SohuAccountEnterVo sohuAccountEnterVo) {
//        MerchantIndvregisterRequest merchant = new MerchantIndvregisterRequest();
//        MerchantIndvregister bo = BeanUtil.copyProperties(sohuAccountEnterVo, MerchantIndvregister.class);
//        if (StringUtils.isEmpty(bo.getCertPhotoB())) {
//            bo.setCertPhotoB(bo.getCertPhotoA());
//        }
//        merchant.setMerchantIndvregisterRequest(bo);
//        merchant.setPosSeq(generateUniqueSequence("GR"));
//        MerchantIndvregisterResponse execute = getYmClient().execute(merchant);
//        if (!execute.isSuccess()) {
//            handleFailure(sohuAccountEnterVo, execute.getResult().getComment());
//            return execute.getResult().getComment();
//        }
//        return Constants.YIMAO_SUCCESS_CODE;
//    }

    /**
     * 生成唯一序列
     *
     * @param prefix 前缀标识
     * @return String
     */
    private String generateUniqueSequence(String prefix) {
        return prefix + System.nanoTime();
    }

//    /**
//     * 处理审核失败
//     *
//     * @param sohuAccountVo SohuAccountEnterVo
//     * @param comment       失败原因
//     */
//    private void handleFailure(SohuAccountEnterVo sohuAccountVo, String comment) {
//        sohuAccountVo.setState(AuditState.Refuse.name());
//        sohuAccountVo.setRejectReason(comment);
//        baseMapper.updateById(BeanUtil.toBean(sohuAccountVo, SohuAccountEnter.class));
//        log.error("Operation failed: {}", comment);
//    }

//    /**
//     * 注册系统用户
//     *
//     * @param bo SohuAccountEnterBo
//     * @return userId 用户id
//     */
//    private Long registerAccount(SohuAccountEnterBo bo) {
//        // 注册用户
//        String username = RandomUtils.genUserName();
//        SysUser sysUser = new SysUser();
//        sysUser.setUserName(username);
//        sysUser.setNickName(username);
//        sysUser.setPassword(DEFAULT_PWD);
//        // 默认系统用户
//        sysUser.setUserType("sys_user");
//        String regStr = null;
//        if (StrUtil.isNotBlank(bo.getPhoneNumber()) && ValidatorUtil.isMobile(bo.getPhoneNumber())) {
//            sysUser.setPhoneNumber(bo.getPhoneNumber());
//            regStr = bo.getPhoneNumber();
//            // 通过手机号查找用户
//            LoginUser exist = remoteUserService.getUserInfoByPhone(regStr);
//            if (Objects.nonNull(exist)) {
//                throw new UserException("user.exist");
//            }
//        }
//
//        sysUser.setAvatar(Constants.DEFAULT_AVATAR);
//        Long userId = remoteUserService.registerUserInfo(sysUser);
//        if (userId == null || userId <= 0L) {
//            throw new UserException("user.register.error");
//        }
//
//        return userId;
//    }

//    /**
//     * 添加翼码用户
//     */
//    private String createYmUser(SohuAccountEnterVo sohuAccountEnterVo) {
//        boolean business = sohuAccountEnterVo.getAccountType().equals(UserAuthEnum.business.name());
//        if (StringUtils.isBlank(sohuAccountEnterVo.getMerchantId())) {
//            // 获取用户数据
//            LoginUser user = remoteUserService.queryById(sohuAccountEnterVo.getUserId());
//            // 添加翼码用户
//            MerchantCreateRequest request = new MerchantCreateRequest();
//            MerchantCreate create = new MerchantCreate();
//            create.setMerchantShortName(user.getUsername());
//            create.setMerchantName(sohuAccountEnterVo.getMerchantName());
//            create.setPhone(user.getPhoneNumber());
//            create.setType(business ? "1" : "3");
//            request.setMerchantCreateRequest(create);
//            request.setPosSeq("12345678901234567890");
//            MerchantCreateResponse response = getYmClient().execute(request);
//            log.info("MerchantCreateResponse :{}", JSONObject.toJSONString(response));
//            if (!response.getResult().getId().equals(OrderConstants.YM_SUCCESS)) {
//                //失败
//                return response.getResult().getComment();
//            }
//            sohuAccountEnterVo.setMerchantId(response.getMerchantId());
//        }
//        //开通翼码账户
//        if (business) {
//            MerchantEntregisterRequest merchant = new MerchantEntregisterRequest();
//            MerchantEntregister bo = BeanUtil.copyProperties(sohuAccountEnterVo, MerchantEntregister.class);
//            if (StringUtils.isEmpty(bo.getCertPhotoB())) {
//                bo.setCertPhotoB(bo.getCertPhotoA());
//            }
//            merchant.setMerchantEntregisterRequest(bo);
//            merchant.setPosSeq("12345678901234567890");
//            MerchantEntregisterResponse execute = getYmClient().execute(merchant);
//            log.info("MerchantEntregisterResponse :{}", JSONObject.toJSONString(execute));
//            if (!"0000".equals(execute.getResult().getId())) {
//                //失败
//                sohuAccountEnterVo.setState(AuditState.Refuse.name());
//                String[] split = execute.getResult().getComment().split("-");
//                sohuAccountEnterVo.setRejectReason(split.length >= 2 ? split[1] : split[0]);
//                baseMapper.updateById(BeanUtil.toBean(sohuAccountEnterVo, SohuAccountEnter.class));
//                return execute.getResult().getComment();
//            }
//        } else {
//            MerchantIndvregisterRequest merchant = new MerchantIndvregisterRequest();
//            MerchantIndvregister bo = BeanUtil.copyProperties(sohuAccountEnterVo, MerchantIndvregister.class);
//            if (StringUtils.isEmpty(bo.getCertPhotoB())) {
//                bo.setCertPhotoB(bo.getCertPhotoA());
//            }
//            merchant.setMerchantIndvregisterRequest(bo);
//            merchant.setPosSeq("12345678901234567890");
//            MerchantIndvregisterResponse execute = getYmClient().execute(merchant);
//            log.info("MerchantIndvregisterResponse :{}", JSONObject.toJSONString(execute));
//            if (!"0000".equals(execute.getResult().getId())) {
//                //失败
//                sohuAccountEnterVo.setState(AuditState.Refuse.name());
//                String[] split = execute.getResult().getComment().split("-");
//                sohuAccountEnterVo.setRejectReason(split.length >= 2 ? split[1] : split[0]);
//                baseMapper.updateById(BeanUtil.toBean(sohuAccountEnterVo, SohuAccountEnter.class));
//                return execute.getResult().getComment();
//            }
//        }
//        return null;
//    }

//    /**
//     * 图片处理
//     */
//    @Deprecated
//    private void uploadPicture(SohuAccountEnterVo sohuAccountEnterVo) {
//        //法人证件处理
//        if (sohuAccountEnterVo.getIdentityType().equals(IdentityTypeEnum.passport.getCode())) {
//            sohuAccountEnterVo.setCertPhotoA(urlChangeBase64(sohuAccountEnterVo.getCertPhotoFront(), PictrueTypeEnum.Passport));
//            sohuAccountEnterVo.setCertPhotoB(urlChangeBase64(sohuAccountEnterVo.getCertPhotoBack(), PictrueTypeEnum.Passport));
//        } else if (sohuAccountEnterVo.getIdentityType().equals(IdentityTypeEnum.IdentificationCard.getCode())) {
//            sohuAccountEnterVo.setCertPhotoA(urlChangeBase64(sohuAccountEnterVo.getCertPhotoFront(), PictrueTypeEnum.CertPhotoA));
//            sohuAccountEnterVo.setCertPhotoB(urlChangeBase64(sohuAccountEnterVo.getCertPhotoBack(), PictrueTypeEnum.CertPhotoB));
//        } else if (sohuAccountEnterVo.getIdentityType().equals(IdentityTypeEnum.TaiwanPass.getCode())) {
//            sohuAccountEnterVo.setCertPhotoA(urlChangeBase64(sohuAccountEnterVo.getCertPhotoFront(), PictrueTypeEnum.TaiwanPass));
//            sohuAccountEnterVo.setCertPhotoB(urlChangeBase64(sohuAccountEnterVo.getCertPhotoBack(), PictrueTypeEnum.TaiwanPass));
//        } else if (sohuAccountEnterVo.getIdentityType().equals(IdentityTypeEnum.ForeignerPass.getCode())) {
//            sohuAccountEnterVo.setCertPhotoA(urlChangeBase64(sohuAccountEnterVo.getCertPhotoFront(), PictrueTypeEnum.ForeignerPass));
//            sohuAccountEnterVo.setCertPhotoB(urlChangeBase64(sohuAccountEnterVo.getCertPhotoBack(), PictrueTypeEnum.ForeignerPass));
//        } else if (sohuAccountEnterVo.getIdentityType().equals(IdentityTypeEnum.MacaoResident.getCode())) {
//            sohuAccountEnterVo.setCertPhotoA(urlChangeBase64(sohuAccountEnterVo.getCertPhotoFront(), PictrueTypeEnum.MacaoResident));
//            sohuAccountEnterVo.setCertPhotoB(urlChangeBase64(sohuAccountEnterVo.getCertPhotoBack(), PictrueTypeEnum.MacaoResident));
//        } else if (sohuAccountEnterVo.getIdentityType().equals(IdentityTypeEnum.TaiwanResident.getCode())) {
//            sohuAccountEnterVo.setCertPhotoA(urlChangeBase64(sohuAccountEnterVo.getCertPhotoFront(), PictrueTypeEnum.TaiwanResident));
//            sohuAccountEnterVo.setCertPhotoB(urlChangeBase64(sohuAccountEnterVo.getCertPhotoBack(), PictrueTypeEnum.TaiwanResident));
//        }
//        if (StringUtils.isNotBlank(sohuAccountEnterVo.getQualificationPhoto()) && StringUtils.isBlank(sohuAccountEnterVo.getLicensePhoto())) {
//            sohuAccountEnterVo.setLicensePhoto(urlChangeBase64(sohuAccountEnterVo.getQualificationPhoto(), PictrueTypeEnum.LicensePhoto));
//        }
//    }

//    @Override
//    public Boolean checkPasswordUnique(String oldPassword, String password) {
//        // 校验两次输入密码是否一致
//        boolean passwordMatch = StringUtils.equals(oldPassword, password);
//        if (!passwordMatch) {
//            throw new RuntimeException("两次输入的密码不一致");
//        }
//
//        return passwordMatch;
//    }

    @Override
    public Boolean checkSmsCode(String smsCode) {
        if (BooleanUtil.isTrue(captchaDisabled)) {
            return true;
        }
        // 获取登录用户
        LoginUser loginUser = LoginHelper.getLoginUser();
        // 获取用户手机号
        assert loginUser != null;
        String phoneNumber = loginUser.getPhoneNumber();
        if (StringUtils.isEmpty(phoneNumber)) {
            Long userId = loginUser.getUserId();
            LoginUser userInfo = remoteUserService.selectAllUserById(userId);
            phoneNumber = userInfo.getPhoneNumber();
        }
        String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + phoneNumber);
        if (StringUtils.isBlank(code)) {
            throw new RuntimeException("输入的验证码有误");
        }
        return code.equals(smsCode);
    }

    @Override
    public Boolean checkEmailCode(String emailCode) {
        LoginUser loginUser = remoteUserService.selectById(LoginHelper.getUserId());
        String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + loginUser.getEmail());
        if (StringUtils.isBlank(code)) {
            throw new RuntimeException("输入的验证码有误");
        }
        boolean result = code.equals(emailCode);
        // 验证码只能使用一次
        if (result) {
            RedisUtils.deleteObject(CacheConstants.CAPTCHA_CODE_KEY + loginUser.getEmail());
        }
        return result;
    }

    @Override
    public SohuAccountEnterModel selectAccountEnterByUserId(Long userId) {
        LambdaQueryWrapper<SohuAccountEnter> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuAccountEnter::getUserId, userId);
        wrapper.in(SohuAccountEnter::getState, AuditState.Pass, AuditState.WaitApprove);
        SohuAccountEnterVo sohuAccountEnterVo = baseMapper.selectVoOne(wrapper);
        if (sohuAccountEnterVo == null) {
            return null;
        }
        //查询收款人名称
        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserIdOfPass(userId);
        if (sohuAccountVo != null) {
            sohuAccountEnterVo.setMerchantName(sohuAccountVo.getMerchantName());
        }
        return BeanUtil.copyProperties(sohuAccountEnterVo, SohuAccountEnterModel.class);
    }

//    @Override
//    public SohuAccountEnterModel checkAccountEnterByUserId(Long userId) {
//        LambdaQueryWrapper<SohuAccountEnter> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(SohuAccountEnter::getUserId, userId);
//        wrapper.in(SohuAccountEnter::getState, AuditState.Pass);
//        SohuAccountEnterVo sohuAccountEnterVo = baseMapper.selectVoOne(wrapper);
//        if (sohuAccountEnterVo == null) {
//            return null;
//        }
//        return BeanUtil.copyProperties(sohuAccountEnterVo, SohuAccountEnterModel.class);
//    }

//    @Override
//    public Boolean checkAccountEnterOfNotPass(Long userId) {
//        SohuAccountEnter entity = baseMapper.selectOne(SohuAccountEnter::getUserId, userId);
//        if (Objects.isNull(entity)) {
//            return true;
//        }
//        if(Objects.equals(entity.getState(),AuditState.Pass.name())){
//            return false;
//        }
//        return true;
//    }

//    @Override
//    public SohuAccountEnterModel queryByUserId(Long userId) {
//        LambdaQueryWrapper<SohuAccountEnter> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(SohuAccountEnter::getUserId, userId);
//        wrapper.eq(SohuAccountEnter::getState, AuditState.Pass);
//        SohuAccountEnterVo sohuAccountEnterVo = baseMapper.selectVoOne(wrapper);
//
//        return BeanUtil.copyProperties(sohuAccountEnterVo, SohuAccountEnterModel.class);
//    }

    @Override
    public Map<String, String> checkUserEntry(Long userId, String roleKey) {
        log.info("前端传递参数:userId:{},roleKey:{}", userId, roleKey);
        LambdaQueryWrapper<SohuAccountEnter> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SohuAccountEnter::getUserId, userId)
                .eq(SohuAccountEnter::getRoleKey, roleKey)
                .in(SohuAccountEnter::getState, AuditState.Pass, AuditState.WaitApprove)
                .last("limit 1");
        SohuAccountEnter sohuAccountEnter = baseMapper.selectOne(wrapper);
        //List<SohuEntryAuthVo> sohuEntryAuthVoList = iSohuEntryAuthService.selectEntryListByUserId(userId);
        log.info("入驻信息:{}", sohuAccountEnter);
        Map<String, String> map = new HashMap<>();
        // 是否入驻或审核通过
        //if (Objects.nonNull(sohuAccountEnter) || CollUtil.isNotEmpty(sohuEntryAuthVoList)) {
        if (Objects.nonNull(sohuAccountEnter)) {
            map.put("hasEntryOrAccount", ENTER);
            return map;
        }
        // 是否实名认证(包含待审核以及通过,不包含拒绝) TODO 待重新写
        SohuAccountVo sohuAccountModel = remoteAccountService.queryByUserIdOfPassOrWaitApprove(userId);
        log.info("实名信息:{}", sohuAccountModel);
        if (Objects.isNull(sohuAccountModel)) {
            map.put("hasEntryOrAccount", null);
            return map;
        }
        // 获取账号类型
        String accountType = sohuAccountModel.getAccountType();
        // 处理账号类型
        if (handleAccountType(roleKey, userId, accountType)) {
            map.put("hasEntryOrAccount", ACCOUNT);
            return map;
        } else {
            map.put("hasEntryOrAccount", null);
            return map;
        }
    }

    /**
     * 处理账号类型
     *
     * @param roleKey 角色标识
     * @param userId  用户id
     * @return boolean
     */
    private boolean handleAccountType(String roleKey, Long userId, String accountType) {

        if (UserAuthEnum.business.getType().equalsIgnoreCase(accountType)) {
            return handleBusinessAccount(roleKey);
        } else if (UserAuthEnum.personal.getType().equalsIgnoreCase(accountType)) {
            return handlePersonalAccount(roleKey);
        }

        return false;
    }

    /**
     * 处理个人账号
     *
     * @param roleKey 角色标识
     * @return Boolean
     */
    private Boolean handlePersonalAccount(String roleKey) {
        return !StrUtil.equalsAnyIgnoreCase(roleKey, PlatformRoleCodeEnum.Professor.getCode());
    }

    /**
     * @param roleKey 角色标识
     * @return Boolean
     */
    private Boolean handleBusinessAccount(String roleKey) {
        return !(StrUtil.equalsAnyIgnoreCase(roleKey, PlatformRoleCodeEnum.MCN.getCode()) ||
                StrUtil.equalsAnyIgnoreCase(roleKey, PlatformRoleCodeEnum.Agent.getCode()));
    }

    private void buildAccountEnterByAccount(SohuAccountEnterVo vo, SohuAccountVo sohuAccountVo) {
        if (Objects.isNull(sohuAccountVo)) {
            return;
        }
        vo.setAccountType(sohuAccountVo.getAccountType());
        vo.setMerchantId(sohuAccountVo.getMerchantId());
        vo.setMerchantName(sohuAccountVo.getMerchantName());
        vo.setIdentityType(sohuAccountVo.getIdentityType());
        vo.setLegalName(sohuAccountVo.getLegalName());
        vo.setIdentityNo(sohuAccountVo.getIdentityNo());
        vo.setIdentityBeginDate(sohuAccountVo.getIdentityBeginDate());
        vo.setIdentityEndDate(sohuAccountVo.getIdentityEndDate());
        vo.setIdentityValidityType(sohuAccountVo.getIdentityValidityType());
        vo.setContactPhone(sohuAccountVo.getContactPhone());
        vo.setCertPhotoA(sohuAccountVo.getCertPhotoA());
        vo.setCertPhotoB(sohuAccountVo.getCertPhotoB());
        vo.setContactName(sohuAccountVo.getContactName());
        vo.setContactEmail(sohuAccountVo.getContactEmail());
        vo.setLicensePhoto(sohuAccountVo.getLicensePhoto());
        vo.setLicenseCode(sohuAccountVo.getLicenseCode());
        vo.setLicenseBeginDate(sohuAccountVo.getLicenseBeginDate());
        vo.setLicenseEndDate(sohuAccountVo.getLicenseEndDate());
        vo.setLicenseValidityType(sohuAccountVo.getLicenseValidityType());
        vo.setLicenseProvinceCode(sohuAccountVo.getLicenseProvinceCode());
        vo.setLicenseCityCode(sohuAccountVo.getLicenseCityCode());
        vo.setLicenseAreaCode(sohuAccountVo.getLicenseAreaCode());
        vo.setLicenseAddress(sohuAccountVo.getLicenseAddress());
        vo.setCertPhotoFront(sohuAccountVo.getCertPhotoFront());
        vo.setCertPhotoBack(sohuAccountVo.getCertPhotoBack());
        vo.setQualificationPhoto(sohuAccountVo.getQualificationPhoto());
        vo.setLicenseProvinceName(sohuAccountVo.getLicenseProvinceName());
        vo.setLicenseCityName(sohuAccountVo.getLicenseCityName());
        vo.setLicenseAreaName(sohuAccountVo.getLicenseAreaName());
        vo.setLicenseType(sohuAccountVo.getLicenseType());
        vo.setAuditState(vo.getState());
        vo.setState(sohuAccountVo.getState());
        vo.setBailSource(sohuAccountVo.getBailSource());
        vo.setBailState(sohuAccountVo.getBailState());
        vo.setAccountId(sohuAccountVo.getId());
        vo.setAccountRejectReason(sohuAccountVo.getRejectReason());
        //查询资质信息
        if (Objects.nonNull(vo.getId())) {
            SohuAccountEnterAptitudeBo bo = new SohuAccountEnterAptitudeBo();
            bo.setAccountEnterId(vo.getId());
            List<SohuAccountEnterAptitudeVo> sohuAccountEnterAptitudeBoList = iSohuAccountEnterAptitudeService.queryList(bo);
            vo.setAptitudeList(sohuAccountEnterAptitudeBoList);
        }
    }

}
